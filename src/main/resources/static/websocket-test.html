<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket RPC通知测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .panel h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .message {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        .message.rpc {
            border-left: 4px solid #007bff;
        }
        .message.system {
            border-left: 4px solid #28a745;
        }
        .message.error {
            border-left: 4px solid #dc3545;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input, select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .form-group {
            margin-bottom: 10px;
        }
        .form-group label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>WebSocket RPC通知测试页面</h1>
    
    <div class="container">
        <!-- 连接控制面板 -->
        <div class="panel">
            <h3>连接控制</h3>
            <div id="connectionStatus" class="status disconnected">未连接</div>
            
            <div class="form-group">
                <label>用户ID:</label>
                <input type="text" id="userId" value="test-user-001" />
            </div>
            
            <div class="form-group">
                <label>连锁ID:</label>
                <input type="text" id="chainId" value="test-chain-001" />
            </div>
            
            <div class="form-group">
                <label>门店ID:</label>
                <input type="text" id="clinicId" value="test-clinic-001" />
            </div>
            
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            
            <h4>订阅管理</h4>
            <button onclick="subscribeToRpcNotifications()">订阅RPC通知</button>
            <button onclick="subscribeToSystemNotifications()">订阅系统通知</button>
            <button onclick="subscribeToChainNotifications()">订阅连锁通知</button>
            
            <h4>测试功能</h4>
            <button onclick="sendTestMessage()">发送测试消息</button>
            <button onclick="registerUser()">注册用户</button>
            <button onclick="testRpcRequest()">测试RPC请求</button>
        </div>
        
        <!-- 消息显示面板 -->
        <div class="panel">
            <h3>消息日志</h3>
            <button onclick="clearMessages()">清空消息</button>
            <div id="messages" class="messages"></div>
        </div>
    </div>

    <script>
        let stompClient = null;
        let connected = false;

        function connect() {
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            
            // 设置连接头信息
            const headers = {
                'userId': document.getElementById('userId').value,
                'chainId': document.getElementById('chainId').value,
                'clinicId': document.getElementById('clinicId').value
            };
            
            stompClient.connect(headers, function (frame) {
                connected = true;
                updateConnectionStatus(true);
                showMessage('系统', '连接成功: ' + frame, 'system');
                
                // 自动订阅一些基本频道
                subscribeToRpcNotifications();
                subscribeToSystemNotifications();
                
            }, function (error) {
                connected = false;
                updateConnectionStatus(false);
                showMessage('错误', '连接失败: ' + error, 'error');
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
            connected = false;
            updateConnectionStatus(false);
            showMessage('系统', '连接已断开', 'system');
        }

        function updateConnectionStatus(isConnected) {
            const statusDiv = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (isConnected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function subscribeToRpcNotifications() {
            if (stompClient && connected) {
                stompClient.subscribe('/topic/rpc-notifications', function (message) {
                    const notification = JSON.parse(message.body);
                    showMessage('RPC通知', formatRpcNotification(notification), 'rpc');
                });
                showMessage('系统', '已订阅RPC通知', 'system');
            }
        }

        function subscribeToSystemNotifications() {
            if (stompClient && connected) {
                stompClient.subscribe('/topic/system-notifications', function (message) {
                    const notification = JSON.parse(message.body);
                    showMessage('系统通知', JSON.stringify(notification, null, 2), 'system');
                });
                showMessage('系统', '已订阅系统通知', 'system');
            }
        }

        function subscribeToChainNotifications() {
            if (stompClient && connected) {
                const chainId = document.getElementById('chainId').value;
                stompClient.subscribe('/topic/rpc-notifications/' + chainId, function (message) {
                    const notification = JSON.parse(message.body);
                    showMessage('连锁RPC通知', formatRpcNotification(notification), 'rpc');
                });
                showMessage('系统', '已订阅连锁通知: ' + chainId, 'system');
            }
        }

        function sendTestMessage() {
            if (stompClient && connected) {
                stompClient.send("/app/send", {}, JSON.stringify({
                    'content': '这是一条测试消息',
                    'timestamp': new Date().toISOString()
                }));
                showMessage('发送', '测试消息已发送', 'system');
            }
        }

        function registerUser() {
            if (stompClient && connected) {
                stompClient.send("/app/register", {}, JSON.stringify({
                    'userId': document.getElementById('userId').value,
                    'chainId': document.getElementById('chainId').value,
                    'clinicId': document.getElementById('clinicId').value
                }));
                showMessage('发送', '用户注册信息已发送', 'system');
            }
        }

        function testRpcRequest() {
            // 发送一个实际的RPC请求来测试通知功能
            fetch('/websocket/test/notification?message=测试RPC通知功能')
                .then(response => response.json())
                .then(data => {
                    showMessage('测试', 'RPC请求已发送: ' + JSON.stringify(data), 'system');
                })
                .catch(error => {
                    showMessage('错误', 'RPC请求失败: ' + error, 'error');
                });
        }

        function formatRpcNotification(notification) {
            const content = notification.content;
            return `
路径: ${content.requestPath}
方法: ${content.httpMethod}
状态: ${content.status}
${content.processingTime ? '处理时间: ' + content.processingTime + 'ms' : ''}
${content.errorMessage ? '错误: ' + content.errorMessage : ''}
时间: ${notification.timestamp}
            `.trim();
        }

        function showMessage(type, content, cssClass) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + cssClass;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <strong>[${timestamp}] ${type}:</strong><br>
                <pre>${content}</pre>
            `;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载完成后自动连接
        window.addEventListener('load', function() {
            // 可以在这里自动连接，或者让用户手动连接
            // connect();
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (connected) {
                disconnect();
            }
        });
    </script>
</body>
</html>
