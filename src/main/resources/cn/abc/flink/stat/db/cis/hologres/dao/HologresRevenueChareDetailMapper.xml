<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresRevenueChareDetailMapper">
    <select id="selectTransaction" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailTransactionDao">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        v2_transaction_id as transactionId,
        v2_patient_order_id as patientOrderId,
        create_time as createTime,
        sell_no as sellNo,
        patientorder_no as patientorderNo,
        patient_id as patientId,
        replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
        if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
        cashier_id as cashierId,
        retail_type as retailType,
        type as type,
        pay_type as payType,
        pay_sub_type as paySubType,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        copywriter_id as copywriterId,
        v2_charge_sheet_id as chargeSheetId,
        <if test='params.hisType == 10'>
            if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
            if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
            patient_source_from_type as visitSourceFromType,
            patient_source_form as visitSourceFrom,
        </if>
        <if test='params.hisType != 10'>
            if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
            if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
            visit_source_from_type as visitSourceFromType,
            visit_source_from as visitSourceFrom,
        </if>
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        visit_source_remark as visiteSourceRemark,
        max(scene_type) as sceneType,
        record_source_type as recordSourceType,
        charge_sheet_remark as chargeSheetRemark,
        comment as comment,
        if(record_source_type = 2, original_price, 0.0) as oweOriginalPrice,
        if(type=-1, -1, 1) * charge_sheet_deduct_price as deductPrice,
        if(record_source_type = 2,
            original_price,
            if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), received_price, charge_sheet_receivable_price)
        ) as shouldReceivePrice,
        if(type=-1, -1, 1) * charge_sheet_adjustment_price as adjustmentPrice,
        member_present AS memberPresent,
        member_principal AS memberPrincipal,
        <if test='params.hisType == 10'>
            if(type=-1, -1, 1) * abs(charge_sheet_total_price) as originalPrice,
            if(type=-1, -1, 1) * charge_sheet_discount_price as discountPrice,
            sum(if(record_source_type = 2, if(type=-1, -1, 1) * record_discount_price, 0.0)) as discountPriceSum ,
        </if>
        <if test='params.hisType != 10'>
            if(type=-1, -1, 1) * (abs(charge_sheet_total_price) - if(charge_sheet_unit_adjustment_price is null,0.0,charge_sheet_unit_adjustment_price) - if (charge_sheet_adjustment_price &lt; 0, 0.0, charge_sheet_adjustment_price)) as originalPrice,
            if(type=-1, -1, 1) * if(charge_sheet_adjustment_price is not null and charge_sheet_adjustment_price &lt; 0, charge_sheet_discount_price-charge_sheet_adjustment_price, charge_sheet_discount_price) as discountPrice,
            sum(if(record_source_type = 2, if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price), 0.0)) as discountPriceSum ,
        </if>
        sum((select count(a.product_id) from (
                select product_id
                from ${env}.dwd_charge_transaction_record_v_partition
                where product_id = b.product_id
                and create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
                and import_flag = 0
                and is_deleted = 0
                and product_type!=11
                and chain_id = #{params.chainId}
                <if test="params.clinicId != null and params.clinicId != ''">
                    and clinic_id=#{params.clinicId}
                </if>
                group by product_id
        ) a) )as productCount,
        sum(if(record_source_type = 2, if(type=-1, -1, 1) * record_adjustment_price, 0.0)) as adjustmentPriceSum,
        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
        referral_doctor_id as referralDoctorId,
        air_pharmacy_order_id as airPharmacyOrderId,
        if(type=-1, -1, 1) * if(record_source_type = 2 ,0.0,if(charge_sheet_unit_adjustment_price is null, 0.0, charge_sheet_unit_adjustment_price)) as chargeSheetUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        pharmacist_id as pharmacistId,
        cooperation_clinic_id as cooperationClinicId,
        member_id as memberId,
        if(type=-1, -1, 1) * charge_sheet_verify_price as recordVerifyPrice,
        shebao_extend::text as shebaoExtend,
        sum(if(replace(discount_info, '\', '')::json ->> 'oddFee' is not null, (replace(discount_info, '\', '')::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
        from ${env}.dwd_charge_transaction_record_v_partition b
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and product_type!=11
        <if test='params.hisType == 100'>
            and (product_compose_type != 1 and goods_fee_type in (0,2))
        </if>
        <if test='params.hisType != 100'>
            and goods_fee_type in(0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and chain_id = #{params.chainId}
        and pay_type != 100
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.cashierId != null and params.cashierId != ''">
            and cashier_id = #{params.cashierId}
        </if>
        <if test="params.visitSource1 != null">
            and ${params.visitSource1}
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
            and cooperation_clinic_id = #{params.cooperationClinicId}
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test="params.sellNo != null and params.sellNo != ''">
            and sell_no = #{params.sellNo}
        </if>
        group by chainId, clinicId, transactionId, createTime, sellNo, patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, type,
                 payType, paySubType, departmentId, employeeId, employeeName, copywriterId,  chargeSheetId, visiteSource1, visiteSource2, visitSourceFromType,visitSourceFrom,patientSourceFromType,
            patientSourceFrom,visiteSourceRemark,recordSourceType,comment,oweOriginalPrice,deductPrice,shouldReceivePrice,adjustmentPrice,memberPresent,memberPrincipal,
            originalPrice,discountPrice,referralDoctorId,airPharmacyOrderId,chargeSheetUnitermAdjustmentPrice,revisitStatus,pharmacistId, chargeSheetRemark,cooperationClinicId,recordVerifyPrice, patientOrderId,memberId,shebaoExtend
        order by createTime desc, patientorderNo desc
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectTransactionCavity" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailTransactionDao">
        /*+MAX_WHERE_ITEMS_COUNT = 512*/
        select
        chain_id as chainId,
        clinic_id as clinicId,
        v2_transaction_id as transactionId,
        create_time as createTime,
        patientorder_no as patientorderNo,
        patient_id as patientId,
        replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
        if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
        cashier_id as cashierId,
        retail_type as retailType,
        type as type,
        pay_type as payType,
        pay_sub_type as paySubType,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        copywriter_id as copywriterId,
        v2_charge_sheet_id as chargeSheetId,
        <if test="params.hisType == 10">
            if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
            if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
            patient_source_form as visitSourceFrom,
        </if>
        <if test="params.hisType != 10">
            if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
            if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
            visit_source_from as visitSourceFrom,
        </if>
        visit_source_from_type as visitSourceFromType,
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        visit_source_remark as visiteSourceRemark,
        max(scene_type) as sceneType,
        record_source_type as recordSourceType,
        comment as comment,
        if(record_source_type = 2, original_price, 0.0) as oweOriginalPrice,
        if(type=-1, -1, 1) * (abs(charge_sheet_total_price) - if(charge_sheet_unit_adjustment_price is null,0.0,charge_sheet_unit_adjustment_price) - if (charge_sheet_adjustment_price &lt; 0, 0.0, charge_sheet_adjustment_price)) as originalPrice,
        if(type=-1, -1, 1) * charge_sheet_deduct_price as deductPrice,
        if(record_source_type = 2,
            original_price,
            if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), received_price, charge_sheet_receivable_price)
        ) as shouldReceivePrice,
        if(type=-1, -1, 1) * if(charge_sheet_adjustment_price is not null and charge_sheet_adjustment_price &lt; 0, charge_sheet_discount_price-charge_sheet_adjustment_price, charge_sheet_discount_price) as discountPrice,
        if(type=-1, -1, 1) * charge_sheet_adjustment_price as adjustmentPrice,
        member_present AS memberPresent,
        member_principal AS memberPrincipal,
        sum((select count(a.product_id) from (
                select product_id
                from ${env}.dwd_charge_transaction_record_v_partition
                where product_id = b.product_id
                and create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
                and import_flag = 0
                and is_deleted = 0
                and product_type!=11
                and chain_id = #{params.chainId}
                <if test="params.clinicId != null and params.clinicId != ''">
                    and clinic_id=#{params.clinicId}
                </if>
                group by product_id
        ) a) )as productCount,
        sum(if(record_source_type = 2, if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price), 0.0)) as discountPriceSum ,
        sum(if(record_source_type = 2, if(type=-1, -1, 1) * record_adjustment_price, 0.0)) as adjustmentPriceSum,
        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
        referral_doctor_id as referralDoctorId,
        air_pharmacy_order_id as airPharmacyOrderId,
        doctor_id as doctorId,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        department_id as doctorDepartmentId,
        consultant_id as consultantId,
        if(type=-1, -1, 1) * if(record_source_type = 2 ,0.0,if(charge_sheet_unit_adjustment_price is null, 0.0, charge_sheet_unit_adjustment_price)) as chargeSheetUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        shebao_extend::text as shebaoExtend,
        if(type=-1, -1, 1) * charge_sheet_verify_price as recordVerifyPrice
        from ${env}.dwd_charge_transaction_record_v_partition b
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and product_type!=11
        <if test='params.hisType == 100'>
            and (product_compose_type != 1 and goods_fee_type in (0,2))
        </if>
        <if test='params.hisType != 100'>
            and goods_fee_type in(0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and chain_id = #{params.chainId}
        and pay_type != 100
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.cashierId != null and params.cashierId != ''">
            and cashier_id = #{params.cashierId}
        </if>
        <if test="params.visitSource1 != null">
            and ${params.visitSource1}
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        group by chainId, clinicId, transactionId, createTime, patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, type, payType, paySubType, departmentId, employeeId, copywriterId,  chargeSheetId, visiteSource1, visiteSource2, visitSourceFromType,patientSourceFromType, patientSourceFrom, visitSourceFrom,visiteSourceRemark,recordSourceType,
                 comment,oweOriginalPrice,originalPrice,deductPrice,shouldReceivePrice,discountPrice,adjustmentPrice,memberPresent,memberPrincipal,referralDoctorId,airPharmacyOrderId,doctorId,doctorDepartmentId,consultantId,chargeSheetUnitermAdjustmentPrice,revisitStatus,recordVerifyPrice,employeeName,shebaoExtend
        order by createTime desc, patientorderNo desc
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectPayModes" resultType="cn.abc.flink.stat.service.cis.selection.entity.PayModeDao">
        select
        distinct pay_type as payMode1,
        if(pay_type=2 and pay_sub_type!=0, 1,
        if(pay_type = 19 and pay_sub_type in (5,6,7,12), 12,
        if(pay_type = 19 and pay_sub_type in (8, 13), 13,
        if(pay_type = 19 and pay_sub_type in (10,14), 14,
        if(pay_type = 5, 0, pay_sub_type))))) as payMode2
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{params.chainId}
        and import_flag = 0
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.cashierId != null and params.cashierId != ''">
            and cashier_id = #{params.cashierId}
        </if>
        and create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
    </select>

    <select id="selectTransactionTotal" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailTotalResp">
        select
        count(1) as count,
        sum(refundAmount) as refundAmount,
        sum(payedAmount) as payedAmount,
        sum(owePayedAmount) as owePayedAmount,
        sum(oweRefundAmount) as oweRefundAmount,
        COALESCE(sum(owePayedAmount), 0.0) - COALESCE(sum(oweRefundAmount), 0.0) as actualOwePayedAmount,
        sum(repayAmount) as repayAmount,
        sum(receivedPrice) as amount
        from
        (
        select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_transaction_id as transactionId,
            create_time as createTime,
            sell_no as sellNo,
            patientorder_no as patientorderNo,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            type as type,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            v2_charge_sheet_id as chargeSheetId,
            <if test='params.hisType == 10'>
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
                patient_source_from_type as visitSourceFromType,
                patient_source_form as visitSourceFrom,
            </if>
            <if test='params.hisType != 10'>
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
                visit_source_from_type as visitSourceFromType,
                visit_source_from as visitSourceFrom,
            </if>
            visit_source_remark as visiteSourceRemark,
            max(scene_type) as sceneType,
            record_source_type as recordSourceType,
            comment as comment,
            if(record_source_type = 2, original_price, 0.0) as oweOriginalPrice,
            if(type=-1, -1, 1) * charge_sheet_deduct_price as deductPrice,
            if(record_source_type = 2,
                original_price,
                if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), received_price, charge_sheet_receivable_price)
            ) as shouldReceivePrice,
            if(type=-1, -1, 1) * charge_sheet_adjustment_price as adjustmentPrice,
            member_present AS memberPresent,
            member_principal AS memberPrincipal,
            <if test='params.hisType == 10'>
                if(type=-1, -1, 1) * abs(charge_sheet_total_price) as originalPrice,
                if(type=-1, -1, 1) * charge_sheet_discount_price as discountPrice,
            </if>
            <if test='params.hisType != 10'>
                if(type=-1, -1, 1) * (abs(charge_sheet_total_price) - if(charge_sheet_unit_adjustment_price is null,0.0,charge_sheet_unit_adjustment_price) - if (charge_sheet_adjustment_price &lt; 0, 0.0, charge_sheet_adjustment_price)) as originalPrice,
                if(type=-1, -1, 1) * if(charge_sheet_adjustment_price is not null and charge_sheet_adjustment_price &lt; 0, charge_sheet_discount_price-charge_sheet_adjustment_price, charge_sheet_discount_price) as discountPrice,
            </if>
            referral_doctor_id as referralDoctorId,
            air_pharmacy_order_id as airPharmacyOrderId,
            if(type=-1, -1, 1) * if(record_source_type = 2 ,0.0,if(charge_sheet_unit_adjustment_price is null, 0.0, charge_sheet_unit_adjustment_price)) as chargeSheetUnitermAdjustmentPrice,
            if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
            pharmacist_id as pharmacistId,
            sum(if(type=-1 and record_source_type = 0, received_price, 0.0)) as refundAmount,
            sum(if(type != -1 and record_source_type = 0, received_price, 0.0)) as payedAmount,
            sum(if(type=-1 and record_source_type = 1, received_price, 0.0)) as oweRefundAmount,
            sum(if(type != -1 and record_source_type = 1, received_price, 0.0)) as owePayedAmount,
            sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice
        from ${env}.dwd_charge_transaction_record_v_partition
            <include refid="transactionWhereSql"/>
        group by chainId, clinicId, transactionId, createTime, sellNo, patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, type,
        payType, paySubType, departmentId, employeeId, employeeName, copywriterId,  chargeSheetId, visiteSource1, visiteSource2, visitSourceFromType,visitSourceFrom,visiteSourceRemark,recordSourceType,comment,oweOriginalPrice,deductPrice,shouldReceivePrice,adjustmentPrice,memberPresent,memberPrincipal,originalPrice,discountPrice,referralDoctorId,airPharmacyOrderId,chargeSheetUnitermAdjustmentPrice,revisitStatus,pharmacistId
        )aa
    </select>
    <select id="selectCountByTime" resultType="java.lang.String">
        SELECT
            jsonb_object_agg(m, cnt) as json_str
        FROM (
                 select
                     to_char(create_time, 'YYYYMM') AS m,
                     COUNT(1) AS cnt
                 from (
                         select
                             create_time
                         from ${env}.dwd_charge_transaction_record_v_partition
                         <if test="type != null and type == 'transaction'">
                             <include refid="transactionWhereSql"/>
                             group by create_time,v2_transaction_id
                         </if>
                         <if test="type != null and type == 'advice'">
                             <include refid="adviceWhereSql"/>
                             group by create_time,v2_transaction_id, product_id
                         </if>
                         <if test="type != null and type == 'classify'">
                             <include refid="classifyListSql"/>
                         </if>
                         <if test="type != null and type == 'item'">
                             <include refid="itemWhereSql"/>
                             group by create_time, if (charge_transaction_record_id is null, id, charge_transaction_record_id)
                         </if>
                         <if test="type != null and type == 'project'">
                             <include refid="projectWhereSql"/>
                             group by create_time, if (charge_transaction_record_id is null, id, charge_transaction_record_id)
                         </if>
                         <if test="type != null and type == 'itemBatch'">
                             <include refid="itemBatchWhereSql"/>
                         </if>
                         <if test="type != null and type == 'projectBatch'">
                             <include refid="projectWhereSql"/>
                         </if>
                      ) a
                 GROUP BY m
            ) b
    </select>
    <sql id="transactionWhereSql">
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and product_type!=11
        <if test='params.hisType == 100'>
            and (product_compose_type != 1 and goods_fee_type in (0,2))
        </if>
        <if test='params.hisType != 100'>
            and goods_fee_type in(0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and chain_id = #{params.chainId}
        and pay_type != 100
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.cashierId != null and params.cashierId != ''">
            and cashier_id = #{params.cashierId}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.visitSource1 != null">
            and ${params.visitSource1}
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test="params.sellNo != null and params.sellNo != ''">
            and sell_no = #{params.sellNo}
        </if>
    </sql>


    <select id="selectClassify" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailClassifyDao">
        select * from (
        <if test="params.fee1 != null or params.fee2 == null">
            select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_charge_sheet_id as chargeSheetId,
            v2_transaction_id as transactionId,
            v2_patient_order_id as patientOrderId,
            create_time as createTime,
            sell_no as sellNo,
            patientorder_no as patientorderNo,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            type as type,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            null as classifyLevel2,
            <if test='params.hisType == 10'>
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
                patient_source_from_type as visitSourceFromType,
                patient_source_form as visitSourceFrom,
            </if>
            <if test='params.hisType != 10'>
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
                visit_source_from_type as visitSourceFromType,
                visit_source_from as visitSourceFrom,
            </if>
            visit_source_remark as visiteSourceRemark,
            scene_type as sceneType,
            record_source_type as recordSourceType,
            charge_sheet_remark as chargeSheetRemark,
            comment as comment,
            <if test='params.hisType == 10'>
                sum(if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)) as discountPrice ,
            </if>
            <if test='params.hisType != 10'>
                sum(
                if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)
            ) as discountPrice ,
            </if>
            sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
            sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
            sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
            sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), abs(received_price), original_price)) as unitPrice,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(deduct_promotion_price* if(type=-1, -1, 1)) as deductPrice,
            referral_doctor_id as referralDoctorId,
            air_pharmacy_order_id as airPharmacyOrderId,
            sum(record_unit_adjustment_price * if(type=-1, -1, 1)) as recordUnitermAdjustmentPriceSum,
            if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
            pharmacist_id as pharmacistId,
            cooperation_clinic_id as cooperationClinicId,
            member_id as memberId,
            shebao_extend::text as shebaoExtend,
            sum(if(type=-1, -1, 1) * record_verify_price) as recordVerifyPrice,
            sum(if(replace(discount_info, '\', '')::json ->> 'oddFee' is not null, (replace(discount_info, '\', '')::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and import_flag = 0
            and is_deleted = 0
            and goods_fee_type in(0,1)
            and chain_id = #{params.chainId}
            and pay_type != 100
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null">
                and ${params.visitSource1}
            </if>
            <if test="params.fee1 != null">
                and ${params.fee1}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
            <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
                and cooperation_clinic_id = #{params.cooperationClinicId}
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            <if test="params.sellNo != null and params.sellNo != ''">
                and sell_no = #{params.sellNo}
            </if>
            group by chainId, clinicId, chargeSheetId, transactionId, patientOrderId, createTime, sellNo,patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, type, payType, paySubType, departmentId, employeeId, copywriterId,  visiteSource1, visiteSource2, visitSourceFromType,visitSourceFrom,visiteSourceRemark, classifyLevel1, sceneType, recordSourceType,comment,referralDoctorId,airPharmacyOrderId,revisitStatus,pharmacistId, chargeSheetRemark,cooperationClinicId,employeeName,memberId,shebaoExtend
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            union all
        </if>
        <if test="params.fee2 != null">
            select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_charge_sheet_id as chargeSheetId,
            v2_transaction_id as transactionId,
            v2_patient_order_id as patientOrderId,
            create_time as createTime,
            sell_no as sellNo,
            patientorder_no as patientorderNo,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            type as type,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            COALESCE(classify_level_2_id, 0) as classifyLevel2,
            <if test='params.hisType == 10'>
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
                patient_source_from_type as visitSourceFromType,
                patient_source_form as visitSourceFrom,
            </if>
            <if test='params.hisType != 10'>
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
                visit_source_from_type as visitSourceFromType,
                visit_source_from as visitSourceFrom,
            </if>
            visit_source_remark as visiteSourceRemark,
            scene_type as sceneType,
            record_source_type as recordSourceType,
            charge_sheet_remark as chargeSheetRemark,
            comment as comment,
            <if test='params.hisType == 10'>
                sum(if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)) as discountPrice ,
            </if>
            <if test='params.hisType != 10'>
                sum(
                if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)
            ) as discountPrice ,
            </if>
            sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
            sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
            sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
            sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), abs(received_price), original_price)) as unitPrice,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(deduct_promotion_price* if(type=-1, -1, 1)) as deductPrice,
            referral_doctor_id as referralDoctorId,
            air_pharmacy_order_id as airPharmacyOrderId,
            sum(record_unit_adjustment_price * if(type=-1, -1, 1)) as recordUnitermAdjustmentPriceSum,
            if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
            pharmacist_id as pharmacistId,
            cooperation_clinic_id as cooperationClinicId,
            member_id as memberId,
            shebao_extend::text as shebaoExtend,
            sum(if(type=-1, -1, 1) * record_verify_price) as recordVerifyPrice,
            sum(if(replace(discount_info, '\', '')::json ->> 'oddFee' is not null, (replace(discount_info, '\', '')::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and import_flag = 0
            and is_deleted = 0
            and goods_fee_type in(0,1)
            and chain_id = #{params.chainId}
            and pay_type != 100
            and ${params.fee2}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
            <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
                and cooperation_clinic_id = #{params.cooperationClinicId}
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            <if test="params.sellNo != null and params.sellNo != ''">
                and sell_no = #{params.sellNo}
            </if>
            group by chainId, clinicId, chargeSheetId,transactionId, patientOrderId, createTime, sellNo,patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, type, payType, paySubType, departmentId, employeeId, copywriterId,  classifyLevel1,classifyLevel2,visiteSource1, visiteSource2, visitSourceFromType,visitSourceFrom,visiteSourceRemark, sceneType, recordSourceType,comment,referralDoctorId,airPharmacyOrderId,revisitStatus,pharmacistId,chargeSheetRemark,cooperationClinicId,employeeName,memberId,shebaoExtend
        </if>
        ) z
        <if test="params.size == null or params.size == 0">
            order by createTime desc, patientorderNo desc
        </if>
        <if test="params.size != null and params.size != 0">
            order by createTime desc, patientorderNo desc, classifyLevel1
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectClassifyCavity" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailClassifyDao">
        /*+MAX_WHERE_ITEMS_COUNT = 512*/
        select * from (
        <if test="params.fee1 != null or params.fee2 == null">
            select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_charge_sheet_id as chargeSheetId,
            v2_transaction_id as transactionId,
            v2_patient_order_id as patientOrderId,
            create_time as createTime,
            patientorder_no as patientorderNo,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            type as type,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            null as classifyLevel2,
            <if test='params.hisType == 10'>
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
                patient_source_from_type as visitSourceFromType,
                patient_source_form as visitSourceFrom,
            </if>
            <if test='params.hisType != 10'>
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
                visit_source_from_type as visitSourceFromType,
                visit_source_from as visitSourceFrom,
            </if>
            visit_source_remark as visiteSourceRemark,
            scene_type as sceneType,
            record_source_type as recordSourceType,
            comment as comment,
            sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
            sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
            sum(
                if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)
            ) as discountPrice ,
            sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
            sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), abs(received_price), original_price)) as unitPrice,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(deduct_promotion_price* if(type=-1, -1, 1)) as deductPrice,
            referral_doctor_id as referralDoctorId,
            air_pharmacy_order_id as airPharmacyOrderId,
            doctor_id as indicationsDoctorId,
            department_id as doctorDepartmentId,
            consultant_id as consultantId,
            shebao_extend::text as shebaoExtend,
            sum(record_unit_adjustment_price * if(type=-1, -1, 1)) as recordUnitermAdjustmentPriceSum,
            if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
            sum(if(type=-1, -1, 1) * record_verify_price) as recordVerifyPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and import_flag = 0
            and is_deleted = 0
            and goods_fee_type in(0,1)
            and chain_id = #{params.chainId}
            and pay_type != 100
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null">
                and ${params.visitSource1}
            </if>
            <if test="params.fee1 != null">
                and ${params.fee1}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            group by chainId, clinicId, chargeSheetId,transactionId, patientOrderId, createTime,patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, type, payType, paySubType, departmentId, employeeId, copywriterId,  visiteSource1, visiteSource2, visitSourceFromType,visitSourceFrom,visiteSourceRemark, classifyLevel1, sceneType, recordSourceType,comment,referralDoctorId,airPharmacyOrderId,indicationsDoctorId,doctorDepartmentId,consultantId,revisitStatus,employeeName,shebaoExtend
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            union all
        </if>
        <if test="params.fee2 != null">
            select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_charge_sheet_id as chargeSheetId,
            v2_transaction_id as transactionId,
            v2_patient_order_id as patientOrderId,
            create_time as createTime,
            patientorder_no as patientorderNo,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            type as type,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            COALESCE(classify_level_2_id, 0) as classifyLevel2,
            <if test='params.hisType == 10'>
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
                patient_source_from_type as visitSourceFromType,
                patient_source_form as visitSourceFrom,
            </if>
            <if test='params.hisType != 10'>
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
                visit_source_from_type as visitSourceFromType,
                visit_source_from as visitSourceFrom,
            </if>
            visit_source_remark as visiteSourceRemark,
            scene_type as sceneType,
            record_source_type as recordSourceType,
            comment as comment,
            sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
            sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
            sum(
                if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)
            ) as discountPrice ,
            sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
            sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), abs(received_price), original_price)) as unitPrice,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(deduct_promotion_price* if(type=-1, -1, 1)) as deductPrice,
            referral_doctor_id as referralDoctorId,
            air_pharmacy_order_id as airPharmacyOrderId,
            doctor_id as indicationsDoctorId,
            department_id as doctorDepartmentId,
            consultant_id as consultantId,
            shebao_extend::text as shebaoExtend,
            sum(record_unit_adjustment_price * if(type=-1, -1, 1)) as recordUnitermAdjustmentPriceSum,
            if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
            sum(if(type=-1, -1, 1) * record_verify_price) as recordVerifyPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and import_flag = 0
            and is_deleted = 0
            and goods_fee_type in(0,1)
            and chain_id = #{params.chainId}
            and pay_type != 100
            and ${params.fee2}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            group by chainId, clinicId, chargeSheetId,transactionId, patientOrderId, createTime,patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, type, payType, paySubType, departmentId, employeeId, copywriterId,  classifyLevel1,classifyLevel2,visiteSource1, visiteSource2, visitSourceFromType,visitSourceFrom,visiteSourceRemark, sceneType, recordSourceType,comment,referralDoctorId,airPharmacyOrderId,indicationsDoctorId,doctorDepartmentId,consultantId,revisitStatus,employeeName,shebaoExtend
        </if>
        ) z
        <if test="params.size == null or params.size == 0">
            order by createTime desc, patientorderNo desc
        </if>
        <if test="params.size != null and params.size != 0">
            order by createTime desc, patientorderNo desc, classifyLevel1
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectAdvice" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailClassifyDao">
            select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_charge_sheet_id as chargeSheetId,
            v2_transaction_id as transactionId,
            v2_patient_order_id as patientOrderId,
            create_time as createTime,
            patientorder_no as patientorderNo,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            charge_sheet_type as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            type as type,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            classify_level_2_id as classifyLevel2,
            <if test="params.hisType == 10">
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
                patient_source_form as visitSourceFrom,
            </if>
            <if test="params.hisType != 10">
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
                visit_source_from as visitSourceFrom,
            </if>
            visit_source_from_type as visitSourceFromType,
            patient_source_from_type as patientSourceFromType,
            patient_source_form as patientSourceFrom,
            visit_source_remark as visiteSourceRemark,
            scene_type as sceneType,
            record_source_type as recordSourceType,
            comment as comment,
            sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
            sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
            sum(
                if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)
            ) as discountPrice ,
            sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
            sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), abs(received_price), original_price)) as unitPrice,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(deduct_promotion_price* if(type=-1, -1, 1)) as deductPrice,
            referral_doctor_id as referralDoctorId,
            if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0) as count,
            product_unit as unit,
            if(type=-1, -1, 1) * abs(product_unit_price) as price,
            doctor_id as doctorId,
            product_id as productId,
            shebao_extend::text as shebaoExtend,
            sum(record_unit_adjustment_price * if(type=-1, -1, 1)) as recordUnitermAdjustmentPriceSum,
            if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                <!-- 不要套餐母项数据和医嘱下面的收费项 -->
                and not (product_compose_type = 1 or goods_fee_type = 2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and ((product_compose_type in(0,1) and goods_fee_type in (0,1)))
            </if>
            and import_flag = 0
            and chain_id = #{params.chainId}
            and pay_type != 100
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id=#{params.productId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null">
                and ${params.visitSource1}
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 == null and params.fee2 != null">
                and ${params.fee2}
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
            group by chainId, clinicId, chargeSheetId, transactionId, patientOrderId, createTime, patientorderNo, patientId, wxOrderId,
        chargeSheetType, retailType, cashierId, type, payType, paySubType, departmentId, employeeId, copywriterId,
        classifyLevel1, classifyLevel2, visiteSource1, visiteSource2, visitSourceFrom, visitSourceFromType, patientSourceFromType, patientSourceFrom,
        visiteSourceRemark, sceneType, recordSourceType, comment, referralDoctorId,count, unit,price, doctorId, productId,shebaoExtend,
        revisitStatus,employeeName
        <if test="params.size == null or params.size == 0">
            order by createTime desc, patientorderNo desc
        </if>
        <if test="params.size != null and params.size != 0">
            order by createTime desc, patientorderNo desc, classifyLevel1
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectClassifyTotal" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailTotalResp">
        select
            count(1) as count,
            sum(refundAmount) as refundAmount,
            sum(payedAmount) as payedAmount,
            sum(owePayedAmount) as owePayedAmount,
            sum(oweRefundAmount) as oweRefundAmount,
            COALESCE(sum(owePayedAmount), 0.0) - COALESCE(sum(oweRefundAmount), 0.0) as actualOwePayedAmount,
            sum(repayAmount) as repayAmount,
            sum(receivedPrice) as amount
        from
        (
            <include refid="classifyListSql" />
       )zz
    </select>
    <sql id="classifyListSql">
        <if test="params.fee1 != null or params.fee2 == null">
            select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_transaction_id as transactionId,
            create_time as createTime,
            patientorder_no as patientorderNo,
            v2_patient_order_id as patientOrderId,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            charge_sheet_type as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            null as classifyLevel2,
            <if test="params.hisType == 10">
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                COALESCE(if(patient_source_id_2='0', null, patient_source_id_2), patient_source_form) as visiteSource2,
            </if>
            <if test="params.hisType != 10">
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                COALESCE(if(visit_source_level_2_id='0', null, visit_source_level_2_id), visit_source_from) as visiteSource2,
            </if>
            visit_source_remark as visiteSourceRemark,
            comment as comment,
            sum(if(type=-1 and record_source_type = 0, received_price, 0.0)) as refundAmount,
            sum(if(type != -1 and record_source_type = 0, received_price, 0.0)) as payedAmount,
            sum(if(type=-1 and record_source_type = 1, received_price, 0.0)) as oweRefundAmount,
            sum(if(type != -1 and record_source_type = 1, received_price, 0.0)) as owePayedAmount,
            sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            referral_doctor_id as referralDoctorId
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and import_flag = 0
            and goods_fee_type in(0,1)
            and chain_id = #{params.chainId}
            and pay_type != 100
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.fee1 != null">
                and ${params.fee1}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            <if test="params.sellNo != null and params.sellNo != ''">
                and sell_no = #{params.sellNo}
            </if>
            group by chainId, clinicId, transactionId, createTime, patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, payType, paySubType, departmentId, employeeId, copywriterId,  visiteSource1, visiteSource2, visiteSourceRemark, classifyLevel1, comment,referralDoctorId,visit_source_from_type,visit_source_from, record_source_type, scene_type,employeeName,patientOrderId
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            union all
        </if>
        <if test="params.fee2 != null">
            select
            chain_id as chainId,
            clinic_id as clinicId,
            v2_transaction_id as transactionId,
            create_time as createTime,
            patientorder_no as patientorderNo,
            v2_patient_order_id as patientOrderId,
            patient_id as patientId,
            replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
            charge_sheet_type as chargeSheetType,
            cashier_id as cashierId,
            retail_type as retailType,
            pay_type as payType,
            pay_sub_type as paySubType,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            copywriter_id as copywriterId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            COALESCE(classify_level_2_id, 0) as classifyLevel2,
            <if test="params.hisType == 10">
                if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                COALESCE(if(patient_source_id_2='0', null, patient_source_id_2), patient_source_form) as visiteSource2,
            </if>
            <if test="params.hisType != 10">
                if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                COALESCE(if(visit_source_level_2_id='0', null, visit_source_level_2_id), visit_source_from) as visiteSource2,
            </if>
            visit_source_remark as visiteSourceRemark,
            comment as comment,
            sum(if(type=-1 and pay_type != 20, received_price, 0.0)) as refundAmount,
            sum(if(type != -1 and pay_type != 20, received_price, 0.0)) as payedAmount,
            sum(if(type=-1 and pay_type = 20, received_price, 0.0)) as oweRefundAmount,
            sum(if(type != -1 and pay_type = 20, received_price, 0.0)) as owePayedAmount,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
            referral_doctor_id as referralDoctorId
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and import_flag = 0
            and goods_fee_type in(0,1)
            and chain_id = #{params.chainId}
            and pay_type != 100
            and ${params.fee2}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            <if test="params.sellNo != null and params.sellNo != ''">
                and sell_no = #{params.sellNo}
            </if>
            group by chainId, clinicId, transactionId, createTime, patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId, payType, paySubType, departmentId, employeeId, copywriterId,  visiteSource1, visiteSource2, visiteSourceRemark, classifyLevel1,classifyLevel2, comment,referralDoctorId,visit_source_from_type,visit_source_from, record_source_type, scene_type,employeeName,patientOrderId
        </if>
    </sql>


    <select id="selectAdviceTotal" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailTotalResp">
        select
            count(1) as count,
            sum(refundAmount) as refundAmount,
            sum(payedAmount) as payedAmount,
            sum(owePayedAmount) as owePayedAmount,
            sum(oweRefundAmount) as oweRefundAmount,
            COALESCE(sum(owePayedAmount), 0.0) - COALESCE(sum(oweRefundAmount), 0.0) as actualOwePayedAmount,
            sum(repayAmount) as repayAmount,
            sum(receivedPrice) as amount
        from (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                v2_charge_sheet_id as chargeSheetId,
                v2_transaction_id as transactionId,
                create_time as createTime,
                patientorder_no as patientorderNo,
                patient_id as patientId,
                replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
                charge_sheet_type as chargeSheetType,
                cashier_id as cashierId,
                retail_type as retailType,
                type as type,
                pay_type as payType,
                pay_sub_type as paySubType,
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
                if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
                copywriter_id as copywriterId,
                COALESCE(classify_level_1_id, '0') as classifyLevel1,
                classify_level_2_id as classifyLevel2,
                <if test="params.hisType == 10">
                    if(patient_source_id_1='0', null, patient_source_id_1) as visiteSource1,
                    if(patient_source_id_2='0', null, patient_source_id_2) as visiteSource2,
                    patient_source_form as visitSourceFrom,
                </if>
                <if test="params.hisType != 10">
                    if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visiteSource1,
                    if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visiteSource2,
                    visit_source_from as visitSourceFrom,
                </if>
                visit_source_from_type as visitSourceFromType,
                visit_source_remark as visiteSourceRemark,
                scene_type as sceneType,
                record_source_type as recordSourceType,
                comment as comment,
                referral_doctor_id as referralDoctorId,
                product_unit as unit,
                if(type=-1, -1, 1) * abs(product_unit_price) as price,
                doctor_id as doctorId,
                product_id as productId,
                sum(if(type=-1 and record_source_type = 0, received_price, 0.0)) as refundAmount,
                sum(if(type != -1 and record_source_type = 0, received_price, 0.0)) as payedAmount,
                sum(if(type=-1 and record_source_type = 1, received_price, 0.0)) as oweRefundAmount,
                sum(if(type != -1 and record_source_type = 1, received_price, 0.0)) as owePayedAmount,
                sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice
            from ${env}.dwd_charge_transaction_record_v_partition
                <include refid="adviceWhereSql"/>
        group by chainId, clinicId, chargeSheetId,transactionId, createTime, patientorderNo, patientId, wxOrderId, chargeSheetType, retailType, cashierId,type,payType, paySubType, departmentId, employeeId, copywriterId,  classifyLevel1,classifyLevel2,visiteSource1, visiteSource2, visitSourceFrom,visitSourceFromType,visiteSourceRemark, sceneType, recordSourceType,comment,referralDoctorId, unit,price,doctorId, productId,employeeName
        <if test="params.size == null or params.size == 0">
            order by createTime desc, patientorderNo desc
        </if>
        ) zz
    </select>
    <sql id="adviceWhereSql">
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                <!-- 不要套餐母项数据和医嘱下面的收费项 -->
                and not (product_compose_type = 1 or goods_fee_type = 2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and ((product_compose_type in(0,1) and goods_fee_type in (0,1)))
            </if>
            and import_flag = 0
            and chain_id = #{params.chainId}
            and pay_type != 100
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id=#{params.productId}
            </if>
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null">
                and ${params.visitSource1}
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 == null and params.fee2 != null">
                and ${params.fee2}
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                ${params.searchDoctorSql}
            </if>
            <if test="params.revisitStatus != null">
                and revisit_status = #{params.revisitStatus}
            </if>
            <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
                and referral_doctor_id = #{params.referralDoctorId}
            </if>
            <if test="params.billDepartment != null and params.billDepartment != ''">
                and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
            </if>
    </sql>


    <select id="selectItems" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailItemsDao">
        select
        max(id) as id,
        product_type as productType,
        product_sub_type as productSubType,
        product_custom_type as productCustomType,
        chain_id as chainId,
        clinic_id as clinicId,
        v2_charge_sheet_id as chargeSheetId,
        v2_charge_form_item_id as chargeFormItemId,
        v2_transaction_id as transactionId,
        create_time as createTime,
        sell_no as sellNo,
        patientorder_no as patientorderNo,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        patient_id as patientId,
        replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
        if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
        cashier_id as cashierId,
        product_id as goodsId,
        retail_type as retailType,
        type as refundType,
        pay_type as payType,
        pay_sub_type as paySubType,
        product_unit as unit,
        <if test='params.hisType == 10'>
            sum(if(type=-1, -1, 1) * (received_price - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)) - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0))) as amount,
            sum(if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)) as discountPrice ,
        </if>
        <if test='params.hisType != 10'>
            sum(if(type=-1, -1, 1) * (received_price - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)) - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0))) as amount,
            sum(if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)) as discountPrice ,
        </if>
        min(if(type=-1, -1, 1) * abs(product_unit_price)) as unitPrice,
        sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0)) as unitCount,
        sum(if(type=-1, -1, 1) * received_price) as receivedPrice,
        sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
        sum(if(type=-1, -original_price, original_price)) as originPrice,
        sum(if(type=-1, -1, 1) * deduct_promotion_price) as deductPrice,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
        item_nurse_id as itemNurseId,
        copywriter_id as copywriterId,
        COALESCE(classify_level_1_id, '0') as classifyLevel1,
        COALESCE(classify_level_2_id, 0) as classifyLevel2,
        sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
        sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
        <if test='params.hisType == 10'>
            if(patient_source_id_1='0', null, patient_source_id_1) as visitSourceId1,
            if(patient_source_id_2='0', null, patient_source_id_2) as visitSourceId2,
            patient_source_from_type as visitSourceFromType,
            patient_source_form as visitSourceFrom,
        </if>
        <if test='params.hisType != 10'>
            if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visitSourceId1,
            if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visitSourceId2,
            visit_source_from_type as visitSourceFromType,
            visit_source_from as visitSourceFrom,
        </if>
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        visit_source_remark as visitSourceRemark,
        if(patient_source_id_1='0', null, patient_source_id_1) as patientSourceId1,
        if(patient_source_id_2='0', null, patient_source_id_2) as patientSourceId2,
        scene_type as sceneType,
        record_source_type as recordSourceType,
        charge_sheet_remark as chargeSheetRemark,
        comment,
        referral_doctor_id as referralDoctorId,
        air_pharmacy_order_id as airPharmacyOrderId,
        sum(if(type=-1, -1, 1) * record_unit_adjustment_price) as recordUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        charge_form_item_remark as chargeFormItemRemark,
        pharmacist_id as pharmacistId,
        v2_charge_form_item_id as v2ChargeFormItemId,
        cooperation_clinic_id as cooperationClinicId,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        member_id as memberId,
        profit_category_type as profitCategoryType,
        shebao_extend::text as shebaoExtend,
        sum(if(type=-1, -1, 1) * record_verify_price) as recordVerifyPrice,
        sum(if(replace(discount_info, '\', '')::json ->> 'oddFee' is not null, (replace(discount_info, '\', '')::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
        from ${env}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            and product_compose_type in (0,2)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and goods_fee_type in(0,1)
        <if test="params.isCharge == 0">
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            and product_type != 18
            and record_type not in (2, 3)
            <if test="params.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="params.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
        </if>
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.departmentId != null and params.departmentId != "" and params.departmentId != "00000000000000000000000000000000"'>
            and department_id = #{params.departmentId}
        </if>
        <if test='params.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '')
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
            and cooperation_clinic_id = #{params.cooperationClinicId}
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.productIds != null ">
            and product_id in
            <foreach collection="params.productIds" item="productId" open="(" close=")" separator=",">
                #{params.productId}
            </foreach>
        </if>
        <if test="params.tagIdSql != null and params.tagIdSql != ''">
            and ${params.tagIdSql}
        </if>
        <if test="params.profitCategoryTypeIdSql != null and params.profitCategoryTypeIdSql != ''">
            and ${params.profitCategoryTypeIdSql}
        </if>
        <if test="params.sellNo != null and params.sellNo != ''">
            and sell_no = #{params.sellNo}
        </if>
        group by productType,productSubType,productCustomType,chainId,clinicId,chargeSheetId,chargeFormItemId,transactionId,createTime,sellNo,patientorderNo,patientOrderId,type,patientId,wxOrderId,
        charge_sheet_type,cashierId,goodsId,retailType,refundType,payType,paySubType,unit,departmentId,employeeId,itemDoctorId,itemDoctorDepartmentId,itemNurseId,copywriterId,
                 classifyLevel1,classifyLevel2,visitSourceId1,visitSourceId2,visitSourceFromType,visitSourceFrom,patientSourceFromType, patientSourceFrom,visitSourceRemark,patientSourceId1,patientSourceId2,sceneType,recordSourceType,comment,referralDoctorId,airPharmacyOrderId,revisitStatus,charge_form_item_remark,pharmacistId
          ,if (charge_transaction_record_id is null, id, charge_transaction_record_id),v2_charge_form_item_id,chargeSheetRemark,cooperationClinicId,employeeName,memberId,profitCategoryType,shebaoExtend
        order by createTime desc, patientorderNo desc, goodsId, if (charge_transaction_record_id is null, id, charge_transaction_record_id)
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectBatchItems" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailItemsDao">
        select
        id,
        product_type as productType,
        product_sub_type as productSubType,
        product_custom_type as productCustomType,
        chain_id as chainId,
        clinic_id as clinicId,
        v2_charge_sheet_id as chargeSheetId,
        v2_charge_form_item_id as chargeFormItemId,
        v2_transaction_id as transactionId,
        create_time as createTime,
        sell_no as sellNo,
        patientorder_no as patientorderNo,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        patient_id as patientId,
        pay_third_party_extra_info::json->>'thirdPartyTransactionId' as wxOrderId,
        if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
        cashier_id as cashierId,
        product_id as goodsId,
        retail_type as retailType,
        type as refundType,
        pay_type as payType,
        pay_sub_type as paySubType,
        product_unit as unit,
        <if test='params.hisType == 10'>
            if(type=-1, -1, 1) * (received_price - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)) - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0)) as amount,
            if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0) as discountPrice ,
        </if>
        <if test='params.hisType != 10'>
            if(type=-1, -1, 1) * (received_price - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)) - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0)) as amount,
            if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0) as discountPrice ,
        </if>
        if(type=-1, -1, 1) * abs(if (product_batch_unit_price is null, product_unit_price, product_batch_unit_price)) as unitPrice,
        if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0) as unitCount,
        if(type=-1, -1, 1) * received_price as receivedPrice,
        if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0) as adjustmentPrice,
        if(type=-1, -original_price, original_price) as originPrice,
        if(type=-1, -1, 1) * deduct_promotion_price as deductPrice,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
        item_nurse_id as itemNurseId,
        copywriter_id as copywriterId,
        COALESCE(classify_level_1_id, '0') as classifyLevel1,
        COALESCE(classify_level_2_id, 0) as classifyLevel2,
        if(type=-1, -1, 1) * record_member_present AS memberPresent,
        if(type=-1, -1, 1) * record_member_principal AS memberPrincipal,
        <if test='params.hisType == 10'>
            if(patient_source_id_1='0', null, patient_source_id_1) as visitSourceId1,
            if(patient_source_id_2='0', null, patient_source_id_2) as visitSourceId2,
            patient_source_from_type as visitSourceFromType,
            patient_source_form as visitSourceFrom,
        </if>
        <if test='params.hisType != 10'>
            if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visitSourceId1,
            if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visitSourceId2,
            visit_source_from_type as visitSourceFromType,
            visit_source_from as visitSourceFrom,
        </if>
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        visit_source_remark as visitSourceRemark,
        if(patient_source_id_1='0', null, patient_source_id_1) as patientSourceId1,
        if(patient_source_id_2='0', null, patient_source_id_2) as patientSourceId2,
        scene_type as sceneType,
        record_source_type as recordSourceType,
        charge_sheet_remark as chargeSheetRemark,
        comment,
        referral_doctor_id as referralDoctorId,
        air_pharmacy_order_id as airPharmacyOrderId,
        if(type=-1, -1, 1) * record_unit_adjustment_price as recordUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        charge_form_item_remark as chargeFormItemRemark,
        batch_id as batchId,
        pharmacist_id as pharmacistId,
        v2_charge_form_item_id as v2ChargeFormItemId,
        cooperation_clinic_id as cooperationClinicId,
        if(type=-1, -1, 1) * record_verify_price as recordVerifyPrice,
        member_id as memberId,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        profit_category_type as profitCategoryType,
        if(replace(discount_info, '\', '')::json ->> 'oddFee' is not null, (replace(discount_info, '\', '')::json ->> 'oddFee')::numeric, 0.0) as roundedAmount,
        shebao_extend::text as shebaoExtend
        from ${env}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            and product_compose_type in (0,2)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and goods_fee_type in(0,1)
        <if test="params.isCharge == 0">
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            and product_type != 18
            and record_type not in (2, 3)
            <if test="params.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="params.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
        </if>
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.departmentId != null and params.departmentId != "" and params.departmentId != "00000000000000000000000000000000"'>
            and department_id = #{params.departmentId}
        </if>
        <if test='params.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '')
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
            and cooperation_clinic_id = #{params.cooperationClinicId}
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test='params.hisType == null or params.hisType == ""'>
            <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
            </if>
            <if test='params.doctorId == "00000000000000000000000000000000"'>
                and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
            </if>
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.productIds != null ">
            and product_id in
            <foreach collection="params.productIds" item="productId" open="(" close=")" separator=",">
                #{params.productId}
            </foreach>
        </if>
        <if test="params.tagIdSql != null and params.tagIdSql != ''">
            and ${params.tagIdSql}
        </if>
        <if test="params.beginExpiryDate != null and params.beginExpiryDate != '' and params.endExpiryDate != null and params.endExpiryDate != ''">
            and expiry_date BETWEEN #{params.beginExpiryDate} and #{params.endExpiryDate}
        </if>
        <if test="params.profitCategoryTypeIdSql != null and params.profitCategoryTypeIdSql != ''">
            and ${params.profitCategoryTypeIdSql}
        </if>
        <if test="params.sellNo != null and params.sellNo != ''">
            and sell_no = #{params.sellNo}
        </if>
        order by createTime desc, patientorderNo desc, goodsId, id
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectItemsCavity" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailItemsDao">
        select
        product_type as productType,
        product_sub_type as productSubType,
        product_custom_type as productCustomType,
        chain_id as chainId,
        clinic_id as clinicId,
        v2_charge_sheet_id as chargeSheetId,
        v2_transaction_id as transactionId,
        create_time as createTime,
        patientorder_no as patientorderNo,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        patient_id as patientId,
        pay_third_party_extra_info::json->>'thirdPartyTransactionId' as wxOrderId,
        if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
        cashier_id as cashierId,
        product_id as goodsId,
        retail_type as retailType,
        type as refundType,
        pay_type as payType,
        pay_sub_type as paySubType,
        product_unit as unit,
        min(if(type=-1, -1, 1) * abs(product_unit_price)) as unitPrice,
        sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0)) as unitCount,
        sum(if(type=-1, -1, 1) * (
        received_price
        - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price))
        - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0)
        )) as amount,
        sum(if(type=-1, -1, 1) * received_price) as receivedPrice,
        sum(if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)) as discountPrice ,
        sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
        sum(if(type=-1, -original_price, original_price)) as originPrice,
        sum(if(type=-1, -1, 1) * deduct_promotion_price) as deductPrice,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), COALESCE(employee_snaps ->> '$.doctorName', ''), null) as itemDoctorName,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
        item_nurse_id as itemNurseId,
        copywriter_id as copywriterId,
        COALESCE(classify_level_1_id, '0') as classifyLevel1,
        COALESCE(classify_level_2_id, 0) as classifyLevel2,
        sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
        sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
        if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visitSourceId1,
        if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visitSourceId2,
        visit_source_from_type as visitSourceFromType,
        visit_source_from as visitSourceFrom,
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        visit_source_remark as visitSourceRemark,
        if(patient_source_id_1='0', null, patient_source_id_1) as patientSourceId1,
        if(patient_source_id_2='0', null, patient_source_id_2) as patientSourceId2,
        scene_type as sceneType,
        record_source_type as recordSourceType,
        comment,
        referral_doctor_id as referralDoctorId,
        air_pharmacy_order_id as airPharmacyOrderId,
        doctor_id as indicationsDoctorId,
        department_id as doctorDepartmentId,
        consultant_id as consultantId,
        sum(if(type=-1, -1, 1) * record_unit_adjustment_price) as recordUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        charge_form_item_remark as chargeFormItemRemark,
        v2_charge_form_item_id as v2ChargeFormItemId,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        shebao_extend::text as shebaoExtend,
        sum(if(type=-1, -1, 1) * record_verify_price) as recordVerifyPrice
        from ${env}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            and product_compose_type in (0,2)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and goods_fee_type in(0,1)
        <if test="params.isCharge == 0">
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            and product_type != 18
            and record_type not in (2, 3)
            <if test="params.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="params.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
        </if>
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.departmentId != null and params.departmentId != "" and params.departmentId != "00000000000000000000000000000000"'>
            and department_id = #{params.departmentId}
        </if>
        <if test='params.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '')
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test='params.hisType == null or params.hisType == ""'>
            <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
            </if>
            <if test='params.doctorId == "00000000000000000000000000000000"'>
                and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
            </if>
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.productIds != null ">
            and product_id in
            <foreach collection="params.productIds" item="productId" open="(" close=")" separator=",">
                #{params.productId}
            </foreach>
        </if>
        <if test="params.tagIdSql != null and params.tagIdSql != ''">
            and ${params.tagIdSql}
        </if>
        group by productType,productSubType,productCustomType,chainId,clinicId,chargeSheetId,transactionId,createTime,patientorderNo,patientOrderId,type,patientId,wxOrderId,
                 chargeSheetType,cashierId,goodsId,retailType,refundType,payType,paySubType,unit,departmentId,employeeId,itemDoctorId,itemDoctorName,itemDoctorDepartmentId,itemNurseId,copywriterId,
                 classifyLevel1,classifyLevel2,visitSourceId1,visitSourceId2,visitSourceFromType,visitSourceFrom,patientSourceFromType, patientSourceFrom,visitSourceRemark,patientSourceId1,patientSourceId2,sceneType,recordSourceType,comment,referralDoctorId,airPharmacyOrderId,indicationsDoctorId,doctorDepartmentId
          ,consultantId,revisitStatus,charge_form_item_remark,if (charge_transaction_record_id is null, id, charge_transaction_record_id),v2_charge_form_item_id,charge_sheet_type,employeeName,shebaoExtend
        order by createTime desc, patientorderNo desc, goodsId, if (charge_transaction_record_id is null, id, charge_transaction_record_id)
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectBatchItemsCavity" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailItemsDao">
        select
        product_type as productType,
        product_sub_type as productSubType,
        product_custom_type as productCustomType,
        chain_id as chainId,
        clinic_id as clinicId,
        v2_charge_sheet_id as chargeSheetId,
        v2_transaction_id as transactionId,
        create_time as createTime,
        patientorder_no as patientorderNo,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        patient_id as patientId,
        replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
        if (air_pharmacy_order_id is not null and air_pharmacy_order_id != 0, 100, charge_sheet_type) as chargeSheetType,
        cashier_id as cashierId,
        product_id as goodsId,
        retail_type as retailType,
        type as refundType,
        pay_type as payType,
        pay_sub_type as paySubType,
        product_unit as unit,
        if(type=-1, -1, 1) * abs(if (product_batch_unit_price is null, product_unit_price, product_batch_unit_price)) as unitPrice,
        if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0) as unitCount,
        if(type=-1, -1, 1) * (
        received_price
        - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price))
        - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0)
        ) as amount,
        if(type=-1, -1, 1) * received_price as receivedPrice,
        if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0) as discountPrice ,
        if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0) as adjustmentPrice,
        if(type=-1, -original_price, original_price) as originPrice,
        if(type=-1, -1, 1) * deduct_promotion_price as deductPrice,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), COALESCE(employee_snaps ->> '$.doctorName', ''), null) as itemDoctorName,
        item_nurse_id as itemNurseId,
        copywriter_id as copywriterId,
        COALESCE(classify_level_1_id, '0') as classifyLevel1,
        COALESCE(classify_level_2_id, 0) as classifyLevel2,
        if(type=-1, -1, 1) * record_member_present AS memberPresent,
        if(type=-1, -1, 1) * record_member_principal AS memberPrincipal,
        if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visitSourceId1,
        if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visitSourceId2,
        visit_source_from_type as visitSourceFromType,
        visit_source_from as visitSourceFrom,
        visit_source_remark as visitSourceRemark,
        if(patient_source_id_1='0', null, patient_source_id_1) as patientSourceId1,
        if(patient_source_id_2='0', null, patient_source_id_2) as patientSourceId2,
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        scene_type as sceneType,
        record_source_type as recordSourceType,
        comment,
        referral_doctor_id as referralDoctorId,
        air_pharmacy_order_id as airPharmacyOrderId,
        doctor_id as indicationsDoctorId,
        department_id as doctorDepartmentId,
        consultant_id as consultantId,
        if(type=-1, -1, 1) * record_unit_adjustment_price as recordUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        charge_form_item_remark as chargeFormItemRemark,
        batch_id as batchId,
        v2_charge_form_item_id as v2ChargeFormItemId,
        if(type=-1, -1, 1) * record_verify_price as recordVerifyPrice,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        shebao_extend::text as shebaoExtend
        from ${env}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            and product_compose_type in (0,2)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and goods_fee_type in(0,1)
        <if test="params.isCharge == 0">
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            and product_type != 18
            and record_type not in (2, 3)
            <if test="params.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="params.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
        </if>
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.departmentId != null and params.departmentId != "" and params.departmentId != "00000000000000000000000000000000"'>
            and department_id = #{params.departmentId}
        </if>
        <if test='params.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '')
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test='params.hisType == null or params.hisType == ""'>
            <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
            </if>
            <if test='params.doctorId == "00000000000000000000000000000000"'>
                and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
            </if>
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.productIds != null ">
            and product_id in
            <foreach collection="params.productIds" item="productId" open="(" close=")" separator=",">
                #{params.productId}
            </foreach>
        </if>
        <if test="params.tagIdSql != null and params.tagIdSql != ''">
            and ${params.tagIdSql}
        </if>
        order by createTime desc, patientorderNo desc, goodsId,id
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectProject" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailItemsDao">
        select
        product_type as productType,
        product_sub_type as productSubType,
        product_custom_type as productCustomType,
        chain_id as chainId,
        clinic_id as clinicId,
        v2_charge_sheet_id as chargeSheetId,
        v2_transaction_id as transactionId,
        create_time as createTime,
        patientorder_no as patientorderNo,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        patient_id as patientId,
        pay_third_party_extra_info::json->>'thirdPartyTransactionId' as wxOrderId,
        charge_sheet_type as chargeSheetType,
        cashier_id as cashierId,
        product_id as goodsId,
        retail_type as retailType,
        type as refundType,
        pay_type as payType,
        pay_sub_type as paySubType,
        product_unit as unit,
        min(if(type=-1, -1, 1) * abs(product_unit_price)) as unitPrice,
        sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0)) as unitCount,
        sum(if(type=-1, -1, 1) * (
        received_price
        - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price))
        - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0)
        )) as amount,
        sum(if(type=-1, -1, 1) * received_price) as receivedPrice,
        sum(if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0)) as discountPrice ,
        sum(if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0)) as adjustmentPrice,
        sum(if(type=-1, -original_price, original_price)) as originPrice,
        sum(if(type=-1, -1, 1) * deduct_promotion_price) as deductPrice,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
        item_nurse_id as itemNurseId,
        copywriter_id as copywriterId,
        fee_type_id as feeTypeId,
        COALESCE(classify_level_1_id, '0') as classifyLevel1,
        classify_level_2_id as classifyLevel2,
        sum(if(type=-1, -1, 1) * record_member_present) AS memberPresent,
        sum(if(type=-1, -1, 1) * record_member_principal) AS memberPrincipal,
        if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visitSourceId1,
        if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visitSourceId2,
        visit_source_from_type as visitSourceFromType,
        visit_source_from as visitSourceFrom,
        visit_source_remark as visitSourceRemark,
        if(patient_source_id_1='0', null, patient_source_id_1) as patientSourceId1,
        if(patient_source_id_2='0', null, patient_source_id_2) as patientSourceId2,
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        scene_type as sceneType,
        record_source_type as recordSourceType,
        comment,
        referral_doctor_id as referralDoctorId,
        doctor_id as doctorId,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        discount_info as discountInfo,
        sum(if(type=-1, -1, 1) * record_unit_adjustment_price) as recordUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        charge_form_item_remark as chargeFormItemRemark,
        shebao_extend::text as shebaoExtend
        from ${env}.dwd_charge_transaction_record_v_partition
        <include refid="projectWhereSql"/>
        group by productType,productSubType,productCustomType,chainId,clinicId,chargeSheetId,transactionId,createTime,patientorderNo,patientOrderId,type,patientId,wxOrderId,
                 chargeSheetType,cashierId,goodsId,retailType,refundType,payType,paySubType,unit,departmentId,employeeId,itemDoctorId,itemDoctorDepartmentId,itemNurseId,
                 copywriterId,feeTypeId,classifyLevel1,classifyLevel2,visitSourceId1,visitSourceId2,visitSourceFromType,visitSourceFrom,visitSourceRemark,patientSourceId1,patientSourceId2,patientSourceFromType, patientSourceFrom,sceneType,recordSourceType,comment,
                 referralDoctorId,doctorId,discountInfo,revisitStatus,charge_form_item_remark,employeeName, if (charge_transaction_record_id is null, id, charge_transaction_record_id),shebaoExtend
        order by createTime desc, patientorderNo desc, goodsId
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectBatchProject" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailItemsDao">
        select
        product_type as productType,
        product_sub_type as productSubType,
        product_custom_type as productCustomType,
        chain_id as chainId,
        clinic_id as clinicId,
        v2_charge_sheet_id as chargeSheetId,
        v2_transaction_id as transactionId,
        create_time as createTime,
        patientorder_no as patientorderNo,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        patient_id as patientId,
        replace(pay_third_party_extra_info, '\', '')::json->>'thirdPartyTransactionId' as wxOrderId,
        charge_sheet_type as chargeSheetType,
        cashier_id as cashierId,
        product_id as goodsId,
        retail_type as retailType,
        type as refundType,
        pay_type as payType,
        pay_sub_type as paySubType,
        product_unit as unit,
        if(type=-1, -1, 1) * abs(if (product_batch_unit_price is null, product_unit_price, product_batch_unit_price)) as unitPrice,
        if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0','17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0) as unitCount,
        if(type=-1, -1, 1) * (
        received_price
        - (if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price))
        - if(adjustment_price is not null and record_adjustment_price > 0, record_adjustment_price, 0.0)
        ) as amount,
        if(type=-1, -1, 1) * received_price as receivedPrice,
        if(type=-1, -1, 1) * if(record_adjustment_price is not null and record_adjustment_price &lt; 0, record_discount_price-record_adjustment_price, record_discount_price)
                + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is not null and adjustment_price &lt; 0, discount_price-adjustment_price, discount_price),0.0) as discountPrice ,
        if((if(type=-1, -1, 1) * record_adjustment_price) is null,0.0,(if(type=-1, -1, 1) * record_adjustment_price)) + if(record_source_type = 2, if(type=-1, -1, 1) * if(adjustment_price is null, 0.0,adjustment_price), 0.0) as adjustmentPrice,
        if(type=-1, -original_price, original_price) as originPrice,
        if(type=-1, -1, 1) * deduct_promotion_price as deductPrice,
        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id != '', seller_id, null)) as employeeId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
        item_nurse_id as itemNurseId,
        copywriter_id as copywriterId,
        fee_type_id as feeTypeId,
        COALESCE(classify_level_1_id, '0') as classifyLevel1,
        classify_level_2_id as classifyLevel2,
        if(type=-1, -1, 1) * record_member_present AS memberPresent,
        if(type=-1, -1, 1) * record_member_principal AS memberPrincipal,
        if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visitSourceId1,
        if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visitSourceId2,
        visit_source_from_type as visitSourceFromType,
        visit_source_from as visitSourceFrom,
        visit_source_remark as visitSourceRemark,
        if(patient_source_id_1='0', null, patient_source_id_1) as patientSourceId1,
        if(patient_source_id_2='0', null, patient_source_id_2) as patientSourceId2,
        patient_source_from_type as patientSourceFromType,
        patient_source_form as patientSourceFrom,
        scene_type as sceneType,
        record_source_type as recordSourceType,
        comment,
        referral_doctor_id as referralDoctorId,
        doctor_id as doctorId,
        if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
        if (batch_discount_info is null, discount_info, batch_discount_info) as discountInfo,
        if(type=-1, -1, 1) * record_unit_adjustment_price as recordUnitermAdjustmentPrice,
        if(revisit_status = 0 or revisit_status is null, '-', if (revisit_status = 1, '初诊', '复诊')) as revisitStatus,
        charge_form_item_remark as chargeFormItemRemark,
        if ((product_compose_type = 3 and goods_fee_type = 2) or (product_compose_type = 0 and goods_fee_type = 2), compose_parent_product_id, null) as composeParentProductId,
        compose_parent_classify_level_1_id as composeParentClassifyLevel1Id,
        compose_parent_classify_level_2_id as composeParentClassifyLevel2Id,
        batch_id as batchId,
        shebao_extend::text as shebaoExtend
        from ${env}.dwd_charge_transaction_record_v_partition
        <include refid="projectWhereSql"/>
        order by createTime desc, patientorderNo desc, goodsId
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectItemsTotal" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailTotalResp">
        select
            count(1) as count,
            sum(refundAmount) as refundAmount,
            sum(payedAmount) as payedAmount,
            sum(oweRefundAmount) as oweRefundAmount,
            sum(owePayedAmount) as owePayedAmount,
            COALESCE(sum(owePayedAmount), 0.0) - COALESCE(sum(oweRefundAmount), 0.0) as actualOwePayedAmount,
            sum(repayAmount) as repayAmount,
            sum(amount) as amount,
            sum(originPrice) as originPrice,
            sum(deductPrice) as deductPrice,
            sum(billOriginPrice) as billOriginPrice,
            sum(unitCount) as unitCount
        from (
        select
            count(1) as count,
            if (charge_transaction_record_id is null, id, charge_transaction_record_id) as  chargeTransactionRecordId,
            sum(if(type=-1 and record_source_type = 0, received_price, 0.0)) as refundAmount,
            sum(if(type != -1 and record_source_type = 0, received_price, 0.0)) as payedAmount,
            sum(if(type=-1 and record_source_type = 1, received_price, 0.0)) as oweRefundAmount,
            sum(if(type != -1 and record_source_type = 1, received_price, 0.0)) as owePayedAmount,
            sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
            sum(if(type=-1, -received_price, received_price)) as amount,
            sum(if(type = -1, -1, 1) * (received_price - record_discount_price - record_adjustment_price)) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice,
            sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price > 0,
            record_adjustment_price,
            0.0)-if(record_source_type=2,(discount_price-if(adjustment_price>0,adjustment_price,0.0)),0.0)-COALESCE(record_unit_adjustment_price,0.0)))
            as billOriginPrice,
            sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0', '17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0)) as unitCount
        from ${env}.dwd_charge_transaction_record_v_partition
            <include refid="itemWhereSql"/>
            group by chargeTransactionRecordId
        ) a
    </select>
    <sql id="itemWhereSql">
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and
        to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            and product_compose_type in (0,2)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and goods_fee_type in(0,1)
        <if test="params.isCharge == 0">
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            and product_type != 18
            and record_type not in (2, 3)
            <if test="params.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="params.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
        </if>
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.departmentId != null and params.departmentId != "" and params.departmentId != "00000000000000000000000000000000"'>
            and department_id = #{params.departmentId}
        </if>
        <if test='params.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '')
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test='params.hisType == null or params.hisType == ""'>
            <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
            </if>
            <if test='params.doctorId == "00000000000000000000000000000000"'>
                and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
            </if>
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.productIds != null">
            and product_id in
            <foreach collection="params.productIds" item="productId" open="(" close=")" separator=",">
                #{params.productId}
            </foreach>
        </if>
        <if test="params.tagIdSql != null and params.tagIdSql != ''">
            and ${params.tagIdSql}
        </if>
        <if test="params.sellNo != null and params.sellNo != ''">
            and sell_no = #{params.sellNo}
        </if>
        <if test="params.beginExpiryDate != null and params.beginExpiryDate != '' and params.endExpiryDate != null and params.endExpiryDate != ''">
            and expiry_date BETWEEN #{params.beginExpiryDate} and #{params.endExpiryDate}
        </if>
        <if test="params.profitCategoryTypeIdSql != null and params.profitCategoryTypeIdSql != ''">
            and ${params.profitCategoryTypeIdSql}
        </if>
    </sql>


    <select id="selectBatchItemsTotal" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailTotalResp">
        select
            count(1) as count,
            sum(if(type=-1 and record_source_type = 0, received_price, 0.0)) as refundAmount,
            sum(if(type != -1 and record_source_type = 0, received_price, 0.0)) as payedAmount,
            sum(if(type=-1 and record_source_type = 1, received_price, 0.0)) as oweRefundAmount,
            sum(if(type != -1 and record_source_type = 1, received_price, 0.0)) as owePayedAmount,
            COALESCE(sum(if(type != -1 and record_source_type = 1, received_price, 0.0)), 0.0) - COALESCE(sum(if(type=-1 and record_source_type = 1, received_price, 0.0)), 0.0) as actualOwePayedAmount,
            sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
            sum(if(type=-1, -received_price, received_price)) as amount,
            sum(if(type = -1, -1, 1) * (received_price - record_discount_price - record_adjustment_price)) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice,
            sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price > 0, record_adjustment_price, 0.0)-if(record_source_type=2,(discount_price-if(adjustment_price>0,adjustment_price,0.0)),0.0)-COALESCE(record_unit_adjustment_price,0.0))) as billOriginPrice,
            sum(if(type=-1, -1, 1) * if(classify_level_1_id in ('-2', '18-0', '17-0'), 1.0, product_unit_count) * if(classify_level_1_id in ('1-12','1-13'), product_dose_count, 1.0)) as unitCount
        from ${env}.dwd_charge_transaction_record_v_partition
            <include refid="itemBatchWhereSql"/>
    </select>
    <sql id="itemBatchWhereSql">
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            and product_compose_type in (0,2)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
        </if>
        and import_flag = 0
        and is_deleted = 0
        and goods_fee_type in(0,1)
        <if test="params.isCharge == 0">
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            and product_type != 18
            and record_type not in (2, 3)
            <if test="params.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="params.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
        </if>
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.departmentId != null and params.departmentId != "" and params.departmentId != "00000000000000000000000000000000"'>
            and department_id = #{params.departmentId}
        </if>
        <if test='params.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '')
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test='params.hisType == null or params.hisType == ""'>
            <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
            </if>
            <if test='params.doctorId == "00000000000000000000000000000000"'>
                and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
            </if>
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.productIds != null">
            and product_id in
            <foreach collection="params.productIds" item="productId" open="(" close=")" separator=",">
                #{params.productId}
            </foreach>
        </if>
        <if test="params.tagIdSql != null and params.tagIdSql != ''">
            and ${params.tagIdSql}
        </if>
        <if test="params.sellNo != null and params.sellNo != ''">
            and sell_no = #{params.sellNo}
        </if>
        <if test="params.beginExpiryDate != null and params.beginExpiryDate != '' and params.endExpiryDate != null and params.endExpiryDate != ''">
            and expiry_date BETWEEN #{params.beginExpiryDate} and #{params.endExpiryDate}
        </if>
        <if test="params.profitCategoryTypeIdSql != null and params.profitCategoryTypeIdSql != ''">
            and ${params.profitCategoryTypeIdSql}
        </if>
    </sql>


    <select id="selectProjectTotal" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailTotalResp">
        select
        count(1) as count,
        sum(refundAmount) as refundAmount,
        sum(payedAmount) as payedAmount,
        sum(oweRefundAmount) as oweRefundAmount,
        sum(owePayedAmount) as owePayedAmount,
        COALESCE(sum(owePayedAmount), 0.0) - COALESCE(sum(oweRefundAmount), 0.0) as actualOwePayedAmount,
        sum(repayAmount) as repayAmount,
        sum(amount) as amount,
        sum(originPrice) as originPrice,
        sum(deductPrice) as deductPrice
        from (
        select
            count(1) as count,
            if (charge_transaction_record_id is null, id, charge_transaction_record_id) as  chargeTransactionRecordId,
            sum(if(type=-1 and record_source_type = 0, received_price, 0.0)) as refundAmount,
            sum(if(type != -1 and record_source_type = 0, received_price, 0.0)) as payedAmount,
            sum(if(type=-1 and record_source_type = 1, received_price, 0.0)) as oweRefundAmount,
            sum(if(type != -1 and record_source_type = 1, received_price, 0.0)) as owePayedAmount,
            sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
            sum(if(type=-1, -received_price, received_price)) as amount,
            sum(if(type = -1, -1, 1) * (received_price - record_discount_price - record_adjustment_price)) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
        from ${env}.dwd_charge_transaction_record_v_partition
            <include refid="projectWhereSql"/>
            group by chargeTransactionRecordId) a
    </select>
    <sql id="projectWhereSql">
        where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            <!-- 只要收费项目，剔除套餐母项 -->
            and goods_fee_type in (0,2) and product_compose_type != 1
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
        </if>
        and import_flag = 0
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test='params.departmentId != null and params.departmentId != "" and params.departmentId != "00000000000000000000000000000000"'>
            and department_id = #{params.departmentId}
        </if>
        <if test='params.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '')
        </if>
        <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
            ${params.searchDoctorSql}
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.revisitStatus != null">
            and revisit_status = #{params.revisitStatus}
        </if>
        <if test="params.referralDoctorId != null and params.referralDoctorId != ''">
            and referral_doctor_id = #{params.referralDoctorId}
        </if>
        <if test="params.billDepartment != null and params.billDepartment != ''">
            and (department_id = #{params.billDepartment} or seller_department_id = #{params.billDepartment})
        </if>
        <if test="params.productIds != null">
            and product_id in
            <foreach collection="params.productIds" item="productId" open="(" close=")" separator=",">
                #{params.productId}
            </foreach>
        </if>
        <if test="params.tagIdSql != null and params.tagIdSql != ''">
            and ${params.tagIdSql}
        </if>
    </sql>


    <select id="selectBatchProjectTotal" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailTotalResp">
        select
            count(1) as count,
            sum(if(type=-1 and record_source_type = 0, received_price, 0.0)) as refundAmount,
            sum(if(type != -1 and record_source_type = 0, received_price, 0.0)) as payedAmount,
            sum(if(type=-1 and record_source_type = 1, received_price, 0.0)) as oweRefundAmount,
            sum(if(type != -1 and record_source_type = 1, received_price, 0.0)) as owePayedAmount,
            COALESCE(sum(if(type != -1 and record_source_type = 1, received_price, 0.0)), 0.0) - COALESCE(sum(if(type = -1 and record_source_type = 1, received_price, 0.0)), 0.0) as actualOwePayedAmount,
            sum(if(record_source_type = 2, received_price, 0.0)) as repayAmount,
            sum(if(type=-1, -received_price, received_price)) as amount,
            sum(if(type = -1, -1, 1) * (received_price - record_discount_price - record_adjustment_price)) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
        from ${env}.dwd_charge_transaction_record_v_partition
        <include refid="projectWhereSql"/>
    </select>

    <select id="selectTransactionId" resultType="java.lang.String">
        select
        v2_transaction_id as transactionId
        from ${env}.dwd_charge_transaction_record_v_partition
        where
        create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and product_type!=11
        and chain_id = #{params.chainId}
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test='params.hisType != null and params.hisType != ""'>
            <if test='params.hisType != 1'>
                <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                    and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                </if>
                <if test='params.doctorId == "00000000000000000000000000000000"'>
                    and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                </if>
            </if>
            <if test='params.hisType == 1'>
                <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 1'>
                    <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                        and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                    </if>
                    <if test='params.doctorId == "00000000000000000000000000000000"'>
                        and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                    </if>
                </if>
                <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 2'>
                    <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                        and ((item_doctor_id = #{params.doctorId} and (retail_type = 2 or charge_sheet_type in(3,6,8,12,16,18) or product_type in(2,3,4,11,19)))
                        or ((doctor_id=#{params.doctorId} or seller_id = #{params.doctorId} ) and record_type != 2 and charge_sheet_type not in(3,6,8,12,16,18) and product_type not in(2,3,4,11,19) ))
                    </if>
                    <if test='params.doctorId == "00000000000000000000000000000000"'>
                        and (((item_doctor_id is null or item_doctor_id = '') and (retail_type = 2 or charge_sheet_type in(3,6,8,12,16,18) or product_type in(2,3,4,11,19)))
                        or ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '') and record_type != 2 and charge_sheet_type not in(3,6,8,12,16,18) and product_type not in(2,3,4,11,19) ))
                    </if>
                </if>
                <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 3'>
                    <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                        and item_nurse_id = #{params.doctorId}
                    </if>
                    <if test='params.doctorId == "00000000000000000000000000000000"'>
                        and (item_nurse_id is null or item_nurse_id = '')
                    </if>
                </if>
            </if>
        </if>
        <if test='params.hisType == null or params.hisType == ""'>
            <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
            </if>
            <if test='params.doctorId == "00000000000000000000000000000000"'>
                and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
            </if>
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test="params.cashierId != null and params.cashierId != ''">
            and cashier_id = #{params.cashierId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        group by transactionId,create_time
        order by create_time desc
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectClassifyTransactionId" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailClassifyDao">
        select * from (
        <if test="params.fee1 != null or params.fee2 == null">
            select
            v2_transaction_id as transactionId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            null as classifyLevel2,
            create_time as createTime
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and product_type!=11
            and chain_id = #{params.chainId}
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test='params.hisType != null and params.hisType != ""'>
                <if test='params.hisType != 1'>
                    <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                        and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                    </if>
                    <if test='params.doctorId == "00000000000000000000000000000000"'>
                        and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                    </if>
                </if>
                <if test='params.hisType == 1'>
                    <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 1'>
                        <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                            and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                        </if>
                        <if test='params.doctorId == "00000000000000000000000000000000"'>
                            and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                        </if>
                    </if>
                    <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 2'>
                        <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                            and ((item_doctor_id = #{params.doctorId} and (retail_type = 2 or charge_sheet_type in(3,6,8,12,16,18) or product_type in(2,3,4,11,19)))
                            or ((doctor_id=#{params.doctorId} or seller_id = #{params.doctorId} ) and record_type != 2 and charge_sheet_type not in(3,6,8,12,16,18) and product_type not in(2,3,4,11,19) ))
                        </if>
                        <if test='params.doctorId == "00000000000000000000000000000000"'>
                            and (((item_doctor_id is null or item_doctor_id = '') and (retail_type = 2 or charge_sheet_type in(3,6,8,12,16,18) or product_type in(2,3,4,11,19)))
                            or ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '') and record_type != 2 and charge_sheet_type not in(3,6,8,12,16,18) and product_type not in(2,3,4,11,19) ))
                        </if>
                    </if>
                    <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 3'>
                        <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                            and item_nurse_id = #{params.doctorId}
                        </if>
                        <if test='params.doctorId == "00000000000000000000000000000000"'>
                            and (item_nurse_id is null or item_nurse_id = '')
                        </if>
                    </if>
                </if>
            </if>
            <if test='params.hisType == null or params.hisType == ""'>
                <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                    and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                </if>
                <if test='params.doctorId == "00000000000000000000000000000000"'>
                    and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                </if>
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.fee1 != null">
                and ${params.fee1}
            </if>
            group by transactionId,classifyLevel1,createTime
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            union all
        </if>
        <if test="params.fee2 != null">
            select
            v2_transaction_id as transactionId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            COALESCE(classify_level_2_id, 0) as classifyLevel2,
            create_time as createTime
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and product_type!=11
            and chain_id = #{params.chainId}
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            and ${params.fee2}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test='params.hisType != null and params.hisType != ""'>
                <if test='params.hisType != 1'>
                    <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                        and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                    </if>
                    <if test='params.doctorId == "00000000000000000000000000000000"'>
                        and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                    </if>
                </if>
                <if test='params.hisType == 1'>
                    <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 1'>
                        <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                            and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                        </if>
                        <if test='params.doctorId == "00000000000000000000000000000000"'>
                            and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                        </if>
                    </if>
                    <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 2'>
                        <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                            and ((item_doctor_id = #{params.doctorId} and (retail_type = 2 or charge_sheet_type in(3,6,8,12,16,18) or product_type in(2,3,4,11,19)))
                            or ((doctor_id=#{params.doctorId} or seller_id = #{params.doctorId} ) and record_type != 2 and charge_sheet_type not in(3,6,8,12,16,18) and product_type not in(2,3,4,11,19) ))
                        </if>
                        <if test='params.doctorId == "00000000000000000000000000000000"'>
                            and (((item_doctor_id is null or item_doctor_id = '') and (retail_type = 2 or charge_sheet_type in(3,6,8,12,16,18) or product_type in(2,3,4,11,19)))
                            or ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '') and record_type != 2 and charge_sheet_type not in(3,6,8,12,16,18) and product_type not in(2,3,4,11,19) ))
                        </if>
                    </if>
                    <if test='params.employeeTypeEnum != null and params.employeeTypeEnum.typeNumber == 3'>
                        <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                            and item_nurse_id = #{params.doctorId}
                        </if>
                        <if test='params.doctorId == "00000000000000000000000000000000"'>
                            and (item_nurse_id is null or item_nurse_id = '')
                        </if>
                    </if>
                </if>
            </if>
            <if test='params.hisType == null or params.hisType == ""'>
                <if test='params.doctorId != null and params.doctorId != "" and params.doctorId != "00000000000000000000000000000000"'>
                    and (doctor_id = #{params.doctorId} or seller_id = #{params.doctorId})
                </if>
                <if test='params.doctorId == "00000000000000000000000000000000"'>
                    and (doctor_id is null or doctor_id = '' ) and (seller_id is null or seller_id = '')
                </if>
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            group by transactionId,classifyLevel1,classifyLevel2,createTime
        </if>
        ) z
        <if test="params.size == null or params.size == 0">
            order by createTime desc
        </if>
        <if test="params.size != null and params.size != 0">
            order by createTime desc, classifyLevel1
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>


    <select id="selectTransactionPerson" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailTransactionDao">
        select
        v2_transaction_id as transactionId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
        if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
        item_nurse_id as itemNurseId
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where
        create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and product_type!=11
        and chain_id = #{params.chainId}
        <if test="params.actionSql != null and params.actionSql != ''">
            and ${params.actionSql}
        </if>
        <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
            ${params.sourceTypeSQL}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="transactionIdList != null and transactionIdList.size() > 0">
            and v2_transaction_id in
            <foreach collection="transactionIdList" separator="," close=")" open=" (" item="id" >
                #{id}
            </foreach>
        </if>
        <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.sellerId == "00000000000000000000000000000000"'>
            and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test='params.patientId != null and params.patientId != ""'>
            and patient_id = #{params.patientId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.cashierId != null and params.cashierId != ''">
            and cashier_id = #{params.cashierId}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
    </select>

    <select id="selectClassifyPerson" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailClassifyDao">
        select * from (
        <if test="params.fee1 != null or params.fee2 == null">
            select
            v2_transaction_id as transactionId,
            if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
            if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
            item_nurse_id as itemNurseId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            null as classifyLevel2
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where
            create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and chain_id = #{params.chainId}
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="classifyTransactionIdDaoList != null and classifyTransactionIdDaoList.size > 0">
                and
                <foreach collection="classifyTransactionIdDaoList" separator="or" close=")" open=" (" item="dao" >
                    ( v2_transaction_id = #{dao.transactionId} and classify_level_1_id = #{dao.classifyLevel1})
                </foreach>
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.fee1 != null">
                and ${params.fee1}
            </if>
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            union all
        </if>
        <if test="params.fee2 != null">
            select
            v2_transaction_id as transactionId,
            if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_id, doctor_id) as itemDoctorId,
            if(retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in(2,3,4,11,19), item_doctor_department_id, department_id) as itemDoctorDepartmentId,
            item_nurse_id as itemNurseId,
            COALESCE(classify_level_1_id, '0') as classifyLevel1,
            COALESCE(classify_level_2_id, 0) as classifyLevel2
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where
            create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            and chain_id = #{params.chainId}
            <if test="params.actionSql != null and params.actionSql != ''">
                and ${params.actionSql}
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            and ${params.fee2}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="classifyTransactionIdDaoList != null and classifyTransactionIdDaoList.size > 0">
                and
                <foreach collection="classifyTransactionIdDaoList" separator="or" close=")" open=" (" item="dao" >
                    ( v2_transaction_id = #{dao.transactionId} and classify_level_1_id = #{dao.classifyLevel1} and classify_level_2_id = #{dao.classifyLevel2})
                </foreach>
            </if>
            <if test='params.sellerId != null and params.sellerId != "" and params.sellerId != "00000000000000000000000000000000"'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test='params.sellerId == "00000000000000000000000000000000"'>
                and (cashier_id is null or cashier_id = '00000000000000000000000000000000')
            </if>
            <if test='params.patientId != null and params.patientId != ""'>
                and patient_id = #{params.patientId}
            </if>
            <if test="params.payModeSql != null and params.payModeSql != ''">
                and ${params.payModeSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
        </if>
        ) z
    </select>

    <select id="selectTotalCashFee" resultType="java.math.BigDecimal">
        select
        COALESCE(sum(if(type=-1,-received_price,received_price)),0.0)  as totalCashFee
        from ${cisTable}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and chain_id=#{param.chainId}
        <if test="param.clinicId!=null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.sellerId != null and param.sellerId == '00000000000000000000000000000000'">
            and (cashier_id is null or cashier_id = '' or cashier_id ='00000000000000000000000000000000')
        </if>
        <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
            and cashier_id = #{param.sellerId}
        </if>
        <if test="param.cashierIdSql != null and param.cashierIdSql != ''">
            and ${param.cashierIdSql}
        </if>
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="param.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
        and pay_type != 5
        and is_deleted =0
    </select>

    <select id="selectMedicalInsurance" resultType="cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyMedicalInsuranceDao">
        select
            chain_id          as chainId,
            clinic_id         as clinicId,
            pay_type          as mode,
            pay_sub_type      as subMode,
            health_card_med_type    as medType,
            sum(if(pay_type != 5 and health_card_cash_received_fee is not null, health_card_cash_received_fee,0.0)) yibaoCash,
            sum(if(pay_type = 5,if(type=-1,-received_price,received_price),0.0)) as yibao
        from ${cisTable}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and chain_id=#{param.chainId}
        and pay_type != 100
        <if test="param.clinicId!=null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.sellerId != null and param.sellerId == '00000000000000000000000000000000'">
            and (cashier_id is null or cashier_id = '' or cashier_id ='00000000000000000000000000000000')
        </if>
        <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
            and cashier_id = #{param.sellerId}
        </if>
        <if test="param.cashierIdSql != null and param.cashierIdSql != ''">
            and ${param.cashierIdSql}
        </if>
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="param.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
        and is_deleted = 0
        group by chain_id, clinic_id, pay_type, pay_sub_type, health_card_med_type
    </select>

    <select id="selectSummary" resultType="cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailySummaryDao">
        select chain_id as chainId,
        <if test="param.clinicId != null and param.clinicId != ''">
            clinic_id as clinicId,
        </if>
        sum(if(pay_type=5 ,if(type=-1,-received_price,received_price),0.0)) as totalMedicalInsurance,
        sum(if(pay_type!=5 ,if(type=-1,-received_price,received_price),0.0)) as totalPayCash,
        sum(if(pay_type!=5 and product_type!=18 and record_type!=2 and (pay_type != 24 and pay_sub_type != 15), if(type=-1,-received_price,received_price),0.0)) as totalCashRevenue,
        sum(if(pay_type!=5 and (product_type=18 or record_type=2) ,if(type=-1,-received_price,received_price),0.0)) as totalRecharge,
        sum(if(type=-1,-received_price,received_price)) as totalAmount
        from ${cisTable}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and chain_id=#{param.chainId}
        <if test="param.clinicId!=null and param.clinicId!=''">
          and clinic_id = #{param.clinicId}
        </if>
        and is_deleted =0
        <if test="param.sellerId != null and param.sellerId == '00000000000000000000000000000000'">
            and (cashier_id is null or cashier_id = '' or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
            and cashier_id = #{param.sellerId}
        </if>
        <if test="param.cashierIdSql != null and param.cashierIdSql != ''">
            and ${param.cashierIdSql}
        </if>
        <if test="param.hisType != null and param.hisType == 100">
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                and goods_fee_type in (0,2) and product_compose_type != 1
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
            </if>
        </if>
        <if test="param.hisType != null and param.hisType != 100">
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
        </if>
        <if test="param.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="param.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
        group by chain_id
        <if test="param.clinicId != null and param.clinicId != ''">
            ,clinic_id
        </if>
    </select>

    <select id="selectMedicalInsuranceChargeSheetId" resultType="java.lang.String">
        select distinct v2_charge_sheet_id
        from ${cisTable}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and chain_id=#{param.chainId}
        <if test="param.clinicId!=null and param.clinicId!=''">
            and clinic_id = #{param.clinicId}
        </if>
        and is_deleted =0
        and pay_type = 5
        <if test='param.sellerId != null and param.sellerId != ""'>
            and cashier_id = #{param.sellerId}
        </if>
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="param.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
    </select>


    <select id="selectAmountBySheBaoPayMode"
            resultType="cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostSheBaoPayModeRes">
        select
            sum(fund_pay_sumamt) as fundPaySumAmt,
            sum(acct_pay) as acctPay,
            sum(oth_pay) as othPay
        from
        (
            select
                if(type = 0, fund_pay_sumamt, -fund_pay_sumamt) as fund_pay_sumamt,
                if(type = 0, acct_pay, -acct_pay)               as acct_pay,
                if(type = 0, oth_pay, -oth_pay)                 as oth_pay
            from ${cisTable}.dwd_shebao_national_settlement_item
            where charged_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and pay_status = 2
            and is_deleted = 0
            and display_type in (0, 1)
            and charge_sheet_type = 5
            and chain_id = #{params.chainId}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.departmentId != null and params.departmentId != ''">
                and department_id = #{params.departmentId}
            </if>
            <if test="params.chargeId != null and params.chargeId != ''">
                and created_by = #{params.chargeId}
            </if>
            group by task_id, type, fund_pay_sumamt, acct_pay, oth_pay
        ) a;
    </select>

    <select id="selectMedicalInsuranceData"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargeDailyNengMengGuSheBaoEntity">
        select
            sum(if(type = 0, b.medfee_sumamt, - b.medfee_sumamt)) as medfeeSumamt,
            sum(b.received_fee) as receivedFee,
            sum(if(type = 0, b.inscp_scp_amt_a, - b.inscp_scp_amt_a)) as inscpScpAmt,
            sum(if(type = 0, 1, -1) * (b.acct_pay + b.wltpay_amt)) as acctPay,
            sum(if(type = 0, b.fund_pay_sumamt, - b.fund_pay_sumamt)) as fundPaySumamt,
            sum(if(type = 0, b.hifp_pay, - b.hifp_pay)) as hifpPay,
            sum(if(type = 0, b.maf_Pay, - b.maf_Pay)) as mafPay,
            sum(if(type = 0, b.hifes_Pay, - b.hifes_Pay)) as hifesPay,
            sum(if(type = 0, b.hifmi_Pay, - b.hifmi_Pay)) as hifmiPay,
            sum(if(type = 0, b.hifob_Pay, - b.hifob_Pay)) as hifobPay,
            sum(if(type = 0, b.cvlserv_pay, - b.cvlserv_pay)) as cvlservPay,
            sum(if(type = 0, b.acct_mulaid_pay, - b.acct_mulaid_pay)) as acctMulaidPay,
            sum(if(type = 0, b.psn_cash_pay, - b.psn_cash_pay)) as psnCashPay,
            sum(if(type = 0, b.oth_pay, - b.oth_pay)) as othPay
        from (
            select
                distinct
                task_id,
                type,
                coalesce(medfee_sumamt,0.0) as medfee_sumamt,
                coalesce(inscp_scp_amt_a,0.0) as inscp_scp_amt_a,
                coalesce(received_fee,0.0) as received_fee,
                coalesce(acct_pay,0.0) as acct_pay,
                coalesce(wltpay_amt,0.0) as wltpay_amt,
                coalesce(fund_pay_sumamt,0.0) as fund_pay_sumamt,
                coalesce(hifp_pay,0.0) as hifp_pay,
                coalesce(maf_Pay,0.0) as maf_Pay,
                coalesce(hifes_Pay,0.0) as hifes_Pay,
                coalesce(hifmi_Pay,0.0) as hifmi_Pay,
                coalesce(hifob_Pay,0.0) as hifob_Pay,
                coalesce(cvlserv_pay,0.0) as cvlserv_pay,
                coalesce(acct_mulaid_pay,0.0) as acct_mulaid_pay,
                coalesce(psn_cash_pay,0.0) as psn_cash_pay,
                coalesce(oth_pay,0.0) as oth_pay
            from ${cisTable}.dwd_shebao_national_settlement_item
            where chain_id = #{params.chainId}
            and is_deleted = 0
            and pay_status = 2
            and charged_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.sellerId != null and params.sellerId == '00000000000000000000000000000000'">
                and (last_modified_by is null or created_by = '')
            </if>
            <if test="params.sellerId != null and params.sellerId != '' and params.sellerId != '00000000000000000000000000000000'">
                and last_modified_by = #{params.sellerId}
            </if>
            <if test="params.cashierIdSql != null and params.cashierIdSql != ''">
                and ${params.cashierIdSql}
            </if>
            <if test="params.departmentId != null and params.departmentId != ''">
                and department_id = #{params.departmentId}
            </if>
            <choose>
                <when test='params.businessScope == null or params.businessScope.size() == 0'>
                    and 1=1
                </when>
                <when test='params.businessScope.contains(1) and !params.businessScope.contains(2)'>
                    and charge_sheet_type != 5
                </when>
                <when test='!params.businessScope.contains(1) and params.businessScope.contains(2)'>
                    and charge_sheet_type = 5
                </when>
                <otherwise>
                    and 1=1
                </otherwise>
            </choose>
        ) b
    </select>

    <select id="selectPayModeByInsutype" resultType="cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeDao">
        select
            if(pay_type is null,-999, pay_type) as payMode,
            if(pay_type=2,if(pay_sub_type is null or pay_sub_type=0,0,1),
            if(pay_type = 19 and pay_sub_type in (5,6,7,12), 12,
            if(pay_type = 19 and pay_sub_type in (8, 13), 13,
            if(pay_type = 19 and pay_sub_type in (10,14), 14, pay_sub_type)))) as paySubMode,
            sum(if(shebao_insutype in ('310', '31003', '312') and type != -1, 1, 0) * received_price) as cityEmployeePrice,
            sum(if(shebao_insutype in ('390', '391', '392') and type != -1, 1, 0) * received_price) as cityResidentPrice,
            sum(if((shebao_insutype is null or shebao_insutype not in ('310', '31003', '312', '390', '391', '392')) and type != -1, 1, 0) * received_price) as selfPayAndOtherPrice,
            sum(if(shebao_insutype in ('310', '31003', '312') and type = -1, 1, 0) * received_price) as cityEmployeeRefundPrice,
            sum(if(shebao_insutype in ('390', '391', '392') and type = -1, 1, 0) * received_price) as cityResidentRefundPrice,
            sum(if((shebao_insutype is null or shebao_insutype not in ('310', '31003', '312', '390', '391', '392')) and type = -1, 1, 0) * received_price) as selfPayAndOtherRefundPrice
        from ${cisTable}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        <if test="param.clinicId!=null and param.clinicId!=''">
            and clinic_id = #{param.clinicId}
        </if>
        and is_deleted =0
        and pay_type != 5
        and pay_type != 100
        <if test='param.dataRange != null and param.dataRange == 0'>
            and classify_level_1_id = '5-0'
        </if>
        <if test="param.sellerId != null and param.sellerId == '00000000000000000000000000000000'">
            and (cashier_id is null or cashier_id = '' or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
            and cashier_id = #{param.sellerId}
        </if>
        <if test="param.cashierIdSql != null and param.cashierIdSql != ''">
            and ${param.cashierIdSql}
        </if>
        <if test="param.departmentId != null and param.departmentId != ''">
            and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
        </if>
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="param.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
        group by payMode, paySubMode
    </select>

    <select id="getSheBaoPayModeByInsutype"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargeDailyNengMengGuSheBaoEntity">
        select
            personType,
            type,
            sum(hifp_pay) as hifpPay,
            sum(acct_pay) as acctPay,
            sum(acct_mulaid_pay) as acctMulaidPay,
            sum(cvlserv_pay) as cvlservPay,
            sum(hifes_Pay) as hifesPay,
            sum(hifmi_Pay) as hifmiPay,
            sum(hifob_Pay) as hifobPay,
            sum(maf_Pay) as mafPay,
            sum(oth_pay) as othPay
        from
        (
            select
                distinct
                case when insutype in ('310', '31003', '312') then '职工'
                     when insutype in ('390', '391', '392') then '居民'
                     else '其他' end as personType,
                type,
                task_id,
                hifp_pay,
                acct_pay,
                acct_mulaid_pay,
                cvlserv_pay,
                hifes_Pay,
                hifmi_Pay,
                hifob_Pay,
                maf_Pay,
                oth_pay
            from ${cisTable}.dwd_shebao_national_settlement_item
            where chain_id = #{params.chainId}
            and is_deleted = 0
            and pay_status = 2
            <if test='params.dataRange != null and params.dataRange == 0'>
                and med_type = '12'
            </if>
            <if test='params.dataRange != null and params.dataRange == 1'>
                and charge_sheet_type != 5
            </if>
            <if test='params.dataRange != null and params.dataRange == 2'>
                and charge_sheet_type = 5
            </if>
            and charged_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.sellerId != null and params.sellerId == '00000000000000000000000000000000'">
                and (last_modified_by is null or last_modified_by = '')
            </if>
            <if test="params.sellerId != null and params.sellerId != '' and params.sellerId != '00000000000000000000000000000000'">
                and last_modified_by = #{params.sellerId}
            </if>
            <if test="cashierIdSql != null and cashierIdSql != ''">
                and ${cashierIdSql}
            </if>
            <if test="params.departmentId != null and params.departmentId != ''">
                and department_id = #{params.departmentId}
            </if>
        ) b
        group by personType, type
    </select>

    <select id="getPayModeTotal" resultType="cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeTotalRes">
        select
            sum(if(type != -1, product_unit_count, 0.0)) as count,
            sum(if(type = -1, product_unit_count, 0.0)) as refundCount,
            sum(if(type != -1, received_price, 0.0)) as receivedPrice,
            sum(if(type = -1, received_price, 0.0)) as refundPrice
        from ${cisTable}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        <if test="param.clinicId!=null and param.clinicId!=''">
            and clinic_id = #{param.clinicId}
        </if>
        and is_deleted =0
        and pay_type != 100
        <if test='param.dataRange != null and param.dataRange == 0'>
            and classify_level_1_id = '5-0'
        </if>
        <if test="param.sellerId != null and param.sellerId == '00000000000000000000000000000000'">
            and (cashier_id is null or cashier_id = '' or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
            and cashier_id = #{param.sellerId}
        </if>
        <if test="param.cashierIdSql != null and param.cashierIdSql != ''">
            and ${param.cashierIdSql}
        </if>
        <if test="param.departmentId != null and param.departmentId != ''">
            and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
        </if>
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="param.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
    </select>

    <select id="selectPharmacyTransaction"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailTransactionDao">
        select charge_sheet_created as createTime,
               charge_sheet_charged_time as chargedTime,
               chain_id as chainId,
               clinic_id as clinicId,
               charge_sheet_id as chargeSheetId,
               sell_no as sellNo,
               charge_sheet_type as chargeSheetType,
               sum(if (status = 2, 0, 1) * source_total_price) as totalPrice,
               sum(if (status = 2, 0, 1) * adjustment_price) as adjustmentPrice,
               sum(if (status = 2, 0, 1) * receivable_price) as shouldReceivePrice,
               sum(if (status = 2, -1, 1) * received_price) as receivedPrice,
               sum(total_cost_price) as cost,
               COALESCE(sum(if (status = 2, -1, 1) * received_price), 0) - COALESCE(sum(total_cost_price), 0) as profit,
               (COALESCE(sum(if(status = 2, -1, 1) * received_price), 0) - COALESCE(sum(total_cost_price), 0)) /NULLIF(sum(if (status = 2, -1, 1) * received_price), 0) AS grossProfit,
               sum(if (charge_sheet_status in(1,2), if (status = 2, -1, 1) * received_price, if (status = 2, 0, 1) * receivable_price)) as receivedCharge,
               sum(if (charge_sheet_status in(1,2), 0.0, ((if (status = 2, -1, 1) * received_price) - (if (status = 2, 0, 1) * receivable_price)))) as receivedRefund,
               max(pay_mode::text) as payModeJson,
               sum(if (status = 2, -1, 1) * point) as point,
               charged_by as cashierId,
               charge_sheet_source_id as sourceFormItemId,
               pharmacist_id as pharmacistId,
               seller_id as sellerId,
               remarks as comment,
               sum(if (status = 2, 0, 1) * deduct_discount_fee) as deductDiscountFee,
               sum(if (status = 2, 0, 1) * unit_adjustment_fee) as unitAdjustmentFee,
               sum(if (status = 2, 0, 1) * patient_point_promotion_fee) as patientPointPromotionFee,
               sum(if (status = 2, 0, 1) * discount_promotion_fee) as discountPromotionFee,
               sum(if (status = 2, 0, 1) * coupon_fee) as couponFee,
               sum(if (status = 2, 0, 1) * gift_rule_promotion_fee) as giftRulePromotionFee,
               sum(if (status = 2, 0, 1) * gift_promotion_fee) as giftPromotionFee,
               cooperation_clinic_id as cooperationClinicId,
               sum(if(promotion_info::json ->> 'oddFee' is not null,(promotion_info::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount,
               member_id as memberId
        from ${env}.dwd_charge_form_item
        where 1=1
            and chain_id = #{params.chainId}
            and charge_sheet_status != 0
            and is_deleted = 0
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.beginDate != null and params.endDate != null">
                and charge_sheet_created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds between to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            </if>
            <if test="params.endChargedDate != null and params.beginChargedDate != null">
                and charge_sheet_charged_time between to_timestamp(#{params.beginChargedDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endChargedDate}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
          <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
              ${params.sourceTypeSQL}
          </if>
          and goods_fee_type in (0, 2)
          and charge_sheet_type != 5
          <if test="params.sellNo != null and params.sellNo != ''">
              and sell_no = #{params.sellNo}
          </if>
          <if test="params.patientId != null and params.patientId != ''">
              and patient_id = #{params.patientId}
          </if>
          <if test="params.sellerId != null and params.sellerId != null">
              and charged_by = #{params.sellerId}
          </if>
          <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
              and cooperation_clinic_id = #{params.cooperationClinicId}
          </if>
          <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
              and ${params.searchDoctorSql}
          </if>
          <if test='params.payModeSql != null and params.payModeSql != ""'>
              and ${params.payModeSql}
          </if>
        group by chain_id,clinic_id,charge_sheet_created,charge_sheet_charged_time,
                 charge_sheet_id,
                 sell_no,
                 charge_sheet_type,
                 charged_by,
                 charge_sheet_source_id,
                 pharmacist_id, seller_id, remarks,cooperation_clinic_id,
                 member_id
        <if test="params.order != null and params.order != ''">
            order by ${params.order}
            <if test="params.sort != null and params.sort != ''">
                ${params.sort}
            </if>
        </if>
        <if test="params.order == null || params.order == ''">
            order by charge_sheet_created desc,charge_sheet_id
        </if>
        <if test="params.size != null and params.offset != null ">
            limit #{params.size} offset #{params.offset}
        </if>
    </select>

    <select id="selectPharmacyTransactionTotal"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailPharmacyTotalResp">
        select count(1) as count,
               sum(totalPrice) as totalPrice,
               sum(deductDiscountFee + unitAdjustmentFee + patientPointPromotionFee + discountPromotionFee + couponFee + giftRulePromotionFee + adjustmentPrice + giftPromotionFee + roundedAmount) as preferentialFee,
               sum(receivablePrice) as receivablePrice,
               sum(receivedPrice) as receivedPrice,
               sum(cost) as cost  from(
        select charge_sheet_created as createTime,
                charge_sheet_charged_time as chargedTime,
                chain_id as chainId,
                clinic_id as clinicId,
                charge_sheet_id as chargeSheetId,
                sell_no as sellNo,
                charge_sheet_type as chargeSheetType,
                sum(if (status = 2, 0, 1) * source_total_price) as totalPrice,
                sum(if (status = 2, 0, 1) * deduct_discount_fee) as deductDiscountFee,
                sum(if (status = 2, 0, 1) * unit_adjustment_fee) as unitAdjustmentFee,
                sum(if (status = 2, 0, 1) * patient_point_promotion_fee) as patientPointPromotionFee,
                sum(if (status = 2, 0, 1) * discount_promotion_fee) as discountPromotionFee,
                sum(if (status = 2, 0, 1) * coupon_fee) as couponFee,
                sum(if (status = 2, 0, 1) * gift_rule_promotion_fee) as giftRulePromotionFee,
                sum(if (status = 2, 0, 1) * gift_promotion_fee) as giftPromotionFee,
                sum(if (status = 2, 0, 1) * adjustment_price) as adjustmentPrice,
                sum(if (status = 2, 0, 1) * receivable_price) as receivablePrice,
                sum(if (status = 2, -1, 1) * received_price) as receivedPrice,
                sum(total_cost_price) as cost,
                max(pay_mode::text) as payModeJson,
                sum(if (status = 2, -1, 1) * point) as point,
                charged_by as cashierId,
                charge_sheet_source_id as sourceFormItemId,
                pharmacist_id as pharmacistId,
                seller_id as sellerId,
                cooperation_clinic_id as cooperationClinicId,
                sum(if(promotion_info::json ->> 'oddFee' is not null,(promotion_info::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount,
                member_id as memberId
        from ${env}.dwd_charge_form_item
        where 1=1
            and chain_id = #{params.chainId}
            and is_deleted = 0
            and charge_sheet_status != 0
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.beginDate != null and params.endDate != null">
                and charge_sheet_created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds between to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            </if>
            <if test="params.endChargedDate != null and params.beginChargedDate != null">
                and charge_sheet_charged_time between to_timestamp(#{params.beginChargedDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endChargedDate}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                ${params.sourceTypeSQL}
            </if>
            and goods_fee_type in (0, 2)
            and charge_sheet_type != 5
            <if test="params.sellNo != null and params.sellNo != ''">
                and sell_no = #{params.sellNo}
            </if>
            <if test="params.patientId != null and params.patientId != ''">
                and patient_id = #{params.patientId}
            </if>
            <if test="params.sellerId != null and params.sellerId != null">
                and charged_by = #{params.sellerId}
            </if>
            <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
                and cooperation_clinic_id = #{params.cooperationClinicId}
            </if>
            <if test="params.chargeStatus != null">
                <if test="params.chargeStatus == 3">
                    and dispensing_status = 0
                </if>
                <if test="params.chargeStatus != 3">
                    and charge_sheet_status = #{params.chargeStatus}
                </if>
            </if>
            <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                and ${params.searchDoctorSql}
            </if>
            <if test='params.payModeSql != null and params.payModeSql != ""'>
                and ${params.payModeSql}
            </if>
            group by chain_id,clinic_id,charge_sheet_created,charge_sheet_charged_time,
                    charge_sheet_id,
                    sell_no,
                    charge_sheet_type,
                    charged_by,
                    charge_sheet_source_id,
                    pharmacist_id, seller_id, cooperation_clinic_id,
                    member_id) a

    </select>

    <select id="selectPharmacyCommodity"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailPharmacyCommodityDto">
        select chain_id as chainId,
               clinic_id as clinicId,
               charge_sheet_id as chargeSheetId,
               charge_sheet_created as created,
               charge_sheet_charged_time as chargedTime,
               sell_no as sellNo,
               member_id as memberId,
               charge_sheet_type as chargeSheetType,
               max(if (status = 2, 0, 1) * source_unit_price) as originalPrice,
               sum(if (status = 2, 0, 1) * source_total_price) as totalPrice,
               product_id as goodsId,
               classify_level_1_id as classifyLevel1,
               classify_level_2_id as classifyLevel2,
               sum(if (status = 2, 0, 1) * unit_count) as count,
               unit,
               sum(if (status = 2, 0, 1) * adjustment_price) as adjustmentPrice,
               sum(if (status = 2, 0, 1) * receivable_price) as receivablePrice,
               sum(if (status = 2, -1, 1) * received_price) as receivedPrice,
               sum(total_cost_price) as cost,
               sum(if (charge_sheet_status in(1,2), if (status = 2, -1, 1) * received_price, if (status = 2, 0, 1) * receivable_price)) as receivedCharge,
               sum(if (charge_sheet_status in(1,2), 0.0, ((if (status = 2, -1, 1) * received_price) - (if (status = 2, 0, 1) * receivable_price)))) as receivedRefund,
               max(pay_mode::text) as payModeJson,
               sum(if (status = 2, -1, 1) * point) as point,
               charged_by as cashierId,
               charge_sheet_source_id as sourceFormItemId,
               pharmacist_id as pharmacistId,
               seller_id as sellerId,
               remarks as comment,
               is_dismounting as isDismounting,
               max(piece_num) as pieceNum,
               sum(if (status = 2, 0, 1) * deduct_discount_fee) as deductDiscountFee,
               sum(if (status = 2, 0, 1) * unit_adjustment_fee) as unitAdjustmentFee,
               sum(if (status = 2, 0, 1) * patient_point_promotion_fee) as patientPointPromotionFee,
               sum(if (status = 2, 0, 1) * discount_promotion_fee) as discountPromotionFee,
               sum(if (status = 2, 0, 1) * coupon_fee) as couponFee,
               sum(if (status = 2, 0, 1) * gift_rule_promotion_fee) as giftRulePromotionFee,
               sum(if (status = 2, 0, 1) * gift_promotion_fee) as giftPromotionFee,
               cooperation_clinic_id as cooperationClinicId,
               sum(if(promotion_info::json ->> 'oddFee' is not null,(promotion_info::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
        from ${env}.dwd_charge_form_item
        where 1=1
                and chain_id = #{params.chainId}
                and is_deleted = 0
                and charge_sheet_status != 0
                <if test="params.clinicId != null and params.clinicId != ''">
                    and clinic_id = #{params.clinicId}
                </if>
                <if test="params.beginDate != null and params.endDate != null">
                    and charge_sheet_created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds between to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
                </if>
                <if test="params.endChargedDate != null and params.beginChargedDate != null">
                    and charge_sheet_charged_time between to_timestamp(#{params.beginChargedDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endChargedDate}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                    ${params.sourceTypeSQL}
                </if>
                and goods_fee_type in (0, 2)
                and charge_sheet_type != 5
                <if test="params.sellNo != null and params.sellNo != ''">
                    and sell_no = #{params.sellNo}
                </if>
                <if test="params.patientId != null and params.patientId != ''">
                    and patient_id = #{params.patientId}
                </if>
                <if test="params.sellerId != null and params.sellerId != null">
                    and charged_by = #{params.sellerId}
                </if>
                <if test="params.fee1 != null and params.fee2 != null">
                    and (${params.fee1} or ${params.fee2})
                </if>
                <if test="params.fee1 != null and params.fee2 == null">
                    and ${params.fee1}
                </if>
                <if test="params.fee2 != null and params.fee1 == null">
                    and ${params.fee2}
                </if>
                <if test="params.tagIdSql != null and params.tagIdSql != ''">
                    and ${params.tagIdSql}
                </if>
                <if test="params.chargeSheetId != null and params.chargeSheetId != ''">
                    and charge_sheet_id = #{params.chargeSheetId}
                </if>
                <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
                    and cooperation_clinic_id = #{params.cooperationClinicId}
                </if>
                <if test="params.productId != null and params.productId != ''">
                    and product_id = #{params.productId}
                </if>
                <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                    and ${params.searchDoctorSql}
                </if>
                <if test='params.payModeSql != null and params.payModeSql != ""'>
                    and ${params.payModeSql}
                </if>
                <if test='params.batchSql != null and params.batchSql != ""'>
                    and ${params.batchSql}
                </if>
        group by chain_id,clinic_id,charge_sheet_created,charge_sheet_charged_time,
            charge_sheet_id,
            sell_no,
            member_id,
            charge_sheet_type,
            charged_by,
            charge_sheet_source_id,
            pharmacist_id, seller_id,product_id,cooperation_clinic_id, classify_level_1_id,classify_level_2_id,unit, remarks, is_dismounting
        order by charge_sheet_created desc,charge_sheet_id, product_id,is_dismounting
        <if test="params.size != null and params.offset != null ">
            limit #{params.size} offset #{params.offset}
        </if>
    </select>

    <select id="selectPharmacyBatchCommodity"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailPharmacyCommodityDto">
        select chain_id as chainId,
               clinic_id as clinicId,
               charge_sheet_id as chargeSheetId,
               charge_sheet_created as created,
               charge_sheet_charged_time as chargedTime,
               sell_no as sellNo,
               member_id as memberId,
               charge_sheet_type as chargeSheetType,
               max(if (status = 2, 0, 1) * source_unit_price) as originalPrice,
               sum(if (status = 2, 0, 1) * source_total_price) as totalPrice,
               product_id as goodsId,
               classify_level_1_id as classifyLevel1,
               classify_level_2_id as classifyLevel2,
               sum(if (status = 2, 0, 1) * unit_count) as count,
               unit,
               sum(if (status = 2, 0, 1) * adjustment_price) as adjustmentPrice,
               sum(if (status = 2, 0, 1) * receivable_price)as receivablePrice,
               sum(if (status = 2, -1, 1) * received_price) as receivedPrice,
               sum(total_cost_price) as cost,
               sum(if (status = 2, -1, 1) * point) as point,
               sum(if (charge_sheet_status in(1,2), if (status = 2, -1, 1) * received_price, if (status = 2, 0, 1) * receivable_price)) as receivedCharge,
               sum(if (charge_sheet_status in(1,2), 0.0, ((if (status = 2, -1, 1) * received_price) - (if (status = 2, 0, 1) * receivable_price)))) as receivedRefund,
               charged_by as cashierId,
               charge_sheet_source_id as sourceFormItemId,
               pharmacist_id as pharmacistId,
               seller_id as sellerId,
               remarks as comment,
               batch_id as batchId,
               is_dismounting as isDismounting,
               max(piece_num) as pieceNum,
               sum(if (status = 2, 0, 1) * deduct_discount_fee) as deductDiscountFee,
               sum(if (status = 2, 0, 1) * unit_adjustment_fee) as unitAdjustmentFee,
               sum(if (status = 2, 0, 1) * patient_point_promotion_fee) as patientPointPromotionFee,
               sum(if (status = 2, 0, 1) * discount_promotion_fee) as discountPromotionFee,
               sum(if (status = 2, 0, 1) * coupon_fee) as couponFee,
               sum(if (status = 2, 0, 1) * gift_rule_promotion_fee) as giftRulePromotionFee,
               sum(if (status = 2, 0, 1) * gift_promotion_fee) as giftPromotionFee,
               cooperation_clinic_id as cooperationClinicId,
               sum(if(promotion_info::json ->> 'oddFee' is not null,(promotion_info::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
        from ${env}.dwd_charge_form_item
        where
                1=1
                and chain_id = #{params.chainId}
                and is_deleted = 0
                and charge_sheet_status != 0
                <if test="params.clinicId != null and params.clinicId != ''">
                    and clinic_id = #{params.clinicId}
                </if>
                <if test="params.beginDate != null and params.endDate != null">
                    and charge_sheet_created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds between to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
                </if>
                <if test="params.endChargedDate != null and params.beginChargedDate != null">
                    and charge_sheet_charged_time between to_timestamp(#{params.beginChargedDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endChargedDate}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                    ${params.sourceTypeSQL}
                </if>
                and goods_fee_type in (0, 2)
                and charge_sheet_type != 5
                <if test="params.sellNo != null and params.sellNo != ''">
                    and sell_no = #{params.sellNo}
                </if>
                <if test="params.patientId != null and params.patientId != ''">
                    and patient_id = #{params.patientId}
                </if>
                <if test="params.sellerId != null and params.sellerId != null">
                    and charged_by = #{params.sellerId}
                </if>
                <if test="params.fee1 != null and params.fee2 != null">
                    and (${params.fee1} or ${params.fee2})
                </if>
                <if test="params.fee1 != null and params.fee2 == null">
                    and ${params.fee1}
                </if>
                <if test="params.fee2 != null and params.fee1 == null">
                    and ${params.fee2}
                </if>
                <if test="params.tagIdSql != null and params.tagIdSql != ''">
                    and ${params.tagIdSql}
                </if>
                <if test="params.chargeSheetId != null and params.chargeSheetId != ''">
                    and charge_sheet_id = #{params.chargeSheetId}
                </if>
                <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
                    and cooperation_clinic_id = #{params.cooperationClinicId}
                </if>
                <if test="params.productId != null and params.productId != ''">
                    and product_id = #{params.productId}
                </if>
                <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                    and ${params.searchDoctorSql}
                </if>
                <if test='params.payModeSql != null and params.payModeSql != ""'>
                    and ${params.payModeSql}
                </if>
                <if test='params.batchSql != null and params.batchSql != ""'>
                    and ${params.batchSql}
                </if>
        group by chain_id,clinic_id,charge_sheet_created,charge_sheet_charged_time,
                charge_sheet_id, batch_id,
                sell_no,
                member_id,
                charge_sheet_type,
                charged_by,
                charge_sheet_source_id,
                pharmacist_id, seller_id,product_id,cooperation_clinic_id, classify_level_1_id,classify_level_2_id,unit, remarks, is_dismounting, if (charge_form_id is null, id, charge_form_id)
        order by charge_sheet_created desc,charge_sheet_id, product_id, batch_id
        <if test="params.size != null and params.offset != null ">
            limit #{params.size} offset #{params.offset}
        </if>
    </select>

    <select id="selectPharmacyCommodityTotal"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailPharmacyTotalResp">
        select count(1) as count,
                sum(totalPrice) as totalPrice,
                sum(if (isDismounting = 0, count, count/pieceNum)) as bigCount,
                sum(deductDiscountFee + unitAdjustmentFee + patientPointPromotionFee + discountPromotionFee + couponFee + giftRulePromotionFee + adjustmentPrice + giftPromotionFee + roundedAmount) as preferentialFee,
                sum(receivablePrice) as receivablePrice,
                sum(receivedPrice) as receivedPrice,
                sum(cost) as cost  from(
        select charge_sheet_created as createTime,
               charge_sheet_charged_time as chargedTime,
               sell_no as sellNo,
               member_id as patientId,
               charge_sheet_type as chargeSheetType,
               sum(if (status = 2, 0, 1) * source_total_price) as totalPrice,
               product_id as goodsId,
               classify_level_1_id as classifyLevel1,
               classify_level_2_id as classifyLevel2,
               sum(if (status = 2, -1, 1) * unit_count) as count,
               unit,
               sum(if (status = 2, 0, 1) * adjustment_price) as adjustmentPrice,
               sum(if (status = 2, 0, 1) * receivable_price) as receivablePrice,
               sum(if (status = 2, -1, 1) * received_price) as receivedPrice,
               sum(total_cost_price) as cost,
               max(pay_mode::text) as payModeJson,
               sum(if (status = 2, -1, 1) * point) as point,
               charged_by as cashierId,
               charge_sheet_source_id as sourceFormItemId,
               pharmacist_id as pharmacistId,
               seller_id as sellerId,
               remarks as comment,
               sum(if (status = 2, 0, 1) * deduct_discount_fee) as deductDiscountFee,
               sum(if (status = 2, 0, 1) * unit_adjustment_fee) as unitAdjustmentFee,
               sum(if (status = 2, 0, 1) * patient_point_promotion_fee) as patientPointPromotionFee,
               sum(if (status = 2, 0, 1) * discount_promotion_fee) as discountPromotionFee,
               sum(if (status = 2, 0, 1) * coupon_fee) as couponFee,
               sum(if (status = 2, 0, 1) * gift_rule_promotion_fee) as giftRulePromotionFee,
               sum(if (status = 2, 0, 1) * gift_promotion_fee) as giftPromotionFee,
               is_dismounting as isDismounting,
               max(piece_num) as pieceNum,
               cooperation_clinic_id as cooperationClinicId,
               sum(if(promotion_info::json ->> 'oddFee' is not null,(promotion_info::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
        from ${env}.dwd_charge_form_item
        where 1=1
                and chain_id = #{params.chainId}
                and is_deleted = 0
                and charge_sheet_status != 0
                <if test="params.clinicId != null and params.clinicId != ''">
                    and clinic_id = #{params.clinicId}
                </if>
                <if test="params.beginDate != null and params.endDate != null">
                    and charge_sheet_created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds between to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
                </if>
                <if test="params.endChargedDate != null and params.beginChargedDate != null">
                    and charge_sheet_charged_time between to_timestamp(#{params.beginChargedDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endChargedDate}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                    ${params.sourceTypeSQL}
                </if>
                and goods_fee_type in (0, 2)
                and charge_sheet_type != 5
                <if test="params.sellNo != null and params.sellNo != ''">
                    and sell_no = #{params.sellNo}
                </if>
                <if test="params.patientId != null and params.patientId != ''">
                    and patient_id = #{params.patientId}
                </if>
                <if test="params.sellerId != null and params.sellerId != null">
                    and charged_by = #{params.sellerId}
                </if>
                <if test="params.fee1 != null and params.fee2 != null">
                    and (${params.fee1} or ${params.fee2})
                </if>
                <if test="params.fee1 != null and params.fee2 == null">
                    and ${params.fee1}
                </if>
                <if test="params.fee2 != null and params.fee1 == null">
                    and ${params.fee2}
                </if>
                <if test="params.tagIdSql != null and params.tagIdSql != ''">
                    and ${params.tagIdSql}
                </if>
                <if test="params.chargeSheetId != null and params.chargeSheetId != ''">
                    and charge_sheet_id = #{params.chargeSheetId}
                </if>
                <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
                    and cooperation_clinic_id = #{params.cooperationClinicId}
                </if>
                <if test="params.productId != null and params.productId != ''">
                    and product_id = #{params.productId}
                </if>
                <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                    and ${params.searchDoctorSql}
                </if>
                <if test='params.payModeSql != null and params.payModeSql != ""'>
                    and ${params.payModeSql}
                </if>
                <if test='params.batchSql != null and params.batchSql != ""'>
                    and ${params.batchSql}
                </if>
        group by charge_sheet_created,charge_sheet_charged_time,
            charge_sheet_id,
            sell_no,
            member_id,
            charge_sheet_type,
            charged_by,
            charge_sheet_source_id,
            pharmacist_id, seller_id,product_id, cooperation_clinic_id,classify_level_1_id,classify_level_2_id,unit, remarks, is_dismounting) a
    </select>

    <select id="selectPharmacyBatchCommodityTotal"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailPharmacyTotalResp">
        select count(1) as count,
                sum(totalPrice) as totalPrice,
                sum(if (isDismounting = 0, count, count/pieceNum)) as bigCount,
                sum(deductDiscountFee + unitAdjustmentFee + patientPointPromotionFee + discountPromotionFee + couponFee + giftRulePromotionFee + adjustmentPrice + giftPromotionFee + roundedAmount) as preferentialFee,
                sum(receivablePrice) as receivablePrice,
                sum(receivedPrice) as receivedPrice,
                sum(cost) as cost  from(
        select chain_id as chainId,
                clinic_id as clinicId,
                charge_sheet_id as chargeSheetId,
                charge_sheet_created as created,
                charge_sheet_charged_time as chargedTime,
                sell_no as sellNo,
                member_id as memberId,
                charge_sheet_type as chargeSheetType,
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                classify_level_2_id as classifyLevel2,
                sum(if (status = 2, 0, 1) * source_total_price) as totalPrice,
                sum(if (status = 2, -1, 1) * unit_count) as count,
                unit,
                sum(if (status = 2, 0, 1) * adjustment_price) as adjustmentPrice,
                sum(if (status = 2, 0, 1) * receivable_price)as receivablePrice,
                sum(if (status = 2, -1, 1) * received_price) as receivedPrice,
                sum(total_cost_price) as cost,
                sum(if (status = 2, -1, 1) * point) as point,
                charged_by as cashierId,
                charge_sheet_source_id as sourceFormItemId,
                pharmacist_id as pharmacistId,
                seller_id as sellerId,
                remarks as comment,
                batch_id as batchId,
                is_dismounting as isDismounting,
                max(piece_num) as pieceNum,
                sum(if (status = 2, 0, 1) * deduct_discount_fee) as deductDiscountFee,
                sum(if (status = 2, 0, 1) * unit_adjustment_fee) as unitAdjustmentFee,
                sum(if (status = 2, 0, 1) * patient_point_promotion_fee) as patientPointPromotionFee,
                sum(if (status = 2, 0, 1) * discount_promotion_fee) as discountPromotionFee,
                sum(if (status = 2, 0, 1) * coupon_fee) as couponFee,
                sum(if (status = 2, 0, 1) * gift_rule_promotion_fee) as giftRulePromotionFee,
                sum(if (status = 2, 0, 1) * gift_promotion_fee) as giftPromotionFee,
                cooperation_clinic_id as cooperationClinicId,
                sum(if(promotion_info::json ->> 'oddFee' is not null,(promotion_info::json ->> 'oddFee')::numeric, 0.0)) as roundedAmount
        from ${env}.dwd_charge_form_item
                where
                    1=1
                    and chain_id = #{params.chainId}
                    and is_deleted = 0
                    and charge_sheet_status != 0
                    <if test="params.clinicId != null and params.clinicId != ''">
                        and clinic_id = #{params.clinicId}
                    </if>
                    <if test="params.beginDate != null and params.endDate != null">
                        and charge_sheet_created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                        and ds between to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
                    </if>
                    <if test="params.endChargedDate != null and params.beginChargedDate != null">
                        and charge_sheet_charged_time between to_timestamp(#{params.beginChargedDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endChargedDate}, 'YYYY-MM-DD HH24:MI:SS')
                    </if>
                    <if test="params.sourceTypeSQL != null and params.sourceTypeSQL != ''">
                        ${params.sourceTypeSQL}
                    </if>
                    and goods_fee_type in (0, 2)
                    and charge_sheet_type != 5
                    <if test="params.sellNo != null and params.sellNo != ''">
                        and sell_no = #{params.sellNo}
                    </if>
                    <if test="params.patientId != null and params.patientId != ''">
                        and patient_id = #{params.patientId}
                    </if>
                    <if test="params.sellerId != null and params.sellerId != null">
                        and charged_by = #{params.sellerId}
                    </if>
                    <if test="params.fee1 != null and params.fee2 != null">
                        and (${params.fee1} or ${params.fee2})
                    </if>
                    <if test="params.fee1 != null and params.fee2 == null">
                        and ${params.fee1}
                    </if>
                    <if test="params.fee2 != null and params.fee1 == null">
                        and ${params.fee2}
                    </if>
                    <if test="params.tagIdSql != null and params.tagIdSql != ''">
                        and ${params.tagIdSql}
                    </if>
                    <if test="params.chargeSheetId != null and params.chargeSheetId != ''">
                        and charge_sheet_id = #{params.chargeSheetId}
                    </if>
                    <if test="params.cooperationClinicId != null and params.cooperationClinicId != ''">
                        and cooperation_clinic_id = #{params.cooperationClinicId}
                    </if>
                    <if test="params.productId != null and params.productId != ''">
                        and product_id = #{params.productId}
                    </if>
                    <if test='params.searchDoctorSql != null and params.searchDoctorSql != ""'>
                        and ${params.searchDoctorSql}
                    </if>
                    <if test='params.payModeSql != null and params.payModeSql != ""'>
                        and ${params.payModeSql}
                    </if>
                    <if test='params.batchSql != null and params.batchSql != ""'>
                        and ${params.batchSql}
                    </if>
                group by chain_id,clinic_id,charge_sheet_created,charge_sheet_charged_time,
                        charge_sheet_id, batch_id,
                        sell_no,
                        member_id,
                        charge_sheet_type,
                        charged_by,
                        charge_sheet_source_id,
                        pharmacist_id, seller_id,product_id,cooperation_clinic_id, classify_level_1_id,classify_level_2_id,unit, remarks, is_dismounting, if (charge_form_id is null, id, charge_form_id)) a
    </select>

    <select id="selectDispensingCost" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDailyRpcResp">
        SELECT
            chain_id as chainId,
            clinic_id as clinicId,
            sum(if(type in(4,6),-cost_price,cost_price)) AS cost
        FROM
            ${cisTable}.dwd_dispensing_log_v_partition
        where
            log_time between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        group by
            chain_id,clinic_id
    </select>
</mapper>
