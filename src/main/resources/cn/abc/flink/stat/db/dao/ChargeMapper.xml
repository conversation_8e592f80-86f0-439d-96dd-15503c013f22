<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.dao.ChargeMapper">
    <select id="selectSheetAndTransactionById" resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.ChargeTransactionSheetDao">
        select
            cs.patient_id as patientId,
            po.no as orderNo,

            if(ca.specified_charged_time is not null and ca.specified_charged_time != '', ca.specified_charged_time, ct.created) as receiveTime,
            ct.pay_mode as payType,
            ct.pay_sub_mode as paySubType,
            ct.created_by as cashierId,
            <choose>
                <when test="productId != null and productId != ''">
                    if (cs.data_version = 1, cfi.receivable_price, (cfi.total_price + cfi.discount_price)) as shouldReceivePrice,
                    (ctr.total_price + ctr.discount_price) as amount,
                </when>
               <otherwise>
                    cs.receivable_fee as shouldReceivePrice,
                    ct.amount as amount,
               </otherwise>
            </choose>
            if(ca.type=1, '退', if(pay_mode = 20, '欠', '收')) as type
        from
            ${chargeDb}.v2_charge_sheet cs
        inner join
            ${chargeDb}.v2_charge_transaction ct on cs.id = ct.charge_sheet_id
        inner join
            ${chargeDb}.v2_charge_action ca on ct.charge_action_id=ca.id
        inner join
            ${patientorderDb}.v2_patientorder po on cs.patient_order_id = po.id
        <if test="productId != null and productId != ''">
            inner join ${chargeRecordDb}.v2_charge_form_item cfi on cfi.charge_sheet_id = cs.id and cfi.product_id = #{productId}  and cfi.associate_form_item_id is null
            <if test="v2ChargeFormItemId != null and v2ChargeFormItemId.size > 0">
                and cfi.id in <foreach collection="v2ChargeFormItemId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            inner join ${chargeRecordDb}.v2_charge_transaction_record ctr on ct.id = ctr.transaction_id and ctr.product_id = #{productId}
            <if test="v2ChargeFormItemId != null and v2ChargeFormItemId.size > 0">
                and ctr.charge_form_item_id in <foreach collection="v2ChargeFormItemId" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            </if>
        </if>
        where cs.chain_id = #{chainId}
        and cs.id = #{id}
        and ct.pay_mode != 100
        <if test="clinicId != null and clinicId != ''">
            and cs.clinic_id = #{clinicId}
        </if>
        order by ct.created desc
    </select>

    <select id="selectChargeFormItemIds" resultType="java.lang.String">
        select id from ${chargeDb}.v2_charge_form_item
        where charge_sheet_id = #{id}
          and chain_id = #{chainId}
          <if test="clinicId != null and clinicId != ''">
              and clinic_id = #{clinicId}
          </if>
          and (id = #{v2ChargeFormItemId} or associate_form_item_id = #{v2ChargeFormItemId});
    </select>

    <select id="selectChargeFormItemId" resultType="java.lang.String">
        select associate_form_item_id from ${chargeDb}.v2_charge_form_item
        where charge_sheet_id = #{id}
          and chain_id = #{chainId}
          <if test="clinicId != null and clinicId != ''">
              and clinic_id = #{clinicId}
          </if>
          and id = #{v2ChargeFormItemId}
    </select>

    <select id="selectPatientorderHospitalExtendById"
            resultType="cn.abc.flink.stat.common.domain.V2PatientorderHospitalExtend">
        select
            id,
            fee_type_name as feeTypeName,
            bed_no as bedNo,
            doctor_id as doctorId,
            nurse_id as nurseId,
            status,
            inpatient_time as inpatientTime,
            discharge_time as dischargeTime
        from ${table}.v2_patientorder_hospital_extend
        where chain_id = #{chainId}
          and is_deleted = 0
          and id in
          <foreach collection="ids" open="(" close=")" separator="," item="id">
              #{id}
          </foreach>
    </select>

    <select id="selectChargeDeposits" resultType="cn.abc.flink.stat.common.domain.V2HisChargeDeposit">
        select
            patient_order_id as patientOrderId,
            recharge_fee as rechargeFee,
            refunded_fee as refundedFee,
            used_fee as usedFee
        from  ${table}.v2_his_charge_deposit
            where chain_id = #{chainId}
            and is_deleted = 0
            and patient_order_id in
            <foreach collection="ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
    </select>

    <select id="selectChargeSheetByIds" resultType="cn.abc.flink.stat.dimension.domain.V2ChargeSheet">
        select
            id as id,
            receivable_fee as receivableFee,
            received_fee as receivedFee
        from
            ${chargeDb}.v2_charge_sheet
        where
            chain_id = #{chainId}
            and id in
            <foreach item="id" collection="notInCacheIds" separator="," open="(" close=")">
                #{id}
            </foreach>
    </select>

    <select id="queryPayModeBySheetId"
            resultType="cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.ChargeChangePayModeRecordDto">
        select
            id,
            chain_id as chainId,
            clinic_id as clinicId,
            business_id as businessId,
            update_pay_mode_info as updatePayModeInfo,
            remark as remark,
            created,
            created_by as createdBy,
            created_by_name as createdByName,
            last_modified_by as lastModifiedBy,
            last_modified as lastModified
        from ${env}.v2_charge_change_pay_mode_record
        where business_id in
          <foreach collection="chargeSheetIds" index="id" item="id" open="(" separator="," close=")">
              #{id}
          </foreach>
          and chain_id = #{chainId}
          <if test="clinicId != null and clinicId != ''">
              and clinic_id = #{clinicId}
          </if>
        order by created desc
    </select>
</mapper>