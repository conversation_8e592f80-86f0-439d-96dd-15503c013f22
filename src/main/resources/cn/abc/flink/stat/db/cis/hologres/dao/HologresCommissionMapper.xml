<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresCommissionMapper">

    <!--获取挂号明细数据-->
    <select id="getRegistrationCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.registration.domain.RegistrationDetail">
        select
            v2_registration_form_id as registrationFormItemId,
            v2_registration_sheet_id as registrationSheetId,
            v2_patientorder_id as patientOrderId,
            chain_id as chainId,
            clinic_id as clinicId,
            doctor_id as doctorId,
            doctor_name as doctorName,
            patient_id as patientId,
            channel as channel,
            revisit_status as revisitStatus,
            department_id as departmentId,
            reserve_start as reserveStart,
            reserve_date as reserveDate,
            order_no as orderNo,
            visit_source_id as visitSourceId,
            visit_source_remark as visitSourceRemark,
            patient_source_id as patientSourceId,
            patient_source_from as patientSourceFrom,
            pay_status_v2 as payStatusV2,
            status_v2 as statusV2,
            original_price as fee,
            received_price as received,
            if(if(status_v2=90,0.0,cost_price) is null,0.0,if(status_v2=90,0.0,cost_price)) as costPrice,
            deduct_promotion_price as deductPromotionPrice,
            cashier_id as cashierId,
            created_by as createdBy,
            created as created,
            type as type,
            is_reserved as isReserved,
            sign_in_time as signInTime,
            visit_source_level1_id as visitSourceLevel1Id,
            visit_source_level2_id as visitSourceLevel2Id,
            visit_source_from as visitSourceFrom,
            is_additional as isAdditional
        from ${env}.dwd_registration
        where
        chain_id = #{param.chainId}
        and reserve_date between date(#{param.beginDate}) and date(#{param.endDate})
        and ds between #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql!= ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.typeSql != null and param.typeSql!= ''">
            and ${param.typeSql}
        </if>
        and status_v2 in (20, 22, 30, 31, 40, 41, 90, 91)
        order by created desc
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <!--获取挂号翻页数据-->
    <select id="fetchCommissionRegistrationDetailTotal" resultType="long">
        select
        count(1) as totalCount
        from ${env}.dwd_registration
        where
        chain_id = #{param.chainId}
        and reserve_date between date(#{param.beginDate}) and date(#{param.endDate})
        and ds between #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql!= ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.typeSql != null and param.typeSql!= ''">
            and ${param.typeSql}
        </if>
        and status_v2 in (20, 22, 30, 31, 40, 41, 90, 91)
    </select>

    <!--销售提成明细数据-->
    <select id="getSaleCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.commission.domain.SaleDetailEntity">
        select * from
        (
        <if test="param.amountClassify == 1">
            select
                if(a.chainId is null, b.chainId, a.chainId) as chainId,
                if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
                if(a.employeeId is null, b.employeeId, a.employeeId) as employeeId,
                if(a.employeeSnapId is null, b.employeeSnapId, a.employeeSnapId) as employeeSnapId,
                if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
                if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
                if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
                if(a.unit is null, b.unit, a.unit) as unit,
                if(a.unitCount is null, 0.0, a.unitCount) as count,
                cast(if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as decimal(20,8)) as costPrice,
                if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
                if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
                if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice,
                0 as isHisData,
                0 as prescriptionNum,
                0 as patientOrderNum
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                    calc_unit as unit,
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(calc_count) as unitCount,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
                from ${cisDb}.dwd_charge_transaction_record_v_partition
                where product_type not in (17, 18)
                and record_type not in (2, 3)
                and is_deleted = 0
                and chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    and product_compose_type = 0
                    and goods_fee_type in (0, 1)
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                    <if test="param.isIncludeCompose == 1">
                        and product_compose_type = 0
                        and goods_fee_type in (0, 1)
                    </if>
                    <if test="param.isIncludeCompose == 0">
                        and product_compose_type in (0, 2)
                        and goods_fee_type in (0, 1)
                    </if>
                </if>
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                group by chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit
            ) a
            full OUTER JOIN
            (
                select chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit, sum(costPrice) as costPrice
                from
                (
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'))) as employeeId,
                        if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                        product_id as goodsId,
                        classify_level_1_id as classifyLevel1,
                        if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                        calc_unit as unit,
                        sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                    from ${cisDb}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 0
                    and form_type = 0
                    <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                        ${param.snapDispensingEmployeeIdsSql}
                    </if>
                    <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                        and ${param.dispensingEmployeeIdsSql}
                    </if>
                    <if test="param.whereSQL != null and param.whereSQL != ''">
                        and ${param.whereSQL}
                    </if>
                    GROUP BY chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit
                    <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                        union all
                        select
                            chain_id as chainId,
                            clinic_id as clinicId,
                            if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                            if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                            product_id as goodsId,
                            classify_level_1_id as classifyLevel1,
                            if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                            calc_unit as unit,
                            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                        from ${cisDb}.dwd_dispensing_log_v_partition
                        where chain_id=#{param.chainId}
                        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                        and form_type = 0
                        and product_compose_type = 2
                        <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                            ${param.snapDispensingEmployeeIdsSql}
                        </if>
                        <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                            and ${param.dispensingEmployeeIdsSql}
                        </if>
                        <if test="param.whereSQL != null and param.whereSQL != ''">
                            and ${param.whereSQL}
                        </if>
                        group by chainId,clinicId,employeeId,employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
                    </if>
                ) as bb
                group by chainId,clinicId,employeeId,employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
            ) b
            on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeSnapId=b.employeeSnapId and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit
            <if test="param.hisType == 100">
                <if test="param.hospitalFeeCommissionTiming == null or param.hospitalFeeCommissionTiming == 1">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                        0::BIGINT as employeeSnapId,
                        advice_goods_id as goodsId,
                        class_level1_id as classifyLevel1,
                        if(classify_level2_id is null, 0, classify_level2_id) as classifyLevel2,
                        product_unit as unit,
                        sum(if(charge_type=-1, -1, 1) * product_unit_count) as count,
                        sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                        sum(if(charge_type=-1, -1, 1) * total_price) as amount,
                        sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                        0.0 as deductPrice,
                        1 as isHisData,
                        0 as prescriptionNum,
                        0 as patientOrderNum
                    from ${hisDb}.dwd_his_charge_settle_transaction_record
                    where chain_id=#{param.chainId}
                    and goods_fee_type in (0,1)
                    and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                        and ${param.hisWhereSQL}
                    </if>
                    <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                        ${param.snapHisEmployeeIdsSql}
                    </if>
                    <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                        and ${param.hisEmployeeIdsSql}
                    </if>
                    group by chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit
                </if>
                <if test="param.hospitalFeeCommissionTiming != null and param.hospitalFeeCommissionTiming == 2">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(doctor_id is not null and doctor_id != '', doctor_id, '00000000000000000000000000000000') as employeeId,
                        0::BIGINT as employeeSnapId,
                        if(advice_classification in ('1-1','1-3','1-12','1-16','1-13','2-0','2-1','2-2','7-0','7-1','7-2','7-3','7-4'), goods_id, advice_goods_id) as goodsId,
                        advice_classification as classifyLevel1,
                        if(advice_sub_classification is null, 0, advice_sub_classification) as classifyLevel2,
                        unit,
                        sum(if(type = 1, -1, 1) * count) as count,
                        sum(if(type=1, -1, 1) * total_cost_price) as costPrice,
                        sum(if(type=1, -1, 1) * amount) as amount,
                        sum(if(type=1, -1, 1) * amount) as originPrice,
                        0.0 as deductPrice,
                        1 as isHisData,
                        0 as prescriptionNum,
                        0 as patientOrderNum
                    from ${hisDb}.dwd_his_charge_form_item_record
                    where chain_id=#{param.chainId}
                    and goods_fee_type in(0,1)
                    and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                        and ${param.hisWhereSQL}
                    </if>
                    <if test="param.employeeId != null and param.employeeId != ''">
                        and doctor_id=#{param.employeeId}
                    </if>
                    <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                        and ${param.hisEmployeeIdsSql}
                    </if>
                    group by chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit
                </if>
            </if>
        </if>
        <if test="param.acceptClassify == 1 and param.amountClassify == 1 ">
            union all
        </if>
        <if test="param.acceptClassify == 1">
            select
                chainId,
                clinicId,
                employeeId,
                employeeSnapId,
                null as goodsId,
                null as classifyLevel1,
                0 as classifyLevel2,
                null as unit,
                0.0 as count,
                0.0 as costPrice,
                0.0 as amount,
                0.0 as originPrice,
                0.0 as deductPrice,
                0 as isHisData,
                0 as prescriptionNum,
                sum(if(amount>0, 1, if(amount &lt; 0, -1, 0))) as patientOrderNum
            from
            (
                select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                        if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                        v2_patient_order_id as patientorderId,
                        sum(if(type=-1, -1, 1) * received_price) as amount
                    from ${cisDb}.dwd_charge_transaction_record_v_partition
                    where product_type not in (17, 18)
                    and record_type not in (2, 3)
                    and charge_sheet_type in (2,4,7)
                    and is_deleted = 0
                    and source_form_type != 1
                    and chain_id=#{param.chainId}
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    <if test="param.statConfigSql != null and param.statConfigSql != ''">
                        and ${param.statConfigSql}
                    </if>
                    group by chainId, clinicId, employeeId, employeeSnapId, patientOrderId
            ) a
            GROUP BY chainId,clinicId,employeeId, employeeSnapId
        </if>
        <if test="param.prescriptionClassify == 1 and (param.amountClassify == 1 or param.acceptClassify == 1)">
            union all
        </if>
        <if test="param.prescriptionClassify == 1">
            select
                chainId,
                clinicId,
                employeeId,
                employeeSnapId,
                null as goodsId,
                null as classifyLevel1,
                0 as classifyLevel2,
                null as unit,
                0.0 as count,
                0.0 as costPrice,
                0.0 as amount,
                0.0 as originPrice,
                0.0 as deductPrice,
                0 as isHisData,
                sum(if(amount>0, 1, if(amount &lt; 0, -1, 0)) * num) as prescriptionNum,
                0 as patientOrderNum
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                    v2_patient_order_id as patientorderId,
                    v2_charge_form_id as chargeFormId,
                    1.0 as num,
                    sum(if(type=-1, -1, 1) * received_price) as amount
                from ${cisDb}.dwd_charge_transaction_record_v_partition
                where product_type not in (17, 18)
                and record_type not in (2, 3)
                and source_form_type in (4,5,6,16)
                and charge_sheet_type in (2,4,7)
                and chain_id=#{param.chainId}
                and is_deleted = 0
                and goods_fee_type in (0,1)
                and import_flag = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type in (0, 2)
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                group by chainId, clinicId, employeeId, employeeSnapId, patientOrderId, chargeFormId
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                    v2_patient_order_id as patientorderId,
                    null as chargeFormId,
                    cast(extend_prescription ->> 'eyeglassPrescriptionCount' as NUMERIC) as num,
                    sum(if(type=-1, -1, 1) * received_price) as amount
                from ${cisDb}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and record_type not in (2, 3)
                and product_type not in (17, 18)
                and extend_prescription is not null
                and import_flag = 0
                and charge_sheet_type in (2,4,7)
                and source_form_type != 1
                and is_deleted = 0
                and goods_fee_type in(0,1)
                and product_compose_type in (0, 2)
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                group by chainId, clinicId, employeeId, employeeSnapId, patientOrderId, chargeFormId, num
            ) a
            group by chainId, clinicId, employeeId,employeeSnapId,goodsId,classifyLevel1,classifyLevel2,unit,count,costPrice,originPrice,deductPrice
        </if>
        <if test="param.isIncludeCompose == 1 and (param.acceptClassify == 1 or param.amountClassify == 1 or param.prescriptionClassify == 1)">
            union all
        </if>
        <if test="param.isIncludeCompose == 1">
            select
                if(a.chainId is null, b.chainId, a.chainId) as chainId,
                if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
                if(a.employeeId is null, b.employeeId, a.employeeId) as employeeId,
                if(a.employeeSnapId is null, b.employeeSnapId, a.employeeSnapId) as employeeSnapId,
                if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
                if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
                if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
                if(a.unit is null, b.unit, a.unit) as unit,
                if(a.unitCount is null, 0.0, a.unitCount) as count,
                cast(if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as decimal(20,8)) as costPrice,
                if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
                if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
                if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice,
                0 as isHisData,
                0 as prescriptionNum,
                0 as patientOrderNum
            from
            (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
                sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount,
                sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
            from
            ${cisDb}.dwd_charge_transaction_record_v_partition
            where record_type not in (2, 3)
            and product_type not in (17, 18)
            and chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type in (1,2)
            and is_deleted = 0
            and import_flag = 0
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
        ) a
        full OUTER JOIN
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null, 0, classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
                sum(if(product_compose_type = 2, if(type=4, -cost_price, cost_price), 0.0)) as costPrice
            from ${cisDb}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and form_type = 0
            and product_compose_type in (1,2)
            <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                ${param.snapDispensingEmployeeIdsSql}
            </if>
            <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                and ${param.dispensingEmployeeIdsSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
        ) b
        on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeSnapId=b.employeeSnapId and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit
        </if>
        ) h
        order by clinicId, employeeId, employeeSnapId, goodsId
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <!--获取销售翻页数据-->
    <select id="fetchCommissionSaleDetailTotal" resultType="long">
        select count(1) as count from
        (
        <if test="param.amountClassify == 1">
            select
            if(a.chainId is null, b.chainId, a.chainId) as chainId,
            if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
            if(a.employeeId is null, b.employeeId, a.employeeId) as employeeId,
            if(a.employeeSnapId is null, b.employeeSnapId, a.employeeSnapId) as employeeSnapId,
            if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
            if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
            if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
            if(a.unit is null, b.unit, a.unit) as unit,
            if(a.unitCount is null, 0.0, a.unitCount) as count,
            cast(if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as decimal(20,8)) as costPrice,
            if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
            if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
            if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice,
            0 as isHisData,
            0 as prescriptionNum,
            0 as patientOrderNum
            from
            (
            select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
            if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
            product_id as goodsId,
            classify_level_1_id as classifyLevel1,
            if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
            calc_unit as unit,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(calc_count) as unitCount,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
            from ${cisDb}.dwd_charge_transaction_record_v_partition
            where product_type not in (17, 18)
            and record_type not in (2, 3)
            and is_deleted = 0
            and chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0
                and goods_fee_type in (0, 1)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                <if test="param.isIncludeCompose == 1">
                    and product_compose_type = 0
                    and goods_fee_type in (0, 1)
                </if>
                <if test="param.isIncludeCompose == 0">
                    and product_compose_type in (0, 2)
                    and goods_fee_type in (0, 1)
                </if>
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            group by chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit
            ) a
            full OUTER JOIN
            (
            select chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit,
            sum(costPrice) as costPrice
            from
            (
                select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id,
                '00000000000000000000000000000000'), if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id
                is not null and seller_id != '',seller_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '',
                if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                calc_unit as unit,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                from ${cisDb}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and
                to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                    ${param.snapDispensingEmployeeIdsSql}
                </if>
                <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                    and ${param.dispensingEmployeeIdsSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                GROUP BY chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit
                <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                    union all
                    select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id,
                    '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id,
                    if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as
                    employeeId,
                    if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '',
                    if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                    calc_unit as unit,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                    from ${cisDb}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and
                    to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and form_type = 0
                    and product_compose_type = 2
                    <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                        ${param.snapDispensingEmployeeIdsSql}
                    </if>
                    <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                        and ${param.dispensingEmployeeIdsSql}
                    </if>
                    <if test="param.whereSQL != null and param.whereSQL != ''">
                        and ${param.whereSQL}
                    </if>
                    group by chainId,clinicId,employeeId,employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
                </if>
                ) c
                group by chainId, clinicId, employeeId, employeeSnapId, goodsId, classifyLevel1, classifyLevel2, unit
            ) b
            on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeSnapId=b.employeeSnapId and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit
            <if test="param.hisType == 100">
                <if test="param.hospitalFeeCommissionTiming == null or param.hospitalFeeCommissionTiming == 1">
                    union all
                    select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                    0::BIGINT as employeeSnapId,
                    advice_goods_id as goodsId,
                    class_level1_id as classifyLevel1,
                    if(classify_level2_id is null, 0, classify_level2_id) as classifyLevel2,
                    product_unit as unit,
                    sum(if(charge_type=-1, -1, 1) * product_unit_count) as count,
                    sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                    sum(if(charge_type=-1, -1, 1) * total_price) as amount,
                    sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                    0.0 as deductPrice,
                    1 as isHisData,
                    0 as prescriptionNum,
                    0 as patientOrderNum
                    from ${hisDb}.dwd_his_charge_settle_transaction_record
                    where chain_id=#{param.chainId}
                    and goods_fee_type in (0,1)
                    and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                        and ${param.hisWhereSQL}
                    </if>
                    <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                        ${param.snapHisEmployeeIdsSql}
                    </if>
                    <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                        and ${param.hisEmployeeIdsSql}
                    </if>
                    group by chainId, clinicId, employeeId, employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
                </if>
                <if test="param.hospitalFeeCommissionTiming != null and param.hospitalFeeCommissionTiming == 2">
                    union all
                    select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, '00000000000000000000000000000000') as employeeId,
                    0::BIGINT as employeeSnapId,
                    if(advice_classification in ('1-1','1-2','1-3','1-12','1-16','1-13','2-0','2-1','2-2','7-0','7-1','7-2','7-3','7-4'), goods_id, advice_goods_id) as goodsId,
                    advice_classification as classifyLevel1,
                    if(advice_sub_classification is null, 0, advice_sub_classification) as classifyLevel2,
                    unit,
                    sum(if(type = 1, -1, 1) * count) as count,
                    sum(if(type=1, -1, 1) * total_cost_price) as costPrice,
                    sum(if(type=1, -1, 1) * amount) as amount,
                    sum(if(type=1, -1, 1) * amount) as originPrice,
                    0.0 as deductPrice,
                    1 as isHisData,
                    0 as prescriptionNum,
                    0 as patientOrderNum
                    from ${hisDb}.dwd_his_charge_form_item_record
                    where chain_id=#{param.chainId}
                    and goods_fee_type in(0,1)
                    and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                        and ${param.hisWhereSQL}
                    </if>
                    <if test="param.employeeId != null and param.employeeId != ''">
                        and doctor_id=#{param.employeeId}
                    </if>
                    <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                        and ${param.hisEmployeeIdsSql}
                    </if>
                    group by chainId, clinicId, employeeId, employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
                </if>
            </if>
        </if>
        <if test="param.acceptClassify == 1 and param.amountClassify == 1 ">
            union all
        </if>
        <if test="param.acceptClassify == 1">
            select
            chainId,
            clinicId,
            employeeId,
            employeeSnapId,
            null as goodsId,
            null as classifyLevel1,
            0 as classifyLevel2,
            null as unit,
            0.0 as count,
            0.0 as costPrice,
            0.0 as amount,
            0.0 as originPrice,
            0.0 as deductPrice,
            0 as isHisData,
            0 as prescriptionNum,
            sum(if(amount>0, 1, if(amount &lt; 0, -1, 0))) as patientOrderNum
            from
            (
            select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
            if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
            v2_patient_order_id as patientorderId,
            sum(if(type=-1, -1, 1) * received_price) as amount
            from ${cisDb}.dwd_charge_transaction_record_v_partition
            where product_type not in (17, 18)
            and record_type not in (2, 3)
            and charge_sheet_type in (2,4,7)
            and is_deleted = 0
            and source_form_type != 1
            and chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId, clinicId, employeeId, employeeSnapId,patientOrderId
            ) a
            GROUP BY chainId,clinicId,employeeId,employeeSnapId
        </if>
        <if test="param.prescriptionClassify == 1 and (param.amountClassify == 1 or param.acceptClassify == 1)">
            union all
        </if>
        <if test="param.prescriptionClassify == 1">
            select
            chainId,
            clinicId,
            employeeId,
            employeeSnapId,
            null as goodsId,
            null as classifyLevel1,
            0 as classifyLevel2,
            null as unit,
            0.0 as count,
            0.0 as costPrice,
            0.0 as amount,
            0.0 as originPrice,
            0.0 as deductPrice,
            0 as isHisData,
            sum(if(amount>0, 1, if(amount &lt; 0, -1, 0)) * num) as prescriptionNum,
            0 as patientOrderNum
            from
            (
            select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
            if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
            v2_patient_order_id as patientorderId,
            v2_charge_form_id as chargeFormId,
            1.0 as num,
            sum(if(type=-1, -1, 1) * received_price) as amount
            from ${cisDb}.dwd_charge_transaction_record_v_partition
            where product_type not in (17, 18)
            and record_type not in (2, 3)
            and source_form_type in (4,5,6,16)
            and charge_sheet_type in (2,4,7)
            and chain_id=#{param.chainId}
            and is_deleted = 0
            and goods_fee_type in (0,1)
            and import_flag = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type in (0, 2)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId, clinicId, employeeId,employeeSnapId, patientOrderId, chargeFormId
            union all
            select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as employeeId,
            if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
            v2_patient_order_id as patientorderId,
            null as chargeFormId,
            cast(extend_prescription ->> 'eyeglassPrescriptionCount' as NUMERIC) as num,
            sum(if(type=-1, -1, 1) * received_price) as amount
            from ${cisDb}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and record_type not in (2, 3)
            and product_type not in (17, 18)
            and extend_prescription is not null
            and import_flag = 0
            and charge_sheet_type in (2,4,7)
            and source_form_type != 1
            and is_deleted = 0
            and goods_fee_type in(0,1)
            and product_compose_type in (0, 2)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId, clinicId, employeeId, employeeSnapId,patientOrderId, chargeFormId, num
            ) a
            group by chainId, clinicId, employeeId,employeeSnapId,goodsId,classifyLevel1,classifyLevel2,unit,count,costPrice,originPrice,deductPrice
        </if>
        <if test="param.isIncludeCompose == 1 and (param.acceptClassify == 1 or param.amountClassify == 1 or param.prescriptionClassify == 1)">
            union all
        </if>
        <if test="param.isIncludeCompose == 1">
            select
            if(a.chainId is null, b.chainId, a.chainId) as chainId,
            if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
            if(a.employeeId is null, b.employeeId, a.employeeId) as employeeId,
            if(a.employeeSnapId is null, b.employeeSnapId, a.employeeSnapId) as employeeSnapId,
            if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
            if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
            if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
            if(a.unit is null, b.unit, a.unit) as unit,
            if(a.unitCount is null, 0.0, a.unitCount) as count,
            cast(if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as decimal(20,8)) as costPrice,
            if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
            if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
            if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice,
            0 as isHisData,
            0 as prescriptionNum,
            0 as patientOrderNum
            from
            (
            select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
            if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
            if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
            if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
            if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
            if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
            sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount,
            sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
            sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
            sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
            from
            ${cisDb}.dwd_charge_transaction_record_v_partition
            where record_type not in (2, 3)
            and product_type not in (17, 18)
            and chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type in (1,2)
            and is_deleted = 0
            and import_flag = 0
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
            ) a
            full OUTER JOIN
            (
            select
            chain_id as chainId,
            clinic_id as clinicId,
            if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
            if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
            if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
            if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
            if (product_compose_type = 2, 0, if(classify_level_2_id is null, 0, classify_level_2_id)) as classifyLevel2,
            if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
            sum(if(product_compose_type = 2, if(type in (4,6), -cost_price, cost_price), 0.0)) as costPrice
            from ${cisDb}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and form_type = 0
            and product_compose_type in (1,2)
            <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                ${param.snapDispensingEmployeeIdsSql}
            </if>
            <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                and ${param.dispensingEmployeeIdsSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,goodsId, classifyLevel1, classifyLevel2, unit
            ) b
            on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeSnapId=b.employeeSnapId and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit
        </if>
        ) h
    </select>

    <!--费用类型提成明细数据-->
    <select id="getSaleFeeTypeCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.commission.domain.SaleDetailEntity">
        select *
        from
        (
        select
            if(a.chainId is null, b.chainId, a.chainId) as chainId,
            if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
            if(a.employeeId is null, b.employeeId, a.employeeId) as employeeId,
            if(a.employeeSnapId is null, b.employeeSnapId, a.employeeSnapId) as employeeSnapId,
            if(a.departmentId is null, b.departmentId, a.departmentId) as departmentId,
            if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
            if(a.feeTypeId is null, b.feeTypeId, a.feeTypeId) as feeTypeId,
            if(a.unit is null, b.unit, a.unit) as unit,
            if(a.unitCount is null, 0.0, a.unitCount) as count,
            cast(if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as decimal(20,8)) as costPrice,
            if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
            if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
            if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice,
            0 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                if(doctor_id is not null and doctor_id != '', department_id, seller_department_id) as departmentId,
                product_id as goodsId,
                fee_type_id as feeTypeId,
                calc_unit as unit,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(calc_count) as unitCount,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
            from ${cisDb}.dwd_charge_transaction_record_v_partition
            where product_type not in (17, 18)
            and record_type not in (2, 3)
            and is_deleted = 0
            and chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0
                and goods_fee_type in (0, 2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                <if test="param.isIncludeCompose == 1">
                    and product_compose_type = 0
                    and goods_fee_type in (0, 2)
                </if>
                <if test="param.isIncludeCompose == 0">
                    and product_compose_type in (0, 2, 3)
                    and goods_fee_type in (0, 2)
                </if>
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            group by chainId, clinicId, employeeId, employeeSnapId, departmentId, goodsId, feeTypeId, unit
        ) a
        full OUTER JOIN
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '',seller_department_id, '00000000000000000000000000000000'), if(seller_department_id is not null and seller_department_id != '', seller_department_id, if(department_id is not null and department_id != '',department_id, '00000000000000000000000000000000'))) as departmentId,
                product_id as goodsId,
                fee_type_id as feeTypeId,
                calc_unit as unit,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
            from ${cisDb}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type = 0
            and form_type = 0
            <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                ${param.snapDispensingEmployeeIdsSql}
            </if>
            <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                and ${param.dispensingEmployeeIdsSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            GROUP BY chainId, clinicId, employeeId, employeeSnapId, departmentId, goodsId, feeTypeId, unit
        ) b
        on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeSnapId=b.employeeSnapId and a.departmentId=b.departmentId and a.goodsId=b.goodsId and a.feeTypeId=b.feeTypeId and a.unit=b.unit
        <if test="param.hisType == 100">
            <if test="param.hospitalFeeCommissionTiming == null or param.hospitalFeeCommissionTiming == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                    0::BIGINT as employeeSnapId,
                    release_department_id as departmentId,
                    product_id as goodsId,
                    fee_type_id as feeTypeId,
                    product_unit as unit,
                    sum(if(charge_type=-1, -1, 1) * product_unit_count) as count,
                    sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                    sum(if(charge_type=-1, -1, 1) * total_price) as amount,
                    sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                    0.0 as deductPrice,
                    1 as isHisData
                from ${hisDb}.dwd_his_charge_settle_transaction_record
                where chain_id=#{param.chainId}
                and goods_fee_type in (0,2)
                and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                    and ${param.hisWhereSQL}
                </if>
                <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                    ${param.snapHisEmployeeIdsSql}
                </if>
                <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                    and ${param.hisEmployeeIdsSql}
                </if>
                group by chainId, clinicId, employeeId, employeeSnapId, departmentId, goodsId, feeTypeId, unit
            </if>
            <if test="param.hospitalFeeCommissionTiming != null and param.hospitalFeeCommissionTiming == 2">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, '00000000000000000000000000000000') as employeeId,
                    0::BIGINT as employeeSnapId,
                    department_id as departmentId,
                    goods_id as goodsId,
                    fee_type_id as feeTypeId,
                    unit,
                    sum(if(type = 1, -1, 1) * count) as count,
                    sum(if(type=1, -1, 1) * total_cost_price) as costPrice,
                    sum(if(type=1, -1, 1) * amount) as amount,
                    sum(if(type=1, -1, 1) * amount) as originPrice,
                    0.0 as deductPrice,
                    1 as isHisData
                from ${hisDb}.dwd_his_charge_form_item_record
                where chain_id=#{param.chainId}
                and goods_fee_type in(0,2)
                and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                    and ${param.hisWhereSQL}
                </if>
                <if test="param.employeeId != null and param.employeeId != ''">
                    and doctor_id=#{param.employeeId}
                </if>
                <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                    and ${param.hisEmployeeIdsSql}
                </if>
                group by chainId, clinicId, employeeId, employeeSnapId, departmentId, goodsId, feeTypeId, unit
            </if>
        </if>
        <if test="param.isIncludeCompose == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                if(doctor_id is not null and doctor_id != '', department_id, seller_department_id) as departmentId,
                product_id as goodsId,
                fee_type_id as feeTypeId,
                calc_unit as unit,
                sum(calc_count) as count,
                sum(if(type=-1, -1, 1) * cost_price) as costPrice,
                sum(if(type=-1,-received_price,received_price)) as amount,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1,-deduct_promotion_price,deduct_promotion_price)) as deductPrice,
                0 as isHisData
            from
            ${cisDb}.dwd_charge_transaction_record_v_partition
            where
            record_type not in (2, 3)
            and chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_type = 11
            and is_deleted = 0
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,departmentId,goodsId, feeTypeId, unit
        </if>
        ) h
        order by clinicId, employeeId, employeeSnapId, goodsId
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <!--获取费用类型翻页数据-->
    <select id="fetchCommissionSaleFeeTypeDetailTotal" resultType="long">
        select count(1) as count
        from
        (
            select
                if(a.chainId is null, b.chainId, a.chainId) as chainId,
                if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
                if(a.employeeId is null, b.employeeId, a.employeeId) as employeeId,
                if(a.employeeSnapId is null, b.employeeSnapId, a.employeeSnapId) as employeeSnapId,
                if(a.departmentId is null, b.departmentId, a.departmentId) as departmentId,
                if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
                if(a.feeTypeId is null, b.feeTypeId, a.feeTypeId) as feeTypeId,
                if(a.unit is null, b.unit, a.unit) as unit,
                if(a.unitCount is null, 0.0, a.unitCount) as count,
                cast(if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as decimal(20,8)) as costPrice,
                if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
                if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
                if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice,
                0 as isHisData
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                    if(doctor_id is not null and doctor_id != '', department_id, seller_department_id) as departmentId,
                    product_id as goodsId,
                    fee_type_id as feeTypeId,
                    calc_unit as unit,
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(calc_count) as unitCount,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
                from ${cisDb}.dwd_charge_transaction_record_v_partition
                where product_type not in (17, 18)
                and record_type not in (2, 3)
                and is_deleted = 0
                and goods_fee_type in(0, 2)
                and product_compose_type = 0
                and chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                group by chainId, clinicId, employeeId,employeeSnapId, departmentId, goodsId, feeTypeId, unit
            ) a
            full OUTER JOIN
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                    if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                    if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '',seller_department_id, '00000000000000000000000000000000'), if(seller_department_id is not null and seller_department_id != '', seller_department_id, if(department_id is not null and department_id != '',department_id, '00000000000000000000000000000000'))) as departmentId,
                    product_id as goodsId,
                    fee_type_id as feeTypeId,
                    calc_unit as unit,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                from ${cisDb}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                    ${param.snapDispensingEmployeeIdsSql}
                </if>
                <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                    and ${param.dispensingEmployeeIdsSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                GROUP BY chainId, clinicId, employeeId, employeeSnapId, departmentId, goodsId, feeTypeId, unit
            ) b
            on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeSnapId=b.employeeSnapId and a.departmentId = b.departmentId and a.goodsId=b.goodsId and a.feeTypeId=b.feeTypeId and a.unit=b.unit
            <if test="param.hisType == 100">
                <if test="param.hospitalFeeCommissionTiming == null or param.hospitalFeeCommissionTiming == 1">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                        0::BIGINT as employeeSnapId,
                        release_department_id as departmentId,
                        product_id as goodsId,
                        fee_type_id as feeTypeId,
                        product_unit as unit,
                        sum(if(charge_type=-1, -1, 1) * product_unit_count) as count,
                        sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                        sum(if(charge_type=-1, -1, 1) * total_price) as amount,
                        sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                        0.0 as deductPrice,
                        1 as isHisData
                    from ${hisDb}.dwd_his_charge_settle_transaction_record
                    where chain_id=#{param.chainId}
                    and goods_fee_type in (0,2)
                    and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                        and ${param.hisWhereSQL}
                    </if>
                    <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                        ${param.snapHisEmployeeIdsSql}
                    </if>
                    <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                        and ${param.hisEmployeeIdsSql}
                    </if>
                    group by chainId, clinicId, employeeId, employeeSnapId, departmentId, goodsId, feeTypeId, unit
                </if>
                <if test="param.hospitalFeeCommissionTiming != null and param.hospitalFeeCommissionTiming == 2">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(doctor_id is not null and doctor_id != '', doctor_id, '00000000000000000000000000000000') as employeeId,
                        0::BIGINT as employeeSnapId,
                        department_id as departmentId,
                        goods_id as goodsId,
                        fee_type_id as feeTypeId,
                        unit,
                        sum(if(type = 1, -1, 1) * count) as count,
                        sum(if(type=1, -1, 1) * total_cost_price) as costPrice,
                        sum(if(type=1, -1, 1) * amount) as amount,
                        sum(if(type=1, -1, 1) * amount) as originPrice,
                        0.0 as deductPrice,
                        1 as isHisData
                    from ${hisDb}.dwd_his_charge_form_item_record
                    where chain_id=#{param.chainId}
                    and goods_fee_type in(0,2)
                    and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                        and ${param.hisWhereSQL}
                    </if>
                    <if test="param.employeeId != null and param.employeeId != ''">
                        and doctor_id=#{param.employeeId}
                    </if>
                    <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                        and ${param.hisEmployeeIdsSql}
                    </if>
                    group by chainId, clinicId, employeeId, employeeSnapId, departmentId, goodsId, feeTypeId, unit
                </if>
            </if>
            <if test="param.isIncludeCompose == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                    if(doctor_id is not null and doctor_id != '', department_id, seller_department_id) as departmentId,
                    product_id as goodsId,
                    fee_type_id as feeTypeId,
                    calc_unit as unit,
                    sum(calc_count) as count,
                    sum(if(type=-1, -1, 1) * cost_price) as costPrice,
                    sum(if(type=-1,-received_price,received_price)) as amount,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1,-deduct_promotion_price,deduct_promotion_price)) as deductPrice,
                    0 as isHisData
                from
                ${cisDb}.dwd_charge_transaction_record_v_partition
                where
                record_type not in (2, 3)
                and chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_type = 11
                and is_deleted = 0
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                group by chainId,clinicId,employeeId,employeeSnapId, departmentId, goodsId, feeTypeId, unit
            </if>
        ) h
    </select>

    <select id="getPharmacySaleCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.commission.domain.SaleDetailEntity">
        select
            chainId,
            clinicId,
            created,
            employeeId,
            sellNo,
            product_id as goodsId,
            classify_level_1_id as classifyLevel1,
            classify_level_2_id as classifyLevel2,
            unit,
            profit,
            profit_category_type as profitCategoryType,
            if(charge_sheet_status=3 and status=2, 3, status::Integer) as status,
            if(status =2,-1,1) * unit_count as count,
            if(status =2,-1,1) * source_total_price as originPrice,
            if(status =2,-1,1) * receivable_price as amount,
            if(status =2,-1,1) * total_cost_price as costPrice,
            0 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                charge_sheet_business_time as created,
                if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000') as employeeId,
                sell_no as sellNo,
                product_id,
                classify_level_1_id,
                classify_level_2_id,
                unit,
                unit_count,
                status,
                charge_sheet_status,
                profit_category_type,
                source_total_price,
                receivable_price,
                total_cost_price,
                round(if(coalesce(receivable_price, 0.0) != 0.0, (coalesce(receivable_price, 0.0) - coalesce(total_cost_price, 0.0)) / coalesce(receivable_price, 0.0), null),4) as profit
            from ${cisDb}.dwd_charge_form_item_business
            where chain_id = #{param.chainId}
            and is_deleted = 0
            and goods_fee_type in (0, 2)
            and charge_sheet_type != 5
            and charge_sheet_business_time between to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.goodsId != null and param.goodsId != ''">
                and product_id = #{param.goodsId}
            </if>
            <if test="param.sellNo != null and param.sellNo != ''">
                and sell_no = #{param.sellNo}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${params.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
        ) h
        where 1=1
        <if test="param.whereSQL != null and param.whereSQL != ''">
            and ${param.whereSQL}
        </if>
        order by created desc, employeeId, sellNo, goodsId
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <select id="fetchPharmacySaleCommissionDetailTotal" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionTotal">
        select
            count(1) as totalCount,
            count(distinct employeeId) as totalEmployeeCount
        from
        (
            select
                chainId,
                clinicId,
                created,
                employeeId,
                sellNo,
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                classify_level_2_id as classifyLevel2,
                unit,
                profit,
                profit_category_type as profitCategoryType,
                if(charge_sheet_status=3 and status=2, 3, status::Integer) as status,
                if(status =2,-1,1) * unit_count as count,
                if(status =2,-1,1) * source_total_price as originPrice,
                if(status =2,-1,1) * receivable_price as amount,
                if(status =2,-1,1) * total_cost_price as costPrice,
                0 as isHisData
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    charge_sheet_business_time as created,
                    if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000') as employeeId,
                    sell_no as sellNo,
                    product_id,
                    classify_level_1_id,
                    classify_level_2_id,
                    unit,
                    unit_count,
                    status,
                    charge_sheet_status,
                    profit_category_type,
                    source_total_price,
                    receivable_price,
                    total_cost_price,
                    round(if(coalesce(receivable_price, 0.0) != 0.0, (coalesce(receivable_price, 0.0) - coalesce(total_cost_price, 0.0)) / coalesce(receivable_price, 0.0), null),4) as profit
                from ${cisDb}.dwd_charge_form_item_business
                where chain_id = #{param.chainId}
                and is_deleted = 0
                and goods_fee_type in (0, 2)
                and charge_sheet_type != 5
                and charge_sheet_business_time between to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds between to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.goodsId != null and param.goodsId != ''">
                    and product_id = #{param.goodsId}
                </if>
                <if test="param.sellNo != null and param.sellNo != ''">
                    and sell_no = #{param.sellNo}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${params.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
            ) h
            where 1=1
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
        ) a
    </select>

    <!--收费员明细提成-->
    <select id="getCashierCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CashierDetailEntity">
        select
            chainId,
            clinicId,
            employeeId,
            chargeSheetType,
            retailType,
            patientOrderNo,
            patientId,
            patientOrderId,
            a.created as created,
            cashierId,
            originPrice,
            receivedPrice,
            if(receivedPrice=0,0,1) as type
        from (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                charged_by as employeeId,
                charge_sheet_type as chargeSheetType,
                retail_type as retailType,
                patientorder_no as patientOrderNo,
                patient_id as patientId,
                v2_patient_order_id as patientOrderId,
                create_time as created,
                if(doctor_id is null, seller_id, doctor_id) as cashierId,
                row_number() over(partition by v2_patient_order_id order by create_time) as rank
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and chain_id = #{param.chainId}
            and is_deleted = 0
            and product_compose_type in (0,2)
            and goods_fee_type in (0,1)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.typeSql != null and param.typeSql != ''">
                and ${param.typeSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
        ) a
        inner join
        (
            select
                v2_patient_order_id,
                sum(if(type=-1,-received_price,received_price)) as receivedPrice,
                sum(if(type=-1,0.0,original_price)) as originPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and chain_id = #{param.chainId}
            and is_deleted = 0
            and product_compose_type in (0,2)
            and goods_fee_type in (0,1)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.typeSql != null and param.typeSql != ''">
                and ${param.typeSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by v2_patient_order_id
        ) b
        on a.patientOrderId = b.v2_patient_order_id
        where a.rank =1
        and b.receivedPrice >= 0
        order by created desc
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <!--获取收费员翻页数据-->
    <select id="fetchCommissionCashierDetailTotal" resultType="long">
        select
            count(1) as totalCount
        from (
        select
            v2_patient_order_id,
            sum(if(type=-1,-received_price,received_price)) as receivedPrice
        from ${env}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        and chain_id = #{param.chainId}
        and is_deleted = 0
        and product_compose_type in (0,2)
        and goods_fee_type in (0,1)
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.typeSql != null and param.typeSql != ''">
            and ${param.typeSql}
        </if>
        <if test="param.statConfigSql != null and param.statConfigSql != ''">
            and ${param.statConfigSql}
        </if>
        group by v2_patient_order_id
        ) b
        where b.receivedPrice >= 0
    </select>

    <select id="selectPersonnelBase" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelBaseDao">
        SELECT
                chain_id AS chainId,
                clinic_id AS clinicId,
                dispensing_by AS dispensedBy,
                sum( CASE WHEN unit_count>0 THEN 1/dispenser_count WHEN unit_count &lt; 0 THEN -1/dispenser_count ELSE 0 END ) AS dispensingNum
            FROM
            (
                SELECT
                    chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                FROM ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and form_type = 0
                and type in (3,4)
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id
            ) z
            INNER JOIN
            (
                select
                    v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                    sum(dispenser_count) as dispenser_count
                from
                (
                    select
                        v2_dispensing_sheet_id,
                        if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                            cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                        dispenser_count,
                        sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and form_type = 0
                    and type in (3,4)
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    group by v2_dispensing_sheet_id, oper_id, dispenser_count
            ) aa
            where count>0
            group by v2_dispensing_sheet_id
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id
        GROUP BY chain_id,clinic_id,dispensing_by;
    </select>

    <select id="selectPersonnelGoodsType" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelGoodsTypeDao">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as dispensedBy,
            sum(goods_type_num/dispenser_count) as goodsTypeNum,
            sum(western_goods_type_num/dispenser_count) as westernGoodsTypeNum,
            sum(external_goods_type_num/dispenser_count) as externalGoodsTypeNum,
            sum(lens_goods_type_num/dispenser_count) as lensGoodsTypeNum,
            sum(spectacles_frame_goods_type_num/dispenser_count) as spectaclesFrameGoodsTypeNum,
            sum(corneal_shaping_mirror_goods_type_num/dispenser_count) as cornealShapingMirrorGoodsTypeNum,
            sum(soft_hydrophilic_mirror_goods_type_num/dispenser_count) as softHydrophilicMirrorGoodsTypeNum,
            sum(rigid_oxygen_permeable_mirror_goods_type_num/dispenser_count) as rigidOxygenPermeableMirrorGoodsTypeNum,
            sum(sunglasses_goods_type_num/dispenser_count) as sunglassesGoodsTypeNum        from
            (
                SELECT chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    oper_id,
                    sum(case when unit_count>0 then 1 when unit_count &lt; 0 then -1 else 0 end) as goods_type_num,
                    sum(case when prescription_type in (4,5) and unit_count>0 then 1 when prescription_type in (4,5) and unit_count &lt; 0 then -1 else 0 end) as western_goods_type_num,
                    sum(case when prescription_type in (16) and unit_count>0 then 1 when prescription_type in (16) and unit_count &lt; 0 then -1 else 0 end) as external_goods_type_num,
                    sum(case when prescription_type=22 and classify_level_1_id = '24-1' and unit_count>0 then 1 when prescription_type=22 and classify_level_1_id = '24-1' and unit_count &lt; 0 then -1 else 0 end) as lens_goods_type_num,
                    sum(case when prescription_type=22 and classify_level_1_id = '24-2' and unit_count>0 then 1 when prescription_type=22 and classify_level_1_id = '24-2' and unit_count &lt; 0 then -1 else 0 end) as spectacles_frame_goods_type_num,
                    sum(case when prescription_type=22 and classify_level_1_id = '24-3' and unit_count>0 then 1 when prescription_type=22 and classify_level_1_id = '24-3' and unit_count &lt; 0 then -1 else 0 end) as corneal_shaping_mirror_goods_type_num,
                    sum(case when prescription_type=22 and classify_level_1_id = '24-4' and unit_count>0 then 1 when prescription_type=22 and classify_level_1_id = '24-4' and unit_count &lt; 0 then -1 else 0 end) as soft_hydrophilic_mirror_goods_type_num,
                    sum(case when prescription_type=22 and classify_level_1_id = '24-5' and unit_count>0 then 1 when prescription_type=22 and classify_level_1_id = '24-5' and unit_count &lt; 0 then -1 else 0 end) as rigid_oxygen_permeable_mirror_goods_type_num,
                    sum(case when prescription_type=22 and classify_level_1_id = '24-6' and unit_count>0 then 1 when prescription_type=22 and classify_level_1_id = '24-6' and unit_count &lt; 0 then -1 else 0 end) as sunglasses_goods_type_num
                FROM
                (
                    SELECT chain_id,
                        clinic_id,
                        dispensing_by,
                        v2_dispensing_sheet_id,
                        if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                        if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                        prescription_type,
                        product_id,
                        classify_level_1_id,
                        sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                    FROM ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and form_type = 0
                    and type in (3,4)
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id, form_id,prescription_type,product_id,classify_level_1_id
                ) aa
                group by chain_id,clinic_id, dispensing_by, v2_dispensing_sheet_id, oper_id
            ) z
            INNER JOIN
            (
                select
                    v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                    oper_id,
                    dispenser_count
                from
                (
                    select
                        v2_dispensing_sheet_id,
                        if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                        dispenser_count,
                        sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and form_type = 0
                    and type in (3,4)
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    group by v2_dispensing_sheet_id, oper_id, dispenser_count
                ) aa
                where count>0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.oper_id = x.oper_id
        GROUP BY chain_id, clinic_id, dispensing_by;
    </select>

    <select id="selectPersonnelMaterialGoodsType" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelGoodsTypeDao">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as dispensedBy,
            sum(goods_type_num/dispenser_count) as goodsTypeNum
        from
        (
            SELECT chain_id,
                clinic_id,
                dispensing_by,
                v2_dispensing_sheet_id,
                oper_id,
                sum( CASE
                WHEN unit_count>0 THEN 1
                WHEN unit_count &lt; 0 THEN -1
                ELSE 0
                END) AS goods_type_num
            FROM
            (
                SELECT chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,
                    product_id,
                    sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                FROM ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4'))
                and dispensing_form_type != 10
                and form_type = 0
                and type in (3,4)
                GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id, form_id,prescription_type,product_id
            ) aa
            group by chain_id,clinic_id, dispensing_by, v2_dispensing_sheet_id, oper_id
        ) z
        INNER JOIN
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                oper_id,
                dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4'))
                and dispensing_form_type != 10
                and form_type = 0
                and type in (3,4)
                group by v2_dispensing_sheet_id, oper_id, dispenser_count
            ) aa
            where count>0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.oper_id = x.oper_id
        GROUP BY chain_id, clinic_id, dispensing_by;
    </select>

    <select id="selectPersonnelComposeGoodsType" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelGoodsTypeDao">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as dispensedBy,
            sum(goods_type_num/dispenser_count) as goodsTypeNum
        from
        (
            SELECT chain_id,
                clinic_id,
                dispensing_by,
                v2_dispensing_sheet_id,
                oper_id,
                sum( CASE
                WHEN unit_count>0 THEN 1
                WHEN unit_count &lt; 0 THEN -1
                ELSE 0
                END) AS goods_type_num
            FROM
            (
                SELECT chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,
                    product_id,
                sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                FROM ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 2
                and form_type = 0
                and type in (3,4)
                GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id, form_id,prescription_type,product_id
            ) aa
            group by chain_id,clinic_id, dispensing_by, v2_dispensing_sheet_id, oper_id
        ) z
        INNER JOIN
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                oper_id,
                dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 2
                and form_type = 0
                and type in (3,4)
                group by v2_dispensing_sheet_id, oper_id, dispenser_count
            ) aa
            where count>0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.oper_id = x.oper_id
        GROUP BY chain_id, clinic_id, dispensing_by;
    </select>

    <select id="selectPersonnelPerscription" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelPrescriptionDao">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as dispensedBy,
            sum(western_prescription_num/dispenser_count) as westernPrescriptionNum,
            sum(slices_prescription_num/dispenser_count) as slicesPrescriptionNum,
            sum(granular_prescription_num/dispenser_count) as granularPrescriptionNum
        from
        (
            select
                chain_id ,
                clinic_id ,
                dispensing_by,
                form_id,
                sum(case
                when prescription_type in (4,5) and unit_count>0 then round(1, 4)
                when prescription_type in (4,5) and unit_count &lt; 0 then round(-1, 4)
                else 0 end
                ) as western_prescription_num,
                sum(case
                when prescription_type in (61) and unit_count>0 then round(1, 4)
                when prescription_type in (61) and unit_count &lt; 0 then round(-1, 4)
                else 0 end
                ) as slices_prescription_num,
                sum(case
                when prescription_type in (62) and unit_count>0 then round(1, 4)
                when prescription_type in (62) and unit_count &lt; 0 then round(-1, 4)
                else 0 end
                ) as granular_prescription_num
            from
            (
                select
                    chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    sum(if(type=3, 1, -1) * product_dose_count*product_unit_count) as unit_count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                and type in (3,4)
                group by chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id,form_id,prescription_type,oper_id
            ) aa
            group by chain_id,clinic_id,dispensing_by, form_id
        ) z
        INNER JOIN
        (
            select
                form_id,
                sum(dispenser_count) as dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                and type in (3,4)
                group by v2_dispensing_sheet_id, form_id, oper_id, dispenser_count
            ) aa
            where count>0
            group by form_id
        ) x
        on z.form_id = x.form_id
        GROUP BY chain_id,clinic_id,dispensing_by;
    </select>

    <select id="selectPersonnelDose" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelDoseDao">
        select
            z.chain_id as chainId,
            z.clinic_id as clinicId,
            z.dispensing_by as dispensedBy,
            z.prescription_type as prescriptionType,
            sum(dose_count * opera_count / dispenser_count) as doseCount
        from
        (
            select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count,
            count(oper_id) as opera_count
            from
            (
                select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id
                from
                (
                    select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, if(source_form_id is null, dispensing_form_id, source_form_id) as form_id, prescription_type,
                    v2_charge_form_item_id as charge_form_item_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    sum(if(type = 3, 1, -1) * product_dose_count_original)  as dose_count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    and prescription_type in (61,62)
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 0
                    and form_type = 0
                    and type in (3,4)
                    group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, charge_form_item_id, oper_id
                    having sum(if(type = 3, 1, -1) * product_dose_count_original) > 0
                ) a
                group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id
            ) b
            group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count
        ) z
        inner join
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                form_id,
                prescription_type,
                sum(dispenser_count) as dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                and type in (3,4)
                group by v2_dispensing_sheet_id, form_id, prescription_type, oper_id, dispenser_count
            ) aa
            where count>0
            group by v2_dispensing_sheet_id, form_id, prescription_type
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.form_id = x.form_id and z.prescription_type = x.prescription_type
        group by chainId,clinicId,dispensedBy,prescriptionType;
    </select>

    <select id="selectPersonnelDoseAndUnitCount" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelDoseDao">
        select
            z.chain_id as chainId,
            z.clinic_id as clinicId,
            z.dispensing_by as dispensedBy,
            z.prescription_type as prescriptionType,
            sum(dose_count * item_count / dispenser_count) as doseAndUnitCount
        from
        (
            select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id,
            count(charge_form_item_id) as item_count
            from
            (
                select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id, charge_form_item_id
                from
                (
                    select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, if(source_form_id is null, dispensing_form_id, source_form_id) as form_id, prescription_type,
                    v2_charge_form_item_id as charge_form_item_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    sum(if(type = 3, 1, -1) * product_dose_count_original)  as dose_count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 0
                    and prescription_type in (61,62)
                    and form_type = 0
                    and type in (3,4)
                    group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, charge_form_item_id, oper_id
                    having sum(if(type = 3, 1, -1) * product_dose_count_original) > 0
                ) a
                group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id, charge_form_item_id
            ) b
            group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id
        ) z
        inner join
        (
            select
                v2_dispensing_sheet_id,
                if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                prescription_type,
                if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                dispenser_count,
                sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
            from ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type = 0
            and form_type = 0
            and type in (3,4)
            group by v2_dispensing_sheet_id, form_id, prescription_type, oper_id, dispenser_count
        ) x
        on z.v2_dispensing_sheet_id=x.v2_dispensing_sheet_id and z.form_id=x.form_id and z.prescription_type=x.prescription_type and z.oper_id=x.oper_id
        group by chainId,clinicId,dispensedBy,prescriptionType;
    </select>


    <select id="selectPersonnelAmount" resultType="cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelAmountDao">
        SELECT
            chain_id AS chainId,
            clinic_id AS clinicId,
            dispensing_by AS dispensedBy,
            sum(if(type=3,1,-1) * coalesce(received_price, 0.0)) as receivedPrice,
            sum(if(type=3,1,-1) * coalesce(original_price, 0.0)) as originalPrice,
            sum(if(type=3,1,-1) * coalesce(cost_price, 0.0)) as costPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (4,5), coalesce(received_price, 0.0), 0.0)) as westernReceivedPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (4,5), coalesce(original_price, 0.0), 0.0)) as westernOriginalPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (4,5), coalesce(cost_price, 0.0), 0.0)) as westernCostPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (61), coalesce(received_price, 0.0), 0.0)) as slicesReceivedPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (61), coalesce(original_price, 0.0), 0.0)) as slicesOriginalPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (61), coalesce(cost_price, 0.0), 0.0)) as slicesCostPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (62), coalesce(received_price, 0.0), 0.0)) as granularReceivedPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (62), coalesce(original_price, 0.0), 0.0)) as granularOriginalPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (62), coalesce(cost_price, 0.0), 0.0)) as granularCostPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (16), coalesce(received_price, 0.0), 0.0)) as externalReceivedPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (16), coalesce(original_price, 0.0), 0.0)) as externalOriginalPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and prescription_type in (16), coalesce(cost_price, 0.0), 0.0)) as externalCostPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4')), coalesce(received_price, 0.0), 0.0)) as materialReceivedPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4')), coalesce(original_price, 0.0), 0.0)) as materialOriginalPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 0 and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4')), coalesce(cost_price, 0.0), 0.0)) as materialCostPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 2, coalesce(received_price, 0.0), 0.0)) as composeReceivedPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 2, coalesce(original_price, 0.0), 0.0)) as composeOriginalPrice,
            sum(if(type=3,1,-1) * if(product_compose_type = 2, coalesce(cost_price, 0.0), 0.0)) as composeCostPrice
        FROM ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{param.chainId}
        and form_type = 0
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        and type in (3,4)
        and dispensing_form_type != 10
        and log_time between to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between #{param.beginDateDs} and #{param.endDateDs}
        GROUP BY chain_id,clinic_id,dispensing_by
    </select>

    <!--充值明细提成-->
    <select id="getReChargeCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.commission.domain.ReChargeDetailEntity">
        select *
        from
        (
        <if test="param.memberSql != null">
            select
                clinic_id as clinicId,
                member_bill_seller_user_id as employeeId,
                patient_id as patientId,
                member_type_id as memberId,
                null as cardId,
                if(member_bill_action='退储蓄金', '充值退款', '充值') as action,
                case when member_bill_action = '退储蓄金' then member_bill_principal*-1 else member_bill_principal end as receivedPrice,
                member_bill_type * member_bill_principal as rechargePrincipal, -- 充值本金
                member_bill_type * member_bill_present as rechargePresent, -- 充值赠金
                member_bill_operator_id as operatorId, -- 收费员id
                member_bill_created as created -- 充值时间
            from ${env}.dwd_member_bill_detail
            where chain_id=#{param.chainId}
            and member_bill_created BETWEEN #{param.beginDate} and #{param.endDate}
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and is_deleted = 0
            and member_bill_action in ('充值','退储蓄金')
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.memberEmployeeIdsSql != null and param.memberEmployeeIdsSql != ''">
                and ${param.memberEmployeeIdsSql}
            </if>
            <if test="param.memberSql != null and param.memberSql != ''">
                and ${param.memberSql}
            </if>
        </if>
        <if test="param.memberSql != null and param.promotionCardSql != null">
            union all
        </if>
        <if test="param.promotionCardSql != null">
            select
                clinic_id as clinicId,
                seller_id as employeeId,
                patient_id as patientId,
                null as memberId,
                card_id as cardId,
                case
                    when charge_type = 4 then '充值'
                    when charge_type = 5 then '充值退费'
                end as action,
                if(charge_type = 5, -1, 1) * pay_principal as receivedPrice,
                if(charge_type = 5, -1, 1) * pay_principal as rechargePrincipal,
                if(charge_type = 5, -1, 1) * pay_present as rechargePresent,
                created_by as operatorId,
                cast(created as text)
            from ${env}.dwd_promotion_card_patient_charge_record
            where chain_id = #{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and charge_type in (4,5)
            <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                ${param.snapDispensingEmployeeIdsSql}
            </if>
            <if test="param.promotionCardEmployeeIdsSql != null and param.promotionCardEmployeeIdsSql != ''">
                and ${param.promotionCardEmployeeIdsSql}
            </if>
            <if test="param.promotionCardSql != null and param.promotionCardSql != ''">
                and ${param.promotionCardSql}
            </if>
        </if>
        ) a
        order by created desc
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <!--获取充值翻页数据-->
    <select id="fetchCommissionReChargeDetailTotal" resultType="long">
        select count(1) as totalCount from
        (
            <if test="param.memberSql != null">
                select
                    clinic_id as clinicId,
                    member_bill_seller_user_id as employeeId,
                    patient_id as patientId,
                    member_type_id as memberId,
                    null as cardId,
                    if(member_bill_action='退储蓄金', '充值退款', '充值') as action,
                    case when member_bill_action = '退储蓄金' then member_bill_principal*-1 else member_bill_principal end as receivedPrice,
                    member_bill_type * member_bill_principal as rechargePrincipal, -- 充值本金
                    member_bill_type * member_bill_present as rechargePresent, -- 充值赠金
                    member_bill_operator_id as operatorId, -- 收费员id
                    member_bill_created as created -- 充值时间
                from ${env}.dwd_member_bill_detail
                where chain_id=#{param.chainId}
                and member_bill_created BETWEEN #{param.beginDate} and #{param.endDate}
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and is_deleted = 0
                and member_bill_action in ('充值','退储蓄金')
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.memberEmployeeIdsSql != null and param.memberEmployeeIdsSql != ''">
                    and ${param.memberEmployeeIdsSql}
                </if>
                <if test="param.memberSql != null and param.memberSql != ''">
                    and ${param.memberSql}
                </if>
            </if>
            <if test="param.memberSql != null and param.promotionCardSql != null">
                union all
            </if>
            <if test="param.promotionCardSql != null">
                select
                    clinic_id as clinicId,
                    seller_id as employeeId,
                    patient_id as patientId,
                    null as memberId,
                    card_id as cardId,
                    case
                        when charge_type = 4 then '充值'
                        when charge_type = 5 then '充值退费'
                    end as action,
                    pay_principal as receivedPrice,
                    case when charge_type = 4 then pay_principal else 0 end as rechargePrincipal,
                    case when charge_type = 4 then pay_present else 0 end as rechargePresent,
                    created_by as operatorId,
                    cast(created as text)
                from ${env}.dwd_promotion_card_patient_charge_record
                where chain_id = #{param.chainId}
                and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and charge_type in (4,5)
                <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                    ${param.snapDispensingEmployeeIdsSql}
                </if>
                <if test="param.promotionCardEmployeeIdsSql != null and param.promotionCardEmployeeIdsSql != ''">
                    and ${param.promotionCardEmployeeIdsSql}
                </if>
                <if test="param.promotionCardSql != null and param.promotionCardSql != ''">
                    and ${param.promotionCardSql}
                </if>
            </if>
        ) a
    </select>

    <!--开卡明细提成-->
    <select id="getPromotionCardCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.commission.domain.PromotionCardDetailEntity">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        if(seller_id is null,'00000000000000000000000000000000',seller_id) as employeeId,
        created,
        patient_id as patientId,
        card_id as cardId,
        charge_type as chargeType,
        case when charge_type = 2 then  -pay_principal else pay_principal end as openCardPrice,
        case when charge_type = 1 then pay_principal else 0 end as payPrincipal,
        case when charge_type = 1 then pay_present else 0 end as payPresent,
        created_by as operatorId
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        and charge_type in (1,2)
        and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.whereSQL != null and param.whereSQL != ''">
            and ${param.whereSQL}
        </if>
        order by created desc
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit} offset #{param.offset}
        </if>
    </select>

    <!--获取开卡翻页数据-->
    <select id="fetchCommissionPromotionCardDetailTotal" resultType="long">
        select
        count(1) as totalCount
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        and charge_type in (1,2)
        and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.whereSQL != null and param.whereSQL != ''">
            and ${param.whereSQL}
        </if>
    </select>

    <!--检验明细提成-->
    <select id="getExaminationCommissionDetailData" resultType="cn.abc.flink.stat.service.cis.commission.domain.ExaminationDetailEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            co_clinic_id as coClinicId,
            tester_id as testerId,
            patient_id as patientId,
            test_time as reportTime,
            goods_type as goodsType,
            sub_type as subType,
            if(pharmacy_type is null,0,pharmacy_type) as pharmacyType,
            custom_type_id as customTypeId,
            goods_id as goodsId,
            goods_name as goodsName,
            count as countNum,
            count * if(pharmacy_type = 20, COALESCE(cost_unit_price,0.0), origin_unit_price) as originPrice,
            count * if(pharmacy_type = 20, COALESCE(cost_unit_price,0.0), (COALESCE(price,0.0) + COALESCE(refund_price,0.0))) as receivedPrice,
            count * if(pharmacy_type = 20, COALESCE(package_cost_price,0.0), COALESCE(cost_unit_price,0.0)) as costPrice,
            if(pharmacy_type = 20, 0.0, deduct_price) as deductPrice,
            created_by as createdBy,
            created,
            if(seller_id='' or seller_id is null,doctor_id,seller_id) as sellerId
        from ${env}.dwd_examination_sheet
        where
        status = 1
        and chain_id = #{param.chainId}
        and test_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.whereSQL != null and param.whereSQL != ''">
            and ${param.whereSQL}
        </if>
        order by reportTime desc,created_by
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <!--获取检验翻页数据-->
    <select id="fetchCommissionExaminationDetailTotal" resultType="long">
        select
        count(1) as totalCount
        from ${env}.dwd_examination_sheet
        where
        status = 1
        and chain_id = #{param.chainId}
        and test_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.whereSQL != null and param.whereSQL != ''">
            and ${param.whereSQL}
        </if>
    </select>

    <!--挂号提成-->
    <select id="getRegistrationCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
        chainId,
        clinicId,
        employeeId,
        employeeName,
        type,
        originPrice,
        originPrice-costPrice as originGrossPrice,
        receivedPrice,
        receivedPrice-costPrice as receivedGrossPrice,
        countOne
        from
        (
        select
        chain_id as chainId,
        clinic_id as clinicId,
        doctor_id as employeeId,
        doctor_name as employeeName,
        if(revisit_status=2,35,5) as type,
        sum(if(status_v2=90,0.0,original_price)) as originPrice,
        sum(received_price) as receivedPrice,
        sum(if(status_v2=90,0.0,cost_price)) as costPrice,
        cast(sum(if(status_v2=90,0,1)) as DECIMAL) as countOne
        from ${env}.dwd_registration
        where chain_id = #{param.chainId}
        and reserve_date between date(#{param.beginDate}) and date(#{param.endDate})
        and ds between #{param.beginDateDs} and #{param.endDateDs}
        and status_v2 in (20,22,30,31,40,41,90,91)
        <if test="param.typeSql != null and param.typeSql != ''">
            and ${param.typeSql}
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
            ${param.snapEmployeeIdsSql}
        </if>
        GROUP BY chainId,doctor_id,doctor_name,clinicId,revisit_status
        ) a
    </select>

    <!--收费员提成-->
    <select id="getCashierCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            charged_by as employeeId,
            cast(sum(if(charge_sheet_type in (3,6,8,12,16,18),if(received_price&lt;=0,0,1),0)) as decimal) as countOne,
            cast(sum(if((charge_sheet_type in (2,4)),if(received_price&lt;=0,0,1),0)) as decimal ) as countTwo
        from(
            select
                chain_id as chainId,
                clinic_id as clinicId,
                charged_by,
                v2_patient_order_id,
                retail_type,
                charge_sheet_type,
                sum(if(type=-1,-received_price,received_price)) as received_price
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type in (0,2)
            and goods_fee_type in (0,1)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.typeSql != null and param.typeSql != ''">
                and ${param.typeSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chain_id,clinic_id,charged_by,v2_patient_order_id,retail_type,charge_sheet_type
        ) a
        group by chainId,clinicId,employeeId
    </select>

    <!--发药员按处方数提成-->
    <select id="getDispensingPrescriptionsCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as employeeId,
            sum(western_prescription_num/dispenser_count) as countOne,
            sum(slices_prescription_num/dispenser_count) as countTwo,
            sum(granular_prescription_num/dispenser_count) as countThree
        from
            (
                select
                    chain_id ,
                    clinic_id ,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    form_id,
                sum(case
                    when prescription_type in (4,5) and unit_count>0 then round(1, 4)
                    when prescription_type in (4,5) and unit_count &lt; 0 then round(-1, 4)
                    else 0 end
                ) as western_prescription_num,
                sum(case
                    when prescription_type in (61) and unit_count>0 then round(1, 4)
                    when prescription_type in (61) and unit_count &lt; 0 then round(-1, 4)
                    else 0 end
                ) as slices_prescription_num,
                sum(case
                    when prescription_type in (62) and unit_count>0 then round(1, 4)
                    when prescription_type in (62) and unit_count &lt; 0 then round(-1, 4)
                    else 0 end
                ) as granular_prescription_num
                from
                     (
                    select
                        chain_id,
                        clinic_id,
                        dispensing_by,
                        v2_dispensing_sheet_id,
                        prescription_type,
                        if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                        if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                            cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                        sum((if(type=3, 1, -1) * product_dose_count * product_unit_count)) as  unit_count
                    from
                    ${env}.dwd_dispensing_log_v_partition
                    where
                    chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    and product_compose_type = 0
                    and form_type = 0
                    and type in (3,4)
                    group by chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id,oper_id,prescription_type,form_id
                    ) a
                    group by chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id,form_id
                ) z
        INNER JOIN
            (
                select
                    v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                    form_id,
                    sum(dispenser_count) as dispenser_count
                from
                (
                    select
                        v2_dispensing_sheet_id,
                        if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                        if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                            cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                        dispenser_count,
                        sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 0
                    and form_type = 0
                    and type in (3,4)
                    group by v2_dispensing_sheet_id, form_id, oper_id, dispenser_count
                ) aa
                where count>0
            group by v2_dispensing_sheet_id, form_id
            having sum(dispenser_count) != 0
            ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.form_id = x.form_id
        GROUP BY chain_id,clinic_id,employeeId;
    </select>

    <!--发药员按药品种数提成-->
    <select id="getDispensingDrugCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as employeeId,
            sum(western_goods_type_num/dispenser_count) as countOne
        from
        (
            SELECT
                chain_id,
                clinic_id,
                dispensing_by,
                v2_dispensing_sheet_id,
                oper_id,
                sum(case
                    when unit_count>0 then round(1, 4)
                    when unit_count &lt;0 then round(-1, 4)
                    else 0 end
                ) as western_goods_type_num
            FROM
            (
                SELECT
                    chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                        cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,
                    product_id,
                    sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                FROM ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and prescription_type in (4,5)
                and form_type = 0
                and type in (3,4)
                GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id, form_id,prescription_type,product_id
            ) aa
            group by chain_id,clinic_id, dispensing_by, v2_dispensing_sheet_id, oper_id
        ) z
        INNER JOIN
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                oper_id,
                dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                        cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and prescription_type in (4,5)
                and form_type = 0
                and type in (3,4)
                group by v2_dispensing_sheet_id, oper_id, dispenser_count
            ) aa
            where count>0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.oper_id = x.oper_id
        GROUP BY chain_id, clinic_id, dispensing_by;
    </select>

    <!--发药员按处方剂数提成-->
    <select id="getDispensingDoseCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            z.chain_id as chainId,
            z.clinic_id as clinicId,
            z.dispensing_by as employeeId,
            sum(if(z.prescription_type in (61), dose_count * opera_count / dispenser_count,0.0)) as countTwo,
            sum(if(z.prescription_type in (62), dose_count * opera_count / dispenser_count,0.0)) as countThree
        from
        (
            select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count,
            count(oper_id) as opera_count
            from
            (
                select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id
                from
                (
                    select chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, if(source_form_id is null, dispensing_form_id, source_form_id) as form_id, prescription_type,
                    v2_charge_form_item_id as charge_form_item_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                        cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    sum(if(type = 3, 1, -1) * product_dose_count_original)  as dose_count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    and prescription_type in (61,62)
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 0
                    and form_type = 0
                    and type in (3,4)
                    group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, charge_form_item_id, oper_id
                    having sum(if(type = 3, 1, -1) * product_dose_count_original) > 0
                ) a
                group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id
            ) b
            group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count
        ) z
        inner join
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                form_id,
                prescription_type,
                sum(dispenser_count) as dispenser_count
            from
                (
                    select
                        v2_dispensing_sheet_id,
                        if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                        prescription_type,
                        if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                            cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                        dispenser_count,
                        sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 0
                    and form_type = 0
                    and type in (3,4)
                    group by v2_dispensing_sheet_id, form_id, prescription_type, oper_id, dispenser_count
                ) aa
            group by v2_dispensing_sheet_id, form_id, prescription_type
            having sum(dispenser_count) != 0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.form_id = x.form_id and z.prescription_type = x.prescription_type
        group by chainId,clinicId,employeeId
    </select>

    <!--发药员按药品味数剂数提成-->
    <select id="getDispensingFlavorsCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            z.chain_id as chainId,
            z.clinic_id as clinicId,
            z.dispensing_by as employeeId,
            sum(if(z.prescription_type in (61), dose_count * item_count / dispenser_count,0.0)) as countTwo,
            sum(if(z.prescription_type in (62), dose_count * item_count / dispenser_count,0.0)) as countThree
        from
        (
            select
                chain_id,
                clinic_id,
                dispensing_by,
                v2_dispensing_sheet_id,
                form_id,
                prescription_type,
                dose_count,
                oper_id,
                count(charge_form_item_id) as item_count
            from
            (
                select
                    chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    form_id,
                    prescription_type,
                    dose_count,
                    oper_id,
                    charge_form_item_id
                from
                (
                    select
                        chain_id,
                        clinic_id,
                        dispensing_by,
                        v2_dispensing_sheet_id,
                        if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                        prescription_type,
                        v2_charge_form_item_id as charge_form_item_id,
                        if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                            cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                        sum(if(type = 3, 1, -1) * product_dose_count_original)  as dose_count
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 0
                    and prescription_type in (61,62)
                    and form_type = 0
                    and type in (3,4)
                    group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, charge_form_item_id, oper_id
                    having sum(if(type = 3, 1, -1) * product_dose_count_original) > 0
                ) a
                group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id, charge_form_item_id
            ) b
            group by chain_id, clinic_id, dispensing_by, v2_dispensing_sheet_id, form_id, prescription_type, dose_count, oper_id
        ) z
        inner join
        (
            select
                v2_dispensing_sheet_id,
                if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                prescription_type,
                if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                    cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                dispenser_count,
                sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
            from ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type = 0
            and form_type = 0
            and type in (3,4)
            group by v2_dispensing_sheet_id, form_id, prescription_type, oper_id, dispenser_count
        ) x
        on z.v2_dispensing_sheet_id=x.v2_dispensing_sheet_id and z.form_id=x.form_id and z.prescription_type=x.prescription_type and z.oper_id=x.oper_id
        group by chainId,clinicId,employeeId
    </select>

    <!--发药员按商品材料种数提成-->
    <select id="getDispensingCommodityMaterialsCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as employeeId,
            sum(goods_type_num/dispenser_count) as countOne
        from
        (
            SELECT chain_id,
            clinic_id,
            dispensing_by,
            v2_dispensing_sheet_id,
            oper_id,
            sum( CASE
            WHEN unit_count>0 THEN 1
            WHEN unit_count &lt; 0 THEN -1
            ELSE 0
            END) AS goods_type_num
            FROM
            (
                SELECT
                    chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                        cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,
                    product_id,
                    sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                FROM ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                and type in (3,4)
                and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4'))
                GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id, form_id,prescription_type,product_id
            ) aa
            group by chain_id,clinic_id, dispensing_by, v2_dispensing_sheet_id, oper_id
        ) z
        INNER JOIN
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                oper_id,
                dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                        cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and form_type = 0
                and type in (3,4)
                and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4'))
                group by v2_dispensing_sheet_id, oper_id, dispenser_count
            ) aa
            where count>0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.oper_id = x.oper_id
        GROUP BY chain_id, clinic_id, dispensing_by
    </select>

    <!--套餐内发药员按药品种数/商品材料种数提成-->
    <select id="getDispensingDrugsAndCommodityCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        dispensing_by as employeeId,
        sum(goods_type_num/dispenser_count) as countOne
        from
        (
            SELECT
                chain_id,
                clinic_id,
                dispensing_by,
                v2_dispensing_sheet_id,
                oper_id,
                sum( CASE
                WHEN unit_count >0 THEN 1
                WHEN unit_count &lt; 0 THEN -1
                ELSE 0
                END) AS goods_type_num
            FROM
                (
                    SELECT
                    chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                        cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,
                    product_id,
                    sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                    FROM ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                        ${param.snapEmployeeIdsSql}
                    </if>
                    <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                        and ${param.employeeIdsSql}
                    </if>
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and product_compose_type = 2
                    and form_type = 0
                    and type in (3,4)
                    GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id, form_id,prescription_type,product_id
                ) aa
            group by chain_id,clinic_id, dispensing_by, v2_dispensing_sheet_id, oper_id
        ) z
        INNER JOIN
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                oper_id,
                dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                       cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 2
                and form_type = 0
                and type in (3,4)
                group by v2_dispensing_sheet_id, oper_id, dispenser_count
            ) aa
            where count>0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.oper_id = x.oper_id
        GROUP BY chain_id, clinic_id, dispensing_by
    </select>

    <select id="getDispensingAmountCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            p_type as type,
            sum(receivedPrice) as receivedPrice,
            sum(receivedPrice) - sum(costPrice) as receivedGrossPrice,
            sum(originalPrice) as originPrice,
            sum(originalPrice) - sum(costPrice) as originGrossPrice,
            sum(costPrice) as costPrice
        from
        (
            SELECT
                chain_id AS chainId,
                clinic_id AS clinicId,
                dispensing_by AS employeeId,
                case when product_compose_type = 0 and prescription_type in (4,5) then 20
                    when product_compose_type = 0 and prescription_type in (61) then 21
                    when product_compose_type = 0 and prescription_type in (62) then 22
                    when product_compose_type = 0 and (dispensing_form_type = 9 or classify_level_1_id in ('7-0','7-1','7-2','7-3','7-4')) then 23
                    when product_compose_type = 2 then 24 else 0 end  as p_type,
                coalesce(received_price, 0.0) as receivedPrice,
                coalesce(original_price, 0.0) as originalPrice,
                if(type=3,1,-1) * coalesce(cost_price, 0.0) as costPrice
            FROM ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            and type in (3,4)
            and dispensing_form_type != 10
            and log_time between to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
        ) a
        GROUP BY chainId,clinicId,employeeId,p_type
    </select>

    <!--发药员按眼镜种数提成-->
    <select id="getDispensingEyeCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            dispensing_by as employeeId,
            sum(goods_type_num/dispenser_count) as countOne
        from
        (
            SELECT
                chain_id,
                clinic_id,
                dispensing_by,
                v2_dispensing_sheet_id,
                oper_id,
                sum(case
                when unit_count >0 then round(1, 4)
                when unit_count &lt;0 then round(-1, 4)
                else 0 end
                ) as goods_type_num
            FROM
            (
                SELECT
                    chain_id,
                    clinic_id,
                    dispensing_by,
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                        cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    if(source_form_id is null, dispensing_form_id, source_form_id) as form_id,
                    prescription_type,
                    product_id,
                    sum(if(TYPE=3, 1, -1) * product_unit_count * product_dose_count) AS unit_count
                FROM ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.dispensingWhere != null and param.dispensingWhere != ''">
                    and ${param.dispensingWhere}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                and type in (3,4)
                GROUP BY chain_id,clinic_id,dispensing_by,v2_dispensing_sheet_id, oper_id, form_id,prescription_type,product_id
            ) aa
            group by chain_id,clinic_id, dispensing_by, v2_dispensing_sheet_id, oper_id
        ) z
        INNER JOIN
        (
            select
                v2_dispensing_sheet_id as v2_dispensing_sheet_id,
                oper_id,
                dispenser_count
            from
            (
                select
                    v2_dispensing_sheet_id,
                    if(cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text) is null, v2_dispensing_sheet_id,
                         cast(if(associate_operation_id is not null and associate_operation_id != 0, associate_operation_id, operation_id) as text)) as oper_id,
                    dispenser_count,
                    sum(if(type=4, -1, 1) * product_dose_count * product_unit_count) as count
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                <if test="param.dispensingWhere != null and param.dispensingWhere != ''">
                    and ${param.dispensingWhere}
                </if>
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and form_type = 0
                and type in (3,4)
                group by v2_dispensing_sheet_id, oper_id, dispenser_count
            ) aa
            where count>0
        ) x
        on z.v2_dispensing_sheet_id = x.v2_dispensing_sheet_id and z.oper_id = x.oper_id
        GROUP BY chain_id, clinic_id, dispensing_by;
    </select>

    <!--会员充值提成-->
    <select id="getReChargeMemberCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select clinicId, employeeId,
        memberId as type,
        sum(receivedPrice) as amount
        from
        (
            select
            clinic_id as clinicId,
            member_bill_seller_user_id as employeeId,
            member_type_id as memberId,
            if(member_bill_action='退储蓄金', '充值退款', '充值') as action,
            case when member_bill_action = '退储蓄金' then member_bill_principal*-1 else member_bill_principal end as receivedPrice
            from ${env}.dwd_member_bill_detail
            where chain_id=#{param.chainId}
            and member_bill_created BETWEEN #{param.beginDate} and #{param.endDate}
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and is_deleted = 0
            and member_bill_action in ('充值','退储蓄金')
            <if test="param.employeeId != null and param.employeeId != ''">
                and member_bill_seller_user_id=#{param.employeeId}
            </if>
            <if test="param.memberEmployeeIdsSql != null and param.memberEmployeeIdsSql != ''">
                and ${param.memberEmployeeIdsSql}
            </if>
            <if test="param.memberWhere != null and param.memberWhere != ''">
                and ${param.memberWhere}
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
        ) a
        group by clinicId, employeeId, type
    </select>


    <!--卡项充值提成-->
    <select id="getReChargePromotionCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select clinicId, employeeId,
               cast(cardId as text) as type,
               sum(receivedPrice) as amount
        from
        (
            select
                clinic_id as clinicId,
                seller_id as employeeId,
                patient_id as patientId,
                card_id as cardId,
                case
                    when charge_type = 4 then '充值'
                    when charge_type = 5 then '充值退费'
                end as action,
                if(charge_type = 5, -1, 1) * pay_principal as receivedPrice
            from ${env}.dwd_promotion_card_patient_charge_record
            where chain_id = #{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and charge_type in (4,5)
            <if test="param.employeeId != null and param.employeeId != ''">
                and seller_id=#{param.employeeId}
            </if>
            <if test="param.promotionCardEmployeeIdsSql != null and param.promotionCardEmployeeIdsSql != ''">
                and ${param.promotionCardEmployeeIdsSql}
            </if>
            <if test="param.promotionCardWhere != null and param.promotionCardWhere != ''">
                and ${param.promotionCardWhere}
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
        ) a
        group by clinicId, employeeId, type
    </select>

    <!--开卡提成-->
    <select id="getPromotionCardCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
        chainId,
        clinicId,
        employeeId,
        type,
        sum(case when charge_type = 1 then pay_principal when charge_type =2 then -pay_principal else 0.0 end) as amount
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                seller_id as employeeId,
                card_id as type,
                charge_type,
                pay_principal
            from ${env}.dwd_promotion_card_patient_charge_record
            where
            chain_id = #{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and charge_type in (1,2)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
        ) a
        group by employeeId,type,clinicId,chainId
    </select>

    <!--销售金额提成-->
    <select id="getSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            if(a.chainId is null,c.chainId,a.chainId) as chainId,
            if(a.clinicId is null,c.clinicId,a.clinicId) as clinicId,
            if(a.employeeId is null,c.employeeId,a.employeeId) as employeeId,
            if(a.employeeSnapId is null,c.employeeSnapId,a.employeeSnapId) as employeeSnapId,
            if(a.classifyType is null,c.classifyType,a.classifyType) as type,
            <if test="type=='product_id'">
                countThree,
            </if>
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice
        from (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                ${type} as classifyType,
                <if test="type=='product_id'">
                    sum(calc_count) as countThree,
                </if>
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1,1,-1) * if(deduct_promotion_price is null,0.0, deduct_promotion_price)) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_type not in (17, 18)
            and record_type not in (2, 3)
            and is_deleted = 0
            and import_flag = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0
                and goods_fee_type in (0, 1)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                <if test="param.isIncludeCompose == 1">
                    and product_compose_type = 0
                    and goods_fee_type in (0, 1)
                </if>
                <if test="param.isIncludeCompose == 0">
                    and product_compose_type in (0, 2)
                    and goods_fee_type in (0, 1)
                </if>
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,classifyType
            <if test="param.isIncludeCompose == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                    <if test="type=='product_id'">
                        if (product_compose_type = 2, compose_parent_product_id, product_id) as classifyType,
                        sum(if(product_compose_type = 2,0.0,calc_count)) as countThree,
                    </if>
                    <if test="type!='product_id'">
                        '11-1' as classifyType,
                    </if>
                    sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                    sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                    sum(if(product_compose_type != 1, if(type=-1,1,-1) * if(deduct_promotion_price is null, 0.0, deduct_promotion_price), 0.0)) as deductPrice
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and is_deleted = 0
                and import_flag = 0
                and product_compose_type in (1,2)
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                group by chainId,clinicId,employeeId,employeeSnapId, classifyType
            </if>
        ) a
        full OUTER JOIN
        (
            select chainId, clinicId, employeeId, employeeSnapId, classifyType, sum(costPrice) as costPrice
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'))) as employeeId,
                    if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                    ${type} as classifyType,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and form_type = 0
                and product_compose_type = 0
                <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                    ${param.snapDispensingEmployeeIdsSql}
                </if>
                <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                    and ${param.dispensingEmployeeIdsSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                GROUP BY chainId, clinicId, employeeId, employeeSnapId, classifyType
                <if test="(param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1) or param.isIncludeCompose == 1">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'))) as employeeId,
                        if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT)) as employeeSnapId,
                        <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                            ${type} as classifyType,
                        </if>
                        <if test="param.isIncludeCompose == 1">
                            <if test="type=='product_id'">
                                compose_parent_product_id AS classifyType,
                            </if>
                            <if test="type!='product_id'">
                                '11-1' as classifyType,
                            </if>
                        </if>
                        sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and form_type = 0
                    and product_compose_type = 2
                    <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                        ${param.snapDispensingEmployeeIdsSql}
                    </if>
                    <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                        and ${param.dispensingEmployeeIdsSql}
                    </if>
                    <if test="param.whereSQL != null and param.whereSQL != ''">
                        and ${param.whereSQL}
                    </if>
                    group by chainId,clinicId,employeeId,employeeSnapId,classifyType
                </if>
            ) as d
            group by chainId,clinicId,employeeId,employeeSnapId,classifyType
        ) c
        on a.chainId=c.chainId and c.clinicId=a.clinicId and a.employeeId=c.employeeId and a.employeeSnapId=c.employeeSnapId and a.classifyType= c.classifyType
    </select>

    <!--获取历史提成人员汇总数据-->
    <select id="getHistoryCommissionEmployeeSummary" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionEmployeeSummary">
        SELECT
            chain_id AS chainId,
            clinic_id AS clinicId,
            employee_id AS employeeId,
            employee_name AS employeeName,
            sum(commission_amount) AS commissionAmount,
            sum(order_count) AS orderCount,
            SUM(goods_count) AS goodsCount,
            SUM(origin_amount) AS originAmount,
            SUM(received_amount) AS receivedAmount,
            SUM(cost_price) AS cost
        FROM ${env}.dws_commission_data
        WHERE chain_id = #{param.chainId}
        AND commission_id = #{param.commissionId}
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
        and create_date BETWEEN date(#{param.beginDate}) and date(#{param.endDate})
        <if test="param.employeeList != null and param.employeeList.size > 0">
            and employee_id in
            <foreach collection="param.employeeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
            and ${param.employeeIdsSql}
        </if>
        GROUP BY chain_id, clinic_id,employee_id,employee_name
    </select>

    <!--销售提成处方数-->
    <select id="getPrescriptionCountSaleData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            employeeSnapId,
            sum(if(amount>0, 1, if(amount &lt; 0, -1, 0)) * num) as countOne
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                v2_patient_order_id as patientorderId,
                v2_charge_form_id as chargeFormId,
                1.0 as num,
                sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and record_type not in (2, 3)
            and charge_sheet_type in (2,4,7)
            and source_form_type in(4,5,6,16)
            and product_type not in (17, 18)
            and goods_fee_type in (0,1)
            and product_compose_type in (0, 2)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId, clinicId, employeeId, employeeSnapId, patientOrderId, chargeFormId
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                v2_patient_order_id as patientorderId,
                null as chargeFormId,
                cast(extend_prescription ->> 'eyeglassPrescriptionCount' as NUMERIC) as num,
                sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and record_type not in (2, 3)
            and product_type not in (17, 18)
            and extend_prescription is not null
            and import_flag = 0
            and charge_sheet_type in (2,4,7)
            and source_form_type != 1
            and goods_fee_type in (0,1)
            and product_compose_type in (0, 2)
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId, clinicId, employeeId,employeeSnapId, patientOrderId, chargeFormId, num
        ) a
        group by chainId,clinicId,employeeId,employeeSnapId
    </select>

    <!--销售提成接诊数-->
    <select id="getPatientOrderCountSaleData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            employeeSnapId,
            sum(if(amount>0, 1, if(amount &lt; 0, -1, 0))) as countTwo
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null, 0::BIGINT, doctor_snap_id),0::BIGINT) as employeeSnapId,
                v2_patient_order_id as v2_patient_order_id,
                sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and record_type not in (2, 3)
            and product_type not in (17,18)
            and charge_sheet_type in (2,4,7)
            and source_form_type != 1
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,v2_patient_order_id
        ) a
        group by chainId,clinicId,employeeId,employeeSnapId
    </select>

    <!--例外goods的销售金额提成-->
    <select id="getExceptGoodsIdSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            if(a.chainId is null,c.chainId,a.chainId) as chainId,
            if(a.clinicId is null,c.clinicId,a.clinicId) as clinicId,
            if(a.employeeId is null,c.employeeId,a.employeeId) as employeeId,
            if(a.employeeSnapId is null,c.employeeSnapId,a.employeeSnapId) as employeeSnapId,
            if(a.productId is null,c.productId,a.productId) as type,
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null,0::BIGINT,doctor_snap_id), 0::BIGINT) as employeeSnapId,
                product_id as productId,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1,1,-1) * if(deduct_promotion_price is null,0.0,deduct_promotion_price)) as deductPrice
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_type not in (17, 18)
            and record_type not in (2, 3)
            and is_deleted = 0
            and import_flag = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0
                and goods_fee_type in (0, 1)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                <if test="param.isIncludeCompose == 1">
                    and product_compose_type = 0
                    and goods_fee_type in (0, 1)
                </if>
                <if test="param.isIncludeCompose == 0">
                    and product_compose_type in (0, 2)
                    and goods_fee_type in (0, 1)
                </if>
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId,clinicId,employeeId,employeeSnapId,productId
            <if test="param.isComposeShareEqually == 0 or param.isIncludeCompose == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null,0::BIGINT,doctor_snap_id), 0::BIGINT) as employeeSnapId,
                    compose_parent_product_id as productId,
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1,1,-1) * if(deduct_promotion_price is null,0.0,deduct_promotion_price)) as deductPrice
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and product_compose_type = 2
                and is_deleted = 0
                and import_flag = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.exceptComposeWhereSQL != null and param.exceptComposeWhereSQL != ''">
                    and ${param.exceptComposeWhereSQL}
                </if>
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                group by chainId,clinicId,employeeId,employeeSnapId,productId
            </if>
        ) a
        full OUTER JOIN
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null,0::BIGINT,doctor_snap_id), 0::BIGINT)) as employeeSnapId,
                product_id as productId,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
            from ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_compose_type = 0
            and form_type = 0
            <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                ${param.snapDispensingEmployeeIdsSql}
            </if>
            <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                and ${param.dispensingEmployeeIdsSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            GROUP BY chainId, clinicId, employeeId, employeeSnapId, productId
            <if test="(param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1) or param.isIncludeCompose == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                    if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '', if(doctor_snap_id is null,0::BIGINT,doctor_snap_id), 0::BIGINT)) as employeeSnapId,
                    <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                        product_id as productId,
                    </if>
                    <if test="param.isIncludeCompose == 1">
                        compose_parent_product_id as productId,
                    </if>
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and form_type = 0
                and product_compose_type = 2
                <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                    ${param.snapDispensingEmployeeIdsSql}
                </if>
                <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                    and ${param.dispensingEmployeeIdsSql}
                </if>
                <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                    <if test="param.whereSQL != null and param.whereSQL != ''">
                        and ${param.whereSQL}
                    </if>
                </if>
                <if test="param.isIncludeCompose == 1">
                    <if test="param.exceptComposeWhereSQL != null and param.exceptComposeWhereSQL != ''">
                        and ${param.exceptComposeWhereSQL}
                    </if>
                </if>
                group by chainId,clinicId,employeeId,employeeSnapId,productId
            </if>
        ) c
        on a.chainId=c.chainId and c.clinicId=a.clinicId and a.employeeId=c.employeeId and a.employeeSnapId=c.employeeSnapId and a.productId= c.productId
    </select>

    <!--销售金额提成(按费用类型-门诊)-->
    <select id="getSaleFeeTypeCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            if(a.chainId is null,c.chainId,a.chainId) as chainId,
            if(a.clinicId is null,c.clinicId,a.clinicId) as clinicId,
            if(a.employeeId is null,c.employeeId,a.employeeId) as employeeId,
            if(a.departmentId is null,c.departmentId,a.departmentId) as departmentId,
            if(a.classifyType is null,c.classifyType,a.classifyType) as type,
            <if test="type=='product_id'">
                countThree,
            </if>
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                ${type} as classifyType,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', department_id, if(seller_department_id is not null and seller_department_id != '', seller_department_id, '00000000000000000000000000000000')) as departmentId,
                <if test="type=='product_id'">
                    sum(calc_count) as countThree,
                </if>
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1,1,-1) * if(deduct_promotion_price is null,0.0, deduct_promotion_price)) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_type not in (17, 18)
            and record_type not in (2, 3)
            and is_deleted = 0
            and import_flag = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0
                and goods_fee_type in (0, 2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                <if test="param.isIncludeCompose == 1">
                    and product_compose_type = 0
                    and goods_fee_type in (0, 2)
                </if>
                <if test="param.isIncludeCompose == 0">
                    and product_compose_type in (0, 2, 3)
                    and goods_fee_type in (0, 2)
                </if>
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId,clinicId,employeeId,departmentId,classifyType
            <if test="param.isIncludeCompose == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    '8' as classifyType,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    if(doctor_id is not null and doctor_id != '', department_id, if(seller_department_id is not null and seller_department_id != '', seller_department_id, '00000000000000000000000000000000')) as departmentId,
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1,1,-1) * if(deduct_promotion_price is null,0.0,deduct_promotion_price)) as deductPrice
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and product_compose_type = 2
                and is_deleted = 0
                and import_flag = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                group by chainId,clinicId,employeeId,departmentId,classifyType
            </if>
        ) a
        full OUTER JOIN
        (
            select
                chainId, clinicId, employeeId, departmentId, classifyType, sum(costPrice) as costPrice
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                    if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '',seller_department_id, '00000000000000000000000000000000'), if(seller_department_id is not null and seller_department_id != '', seller_department_id, if(department_id is not null and department_id != '',department_id, '00000000000000000000000000000000'))) as departmentId,
                    ${type} as classifyType,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and form_type = 0
                and product_compose_type = 0
                <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                    ${param.snapDispensingEmployeeIdsSql}
                </if>
                <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                    and ${param.dispensingEmployeeIdsSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                GROUP BY chainId, clinicId, employeeId,departmentId, classifyType
                <if test="(param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1) or param.isIncludeCompose == 1">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                        if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '',seller_department_id, '00000000000000000000000000000000'), if(seller_department_id is not null and seller_department_id != '', seller_department_id, if(department_id is not null and department_id != '',department_id, '00000000000000000000000000000000'))) as departmentId,
                        <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                            ${type} as classifyType,
                        </if>
                        <if test="param.isIncludeCompose == 1">
                            '8' as classifyType,
                        </if>
                        sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and form_type = 0
                    and product_compose_type = 2
                    <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                        ${param.snapDispensingEmployeeIdsSql}
                    </if>
                    <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                        and ${param.dispensingEmployeeIdsSql}
                    </if>
                    <if test="param.whereSQL != null and param.whereSQL != ''">
                        and ${param.whereSQL}
                    </if>
                    group by chainId,clinicId,employeeId,departmentId,classifyType
                </if>
            ) b
            group by chainId,clinicId,employeeId,departmentId,classifyType
        ) c
        on a.chainId=c.chainId and c.clinicId=a.clinicId and a.employeeId=c.employeeId and a.departmentId = c.departmentId and a.classifyType= c.classifyType
    </select>

    <!--例外费用项的销售金额提成(按费用类型)-->
    <select id="getExceptFeeGoodsIdSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            if(a.chainId is null,c.chainId,a.chainId) as chainId,
            if(a.clinicId is null,c.clinicId,a.clinicId) as clinicId,
            if(a.employeeId is null,c.employeeId,a.employeeId) as employeeId,
            if(a.departmentId is null,c.departmentId,a.departmentId) as departmentId,
            if(a.productId is null,c.productId,a.productId) as type,
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - (if(a.costPrice is null,0.0,a.costPrice) + if(c.costPrice is null,0.0,c.costPrice)) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', department_id, if(seller_department_id is not null and seller_department_id != '', seller_department_id, '00000000000000000000000000000000')) as departmentId,
                product_id as productId,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1,1,-1) * if(deduct_promotion_price is null,0.0,deduct_promotion_price)) as deductPrice
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and product_type not in (17, 18)
            and record_type not in (2, 3)
            and is_deleted = 0
            and import_flag = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0
                and goods_fee_type in (0, 2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                <if test="param.isIncludeCompose == 1">
                    and product_compose_type = 0
                    and goods_fee_type in (0, 2)
                </if>
                <if test="param.isIncludeCompose == 0">
                    and product_compose_type in (0, 2, 3)
                    and goods_fee_type in (0, 2)
                </if>
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            <if test="param.statConfigSql != null and param.statConfigSql != ''">
                and ${param.statConfigSql}
            </if>
            group by chainId,clinicId,employeeId,productId
            <if test="param.isComposeShareEqually == 0 or param.isIncludeCompose == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                    compose_parent_product_id as productId,
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price is not null and record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1,1,-1) * if(deduct_promotion_price is null,0.0,deduct_promotion_price)) as deductPrice
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and product_compose_type in (2,3)
                and goods_fee_type in (0,2)
                and is_deleted = 0
                and import_flag = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.exceptComposeWhereSQL != null and param.exceptComposeWhereSQL != ''">
                    and ${param.exceptComposeWhereSQL}
                </if>
                <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                    ${param.snapEmployeeIdsSql}
                </if>
                <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                    and ${param.employeeIdsSql}
                </if>
                <if test="param.statConfigSql != null and param.statConfigSql != ''">
                    and ${param.statConfigSql}
                </if>
                group by chainId,clinicId,employeeId,departmentId,productId
            </if>
        ) a
        full OUTER JOIN
        (
            select
                chainId, clinicId, employeeId, departmentId, productId, sum(costPrice) as costPrice
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                    if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '',seller_department_id, '00000000000000000000000000000000'), if(seller_department_id is not null and seller_department_id != '', seller_department_id, if(department_id is not null and department_id != '',department_id, '00000000000000000000000000000000'))) as departmentId,
                    product_id as productId,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_compose_type = 0
                and form_type = 0
                <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                    ${param.snapDispensingEmployeeIdsSql}
                </if>
                <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                    and ${param.dispensingEmployeeIdsSql}
                </if>
                <if test="param.whereSQL != null and param.whereSQL != ''">
                    and ${param.whereSQL}
                </if>
                GROUP BY chainId, clinicId, employeeId,departmentId,productId
                <if test="(param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1) or param.isIncludeCompose == 1">
                    union all
                    select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                    if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '',seller_department_id, '00000000000000000000000000000000'), if(seller_department_id is not null and seller_department_id != '', seller_department_id, if(department_id is not null and department_id != '',department_id, '00000000000000000000000000000000'))) as departmentId,
                    <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                        product_id as productId,
                    </if>
                    <if test="param.isIncludeCompose == 1">
                        compose_parent_product_id as productId,
                    </if>
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and form_type = 0
                    and product_compose_type = 2
                    <if test="param.snapDispensingEmployeeIdsSql != null and param.snapDispensingEmployeeIdsSql != ''">
                        ${param.snapDispensingEmployeeIdsSql}
                    </if>
                    <if test="param.dispensingEmployeeIdsSql != null and param.dispensingEmployeeIdsSql != ''">
                        and ${param.dispensingEmployeeIdsSql}
                    </if>
                    <if test="param.isIncludeCompose == 0 and param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                        <if test="param.whereSQL != null and param.whereSQL != ''">
                            and ${param.whereSQL}
                        </if>
                    </if>
                    <if test="param.isIncludeCompose == 1">
                        <if test="param.exceptComposeWhereSQL != null and param.exceptComposeWhereSQL != ''">
                            and ${param.exceptComposeWhereSQL}
                        </if>
                    </if>
                    group by chainId,clinicId,employeeId,departmentId,productId
                </if>
            ) b
            group by chainId,clinicId,employeeId,departmentId,productId
        ) c
        on a.chainId=c.chainId and c.clinicId=a.clinicId and a.employeeId=c.employeeId and a.departmentId = c.departmentId and a.productId= c.productId
    </select>

    <!--例外费用项的销售金额提成(按医嘱-住院-结算)-->
    <select id="getHisSettleExceptGoodsIdSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            productId as type,
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - if(costPrice is null,0.0,costPrice) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice,
            1 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                if(goods_fee_type=0, product_id, advice_goods_id) as productId,
                sum(if(charge_type=-1, -1, 1) * total_price) as receivedPrice,
                sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                0.0 as deductPrice
            from
            ${env}.dwd_his_charge_settle_transaction_record
            where chain_id=#{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and goods_fee_type in (0,2)
            <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                and ${param.hisWhereSQL}
            </if>
            <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                ${param.snapHisEmployeeIdsSql}
            </if>
            <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                and ${param.hisEmployeeIdsSql}
            </if>
            group by chainId,clinicId,employeeId,productId
        ) a
    </select>

    <!--例外费用项的销售金额提成(按医嘱-住院-计提)-->
    <select id="getHisChargeExceptGoodsIdSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            productId as type,
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - if(costPrice is null,0.0,costPrice) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice,
            1 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, '00000000000000000000000000000000') as employeeId,
                if(goods_fee_type=0, goods_id, advice_goods_id) as productId,
                sum(if(type=1, -1, 1) * amount) as receivedPrice,
                sum(if(type=1, -1, 1) * total_cost_price) as costPrice,
                sum(if(type =1, -1, 1) * amount) as originPrice,
                0.0 as deductPrice
            from ${env}.dwd_his_charge_form_item_record
            where chain_id=#{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and goods_fee_type in (0,1)
            <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                and ${param.hisWhereSQL}
            </if>
            <if test="param.employeeId != null and param.employeeId != ''">
                and doctor_id=#{param.employeeId}
            </if>
            <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                and ${param.hisEmployeeIdsSql}
            </if>
            group by chainId,clinicId,employeeId,productId
        ) a
    </select>

    <!--例外费用项的销售金额提成(按费用类型-住院-结算)-->
    <select id="getHisSettleFeeExceptGoodsIdSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            departmentId,
            productId as type,
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - if(costPrice is null,0.0,costPrice) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice,
            1 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                if(release_department_id is not null and release_department_id != '', release_department_id, '00000000000000000000000000000000') as departmentId,
                product_id as productId,
                sum(if(charge_type=-1, -1, 1) * total_price) as receivedPrice,
                sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                0.0 as deductPrice
            from
            ${env}.dwd_his_charge_settle_transaction_record
            where chain_id=#{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and goods_fee_type in (0,2)
            <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                and ${param.hisWhereSQL}
            </if>
            <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                ${param.snapHisEmployeeIdsSql}
            </if>
            <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                and ${param.hisEmployeeIdsSql}
            </if>
            group by chainId,clinicId,employeeId,departmentId,productId
        ) a
    </select>

    <!--例外费用项的销售金额提成(按费用类型-住院-计提)-->
    <select id="getHisChargeFeeExceptGoodsIdSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            departmentId,
            productId as type,
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - if(costPrice is null,0.0,costPrice) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice,
            1 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, '00000000000000000000000000000000') as employeeId,
                if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as departmentId,
                goods_id as productId,
                sum(if(type=1, -1, 1) * amount) as receivedPrice,
                sum(if(type=1, -1, 1) * total_cost_price) as costPrice,
                sum(if(type =1, -1, 1) * amount) as originPrice,
                0.0 as deductPrice
            from ${env}.dwd_his_charge_form_item_record
            where chain_id=#{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and goods_fee_type in (0,2)
            <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                and ${param.hisWhereSQL}
            </if>
            <if test="param.employeeId != null and param.employeeId != ''">
                and doctor_id=#{param.employeeId}
            </if>
            <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                and ${param.hisEmployeeIdsSql}
            </if>
            group by chainId,clinicId,employeeId,departmentId,productId
        ) a
    </select>

    <!--销售(结算住院数据)提成-->
    <select id="getHisSettleSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            classifyType as type,
            <if test="type=='product_id'">
                countThree,
            </if>
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - if(costPrice is null,0.0,costPrice) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice,
            1 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                ${type} as classifyType,
                if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                <if test="type=='product_id'">
                    sum(if(charge_type=-1, -1, 1) * product_unit_count) as countThree,
                </if>
                sum(if(charge_type=-1, -1, 1) * total_price) as receivedPrice,
                sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                0.0 as deductPrice
            from ${env}.dwd_his_charge_settle_transaction_record
            where chain_id=#{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and goods_fee_type in (0,2)
            <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                and ${param.hisWhereSQL}
            </if>
            <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                ${param.snapHisEmployeeIdsSql}
            </if>
            <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                and ${param.hisEmployeeIdsSql}
            </if>
            group by chainId,clinicId,employeeId,classifyType
        ) a
    </select>

    <!--销售(结算住院数据)提成-->
    <select id="getHisSettleFeeTypeSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            employeeId,
            departmentId,
            classifyType as type,
            <if test="type=='product_id'">
                countThree,
            </if>
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - if(costPrice is null,0.0,costPrice) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice,
            1 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                ${type} as classifyType,
                if(release_created_by is not null and release_created_by != '', release_created_by, '00000000000000000000000000000000') as employeeId,
                if(release_department_id is not null and release_department_id != '', release_department_id, '00000000000000000000000000000000') as departmentId,
                <if test="type=='product_id'">
                    sum(if(charge_type=-1, -1, 1) * product_unit_count) as countThree,
                </if>
                sum(if(charge_type=-1, -1, 1) * total_price) as receivedPrice,
                sum(if(charge_type=-1, -1, 1) * total_cost_price) as costPrice,
                sum(if(charge_type =-1, -1, 1) * total_price) as originPrice,
                0.0 as deductPrice
            from ${env}.dwd_his_charge_settle_transaction_record
            where chain_id=#{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and goods_fee_type in (0,2)
            <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                and ${param.hisWhereSQL}
            </if>
            <if test="param.snapHisEmployeeIdsSql != null and param.snapHisEmployeeIdsSql != ''">
                ${param.snapHisEmployeeIdsSql}
            </if>
            <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                and ${param.hisEmployeeIdsSql}
            </if>
            group by chainId,clinicId,employeeId,departmentId,classifyType
        ) a
    </select>

    <!--销售(计提住院数据)提成-->
    <select id="getHisChargeSaleCommissionData" resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
            chainId,
            clinicId,
            classifyType as type,
            employeeId,
            <if test="type=='product_id'">
                countThree,
            </if>
            receivedPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) as receivedGrossPrice,
            originPrice,
            if(originPrice is null,0.0,originPrice) - if(costPrice is null,0.0,costPrice) as originGrossPrice,
            if(receivedPrice is null,0.0,receivedPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductPrice,
            if(receivedPrice is null,0.0,receivedPrice) - if(costPrice is null,0.0,costPrice) + if(deductPrice is null,0.0,deductPrice) as receivedDeductGrossPrice,
            1 as isHisData
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                ${type} as classifyType,
                if(doctor_id is not null and doctor_id != '', doctor_id, '00000000000000000000000000000000') as employeeId,
                <if test="type=='product_id'">
                    sum(if(type = 1, -1, 1) * count) as countThree,
                </if>
                sum(if(type=1, -1, 1) * amount) as receivedPrice,
                sum(if(type=1, -1, 1) * total_cost_price) as costPrice,
                sum(if(type =1, -1, 1) * amount) as originPrice,
                0.0 as deductPrice
            from ${env}.dwd_his_charge_form_item_record
            where chain_id=#{param.chainId}
            and created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and goods_fee_type in (0,2)
            <if test="param.hisWhereSQL != null and param.hisWhereSQL != ''">
                and ${param.hisWhereSQL}
            </if>
            <if test="param.employeeId != null and param.employeeId != ''">
                and doctor_id=#{param.employeeId}
            </if>
            <if test="param.hisEmployeeIdsSql != null and param.hisEmployeeIdsSql != ''">
                and ${param.hisEmployeeIdsSql}
            </if>
            group by chainId,clinicId,employeeId,classifyType
        ) a
    </select>

    <!--检验提成-->
    <select id="getExaminationCommissionData"  resultType="cn.abc.flink.stat.service.cis.commission.domain.CommissionCountAndAmountEntity">
        select
        chainId,
        clinicId,
        employeeId,
        classifyType as type,
        countOne,
        receivedPrice,
        receivedPrice - costPrice as receivedGrossPrice,
        originPrice,
        originPrice - costPrice as originGrossPrice,
        receivedPrice + abs(deductPrice) as receivedDeductPrice,
        receivedPrice - costPrice + abs(deductPrice) as receivedDeductGrossPrice
        from (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                tester_id as employeeId,
                ${type} as classifyType,
                sum(count) as countOne,
                sum(count * if(pharmacy_type = 20, COALESCE(cost_unit_price,0.0), origin_unit_price)) as originPrice,
                sum(count * if(pharmacy_type = 20, COALESCE(package_cost_price,0.0), COALESCE(cost_unit_price,0.0))) as costPrice,
                sum(count * if(pharmacy_type = 20, COALESCE(cost_unit_price,0.0), (COALESCE(price,0.0) + COALESCE(refund_price,0.0)))) as receivedPrice,
                sum(count * if(pharmacy_type = 20, 0.0, deduct_price)) as deductPrice
            from ${env}.dwd_examination_sheet
            where
            status = 1
            and chain_id = #{param.chainId}
            and test_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.snapEmployeeIdsSql != null and param.snapEmployeeIdsSql != ''">
                ${param.snapEmployeeIdsSql}
            </if>
            <if test="param.whereSQL != null and param.whereSQL != ''">
                and ${param.whereSQL}
            </if>
            <if test="param.employeeIdsSql != null and param.employeeIdsSql != ''">
                and ${param.employeeIdsSql}
            </if>
            group by chainId, clinicId,employeeId, classifyType
        ) e
    </select>
</mapper>