<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresGoodsInventoryMapper">

    <!--  药品页签数据   -->
    <select id="selectGoods" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            goods_id        as goodsId,

            <if test="param.hisType != null and param.hisType == 10">
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_package_count *  inventory.piece_num + action_piece_count ,0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_package_count *  inventory.piece_num + action_piece_count ,0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>

            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_count, 0.0)) AS inReceiveCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inReceiveConvertPieceCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price, 0.0)) AS inReceiveAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price_exclude_tax, 0.0)) AS inReceiveAmountExcludingTax,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost, 0.0)) AS inReceiveCostAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost_exclude_tax, 0.0)) AS inReceiveCostAmountExcludingTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotInCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price, 0.0)) AS allotInPrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInPriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotInCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInCostExcludeTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotInInsideCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInInsideConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price, 0.0)) AS allotInInsidePrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInInsidePriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotInInsideCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInInsideCostExcludeTax,

            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS checkInCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkInConvertPieceCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS checkInPrice,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS checkInPriceExcludeTax,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS checkInCost,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS checkInCostExcludeTax,

            SUM(IF(action IN('药品规格修改'), action_count, 0.0)) AS inSpecificationModificationCount,
            SUM(IF(action IN('药品规格修改'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inSpecificationModificationConvertPieceCount,
            SUM(IF(action IN('药品规格修改'), action_total_price, 0.0)) AS inSpecificationModificationPrice,
            SUM(IF(action IN('药品规格修改'), action_total_price_exclude_tax, 0.0)) AS inSpecificationModificationPriceExcludeTax,
            SUM(IF(action IN('药品规格修改'), action_total_cost, 0.0)) AS inSpecificationModificationCost,
            SUM(IF(action IN('药品规格修改'), action_total_cost_exclude_tax, 0.0)) AS inSpecificationModificationCostExcludeTax,

            <if test="param.hisType != null and param.hisType == 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>

            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_count, 0.0)) AS inInitReturnCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitReturnConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_price, 0.0)) AS inInitReturnPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_price_exclude_tax, 0.0)) AS inInitReturnPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost, 0.0)) AS inInitReturnCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost_exclude_tax, 0.0)) AS inInitReturnCostExcludeTax,

            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_count, 0.0)) AS returnGoodsOutCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS returnGoodsOutConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_price, 0.0)) AS returnGoodsOutPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_price_exclude_tax, 0.0)) AS returnGoodsOutPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost, 0.0)) AS returnGoodsOutCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS returnGoodsOutCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_count, 0.0)) AS outPatientDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outPatientDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS outPatientDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS outPatientDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost, 0.0)) AS outPatientDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost_exclude_tax, 0.0)) AS outPatientDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_count,0.0)) AS hospitalPharmacyDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalPharmacyDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalPharmacyDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalPharmacyDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost,0.0)) AS hospitalPharmacyDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalPharmacyDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_count,0.0)) AS hospitalAutomaticDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalAutomaticDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalAutomaticDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalAutomaticDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost,0.0)) AS hospitalAutomaticDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalAutomaticDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_count,0.0)) AS hospitalNoSettleDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalNoSettleDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalNoSettleDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalNoSettleDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost,0.0)) AS hospitalNoSettleDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost_exclude_tax,0.0)) AS hospitalNoSettleDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_count, 0.0)) AS dispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS dispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS dispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS dispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost, 0.0)) AS dispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost_exclude_tax, 0.0)) AS dispenseCostExcludeTax,

            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_count, 0.0)) AS collectOutCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS collectOutConvertPieceCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_price, 0.0)) AS collectOutPrice,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_price_exclude_tax, 0.0)) AS collectOutPriceExcludeTax,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost, 0.0)) AS collectOutCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost_exclude_tax, 0.0)) AS collectOutCostExcludeTax,

            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_count, 0.0)) AS outDepartmentConsumptionCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outDepartmentConsumptionConvertPieceCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_price, 0.0)) AS outDepartmentConsumptionAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_price_exclude_tax, 0.0)) AS outDepartmentConsumptionAmountExcludingTax,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost, 0.0)) AS outDepartmentConsumptionCostAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost_exclude_tax, 0.0)) AS outDepartmentConsumptionCostAmountExcludingTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotOutCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price , 0.0)) AS allotOutPrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price_exclude_tax , 0.0)) AS allotOutPriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotOutCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutCostExcludeTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotOutInsideCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutInsideConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price , 0.0)) AS allotOutInsidePrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price_exclude_tax , 0.0)) AS allotOutInsidePriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotOutInsideCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutInsideCostExcludeTax,

            SUM(IF(action IN('报损出库', '修正报损出库'), action_count, 0.0)) AS damagedOutCount,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS damagedOutConvertPieceCount,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_price, 0.0)) AS damagedOutPrice,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_price_exclude_tax, 0.0)) AS damagedOutPriceExcludeTax,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost, 0.0)) AS damagedOutCost,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost_exclude_tax, 0.0)) AS damagedOutCostExcludeTax,

            SUM(IF(action IN('其他出库', '修正其他出库'), action_count, 0.0)) AS outOtherCount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outOtherConvertPieceCount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_price, 0.0)) AS outOtherAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_price_exclude_tax, 0.0)) AS outOtherAmountExcludingTax,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost, 0.0)) AS outOtherCostAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost_exclude_tax, 0.0)) AS outOtherCostAmountExcludingTax,

            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_count, 0.0)) AS checkOutCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkOutConvertPieceCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price, 0.0)) AS checkOutPrice,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price_exclude_tax, 0.0)) AS checkOutPriceExcludeTax,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS checkOutCost,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost_exclude_tax, 0.0)) AS checkOutCostExcludeTax,

            SUM(IF(action IN('生产出库', '修正生产出库'), action_count ,0.0)) AS productionOutCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_package_count * inventory.piece_num + action_piece_count ,0.0)) AS productionOutConvertPieceCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price,0.0)) AS productionOutPrice,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price_exclude_tax,0.0)) AS productionOutPriceExcludeTax,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost,0.0)) AS productionOutCost,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost_exclude_tax,0.0)) AS productionOutCostExcludeTax,

            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_count ,0.0)) AS deliveryOutCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_package_count * inventory.piece_num + action_piece_count ,0.0)) AS deliveryOutConvertPieceCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price,0.0)) AS deliveryOutPrice,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price_exclude_tax,0.0)) AS deliveryOutPriceExcludeTax,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost,0.0)) AS deliveryOutCost,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost_exclude_tax,0.0)) AS deliveryOutCostExcludeTax,

            <if test="param.hisType != null and param.hisType == 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>


        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
        GROUP BY goods_id
        <if test="param.limit != null and param.limit != ''">
            order by goods_id
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <select id="selectGoodsTotal" resultType="java.lang.Long">
        SELECT
            count(1)
        from(
            SELECT
                goods_id
            FROM
                ${cisTable}.${tempTable.dwdEndTable} as dgibe
                inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
            where
                create_date = CAST(#{tempTable.endDate} AS INTEGER)
                <if test="param.pharmacyType != null">
                    and pharmacy_type=#{param.pharmacyType}
                </if>
                <if test="param.pharmacyNo != null">
                    and pharmacy_no = #{param.pharmacyNo}
                </if>
                <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                    and ${param.pharmacyNosSql}
                </if>
                <if test="param.chainId != null and param.chainId != ''">
                    and dgibe.chain_id=#{param.chainId}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id=#{param.clinicId}
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                    and (${param.goodsFeeType1} or ${param.goodsFeeType2})
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                    and ${param.goodsFeeType1}
                </if>
                <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                    and ${param.goodsFeeType2}
                </if>
                <if test="goodsIds != null and goodsIds.size > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                    and dgoods.fee_type_id in
                    <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                    ${param.dimGoodsBaseMedicineTypeSql}
                </if>
                <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                    and dgoods.profit_category_type in
                    <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                        #{profitCategoryTypeId}
                    </foreach>
                </if>
            group by goods_id
        ) a
    </select>

    <!--  单据页签数据-药品维度 -->
    <select id="selectRecordByGoods" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryRecord">
        SELECT
        c.createDate,
        c.organId,
        c.action,
        c.goodsId,
        c.feeType1,
        c.feeType2,
        c.supplierId,
        c.operatorId,
        if(b.actionCount = 0, c.inPrice, b.actionCost / b.actionCount) as inPrice,
        if(b.actionCount = 0, c.outPrice, b.actionPrice / b.actionCount) as outPrice,
        b.actionPrice,
        b.actionCost,
        c.beforeCount,
        c.beforePackageCount,
        c.beforePieceCount,
        b.actionCount,
        b.actionConvertPieceCount as actionConvertPieceCount,
        b.actionPackageCount,
        b.actionPieceCount,
        d.afterCount,
        d.afterPackageCount,
        d.afterPieceCount,
        c.patientId,
        c.inOrderModified,
        c.beforePieceNumber,
        c.afterPieceNumber,
        c.comment,
        c.orderId,
        c.orderNo,
        c.scene,
        c.dispenseType,
        c.dispensingMethod,
        c.batId,
        c.profitCategoryTypeId,
        c.stockId,
        c.cooperationClinicId,
        b.ids,
        c.batchId,
        c.patientOrderId
        from
        (
        SELECT
            inventory.chain_id,
            organ_id,
            goods_id,
            bat_id,
            action,
            MIN(inventory.id) as minId,
            max(inventory.id) as maxId,
            STRING_AGG(CAST(inventory.id AS VARCHAR), ',') as ids,
            sum(action_count) as actionCount,
            sum(action_package_count * inventory.piece_num + action_piece_count) as actionConvertPieceCount,
            sum(action_package_count) as actionPackageCount,
            sum(action_piece_count) as actionPieceCount,
            sum(case when action in ('发药', '修正发药', '退药', '修正退药')
            then  case when  action in ('发药', '修正发药') then abs(if(origin_flat_price is not null,origin_flat_price,origin_stock_price))*-1
            else abs(if(origin_flat_price is not null,origin_flat_price,origin_stock_price))
            end
            else  action_total_price end) as actionPrice,
            sum(action_total_cost) as actionCost
        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="actionSql != null and actionSql != ''">
                ${actionSql}
            </if>
            <if test="param.stockIds != null and param.stockIds.size > 0">
                and stock_id in
                <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.batchIds != null and param.batchIds.size > 0">
                and batch_id in
                <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!='' ">
                and supplier_id = #{param.supplierId}
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
            <if test="param.sceneIds != null and param.sceneIds.size > 0">
                and scene in
                <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                    #{scene}
                </foreach>
            </if>
            <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                and cooperation_clinic_id = #{param.cooperationClinicId}
            </if>
        GROUP BY
            inventory.chain_id,organ_id,goods_id,bat_id,action
        order by
            maxId desc
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
        ) as b
        INNER JOIN
        (
        SELECT
        *
        from(
        SELECT
            inventory.chain_id,
            inventory.id,
            bat_id as batId,
            create_date as createDate,
            organ_id as organId,
            action as action,
            goods_id as goodsId,
            dgoods.classify_level_1 as feeType1,
            dgoods.classify_level_2 as feeType2,
            supplier_id as supplierId,
            created_user_id as operatorId,
            inventory.package_cost_price AS inPrice,
            inventory.package_price AS outPrice,
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                goods_pharmacy_before_count as beforeCount,
                goods_pharmacy_before_package_count * before_piece_num + goods_pharmacy_before_piece_count as beforeConvertPieceCount,
                goods_pharmacy_before_package_count as beforePackageCount,
                goods_pharmacy_before_piece_count as beforePieceCount,
            </if>
            <if test="param.pharmacyNosSql == null or param.pharmacyNosSql == ''">
                goods_before_count as beforeCount,
                if(action in ('药品规格修改'),goods_spec_modify_before_package_count,goods_before_package_count) * before_piece_num + if(action in ('药品规格修改'),goods_spec_modify_before_piece_count,goods_before_piece_count) as beforeConvertPieceCount,
                if(action in ('药品规格修改'),goods_spec_modify_before_package_count,goods_before_package_count) as beforePackageCount,
                if(action in ('药品规格修改'),goods_spec_modify_before_piece_count,goods_before_piece_count) as beforePieceCount,
            </if>
            dispensing_sheet_patient_id as patientId,
            if(action = '采购入库', if(stock_in_count != action_count or stock_in_cost != action_total_cost, 1, 0), 0) as inOrderModified,
            row_number() OVER(PARTITION BY goods_id,bat_id,action ORDER BY inventory.id) as sort_num,
            before_piece_num as beforePieceNumber,
            inventory.piece_num as afterPieceNumber,
            comment as comment,
            order_id as orderId,
            order_no as orderNo,
            dispense_type as dispenseType,
            scene as scene,
            dispensing_method as dispensingMethod,
            dgoods.profit_category_type as profitCategoryTypeId,
            stock_id as stockId,
            cooperation_clinic_id as cooperationClinicId,
            batch_id as batchId,
            patient_order_id as patientOrderId
        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="actionSql != null and actionSql != ''">
                ${actionSql}
            </if>
            <if test="param.stockIds != null and param.stockIds.size > 0">
                and stock_id in
                <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.batchIds != null and param.batchIds.size > 0">
                and batch_id in
                <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!='' ">
                and supplier_id = #{param.supplierId}
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
            <if test="param.sceneIds != null and param.sceneIds.size > 0">
                and scene in
                <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                    #{scene}
                </foreach>
            </if>
            <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                and cooperation_clinic_id = #{param.cooperationClinicId}
            </if>
        ) as a
        where sort_num = 1
        ORDER BY id desc
        ) as c
        on b.minId = c.id
        inner join
        (SELECT
        *
        from(
        SELECT
            inventory.id,
            inventory.chain_id,
            bat_id,
            organ_id as organId,
            goods_id as goodsId,
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                goods_pharmacy_after_count as afterCount,
                goods_pharmacy_after_package_count * inventory.piece_num + goods_pharmacy_after_piece_count as afterConvertPieceCount,
                goods_pharmacy_after_package_count as afterPackageCount,
                goods_pharmacy_after_piece_count as afterPieceCount,
            </if>
            <if test="param.pharmacyNosSql == null or param.pharmacyNosSql == ''">
                goods_after_count as afterCount,
                goods_after_package_count * inventory.piece_num + goods_after_piece_count as afterConvertPieceCount,
                goods_after_package_count as afterPackageCount,
                goods_after_piece_count as afterPieceCount,
            </if>
            row_number() OVER(PARTITION BY goods_id,bat_id,action ORDER BY inventory.id desc) as sort_num
        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="actionSql != null and actionSql != ''">
                ${actionSql}
            </if>
            <if test="param.stockIds != null and param.stockIds.size > 0">
                and stock_id in
                <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.batchIds != null and param.batchIds.size > 0">
                and batch_id in
                <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!='' ">
                and supplier_id = #{param.supplierId}
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
            <if test="param.sceneIds != null and param.sceneIds.size > 0">
                and scene in
                <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                    #{scene}
                </foreach>
            </if>
            <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                and cooperation_clinic_id = #{param.cooperationClinicId}
            </if>
        ) as a
        where sort_num = 1
        ) d
        on b.maxId = d.id
        order by
            c.createDate desc,b.maxId desc
    </select>

    <!--  药品页签数据   -->
    <select id="selectGoodsSummary" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            '合计' as goodsShortId,

            <if test="param.hisType != null and param.hisType == 10">
                SUM(coalesce(in_init_count, 0.0) + coalesce(amend_in_init_count, 0.0)) AS inInitCount,
                SUM(coalesce(in_init_piece_count, 0.0) + coalesce(amend_in_init_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(coalesce(in_init_amount, 0.0) + coalesce(amend_in_init_amount, 0.0)) AS inInitPrice,
                SUM(coalesce(in_init_amount_excluding_tax, 0.0) + coalesce(amend_in_init_amount_excluding_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(coalesce(in_init_cost_amount, 0.0) + coalesce(amend_in_init_cost_amount, 0.0)) AS inInitCost,
                SUM(coalesce(in_init_cost_amount_excluding_tax, 0.0) + coalesce(amend_in_init_cost_amount_excluding_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(coalesce(in_count,0.0) + coalesce(in_count_modify,0.0)) AS purchaseInCount,
                SUM(coalesce(in_piece_count,0.0) + coalesce(in_modify_piece_count,0.0)) AS purchaseInConvertPieceCount,
                SUM(coalesce(in_amount,0.0) + coalesce(in_amount_modify,0.0)) AS purchaseInPrice,
                SUM(coalesce(in_amount_excluding_tax,0.0) + coalesce(in_amount_modify_excluding_tax,0.0)) AS purchaseInPriceExcludeTax,
                SUM(coalesce(in_cost_amount,0.0) + coalesce(in_cost_amount_modify,0.0)) AS purchaseInCost,
                SUM(coalesce(in_cost_amount_excluding_tax,0.0) + coalesce(in_cost_amount_modify_excluding_tax,0.0)) AS purchaseInCostExcludeTax,
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                SUM(coalesce(in_init_count, 0.0) + coalesce(amend_in_init_count, 0.0) + coalesce(out_init_count, 0.0)) AS inInitCount,
                SUM(coalesce(in_init_piece_count, 0.0) + coalesce(amend_in_init_piece_count, 0.0) + coalesce(out_init_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(coalesce(in_init_amount, 0.0) + coalesce(amend_in_init_amount, 0.0) + coalesce(out_init_amount, 0.0)) AS inInitPrice,
                SUM(coalesce(in_init_amount_excluding_tax, 0.0) + coalesce(amend_in_init_amount_excluding_tax, 0.0) + coalesce(out_init_amount_excluding_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(coalesce(in_init_cost_amount, 0.0) + coalesce(amend_in_init_cost_amount, 0.0) + coalesce(out_init_cost_amount, 0.0)) AS inInitCost,
                SUM(coalesce(in_init_cost_amount_excluding_tax, 0.0) + coalesce(amend_in_init_cost_amount_excluding_tax, 0.0) + coalesce(out_init_cost_amount_excluding_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(coalesce(in_count,0.0) + coalesce(in_count_modify,0.0) + coalesce(out_count_refund,0.0)) AS purchaseInCount,
                SUM(coalesce(in_piece_count,0.0) + coalesce(in_modify_piece_count,0.0) + coalesce(out_refund_piece_count,0.0)) AS purchaseInConvertPieceCount,
                SUM(coalesce(in_amount,0.0) + coalesce(in_amount_modify,0.0) + coalesce(out_amount_refund,0.0)) AS purchaseInPrice,
                SUM(coalesce(in_amount_excluding_tax,0.0) + coalesce(in_amount_modify_excluding_tax,0.0) + coalesce(out_amount_refund_excluding_tax,0.0)) AS purchaseInPriceExcludeTax,
                SUM(coalesce(in_cost_amount,0.0) + coalesce(in_cost_amount_modify,0.0) + coalesce(out_cost_amount_refund,0.0)) AS purchaseInCost,
                SUM(coalesce(in_cost_amount_excluding_tax,0.0) + coalesce(in_cost_amount_modify_excluding_tax,0.0) + coalesce(out_cost_amount_refund_excluding_tax,0.0)) AS purchaseInCostExcludeTax,
            </if>

            SUM(coalesce(allot_in_count, 0.0)) AS allotInCount,
            SUM(coalesce(in_allot_piece_count, 0.0)) AS allotInConvertPieceCount,
            SUM(coalesce(allot_in_amount, 0.0)) AS allotInPrice,
            SUM(coalesce(allot_in_amount_excluding_tax, 0.0)) AS allotInPriceExcludeTax,
            SUM(coalesce(allot_in_cost_amount, 0.0)) AS allotInCost,
            SUM(coalesce(allot_in_cost_amount_excluding_tax, 0.0)) AS allotInCostExcludeTax,

            SUM(coalesce(in_allot_inside_count, 0.0)) AS allotInInsideCount,
            SUM(coalesce(in_allot_inside_piece_count, 0.0)) AS allotInInsideConvertPieceCount,
            SUM(coalesce(in_allot_inside_amount, 0.0)) AS allotInInsidePrice,
            SUM(coalesce(in_allot_inside_amount_excluding_tax, 0.0)) AS allotInInsidePriceExcludeTax,
            SUM(coalesce(in_allot_inside_cost_amount, 0.0)) AS allotInInsideCost,
            SUM(coalesce(in_allot_inside_cost_amount_excluding_tax, 0.0)) AS allotInInsideCostExcludeTax,

            SUM(coalesce(check_in_count, 0.0)) AS checkInCount,
            SUM(coalesce(in_check_piece_count, 0.0)) AS checkInConvertPieceCount,
            SUM(coalesce(check_in_amount, 0.0)) AS checkInPrice,
            SUM(coalesce(check_in_amount_excluding_tax, 0.0)) AS checkInPriceExcludeTax,
            SUM(coalesce(check_in_cost_amount, 0.0)) AS checkInCost,
            SUM(coalesce(check_in_cost_amount_excluding_tax, 0.0)) AS checkInCostExcludeTax,

            SUM(coalesce(in_receive_count,0.0) + coalesce(out_receive_return_count,0.0)) AS inReceiveCount,
            SUM(coalesce(in_receive_piece_count,0.0) + coalesce(out_receive_return_piece_count,0.0)) AS inReceiveConvertPieceCount,
            SUM(coalesce(in_receive_amount,0.0) + coalesce(out_receive_return_amount,0.0)) AS inReceiveAmount,
            SUM(coalesce(in_receive_amount_excluding_tax,0.0) + coalesce(out_receive_return_amount_excluding_tax,0.0)) AS inReceiveAmountExcludingTax,
            SUM(coalesce(in_receive_cost_amount,0.0) + coalesce(out_receive_return_cost_amount,0.0)) AS inReceiveCostAmount,
            SUM(coalesce(in_receive_cost_amount_excluding_tax,0.0) + coalesce(out_receive_return_cost_amount_excluding_tax,0.0)) AS inReceiveCostAmountExcludingTax,

            SUM(coalesce(in_spec_count_modify, 0.0)) AS inSpecificationModificationCount,
            SUM(coalesce(in_modify_spec_piece_count, 0.0)) AS inSpecificationModificationConvertPieceCount,
            SUM(coalesce(in_spec_amount_modify, 0.0)) AS inSpecificationModificationPrice,
            SUM(coalesce(in_spec_amount_modify_excluding_tax, 0.0)) AS inSpecificationModificationPriceExcludeTax,
            SUM(coalesce(in_spec_cost_amount_modify, 0.0)) AS inSpecificationModificationCost,
            SUM(coalesce(in_spec_cost_amount_modify_excluding_tax, 0.0)) AS inSpecificationModificationCostExcludeTax,

            <if test="param.hisType != null and param.hisType == 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(coalesce(in_init_count, 0.0) + coalesce(amend_in_init_count, 0.0) + coalesce(in_count,0.0) + coalesce(in_count_modify,0.0) + coalesce(allot_in_count, 0.0) + coalesce(in_allot_inside_count, 0.0) + coalesce(check_in_count, 0.0) + coalesce(in_receive_count,0.0) + coalesce(out_receive_return_count,0.0)) AS inTotalCount,
                    SUM(coalesce(in_init_piece_count, 0.0) + coalesce(amend_in_init_piece_count, 0.0) + coalesce(in_piece_count,0.0) + coalesce(in_modify_piece_count,0.0) + coalesce(in_allot_piece_count, 0.0) + coalesce(in_allot_inside_piece_count, 0.0) + coalesce(in_check_piece_count, 0.0) + coalesce(in_receive_piece_count,0.0) + coalesce(out_receive_return_piece_count,0.0)) AS inTotalConvertPieceCount,
                    SUM(coalesce(in_init_amount, 0.0) + coalesce(amend_in_init_amount, 0.0) + coalesce(in_amount,0.0) + coalesce(in_amount_modify,0.0) + coalesce(allot_in_amount, 0.0) + coalesce(in_allot_inside_amount, 0.0) + coalesce(check_in_amount, 0.0) + coalesce(in_receive_amount,0.0) + coalesce(out_receive_return_amount,0.0)) AS inTotalPrice,
                    SUM(coalesce(in_init_amount_excluding_tax, 0.0) + coalesce(amend_in_init_amount_excluding_tax, 0.0) + coalesce(in_amount_excluding_tax,0.0) + coalesce(in_amount_modify_excluding_tax,0.0) + coalesce(allot_in_amount_excluding_tax, 0.0) + coalesce(in_allot_inside_amount_excluding_tax, 0.0) + coalesce(check_in_amount_excluding_tax, 0.0) + coalesce(in_receive_amount_excluding_tax,0.0) + coalesce(out_receive_return_amount_excluding_tax,0.0)) AS inTotalPriceExcludeTax,
                    SUM(coalesce(in_init_cost_amount, 0.0) + coalesce(amend_in_init_cost_amount, 0.0) + coalesce(in_cost_amount,0.0) + coalesce(in_cost_amount_modify,0.0) + coalesce(allot_in_cost_amount, 0.0) + coalesce(in_allot_inside_cost_amount, 0.0) + coalesce(check_in_cost_amount, 0.0) + coalesce(in_receive_cost_amount,0.0) + coalesce(out_receive_return_cost_amount,0.0)) AS inTotalCost,
                    SUM(coalesce(in_init_cost_amount_excluding_tax, 0.0) + coalesce(amend_in_init_cost_amount_excluding_tax, 0.0) + coalesce(in_cost_amount_excluding_tax,0.0) + coalesce(in_cost_amount_modify_excluding_tax,0.0) + coalesce(allot_in_cost_amount_excluding_tax, 0.0) + coalesce(in_allot_inside_cost_amount_excluding_tax, 0.0) + coalesce(check_in_cost_amount_excluding_tax, 0.0) + coalesce(in_receive_cost_amount_excluding_tax,0.0) + coalesce(out_receive_return_cost_amount_excluding_tax,0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(coalesce(in_init_count, 0.0) + coalesce(amend_in_init_count, 0.0) + coalesce(in_count,0.0) + coalesce(in_count_modify,0.0) + coalesce(allot_in_count, 0.0) + coalesce(check_in_count, 0.0) + coalesce(in_receive_count,0.0) + coalesce(out_receive_return_count,0.0)) AS inTotalCount,
                    SUM(coalesce(in_init_piece_count, 0.0) + coalesce(amend_in_init_piece_count, 0.0) + coalesce(in_piece_count,0.0) + coalesce(in_modify_piece_count,0.0) + coalesce(in_allot_piece_count, 0.0) + coalesce(in_check_piece_count, 0.0) + coalesce(in_receive_piece_count,0.0) + coalesce(out_receive_return_piece_count,0.0)) AS inTotalConvertPieceCount,
                    SUM(coalesce(in_init_amount, 0.0) + coalesce(amend_in_init_amount, 0.0) + coalesce(in_amount,0.0) + coalesce(in_amount_modify,0.0) + coalesce(allot_in_amount, 0.0) + coalesce(check_in_amount, 0.0) + coalesce(in_receive_amount,0.0) + coalesce(out_receive_return_amount,0.0)) AS inTotalPrice,
                    SUM(coalesce(in_init_amount_excluding_tax, 0.0) + coalesce(amend_in_init_amount_excluding_tax, 0.0) + coalesce(in_amount_excluding_tax,0.0) + coalesce(in_amount_modify_excluding_tax,0.0) + coalesce(allot_in_amount_excluding_tax, 0.0) + coalesce(check_in_amount_excluding_tax, 0.0) + coalesce(in_receive_amount_excluding_tax,0.0) + coalesce(out_receive_return_amount_excluding_tax,0.0)) AS inTotalPriceExcludeTax,
                    SUM(coalesce(in_init_cost_amount, 0.0) + coalesce(amend_in_init_cost_amount, 0.0) + coalesce(in_cost_amount,0.0) + coalesce(in_cost_amount_modify,0.0) + coalesce(allot_in_cost_amount, 0.0) + coalesce(in_allot_inside_cost_amount, 0.0) + coalesce(check_in_cost_amount, 0.0) + coalesce(in_receive_cost_amount,0.0) + coalesce(out_receive_return_cost_amount,0.0)) AS inTotalCost,
                    SUM(coalesce(in_init_cost_amount_excluding_tax, 0.0) + coalesce(amend_in_init_cost_amount_excluding_tax, 0.0) + coalesce(in_cost_amount_excluding_tax,0.0) + coalesce(in_cost_amount_modify_excluding_tax,0.0) + coalesce(allot_in_cost_amount_excluding_tax, 0.0) + coalesce(check_in_cost_amount_excluding_tax, 0.0) + coalesce(in_receive_cost_amount_excluding_tax,0.0) + coalesce(out_receive_return_cost_amount_excluding_tax,0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(coalesce(in_init_count, 0.0) + coalesce(amend_in_init_count, 0.0) + coalesce(in_count,0.0) + coalesce(in_count_modify,0.0) + coalesce(out_count_refund,0.0) + coalesce(allot_in_count, 0.0) + coalesce(in_allot_inside_count, 0.0) + coalesce(check_in_count, 0.0) + coalesce(in_receive_count,0.0) + coalesce(out_receive_return_count,0.0)) AS inTotalCount,
                    SUM(coalesce(in_init_piece_count, 0.0) + coalesce(amend_in_init_piece_count, 0.0) + coalesce(in_piece_count,0.0) + coalesce(in_modify_piece_count,0.0) + coalesce(out_refund_piece_count,0.0) + coalesce(in_allot_piece_count, 0.0) + coalesce(in_allot_inside_piece_count, 0.0) + coalesce(in_check_piece_count, 0.0) + coalesce(in_receive_piece_count,0.0) + coalesce(out_receive_return_piece_count,0.0)) AS inTotalConvertPieceCount,
                    SUM(coalesce(in_init_amount, 0.0) + coalesce(amend_in_init_amount, 0.0) + coalesce(in_amount,0.0) + coalesce(in_amount_modify,0.0) + coalesce(out_amount_refund,0.0) + coalesce(allot_in_amount, 0.0) + coalesce(in_allot_inside_amount, 0.0) + coalesce(check_in_amount, 0.0) + coalesce(in_receive_amount,0.0) + coalesce(out_receive_return_amount,0.0)) AS inTotalPrice,
                    SUM(coalesce(in_init_amount_excluding_tax, 0.0) + coalesce(amend_in_init_amount_excluding_tax, 0.0) + coalesce(in_amount_excluding_tax,0.0) + coalesce(in_amount_modify_excluding_tax,0.0) + coalesce(out_amount_refund_excluding_tax,0.0) + coalesce(allot_in_amount_excluding_tax, 0.0) + coalesce(in_allot_inside_amount_excluding_tax, 0.0) + coalesce(check_in_amount_excluding_tax, 0.0) + coalesce(in_receive_amount_excluding_tax,0.0) + coalesce(out_receive_return_amount_excluding_tax,0.0)) AS inTotalPriceExcludeTax,
                    SUM(coalesce(in_init_cost_amount, 0.0) + coalesce(amend_in_init_cost_amount, 0.0) + coalesce(in_cost_amount,0.0) + coalesce(in_cost_amount_modify,0.0) + coalesce(out_cost_amount_refund,0.0) + coalesce(allot_in_cost_amount, 0.0) + coalesce(in_allot_inside_cost_amount, 0.0) + coalesce(check_in_cost_amount, 0.0) + coalesce(in_receive_cost_amount,0.0) + coalesce(out_receive_return_cost_amount,0.0)) AS inTotalCost,
                    SUM(coalesce(in_init_cost_amount_excluding_tax, 0.0) + coalesce(amend_in_init_cost_amount_excluding_tax, 0.0) + coalesce(in_cost_amount_excluding_tax,0.0) + coalesce(in_cost_amount_modify_excluding_tax,0.0) + coalesce(out_cost_amount_refund_excluding_tax,0.0) + coalesce(allot_in_cost_amount_excluding_tax, 0.0) + coalesce(in_allot_inside_cost_amount_excluding_tax, 0.0) + coalesce(check_in_cost_amount_excluding_tax, 0.0) + coalesce(in_receive_cost_amount_excluding_tax,0.0) + coalesce(out_receive_return_cost_amount_excluding_tax,0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(coalesce(in_init_count, 0.0) + coalesce(amend_in_init_count, 0.0) + coalesce(out_init_count,0.0) + coalesce(in_count,0.0) + coalesce(in_count_modify,0.0) + coalesce(out_count_refund,0.0) +  coalesce(allot_in_count, 0.0) + coalesce(check_in_count, 0.0) + coalesce(in_receive_count,0.0) + coalesce(out_receive_return_count,0.0)) AS inTotalCount,
                    SUM(coalesce(in_init_piece_count, 0.0) + coalesce(amend_in_init_piece_count, 0.0) + coalesce(out_init_piece_count,0.0) + coalesce(in_piece_count,0.0) + coalesce(in_modify_piece_count,0.0) + coalesce(out_refund_piece_count,0.0) +  coalesce(in_allot_piece_count, 0.0) + coalesce(in_check_piece_count, 0.0) + coalesce(in_receive_piece_count,0.0) + coalesce(out_receive_return_piece_count,0.0)) AS inTotalConvertPieceCount,
                    SUM(coalesce(in_init_amount, 0.0) + coalesce(amend_in_init_amount, 0.0) + coalesce(out_init_cost_amount,0.0) + coalesce(in_amount,0.0) + coalesce(in_amount_modify,0.0) + coalesce(out_amount_refund,0.0) +  coalesce(allot_in_amount, 0.0) + coalesce(check_in_amount, 0.0) + coalesce(in_receive_amount,0.0) + coalesce(out_receive_return_amount,0.0)) AS inTotalPrice,
                    SUM(coalesce(in_init_amount_excluding_tax, 0.0) + coalesce(amend_in_init_amount_excluding_tax, 0.0) + coalesce(out_init_cost_amount_excluding_tax,0.0) + coalesce(in_amount_excluding_tax,0.0) + coalesce(in_amount_modify_excluding_tax,0.0) + coalesce(out_amount_refund_excluding_tax,0.0) +  coalesce(allot_in_amount_excluding_tax, 0.0) + coalesce(check_in_amount_excluding_tax, 0.0) + coalesce(in_receive_amount_excluding_tax,0.0) + coalesce(out_receive_return_amount_excluding_tax,0.0)) AS inTotalPriceExcludeTax,
                    SUM(coalesce(in_init_cost_amount, 0.0) + coalesce(amend_in_init_cost_amount, 0.0) + coalesce(out_init_amount,0.0) + coalesce(in_cost_amount,0.0) + coalesce(in_cost_amount_modify,0.0) + coalesce(out_cost_amount_refund,0.0) +  coalesce(allot_in_cost_amount, 0.0) + coalesce(in_allot_inside_cost_amount, 0.0) + coalesce(check_in_cost_amount, 0.0) + coalesce(in_receive_cost_amount,0.0) + coalesce(out_receive_return_cost_amount,0.0)) AS inTotalCost,
                    SUM(coalesce(in_init_cost_amount_excluding_tax, 0.0) + coalesce(amend_in_init_cost_amount_excluding_tax, 0.0) + coalesce(out_init_amount_excluding_tax,0.0) + coalesce(in_cost_amount_excluding_tax,0.0) + coalesce(in_cost_amount_modify_excluding_tax,0.0) + coalesce(out_cost_amount_refund_excluding_tax,0.0) +  coalesce(allot_in_cost_amount_excluding_tax, 0.0) + coalesce(check_in_cost_amount_excluding_tax, 0.0) + coalesce(in_receive_cost_amount_excluding_tax,0.0) + coalesce(out_receive_return_cost_amount_excluding_tax,0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>

            SUM(coalesce(out_init_count,0.0)) AS inInitReturnCount,
            SUM(coalesce(out_init_piece_count,0.0)) AS inInitReturnConvertPieceCount,
            SUM(coalesce(out_init_amount,0.0)) AS inInitReturnPrice,
            SUM(coalesce(out_init_amount_excluding_tax,0.0)) AS inInitReturnPriceExcludeTax,
            SUM(coalesce(out_init_cost_amount,0.0)) AS inInitReturnCost,
            SUM(coalesce(out_init_cost_amount_excluding_tax,0.0)) AS inInitReturnCostExcludeTax,

            SUM(coalesce(out_count_refund,0.0)) AS returnGoodsOutCount,
            SUM(coalesce(out_refund_piece_count,0.0)) AS returnGoodsOutConvertPieceCount,
            SUM(coalesce(out_amount_refund,0.0)) AS returnGoodsOutPrice,
            SUM(coalesce(out_amount_refund_excluding_tax,0.0)) AS returnGoodsOutPriceExcludeTax,
            SUM(coalesce(out_cost_amount_refund,0.0)) AS returnGoodsOutCost,
            SUM(coalesce(out_cost_amount_refund_excluding_tax,0.0)) AS returnGoodsOutCostExcludeTax,

            SUM(coalesce(dispense_count, 0.0) + coalesce(return_count, 0.0)) AS dispenseCount,
            SUM(coalesce(out_dispense_piece_count, 0.0) + coalesce(in_return_piece_count, 0.0)) AS dispenseConvertPieceCount,
            SUM(coalesce(dispense_amount, 0.0) + coalesce(return_amount, 0.0)) AS dispensePrice,
            SUM(coalesce(dispense_amount_excluding_tax, 0.0) + coalesce(return_amount_excluding_tax, 0.0)) AS dispensePriceExcludeTax,
            SUM(coalesce(dispense_cost_amount, 0.0) + coalesce(return_cost_amount, 0.0)) AS dispenseCost,
            SUM(coalesce(dispense_cost_amount_excluding_tax, 0.0) + coalesce(return_cost_amount_excluding_tax, 0.0)) AS dispenseCostExcludeTax,

            SUM(coalesce(outpatient_dispense_count, 0.0) + coalesce(outpatient_return_count, 0.0)) AS outPatientDispenseCount,
            SUM(coalesce(outpatient_dispense_piece_count, 0.0) + coalesce(outpatient_return_piece_count, 0.0)) AS outPatientDispenseConvertPieceCount,
            SUM(coalesce(outpatient_dispense_amount, 0.0) + coalesce(outpatient_return_amount, 0.0)) AS outPatientDispensePrice,
            SUM(coalesce(outpatient_dispense_amount_excluding_tax, 0.0) + coalesce(outpatient_return_amount_excluding_tax, 0.0)) AS outPatientDispensePriceExcludeTax,
            SUM(coalesce(outpatient_dispense_cost_amount, 0.0) + coalesce(outpatient_return_cost_amount, 0.0)) AS outPatientDispenseCost,
            SUM(coalesce(outpatient_dispense_cost_amount_excluding_tax, 0.0) + coalesce(outpatient_return_cost_amount_excluding_tax, 0.0)) AS outPatientDispenseCostExcludeTax,

            SUM(coalesce(inpatient_dispense_count, 0.0) + coalesce(inpatient_return_count, 0.0)) AS hospitalPharmacyDispenseCount,
            SUM(coalesce(inpatient_dispense_piece_count, 0.0) + coalesce(inpatient_return_piece_count, 0.0)) AS hospitalPharmacyDispenseConvertPieceCount,
            SUM(coalesce(inpatient_dispense_amount, 0.0) + coalesce(inpatient_return_amount, 0.0)) AS hospitalPharmacyDispensePrice,
            SUM(coalesce(inpatient_dispense_amount_excluding_tax, 0.0) + coalesce(inpatient_return_amount_excluding_tax, 0.0)) AS hospitalPharmacyDispensePriceExcludeTax,
            SUM(coalesce(inpatient_dispense_cost_amount, 0.0) + coalesce(inpatient_return_cost_amount, 0.0)) AS hospitalPharmacyDispenseCost,
            SUM(coalesce(inpatient_dispense_cost_amount_excluding_tax, 0.0) + coalesce(inpatient_return_cost_amount_excluding_tax, 0.0)) AS hospitalPharmacyDispenseCostExcludeTax,

            SUM(coalesce(automatic_dispense_count, 0.0) + coalesce(automatic_return_count, 0.0)) AS hospitalAutomaticDispenseCount,
            SUM(coalesce(automatic_dispense_piece_count, 0.0) + coalesce(automatic_return_piece_count, 0.0)) AS hospitalAutomaticDispenseConvertPieceCount,
            SUM(coalesce(automatic_dispense_amount, 0.0) + coalesce(automatic_return_amount, 0.0)) AS hospitalAutomaticDispensePrice,
            SUM(coalesce(automatic_dispense_amount_excluding_tax, 0.0) + coalesce(automatic_return_amount_excluding_tax, 0.0)) AS hospitalAutomaticDispensePriceExcludeTax,
            SUM(coalesce(automatic_dispense_cost_amount, 0.0) + coalesce(automatic_return_cost_amount, 0.0)) AS hospitalAutomaticDispenseCost,
            SUM(coalesce(automatic_dispense_cost_amount_excluding_tax, 0.0) + coalesce(automatic_return_cost_amount_excluding_tax, 0.0)) AS hospitalAutomaticDispenseCostExcludeTax,

            SUM(coalesce(no_settle_dispense_count, 0.0) + coalesce(no_settle_return_count, 0.0)) AS hospitalNoSettleDispenseCount,
            SUM(coalesce(no_settle_dispense_piece_count, 0.0) + coalesce(no_settle_return_piece_count, 0.0)) AS hospitalNoSettleDispenseConvertPieceCount,
            SUM(coalesce(no_settle_dispense_amount, 0.0) + coalesce(no_settle_return_amount, 0.0)) AS hospitalNoSettleDispensePrice,
            SUM(coalesce(no_settle_dispense_amount_excluding_tax, 0.0) + coalesce(no_settle_return_amount_excluding_tax, 0.0)) AS hospitalNoSettleDispensePriceExcludeTax,
            SUM(coalesce(no_settle_dispense_cost_amount, 0.0) + coalesce(no_settle_return_cost_amount, 0.0)) AS hospitalNoSettleDispenseCost,
            SUM(coalesce(no_settle_dispense_cost_amount_excluding_tax, 0.0) + coalesce(no_settle_return_cost_amount_excluding_tax, 0.0)) AS hospitalNoSettleDispenseCostExcludeTax,

            SUM(coalesce(out_count_recipients, 0.0) + coalesce(in_receive_return_count, 0.0)) AS collectOutCount,
            SUM(coalesce(out_recipients_piece_count, 0.0) + coalesce(in_receive_return_piece_count, 0.0)) AS collectOutConvertPieceCount,
            SUM(coalesce(out_amount_recipients, 0.0) + coalesce(in_receive_return_amount, 0.0)) AS collectOutPrice,
            SUM(coalesce(out_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_amount_excluding_tax, 0.0)) AS collectOutPriceExcludeTax,
            SUM(coalesce(out_cost_amount_recipients, 0.0) + coalesce(in_receive_return_cost_amount, 0.0)) AS collectOutCost,
            SUM(coalesce(out_cost_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_cost_amount_excluding_tax, 0.0)) AS collectOutCostExcludeTax,

            SUM(coalesce(allot_out_count, 0.0)) AS allotOutCount,
            SUM(coalesce(out_allot_piece_count, 0.0)) AS allotOutConvertPieceCount,
            SUM(coalesce(allot_out_amount, 0.0)) AS allotOutPrice,
            SUM(coalesce(allot_out_amount_excluding_tax, 0.0)) AS allotOutPriceExcludeTax,
            SUM(coalesce(allot_out_cost_amount, 0.0)) AS allotOutCost,
            SUM(coalesce(allot_out_cost_amount_excluding_tax, 0.0)) AS allotOutCostExcludeTax,

            SUM(coalesce(out_allot_inside_count, 0.0)) AS allotOutInsideCount,
            SUM(coalesce(out_allot_inside_piece_count, 0.0)) AS allotOutInsideConvertPieceCount,
            SUM(coalesce(out_allot_inside_amount, 0.0)) AS allotOutInsidePrice,
            SUM(coalesce(out_allot_inside_amount_excluding_tax, 0.0)) AS allotOutInsidePriceExcludeTax,
            SUM(coalesce(out_allot_inside_cost_amount, 0.0)) AS allotOutInsideCost,
            SUM(coalesce(out_allot_inside_cost_amount_excluding_tax, 0.0)) AS allotOutInsideCostExcludeTax,

            SUM(coalesce(out_count_breakage, 0.0)) AS damagedOutCount,
            SUM(coalesce(out_breakage_piece_count, 0.0)) AS damagedOutConvertPieceCount,
            SUM(coalesce(out_amount_breakage, 0.0)) AS damagedOutPrice,
            SUM(coalesce(out_amount_breakage_excluding_tax, 0.0)) AS damagedOutPriceExcludeTax,
            SUM(coalesce(out_cost_amount_breakage, 0.0)) AS damagedOutCost,
            SUM(coalesce(out_cost_amount_breakage_excluding_tax, 0.0)) AS damagedOutCostExcludeTax,

            SUM(coalesce(out_department_consumption_count, 0.0)) AS outDepartmentConsumptionCount,
            SUM(coalesce(out_department_consumption_piece_count, 0.0)) AS outDepartmentConsumptionConvertPieceCount,
            SUM(coalesce(out_department_consumption_amount, 0.0)) AS outDepartmentConsumptionAmount,
            SUM(coalesce(out_department_consumption_amount_excluding_tax, 0.0)) AS outDepartmentConsumptionAmountExcludingTax,
            SUM(coalesce(out_department_consumption_cost_amount, 0.0)) AS outDepartmentConsumptionCostAmount,
            SUM(coalesce(out_department_consumption_cost_amount_excluding_tax, 0.0)) AS outDepartmentConsumptionCostAmountExcludingTax,

            SUM(coalesce(out_other_count, 0.0)) AS outOtherCount,
            SUM(coalesce(out_other_piece_count, 0.0)) AS outOtherConvertPieceCount,
            SUM(coalesce(out_other_amount, 0.0)) AS outOtherAmount,
            SUM(coalesce(out_other_amount_excluding_tax, 0.0)) AS outOtherAmountExcludingTax,
            SUM(coalesce(out_other_cost_amount, 0.0)) AS outOtherCostAmount,
            SUM(coalesce(out_other_cost_amount_excluding_tax, 0.0)) AS outOtherCostAmountExcludingTax,

            SUM(coalesce(check_out_count, 0.0)) AS checkOutCount,
            SUM(coalesce(out_check_piece_count, 0.0)) AS checkOutConvertPieceCount,
            SUM(coalesce(check_out_amount, 0.0)) AS checkOutPrice,
            SUM(coalesce(check_out_amount_excluding_tax, 0.0)) AS checkOutPriceExcludeTax,
            SUM(coalesce(check_out_cost_amount, 0.0)) AS checkOutCost,
            SUM(coalesce(check_out_cost_amount_excluding_tax, 0.0)) AS checkOutCostExcludeTax,

            SUM(coalesce(out_production_count, 0.0)) AS productionOutCount,
            SUM(coalesce(out_production_piece_count, 0.0)) AS productionOutConvertPieceCount,
            SUM(coalesce(out_production_amount, 0.0)) AS productionOutPrice,
            SUM(coalesce(out_production_amount_excluding_tax, 0.0)) AS productionOutPriceExcludeTax,
            SUM(coalesce(out_production_cost_amount, 0.0)) AS productionOutCost,
            SUM(coalesce(out_production_cost_amount_excluding_tax, 0.0)) AS productionOutCostExcludeTax,

            SUM(coalesce(delivery_out_count, 0.0) + coalesce(delivery_return_in_count, 0.0)) AS deliveryOutCount,
            SUM(coalesce(delivery_out_piece_count, 0.0) + coalesce(delivery_return_in_piece_count, 0.0)) AS deliveryOutConvertPieceCount,
            SUM(coalesce(delivery_out_amount, 0.0) + coalesce(delivery_return_in_amount, 0.0)) AS deliveryOutPrice,
            SUM(coalesce(delivery_out_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_amount_excluding_tax, 0.0)) AS deliveryOutPriceExcludeTax,
            SUM(coalesce(delivery_out_cost_amount, 0.0) + coalesce(delivery_return_in_cost_amount, 0.0)) AS deliveryOutCost,
            SUM(coalesce(delivery_out_cost_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_cost_amount_excluding_tax, 0.0)) AS deliveryOutCostExcludeTax,

            <if test="param.hisType != null and param.hisType == 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(coalesce(out_init_count,0.0) + coalesce(out_count_refund,0.0) + coalesce(dispense_count, 0.0) + coalesce(return_count, 0.0) + coalesce(out_count_recipients, 0.0) + coalesce(in_receive_return_count, 0.0) + coalesce(allot_out_count, 0.0) + coalesce(out_allot_inside_count, 0.0) + coalesce(out_count_breakage, 0.0) + coalesce(out_department_consumption_count, 0.0) + coalesce(out_other_count, 0.0) + coalesce(check_out_count, 0.0)+ coalesce(out_production_count, 0.0) + coalesce(delivery_out_count, 0.0) + coalesce(delivery_return_in_count, 0.0)) AS outTotalCount,
                    SUM(coalesce(out_init_piece_count,0.0) + coalesce(out_refund_piece_count,0.0) + coalesce(out_dispense_piece_count, 0.0) + coalesce(in_return_piece_count, 0.0) + coalesce(out_recipients_piece_count, 0.0) + coalesce(in_receive_return_piece_count, 0.0) + coalesce(out_allot_piece_count, 0.0) + coalesce(out_allot_inside_piece_count, 0.0) + coalesce(out_breakage_piece_count, 0.0) + coalesce(out_department_consumption_piece_count, 0.0) + coalesce(out_other_piece_count, 0.0) + coalesce(out_check_piece_count, 0.0)+ coalesce(out_production_piece_count, 0.0) + coalesce(delivery_out_piece_count, 0.0) + coalesce(delivery_return_in_piece_count, 0.0)) AS outTotalConvertPieceCount,
                    SUM(coalesce(out_init_amount,0.0) + coalesce(out_amount_refund,0.0) + coalesce(dispense_amount, 0.0) + coalesce(return_amount, 0.0) + coalesce(out_amount_recipients, 0.0) + coalesce(in_receive_return_amount, 0.0) + coalesce(allot_out_amount, 0.0) + coalesce(out_allot_inside_amount, 0.0) + coalesce(out_amount_breakage, 0.0) + coalesce(out_department_consumption_amount, 0.0) + coalesce(out_other_amount, 0.0) + coalesce(check_out_amount, 0.0)+ coalesce(out_production_amount, 0.0) + coalesce(delivery_out_amount, 0.0) + coalesce(delivery_return_in_amount, 0.0)) AS outTotalPrice,
                    SUM(coalesce(out_init_amount_excluding_tax,0.0) + coalesce(out_amount_refund_excluding_tax,0.0) + coalesce(dispense_amount_excluding_tax, 0.0) + coalesce(return_amount_excluding_tax, 0.0) + coalesce(out_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_amount_excluding_tax, 0.0) + coalesce(allot_out_amount_excluding_tax, 0.0) + coalesce(out_allot_inside_amount_excluding_tax, 0.0) + coalesce(out_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_amount_excluding_tax, 0.0) + coalesce(out_other_amount_excluding_tax, 0.0) + coalesce(check_out_amount_excluding_tax, 0.0)+ coalesce(out_production_amount_excluding_tax, 0.0) + coalesce(delivery_out_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_amount_excluding_tax, 0.0)) AS outTotalPriceExcludeTax,
                    SUM(coalesce(out_init_cost_amount,0.0) + coalesce(out_cost_amount_refund,0.0) + coalesce(dispense_cost_amount, 0.0) + coalesce(return_cost_amount, 0.0) + coalesce(out_cost_amount_recipients, 0.0) + coalesce(in_receive_return_cost_amount, 0.0) + coalesce(allot_out_cost_amount, 0.0) + coalesce(out_allot_inside_cost_amount, 0.0) + coalesce(out_cost_amount_breakage, 0.0) + coalesce(out_department_consumption_cost_amount, 0.0) + coalesce(out_other_cost_amount, 0.0) + coalesce(check_out_cost_amount, 0.0)+ coalesce(out_production_cost_amount, 0.0) + coalesce(delivery_out_cost_amount, 0.0) + coalesce(delivery_return_in_cost_amount, 0.0)) AS outTotalCost,
                    SUM(coalesce(out_init_cost_amount_excluding_tax,0.0) + coalesce(out_cost_amount_refund_excluding_tax,0.0) + coalesce(dispense_cost_amount_excluding_tax, 0.0) + coalesce(return_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_cost_amount_excluding_tax, 0.0) + coalesce(allot_out_cost_amount_excluding_tax, 0.0) + coalesce(out_allot_inside_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_cost_amount_excluding_tax, 0.0) + coalesce(out_other_cost_amount_excluding_tax, 0.0) + coalesce(check_out_cost_amount_excluding_tax, 0.0)+ coalesce(out_production_cost_amount_excluding_tax, 0.0) + coalesce(delivery_out_cost_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_cost_amount_excluding_tax, 0.0)) AS outTotalCostExcludeTax
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(coalesce(out_init_count,0.0) + coalesce(out_count_refund,0.0) + coalesce(dispense_count, 0.0) + coalesce(return_count, 0.0) + coalesce(out_count_recipients, 0.0) + coalesce(in_receive_return_count, 0.0) + coalesce(allot_out_count, 0.0) + coalesce(out_count_breakage, 0.0) + coalesce(out_department_consumption_count, 0.0) + coalesce(out_other_count, 0.0) + coalesce(check_out_count, 0.0)+ coalesce(out_production_count, 0.0) + coalesce(delivery_out_count, 0.0) + coalesce(delivery_return_in_count, 0.0)) AS outTotalCount,
                    SUM(coalesce(out_init_piece_count,0.0) + coalesce(out_refund_piece_count,0.0) + coalesce(out_dispense_piece_count, 0.0) + coalesce(in_return_piece_count, 0.0) + coalesce(out_recipients_piece_count, 0.0) + coalesce(in_receive_return_piece_count, 0.0) + coalesce(out_allot_piece_count, 0.0) + coalesce(out_breakage_piece_count, 0.0) + coalesce(out_department_consumption_piece_count, 0.0) + coalesce(out_other_piece_count, 0.0) + coalesce(out_check_piece_count, 0.0)+ coalesce(out_production_piece_count, 0.0) + coalesce(delivery_out_piece_count, 0.0) + coalesce(delivery_return_in_piece_count, 0.0)) AS outTotalConvertPieceCount,
                    SUM(coalesce(out_init_amount,0.0) + coalesce(out_amount_refund,0.0) + coalesce(dispense_amount, 0.0) + coalesce(return_amount, 0.0) + coalesce(out_amount_recipients, 0.0) + coalesce(in_receive_return_amount, 0.0) + coalesce(allot_out_amount, 0.0) + coalesce(out_amount_breakage, 0.0) + coalesce(out_department_consumption_amount, 0.0) + coalesce(out_other_amount, 0.0) + coalesce(check_out_amount, 0.0)+ coalesce(out_production_amount, 0.0) + coalesce(delivery_out_amount, 0.0) + coalesce(delivery_return_in_amount, 0.0)) AS outTotalPrice,
                    SUM(coalesce(out_init_amount_excluding_tax,0.0) + coalesce(out_amount_refund_excluding_tax,0.0) + coalesce(dispense_amount_excluding_tax, 0.0) + coalesce(return_amount_excluding_tax, 0.0) + coalesce(out_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_amount_excluding_tax, 0.0) + coalesce(allot_out_amount_excluding_tax, 0.0) + coalesce(out_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_amount_excluding_tax, 0.0) + coalesce(out_other_amount_excluding_tax, 0.0) + coalesce(check_out_amount_excluding_tax, 0.0)+ coalesce(out_production_amount_excluding_tax, 0.0) + coalesce(delivery_out_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_amount_excluding_tax, 0.0)) AS outTotalPriceExcludeTax,
                    SUM(coalesce(out_init_cost_amount,0.0) + coalesce(out_cost_amount_refund,0.0) + coalesce(dispense_cost_amount, 0.0) + coalesce(return_cost_amount, 0.0) + coalesce(out_cost_amount_recipients, 0.0) + coalesce(in_receive_return_cost_amount, 0.0) + coalesce(allot_out_cost_amount, 0.0) + coalesce(out_cost_amount_breakage, 0.0) + coalesce(out_department_consumption_cost_amount, 0.0) + coalesce(out_other_cost_amount, 0.0) + coalesce(check_out_cost_amount, 0.0)+ coalesce(out_production_cost_amount, 0.0) + coalesce(delivery_out_cost_amount, 0.0) + coalesce(delivery_return_in_cost_amount, 0.0)) AS outTotalCost,
                    SUM(coalesce(out_init_cost_amount_excluding_tax,0.0) + coalesce(out_cost_amount_refund_excluding_tax,0.0) + coalesce(dispense_cost_amount_excluding_tax, 0.0) + coalesce(return_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_cost_amount_excluding_tax, 0.0) + coalesce(allot_out_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_cost_amount_excluding_tax, 0.0) + coalesce(out_other_cost_amount_excluding_tax, 0.0) + coalesce(check_out_cost_amount_excluding_tax, 0.0)+ coalesce(out_production_cost_amount_excluding_tax, 0.0) + coalesce(delivery_out_cost_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_cost_amount_excluding_tax, 0.0)) AS outTotalCostExcludeTax
                </if>
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(coalesce(dispense_count, 0.0) + coalesce(return_count, 0.0) + coalesce(out_count_recipients, 0.0) + coalesce(in_receive_return_count, 0.0) + coalesce(allot_out_count, 0.0) + coalesce(out_allot_inside_count, 0.0) + coalesce(out_count_breakage, 0.0) + coalesce(out_department_consumption_count, 0.0) + coalesce(out_other_count, 0.0) + coalesce(check_out_count, 0.0)+ coalesce(out_production_count, 0.0) + coalesce(delivery_out_count, 0.0) + coalesce(delivery_return_in_count, 0.0)) AS outTotalCount,
                    SUM(coalesce(out_dispense_piece_count, 0.0) + coalesce(in_return_piece_count, 0.0) + coalesce(out_recipients_piece_count, 0.0) + coalesce(in_receive_return_piece_count, 0.0) + coalesce(out_allot_piece_count, 0.0) + coalesce(out_allot_inside_piece_count, 0.0) + coalesce(out_breakage_piece_count, 0.0) + coalesce(out_department_consumption_piece_count, 0.0) + coalesce(out_other_piece_count, 0.0) + coalesce(out_check_piece_count, 0.0)+ coalesce(out_production_piece_count, 0.0) + coalesce(delivery_out_piece_count, 0.0) + coalesce(delivery_return_in_piece_count, 0.0)) AS outTotalConvertPieceCount,
                    SUM(coalesce(dispense_amount, 0.0) + coalesce(return_amount, 0.0) + coalesce(out_amount_recipients, 0.0) + coalesce(in_receive_return_amount, 0.0) + coalesce(allot_out_amount, 0.0) + coalesce(out_allot_inside_amount, 0.0) + coalesce(out_amount_breakage, 0.0) + coalesce(out_department_consumption_amount, 0.0) + coalesce(out_other_amount, 0.0) + coalesce(check_out_amount, 0.0)+ coalesce(out_production_amount, 0.0) + coalesce(delivery_out_amount, 0.0) + coalesce(delivery_return_in_amount, 0.0)) AS outTotalPrice,
                    SUM(coalesce(dispense_amount_excluding_tax, 0.0) + coalesce(return_amount_excluding_tax, 0.0) + coalesce(out_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_amount_excluding_tax, 0.0) + coalesce(allot_out_amount_excluding_tax, 0.0) + coalesce(out_allot_inside_amount_excluding_tax, 0.0) + coalesce(out_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_amount_excluding_tax, 0.0) + coalesce(out_other_amount_excluding_tax, 0.0) + coalesce(check_out_amount_excluding_tax, 0.0)+ coalesce(out_production_amount_excluding_tax, 0.0) + coalesce(delivery_out_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_amount_excluding_tax, 0.0)) AS outTotalPriceExcludeTax,
                    SUM(coalesce(dispense_cost_amount, 0.0) + coalesce(return_cost_amount, 0.0) + coalesce(out_cost_amount_recipients, 0.0) + coalesce(in_receive_return_cost_amount, 0.0) + coalesce(allot_out_cost_amount, 0.0) + coalesce(out_allot_inside_cost_amount, 0.0) + coalesce(out_cost_amount_breakage, 0.0) + coalesce(out_department_consumption_cost_amount, 0.0) + coalesce(out_other_cost_amount, 0.0) + coalesce(check_out_cost_amount, 0.0)+ coalesce(out_production_cost_amount, 0.0) + coalesce(delivery_out_cost_amount, 0.0) + coalesce(delivery_return_in_cost_amount, 0.0)) AS outTotalCost,
                    SUM(coalesce(dispense_cost_amount_excluding_tax, 0.0) + coalesce(return_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_cost_amount_excluding_tax, 0.0) + coalesce(allot_out_cost_amount_excluding_tax, 0.0) + coalesce(out_allot_inside_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_cost_amount_excluding_tax, 0.0) + coalesce(out_other_cost_amount_excluding_tax, 0.0) + coalesce(check_out_cost_amount_excluding_tax, 0.0)+ coalesce(out_production_cost_amount_excluding_tax, 0.0) + coalesce(delivery_out_cost_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_cost_amount_excluding_tax, 0.0)) AS outTotalCostExcludeTax
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(coalesce(dispense_count, 0.0) + coalesce(return_count, 0.0) + coalesce(out_count_recipients, 0.0) + coalesce(in_receive_return_count, 0.0) + coalesce(allot_out_count, 0.0) + coalesce(out_count_breakage, 0.0) + coalesce(out_department_consumption_count, 0.0) + coalesce(out_other_count, 0.0) + coalesce(check_out_count, 0.0)+ coalesce(out_production_count, 0.0) + coalesce(delivery_out_count, 0.0) + coalesce(delivery_return_in_count, 0.0)) AS outTotalCount,
                    SUM(coalesce(out_dispense_piece_count, 0.0) + coalesce(in_return_piece_count, 0.0) + coalesce(out_recipients_piece_count, 0.0) + coalesce(in_receive_return_piece_count, 0.0) + coalesce(out_allot_piece_count, 0.0) + coalesce(out_breakage_piece_count, 0.0) + coalesce(out_department_consumption_piece_count, 0.0) + coalesce(out_other_piece_count, 0.0) + coalesce(out_check_piece_count, 0.0)+ coalesce(out_production_piece_count, 0.0) + coalesce(delivery_out_piece_count, 0.0) + coalesce(delivery_return_in_piece_count, 0.0)) AS outTotalConvertPieceCount,
                    SUM(coalesce(dispense_amount, 0.0) + coalesce(return_amount, 0.0) + coalesce(out_amount_recipients, 0.0) + coalesce(in_receive_return_amount, 0.0) + coalesce(allot_out_amount, 0.0) + coalesce(out_amount_breakage, 0.0) + coalesce(out_department_consumption_amount, 0.0) + coalesce(out_other_amount, 0.0) + coalesce(check_out_amount, 0.0)+ coalesce(out_production_amount, 0.0) + coalesce(delivery_out_amount, 0.0) + coalesce(delivery_return_in_amount, 0.0)) AS outTotalPrice,
                    SUM(coalesce(dispense_amount_excluding_tax, 0.0) + coalesce(return_amount_excluding_tax, 0.0) + coalesce(out_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_amount_excluding_tax, 0.0) + coalesce(allot_out_amount_excluding_tax, 0.0) + coalesce(out_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_amount_excluding_tax, 0.0) + coalesce(out_other_amount_excluding_tax, 0.0) + coalesce(check_out_amount_excluding_tax, 0.0)+ coalesce(out_production_amount_excluding_tax, 0.0) + coalesce(delivery_out_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_amount_excluding_tax, 0.0)) AS outTotalPriceExcludeTax,
                    SUM(coalesce(dispense_cost_amount, 0.0) + coalesce(return_cost_amount, 0.0) + coalesce(out_cost_amount_recipients, 0.0) + coalesce(in_receive_return_cost_amount, 0.0) + coalesce(allot_out_cost_amount, 0.0) + coalesce(out_cost_amount_breakage, 0.0) + coalesce(out_department_consumption_cost_amount, 0.0) + coalesce(out_other_cost_amount, 0.0) + coalesce(check_out_cost_amount, 0.0)+ coalesce(out_production_cost_amount, 0.0) + coalesce(delivery_out_cost_amount, 0.0) + coalesce(delivery_return_in_cost_amount, 0.0)) AS outTotalCost,
                    SUM(coalesce(dispense_cost_amount_excluding_tax, 0.0) + coalesce(return_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_recipients_excluding_tax, 0.0) + coalesce(in_receive_return_cost_amount_excluding_tax, 0.0) + coalesce(allot_out_cost_amount_excluding_tax, 0.0) + coalesce(out_cost_amount_breakage_excluding_tax, 0.0) + coalesce(out_department_consumption_cost_amount_excluding_tax, 0.0) + coalesce(out_other_cost_amount_excluding_tax, 0.0) + coalesce(check_out_cost_amount_excluding_tax, 0.0)+ coalesce(out_production_cost_amount_excluding_tax, 0.0) + coalesce(delivery_out_cost_amount_excluding_tax, 0.0) + coalesce(delivery_return_in_cost_amount_excluding_tax, 0.0)) AS outTotalCostExcludeTax
                </if>
            </if>

        FROM
            ${cisTable}.dws_inventory_day_stat_pharmacy as didsp
            inner join ${cisTable}.dim_goods as dgoods on didsp.goods_id = dgoods.id
        WHERE
            data_date &gt;= #{param.subBeginDate} AND data_date &lt;= #{param.subEndDate}
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and didsp.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
    </select>


    <!--  单据页签数据-批次维度 -->
    <select id="selectRecord" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryRecord">
        SELECT
        c.createDate,
        c.organId,
        c.action,
        c.goodsId,
        c.feeType1,
        c.feeType2,
        c.supplierId,
        c.operatorId,
        if(b.actionCount = 0, c.inPrice, b.actionCost / b.actionCount) as inPrice,
        if(b.actionCount = 0, c.outPrice, b.actionPrice / b.actionCount) as outPrice,
        b.actionPrice,
        b.actionCost,
        c.beforeCount,
        c.beforePackageCount,
        c.beforePieceCount,
        b.actionCount,
        b.actionConvertPieceCount as actionConvertPieceCount,
        b.actionPackageCount,
        b.actionPieceCount,
        d.afterCount,
        d.afterPackageCount,
        d.afterPieceCount,
        c.patientId,
        c.inOrderModified,
        c.beforePieceNumber,
        c.afterPieceNumber,
        c.comment,
        c.orderId,
        c.orderNo,
        c.scene,
        c.dispenseType,
        c.dispensingMethod,
        c.batId,
        c.pharmacyNo,
        c.profitCategoryTypeId,
        c.stockId,
        c.cooperationClinicId,
        b.ids,
        c.batchId,
        c.patientOrderId
        from
        (SELECT
            inventory.chain_id,
            organ_id,
            goods_id,
            bat_id,
            action,
            MIN(inventory.id) as minId,
            max(inventory.id) as maxId,
            STRING_AGG(CAST(inventory.id AS VARCHAR), ',') as ids,
            sum(action_count) as actionCount,
            sum(action_package_count * inventory.piece_num + action_piece_count) as actionConvertPieceCount,
            sum(action_package_count) as actionPackageCount,
            sum(action_piece_count) as actionPieceCount,
            sum(case when action in ('发药', '修正发药', '退药', '修正退药')
            then  case when  action in ('发药', '修正发药') then abs(if(origin_flat_price is not null,origin_flat_price,origin_stock_price))*-1
            else abs(if(origin_flat_price is not null,origin_flat_price,origin_stock_price))
            end
            else  action_total_price end) as actionPrice,
            sum(action_total_cost) as actionCost
        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="actionSql != null and actionSql != ''">
                ${actionSql}
            </if>
            <if test="param.stockIds != null and param.stockIds.size > 0">
                and stock_id in
                <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.batchIds != null and param.batchIds.size > 0">
                and batch_id in
                <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!='' ">
                and supplier_id = #{param.supplierId}
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
            <if test="param.sceneIds != null and param.sceneIds.size > 0">
                and scene in
                <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                    #{scene}
                </foreach>
            </if>
            <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                and cooperation_clinic_id = #{param.cooperationClinicId}
            </if>
        GROUP BY
            inventory.chain_id,organ_id,goods_id,bat_id,batch_id,action,from_organ_id,to_organ_id,out_pharmacy_no,in_pharmacy_no,create_date
        order by
            create_date desc,maxId desc
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
        ) as b
        INNER JOIN
        (
        SELECT
        *
        from(
        SELECT
            inventory.chain_id,
            inventory.id,
            bat_id as batId,
            create_date as createDate,
            organ_id as organId,
            action as action,
            goods_id as goodsId,
            dgoods.classify_level_1 as feeType1,
            dgoods.classify_level_2 as feeType2,
            supplier_id as supplierId,
            created_user_id as operatorId,
            inventory.package_cost_price AS inPrice,
            inventory.package_price AS outPrice,
            before_count as beforeCount,
            before_package_count as beforePackageCount,
            before_piece_count as beforePieceCount,
            dispensing_sheet_patient_id as patientId,
            if(action = '采购入库', if(stock_in_count != action_count or stock_in_cost != action_total_cost, 1, 0), 0) as inOrderModified,
            row_number() OVER (PARTITION BY bat_id,batch_id,action,from_organ_id,to_organ_id,out_pharmacy_no,in_pharmacy_no ORDER BY inventory.id) as sort_num,
            before_piece_num as beforePieceNumber,
            inventory.piece_num as afterPieceNumber,
            comment as comment,
            order_id as orderId,
            order_no as orderNo,
            dispense_type as dispenseType,
            scene as scene,
            dispensing_method as dispensingMethod,
            pharmacy_no as pharmacyNo,
            dgoods.profit_category_type as profitCategoryTypeId,
            stock_id as stockId,
            cooperation_clinic_id as cooperationClinicId,
            batch_id as batchId,
            patient_order_id as patientOrderId
        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="actionSql != null and actionSql != ''">
                ${actionSql}
            </if>
            <if test="param.stockIds != null and param.stockIds.size > 0">
                and stock_id in
                <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.batchIds != null and param.batchIds.size > 0">
                and batch_id in
                <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!='' ">
                and supplier_id = #{param.supplierId}
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
            <if test="param.sceneIds != null and param.sceneIds.size > 0">
                and scene in
                <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                    #{scene}
                </foreach>
            </if>
            <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                and cooperation_clinic_id = #{param.cooperationClinicId}
            </if>
        ) as a
        where sort_num = 1
        ORDER BY id desc
        ) as c
        on b.minId = c.id
        inner join
        (SELECT
        *
        from(
        SELECT
            inventory.id,
            inventory.chain_id,
            bat_id,
            organ_id as organId,
            goods_id as goodsId,
            after_count as afterCount,
            after_package_count as afterPackageCount,
            after_piece_count as afterPieceCount,
            row_number() OVER(PARTITION BY bat_id,batch_id,action,from_organ_id,to_organ_id,out_pharmacy_no,in_pharmacy_no ORDER BY inventory.id desc) as sort_num
        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="actionSql != null and actionSql != ''">
                ${actionSql}
            </if>
            <if test="param.stockIds != null and param.stockIds.size > 0">
                and stock_id in
                <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.batchIds != null and param.batchIds.size > 0">
                and batch_id in
                <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!='' ">
                and supplier_id = #{param.supplierId}
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
            <if test="param.sceneIds != null and param.sceneIds.size > 0">
                and scene in
                <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                    #{scene}
                </foreach>
            </if>
            <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                and cooperation_clinic_id = #{param.cooperationClinicId}
            </if>
        ) as a
        where sort_num = 1
        ) d
        on b.maxId = d.id
        order by
            c.createDate desc,b.maxId desc
    </select>

    <!--  单据页签数据汇总   -->
    <select id="selectRecordSummary" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryRecord">
        SELECT
            '合计' as createDate,
            sum(case when action in ('发药','退药')
            then  case when  action in ('发药') then abs(if(origin_flat_price is not null,origin_flat_price,origin_stock_price))*-1
            else abs(if(origin_flat_price is not null,origin_flat_price,origin_stock_price))
            end
            else  action_total_price end)  as actionPrice,
            -- 变更总进价
            sum(action_total_cost) as actionCost,
            sum(action_count) as actionCount,
            sum(action_package_count * inventory.piece_num + action_piece_count) as actionConvertPieceCount,
            sum(action_package_count) as actionPackageCount,
            sum(action_piece_count) as actionPieceCount,
            sum(before_count) as beforeCount,
            sum(before_package_count) as beforePackageCount,
            sum(before_piece_count) as beforePieceCount,
            sum(after_count) as afterCount,
            sum(after_package_count) as afterPackageCount,
            sum(after_piece_count) as afterPieceCount
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where 1=1
            and not (action_count = 0.0  and action_total_cost = 0.0)
            and action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="actionSql != null and actionSql != ''">
                ${actionSql}
            </if>
            <if test="param.stockIds != null and param.stockIds.size > 0">
                and stock_id in
                <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.batchIds != null and param.batchIds.size > 0">
                and batch_id in
                <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                    #{ac}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!=''">
                and supplier_id = #{param.supplierId}
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and dgoods.profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
            <if test="param.sceneIds != null and param.sceneIds.size > 0">
                and scene in
                <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                    #{scene}
                </foreach>
            </if>
            <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                and cooperation_clinic_id = #{param.cooperationClinicId}
            </if>
    </select>

    <!--  单据页签数据-药品维度 记录数  -->
    <select id="selectRecordByGoodsTotal" resultType="java.lang.Long">
        SELECT
            count(*) as cnt
        from(
            SELECT
                inventory.chain_id,
                organ_id,
                bat_id,
                inventory.id,
                row_number() OVER(PARTITION BY goods_id,bat_id,action ORDER BY inventory.id) as sort_num
            from
                ${cisTable}.dwd_goods_inventory as inventory
                inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
            where
                action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.pharmacyType != null">
                    and pharmacy_type=#{param.pharmacyType}
                </if>
                <if test="param.pharmacyNo != null">
                    and pharmacy_no = #{param.pharmacyNo}
                </if>
                <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                    and ${param.pharmacyNosSql}
                </if>
                <if test="param.pharmacyType == 2 ">
                    and dgoods.classify_level_1 in ('1-12','1-13')
                </if>
                and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
                <if test="param.chainId != null and param.chainId != ''">
                    and inventory.chain_id=#{param.chainId}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id=#{param.clinicId}
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                    and (${param.goodsFeeType1} or ${param.goodsFeeType2})
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                    and ${param.goodsFeeType1}
                </if>
                <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                    and ${param.goodsFeeType2}
                </if>
                <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                    and dgoods.fee_type_id in
                    <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="goodsIds != null and goodsIds.size > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="actionSql != null and actionSql != ''">
                    ${actionSql}
                </if>
                <if test="param.stockIds != null and param.stockIds.size > 0">
                    and stock_id in
                    <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.batchIds != null and param.batchIds.size > 0">
                    and batch_id in
                    <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.supplierId != null and param.supplierId!='' ">
                    and supplier_id = #{param.supplierId}
                </if>
                <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                    ${param.dimGoodsBaseMedicineTypeSql}
                </if>
                <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                    and dgoods.profit_category_type in
                    <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                        #{profitCategoryTypeId}
                    </foreach>
                </if>
                <if test="param.sceneIds != null and param.sceneIds.size > 0">
                    and scene in
                    <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                        #{scene}
                    </foreach>
                </if>
                <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                    and cooperation_clinic_id = #{param.cooperationClinicId}
                </if>
        ) as a
        where sort_num = 1
    </select>

    <!--  单据页签数据-批次维度 记录数  -->
    <select id="selectRecordTotal" resultType="java.lang.Long">
        SELECT
            count(*) as cnt
        from(
            SELECT
                inventory.chain_id,
                inventory.id,
                bat_id,
                create_date as createDate,
                organ_id as organId,
                action as action,
                goods_id as goodsId,
                dgoods.classify_level_1 as feeType1,
                dgoods.classify_level_2 as feeType2,
                supplier_id as supplierId,
                expiry_date as expiryDate,
                created_user_id as operatorId,
                inventory.package_cost_price AS inPrice,
                inventory.package_price AS outPrice,
                before_count as beforeCount,
                before_package_count as beforePackageCount,
                before_piece_count as beforePieceCount,
                dispensing_sheet_patient_id as patientId,
                if(action = '采购入库', if(stock_in_count != action_count or stock_in_cost != action_total_cost, 1, 0), 0) as inOrderModified,
                row_number() OVER (PARTITION BY bat_id,batch_id,action,from_organ_id,to_organ_id,out_pharmacy_no,in_pharmacy_no ORDER BY inventory.id) as sort_num,
                before_piece_num as beforePieceNumber,
                inventory.piece_num as afterPieceNumber,
                comment as comment,
                order_id as orderId,
                dispense_type as dispenseType,
                scene as scene,
                dispensing_method as dispensingMethod
            from
                ${cisTable}.dwd_goods_inventory as inventory
                inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
            where
                action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.pharmacyType != null">
                    and pharmacy_type=#{param.pharmacyType}
                </if>
                <if test="param.pharmacyNo != null">
                    and pharmacy_no = #{param.pharmacyNo}
                </if>
                <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                    and ${param.pharmacyNosSql}
                </if>
                <if test="param.pharmacyType == 2 ">
                    and dgoods.classify_level_1 in ('1-12','1-13')
                </if>
                and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
                <if test="param.chainId != null and param.chainId != ''">
                    and inventory.chain_id=#{param.chainId}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id=#{param.clinicId}
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                    and (${param.goodsFeeType1} or ${param.goodsFeeType2})
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                    and ${param.goodsFeeType1}
                </if>
                <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                    and ${param.goodsFeeType2}
                </if>
                <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                    and dgoods.fee_type_id in
                    <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="goodsIds != null and goodsIds.size > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="actionSql != null and actionSql != ''">
                    ${actionSql}
                </if>
                <if test="param.stockIds != null and param.stockIds.size > 0">
                    and stock_id in
                    <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.batchIds != null and param.batchIds.size > 0">
                    and batch_id in
                    <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.supplierId != null and param.supplierId!='' ">
                    and supplier_id = #{param.supplierId}
                </if>
                <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                    ${param.dimGoodsBaseMedicineTypeSql}
                </if>
                <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                    and dgoods.profit_category_type in
                    <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                        #{profitCategoryTypeId}
                    </foreach>
                </if>
                <if test="param.sceneIds != null and param.sceneIds.size > 0">
                    and scene in
                    <foreach collection="param.sceneIds" item="scene" open="(" separator="," close=")">
                        #{scene}
                    </foreach>
                </if>
                <if test="param.cooperationClinicId != null and param.cooperationClinicId != ''">
                    and cooperation_clinic_id = #{param.cooperationClinicId}
                </if>
        ) as a
        where sort_num = 1
    </select>


    <select id="selectGoodsTaxDetail" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.GoodsTaxDetail">
        SELECT
        goods_id as goodsId,
        <if test="param.taxType == 'inTax' ">
            inventory.in_tax_rat as tax,
            '进项税' as taxName,
            <if test="param.actionType == 'in' ">
              '入库成本' as actionName,
              SUM(IF((action IN('采购入库' ) and (supplier_name !='盘点入库' or supplier_name is null)) or action IN('修正入库','药品规格修改'), action_total_cost_exclude_tax,0.0)) AS excludeTax,
            </if>
            <if test="param.actionType == 'dispense' ">
              '发药成本' as actionName,
              abs(SUM(IF(action IN ('发药'), action_total_cost_exclude_tax,0.0))) AS excludeTax,
            </if>

            <if test="param.actionType == 'return' ">
              '退药成本' as actionName,
              SUM(IF(action IN ('退药'), action_total_cost_exclude_tax,0.0)) AS excludeTax,
            </if>

            <if test="param.actionType == 'out' ">
              '出库成本' as actionName,
            abs(SUM(IF(action IN('报损出库','科室出库','退货出库'),action_total_cost_exclude_tax,0.0))) AS excludeTax,
            </if>

            <if test="param.actionType == 'allot' ">
              '调拨成本' as actionName,
            SUM(IF(action IN('调拨入库','调拨出库'), action_total_cost_exclude_tax,0.0)) AS excludeTax,
            </if>

            <if test="param.actionType = 'check' ">
              '盘点成本' as actionName,
            SUM(IF(action IN('盘亏出库','盘盈入库','盘点入库') or (action IN('采购入库' ) and supplier_name='盘点入库'), action_total_cost_exclude_tax,0.0)) AS excludeTax,
            </if>
        </if>
        <if test="param.taxType == 'outTax' ">
            inventory.out_tax_rat as tax,
            '销项税' as taxName,
            <if test="param.actionType == 'dispense' ">
              '发药销售' as actionName,
            abs(SUM(IF(action IN ('发药'), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0))) AS excludeTax,
            </if>

            <if test="param.actionType == 'return' ">
              '退药销售' as actionName,
            SUM(IF(action IN ('退药'), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS excludeTax,
            </if>

            <if test="param.actionType == 'check' ">
              '盘点销售' as actionName,
            SUM(IF(action IN('盘亏出库','盘盈入库','盘点入库') or (action IN('采购入库' ) and supplier_name='盘点入库'), action_total_price_exclude_tax,0.0)) AS excludeTax,
            </if>
        </if>
        substr(min(create_date),1,10) as beginDate,
        substr(max(create_date),1,10) as endDate
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            and inventory.chain_id=#{chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsId != null and param.goodsId != '' ">
                and goods_id  = #{param.goodsId}
            </if>
        GROUP BY goods_id,
        <if test="param.taxType == 'inTax' ">
            inventory.in_tax_rat
        </if>
        <if test="param.taxType == 'outTax' ">
            inventory.out_tax_rat
        </if>
    </select>

    <select id="selectGoodsTaxDis" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.GoodsTaxDetail">
        SELECT
        goods_id as goodsId,
        <if test="param.taxType == 'inTax' ">
            '进项税' as taxName,
            inventory.in_tax_rat as tax,
        </if>
        <if test="param.taxType == 'outTax' ">
            '销项税' as taxName,
            inventory.out_tax_rat as tax,
        </if>
        substr(min(create_date),1,10) as beginDate,
        substr(max(create_date),1,10) as endDate
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            and inventory.chain_id=#{param.chainId}

            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsId != null and param.goodsId != '' ">
                and goods_id  = #{param.goodsId}
            </if>
        GROUP BY goods_id,
        <if test="param.taxType == 'inTax' ">
            inventory.in_tax_rat
        </if>
        <if test="param.taxType == 'outTax' ">
            inventory.out_tax_rat
        </if>
    </select>



    <select id="selectSpecificationModificationCount" resultType="java.lang.Integer">
        select
            COALESCE(count(1),0)
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            and action = '药品规格修改'
            and action_count != 0.0
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
    </select>

    <!--  药品页签数据   -->
    <select id="selectPackageAndPieceStockSummary" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">

        SELECT
        d.goodsId,
        d.classifyLevel1 as feeType1,
        d.classifyLevel2 as feeType2,
        d.feeTypeId as feeTypeId,

        coalesce(g.endCount,0.0) as beginCount,
        coalesce(g.endConvertPieceCount,0.0) as beginConvertPieceCount,
        coalesce(g.endPackageCount,0.0) as beginPackageCount,
        coalesce(g.endPieceCount,0.0) as beginPieceCount,
        coalesce(g.endCost,0.0) as beginCost,
        coalesce(g.endCostExcludeTax,0.0) as beginCostExcludeTax,

        d.endCount as endCount,
        d.endConvertPieceCount as endConvertPieceCount,
        d.endPackageCount as endPackageCount,
        d.endPieceCount as endPieceCount,
        d.endCost as endCost,
        d.endCostExcludeTax as endCostExcludeTax,

        b.inInitCount,
        b.inInitConvertPieceCount,
        b.inInitPackageCount,
        b.inInitPieceCount,
        b.inInitCost,
        b.inInitCostExcludeTax,
        b.inInitPrice,
        b.inInitPriceExcludeTax,

        b.purchaseInCount,
        b.purchaseInConvertPieceCount,
        b.purchaseInPackageCount,
        b.purchaseInPieceCount,
        b.purchaseInCost,
        b.purchaseInCostExcludeTax,
        b.purchaseInPrice,
        b.purchaseInPriceExcludeTax,

        b.inReceiveCount,
        b.inReceiveConvertPieceCount,
        b.inReceivePackageCount,
        b.inReceivePieceCount,
        b.inReceiveCostAmount,
        b.inReceiveCostAmountExcludingTax,
        b.inReceiveAmount,
        b.inReceiveAmountExcludingTax,

        b.allotInCount,
        b.allotInConvertPieceCount,
        b.allotInPackageCount,
        b.allotInPieceCount,
        b.allotInCost,
        b.allotInCostExcludeTax,
        b.allotInPrice,
        b.allotInPriceExcludeTax,

        b.allotInInsideCount,
        b.allotInInsideConvertPieceCount,
        b.allotInInsidePackageCount,
        b.allotInInsidePieceCount,
        b.allotInInsideCost,
        b.allotInInsideCostExcludeTax,
        b.allotInInsidePrice,
        b.allotInInsidePriceExcludeTax,

        b.checkInCount,
        b.checkInConvertPieceCount,
        b.checkInPackageCount,
        b.checkInPieceCount,
        b.checkInCost,
        b.checkInCostExcludeTax,
        b.checkInPrice,
        b.checkInPriceExcludeTax,

        b.inSpecificationModificationCount,
        b.inSpecificationModificationConvertPieceCount,
        b.inSpecificationModificationPackageCount,
        b.inSpecificationModificationPieceCount,
        b.inSpecificationModificationCost,
        b.inSpecificationModificationCostExcludeTax,
        b.inSpecificationModificationPrice,
        b.inSpecificationModificationPriceExcludeTax,

        b.inTotalCount,
        b.inTotalConvertPieceCount,
        b.inTotalPackageCount,
        b.inTotalPieceCount,
        b.inTotalCost,
        b.inTotalCostExcludeTax,
        b.inTotalPrice,
        b.inTotalPriceExcludeTax,

        b.inInitReturnCount,
        b.inInitReturnConvertPieceCount,
        b.inInitReturnPrice,
        b.inInitReturnPriceExcludeTax,
        b.inInitReturnCost,
        b.inInitReturnCostExcludeTax,

        b.returnGoodsOutCount,
        b.returnGoodsOutConvertPieceCount,
        b.returnGoodsOutPrice,
        b.returnGoodsOutPriceExcludeTax,
        b.returnGoodsOutCost,
        b.returnGoodsOutCostExcludeTax,

        b.outPatientDispenseCount,
        b.outPatientDispenseConvertPieceCount,
        b.outPatientDispensePackageCount,
        b.outPatientDispensePieceCount,
        b.outPatientDispenseCost,
        b.outPatientDispenseCostExcludeTax,
        b.outPatientDispensePrice,
        b.outPatientDispensePriceExcludeTax,

        b.hospitalPharmacyDispenseCount,
        b.hospitalPharmacyDispenseConvertPieceCount,
        b.hospitalPharmacyDispensePackageCount,
        b.hospitalPharmacyDispensePieceCount,
        b.hospitalPharmacyDispenseCost,
        b.hospitalPharmacyDispenseCostExcludeTax,
        b.hospitalPharmacyDispensePrice,
        b.hospitalPharmacyDispensePriceExcludeTax,

        b.hospitalAutomaticDispenseCount,
        b.hospitalAutomaticDispenseConvertPieceCount,
        b.hospitalAutomaticDispensePackageCount,
        b.hospitalAutomaticDispensePieceCount,
        b.hospitalAutomaticDispenseCost,
        b.hospitalAutomaticDispenseCostExcludeTax,
        b.hospitalAutomaticDispensePrice,
        b.hospitalAutomaticDispensePriceExcludeTax,

        b.hospitalNoSettleDispenseCount,
        b.hospitalNoSettleDispenseConvertPieceCount,
        b.hospitalNoSettleDispensePackageCount,
        b.hospitalNoSettleDispensePieceCount,
        b.hospitalNoSettleDispenseCost,
        b.hospitalNoSettleDispenseCostExcludeTax,
        b.hospitalNoSettleDispensePrice,
        b.hospitalNoSettleDispensePriceExcludeTax,

        b.dispenseCount,
        b.dispenseConvertPieceCount,
        b.dispensePackageCount,
        b.dispensePieceCount,
        b.dispenseCost,
        b.dispenseCostExcludeTax,
        b.dispensePrice,
        b.dispensePriceExcludeTax,

        b.collectOutCount,
        b.collectOutConvertPieceCount,
        b.collectOutPackageCount,
        b.collectOutPieceCount,
        b.collectOutCost,
        b.collectOutCostExcludeTax,
        b.collectOutPrice,
        b.collectOutPriceExcludeTax,

        b.outDepartmentConsumptionCount,
        b.outDepartmentConsumptionConvertPieceCount,
        b.outDepartmentConsumptionPackageCount,
        b.outDepartmentConsumptionPieceCount,
        b.outDepartmentConsumptionCostAmount,
        b.outDepartmentConsumptionCostAmountExcludingTax,
        b.outDepartmentConsumptionAmount,
        b.outDepartmentConsumptionAmountExcludingTax,

        b.allotOutCount,
        b.allotOutConvertPieceCount,
        b.allotOutPackageCount,
        b.allotOutPieceCount,
        b.allotOutCost,
        b.allotOutCostExcludeTax,
        b.allotOutPrice,
        b.allotOutPriceExcludeTax,

        b.allotOutInsideCount,
        b.allotOutInsideConvertPieceCount,
        b.allotOutInsidePackageCount,
        b.allotOutInsidePieceCount,
        b.allotOutInsideCost,
        b.allotOutInsideCostExcludeTax,
        b.allotOutInsidePrice,
        b.allotOutInsidePriceExcludeTax,

        b.damagedOutCount,
        b.damagedOutConvertPieceCount,
        b.damagedOutPackageCount,
        b.damagedOutPieceCount,
        b.damagedOutCost,
        b.damagedOutCostExcludeTax,
        b.damagedOutPrice,
        b.damagedOutPriceExcludeTax,

        b.outOtherCount,
        b.outOtherConvertPieceCount,
        b.outOtherPackageCount,
        b.outOtherPieceCount,
        b.outOtherCostAmount,
        b.outOtherCostAmountExcludingTax,
        b.outOtherAmount,
        b.outOtherAmountExcludingTax,

        b.checkOutCount,
        b.checkOutConvertPieceCount,
        b.checkOutPackageCount,
        b.checkOutPieceCount,
        b.checkOutCost,
        b.checkOutCostExcludeTax,
        b.checkOutPrice,
        b.checkOutPriceExcludeTax,

        b.productionOutCount,
        b.productionOutConvertPieceCount,
        b.productionOutPackageCount,
        b.productionOutPieceCount,
        b.productionOutCost,
        b.productionOutCostExcludeTax,
        b.productionOutPrice,
        b.productionOutPriceExcludeTax,

        b.deliveryOutCount,
        b.deliveryOutConvertPieceCount,
        b.deliveryOutPackageCount,
        b.deliveryOutPieceCount,
        b.deliveryOutCost,
        b.deliveryOutCostExcludeTax,
        b.deliveryOutPrice,
        b.deliveryOutPriceExcludeTax,

        b.outTotalCount,
        b.outTotalConvertPieceCount,
        b.outTotalPackageCount,
        b.outTotalPieceCount,
        b.outTotalCost,
        b.outTotalCostExcludeTax,
        b.outTotalPrice,
        b.outTotalPriceExcludeTax,

        if(c.cnt is null or c.cnt = 1 ,false,true) as inTaxChanged,
        if(c.cnt is null or c.cnt = 1 ,false,true) as outTaxChanged

        FROM (
        SELECT a.goodsId,
        a.classifyLevel1,
        a.classifyLevel2,
        a.feeTypeId,
        SUM(a.endCount) as endCount,
        SUM(a.endConvertPieceCount) as endConvertPieceCount,
        SUM(a.endPackageCount) as endPackageCount,
        SUM(a.endPieceCount) as endPieceCount,
        SUM(a.endCost) as endCost,
        SUM(a.endCostExcludeTax) as endCostExcludeTax
        FROM(
        SELECT
        goods_id as goodsId,
        max(dgoods.classify_level_1) as classifyLevel1,
        max(dgoods.classify_level_2) as classifyLevel2,
        max(dgoods.fee_type_id) as feeTypeId
        ,sum(goods_pharmacy_after_count) AS endCount
        ,sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) AS endConvertPieceCount
        ,sum(goods_pharmacy_after_package_count) AS endPackageCount
        ,sum(goods_pharmacy_after_piece_count) AS endPieceCount
        ,sum(goods_pharmacy_after_total_cost) AS endCost
        ,sum(goods_pharmacy_after_total_cost_exclude_tax) AS endCostExcludeTax
        FROM
            ${cisTable}.${tempTable.dwdEndTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            1=1
            and create_date = CAST(#{tempTable.endDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        GROUP BY
            goods_id
        <if test="param.limit != null and param.limit != ''">
            order by goods_id desc
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
        ) a
        GROUP BY   a.goodsId,
        a.classifyLevel1,
        a.classifyLevel2,
        a.feeTypeId
        )d
        LEFT JOIN
        (
        SELECT e.goodsId,
        e.classifyLevel1,
        e.classifyLevel2,
        e.feeTypeId,
        SUM(e.endCount) as endCount,
        SUM(e.endConvertPieceCount) as endConvertPieceCount,
        SUM(e.endPackageCount) as endPackageCount,
        SUM(e.endPieceCount) as endPieceCount,
        SUM(e.endCost) as endCost,
        SUM(e.endCostExcludeTax) as endCostExcludeTax
        FROM(
        SELECT
        goods_id as goodsId,
        max(dgoods.classify_level_1) as classifyLevel1,
        max(dgoods.classify_level_2) as classifyLevel2,
        max(dgoods.fee_type_id) as feeTypeId
        ,sum(goods_pharmacy_after_count) AS endCount
        ,sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) AS endConvertPieceCount
        ,sum(goods_pharmacy_after_package_count) AS endPackageCount
        ,sum(goods_pharmacy_after_piece_count) AS endPieceCount
        ,sum(goods_pharmacy_after_total_cost) AS endCost
        ,sum(goods_pharmacy_after_total_cost_exclude_tax) AS endCostExcludeTax
        FROM
            ${cisTable}.${tempTable.dwdBeginTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            1=1
            and create_date = CAST(#{tempTable.beginDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        GROUP BY
            goods_id
        <if test="param.limit != null and param.limit != ''">
            order by goods_id desc
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
        ) e

        GROUP BY   e.goodsId,
        e.classifyLevel1,
        e.classifyLevel2,
        e.feeTypeId
        )g on d.goodsId = g.goodsId

        LEFT JOIN (
        SELECT
            goods_id        as goodsId,

            <if test="param.hisType != null and param.hisType == 10">
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_package_count, 0.0)) AS inInitPackageCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_piece_count, 0.0)) AS inInitPieceCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_package_count, 0.0)) AS purchaseInPackageCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_piece_count, 0.0)) AS purchaseInPieceCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_package_count, 0.0)) AS inInitPackageCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_piece_count, 0.0)) AS inInitPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_package_count, 0.0)) AS purchaseInPackageCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_piece_count, 0.0)) AS purchaseInPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>

            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_count, 0.0)) AS inReceiveCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inReceiveConvertPieceCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_package_count, 0.0)) AS inReceivePackageCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_piece_count, 0.0)) AS inReceivePieceCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price, 0.0)) AS inReceiveAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price_exclude_tax, 0.0)) AS inReceiveAmountExcludingTax,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost, 0.0)) AS inReceiveCostAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost_exclude_tax, 0.0)) AS inReceiveCostAmountExcludingTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotInCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_package_count, 0.0)) AS allotInPackageCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_piece_count, 0.0)) AS allotInPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price, 0.0)) AS allotInPrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInPriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotInCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInCostExcludeTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotInInsideCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInInsideConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_package_count, 0.0)) AS allotInInsidePackageCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_piece_count, 0.0)) AS allotInInsidePieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price, 0.0)) AS allotInInsidePrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInInsidePriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotInInsideCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInInsideCostExcludeTax,

            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS checkInCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkInConvertPieceCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count, 0.0)) AS checkInPackageCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_piece_count, 0.0)) AS checkInPieceCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS checkInPrice,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS checkInPriceExcludeTax,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS checkInCost,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS checkInCostExcludeTax,

            SUM(IF(action IN('药品规格修改'), action_count, 0.0)) AS inSpecificationModificationCount,
            SUM(IF(action IN('药品规格修改'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inSpecificationModificationConvertPieceCount,
            SUM(IF(action IN('药品规格修改'), action_package_count, 0.0)) AS inSpecificationModificationPackageCount,
            SUM(IF(action IN('药品规格修改'), action_piece_count, 0.0)) AS inSpecificationModificationPieceCount,
            SUM(IF(action IN('药品规格修改'), action_total_price, 0.0)) AS inSpecificationModificationPrice,
            SUM(IF(action IN('药品规格修改'), action_total_price_exclude_tax, 0.0)) AS inSpecificationModificationPriceExcludeTax,
            SUM(IF(action IN('药品规格修改'), action_total_cost, 0.0)) AS inSpecificationModificationCost,
            SUM(IF(action IN('药品规格修改'), action_total_cost_exclude_tax, 0.0)) AS inSpecificationModificationCostExcludeTax,

            <if test="param.hisType != null and param.hisType == 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count, 0.0)) AS inTotalPackageCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_piece_count, 0.0)) AS inTotalPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count, 0.0)) AS inTotalPackageCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_piece_count, 0.0)) AS inTotalPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count, 0.0)) AS inTotalPackageCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_piece_count, 0.0)) AS inTotalPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count, 0.0)) AS inTotalPackageCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_piece_count, 0.0)) AS inTotalPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>


            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_count, 0.0)) AS inInitReturnCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitReturnConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_package_count, 0.0)) AS inInitReturnPackageCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_piece_count, 0.0)) AS inInitReturnPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS inInitReturnPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS inInitReturnPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost, 0.0)) AS inInitReturnCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost_exclude_tax, 0.0)) AS inInitReturnCostExcludeTax,

            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_count, 0.0)) AS returnGoodsOutCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS returnGoodsOutConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_package_count, 0.0)) AS returnGoodsOutPackageCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_piece_count, 0.0)) AS returnGoodsOutPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS returnGoodsOutPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS returnGoodsOutPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost, 0.0)) AS returnGoodsOutCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS returnGoodsOutCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_count, 0.0)) AS outPatientDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outPatientDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_package_count, 0.0)) AS outPatientDispensePackageCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_piece_count, 0.0)) AS outPatientDispensePieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS outPatientDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS outPatientDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost, 0.0)) AS outPatientDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost_exclude_tax, 0.0)) AS outPatientDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_count,0.0)) AS hospitalPharmacyDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalPharmacyDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_package_count,0.0)) AS hospitalPharmacyDispensePackageCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_piece_count,0.0)) AS hospitalPharmacyDispensePieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalPharmacyDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalPharmacyDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost,0.0)) AS hospitalPharmacyDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalPharmacyDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_count,0.0)) AS hospitalAutomaticDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalAutomaticDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_package_count,0.0)) AS hospitalAutomaticDispensePackageCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_piece_count,0.0)) AS hospitalAutomaticDispensePieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalAutomaticDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalAutomaticDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost,0.0)) AS hospitalAutomaticDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalAutomaticDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_count,0.0)) AS hospitalNoSettleDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalNoSettleDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_package_count,0.0)) AS hospitalNoSettleDispensePackageCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_piece_count,0.0)) AS hospitalNoSettleDispensePieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalNoSettleDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalNoSettleDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost,0.0)) AS hospitalNoSettleDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost_exclude_tax,0.0)) AS hospitalNoSettleDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_count, 0.0)) AS dispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS dispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_package_count, 0.0)) AS dispensePackageCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_piece_count, 0.0)) AS dispensePieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS dispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS dispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost, 0.0)) AS dispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost_exclude_tax, 0.0)) AS dispenseCostExcludeTax,

            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_count, 0.0)) AS collectOutCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS collectOutConvertPieceCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_package_count, 0.0)) AS collectOutPackageCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_piece_count, 0.0)) AS collectOutPieceCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_total_price, 0.0)) AS collectOutPrice,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_total_price_exclude_tax, 0.0)) AS collectOutPriceExcludeTax,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_total_cost, 0.0)) AS collectOutCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'),action_total_cost_exclude_tax, 0.0)) AS collectOutCostExcludeTax,

            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_count, 0.0)) AS outDepartmentConsumptionCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outDepartmentConsumptionConvertPieceCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_package_count, 0.0)) AS outDepartmentConsumptionPackageCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_piece_count, 0.0)) AS outDepartmentConsumptionPieceCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_total_price, 0.0)) AS outDepartmentConsumptionAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_total_price_exclude_tax, 0.0)) AS outDepartmentConsumptionAmountExcludingTax,
            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_total_cost, 0.0)) AS outDepartmentConsumptionCostAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'),action_total_cost_exclude_tax, 0.0)) AS outDepartmentConsumptionCostAmountExcludingTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotOutCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_package_count, 0.0)) AS allotOutPackageCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_piece_count, 0.0)) AS allotOutPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price, 0.0)) AS allotOutPrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotOutPriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotOutCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutCostExcludeTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotOutInsideCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutInsideConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_package_count, 0.0)) AS allotOutInsidePackageCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_piece_count, 0.0)) AS allotOutInsidePieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price, 0.0)) AS allotOutInsidePrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotOutInsidePriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotOutInsideCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutInsideCostExcludeTax,

            SUM(IF(action IN('报损出库', '修正报损出库'),action_count, 0.0)) AS damagedOutCount,
            SUM(IF(action IN('报损出库', '修正报损出库'),action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS damagedOutConvertPieceCount,
            SUM(IF(action IN('报损出库', '修正报损出库'),action_package_count, 0.0)) AS damagedOutPackageCount,
            SUM(IF(action IN('报损出库', '修正报损出库'),action_piece_count, 0.0)) AS damagedOutPieceCount,
            SUM(IF(action IN('报损出库', '修正报损出库'),action_total_price, 0.0)) AS damagedOutPrice,
            SUM(IF(action IN('报损出库', '修正报损出库'),action_total_price_exclude_tax, 0.0)) AS damagedOutPriceExcludeTax,
            SUM(IF(action IN('报损出库', '修正报损出库'),action_total_cost, 0.0)) AS damagedOutCost,
            SUM(IF(action IN('报损出库', '修正报损出库'),action_total_cost_exclude_tax, 0.0)) AS damagedOutCostExcludeTax,

            SUM(IF(action IN('其他出库', '修正其他出库'),action_count, 0.0)) AS outOtherCount,
            SUM(IF(action IN('其他出库', '修正其他出库'),action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outOtherConvertPieceCount,
            SUM(IF(action IN('其他出库', '修正其他出库'),action_package_count, 0.0)) AS outOtherPackageCount,
            SUM(IF(action IN('其他出库', '修正其他出库'),action_piece_count, 0.0)) AS outOtherPieceCount,
            SUM(IF(action IN('其他出库', '修正其他出库'),action_total_price, 0.0)) AS outOtherAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'),action_total_price_exclude_tax, 0.0)) AS outOtherAmountExcludingTax,
            SUM(IF(action IN('其他出库', '修正其他出库'),action_total_cost, 0.0)) AS outOtherCostAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'),action_total_cost_exclude_tax, 0.0)) AS outOtherCostAmountExcludingTax,

            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_count, 0.0)) AS checkOutCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkOutConvertPieceCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_package_count, 0.0)) AS checkOutPackageCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_piece_count, 0.0)) AS checkOutPieceCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price, 0.0)) AS checkOutPrice,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price_exclude_tax, 0.0)) AS checkOutPriceExcludeTax,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS checkOutCost,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost_exclude_tax, 0.0)) AS checkOutCostExcludeTax,

            SUM(IF(action IN('生产出库', '修正生产出库'), action_count,0.0)) AS productionOutCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_package_count * inventory.piece_num + action_piece_count,0.0)) AS productionOutConvertPieceCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_package_count,0.0)) AS productionOutPackageCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_piece_count,0.0)) AS productionOutPieceCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price,0.0)) AS productionOutPrice,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price_exclude_tax,0.0)) AS productionOutPriceExcludeTax,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost,0.0)) AS productionOutCost,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost_exclude_tax,0.0)) AS productionOutCostExcludeTax,

            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_count,0.0)) AS deliveryOutCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_package_count * inventory.piece_num + action_piece_count,0.0)) AS deliveryOutConvertPieceCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_package_count,0.0)) AS deliveryOutPackageCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_piece_count,0.0)) AS deliveryOutPieceCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price,0.0)) AS deliveryOutPrice,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price_exclude_tax,0.0)) AS deliveryOutPriceExcludeTax,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost,0.0)) AS deliveryOutCost,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost_exclude_tax,0.0)) AS deliveryOutCostExcludeTax,

            <if test="param.hisType != null and param.hisType == 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count,0.0)) AS outTotalPackageCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_piece_count,0.0)) AS outTotalPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count,0.0)) AS outTotalPackageCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_piece_count,0.0)) AS outTotalPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 1">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count,0.0)) AS outTotalPackageCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_piece_count,0.0)) AS outTotalPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="param.isContainsAllotInInside != null and param.isContainsAllotInInside == 0">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count,0.0)) AS outTotalPackageCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_piece_count,0.0)) AS outTotalPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>

        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        GROUP BY goods_id
        ) b on d.goodsId = b.goodsId
        left join (
        select
        goods_id as goodsId,
        count(1) cnt
        from(
        select
        goods_id,
        inventory.in_tax_rat,
        inventory.out_tax_rat
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        GROUP BY goods_id,inventory.in_tax_rat,inventory.out_tax_rat
        ) tmp
        group by goods_id
        ) c
        on d.goodsId = c.goodsId

    </select>

    <select id="selectSpecificationModification"
            resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryRecord">
        select
            min(goods_spec_modify_before_package_count),
            min(goods_spec_modify_before_piece_count),
            min(goods_after_package_count),
            min(goods_after_piece_count),
            min(created_user_id) as createdUserId,
            min(organ_id) as organId,
            create_date as createDate
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            and action = '药品规格修改'
            and action_count != 0.0
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            group by create_date
    </select>

    <select id="selectBeginGoods" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            e.goodsId,
            e.classifyLevel1,
            e.classifyLevel2,
            coalesce(SUM(e.endCount), 0.0) as beginCount,
            coalesce(SUM(e.endConvertPieceCount), 0.0) as beginConvertPieceCount,
            coalesce(SUM(e.endCost), 0.0) as beginCost,
            coalesce(SUM(e.endCostExcludeTax), 0.0) as beginCostExcludeTax
        FROM(
            SELECT
                goods_id as goodsId,
                max(dgoods.classify_level_1) as classifyLevel1,
                max(dgoods.classify_level_2) as classifyLevel2
                ,sum(goods_pharmacy_after_count) AS endCount
                ,sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) AS endConvertPieceCount
                ,sum(goods_pharmacy_after_total_cost) AS endCost
                ,sum(goods_pharmacy_after_total_cost_exclude_tax) AS endCostExcludeTax
            FROM
                ${cisTable}.${tempTable.dwdBeginTable} as dgibe
                inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
            where
                1=1
                and create_date = CAST(#{tempTable.beginDate} AS INTEGER)
                <!--and create_date between #{param.beginDate} and #{param.endDate}-->
                <if test="param.pharmacyType != null">
                    and pharmacy_type= #{param.pharmacyType}
                </if>
                <if test="param.pharmacyNo != null">
                    and pharmacy_no = #{param.pharmacyNo}
                </if>
                <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                    and ${param.pharmacyNosSql}
                </if>
                <if test="param.pharmacyType == 2 ">
                    and dgoods.classify_level_1 in ('1-12','1-13')
                </if>
                and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
                <if test="param.chainId != null and param.chainId != ''">
                    and dgibe.chain_id=#{param.chainId}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id=#{param.clinicId}
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                    and (${param.goodsFeeType1} or ${param.goodsFeeType2})
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                    and ${param.goodsFeeType1}
                </if>
                <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                    and ${param.goodsFeeType2}
                </if>
                <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                    and dgoods.fee_type_id in
                    <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="goodsIds != null and goodsIds.size > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                    ${param.dimGoodsBaseMedicineTypeSql}
                </if>
            GROUP BY
                goods_id
                <if test="param.limit != null and param.limit != ''">
                    order by goods_id desc
                    limit #{param.limit}
                </if>
                <if test="param.offset != null and param.offset != ''">
                    offset #{param.offset}
                </if>
        ) e
        GROUP BY
            e.goodsId,
            e.classifyLevel1,
            e.classifyLevel2

    </select>

    <select id="selectEndGoods" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            a.goodsId,
            a.classifyLevel1 as feeType1,
            a.classifyLevel2 as feeType2,
            a.profitCategoryTypeId as profitCategoryTypeId,
            SUM(a.endCount) as endCount,
            SUM(a.endConvertPieceCount) as endConvertPieceCount,
            SUM(a.endCost) as endCost,
            SUM(a.endCostExcludeTax) as endCostExcludeTax
        FROM(
            SELECT
                goods_id as goodsId,
                max(dgoods.classify_level_1) as classifyLevel1,
                max(dgoods.classify_level_2) as classifyLevel2,
                max(dgoods.profit_category_type) as profitCategoryTypeId,
                sum(goods_pharmacy_after_count) AS endCount,
                sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) AS endConvertPieceCount,
                sum(goods_pharmacy_after_total_cost) AS endCost,
                sum(goods_pharmacy_after_total_cost_exclude_tax) AS endCostExcludeTax
            FROM
                ${cisTable}.${tempTable.dwdEndTable} as dgibe
                inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
            where
                1=1
                and create_date =  CAST(#{tempTable.endDate} AS INTEGER)
                <if test="param.pharmacyType != null">
                    and pharmacy_type=#{param.pharmacyType}
                </if>
                <if test="param.pharmacyNo != null">
                    and pharmacy_no = #{param.pharmacyNo}
                </if>
                <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                    and ${param.pharmacyNosSql}
                </if>
                <if test="param.pharmacyType == 2 ">
                    and dgoods.classify_level_1 in ('1-12','1-13')
                </if>
                and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
                <if test="param.chainId != null and param.chainId != ''">
                    and dgibe.chain_id=#{param.chainId}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id=#{param.clinicId}
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                    and (${param.goodsFeeType1} or ${param.goodsFeeType2})
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                    and ${param.goodsFeeType1}
                </if>
                <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                    and ${param.goodsFeeType2}
                </if>
                <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                    and dgoods.fee_type_id in
                    <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="goodsIds != null and goodsIds.size > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                    ${param.dimGoodsBaseMedicineTypeSql}
                </if>
            GROUP BY
                goods_id
            <if test="param.limit != null and param.limit != ''">
                order by goods_id desc
                limit #{param.limit}
            </if>
            <if test="param.offset != null and param.offset != ''">
                offset #{param.offset}
            </if>
        ) a
        GROUP BY
            a.goodsId,
            a.classifyLevel1,
            a.classifyLevel2,
            a.profitCategoryTypeId

    </select>

    <select id="selectGoodsSummaryBegin" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            coalesce(sum(goods_pharmacy_after_count), 0.0) as  beginCount,
            coalesce(sum(goods_pharmacy_after_total_cost), 0.0) as  beginCost,
            coalesce(sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count),0.0) AS beginConvertPieceCount,
            coalesce(sum(goods_pharmacy_after_total_cost_exclude_tax), 0.0) as  beginCostExcludeTax
        FROM
            ${cisTable}.${tempTable.dwdBeginTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        WHERE
            create_date = CAST(#{tempTable.beginDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
    </select>

    <select id="selectGoodsSummaryEnd" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            sum(goods_pharmacy_after_count) as endCount,
            sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) AS endConvertPieceCount,
            sum(goods_pharmacy_after_total_cost) as endCost,
            sum(goods_pharmacy_after_total_cost_exclude_tax) as endCostExcludeTax
        FROM
            ${cisTable}.${tempTable.dwdEndTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        WHERE
            create_date = CAST(#{tempTable.endDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
    </select>

    <select id="selectTaxRatModify" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            goods_id as goodsId,
            max(before_in_tax_rat)                                       as beforeInTaxRat,
            max(before_out_tax_rat)                                      as beforeOutTaxRat,
            max(in_tax_rat)                                              as inTaxRat,
            max(out_tax_rat)                                             as outTaxRat,
            sum(after_total_cost_modify_exclude_tax)                     as afterTotalCostModifyExcludeTax,
            sum(goods_after_total_cost_modify_exclude_tax)               as goodsAfterTotalCostModifyExcludeTax,
            sum(goods_pharmacy_after_total_cost_modify_exclude_tax)      as goodsPharmacyAfterTotalCostModifyExcludeTax,
            max(effected_time)                                           as effectedTime,
            max(last_modified_by)                                        as lastModifiedBy,
            max(last_modified)                                           as lastModified
        FROM
            ${cisTable}.dwd_goods_inout_tax_rat_modify
        WHERE
            month BETWEEN LEFT(#{param.beginDate}, 7) AND LEFT(#{param.endDate}, 7)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.baseMedicineTypeSql != null and param.baseMedicineTypeSql != ''">
                ${param.baseMedicineTypeSql}
            </if>
            and status = 1
        group by
            goods_id
    </select>

    <select id="selectClassifyTaxRatModify" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        SELECT
            dgoods.classify_level_1 as feeType1,
            max(before_in_tax_rat)                                       as beforeInTaxRat,
            max(before_out_tax_rat)                                      as beforeOutTaxRat,
            max(dgitrm.in_tax_rat)                                       as inTaxRat,
            max(dgitrm.out_tax_rat)                                      as outTaxRat,
            sum(after_total_cost_modify_exclude_tax)                     as afterTotalCostModifyExcludeTax,
            sum(goods_after_total_cost_modify_exclude_tax)               as goodsAfterTotalCostModifyExcludeTax,
            sum(goods_pharmacy_after_total_cost_modify_exclude_tax)      as goodsPharmacyAfterTotalCostModifyExcludeTax,
            max(effected_time)                                           as effectedTime,
            max(last_modified_by)                                        as lastModifiedBy,
            max(last_modified)                                           as lastModified
        FROM
            ${cisTable}.dwd_goods_inout_tax_rat_modify as dgitrm
            inner join ${cisTable}.dim_goods as dgoods on dgitrm.goods_id = dgoods.id
        WHERE
            month BETWEEN LEFT(#{params.beginDate}, 7) AND LEFT(#{params.endDate}, 7)
            and classify_level1 is not null
            <if test="params.pharmacyType != null">
                and pharmacy_type=#{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.chainId != null and params.chainId != ''">
                and dgitrm.chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
            and dgitrm.status = 1
        group by
            dgoods.classify_level_1
    </select>

    <select id="selectStockSummaryTaxRateModifyInfo"
            resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.StockSummaryTaxRateChange">
        select
            last_modified as changeDate,
            before_in_tax_rat as changeBeforeInTax,
            before_out_tax_rat as changeBeforeOutTax,
            in_tax_rat as changeAfterInTax,
            out_tax_rat as changeAfterOutTax,
            goods_after_total_cost_modify_exclude_tax as costTaxAmountCharge,
            effected_time as effectiveTime,
            last_modified_by as operator
        FROM ${cisTable}.dwd_goods_inout_tax_rat_modify
        where
            chain_id = #{param.chainId}
            and last_modified between to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.goodsId != null and param.goodsId != ''">
                and goods_id=#{param.goodsId}
            </if>
        order by
                last_modified desc
    </select>

    <select id="selectTaxModifyCount" resultType="java.lang.Integer">
        select
            COALESCE(count(1),0)
        FROM ${cisTable}.dwd_goods_inout_tax_rat_modify
        <where>
            1=1
            and month &gt;= to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'), 'yyyy-MM')
            and month &lt;= to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'), 'yyyy-MM')
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSummaryTaxRatModify" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods">
        SELECT
            if(max(before_in_tax_rat) is null, 0.0, max(before_in_tax_rat))                                     as beforeInTaxRat,
            if(max(before_out_tax_rat) is null, 0.0, max(before_out_tax_rat))                                   as beforeOutTaxRat,
            if(max(in_tax_rat) is null, 0.0, max(in_tax_rat))                                                   as inTaxRat,
            if(max(out_tax_rat) is null, 0.0, max(out_tax_rat))                                                 as outTaxRat,
            if(sum(after_total_cost_modify_exclude_tax) is null, 0.0,
            sum(after_total_cost_modify_exclude_tax))                                                        as afterTotalCostModifyExcludeTax,
            if(sum(goods_after_total_cost_modify_exclude_tax) is null, 0.0,
            sum(goods_after_total_cost_modify_exclude_tax))                                                  as goodsAfterTotalCostModifyExcludeTax,
            if(sum(goods_pharmacy_after_total_cost_modify_exclude_tax) is null, 0.0,
            sum(goods_pharmacy_after_total_cost_modify_exclude_tax))                                         as goodsPharmacyAfterTotalCostModifyExcludeTax,
            max(effected_time)                                                                                  as effectedTime,
            max(last_modified_by)                                                                               as lastModifiedBy,
            max(last_modified)                                                                                  as lastModified
        FROM
            ${cisTable}.dwd_goods_inout_tax_rat_modify
        WHERE
            month BETWEEN LEFT(#{param.beginDate}, 7) AND LEFT(#{param.endDate}, 7)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            and status = 1
            <if test="param.baseMedicineTypeSql != null and param.baseMedicineTypeSql != ''">
                ${param.baseMedicineTypeSql}
            </if>
    </select>

    <select id="selectSheBaoTakeInventoryData"
            resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.ShebaoStockNewInfo">
        select
            batch_id as batchId,
            piece_num as pieceNum,
            batch_after_piece_count as stockPieceCount,
            batch_after_package_count as stockPackageCount,
            concat(create_date::text,'-12-31') as  createTime,
            batch_no as batchNo,
            production_date as productionDate,
            expiry_date as expiry,
            batch_after_total_cost as totalCostPrice,
            goods_id as goodsId,
            comment as memo
        from
            ${cisTable}.dwd_goods_inventory_batch_begin_end
        where
            chain_id = #{params.chainId}
            and organ_id = #{params.clinicId}
            and create_date = #{params.takeInventoryDate}
            and classify_level1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            and pharmacy_type = 0
    </select>

    <select id="selectReportBeginData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport">
        SELECT
            dgoods.classify_level_1 as feeType1,
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                dgoods.classify_level_2 as feeType2,
            </if>
            sum(goods_pharmacy_after_total_cost) AS beginCost
        FROM
            ${cisTable}.${tempTable.dwdBeginTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            1=1
            and create_date = CAST(#{tempTable.beginDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
        GROUP BY
            dgoods.classify_level_1
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                ,dgoods.classify_level_2
            </if>
        order by
            dgoods.classify_level_1
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                ,dgoods.classify_level_2
            </if>
        desc
    </select>

    <select id="selectReportActionData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport">
        SELECT
            dgoods.classify_level_1 as feeType1,
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                dgoods.classify_level_2 as feeType2,
            </if>
            SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
            SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5, 7, 10, 11) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost, 0.0)) AS inReceiveCostAmount,
            SUM(IF(action IN('调拨入库', '修正调拨入库'), action_total_cost, 0.0)) AS allotInCost,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS checkInCost,
            SUM(IF(action IN('药品规格修改'), action_total_cost, 0.0)) AS inSpecificationModificationCost,
            SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS outPatientDispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost, 0.0)) AS outPatientDispenseCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS hospitalPharmacyDispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null), action_total_cost, 0.0)) AS hospitalPharmacyDispenseCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and dispensing_method = 10, if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS hospitalAutomaticDispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and dispensing_method = 10, action_total_cost, 0.0)) AS hospitalAutomaticDispenseCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药'), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS dispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药'), action_total_cost, 0.0)) AS dispenseCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost, 0.0)) AS collectOutCost,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost, 0.0)) AS outDepartmentConsumptionCostAmount,
            SUM(IF(action IN('调拨出库', '修正调拨出库'), action_total_cost, 0.0)) AS allotOutCost,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost, 0.0)) AS damagedOutCost,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost, 0.0)) AS outOtherCostAmount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS checkOutCost,
            SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS outTotalCost
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
        GROUP BY
            dgoods.classify_level_1
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                ,dgoods.classify_level_2
            </if>
    </select>

    <select id="selectReportEndData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport">
        SELECT
            dgoods.classify_level_1 as feeType1,
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                dgoods.classify_level_2 as feeType2,
            </if>
            sum(goods_pharmacy_after_total_cost) AS endCost
        FROM
            ${cisTable}.${tempTable.dwdEndTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            1=1
            and create_date =  CAST(#{tempTable.endDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
        GROUP BY
            dgoods.classify_level_1
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                ,dgoods.classify_level_2
            </if>
        order by
            dgoods.classify_level_1
            <if test="param.classifyStatisticsType != null and param.classifyStatisticsType == 2">
                ,dgoods.classify_level_2
            </if>
        desc
    </select>

    <select id="selectReportSummaryBeginData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport">
        SELECT
            sum(goods_pharmacy_after_total_cost) AS beginCost
        FROM
            ${cisTable}.${tempTable.dwdBeginTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            1=1
            and create_date = CAST(#{tempTable.beginDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
    </select>

    <select id="selectReportSummaryActionData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport">
        SELECT
            SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
            SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5, 7, 10, 11) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost, 0.0)) AS inReceiveCostAmount,
            SUM(IF(action IN('调拨入库', '修正调拨入库'), action_total_cost, 0.0)) AS allotInCost,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS checkInCost,
            SUM(IF(action IN('药品规格修改'), action_total_cost, 0.0)) AS inSpecificationModificationCost,
            SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS outPatientDispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost, 0.0)) AS outPatientDispenseCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS hospitalPharmacyDispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null), action_total_cost, 0.0)) AS hospitalPharmacyDispenseCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and dispensing_method = 10, if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS hospitalAutomaticDispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and dispensing_method = 10, action_total_cost, 0.0)) AS hospitalAutomaticDispenseCost,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药'), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS dispensePrice,
            SUM(IF(action IN ('发药' ,'修正发药' ,'退药' ,'修正退药'), action_total_cost, 0.0)) AS dispenseCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost, 0.0)) AS collectOutCost,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost, 0.0)) AS outDepartmentConsumptionCostAmount,
            SUM(IF(action IN('调拨出库', '修正调拨出库'), action_total_cost, 0.0)) AS allotOutCost,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost, 0.0)) AS damagedOutCost,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost, 0.0)) AS outOtherCostAmount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS checkOutCost,
            SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS outTotalCost
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
    </select>

    <select id="selectReportSummaryEndData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport">
        SELECT
            sum(goods_pharmacy_after_total_cost) AS endCost
        FROM
            ${cisTable}.${tempTable.dwdEndTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            1=1
            and create_date =  CAST(#{tempTable.endDate} AS INTEGER)
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and dgibe.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
    </select>

    <select id="selectEssentialMedicineReport" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryEssentialMedicineReport">
        SELECT
            dgoods.classify_level_1 as feeType1,
            SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS stockInEssentialMedicineCost,
            SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS stockInNoEssentialMedicineCost,
            SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS stockInCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and (dispense_type = 0 or dispense_type is null) and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS outpatientDispenseEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and (dispense_type = 0 or dispense_type is null) and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS outpatientDispenseNoEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost, 0.0)) AS outpatientDispenseCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS hospitalPharmacyDispenseEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS hospitalPharmacyDispenseNoEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null), action_total_cost, 0.0)) AS hospitalPharmacyDispenseCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and dispensing_method = 10 and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS hospitalAutomaticDispenseEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and dispensing_method = 10 and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS hospitalAutomaticDispenseNoEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dispense_type = 10 and dispensing_method = 10, action_total_cost, 0.0)) AS hospitalAutomaticDispenseCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS dispenseEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药') and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS dispenseNoEssentialMedicineCost,
            SUM(IF(action IN('发药' ,'修正发药' ,'退药' ,'修正退药'), action_total_cost, 0.0)) AS dispenseCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入','科室消耗','调拨出库','报损出库','其他出库','盘亏出库') and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS stockOutEssentialMedicineCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入','科室消耗','调拨出库','报损出库','其他出库','盘亏出库') and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS stockOutNoEssentialMedicineCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入','科室消耗','调拨出库','报损出库','其他出库','盘亏出库'), action_total_cost, 0.0)) AS stockOutCost,
            SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库') and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS totalEssentialMedicineCost,
            SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库') and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS totalNoEssentialMedicineCost,
            SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS totalCost,
            SUM(IF(action IN('药品规格修改') and dgoods.base_medicine_type in (1, 2), action_total_cost, 0.0)) AS essentialMedicineModifyCost,
            SUM(IF(action IN('药品规格修改') and dgoods.base_medicine_type is null, action_total_cost, 0.0)) AS noEssentialMedicineModifyCost,
            SUM(IF(action IN('药品规格修改'), action_total_cost, 0.0)) AS modifyCost
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
        GROUP BY
            dgoods.classify_level_1
    </select>

    <select id="selectStockHaveChangeGoods" resultType="java.lang.String">
        SELECT
            goods_id        as goodsId
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and classify_level1 in ('1-12','1-13')
            </if>
            and classify_level1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                and dgoods.fee_type_id in
                <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="goodsIds != null and goodsIds.size > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                ${param.dimGoodsBaseMedicineTypeSql}
            </if>
            <if test="param.profitCategoryTypeIds != null and param.profitCategoryTypeIds.size > 0">
                and profit_category_type in
                <foreach collection="param.profitCategoryTypeIds" item="profitCategoryTypeId" open="(" separator="," close=")">
                    #{profitCategoryTypeId}
                </foreach>
            </if>
        GROUP BY goods_id
        <if test="param.limit != null and param.limit != ''">
            order by goods_id
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>

    <select id="selectClassifyBeginData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        select
            dgoods.classify_level_1 as feeType1,
            sum(goods_pharmacy_after_count) as beginCount,
            sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) as beginConvertPieceCount,
            sum(goods_pharmacy_after_total_cost) as beginCost,
            sum(goods_pharmacy_after_total_cost_exclude_tax) as beginCostExcludeTax
        from
            ${cisTable}.${tempTable.dwdBeginTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            create_date = CAST(#{tempTable.beginDate} AS INTEGER)
            <if test="params.pharmacyType != null">
                and pharmacy_type=#{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.pharmacyNosSql != null and params.pharmacyNosSql != ''">
                and ${params.pharmacyNosSql}
            </if>
            <if test="params.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="params.chainId != null and params.chainId != ''">
                and dgibe.chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
        group by
            dgoods.classify_level_1
        order by
            dgoods.classify_level_1 desc
    </select>

    <select id="selectClassifyActionData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        select
            dgoods.classify_level_1 as feeType1,

            <if test="params.hisType != null and params.hisType == 10">
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_package_count *  inventory.piece_num + action_piece_count ,0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>
            <if test="params.hisType != null and params.hisType != 10">
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_package_count *  inventory.piece_num + action_piece_count ,0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>

            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_count, 0.0)) AS inReceiveCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inReceiveConvertPieceCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price, 0.0)) AS inReceiveAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price_exclude_tax, 0.0)) AS inReceiveAmountExcludingTax,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost, 0.0)) AS inReceiveCostAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost_exclude_tax, 0.0)) AS inReceiveCostAmountExcludingTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotInCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price, 0.0)) AS allotInPrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInPriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotInCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInCostExcludeTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotInInsideCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInInsideConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price, 0.0)) AS allotInInsidePrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInInsidePriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotInInsideCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInInsideCostExcludeTax,

            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS checkInCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkInConvertPieceCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS checkInPrice,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS checkInPriceExcludeTax,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS checkInCost,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS checkInCostExcludeTax,

            SUM(IF(action IN('药品规格修改'), action_count, 0.0)) AS inSpecificationModificationCount,
            SUM(IF(action IN('药品规格修改'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inSpecificationModificationConvertPieceCount,
            SUM(IF(action IN('药品规格修改'), action_total_price, 0.0)) AS inSpecificationModificationPrice,
            SUM(IF(action IN('药品规格修改'), action_total_price_exclude_tax, 0.0)) AS inSpecificationModificationPriceExcludeTax,
            SUM(IF(action IN('药品规格修改'), action_total_cost, 0.0)) AS inSpecificationModificationCost,
            SUM(IF(action IN('药品规格修改'), action_total_cost_exclude_tax, 0.0)) AS inSpecificationModificationCostExcludeTax,

            <if test="params.hisType != null and params.hisType == 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>
            <if test="params.hisType != null and params.hisType != 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>

            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_count, 0.0)) AS inInitReturnCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitReturnConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_price, 0.0)) AS inInitReturnPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_price_exclude_tax, 0.0)) AS inInitReturnPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost, 0.0)) AS inInitReturnCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost_exclude_tax, 0.0)) AS inInitReturnCostExcludeTax,

            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_count, 0.0)) AS returnGoodsOutCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS returnGoodsOutConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_price, 0.0)) AS returnGoodsOutPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_price_exclude_tax, 0.0)) AS returnGoodsOutPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost, 0.0)) AS returnGoodsOutCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS returnGoodsOutCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_count, 0.0)) AS outPatientDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outPatientDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS outPatientDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS outPatientDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost, 0.0)) AS outPatientDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost_exclude_tax, 0.0)) AS outPatientDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_count,0.0)) AS hospitalPharmacyDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalPharmacyDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalPharmacyDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalPharmacyDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost,0.0)) AS hospitalPharmacyDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalPharmacyDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_count,0.0)) AS hospitalAutomaticDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalAutomaticDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalAutomaticDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalAutomaticDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost,0.0)) AS hospitalAutomaticDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalAutomaticDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_count,0.0)) AS hospitalNoSettleDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalNoSettleDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalNoSettleDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalNoSettleDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost,0.0)) AS hospitalNoSettleDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost_exclude_tax,0.0)) AS hospitalNoSettleDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_count, 0.0)) AS dispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS dispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS dispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS dispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost, 0.0)) AS dispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost_exclude_tax, 0.0)) AS dispenseCostExcludeTax,

            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_count, 0.0)) AS collectOutCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS collectOutConvertPieceCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_price, 0.0)) AS collectOutPrice,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_price_exclude_tax, 0.0)) AS collectOutPriceExcludeTax,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost, 0.0)) AS collectOutCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost_exclude_tax, 0.0)) AS collectOutCostExcludeTax,

            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_count, 0.0)) AS outDepartmentConsumptionCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outDepartmentConsumptionConvertPieceCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_price, 0.0)) AS outDepartmentConsumptionAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_price_exclude_tax, 0.0)) AS outDepartmentConsumptionAmountExcludingTax,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost, 0.0)) AS outDepartmentConsumptionCostAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost_exclude_tax, 0.0)) AS outDepartmentConsumptionCostAmountExcludingTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotOutCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price , 0.0)) AS allotOutPrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price_exclude_tax , 0.0)) AS allotOutPriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotOutCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutCostExcludeTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotOutInsideCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutInsideConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price , 0.0)) AS allotOutInsidePrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price_exclude_tax , 0.0)) AS allotOutInsidePriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotOutInsideCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutInsideCostExcludeTax,

            SUM(IF(action IN('报损出库', '修正报损出库'), action_count, 0.0)) AS damagedOutCount,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS damagedOutConvertPieceCount,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_price, 0.0)) AS damagedOutPrice,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_price_exclude_tax, 0.0)) AS damagedOutPriceExcludeTax,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost, 0.0)) AS damagedOutCost,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost_exclude_tax, 0.0)) AS damagedOutCostExcludeTax,

            SUM(IF(action IN('其他出库', '修正其他出库'), action_count, 0.0)) AS outOtherCount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outOtherConvertPieceCount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_price, 0.0)) AS outOtherAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_price_exclude_tax, 0.0)) AS outOtherAmountExcludingTax,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost, 0.0)) AS outOtherCostAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost_exclude_tax, 0.0)) AS outOtherCostAmountExcludingTax,

            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_count, 0.0)) AS checkOutCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkOutConvertPieceCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price, 0.0)) AS checkOutPrice,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price_exclude_tax, 0.0)) AS checkOutPriceExcludeTax,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS checkOutCost,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost_exclude_tax, 0.0)) AS checkOutCostExcludeTax,

            SUM(IF(action IN('生产出库', '修正生产出库'), action_count ,0.0)) AS productionOutCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_package_count * inventory.piece_num + action_piece_count ,0.0)) AS productionOutConvertPieceCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price,0.0)) AS productionOutPrice,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price_exclude_tax,0.0)) AS productionOutPriceExcludeTax,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost,0.0)) AS productionOutCost,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost_exclude_tax,0.0)) AS productionOutCostExcludeTax,

            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_count ,0.0)) AS deliveryOutCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_package_count * inventory.piece_num + action_piece_count ,0.0)) AS deliveryOutConvertPieceCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price,0.0)) AS deliveryOutPrice,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price_exclude_tax,0.0)) AS deliveryOutPriceExcludeTax,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost,0.0)) AS deliveryOutCost,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost_exclude_tax,0.0)) AS deliveryOutCostExcludeTax,

            <if test="params.hisType != null and params.hisType == 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>
            <if test="params.hisType != null and params.hisType != 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>

        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            ds between #{params.beginDateDs} and #{params.endDateDs}
            and create_date between #{params.beginDate} and #{params.endDate}
            <if test="params.pharmacyType != null">
                and pharmacy_type=#{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.pharmacyNosSql != null and params.pharmacyNosSql != ''">
                and ${params.pharmacyNosSql}
            </if>
            <if test="params.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="params.chainId != null and params.chainId != ''">
                and inventory.chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
        group by
            dgoods.classify_level_1
    </select>

    <select id="selectClassifyEndData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        select
            dgoods.classify_level_1 as feeType1,
            sum(goods_pharmacy_after_count) as endCount,
            sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) as endConvertPieceCount,
            sum(goods_pharmacy_after_total_cost) as endCost,
            sum(goods_pharmacy_after_total_cost_exclude_tax) as endCostExcludeTax
        from
            ${cisTable}.${tempTable.dwdEndTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            create_date = CAST(#{tempTable.endDate} AS INTEGER)
            <if test="params.pharmacyType != null">
                and pharmacy_type=#{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.pharmacyNosSql != null and params.pharmacyNosSql != ''">
                and ${params.pharmacyNosSql}
            </if>
            <if test="params.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="params.chainId != null and params.chainId != ''">
                and dgibe.chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
        group by
            dgoods.classify_level_1
        order by
            dgoods.classify_level_1 desc
    </select>

    <select id="selectClassifySummaryBeginData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        select
            sum(goods_pharmacy_after_count) as beginCount,
            sum(goods_pharmacy_after_package_count * dgibe.piece_num + goods_pharmacy_after_piece_count) as beginConvertPieceCount,
            sum(goods_pharmacy_after_total_cost) as beginCost,
            sum(goods_pharmacy_after_total_cost_exclude_tax) as beginCostExcludeTax
        from
            ${cisTable}.${tempTable.dwdBeginTable} as dgibe
            inner join ${cisTable}.dim_goods as dgoods on dgibe.goods_id = dgoods.id
        where
            create_date = CAST(#{tempTable.beginDate} AS INTEGER)
            <if test="params.pharmacyType != null">
                and pharmacy_type=#{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.pharmacyNosSql != null and params.pharmacyNosSql != ''">
                and ${params.pharmacyNosSql}
            </if>
            <if test="params.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="params.chainId != null and params.chainId != ''">
                and dgibe.chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
    </select>

    <select id="selectClassifySummaryActionData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        select
            <if test="params.hisType != null and params.hisType == 10">
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_package_count *  inventory.piece_num + action_piece_count ,0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>
            <if test="params.hisType != null and params.hisType != 10">
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_count, 0.0)) AS inInitCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price, 0.0)) AS inInitPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_price_exclude_tax, 0.0)) AS inInitPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) AS inInitCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) AS inInitCostExcludeTax,

                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_count, 0.0)) AS purchaseInCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_package_count *  inventory.piece_num + action_piece_count ,0.0)) AS purchaseInConvertPieceCount,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price, 0.0)) AS purchaseInPrice,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_price_exclude_tax, 0.0)) AS purchaseInPriceExcludeTax,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost, 0.0)) AS purchaseInCost,
                SUM(IF(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and (scene in (0, 1, 2, 4, 5 ,7, 10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS purchaseInCostExcludeTax,
            </if>


            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_count, 0.0)) AS inReceiveCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inReceiveConvertPieceCount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price, 0.0)) AS inReceiveAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_price_exclude_tax, 0.0)) AS inReceiveAmountExcludingTax,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost, 0.0)) AS inReceiveCostAmount,
            SUM(IF(action IN('领用入库', '修正领用入库', '领用退出', '修正领用退出'), action_total_cost_exclude_tax, 0.0)) AS inReceiveCostAmountExcludingTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotInCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price, 0.0)) AS allotInPrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInPriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotInCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInCostExcludeTax,

            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotInInsideCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotInInsideConvertPieceCount,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price, 0.0)) AS allotInInsidePrice,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_price_exclude_tax, 0.0)) AS allotInInsidePriceExcludeTax,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotInInsideCost,
            SUM(IF(action IN('调拨入库', '修正调拨入库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotInInsideCostExcludeTax,

            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS checkInCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkInConvertPieceCount,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS checkInPrice,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS checkInPriceExcludeTax,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS checkInCost,
            SUM(IF(action IN('盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS checkInCostExcludeTax,

            SUM(IF(action IN('药品规格修改'), action_count, 0.0)) AS inSpecificationModificationCount,
            SUM(IF(action IN('药品规格修改'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inSpecificationModificationConvertPieceCount,
            SUM(IF(action IN('药品规格修改'), action_total_price, 0.0)) AS inSpecificationModificationPrice,
            SUM(IF(action IN('药品规格修改'), action_total_price_exclude_tax, 0.0)) AS inSpecificationModificationPriceExcludeTax,
            SUM(IF(action IN('药品规格修改'), action_total_cost, 0.0)) AS inSpecificationModificationCost,
            SUM(IF(action IN('药品规格修改'), action_total_cost_exclude_tax, 0.0)) AS inSpecificationModificationCostExcludeTax,

            <if test="params.hisType != null and params.hisType == 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>
            <if test="params.hisType != null and params.hisType != 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '调拨入库', '修正调拨入库', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库'), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_count, 0.0)) AS inTotalCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inTotalConvertPieceCount,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price, 0.0)) AS inTotalPrice,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_price_exclude_tax, 0.0)) AS inTotalPriceExcludeTax,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost, 0.0)) AS inTotalCost,
                    SUM(IF(action IN('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '领用入库', '修正领用入库', '领用退出', '修正领用退出', '盘盈入库', '修正盘盈入库', '盘点入库', '修正盘点入库') or (action IN('调拨入库', '修正调拨入库') and from_organ_id != to_organ_id), action_total_cost_exclude_tax, 0.0)) AS inTotalCostExcludeTax,
                </if>
            </if>

            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_count, 0.0)) AS inInitReturnCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS inInitReturnConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_price, 0.0)) AS inInitReturnPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_price_exclude_tax, 0.0)) AS inInitReturnPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost, 0.0)) AS inInitReturnCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and scene in (12, 13), action_total_cost_exclude_tax, 0.0)) AS inInitReturnCostExcludeTax,

            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_count, 0.0)) AS returnGoodsOutCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS returnGoodsOutConvertPieceCount,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_price, 0.0)) AS returnGoodsOutPrice,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_price_exclude_tax, 0.0)) AS returnGoodsOutPriceExcludeTax,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost, 0.0)) AS returnGoodsOutCost,
            SUM(IF(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), action_total_cost_exclude_tax, 0.0)) AS returnGoodsOutCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_count, 0.0)) AS outPatientDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outPatientDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS outPatientDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS outPatientDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost, 0.0)) AS outPatientDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and (dispense_type = 0 or dispense_type is null), action_total_cost_exclude_tax, 0.0)) AS outPatientDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_count,0.0)) AS hospitalPharmacyDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalPharmacyDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalPharmacyDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalPharmacyDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost,0.0)) AS hospitalPharmacyDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and (dispensing_method != 10 or dispensing_method is null) and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalPharmacyDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_count,0.0)) AS hospitalAutomaticDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalAutomaticDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalAutomaticDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalAutomaticDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost,0.0)) AS hospitalAutomaticDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and dispensing_method = 10 and settle_status = 200, action_total_cost_exclude_tax,0.0)) AS hospitalAutomaticDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_count,0.0)) AS hospitalNoSettleDispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_package_count * inventory.piece_num + action_piece_count,0.0)) AS hospitalNoSettleDispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0)) AS hospitalNoSettleDispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0)) AS hospitalNoSettleDispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost,0.0)) AS hospitalNoSettleDispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药') and dispense_type = 10 and settle_status != 200, action_total_cost_exclude_tax,0.0)) AS hospitalNoSettleDispenseCostExcludeTax,

            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_count, 0.0)) AS dispenseCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS dispenseConvertPieceCount,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0)) AS dispensePrice,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0)) AS dispensePriceExcludeTax,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost, 0.0)) AS dispenseCost,
            SUM(IF(action IN ('发药', '修正发药', '退药', '修正退药'), action_total_cost_exclude_tax, 0.0)) AS dispenseCostExcludeTax,

            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_count, 0.0)) AS collectOutCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS collectOutConvertPieceCount,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_price, 0.0)) AS collectOutPrice,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_price_exclude_tax, 0.0)) AS collectOutPriceExcludeTax,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost, 0.0)) AS collectOutCost,
            SUM(IF(action IN('科室出库', '修正科室出库', '领用退入', '修正领用退入'), action_total_cost_exclude_tax, 0.0)) AS collectOutCostExcludeTax,

            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_count, 0.0)) AS outDepartmentConsumptionCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outDepartmentConsumptionConvertPieceCount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_price, 0.0)) AS outDepartmentConsumptionAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_price_exclude_tax, 0.0)) AS outDepartmentConsumptionAmountExcludingTax,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost, 0.0)) AS outDepartmentConsumptionCostAmount,
            SUM(IF(action IN('科室消耗', '修正科室消耗'), action_total_cost_exclude_tax, 0.0)) AS outDepartmentConsumptionCostAmountExcludingTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_count, 0.0)) AS allotOutCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price , 0.0)) AS allotOutPrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_price_exclude_tax , 0.0)) AS allotOutPriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost, 0.0)) AS allotOutCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutCostExcludeTax,

            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_count, 0.0)) AS allotOutInsideCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS allotOutInsideConvertPieceCount,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price , 0.0)) AS allotOutInsidePrice,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_price_exclude_tax , 0.0)) AS allotOutInsidePriceExcludeTax,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost, 0.0)) AS allotOutInsideCost,
            SUM(IF(action IN('调拨出库', '修正调拨出库') and from_organ_id = to_organ_id, action_total_cost_exclude_tax, 0.0)) AS allotOutInsideCostExcludeTax,

            SUM(IF(action IN('报损出库', '修正报损出库'), action_count, 0.0)) AS damagedOutCount,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS damagedOutConvertPieceCount,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_price, 0.0)) AS damagedOutPrice,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_price_exclude_tax, 0.0)) AS damagedOutPriceExcludeTax,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost, 0.0)) AS damagedOutCost,
            SUM(IF(action IN('报损出库', '修正报损出库'), action_total_cost_exclude_tax, 0.0)) AS damagedOutCostExcludeTax,

            SUM(IF(action IN('其他出库', '修正其他出库'), action_count, 0.0)) AS outOtherCount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS outOtherConvertPieceCount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_price, 0.0)) AS outOtherAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_price_exclude_tax, 0.0)) AS outOtherAmountExcludingTax,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost, 0.0)) AS outOtherCostAmount,
            SUM(IF(action IN('其他出库', '修正其他出库'), action_total_cost_exclude_tax, 0.0)) AS outOtherCostAmountExcludingTax,

            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_count, 0.0)) AS checkOutCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_package_count * inventory.piece_num + action_piece_count, 0.0)) AS checkOutConvertPieceCount,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price, 0.0)) AS checkOutPrice,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_price_exclude_tax, 0.0)) AS checkOutPriceExcludeTax,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost, 0.0)) AS checkOutCost,
            SUM(IF(action IN('盘亏出库', '修正盘亏出库'), action_total_cost_exclude_tax, 0.0)) AS checkOutCostExcludeTax,

            SUM(IF(action IN('生产出库', '修正生产出库'), action_count ,0.0)) AS productionOutCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_package_count * inventory.piece_num + action_piece_count ,0.0)) AS productionOutConvertPieceCount,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price,0.0)) AS productionOutPrice,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_price_exclude_tax,0.0)) AS productionOutPriceExcludeTax,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost,0.0)) AS productionOutCost,
            SUM(IF(action IN('生产出库', '修正生产出库'), action_total_cost_exclude_tax,0.0)) AS productionOutCostExcludeTax,

            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_count ,0.0)) AS deliveryOutCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_package_count * inventory.piece_num + action_piece_count ,0.0)) AS deliveryOutConvertPieceCount,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price,0.0)) AS deliveryOutPrice,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_price_exclude_tax,0.0)) AS deliveryOutPriceExcludeTax,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost,0.0)) AS deliveryOutCost,
            SUM(IF(action IN('配货出库', '修正配货出库', '配货退入', '修正配货退入'), action_total_cost_exclude_tax,0.0)) AS deliveryOutCostExcludeTax,

            <if test="params.hisType != null and params.hisType == 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>
            <if test="params.hisType != null and params.hisType != 10">
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 1">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '调拨出库', '修正调拨出库', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入'),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
                <if test="params.isContainsAllotInInside != null and params.isContainsAllotInInside == 0">
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_count,0.0)) AS outTotalCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_package_count * inventory.piece_num + action_piece_count,0.0)) AS outTotalConvertPieceCount,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price,0.0)) AS outTotalPrice,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_price_exclude_tax,0.0)) AS outTotalPriceExcludeTax,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost,0.0)) AS outTotalCost,
                    SUM(IF(action IN('发药', '修正发药', '退药', '修正退药', '科室出库', '修正科室出库', '领用退入', '修正领用退入', '科室消耗', '修正科室消耗', '报损出库', '修正报损出库', '其他出库', '修正其他出库', '盘亏出库', '修正盘亏出库','生产出库','修正生产出库', '配货出库', '修正配货出库', '配货退入', '修正配货退入') or (action IN('调拨出库', '修正调拨出库') and from_organ_id != to_organ_id),action_total_cost_exclude_tax,.00)) AS outTotalCostExcludeTax
                </if>
            </if>

        from
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            ds between #{params.beginDateDs} and #{params.endDateDs}
            and create_date between #{params.beginDate} and #{params.endDate}
            <if test="params.pharmacyType != null">
                and pharmacy_type=#{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.pharmacyNosSql != null and params.pharmacyNosSql != ''">
                and ${params.pharmacyNosSql}
            </if>
            <if test="params.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="params.chainId != null and params.chainId != ''">
                and inventory.chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
    </select>

    <select id="selectClassifySummaryEndData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        select
            sum(goods_pharmacy_after_count) as endCount,
            sum(goods_pharmacy_after_package_count * digbe.piece_num + goods_pharmacy_after_piece_count) as endConvertPieceCount,
            sum(goods_pharmacy_after_total_cost) as endCost,
            sum(goods_pharmacy_after_total_cost_exclude_tax) as endCostExcludeTax
        from
            ${cisTable}.${tempTable.dwdEndTable} as digbe
            inner join ${cisTable}.dim_goods as dgoods on digbe.goods_id = dgoods.id
        where
            create_date = CAST(#{tempTable.endDate} AS INTEGER)
            <if test="params.pharmacyType != null">
                and pharmacy_type=#{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.pharmacyNosSql != null and params.pharmacyNosSql != ''">
                and ${params.pharmacyNosSql}
            </if>
            <if test="params.pharmacyType == 2 ">
                and dgoods.classify_level_1 in ('1-12','1-13')
            </if>
            and dgoods.classify_level_1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="params.chainId != null and params.chainId != ''">
                and digbe.chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
    </select>

    <select id="selectRecordTraceableCode" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryRecord">
        SELECT
            inventory.id as id,
            goods as goodsStr
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and not (action_count = 0.0 and action_total_cost = 0.0)
            and action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and classify_level1 in ('1-12','1-13')
            </if>
            and classify_level1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
            <if test="idList != null and idList.size() > 0">
                and inventory.id in
                <foreach collection="idList" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.supplierId != null and param.supplierId!='' ">
                and supplier_id = #{param.supplierId}
            </if>
    </select>

    <select id="selectReportNoSettleData" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport">
        <if test="param.hint != null and param.hint != ''">
            ${param.hint}
        </if>
        SELECT
            SUM(IF(action IN ('发药','退药') and (dispensing_method != 10 or dispensing_method is null), action_count,0.0)) AS hospitalPharmacyNoSettleDispenseCount,
            SUM(IF(action IN ('发药','退药') and dispensing_method = 10, action_count,0.0)) AS hospitalAutomaticNoSettleDispenseCount
        FROM
            ${cisTable}.dwd_goods_inventory as inventory
            inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
        where
            1=1
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            and settle_status != 200
            and dispense_type = 10
            <if test="param.pharmacyType != null">
                and pharmacy_type=#{param.pharmacyType}
            </if>
            <if test="param.pharmacyNo != null">
                and pharmacy_no = #{param.pharmacyNo}
            </if>
            <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                and ${param.pharmacyNosSql}
            </if>
            <if test="param.pharmacyType == 2 ">
                and classify_level1 in ('1-12','1-13')
            </if>
            and classify_level1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="param.chainId != null and param.chainId != ''">
                and inventory.chain_id=#{param.chainId}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id=#{param.clinicId}
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                and (${param.goodsFeeType1} or ${param.goodsFeeType2})
            </if>
            <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                and ${param.goodsFeeType1}
            </if>
            <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                and ${param.goodsFeeType2}
            </if>
    </select>

    <select id="selectNoSettleRecordTotal" resultType="java.lang.Long">
        <if test="param.hint != null and param.hint != ''">
            ${param.hint}
        </if>
        SELECT
            sum(t.cnt) as cnt
        from (
            SELECT
                count(stock_id) as cnt
            FROM
                ${cisTable}.dwd_goods_inventory as inventory
                inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
            where
                1=1
                and settle_status != 200
                and not (action_count = 0.0 and action_total_cost = 0.0)
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                and action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
                <if test="param.pharmacyType != null">
                    and pharmacy_type=#{param.pharmacyType}
                </if>
                <if test="param.pharmacyNo != null">
                    and pharmacy_no = #{param.pharmacyNo}
                </if>
                <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                    and ${param.pharmacyNosSql}
                </if>
                <if test="param.pharmacyType == 2 ">
                    and classify_level1 in ('1-12','1-13')
                </if>
                and classify_level1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
                <if test="param.chainId != null and param.chainId != ''">
                    and inventory.chain_id=#{param.chainId}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id=#{param.clinicId}
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                    and (${param.goodsFeeType1} or ${param.goodsFeeType2})
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                    and ${param.goodsFeeType1}
                </if>
                <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                    and ${param.goodsFeeType2}
                </if>
                <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                    and dgoods.fee_type_id in
                    <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="goodsIds != null and goodsIds.size > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="actionSql != null and actionSql != ''">
                    ${actionSql}
                </if>
                <if test="param.stockIds != null and param.stockIds.size > 0">
                    and stock_id in
                    <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.batchIds != null and param.batchIds.size > 0">
                    and batch_id in
                    <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.supplierId != null and param.supplierId!=''">
                    and supplier_id = #{param.supplierId}
                </if>
                <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                    ${param.dimGoodsBaseMedicineTypeSql}
                </if>
            group by
                batch_id
        ) t
    </select>

    <select id="selectNoSettleRecordByGoodsTotal" resultType="java.lang.Long">
        <if test="param.hint != null and param.hint != ''">
            ${param.hint}
        </if>
        SELECT
            count(*) as cnt
        from(
            SELECT
                inventory.chain_id,
                organ_id,
                bat_id,
                inventory.id,
                row_number() OVER(PARTITION BY goods_id,bat_id ORDER BY inventory.id) as sort_num
            from
                ${cisTable}.dwd_goods_inventory as inventory
                inner join ${cisTable}.dim_goods as dgoods on inventory.goods_id = dgoods.id
            where
                not (action_count = 0.0 and action_total_cost =0.0)
                and settle_status != 200
                and action not in ( '入库单减少', '入库单增加', '入库单删除', '入库单成本价修改')
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.pharmacyType != null">
                    and pharmacy_type=#{param.pharmacyType}
                </if>
                <if test="param.pharmacyNo != null">
                    and pharmacy_no = #{param.pharmacyNo}
                </if>
                <if test="param.pharmacyNosSql != null and param.pharmacyNosSql != ''">
                    and ${param.pharmacyNosSql}
                </if>
                <if test="param.pharmacyType == 2 ">
                    and classify_level1 in ('1-12','1-13')
                </if>
                and classify_level1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
                <if test="param.chainId != null and param.chainId != ''">
                    and inventory.chain_id=#{param.chainId}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id=#{param.clinicId}
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 != null">
                    and (${param.goodsFeeType1} or ${param.goodsFeeType2})
                </if>
                <if test="param.goodsFeeType1 != null and param.goodsFeeType2 == null">
                    and ${param.goodsFeeType1}
                </if>
                <if test="param.goodsFeeType2 != null and param.goodsFeeType1 == null">
                    and ${param.goodsFeeType2}
                </if>
                <if test="feeTypeIds != null and feeTypeIds.size() > 0">
                    and dgoods.fee_type_id in
                    <foreach collection="feeTypeIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="goodsIds != null and goodsIds.size > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="actionSql != null and actionSql != ''">
                    ${actionSql}
                </if>
                <if test="param.stockIds != null and param.stockIds.size > 0">
                    and stock_id in
                    <foreach collection="param.stockIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.batchIds != null and param.batchIds.size > 0">
                    and batch_id in
                    <foreach collection="param.batchIds" separator="," close=")" open=" (" item="ac">
                        #{ac}
                    </foreach>
                </if>
                <if test="param.supplierId != null and param.supplierId!='' ">
                    and supplier_id = #{param.supplierId}
                </if>
                <if test="param.dimGoodsBaseMedicineTypeSql != null and param.dimGoodsBaseMedicineTypeSql != ''">
                    ${param.dimGoodsBaseMedicineTypeSql}
                </if>
        ) as a
        where
            sort_num = 1
    </select>

    <select id="selectClassifySummaryTaxRatModify" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify">
        SELECT
            if(max(before_in_tax_rat) is null, 0.0, max(before_in_tax_rat))                                     as beforeInTaxRat,
            if(max(before_out_tax_rat) is null, 0.0, max(before_out_tax_rat))                                   as beforeOutTaxRat,
            if(max(dgitrm.in_tax_rat) is null, 0.0, max(dgitrm.in_tax_rat))                                                   as inTaxRat,
            if(max(dgitrm.out_tax_rat) is null, 0.0, max(dgitrm.out_tax_rat))                                                 as outTaxRat,
            if(sum(after_total_cost_modify_exclude_tax) is null, 0.0,
            sum(after_total_cost_modify_exclude_tax))                                                        as afterTotalCostModifyExcludeTax,
            if(sum(goods_after_total_cost_modify_exclude_tax) is null, 0.0,
            sum(goods_after_total_cost_modify_exclude_tax))                                                  as goodsAfterTotalCostModifyExcludeTax,
            if(sum(goods_pharmacy_after_total_cost_modify_exclude_tax) is null, 0.0,
            sum(goods_pharmacy_after_total_cost_modify_exclude_tax))                                         as goodsPharmacyAfterTotalCostModifyExcludeTax,
            max(effected_time)                                                                                  as effectedTime,
            max(last_modified_by)                                                                               as lastModifiedBy,
            max(last_modified)                                                                                  as lastModified
        FROM
            ${cisTable}.dwd_goods_inout_tax_rat_modify as dgitrm
            inner join ${cisTable}.dim_goods as dgoods on dgitrm.goods_id = dgoods.id
        WHERE
            month BETWEEN LEFT(#{params.beginDate}, 7) AND LEFT(#{params.endDate}, 7)
            and classify_level1 is not null
            <if test="params.pharmacyType != null">
                and pharmacy_type = #{params.pharmacyType}
            </if>
            <if test="params.pharmacyNo != null">
                and pharmacy_no = #{params.pharmacyNo}
            </if>
            <if test="params.chainId != null and params.chainId != ''">
                and dgitrm.chain_id = #{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id = #{params.clinicId}
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 != null">
                and (${params.goodsFeeType1} or ${params.goodsFeeType2})
            </if>
            <if test="params.goodsFeeType1 != null and params.goodsFeeType2 == null">
                and ${params.goodsFeeType1}
            </if>
            <if test="params.goodsFeeType2 != null and params.goodsFeeType1 == null">
                and ${params.goodsFeeType2}
            </if>
            and dgitrm.status = 1
    </select>

    <select id="selectProfitCategoryTypeId" resultType="java.lang.Long">
        SELECT
            distinct
            profit_category_type
        FROM
            ${cisTable}.dwd_goods_inventory
        WHERE
            1=1
            and ds between #{params.beginDateDs} and #{params.endDateDs}
            and create_date between #{params.beginDate} and #{params.endDate}
            and classify_level1 in ('1-1','1-3','1-12','1-16','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            <if test="params.chainId != null and params.chainId != ''">
                and chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
    </select>

    <select id="selectInventorySource" resultType="cn.abc.flink.stat.service.cis.goods.inventory.domain.InventorySource">
        SELECT
            distinct
            scene as id,
            order_id as orderId
        FROM
            ${cisTable}.dwd_goods_inventory
        WHERE
            1=1
            and ds between #{params.beginDateDs} and #{params.endDateDs}
            and create_date between #{params.beginDate} and #{params.endDate}
            and classify_level1 in ('1-1','1-3','1-12','1-13','2-1','2-2','2-3','2-4','7-1','7-2','7-3','7-4','7-5','24-0','24-1','24-2','24-3','24-4','24-5','24-6')
            and scene is not null
            and scene in (20, 25, 40)
            <if test="params.chainId != null and params.chainId != ''">
                and chain_id=#{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and organ_id=#{params.clinicId}
            </if>
    </select>

</mapper>