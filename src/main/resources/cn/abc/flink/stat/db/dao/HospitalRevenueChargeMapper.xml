<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.dao.HospitalRevenueChargeMapper">


    <select id="selectionTypeIdByTransaction" resultType="java.lang.Integer">
        select distinct
            v2hcst.type
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
        where
            v2hcstr.chain_id = #{param.chainId}
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcst.clinic_id = #{param.clinicId}
            </if>
    </select>

    <select id="selectionPayModeIdByTransaction"
            resultType="cn.abc.flink.stat.service.cis.selection.entity.PayModeDao">
        select distinct
            v2hcst.pay_mode as payMode1,
            v2hcst.pay_sub_mode as payMode2
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
        where
            v2hcst.chain_id = #{param.chainId}
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcst.clinic_id = #{param.clinicId}
            </if>
    </select>

    <select id="selectionEmployeeIdByTransaction" resultType="java.lang.String">

        select distinct
            v2hcst.created_by
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
        where
            v2hcst.chain_id = #{param.chainId}
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcst.clinic_id = #{param.clinicId}
            </if>
    </select>

    <select id="selectionDepartmentIdByTransaction" resultType="java.lang.String">
        select distinct
            v2phe.department_id
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
            left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe  on v2hcst.patient_order_id = v2phe.id
        where
            v2hcst.chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcst.clinic_id = #{param.clinicId}
            </if>
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
    </select>

    <select id="selectionWardIdByTransaction" resultType="java.lang.String">
        select distinct
            v2phe.ward_id
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
            left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe  on v2hcst.patient_order_id = v2phe.id
        where
            v2hcst.chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcst.clinic_id = #{param.clinicId}
            </if>
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
    </select>

    <select id="selectTransaction"
            resultType="cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeTransaction">
        select
            v2hcstr.clinic_id as clinicId,
            v2hcst.created as settlementTime,
            v2p.no as admissionNo,
            v2hcstr.patient_id as patientId,
            v2phe.fee_type_name chargeFee,
            if(v2hcst.type = 1,'出院结算','撤销结算') as type,
            v2hcs.total_price as totalSettlement,
            sum(if(v2hcst.type = 1, v2hcstr.total_price, -1 * v2hcstr.total_price)) as netReceipts,
            v2hcst.pay_mode as payMode,
            v2hcst.pay_sub_mode as paySubMode,
            v2phe.department_id as leaveHospitalDepartmentId,
            v2phe.ward_id as leaveHospitalWardId,
            v2phe.inpatient_time as beHospitalizedTime,
            date_format(v2phe.discharge_time,'%Y-%m-%d') as leaveHospitalTime,
            if(DATE_FORMAT(v2phe.inpatient_time,'%Y-%m-%d') =DATE_FORMAT(v2phe.discharge_time,'%Y-%m-%d'),1,timestampdiff(day,v2phe.inpatient_time,v2phe.discharge_time)) as beHospitalizedDayNumber,
            v2hcst.created_by as sellerId
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
            left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe on v2hcstr.patient_order_id = v2phe.id
            left join ${patientOrderDb}.v2_patientorder v2p on v2hcstr.patient_order_id = v2p.id
            left join ${hisChargeDb}.v2_his_charge_settle v2hcs  on v2hcstr.his_charge_settle_id = v2hcs.id
        where
            v2hcstr.chain_id = #{param.chainId}
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcstr.clinic_id = #{param.clinicId}
            </if>
            <if test="param.type != null">
                and v2hcst.type = #{param.type}
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.sellerId != null and param.sellerId != ''">
                and v2hcst.created_by = #{param.sellerId}
            </if>
            <if test="param.departmentId != null and param.departmentId != ''">
                and v2phe.department_id = #{param.departmentId}
            </if>
            <if test="param.wardId != null and param.wardId != ''">
                and v2phe.ward_id = #{param.wardId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and v2hcstr.patient_id = #{param.patientId}
            </if>
            <!-- 只要收费项目 -->
            and ((v2hcstr.goods_fee_type = 2) or ( v2hcstr.goods_fee_type = 0 and v2hcstr.compose_type != 1))
        group by
            v2hcstr.clinic_id,v2hcst.created,v2p.no,v2hcstr.patient_id,v2phe.fee_type_name,v2hcst.type,v2hcs.total_price,v2hcst.pay_mode,v2hcst.pay_sub_mode,v2phe.department_id,v2phe.ward_id,v2phe.inpatient_time,v2phe.discharge_time,v2hcst.created_by,v2hcstr.transaction_id
        order by v2hcst.created desc
        <if test="param.size != null and param.size != 0">
            limit #{param.size}
            offset #{param.offset}
        </if>
    </select>

    <select id="selectClassify"
            resultType="cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeClassify">
        select
            v2hcstr.clinic_id as clinicId,
            v2hcst.created as settlementTime,
            v2p.no as admissionNo,
            v2hcstr.patient_id as patientId,
            v2phe.fee_type_name as chargeFee,
            v2hcst.type as typeNumber,
            if(v2hcst.type = 1,'出院结算','撤销结算') as type,
            v2hcs.total_price as totalSettlement,
            sum(if(v2hcst.type = 1, v2hcstr.total_price, -1 * v2hcstr.total_price)) as netReceipts,
            v2hcst.pay_mode as payMode,
            v2hcst.pay_sub_mode as paySubMode,
            v2phe.department_id as leaveHospitalDepartmentId,
            v2phe.ward_id as leaveHospitalWardId,
            v2phe.inpatient_time as beHospitalizedTime,
            date_format(v2phe.discharge_time,'%Y-%m-%d') as leaveHospitalTime,
            if(DATE_FORMAT(v2phe.inpatient_time,'%Y-%m-%d') =DATE_FORMAT(v2phe.discharge_time,'%Y-%m-%d'),1,timestampdiff(day,v2phe.inpatient_time,v2phe.discharge_time)) as beHospitalizedDayNumber,
            v2hcst.created_by as sellerId,
            v2hcstr.fee_type_id as feeTypeId
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
            left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe on v2hcstr.patient_order_id = v2phe.id
            left join ${patientOrderDb}.v2_patientorder v2p on v2hcstr.patient_order_id = v2p.id
            left join ${hisChargeDb}.v2_his_charge_settle v2hcs  on v2hcstr.his_charge_settle_id = v2hcs.id
        where
            v2hcstr.chain_id = #{param.chainId}
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcstr.clinic_id = #{param.clinicId}
            </if>
            <if test="param.type != null">
                and v2hcst.type = #{param.type}
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.sellerId != null and param.sellerId != ''">
                and v2hcst.created_by = #{param.sellerId}
            </if>
            <if test="param.departmentId != null and param.departmentId != ''">
                and v2phe.department_id = #{param.departmentId}
            </if>
            <if test="param.wardId != null and param.wardId != ''">
                and v2phe.ward_id = #{param.wardId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and v2hcstr.patient_id = #{param.patientId}
            </if>
            <if test="param.customTypeSql != null">
                and ${param.customTypeSql}
            </if>
            <!-- 只要收费项目 -->
            and ((v2hcstr.goods_fee_type = 2) or ( v2hcstr.goods_fee_type = 0 and v2hcstr.compose_type != 1))
            <if test="param.feeTypeIds != null and param.feeTypeIds != ''">
                and v2hcstr.fee_type_id in ${param.feeTypeIds}
            </if>
        group by
            v2hcstr.clinic_id,v2hcst.created,v2p.no,v2hcstr.patient_id,v2phe.fee_type_name,v2hcst.type,v2hcs.total_price,v2hcst.pay_mode,v2hcst.pay_sub_mode,v2phe.department_id,v2phe.ward_id,v2phe.inpatient_time,v2phe.discharge_time,v2hcst.created_by,v2hcstr.fee_type_id
        order by v2hcst.created desc
        <if test="param.size != null and param.size != 0">
            limit #{param.size}
            offset #{param.offset}
        </if>
    </select>

    <select id="selectDetail"
            resultType="cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeDetail">
        select
            v2hcstr.clinic_id as clinicId,
            v2hcst.created as settlementTime,
            v2p.no as admissionNo,
            v2hcstr.patient_id as patientId,
            v2phe.fee_type_name chargeFee,
            v2hcst.type as typeNumber,
            if(v2hcst.type = 1,'出院结算','撤销结算') as type,
            v2hcs.total_price as totalSettlement,
            sum(if(v2hcst.type = 1, v2hcstr.total_price, -1 * v2hcstr.total_price)) as netReceipts,
            v2hcst.pay_mode as payMode,
            v2hcst.pay_sub_mode as paySubMode,
            v2phe.department_id as leaveHospitalDepartmentId,
            v2phe.ward_id as leaveHospitalWardId,
            v2phe.inpatient_time as beHospitalizedTime,
            date_format(v2phe.discharge_time,'%Y-%m-%d') as leaveHospitalTime,
            if(DATE_FORMAT(v2phe.inpatient_time,'%Y-%m-%d') =DATE_FORMAT(v2phe.discharge_time,'%Y-%m-%d'),1,timestampdiff(day,v2phe.inpatient_time,v2phe.discharge_time)) as beHospitalizedDayNumber,
            v2hcst.created_by as sellerId,
            v2gst.name as classifyLevel1,
            if(v2gct.name is not null,v2gct.name,'-') as classifyLevel2,
            v2hcstr.fee_type_id as feeTypeId,
            v2hcstr.product_id as productId
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
            left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe on v2hcstr.patient_order_id = v2phe.id
            left join ${patientOrderDb}.v2_patientorder v2p on v2hcstr.patient_order_id = v2p.id
            left join ${hisChargeDb}.v2_his_charge_settle v2hcs  on v2hcstr.his_charge_settle_id = v2hcs.id
            left join ${cisGoodsDb}.v2_goods v2g on v2hcstr.product_id = v2g.id
            left join ${cisGoodsDb}.v2_goods_sys_type v2gst on v2g.type_id = v2gst.id
            left join ${cisGoodsDb}.v2_goods_custom_type v2gct on v2g.custom_type_id = v2gct.id
        where
            v2hcstr.chain_id = #{param.chainId}
            and v2hcst.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcstr.clinic_id = #{param.clinicId}
            </if>
            <if test="param.type != null">
                and v2hcst.type = #{param.type}
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.sellerId != null and param.sellerId != ''">
                and v2hcst.created_by = #{param.sellerId}
            </if>
            <if test="param.departmentId != null and param.departmentId != ''">
                and v2phe.department_id = #{param.departmentId}
            </if>
            <if test="param.wardId != null and param.wardId != ''">
                and v2phe.ward_id = #{param.wardId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and v2hcstr.patient_id = #{param.patientId}
            </if>
            <if test="param.productIds != null ">
                and product_id in
                <foreach collection="param.productIds" item="productId" open="(" close=")" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="param.typeId != null">
                and v2g.type_id in
                <foreach collection="param.typeId" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.customTypeSql != null">
                and ${param.customTypeSql}
            </if>
            <!-- 只要收费项目 -->
            and ((v2hcstr.goods_fee_type = 2) or ( v2hcstr.goods_fee_type = 0 and v2hcstr.compose_type != 1))
            <if test="param.feeTypeIds != null and param.feeTypeIds != ''">
                and v2hcstr.fee_type_id in ${param.feeTypeIds}
            </if>
        group by
        v2hcstr.clinic_id,v2hcst.created,v2p.no,v2hcstr.patient_id,v2phe.fee_type_name,v2hcst.type,v2hcs.total_price,v2hcst.pay_mode,v2hcst.pay_sub_mode,v2phe.department_id,v2phe.ward_id,v2phe.inpatient_time,v2phe.discharge_time,v2hcst.created_by,product_type,product_sub_type,v2hcstr.product_id
        order by v2hcst.created desc
        <if test="param.size != null and param.size != 0">
            limit #{param.size}
            offset #{param.offset}
        </if>
    </select>

    <select id="selectTransactionTotal"
            resultType="cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeTotalResp">
        select
            count(1) as count,
            sum(if(typeNumber = 1,netReceipts,0)) as dischargeSettlement,
            sum(if(typeNumber = 2,netReceipts,0)) as cancelSettlement
        from
        (
            select
                v2hcstr.clinic_id as clinicId,
                v2hcst.created as settlementTime,
                v2p.no as admissionNo,
                v2hcstr.patient_id as patientId,
                v2phe.fee_type_name chargeFee,
                v2hcst.type as typeNumber,
                if(v2hcst.type = 1,'出院结算','撤销结算') as type,
                v2hcs.total_price as totalSettlement,
                sum(if(v2hcst.type = 1, v2hcstr.total_price, -1 * v2hcstr.total_price)) as netReceipts,
                v2hcst.pay_mode as payMode,
                v2hcst.pay_sub_mode as paySubMode,
                v2phe.department_id as leaveHospitalDepartmentId,
                v2phe.ward_id as leaveHospitalWardId,
                v2phe.inpatient_time as beHospitalizedTime,
                date_format(v2phe.discharge_time,'%Y-%m-%d') as leaveHospitalTime,
                if(DATE_FORMAT(v2phe.inpatient_time,'%Y-%m-%d') =DATE_FORMAT(v2phe.discharge_time,'%Y-%m-%d'),1,timestampdiff(day,v2phe.inpatient_time,v2phe.discharge_time)) as beHospitalizedDayNumber,
                v2hcst.created_by as sellerId
            from
                ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
                left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
                left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe on v2hcstr.patient_order_id = v2phe.id
                left join ${patientOrderDb}.v2_patientorder v2p on v2hcstr.patient_order_id = v2p.id
                left join ${hisChargeDb}.v2_his_charge_settle v2hcs  on v2hcstr.his_charge_settle_id = v2hcs.id
            where
                v2hcstr.chain_id = #{param.chainId}
                and v2hcst.created between #{param.beginDate} and #{param.endDate}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and v2hcstr.clinic_id = #{param.clinicId}
                </if>
                <if test="param.type != null">
                    and v2hcst.type = #{param.type}
                </if>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.sellerId != null and param.sellerId != ''">
                    and v2hcst.created_by = #{param.sellerId}
                </if>
                <if test="param.departmentId != null and param.departmentId != ''">
                    and v2phe.department_id = #{param.departmentId}
                </if>
                <if test="param.wardId != null and param.wardId != ''">
                    and v2phe.ward_id = #{param.wardId}
                </if>
                <if test="param.patientId != null and param.patientId != ''">
                    and v2hcstr.patient_id = #{param.patientId}
                </if>
                <!-- 只要收费项目 -->
                and ((v2hcstr.goods_fee_type = 2) or ( v2hcstr.goods_fee_type = 0 and v2hcstr.compose_type != 1))
            group by
                v2hcstr.clinic_id,v2hcst.created,v2p.no,v2hcstr.patient_id,v2phe.fee_type_name,v2hcst.type,v2hcs.total_price,v2hcst.pay_mode,v2hcst.pay_sub_mode,v2phe.department_id,v2phe.ward_id,v2phe.inpatient_time,v2phe.discharge_time,v2hcst.created_by,v2hcstr.transaction_id
            order by v2hcst.created desc
        ) a
    </select>

    <select id="selectClassifyTotal"
            resultType="cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeTotalResp">
        select
            count(1) as count,
            sum(if(typeNumber = 1,netReceipts,0)) as dischargeSettlement,
            sum(if(typeNumber = 2,netReceipts,0)) as cancelSettlement
        from (
            select
                v2hcstr.clinic_id as clinicId,
                v2hcst.created as settlementTime,
                v2p.no as admissionNo,
                v2hcstr.patient_id as patientId,
                v2phe.fee_type_name as chargeFee,
                v2hcst.type as typeNumber,
                if(v2hcst.type = 1,'出院结算','撤销结算') as type,
                v2hcs.total_price as totalSettlement,
                sum(if(v2hcst.type = 1, v2hcstr.total_price, -1 * v2hcstr.total_price)) as netReceipts,
                v2hcst.pay_mode as payMode,
                v2hcst.pay_sub_mode as paySubMode,
                v2phe.department_id as leaveHospitalDepartmentId,
                v2phe.ward_id as leaveHospitalWardId,
                v2phe.inpatient_time as beHospitalizedTime,
                date_format(v2phe.discharge_time,'%Y-%m-%d') as leaveHospitalTime,
                if(DATE_FORMAT(v2phe.inpatient_time,'%Y-%m-%d') =DATE_FORMAT(v2phe.discharge_time,'%Y-%m-%d'),1,timestampdiff(day,v2phe.inpatient_time,v2phe.discharge_time)) as beHospitalizedDayNumber,
                v2hcst.created_by as sellerId,
                v2hcstr.fee_type_id as feeTypeId
            from
                ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
                left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
                left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe on v2hcstr.patient_order_id = v2phe.id
                left join ${patientOrderDb}.v2_patientorder v2p on v2hcstr.patient_order_id = v2p.id
                left join ${hisChargeDb}.v2_his_charge_settle v2hcs  on v2hcstr.his_charge_settle_id = v2hcs.id
            where
                v2hcstr.chain_id = #{param.chainId}
                and v2hcst.created between #{param.beginDate} and #{param.endDate}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and v2hcstr.clinic_id = #{param.clinicId}
                </if>
                <if test="param.type != null">
                    and v2hcst.type = #{param.type}
                </if>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.sellerId != null and param.sellerId != ''">
                    and v2hcst.created_by = #{param.sellerId}
                </if>
                <if test="param.departmentId != null and param.departmentId != ''">
                    and v2phe.department_id = #{param.departmentId}
                </if>
                <if test="param.wardId != null and param.wardId != ''">
                    and v2phe.ward_id = #{param.wardId}
                </if>
                <if test="param.patientId != null and param.patientId != ''">
                    and v2hcstr.patient_id = #{param.patientId}
                </if>
                <if test="param.customTypeSql != null">
                    and ${param.customTypeSql}
                </if>
                <!-- 只要收费项目 -->
                and ((v2hcstr.goods_fee_type = 2) or ( v2hcstr.goods_fee_type = 0 and v2hcstr.compose_type != 1))
                <if test="param.feeTypeIds != null and param.feeTypeIds != ''">
                    and v2hcstr.fee_type_id in ${param.feeTypeIds}
                </if>
            group by
                v2hcstr.clinic_id,v2hcst.created,v2p.no,v2hcstr.patient_id,v2phe.fee_type_name,v2hcst.type,v2hcs.total_price,v2hcst.pay_mode,v2hcst.pay_sub_mode,v2phe.department_id,v2phe.ward_id,v2phe.inpatient_time,v2phe.discharge_time,v2hcst.created_by,v2hcstr.fee_type_id
        ) z
    </select>

    <select id="selectDetailTotal"
            resultType="cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeTotalResp">
        select
            count(1) as count,
            sum(if(typeNumber = 1,netReceipts,0)) as dischargeSettlement,
            sum(if(typeNumber = 2,netReceipts,0)) as cancelSettlement
        from(
            select
                v2hcstr.clinic_id as clinicId,
                v2hcst.created as settlementTime,
                v2p.no as admissionNo,
                v2hcstr.patient_id as patientId,
                v2phe.fee_type_name chargeFee,
                v2hcst.type as typeNumber,
                if(v2hcst.type = 1,'出院结算','撤销结算') as type,
                v2hcs.total_price as totalSettlement,
                sum(if(v2hcst.type = 1, v2hcstr.total_price, -1 * v2hcstr.total_price)) as netReceipts,
                v2hcst.pay_mode as payMode,
                v2hcst.pay_sub_mode as paySubMode,
                v2phe.department_id as leaveHospitalDepartmentId,
                v2phe.ward_id as leaveHospitalWardId,
                v2phe.inpatient_time as beHospitalizedTime,
                date_format(v2phe.discharge_time,'%Y-%m-%d') as leaveHospitalTime,
                if(DATE_FORMAT(v2phe.inpatient_time,'%Y-%m-%d') =DATE_FORMAT(v2phe.discharge_time,'%Y-%m-%d'),1,timestampdiff(day,v2phe.inpatient_time,v2phe.discharge_time)) as beHospitalizedDayNumber,
                v2hcst.created_by as sellerId,
                v2gst.name as classifyLevel1,
                if(v2gct.name is not null,v2gct.name,'-') as classifyLevel2,
                v2hcstr.product_id as productId
            from
                ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
                left join ${hisChargeDb}.v2_his_charge_settle_transaction v2hcst on v2hcstr.transaction_id = v2hcst.id
                left join ${patientOrderDb}.v2_patientorder_hospital_extend v2phe on v2hcstr.patient_order_id = v2phe.id
                left join ${patientOrderDb}.v2_patientorder v2p on v2hcstr.patient_order_id = v2p.id
                left join ${hisChargeDb}.v2_his_charge_settle v2hcs  on v2hcstr.his_charge_settle_id = v2hcs.id
                left join ${cisGoodsDb}.v2_goods v2g on v2hcstr.product_id = v2g.id
                left join ${cisGoodsDb}.v2_goods_sys_type v2gst on v2g.type_id = v2gst.id
                left join ${cisGoodsDb}.v2_goods_custom_type v2gct on v2g.custom_type_id = v2gct.id
            where
                v2hcstr.chain_id = #{param.chainId}
                and v2hcst.created between #{param.beginDate} and #{param.endDate}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and v2hcstr.clinic_id = #{param.clinicId}
                </if>
                <if test="param.type != null">
                    and v2hcst.type = #{param.type}
                </if>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.sellerId != null and param.sellerId != ''">
                    and v2hcst.created_by = #{param.sellerId}
                </if>
                <if test="param.departmentId != null and param.departmentId != ''">
                    and v2phe.department_id = #{param.departmentId}
                </if>
                <if test="param.wardId != null and param.wardId != ''">
                    and v2phe.ward_id = #{param.wardId}
                </if>
                <if test="param.patientId != null and param.patientId != ''">
                    and v2hcstr.patient_id = #{param.patientId}
                </if>
                <if test="param.productIds != null ">
                    and product_id in
                    <foreach collection="param.productIds" item="productId" open="(" close=")" separator=",">
                        #{productId}
                    </foreach>
                </if>
                <if test="param.typeId != null">
                    and v2gst.id in
                    <foreach collection="param.typeId" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="param.customTypeSql != null">
                    and ${param.customTypeSql}
                </if>
                <!-- 只要收费项目 -->
                and ((v2hcstr.goods_fee_type = 2) or ( v2hcstr.goods_fee_type = 0 and v2hcstr.compose_type != 1))
                <if test="param.feeTypeIds != null and param.feeTypeIds != ''">
                    and v2hcstr.fee_type_id in ${param.feeTypeIds}
                </if>
            group by
                v2hcstr.clinic_id,v2hcst.created,v2p.no,v2hcstr.patient_id,v2phe.fee_type_name,v2hcst.type,v2hcs.total_price,v2hcst.pay_mode,v2hcst.pay_sub_mode,v2phe.department_id,v2phe.ward_id,v2phe.inpatient_time,v2phe.discharge_time,v2hcst.created_by,product_type,product_sub_type,v2hcstr.product_id
            order by v2hcst.created desc
        ) a
    </select>

    <select id="selectionFeeTypesIdByTransaction"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.pojos.HospitalAchievementChargeType">
        select distinct
            v2gst.id as id,
            v2gst.name as name,
            v2gst.sort as sort,
            v2gct.id as customTypeId,
            v2gct.name as customTypeName,
            v2gct.sort as customTypeSort,
            v2hcstr.product_type as goodsType,
            v2hcstr.product_sub_type as goodsSubType
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
            inner join ${cisGoodsDb}.v2_goods v2g on v2hcstr.product_id = v2g.id
            left join ${cisGoodsDb}.v2_goods_sys_type v2gst on v2g.type_id = v2gst.id
            left join ${cisGoodsDb}.v2_goods_custom_type v2gct on v2g.custom_type_id = v2gct.id
        where
            v2hcstr.chain_id = #{param.chainId}
            and v2hcstr.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcstr.clinic_id = #{param.clinicId}
            </if>
    </select>

    <select id="selectFeeTypeIds"
            resultType="long">
        select distinct
            v2hcstr.fee_type_id
        from
            ${hisChargeDb}.v2_his_charge_settle_transaction_record v2hcstr
        where
            v2hcstr.chain_id = #{param.chainId}
            and v2hcstr.created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and v2hcstr.clinic_id = #{param.clinicId}
            </if>
            and v2hcstr.fee_type_id is not null
    </select>

</mapper>