<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresExecuteBillMapper">

    <select id="selectBillTabProjects"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillProject">
        SELECT
        bill.product_id as productId,
        bill.classify_level_1_id as classifyLevel1Id,
        bill.classify_level_2_id as classifyLevel2Id,
        sum(bill.total_count) as totalCount,
        sum(bill.executed_count) as executedCount,
        sum(bill.received_price) as receivedPrice,
        sum(if(bill.total_count = 0, 0.0, bill.received_price * bill.executed_count/bill.total_count)) as executePrice
        FROM ${db}.dwd_charge_execute_bill_new bill
        <where>
            bill.charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and bill.ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and bill.chain_id=#{param.chainId}
            and bill.classify_level_1_id in ('4-0','4-1','4-2')
            and bill.is_deleted = 0
            and bill.is_closed = 0
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and bill.clinic_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null">
                and bill.product_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY bill.product_id, bill.classify_level_1_id, bill.classify_level_2_id
        order by sum(bill.total_count) desc
        <if test="limit != null">
            limit #{limit}
        </if>
        <if test="offset != null">
            offset #{offset}
        </if>
    </select>

    <select id="selectBillTabProjectSummary"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillProject">
        SELECT
        sum(bill.total_count) as totalCount,
        sum(bill.executed_count) as executedCount,
        sum(bill.received_price) as receivedPrice,
        sum(if(bill.total_count=0, 0.0, bill.received_price * bill.executed_count/bill.total_count)) as executePrice
        FROM ${db}.dwd_charge_execute_bill_new bill
        <where>
            bill.charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and bill.ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and bill.chain_id=#{param.chainId}
            and bill.is_deleted = 0
            and bill.is_closed = 0
            and bill.classify_level_1_id in ('4-0','4-1','4-2')
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and bill.clinic_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size>0">
                and bill.product_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBillTabProjectsTotal"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillProjectTotal">
        select count(a.productCount) as productCount from (
        SELECT
        bill.product_id as productCount,
        sum(bill.total_count) as totalCount,
        sum(bill.executed_count) as executedCount,
        sum(bill.received_price) as receivedPrice
        FROM ${db}.dwd_charge_execute_bill_new bill
        <where>
            bill.charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and bill.ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and bill.chain_id=#{param.chainId}
            and bill.is_deleted = 0
            and bill.is_closed = 0
            and bill.classify_level_1_id in('4-0','4-1','4-2')
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and bill.clinic_id=#{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size>0">
                and bill.product_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY bill.product_id, bill.classify_level_1_id, bill.classify_level_2_id
        ) a
    </select>

    <select id="selectBillTabPerson"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillPerson">
        SELECT
            COALESCE(bill.doctor_id, bill.seller_id) as sellerId,
            bill.doctor_snap_id as doctorSnapId,
            sum(bill.total_count) as totalCount,
            sum(bill.executed_count) as executedCount,
            sum(bill.received_price) as receivedPrice,
            sum(if(bill.total_count=0, 0.0, bill.received_price * bill.executed_count/bill.total_count)) as executePrice
        FROM ${db}.dwd_charge_execute_bill_new bill
        <where>
            bill.charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and bill.ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and bill.chain_id=#{param.chainId}
            and bill.is_deleted = 0
            and bill.is_closed = 0
            and bill.classify_level_1_id in ('4-0','4-1','4-2')
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and bill.clinic_id=#{param.clinicId}
            </if>
            <if test="param.employeeSql != null and param.employeeSql != ''">
                ${param.employeeSql}
            </if>
        </where>
        GROUP BY sellerId, doctorSnapId
        order by sum(bill.total_count) desc
        <if test="limit != null">
            limit #{limit}
        </if>
        <if test="offset != null">
            offset #{offset}
        </if>
    </select>

    <select id="selectBillTabPersonSummary"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillPerson">
        SELECT
        sum(bill.total_count) as totalCount,
        sum(bill.executed_count) as executedCount,
        sum(bill.received_price) as receivedPrice,
        sum(if(bill.total_count=0, 0.0, bill.received_price * bill.executed_count/bill.total_count)) as executePrice
        FROM ${db}.dwd_charge_execute_bill_new bill
        <where>
            bill.charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and bill.ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and bill.chain_id=#{param.chainId}
            and bill.is_deleted = 0
            and bill.is_closed = 0
            and bill.classify_level_1_id in ('4-0','4-1','4-2')
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and bill.clinic_id=#{param.clinicId}
            </if>
            <if test="personIds != null and personIds.size>0">
                and (bill.seller_id in
                <foreach collection="personIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
                or bill.doctor_id in
                <foreach collection="personIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>)
            </if>
        </where>
    </select>

    <select id="selectBillTabPersonTotal"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillPersonTotal">
        select count(a.sellerId) as personCount from (
        SELECT
        COALESCE(bill.doctor_id, bill.seller_id) as sellerId,
        sum(bill.total_count) as totalCount,
        sum(bill.executed_count) as executedCount,
        sum(bill.received_price) as receivedPrice
        FROM ${db}.dwd_charge_execute_bill_new bill
        <where>
            bill.charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and bill.ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and bill.chain_id=#{param.chainId}
            and bill.is_deleted = 0
            and bill.is_closed = 0
            and bill.classify_level_1_id in ('4-0','4-1','4-2')
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and bill.clinic_id=#{param.clinicId}
            </if>
            <if test="personIds != null">
                and (bill.seller_id in
                <foreach collection="personIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
                or bill.doctor_id in
                <foreach collection="personIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>)
            </if>
        </where>
        group by sellerId
        ) a

    </select>

    <select id="listBillDetails"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillDetail">
        SELECT
        bill.patient_id as patientId,
        bill.v2_outpatient_medical_record_id as v2OutpatientMedicalRecordId,
        bill.product_id as productId,
        bill.charge_created as created,
        bill.parent_product_id as parentProductId,
        bill.classify_level_1_id as classifyLevel1Id,
        bill.classify_level_2_id as classifyLevel2Id,
        bill.total_count as totalCount,
        IF(bill.total_count IS NULL, 0.0, bill.total_count - IF(bill.executed_count IS NULL, 0.0, bill.executed_count)) AS remindCount,
        bill.v2_charge_form_item_id as v2ChargeFormItemId,
        bill.v2_therapy_sheet_id as v2TherapySheetId,
        bill.executed_count as executedCount,
        COALESCE(bill.doctor_id, bill.seller_id) as sellerId,
        bill.doctor_snap_id as doctorSnapId,
        bill.last_execute_time as lastExecuteTime,
        bill.last_executor_ids as lastExecutorIds,
        bill.received_price as receivedPrice,
        bill.patient_tag as patientTags,
        bill.department_id as departmentId,
        bill.clinic_id as clinicId
        FROM ${db}.dwd_charge_execute_bill_new bill
        <where>
            bill.charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and bill.ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and bill.chain_id=#{param.chainId}
            and bill.is_deleted = 0
            and bill.is_closed = 0
            and bill.classify_level_1_id in ('4-0','4-1','4-2')
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and bill.clinic_id=#{param.clinicId}
            </if>
            <if test="param.sellType != null and param.sellType == 1">
                and bill.parent_product_id is not null
            </if>
            <if test="param.sellType != null and param.sellType == 0">
                and bill.parent_product_id is null
            </if>
            <if test="param.composeId != null and param.composeId != ''">
                and bill.parent_product_id = #{param.composeId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and bill.patient_id = #{param.patientId}
            </if>
            <if test="param.feeType1 != null and param.feeType2 != null">
                and (${param.feeType1} or ${param.feeType2})
            </if>
            <if test="param.feeType1 != null and param.feeType2 == null">
                and ${param.feeType1}
            </if>
            <if test="param.feeType2 != null and param.feeType1 == null">
                and ${param.feeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size>0">
                and bill.product_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.patientTagSql != null and param.patientTagSql != ''">
                ${param.patientTagSql}
            </if>
            <if test="param.employeeSql != null and param.employeeSql != ''">
                ${param.employeeSql}
            </if>
        </where>
        order by
        <if test="param.orderBy != null and param.orderBy != '' and param.orderType != null and param.orderType != ''">
            ${param.orderBy} ${param.orderType},
        </if>
            bill.charge_created desc
        <if test="param.limit != null">
            limit #{param.limit}
        </if>
        <if test="param.offset != null">
            offset #{param.offset}
        </if>
    </select>

    <select id="selectBillDetailsTotal"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillDetailTotal">
        SELECT
        count(id) as  count,
        sum(total_count) as totalCount,
        sum(executed_count) as executedCount
        FROM ${db}.dwd_charge_execute_bill_new
        <where>
            charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and chain_id=#{param.chainId}
            and is_deleted = 0
            and is_closed = 0
            and classify_level_1_id in ('4-0','4-1','4-2')
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.sellType != null and param.sellType == 1">
                and parent_product_id is not null
            </if>
            <if test="param.sellType != null and param.sellType == 0">
                and parent_product_id is null
            </if>
            <if test="param.composeId != null and param.composeId != ''">
                and parent_product_id = #{param.composeId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id = #{param.patientId}
            </if>
            <if test="param.feeType1 != null and param.feeType2 != null">
                and (${param.feeType1} or ${param.feeType2})
            </if>
            <if test="param.feeType1 != null and param.feeType2 == null">
                and ${param.feeType1}
            </if>
            <if test="param.feeType2 != null and param.feeType1 == null">
                and ${param.feeType2}
            </if>
            <if test="goodsIds != null and goodsIds.size>0">
                and product_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <if test="param.patientTagSql != null and param.patientTagSql != ''">
                ${param.patientTagSql}
            </if>
            <if test="param.employeeSql != null and param.employeeSql != ''">
                ${param.employeeSql}
            </if>
        </where>
    </select>

    <select id="selectBillDetailsKeyData"
            resultType="cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillDetailKeyData">
        SELECT
            count(distinct patient_id) as patientCount,
            sum(total_count) as totalCount,
            sum(executed_count) as executedCount,
            sum(received_price) as receivedPrice,
            sum(execute_price) as executePrice
        from (
            SELECT
                patient_id,
                coalesce(total_count, 0) as total_count,
                coalesce(executed_count,0.0) as executed_count,
                coalesce(received_price, 0.0) as received_price,
                if(total_count is not null and total_count != 0, (if(executed_count is null, 0.0, executed_count)/total_count) * received_price, 0.0) as execute_price
            FROM ${db}.dwd_charge_execute_bill_new
            <where>
                charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
                and chain_id=#{param.chainId}
                and is_deleted = 0
                and is_closed = 0
                and classify_level_1_id in ('4-0','4-1','4-2')
                <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                    and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.sellType != null and param.sellType == 1">
                    and parent_product_id is not null
                </if>
                <if test="param.sellType != null and param.sellType == 0">
                    and parent_product_id is null
                </if>
                <if test="param.composeId != null and param.composeId != ''">
                    and parent_product_id = #{param.composeId}
                </if>
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id = #{param.patientId}
                </if>
                <if test="param.feeType1 != null and param.feeType2 != null">
                    and (${param.feeType1} or ${param.feeType2})
                </if>
                <if test="param.feeType1 != null and param.feeType2 == null">
                    and ${param.feeType1}
                </if>
                <if test="param.feeType2 != null and param.feeType1 == null">
                    and ${param.feeType2}
                </if>
                <if test="goodsIds != null and goodsIds.size>0">
                    and product_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <if test="param.patientTagSql != null and param.patientTagSql != ''">
                    ${param.patientTagSql}
                </if>
                <if test="param.employeeSql != null and param.employeeSql != ''">
                    ${param.employeeSql}
                </if>
            </where>
        ) t1
    </select>

    <select id="selectAvailableClinics" resultType="java.lang.String">
        SELECT
        DISTINCT clinic_id
        FROM ${db}.dwd_charge_execute_bill_new
        <where>
            charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and chain_id=#{param.chainId}
            and is_closed = 0
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
        </where>
    </select>

    <select id="selectAvailableComposeGoods" resultType="java.lang.String">
        SELECT
        DISTINCT parent_product_id
        FROM ${db}.dwd_charge_execute_bill_new
        <where>
            charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and chain_id=#{param.chainId}
            and is_closed = 0
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            and parent_product_id is not null
        </where>
    </select>

    <select id="selectSellType1Count" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM ${db}.dwd_charge_execute_bill_new
        <where>
            parent_product_id is not null
            and is_closed = 0
            and charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and chain_id=#{param.chainId}
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
        </where>
    </select>

    <select id="selectSellType0Count" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM ${db}.dwd_charge_execute_bill_new
        <where>
            parent_product_id is null
            and is_closed = 0
            and charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and chain_id=#{param.chainId}
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
        </where>
    </select>

    <select id="selectAvailablePersons" resultType="cn.abc.flink.stat.pojo.EmployeeResp">
        SELECT
        DISTINCT COALESCE(doctor_id, seller_id) AS id,
        doctor_snap_id AS snapId
        FROM ${db}.dwd_charge_execute_bill_new
        <where>
            charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            and chain_id=#{param.chainId}
            and is_closed = 0
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
        </where>
    </select>
    <!--  济华治疗理疗统计  -->
    <select id="jhExecuteBill" resultType="cn.abc.flink.stat.service.customize.revenue.entity.RevenueDataWithFeeClassifyDao">
        SELECT
            <if test="param.clinicId != null and param.clinicId != ''">
                clinic_id as clinicId,
            </if>
            date(charge_created) as created,
            sum(received_price) as amount -- 总金额
            ,sum(if(doctor_id !='' and doctor_id is not null, received_price, 0.0)) as doctorAmount -- 医生开单金额
            ,sum(if( doctor_id is null or doctor_id ='' , received_price, 0.0)) as sellerAmount -- 理疗师开单金额
            FROM ${env}.dwd_charge_execute_bill_new
        WHERE
        charge_created BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
        and chain_id=#{param.chainId}
            <if test="param.chargeStatus != null and param.chargeStatus != '' ">
                and charge_status in (SELECT CAST(regexp_split_to_table(#{param.chargeStatus}, ',') AS INTEGER))
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            and classify_level_1_id in ('4-0','4-1','4-2')
            and is_deleted = 0
            and is_closed = 0
        GROUP BY
            <if test="param.clinicId != null and param.clinicId != ''">
                clinic_id,
            </if>
            date(charge_created);

    </select>

</mapper>