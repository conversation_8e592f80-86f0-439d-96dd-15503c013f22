<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresPromotionCardItemMapper">

    <!--卡项统计 持卡列表-->
    <select id="selectCardItemList"
            resultType="cn.abc.flink.stat.service.cis.promotion.card.item.domain.PromotionCardItemListEntity">
        select
        patientId,
        cardId,
        cardPatientCardFee as cardPatientCardFee,
        cardDudectIsComplete as cardDudectIsComplete,
        issuingClinicId,
        cardPatientCreated,
        date(cardPatientBeginDate) as cardPatientBeginDate,
        cardValidityPeriod,
        cardValidityType,
        cardValidityPeriodUnit,
        date(cardPatientEndDate) as cardPatientEndDate,
        beginBalancePrincipal,
        beginBalancePresent,
        rechargePrincipal,
        rechargePresent,
        consumePrincipal,
        consumePresent,
        endBalancePrincipal,
        endBalancePresent,
        cardPatientId
        from(
        select
        patient_id AS patientId,
        card_id AS cardId,
        card_patient_card_fee as cardPatientCardFee,
        card_dudect_is_complete as cardDudectIsComplete,
        card_patient_issuing_clinic_id AS issuingClinicId,
        card_patient_created AS cardPatientCreated,
        card_patient_begin_date  AS cardPatientBeginDate,
        card_validity_period as cardValidityPeriod,
        max(card_validity_type) as cardValidityType,
        max(card_validity_period_unit) as cardValidityPeriodUnit,
        card_patient_end_date  AS cardPatientEndDate,
        sum(case when to_char(created, 'YYYY-MM-DD HH24:MI:SS')  &lt;= #{param.beginDate} then case when charge_type in (2,3,5) then  pay_principal*-1 when charge_type in (4,6) then pay_principal else 0.0 end  else 0.0 end) AS beginBalancePrincipal,
        sum(case when to_char(created, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.beginDate} then case when charge_type in (2,3,5) then  pay_present*-1 when charge_type in (4,6) then pay_present  else 0.0 end  else 0.0 end) AS beginBalancePresent,
        sum(case when charge_type =4 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_principal when charge_type =5 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_principal*-1 else 0.0 end) AS
        rechargePrincipal,
        sum(case when charge_type =4 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_present when charge_type =5 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_present*-1 else 0.0 end) AS
        rechargePresent,

        sum(case when charge_type = 3 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_principal when charge_type =6 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_principal*-1 else 0.0 end)
        AS consumePrincipal,
        sum(case when charge_type = 3 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_present when charge_type =6 and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate} then pay_present*-1  else 0.0 end) AS
        consumePresent,
        sum(case when to_char(created, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate} then case when charge_type in (3,5) then pay_principal*-1 when charge_type in (4,6) then pay_principal else 0.0 end else 0.0 end) AS endBalancePrincipal,
        sum(case when to_char(created, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate} then case when charge_type in (3,5) then pay_present*-1 when charge_type in (4,6) then pay_present  else 0.0 end else 0.0 end) AS endBalancePresent,
        card_patient_id AS cardPatientId
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        and card_patient_status = 0
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null and param.cardId!=''">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        <if test="param.sourceId!= null and param.sourceId !=''">
            and
            card_patient_issuing_clinic_id = #{param.sourceId}
        </if>
        <if test="param.cardDudectIsComplete!= null">
            and
            card_dudect_is_complete = #{param.cardDudectIsComplete}
        </if>
        group by
        patient_id,card_id,card_patient_id,card_patient_issuing_clinic_id,card_patient_created,card_patient_begin_date,card_validity_period,card_patient_end_date,card_patient_card_fee,card_dudect_is_complete
        ) t
        order by cardPatientBeginDate,patientId
        <if test="param.offset != null and param.size != null">
            limit #{param.size} offset #{param.offset}
        </if>

    </select>

    <!--卡项统计 持卡列表记录数-->
    <select id="selectCardItemListCount"
            resultType="Long">
        select
        count(1) AS count
        from(
        select
        patient_id AS patientId,
        card_id AS cardId,
        card_patient_issuing_clinic_id AS issuingClinicId,
        card_patient_created AS cardPatientCreated,
        card_patient_begin_date AS cardPatientBeginDate,
        card_validity_period AS cardValidityPeriod,
        card_patient_end_date AS cardPatientEndDate,
        card_patient_id AS cardPatientId
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        and card_patient_status = 0
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null and param.cardId!=''">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        <if test="param.sourceId!= null and param.sourceId !=''">
            and
            card_patient_issuing_clinic_id = #{param.sourceId}
        </if>
        <if test="param.cardDudectIsComplete!= null">
            and
            card_dudect_is_complete = #{param.cardDudectIsComplete}
        </if>
        group by
        patient_id,card_id,card_patient_id,card_patient_issuing_clinic_id,card_patient_created,card_patient_begin_date,card_validity_period,card_patient_end_date
        ) t


    </select>


    <!--卡项统计 持卡列表记汇总-->
    <select id="selectCardItemListSummary"
            resultType="cn.abc.flink.stat.service.cis.promotion.card.item.domain.PromotionCardItemListSummary">
        select
        sum(openCardNum) as openCardNum,
        sum(cardPatientCardFee) as cardPatientCardFee,
        sum(rechargeMoney) as rechargeMoney,
        sum(balanceConsume) as balanceConsume,
        sum(balanceMoney) as balanceMoney
        from(
        select
        sum(case when charge_type= 1  then 1 when charge_type= 2  then -1 else 0 end) AS openCardNum,
        sum(case when charge_type= 1  then pay_money when charge_type= 2 then pay_money*-1  else 0.0 end) as cardPatientCardFee ,
        sum(case when charge_type= 4 and card_patient_status = 0 then pay_money when charge_type = 5 and card_patient_status=0 then pay_money*-1 else 0.0 end) as rechargeMoney,
        sum(case when charge_type= 3 and card_patient_status = 0 then pay_principal+pay_present when charge_type =6 and card_patient_status = 0  then -1*(pay_principal+pay_present) else 0 end) as balanceConsume,
        0 as balanceMoney
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null and param.cardId!=''">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        <if test="param.sourceId!= null and param.sourceId !=''">
            and
            card_patient_issuing_clinic_id = #{param.sourceId}
        </if>
        <if test="param.cardDudectIsComplete!= null">
            and
            card_dudect_is_complete = #{param.cardDudectIsComplete}
        </if>
        and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}
        union all
        select
        0 AS openCardNum,
        0 as cardPatientCardFee ,
        0 as rechargeMoney,
        0 as balanceConsume,
        sum(balanceMoney) as balanceMoney
        from
        (
        select
        created,
        sum(case when to_char(created, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate} then case when charge_type in (3,5) then pay_principal*-1 when charge_type in (4,6) then pay_principal else 0.0 end else 0.0 end) + sum(case when to_char(created, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate} then case when charge_type in (3,5) then pay_present*-1 when charge_type in (4,6) then pay_present  else 0.0 end else 0.0 end) AS balanceMoney
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null and param.cardId!=''">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        <if test="param.sourceId!= null and param.sourceId !=''">
            and
            card_patient_issuing_clinic_id = #{param.sourceId}
        </if>
        <if test="param.cardDudectIsComplete!= null">
            and
            card_dudect_is_complete = #{param.cardDudectIsComplete}
        </if>
        and  card_patient_status = 0
        group by patient_id,card_id,card_patient_id,card_patient_issuing_clinic_id,created,card_patient_begin_date,card_validity_period,card_patient_end_date
        ) m
        ) tmp
    </select>

    <!--卡项统计 查询card_patient_id   -->
    <select id="selectCardPatientIds"
            resultType="Long">
        select
        card_patient_id
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        and card_patient_status = 0
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null and param.cardId!=''">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        <if test="param.sourceId!= null and param.sourceId !=''">
            and
            card_patient_issuing_clinic_id = #{param.sourceId}
        </if>
        and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}
        group by card_patient_id
    </select>

    <!--卡项统计 服务划扣流水-->
    <select id="selectCardItemDeductionTotal"
            resultType="Integer">
        select
        sum(case when deduct_type =1 then deduct_number when deduct_type =2 then deduct_number*-1 else 0 end)  as use_total
        from ${env}.dwd_promotion_card_patient_dudect_record AS dudect
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null ">
            and
            card_id = #{param.cardId}
        </if>
            and
            deduct_type in (2,1)
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}

    </select>

    <!--卡项统计 余额交易流水-->
    <select id="selectCardItemBalancePayList"
            resultType="cn.abc.flink.stat.service.cis.promotion.card.item.domain.PromotionCardItemBalancePayEntity">

        select
        patient_id AS patientId,
        card_id AS cardId,
        charge_patient_id AS chargePatientId,
        to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS payTime,
        clinic_id AS operateClinicId,
        case when charge_type = 3 then '消费'
        when charge_type = 4 then '充值'
        when charge_type = 5 then '充值退费'
        when charge_type = 6 then '消费退费'
        end AS chargeType,
        case when charge_type in (5,3) then pay_money*-1 else pay_money end  AS payMoney,
        case when charge_type in (5,3) then pay_principal*-1 else pay_principal end  AS payPrincipal,
        case when charge_type in (5,3) then pay_present*-1 else pay_present end  AS payPresent,
        principal_balance AS principalBalance,
        present_balance AS presentBalance,
        pay_type as payTypeNo,
        if(seller_id is not null and seller_id != '', seller_id, doctor_id) AS sellerId,
        created_by AS createdBy
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null and param.cardId!=''">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.chargeType == null">
            and
            charge_type in(3,4,5,6)
        </if>
        <if test="param.chargeType!= null and param.chargeType !=0">
            and
            charge_type = #{param.chargeType}
        </if>

        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}
        order by to_char(created, 'YYYY-MM-DD HH24:MI:SS') desc
        <if test="param.offset != null and param.size != null">
            limit #{param.size} offset #{param.offset}
        </if>
    </select>

    <!--卡项统计 余额交易流水记录数-->
    <select id="selectCardItemBalancePayCount"
            resultType="Long">
        select
        count(1) AS count
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null and param.cardId!=''">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.chargeType == null">
            and
            charge_type in(3,4,5,6)
        </if>
        <if test="param.chargeType!= null and param.chargeType !=0">
            and
            charge_type = #{param.chargeType}
        </if>
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}

    </select>


    <!--卡项统计 服务划扣流水-->
    <select id="selectCardItemServiceDeductionList"
            resultType="cn.abc.flink.stat.service.cis.promotion.card.item.domain.PromotionCardItemServiceDeductionEntity">
        select
            patient_id AS patientId,
            card_id AS cardId,
            deduct_patient_id AS chargePatientId,
            to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS payTime,
            clinic_id consumeClinicId,
            case when deduct_type = 1 then '消费抵扣'
            when deduct_type = 2 then '退费'
            end AS chargeType,
            COALESCE(doctor_id,seller_id) AS sellerId,
            doctor_snap_id as doctorSnapId,
            created_by AS createdBy,
            goods_id AS goodsId,
            case when deduct_type = 1 then -1*deduct_number else deduct_number end AS projectNum,
            COALESCE(if(deduct_type=1,abs(discoun_price),discoun_price)::text, '-') as discounPrice,
            equity_total_price as equityTotalPrice
        from
            ${env}.dwd_promotion_card_patient_dudect_record AS dudect
        where
            chain_id = #{param.chainId}
            <if test="param.clinicId!= null and param.clinicId!=''">
                and
                clinic_id = #{param.clinicId}
            </if>
            <if test="param.cardId!= null ">
                and
                card_id = #{param.cardId}
            </if>
            <if test="param.chargeType != null and param.chargeType !=0">
                and
                deduct_type = #{param.chargeType}
            </if>
            <if test="param.chargeType == null or param.chargeType ==0">
                and
                deduct_type in (2,1)
            </if>
            <if test="param.patientId!= null and param.patientId !=''">
                and
                patient_id = #{param.patientId}
            </if>
            <if test="param.searchDoctorSql!= null and param.searchDoctorSql !=''">
                ${param.searchDoctorSql}
            </if>
            <if test="param.goodsId!= null and param.goodsId !=''">
                and goods_id = #{param.goodsId}
            </if>
            and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}
        order by to_char(created, 'YYYY-MM-DD HH24:MI:SS') desc
        <if test="param.offset != null and param.size != null">
            limit #{param.size} offset #{param.offset}
        </if>
    </select>


    <!--卡项统计 服务划扣流水 记录数-->
    <select id="selectCardItemServiceDeductionCount"
            resultType="Long">
        select
        count(1) as cnt
        from ${env}.dwd_promotion_card_patient_dudect_record AS dudect
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        <if test="param.cardId!= null ">
            and
            card_id = #{param.cardId}
        </if>
        <if test="param.chargeType == null">
            and
            deduct_type in (2,1)
        </if>
        <if test="param.chargeType != null and param.chargeType !=0">
            and
            deduct_type = #{param.chargeType}
        </if>
        <if test="param.patientId!= null and param.patientId !=''">
            and
            patient_id = #{param.patientId}
        </if>
        <if test="param.searchDoctorSql!= null and param.searchDoctorSql !=''">
            ${param.searchDoctorSql}
        </if>
        <if test="param.sellerId!= null and param.sellerId !=''">
            and (seller_id = #{param.sellerId} or doctor_id = #{param.sellerId})
        </if>
        <if test="param.goodsId!= null and param.goodsId !=''">
            and goods_id = #{param.goodsId}
        </if>
        and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}

    </select>


    <!--卡项统计 条件 持卡人-->
    <select id="selectCard"
            resultType="java.util.HashMap">
        select
        card_id
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        group by card_id
    </select>


    <!--卡项统计 条件 来源-->
    <select id="selectSource"
            resultType="java.util.HashMap">
        select
        card_patient_issuing_clinic_id AS source_id
        from ${env}.dwd_promotion_card_patient_charge_record
        where
        chain_id = #{param.chainId}
        <if test="param.clinicId!= null and param.clinicId!=''">
            and
            clinic_id = #{param.clinicId}
        </if>
        group by card_patient_issuing_clinic_id
    </select>

    <select id="selectCardItemServiceDeductionAmount"
            resultType="BigDecimal">
        select
            sum(if(deduct_type=1,abs(discoun_price),discoun_price)) as amount
        from
            ${env}.dwd_promotion_card_patient_dudect_record AS dudect
        where
            chain_id = #{param.chainId}
            <if test="param.clinicId!= null and param.clinicId!=''">
                and
                clinic_id = #{param.clinicId}
            </if>
            <if test="param.cardId!= null ">
                and
                card_id = #{param.cardId}
            </if>
            <if test="param.chargeType == null">
                and
                deduct_type in (2,1)
            </if>
            <if test="param.chargeType != null and param.chargeType !=0">
                and
                deduct_type = #{param.chargeType}
            </if>
            <if test="param.patientId!= null and param.patientId !=''">
                and
                patient_id = #{param.patientId}
            </if>
            <if test="param.searchDoctorSql!= null and param.searchDoctorSql !=''">
                ${param.searchDoctorSql}
            </if>
            <if test="param.sellerId!= null and param.sellerId !=''">
                and (seller_id = #{param.sellerId} or doctor_id = #{param.sellerId})
            </if>
            <if test="param.goodsId!= null and param.goodsId !=''">
                and goods_id = #{param.goodsId}
            </if>
            and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}
    </select>

    <select id="selectCardDoctorSnapIds" resultType="cn.abc.flink.stat.pojo.EmployeeResp">
        select
            DISTINCT COALESCE(doctor_id, seller_id) AS id,
            doctor_snap_id AS snapId
        from
            ${env}.dwd_promotion_card_patient_dudect_record AS dudect
        where
            chain_id = #{param.chainId}
            <if test="param.clinicId!= null and param.clinicId!=''">
                and clinic_id = #{param.clinicId}
            </if>
            and to_char(created, 'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate}
            and (doctor_id is not null or seller_id is not null)
    </select>
</mapper>