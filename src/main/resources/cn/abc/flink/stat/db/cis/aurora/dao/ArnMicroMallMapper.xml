<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.ArnMicroMallMapper">

    <select id="selectPromotionOverview"
            resultType="cn.abc.flink.stat.service.cis.micro.mall.domain.MicroMallCouponOverview">
        select
            promotionId                      as promotionId,
            count(distinct a.orderId)         as useCouponOrderNumber,
            sum(a.couponTotalAmount)          as couponTotalAmount,
            sum(a.useCouponChargeTotalAmount) as useCouponChargeTotalAmount
        from (
            select
                use_coupon_charge_total_amount as useCouponChargeTotalAmount,
                sum(discount_price)            as couponTotalAmount,
                order_id                       as orderId,
                promotion_id                   as promotionId
              from
                ${cisTable}.dwd_mall_order_promotion_record
              where
                1 = 1
                <if test="params.chainId != null and params.chainId != ''">
                    and chain_id=#{params.chainId}
                </if>
                <if test="params.promotionIds != null and params.promotionIds.size > 0">
                    and promotion_id in
                    <foreach collection="params.promotionIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
              group by
                    order_id, promotion_id, use_coupon_charge_total_amount
              ) a
        group by promotionId
    </select>

</mapper>