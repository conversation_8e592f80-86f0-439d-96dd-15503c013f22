<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.ArnMemberChargeMapper">

    <select id="getMemberChargeDetail"
            resultType="cn.abc.flink.stat.service.cis.membercharge.domain.MemberChargeDetailInfo">
        select
            member_bill_id as id,
            clinic_id as clinicId,
            member_bill_created as chargeTime,
            patient_id as memberId,
            member_bill_type * member_bill_principal as chargePrincipal,
            member_bill_type * member_bill_present as chargePresent,
            member_bill_seller_user_id as sellerId,
            member_bill_operator_id as tollCollectorId,
            member_bill_remark as remark
        from
            ${db}.dwd_member_bill_detail
        where
            1=1
            and is_deleted = 0
            and member_bill_action in ('充值','退储蓄金')
            and date_index between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
                and member_bill_seller_user_id=#{param.sellerId}
            </if>
            <if test="param.sellerId == '00000000000000000000000000000000'">
                and member_bill_seller_user_id is null

            </if>
        order by member_bill_created desc
        <if test="limit != null and limit != ''">
            limit #{limit}
        </if>
        <if test="offset != null and offset != ''">
            offset #{offset}
        </if>
    </select>

    <select id="getMemberChargeSummary"
            resultType="cn.abc.flink.stat.service.cis.membercharge.domain.MemberChargeSummaryInfo">
        select
        clinic_id as clinicId,
        member_bill_seller_user_id as sellerId, -- 销售员id
        -- 退储蓄金 要算-1次
        sum(if(member_bill_action='充值',1,-1)) as chargeCount,
        sum(member_bill_type * member_bill_principal) as chargePrincipal, -- 充值本金
        sum(member_bill_type * member_bill_present) as chargePresent -- 充值赠金

        from ${db}.dwd_member_bill_detail
        <where>1=1
            and is_deleted = 0
           and member_bill_action in ('充值','退储蓄金')
            and date_index between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
                and member_bill_seller_user_id=#{param.sellerId}
            </if>
            <if test="param.sellerId == '00000000000000000000000000000000'">
                 and member_bill_seller_user_id is null

            </if>
        </where>
        group by clinic_id,member_bill_seller_user_id
        order by sum(member_bill_type * member_bill_principal) desc
        <if test="limit != null and limit != ''">
            limit #{limit}
        </if>
        <if test="offset != null and offset != ''">
            offset #{offset}
        </if>
    </select>



    <select id="getMemberChargeDetailCount"
            resultType="java.lang.Long">
        select
        count(1)
        from ${db}.dwd_member_bill_detail
        <where> 1=1
            and is_deleted = 0
            and member_bill_action in ('充值','退储蓄金')
            and date_index between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
                and member_bill_seller_user_id=#{param.sellerId}
            </if>
            <if test="param.sellerId == '00000000000000000000000000000000'">
                and member_bill_seller_user_id is null

            </if>
        </where>
    </select>

    <select id="getMemberChargeSummaryCount"
            resultType="cn.abc.flink.stat.service.cis.membercharge.domain.MemberChargeSummaryInfo">
        -- 20210922 将统计记录数 与求合计 合并
        select
         sum(chargeCount) as chargeCount
        ,sum(chargePrincipal) as chargePrincipal
        ,sum(chargePresent) as chargePresent
        ,count(1) as totalCount
        from(
        select
        clinic_id as clinicId,
        member_bill_seller_user_id as sellerId, -- 销售员id
        sum(if(member_bill_action='充值',1,-1)) as chargeCount,
        sum(member_bill_type * member_bill_principal) as chargePrincipal, -- 充值本金
        sum(member_bill_type * member_bill_present) as chargePresent -- 充值赠金

        from ${db}.dwd_member_bill_detail
        <where> 1=1
            and is_deleted = 0
            and member_bill_action in ('充值','退储蓄金')
            and date_index between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.sellerId != null and param.sellerId != '' and param.sellerId != '00000000000000000000000000000000'">
                and member_bill_seller_user_id=#{param.sellerId}
            </if>
            <if test="param.sellerId == '00000000000000000000000000000000'">
                and member_bill_seller_user_id is null

            </if>
        </where>
        group by clinic_id,member_bill_seller_user_id
        )t
    </select>

    <select id="getMemberChargeSellers"
            resultType="cn.abc.flink.stat.service.cis.membercharge.domain.SellersInfo">

        select
        if(member_bill_seller_user_id is not null ,member_bill_seller_user_id, '00000000000000000000000000000000') as sellerId
        from ${db}.dwd_member_bill_detail
        <where> 1=1
            and is_deleted = 0
            and member_bill_action in ('充值','退储蓄金')
            and date_index between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
        </where>
        group by if(member_bill_seller_user_id is not null ,member_bill_seller_user_id, '00000000000000000000000000000000')
    </select>

</mapper>