<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.MysqlOutpatientMapper">
    <select id="getPrescriptionCount" resultType="java.lang.Integer">
        SELECT
            sum(ifnull(JSON_EXTRACT(prescription_infos,'$.prescriptionNum'),0))
        FROM
            ${cisDb}.dwd_outpatient
        WHERE
            is_deleted = 0
            and outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
            <if test="param.employeeId != null and param.employeeId != ''">
                and doctor_id = #{param.employeeId}
            </if>
            and outpatient_sheet_status = 1
            and outpatient_sheet_import_flag != 1
            and chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id = #{param.clinicId}
            </if>
    </select>

    <select id="selectRevisitStat" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.RevisitStatRsp">
        SELECT
        ${param.dateSql} AS `date`,
        sum(if(outpatient_sheet_status > 0 and ${param.revisitCol}=0,1,0)) AS firstVisitCount,
        -- 未指定来源的数据
        sum(if(outpatient_sheet_status > 0 and ${param.revisitCol}=0 and visit_source_level_two is null,1,0)) AS notSpecifiedVisitCount,
        sum(if(outpatient_sheet_status > 0 and ${param.revisitCol}=1,1,0)) AS reVisitCount,
        -- 部分收费也算已收费
        sum(if(outpatient_sheet_status > 0 and outpatient_charge_status&lt;1,1,0)) AS dropCount
        FROM ${env}.dwd_outpatient
        <where>
          chain_id = #{param.chainId}
          and outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
          <if test="param.clinicId != null and param.clinicId != ''">
              and clinic_id = #{param.clinicId}
          </if>
          <if test="param.doctorIdIn !=null and param.doctorIdIn != ''">
              and outpatient_sheet_doctor_id in (${param.doctorIdIn})
          </if>
            <if test="param.deptIds !=null and param.deptIds != ''">
                and outpatient_sheet_department_id in (${param.deptIds})
            </if>
        group by ${param.dateSql}
        order by `date`
        <if test="param.pagesize !=null and param.pagesize != ''">
            limit #{param.pagesize} offset #{param.pageindex}
        </if>
        </where>
    </select>

    <select id="selectExpertOutpatientRevisitStat" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.RevisitStatRsp">
        SELECT
        substr(outpatient_sheet_diagnosed_date,1,10) as `date`,
        sum(if(outpatient_sheet_status > 0 and is_doctor_revisit=0,1,0)) AS firstVisitCount,
        sum(if(outpatient_sheet_status > 0 and is_doctor_revisit=1,1,0)) AS reVisitCount,
        -- 部分收费也算已收费
        sum(if(outpatient_sheet_status > 0 and outpatient_charge_status&lt;1,1,0)) AS dropCount
        from ${env}.dwd_outpatient
        where chain_id = #{param.chainId}
        and outpatient_sheet_diagnosed_date BETWEEN  #{param.beginDate} and #{param.endDate}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="departmentId !=null and departmentId != ''">
            and outpatient_sheet_department_id != #{departmentId}
        </if>
        GROUP BY substr(outpatient_sheet_diagnosed_date,1,10)
        ORDER BY `date`
        <if test="pagesize !=null and pagesize != ''">
            limit #{pagesize} offset #{pageindex}
        </if>
    </select>



    <select id="selectVisitSourceStat" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.VisitSourceInfo">
        SELECT
        ${dateSql} AS `date`,
        visit_source_level_two AS sourceId,
        count(1) AS sourceCount
        FROM ${env}.dwd_outpatient
        <where>
            chain_id = #{param.chainId}
            and outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
            and outpatient_sheet_status > 0
            <if test="filterSql != null and filterSql != ''">
                and ${filterSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id = #{param.clinicId}
            </if>
            <if test="param.sourceIds != null and param.sourceIds != ''">
                and visit_source_level_two in (${param.sourceIds})
            </if>
            and visit_source_level_two is not null
        </where>
        group by ${dateSql},visit_source_level_two
    </select>

    <select id="selectPatientList" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.PatientListInfo">
        SELECT
        patient_id AS id,
        max(patient_order_patient_name) AS name,
        max(patient_order_patient_sex) AS sex,
        max(patient_order_patient_age) AS age,
        max(patient_order_patient_mobile) AS mobile,
        max(outpatient_sheet_diagnosed_date) AS lastCreated,
        count(1) AS `count`,
        sum(ifnull(outpatient_charge_amount,0)) as fee,
        #{param.chainId} AS chainId,
        max(patient_order_is_member) isMember
        FROM ${env}.dwd_outpatient
        <where>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id = #{param.chainId}
            </if>
            and outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id = #{param.clinicId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id = #{param.patientId}
            </if>
        </where>
        group by patient_id
        order by lastCreated desc
        <if test="param.pageSize !=null and param.pageSize != ''">
            limit #{param.pageSize} offset #{param.pageIndex}
        </if>
    </select>

    <select id="getPatientListCount" resultType="java.lang.Long">
        select count(a.patient_id) as `count` from (
            SELECT
            patient_id
            FROM ${env}.dwd_outpatient
            <where>
                <if test="param.chainId != null and param.chainId != ''">
                    and chain_id = #{param.chainId}
                </if>
                and outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id = #{param.clinicId}
                </if>
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id = #{param.patientId}
                </if>
            </where>
        group by patient_id) a
    </select>

    <select id="selectOutpatientList" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo">
        SELECT
            <include refid="outpatientListFiled"/>
        FROM ${env}.dwd_outpatient
            <include refid="outpatientListWhere"/>
        order by outpatient_sheet_diagnosed_date desc
        <if test="param.pageSize !=null and param.pageSize != ''">
            limit #{param.pageSize} offset #{param.pageIndex}
        </if>
    </select>

    <select id="selectOutpatientListByIds" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo">
        SELECT
            <include refid="outpatientListFiled"/>
        FROM ${env}.dwd_outpatient
        where outpatient_sheet_diagnosed_date between #{beginDate} and #{endDate}
            and outpatient_sheet_id in
        <foreach collection="ids" item="id" index="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by outpatient_sheet_diagnosed_date desc
    </select>

    <select id="selectOutpatientIds" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientIdDto">
        SELECT
            outpatient_sheet_id AS id,
            outpatient_sheet_diagnosed_date as created
        FROM ${env}.dwd_outpatient
            <include refid="outpatientListWhere"/>
        order by outpatient_sheet_diagnosed_date desc
        <if test="param.pageSize !=null and param.pageSize != ''">
            limit #{param.pageSize} offset #{param.pageIndex}
        </if>
    </select>

    <select id="getOutpatientListCount" resultType="java.lang.Long">
        SELECT
            count(1) AS `count`
        FROM ${env}.dwd_outpatient
            <include refid="outpatientListWhere"/>
    </select>

    <sql id="outpatientListFiled">
            id AS id,
            outpatient_sheet_id AS outpatientSheetId,
            clinic_id AS clinicId,
            outpatient_sheet_doctor_id AS doctorId,
            if(outpatient_sheet_is_online=1,'网诊','门诊') AS `type`,
            patient_id AS patientId,
            patient_order_patient_name AS patientName,
            patient_order_patient_sex AS patientSex,
            patient_order_patient_age AS patientAge,
            patient_order_patient_mobile AS patientMobile,
            if(patient_order_revisit_status is null,'-',if(patient_order_revisit_status=2,'复诊','初诊')) AS diagnoseStatus,
            patient_order_is_member AS isMember,
            chief_complaint AS chiefComplaint,
            past_history AS pastHistory,
            allergic_history AS allergicHistory,
            family_history AS familyHistory, -- 家族史
            personal_history AS personalHistory,
            present_history AS presentHistory,
            epidemiological_history AS epidemiologicalHistory, -- Modified by: libl 添加流行病
            physical_examination AS physicalExamination,
            chinese_examination AS chineseExamination,
            oral_examination AS oralExamination,
            extend_data AS extendData,
            syndrome AS syndrome,
            therapy AS therapy,
            diagnosis AS diagnosis,
            doctor_advice AS doctorAdvice,
            count(1) over(partition by outpatient_sheet_id) `count`,
            ifnull(outpatient_charge_amount,0) AS fee,
            outpatient_sheet_diagnosed_date AS created,
            patient_source_id AS sourceId,
            patient_source_from AS referrerId,
            -- 添加诊号 by：libl
            outpatient_sheet_order_no AS orderNo,
            dentistry_extend as dentistryExtend,
            extend_diagnosis_infos as extendDiagnosisInfos, -- 口腔管家诊断
            auxiliary_examinations as auxiliaryExaminations, -- 辅助检查
            registration_charge_by as registrationChargeBy,
            outpatient_charge_by as outpatientChargeBy,
            revisit_id as revisitId,
            visit_source_level_one as visitSourceOne,
            visit_source_level_two as visitSourceTwo,
            visit_source_from as visitSourceFrom,
            visit_source_from_type as visitSourceFromType,
            visit_source_remark as visitSourceRemark, -- 就诊备注
            wear_glasses_history as wearGlassesHistory, -- 戴镜史
            eye_examination as eyeExamination, -- 眼科检查
            obstetrical_history as obstetricalHistory, -- 月经史
            if (symptom_time is null, '-', date(symptom_time)) as symptomTime, -- 发病时间
            syndrome_treatment as syndromeTreatment,
            department_id as departmentId,
            shebao_charge_type as shebaoChargeTypeNumber,
            outpatient_sheet_copywriter_id as outpatientSheetCopywriterId,
            COALESCE(outpatient_sheet_snap_name ->> '$.doctorName', '') as doctorName,
            patient_order_id as patientOrderId,
            pulse,
            tongue,
            birth_history as birthHistory,
            target,
            prognosis,
            age_lock_time as ageLockTime,
            prescription_form_infos as prescriptionFormInfos,
            prescription_form_item_infos as prescriptionFormItemInfos
    </sql>

    <sql id="outpatientListWhere">
        <where>
            outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
            and patient_order_is_deleted = 0
            and outpatient_sheet_is_deleted = 0
            and outpatient_sheet_status = 1
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id = #{param.chainId}
            </if>
            <if test="param.isInfectiousDiseases != null and param.isInfectiousDiseases == 1">
                and is_infectious_diseases = 1
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id = #{param.clinicId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id = #{param.patientId}
            </if>
            <if test="isOnline != null">
                and outpatient_sheet_is_online = #{isOnline}
            </if>
            <if test="param.employeeId != null and param.employeeId != ''">
                and outpatient_sheet_doctor_id = #{param.employeeId}
            </if>
            <if test="param.searchDoctorSql != null and param.searchDoctorSql != ''">
                ${param.searchDoctorSql}
            </if>
            <if test="param.departmentId != null and param.departmentId != ''">
                and department_id = #{param.departmentId}
            </if>
        </where>
    </sql>

    <select id="selectOutpatientSummary" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientSummaryDao">
        select count(a.patient_order_id) as patientCount,
            sum(a.doctorVisitCount) as doctorVisitCount,
            sum(a.doctorRevisitCount) as doctorRevisitCount
        from (
            select
                patient_order_id,
                count(if(is_doctor_revisit=0, patient_order_id, null)) as doctorVisitCount,
                count(if(is_doctor_revisit=1, patient_order_id, null)) as doctorRevisitCount
            from
                ${env}.dwd_outpatient
            where chain_id = #{chainId}
            and outpatient_sheet_diagnosed_date between #{beginDate} and #{endDate}
            <if test="clinicId != null and clinicId != ''">
                and clinic_id = #{clinicId}
            </if>
            <if test="employeeId != null and employeeId != ''">
                and outpatient_sheet_doctor_id = #{employeeId}
            </if>
        group by patient_order_id) a
    </select>

    <select id="getOutpatientFirstRevisitNum" resultType="cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientFirstRevisitRsp">
        SELECT
        count(outpatient_sheet_id) AS outpatientCount,
        count(if(patient_order_revisit_status = 1,1,null)) AS firstVisitCount,
        count(if(patient_order_revisit_status = 2,1,null)) AS reVisitCount
        FROM ${db}.dwd_outpatient
        <where> 1=1
         and patient_order_is_deleted = 0
         and outpatient_sheet_is_deleted =0
         and outpatient_sheet_import_flag = 0
         and outpatient_sheet_status = 1
         and chain_id = #{chainId}
          and outpatient_sheet_diagnosed_date between #{beginDate} and #{endDate}
          <if test="clinicId != null and clinicId != ''">
              and clinic_id = #{clinicId}
          </if>
          <if test="employeeId !=null and employeeId != ''">
              and outpatient_sheet_doctor_id  = #{employeeId}
          </if>
        </where>
    </select>

    <select id="selectPrescriptionCountByDoctorId" resultType="java.lang.String">
        SELECT
        prescription_infos
        FROM ${db}.dwd_outpatient
        where chain_id = #{chainId}
        and patient_order_is_deleted = 0
        and outpatient_sheet_is_deleted =0
        and outpatient_sheet_import_flag = 0
        and outpatient_sheet_status = 1
        and outpatient_sheet_diagnosed_date between #{beginDate} and #{endDate}
        and prescription_infos is not null
        <if test="clinicId != null and clinicId != ''">
            and clinic_id = #{clinicId}
        </if>
        <if test="employeeId !=null and employeeId != ''">
            and outpatient_sheet_doctor_id  = #{employeeId}
        </if>
    </select>

    <resultMap id="result" type="java.util.Map">
        <!-- 不可缺少 -->
        <result column="outpatient_sheet_id" property="outpatient_sheet_id"></result>
        <collection property="outpatientProductFormItem"  javaType="arrayList" ofType="cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientProductFormItem">
            <result property="outpatient_sheet_id" column="outpatient_sheet_id"></result>
            <result property="tooth_nos" column="tooth_nos"></result>
            <result property="name" column="name"></result>
            <result property="unit_count" column="unit_count"></result>
            <result property="unit" column="unit"></result>
            <result property="extend_data" column="extend_data"></result>
        </collection>
    </resultMap>

    <select id="outpatientProductFormItem" resultMap="result">
        select
            b.outpatient_sheet_id,
            b.tooth_nos,
            b.name,
            b.unit_count,
            b.unit,
            b.extend_data
        from
            ${outpatientDb}.v2_outpatient_product_form_item b
        where
            b.is_deleted=0 and
            b.chain_id=#{chainId}
            <if test="clinicId != null and clinicId != ''">
                and b.clinic_id=#{clinicId}
            </if>
            <if test="hisType != null and hisType == 100">
                and ((b.compose_type = 0 and b.goods_fee_type = 0) or (b.compose_type = 1 and b.goods_fee_type = 0)  or (b.compose_type = 0 and b.goods_fee_type = 1))
            </if>
            and b.outpatient_sheet_id in
            <foreach collection="list" separator="," close=")" open=" (" item="id" >
                #{id}
            </foreach>
    </select>

    <select id="selectArrivalPatientIdList" resultType="java.lang.String">
        select
            distinct patient_id
        from
            ${db}.dwd_outpatient
        where
            chain_id = #{param.chainId}
            and outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
            <if test="param.referralId != null and param.referralId != ''">
                and referral_id = #{param.referralId}
            </if>
    </select>

    <select id="selectOutPatientListByChainIdList" resultType="cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralDaily">
        select
            distinct
            chain_id as chainId,
            patient_id as patientId
        from
            ${db}.dwd_outpatient
        where
            referral_id is not null
            and outpatient_sheet_diagnosed_date between #{param.beginDate} and #{param.endDate}
            <if test="param.chainIdList != null and param.chainIdList.size() > 0">
                and chain_id in
                <foreach collection="param.chainIdList" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
    </select>


    <select id="selectOutpatientPatientIds" resultType="java.lang.String">
        SELECT
        patient_id AS patientId
        FROM ${env}.dwd_outpatient
        where
        outpatient_sheet_diagnosed_date between #{beginDate} and #{endDate}
        and patient_order_is_deleted = 0
        and outpatient_sheet_is_deleted = 0
        and outpatient_sheet_status = 1
        <if test="chainId != null and chainId != ''">
            and chain_id = #{chainId}
        </if>
        <if test="clinicId != null and clinicId != ''">
            and clinic_id = #{clinicId}
        </if>
        <if test="employeeId != null and employeeId != ''">
            and outpatient_sheet_doctor_id = #{employeeId}
        </if>
    </select>
</mapper>
