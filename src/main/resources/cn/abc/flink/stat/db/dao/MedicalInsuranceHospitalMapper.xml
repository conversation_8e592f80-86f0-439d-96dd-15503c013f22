<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.dao.MedicalInsuranceHospitalMapper">

    <select id="selectHospitalPatients"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalPatient">
        select
            basic.id,
            basic.patientId,
            basic.name,
            basic.userCategory,
            basic.personalNo,
            basic.chargeType,
            basic.directDoctorId,
            basic.registerDoctorId,
            basic.registerNurseId,
            basic.hospitalStatus,
            basic.registerTime,
            basic.dischargeTime,
            basic.idCardNo,
            basic.assessmentLevel,
            if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) as duraHospitalDays,
        case
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory !='职工'
          then if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) * 200
        when basic.chargeType != '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) * 50
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) * 200
          else 0 end as duraMedicalServiceFee,
        if(basic.allHospitalDays=0,1,basic.allHospitalDays) as allHospitalDays,

        case
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory !='职工'
          then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
        when basic.chargeType != '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 50
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
        else 0 end as allMedicalServiceFee,
        hs.id as hospitalSheetId,
        hs.total_price as inHospitalFee
        from
        (select
        id,
        chain_id,
        clinic_id,
        patient_id as patientId,
        JSON_UNQUOTE(JSON_EXTRACT(shebao_card_info,'$.name')) as name,
        JSON_UNQUOTE(JSON_EXTRACT(shebao_info,'$.userCategory')) as userCategory,
        JSON_UNQUOTE(JSON_EXTRACT(shebao_card_info,'$.cardNo')) as personalNo,
        JSON_UNQUOTE(JSON_EXTRACT(shebao_card_info,'$.idCardNo')) as idCardNo,
        JSON_UNQUOTE(JSON_EXTRACT(shebao_info,'$.assessmentLevel')) as assessmentLevel,
        charge_type as chargeType,
        direct_doctor_id as directDoctorId,
        register_doctor_id as registerDoctorId,
        register_nurse_id as registerNurseId,
        if(status !=1,'出院','在院') as hospitalStatus,
        DATE_FORMAT(register_time,'%Y-%m-%d') as registerTime,
        DATE_FORMAT(discharge_time,'%Y-%m-%d') as dischargeTime,
        case when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;=#{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d')  &gt;= #{param.endDate} then datediff(#{param.endDate},#{param.beginDate})
        when DATE_FORMAT(register_time,'%Y-%m-%d') &gt; #{param.beginDate} and discharge_time is not null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,register_time)
        when DATE_FORMAT(register_time,'%Y-%m-%d') > #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), register_time)
        when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), #{param.beginDate})
        when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,#{param.beginDate})
        end as duraHospitalDays,
        datediff(ifnull(discharge_time,curdate()),register_time) as allHospitalDays


        from ${patientorderDB}.v2_patientorder_hospital
        where chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patient_id = #{param.patientId}
        </if>
        <if test="param.chargeType != null and param.chargeType != ''">
            and charge_type = #{param.chargeType}
        </if>
        <if test="param.hospitalStatus == 1 ">
            and status = 1
        </if>
        <if test="param.hospitalStatus == 2 ">
            and status != 1
        </if>
        <if test="param.directDoctorId != null and param.directDoctorId != ''">
            and direct_doctor_id = #{param.directDoctorId}
        </if>
        <if test="param.personalType != null and param.personalType != ''">
            and JSON_UNQUOTE(JSON_EXTRACT(shebao_info, '$.userCategory')) like concat("%", #{param.personalType},"%")
        </if>
        and status !=3
        and  (discharge_time &gt;= #{param.beginDate} or discharge_time is null)
        and   register_time &lt;= #{param.endDate}

        ) as basic left join ${chargeDB}.v2_charge_hospital_sheet as hs
        on basic.id = hs.hospital_order_id and basic.chain_id = hs.chain_id and basic.clinic_id =  hs.clinic_id
        where hs.chain_id =#{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and hs.clinic_id = #{param.clinicId}
        </if>
        order by basic.registerTime desc
        <if test="param.offset != null and param.size != null">
            limit #{param.size} offset #{param.offset}
        </if>
    </select>

    <!--患者tab 计算其他成本-->
    <select id="selectTransactionCost"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost">
        select
        po.hospital_patient_order_id as id,
        ifnull(tr.total_cost_price*tr.charge_type,0) costprice
        from ${patientorderDB}.v2_patientorder as po left join ${chargeRecordDb}.v2_charge_transaction_record as tr
        on tr.patient_order_id = po.id and po.chain_id = tr.chain_id
        where po.chain_id = #{chainId}
        and tr.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and po.clinic_id = #{clinicId}
            and tr.clinic_Id = #{clinicId}
        </if>
        and tr.product_type not in (1,2,7)
        and tr.product_type !=11
        and po.hospital_patient_order_id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by po.hospital_patient_order_id
    </select>

    <!--患者tab 计算发药成本-->
    <select id="selectDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost">
        select
        po.hospital_patient_order_id as id,
        sum(if(dl.type=3,1,-1)*ifnull(dft.total_cost_price,0)) as costprice
        from ${patientorderDB}.v2_patientorder as po left join ${dispensingDB}.v2_dispensing_log as dl
        on po.id =dl.patient_order_id and po.chain_id = dl.chain_id
        left join ${dispensingDB}.v2_dispensing_form_item as dft
        on dl.dispensing_form_item_id = dft.id and dl.chain_id = dft.chain_id
        where po.chain_id = #{chainId}
        and dl.chain_id = #{chainId}
        and dft.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and po.clinic_id = #{clinicId}
            and dl.clinic_id = #{clinicId}
            and dft.clinic_id = #{clinicId}
        </if>
        and dft.product_type !=11
        and po.hospital_patient_order_id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by po.hospital_patient_order_id
    </select>

    <!--患者tab 记录数-->
    <select id="selectHospitalPatientsCount" resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCount">
        select
        count(id) as cnt,
        sum(if(status = 1, 1, 0)) as inCount,
        sum(if(status = 2, 1, 0)) as outCount,
        sum(case when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;=#{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d')  &gt;= #{param.endDate} then datediff(#{param.endDate},#{param.beginDate})
            when DATE_FORMAT(register_time,'%Y-%m-%d') &gt; #{param.beginDate} and discharge_time is not null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,register_time)
            when DATE_FORMAT(register_time,'%Y-%m-%d') > #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), register_time)
            when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), #{param.beginDate})
            when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,#{param.beginDate})
        end) as duraHospitalDays,
        sum(datediff(ifnull(discharge_time,curdate()),register_time)) as allHospitalDays
        from ${patientorderDB}.v2_patientorder_hospital
        where chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patient_id = #{param.patientId}
        </if>
        <if test="param.chargeType != null and param.chargeType != ''">
            and charge_type = #{param.chargeType}
        </if>
        <if test="param.hospitalStatus == 1 ">
            and status = 1
        </if>
        <if test="param.hospitalStatus == 2 ">
            and status != 1
        </if>
        <if test="param.directDoctorId != null and param.directDoctorId != ''">
            and direct_doctor_id = #{param.directDoctorId}
        </if>
        <if test="param.personalType != null and param.personalType != ''">
            and JSON_UNQUOTE(JSON_EXTRACT(shebao_info, '$.userCategory')) like concat("%", #{param.personalType},"%")
        </if>
        and status !=3
        and  (discharge_time &gt;= #{param.beginDate} or discharge_time is null)
        and   register_time &lt;= #{param.endDate}
    </select>

    <!--医生tab 责任医生-->
    <select id="selectHospitalDirectDoctor"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">

        select
        tmp.doctorId,
        tmp.role,
        sum(beginPatientTimes) as beginPatientTimes,
        sum(endPatientTimes) as endPatientTimes,
        sum(registerPatientTimes) as registerPatientTimes,
        sum(dischargePatientTimes) as dischargePatientTimes,
        sum(orderTimes) as orderTimes,
        count(tmp.id) as hospitalPatientTimes,
        count(distinct tmp.patient_id) as hospitalPatientCount,
        sum(if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays)) as duraHospitalDays,
        sum(if(tmp.allHospitalDays=0,1,tmp.allHospitalDays)) as allHospitalDays,

        sum(case
            when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory !='职工'
            then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 200
            when tmp.charge_type != '气管切开保留气管套管' and tmp.userCategory ='职工'
            then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 50
            when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory ='职工'
            then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 200
            else 0 end) as duraMedicalServiceFee,
        sum(hs.total_price-hs.received_price) as arrearsAmount,
        sum(hs.received_price) as settlementAmount
        from (select
        ph.direct_doctor_id as doctorId,
        ph.id,
        ph.chain_id,
        ph.charge_type,
        JSON_EXTRACT(ph.shebao_info, '$.userCategory') as userCategory,
        '责任医生' as role,
        ph.patient_id,
        sum(if(  DATE_FORMAT(ph.register_time,'%Y-%m-%d')  =  DATE_FORMAT(#{param.beginDate},'%Y-%m-%d') ,1,0)) as beginPatientTimes,
        sum(if(  DATE_FORMAT(ph.register_time,'%Y-%m-%d')  =  DATE_FORMAT(#{param.endDate},'%Y-%m-%d') ,1,0)) as endPatientTimes,
        sum(if(ph.register_time between #{param.beginDate} and #{param.endDate},1,0)) as registerPatientTimes,
        sum(if(ph.discharge_time between #{param.beginDate} and #{param.endDate},1,0)) as dischargePatientTimes,
        0 as orderTimes,
        sum(
        case when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;=#{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d')  &gt;= #{param.endDate} then datediff(#{param.endDate},#{param.beginDate})
        when DATE_FORMAT(register_time,'%Y-%m-%d') &gt; #{param.beginDate} and discharge_time is not null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,register_time)
        when DATE_FORMAT(register_time,'%Y-%m-%d') > #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), register_time)
        when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), #{param.beginDate})
        when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,#{param.beginDate})
        end
        ) as duraHospitalDays,
        sum(datediff(ifnull(discharge_time,curdate()),register_time)) as allHospitalDays
        from ${patientorderDB}.v2_patientorder_hospital as ph
        where !(#{param.beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{param.endDate} &lt; ph.register_time)
        and ph.status !=3
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
        <if test="param.doctorId != null and param.doctorId != ''">
            and ph.direct_doctor_id = #{param.doctorId}
        </if>
        group by ph.direct_doctor_id,ph.id,ph.charge_type,JSON_EXTRACT(ph.shebao_info, '$.userCategory'),ph.patient_id,ph.chain_id
        ) tmp
        left join ${chargeDB}.v2_charge_hospital_sheet as hs
        on tmp.id = hs.hospital_order_id and tmp.chain_id = hs.chain_id
        group by tmp.doctorId, tmp.role

    </select>

    <!--医生tab 登记医生-->
    <select id="selectHospitalRegisterDoctor"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">




        select
        tmp.doctorId,
        tmp.role,
        sum(beginPatientTimes) as beginPatientTimes,
        sum(endPatientTimes) as endPatientTimes,
        sum(registerPatientTimes) as registerPatientTimes,
        sum(dischargePatientTimes) as dischargePatientTimes,
        sum(orderTimes) as orderTimes,
        count(tmp.id) as hospitalPatientTimes,
        count(distinct tmp.patient_id) as hospitalPatientCount,
        sum(if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays)) as duraHospitalDays,
        sum(if(tmp.allHospitalDays=0,1,tmp.allHospitalDays)) as allHospitalDays,

        sum(case
        when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory !='职工'
        then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 200
        when tmp.charge_type != '气管切开保留气管套管' and tmp.userCategory ='职工'
        then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 50
        when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory ='职工'
        then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays)* 200
        else 0 end) as duraMedicalServiceFee,
        sum(hs.total_price-hs.received_price) as arrearsAmount,
        sum(hs.received_price) as settlementAmount
        from (select
        ph.register_doctor_id as doctorId,
        ph.id,
        ph.chain_id,
        ph.charge_type,
        JSON_EXTRACT(ph.shebao_info, '$.userCategory') as userCategory,
        '登记医生' as role,
        ph.patient_id,
        sum(if(  DATE_FORMAT(ph.register_time,'%Y-%m-%d')  =  DATE_FORMAT(#{param.beginDate},'%Y-%m-%d') ,1,0)) as beginPatientTimes,
        sum(if(  DATE_FORMAT(ph.register_time,'%Y-%m-%d')  =  DATE_FORMAT(#{param.endDate},'%Y-%m-%d') ,1,0)) as endPatientTimes,
        sum(if(ph.register_time between #{param.beginDate} and #{param.endDate},1,0)) as registerPatientTimes,
        sum(if(ph.discharge_time between #{param.beginDate} and #{param.endDate},1,0)) as dischargePatientTimes,
        0 as orderTimes,
        sum(
        case when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;=#{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d')  &gt;= #{param.endDate} then datediff(#{param.endDate},#{param.beginDate})
        when DATE_FORMAT(register_time,'%Y-%m-%d') &gt; #{param.beginDate} and discharge_time is not null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,register_time)
        when DATE_FORMAT(register_time,'%Y-%m-%d') > #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), register_time)
        when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and  ( discharge_time is null or  DATE_FORMAT(discharge_time,'%Y-%m-%d') &gt;= #{param.endDate})  then datediff(if(discharge_time is null and DATE_FORMAT(#{param.endDate},'%Y-%m-%d') = curdate() ,curdate(),#{param.endDate}), #{param.beginDate})
        when DATE_FORMAT(register_time,'%Y-%m-%d') &lt;= #{param.beginDate} and discharge_time is not  null and  DATE_FORMAT(discharge_time,'%Y-%m-%d') &lt; #{param.endDate} then datediff(discharge_time,#{param.beginDate})
        end
        ) as duraHospitalDays,
        sum(datediff(ifnull(discharge_time,curdate()),register_time)) as allHospitalDays
        from ${patientorderDB}.v2_patientorder_hospital as ph
        where !(#{param.beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{param.endDate} &lt; ph.register_time)
        and ph.status !=3
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
        <if test="param.doctorId != null and param.doctorId != ''">
            and ph.register_doctor_id = #{param.doctorId}
        </if>
        group by ph.register_doctor_id,ph.id,ph.charge_type,JSON_EXTRACT(ph.shebao_info, '$.userCategory'),ph.patient_id,ph.chain_id
        ) tmp
        left join ${chargeDB}.v2_charge_hospital_sheet as hs
        on tmp.id = hs.hospital_order_id and tmp.chain_id = hs.chain_id
        group by tmp.doctorId,tmp.role

    </select>

    <!--医生tab 开单医生-->
    <select id="selectHospitalOrderDoctor"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">
        select
        os.doctor_id as doctorId,
        '开单医生' as role,
        count(os.patient_order_id) as orderTimes,
        count(distinct ph.id) as hospitalPatientTimes,
        count(distinct ph.patient_id) as hospitalPatientCount
        from ${patientorderDB}.v2_patientorder_hospital as ph left join ${outpatientDB}.v2_outpatient_sheet as os
        on ph.id = os.hospital_patient_order_id
        where !(#{param.beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{param.endDate} &lt; ph.register_time)
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
        <if test="param.doctorId != null and param.doctorId != ''">
            and os.doctor_id = #{param.doctorId}
        </if>
        and os.doctor_id is not null
        and ph.status !=3
        group by os.doctor_id;
    </select>

    <!--医生tab-汇总-->
    <select id="selectHospitalSummary"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">
        select
        count(ph.id) as hospitalPatientTimes,
        count(distinct ph.patient_id) as hospitalPatientCount,
        sum(datediff(ifnull(ph.discharge_time, curdate()), ph.register_time)) as duraHospitalDays,
        sum(hs.total_price-hs.received_price) as arrearsAmount,
        sum(hs.received_price) as settlementAmount
        from ${patientorderDB}.v2_patientorder_hospital as ph left join ${chargeDB}.v2_charge_hospital_sheet as hs
        on ph.id = hs.hospital_order_id and ph.chain_id = hs.chain_id
        where !(#{param.beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{param.endDate} &lt; ph.register_time)
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
    </select>

    <!--筛选框-责任医生-->
    <select id="selectConditionDirectDoctor"
            resultType="java.lang.String">
        select
        direct_doctor_id
        from ${patientorderDB}.v2_patientorder_hospital as ph
        where !(#{beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{endDate} &lt; ph.register_time)
        and ph.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and ph.clinic_id = #{clinicId}
        </if>
        group by ph.direct_doctor_id;
    </select>

    <!--筛选框-结算类型-->
    <select id="selectConditionChargeType"
            resultType="java.lang.String">
        select
        charge_type
        from ${patientorderDB}.v2_patientorder_hospital as ph
        where !(#{beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{endDate} &lt; ph.register_time)
        and ph.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and ph.clinic_id = #{clinicId}
        </if>
        group by ph.charge_type;
    </select>

    <!--筛选框-个人类别-->
    <select id="selectPersonalType"
            resultType="java.lang.String">
        select left(a.userCategory,2) from (
            select
                JSON_UNQUOTE(JSON_EXTRACT(shebao_info, '$.userCategory')) as userCategory
            from ${patientorderDB}.v2_patientorder_hospital as ph
            where !(#{beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{endDate} &lt; ph.register_time)
                and ph.chain_id = #{chainId}
                <if test="clinicId != null and clinicId != ''">
                    and ph.clinic_id = #{clinicId}
                </if>
        ) a
        where userCategory != "" and userCategory != "null"
        group by left(a.userCategory,2)
    </select>

    <select id="selectHospitalPatientOrderIds" resultType="java.lang.Long">
        select
        distinct ph.id
        from ${patientorderDB}.v2_patientorder_hospital as ph
        where !(#{beginDate} &gt; ifNull(ph.discharge_time,'9999-99-99') or #{endDate} &lt; ph.register_time)
        and ph.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and ph.clinic_id = #{clinicId}
        </if>
        and ph.status !=3

    </select>
    <select id="selectHospitalPatientsSummy"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary">
        select
               sum(b.allMedicalServiceFee) as allMedicalServiceFee,
               sum(b.outHospitalStatus) as outHospitalStatus,
               sum(b.inHospitalStatus) as inHospitalStatus,
               sum(b.allHospitalDays) as allHospitalDays,
               min(register_time) as minDate
        from (
        select
            basic.id,
            case
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and find_in_set(basic.chargeType,'院护(失智),院护(失能),家护(失能),日护(失智),日护(失能)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 50
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and find_in_set(basic.chargeType,'院护(门诊慢特病),家护(门诊慢特病),日护(门诊慢特病)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 9.59
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and find_in_set(basic.chargeType,'院护(气管切开保留气管套管),家护(气管切开保留气管套管)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and find_in_set(basic.chargeType,'专护(过渡期)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 0

            when basic.userCategory = '居民' and find_in_set(basic.chargeType,'院护(失智),院护(失能),家护(失能),日护(失智),日护(失能)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 13.7
            when basic.userCategory = '居民' and find_in_set(basic.chargeType,'院护(门诊慢特病),家护(门诊慢特病),日护(门诊慢特病)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 8.22
            when basic.userCategory = '居民' and find_in_set(basic.chargeType,'院护(气管切开保留气管套管),家护(气管切开保留气管套管)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
            when basic.userCategory = '居民' and find_in_set(basic.chargeType,'专护(过渡期)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 0

            when basic.userCategory = '离休' and find_in_set(basic.chargeType,'院护(失智),院护(失能),家护(失能),日护(失智),日护(失能)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 80
            when basic.userCategory = '离休' and find_in_set(basic.chargeType,'院护(门诊慢特病),家护(门诊慢特病),日护(门诊慢特病)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * if(basic.assessmentLevel = '4' or
            basic.assessmentLevel = '5',16.44,8.22)
            when basic.userCategory = '离休' and find_in_set(basic.chargeType,'院护(气管切开保留气管套管),家护(气管切开保留气管套管)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
            when basic.userCategory = '离休' and find_in_set(basic.chargeType,'专护(过渡期)') != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 0
            else 0 end as allMedicalServiceFee,
            ifnull(basic.outHospitalStatus,0.0) as outHospitalStatus,
            ifnull(basic.inHospitalStatus,0.0) as inHospitalStatus,
            ifnull(basic.allHospitalDays,0.0) as allHospitalDays,
            register_time
        from
        (select
            id,
            chain_id,
            clinic_id,
            register_time,
            left(ifnull(JSON_UNQUOTE(JSON_EXTRACT(shebao_info,'$.userCategory')),''),2) as userCategory,
            JSON_UNQUOTE(JSON_EXTRACT(shebao_info,'$.assessmentLevel')) as assessmentLevel,
            charge_type as chargeType,
            if(status !=1,1,0) as outHospitalStatus,
            if(status = 1,1,0) as inHospitalStatus,
            datediff(ifnull(discharge_time,curdate()),register_time) as allHospitalDays
            from ${patientorderDB}.v2_patientorder_hospital a
            where chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id = #{param.clinicId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id = #{param.patientId}
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                and charge_type = #{param.chargeType}
            </if>
            <if test="param.hospitalStatus == 1 ">
                and status = 1
            </if>
            <if test="param.hospitalStatus == 2 ">
                and status != 1
            </if>
            <if test="param.directDoctorId != null and param.directDoctorId != ''">
                and direct_doctor_id = #{param.directDoctorId}
            </if>
            <if test="param.personalType != null and param.personalType != ''">
                and JSON_UNQUOTE(JSON_EXTRACT(shebao_info, '$.userCategory')) like concat("%", #{param.personalType},"%")
            </if>
            and status !=3
            and  (discharge_time &gt;= #{param.beginDate} or discharge_time is null)
            and   register_time &lt;= #{param.endDate}
        ) as basic
        ) b
    </select>

    <select id="selectHospitalPatientsIds" resultType="java.lang.String">
        select
        id
        from ${patientorderDB}.v2_patientorder_hospital a
        where chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patient_id = #{param.patientId}
        </if>
        <if test="param.chargeType != null and param.chargeType != ''">
            and charge_type = #{param.chargeType}
        </if>
        <if test="param.hospitalStatus == 1 ">
            and status = 1
        </if>
        <if test="param.hospitalStatus == 2 ">
            and status != 1
        </if>
        <if test="param.directDoctorId != null and param.directDoctorId != ''">
            and direct_doctor_id = #{param.directDoctorId}
        </if>
        <if test="param.personalType != null and param.personalType != ''">
            and JSON_UNQUOTE(JSON_EXTRACT(shebao_info, '$.userCategory')) like concat("%", #{param.personalType},"%")
        </if>
        and status !=3
        and  (discharge_time &gt;= #{param.beginDate} or discharge_time is null)
        and   register_time &lt;= #{param.endDate}
    </select>

    <select id="selectExternalDiagnosis"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary">
        select sum(if(charged_time between #{params.beginDate} and #{params.endDate} ,total_fee, 0)) as partExternalDiagnosisFee,
               sum(total_fee) as externalDiagnosisFee
        from ${shebaodb}.shebao_external_outpatient_sheet
        where
          is_deleted = 0
          and chain_id = #{params.chainId}
          <if test="params.clinicId != null and params.clinicId != ''">
              and clinic_id = #{params.clinicId}
          </if>
          and status in (1, 3)
          and hospital_order_id in
          <foreach collection="ids" item="id" open="(" separator="," close=")">
              #{id}
          </foreach>
    </select>

    <select id="selectTotalSettlementAmount" resultType="java.lang.Double">
        select
            sum(ifnull(b.ckh917, 0) + ifnull(ckh998, 0))
        from ${shebaodb}.shebao_qingdao_longcare_register a inner join
             ${shebaodb}.shebao_qingdao_longcare_payment_result b on a.payment_result_id = b.id
        where a.chain_id = #{params.chainId}
            <if test="params.clinicId != null and params.clinicId != ''">
                and a.clinic_id = #{params.clinicId}
            </if>
            and a.status = 3
            and a.his_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

    <select id="selectPendingSettlementAmount" resultType="java.lang.Double">
        select sum(a.received_price) from (
            select  d.shebao_code_national_code,
                    CASE
                    WHEN INSTR(JSON_UNQUOTE(JSON_EXTRACT(e.shebao_info, '$.userCategory')), '在职') > 0
                    OR INSTR(JSON_UNQUOTE(JSON_EXTRACT(e.shebao_info, '$.userCategory')), '退休') > 0 THEN '310'
                    WHEN INSTR(JSON_UNQUOTE(JSON_EXTRACT(e.shebao_info, '$.userCategory')), '离休') > 0 THEN '330'
                    WHEN INSTR(JSON_UNQUOTE(JSON_EXTRACT(e.shebao_info, '$.userCategory')), '居民') > 0
                    OR INSTR(JSON_UNQUOTE(JSON_EXTRACT(e.shebao_info, '$.userCategory')), '其他') > 0 THEN '390'
                    ELSE '310'
                    END AS code,
                    sum(c.received_price) as received_price
            from ${patientorderDb}.v2_patientorder_hospital e
                inner join ${chargeDb}.v2_charge_hospital_sheet a on a.hospital_order_id = e.id
                inner join ${chargeDb}.v2_charge_sheet b on a.clinic_id = b.clinic_id and a.id = b.hospital_sheet_id and b.status in (2,3)
                inner join ${chargeRecordDb}.v2_charge_form_item c on b.id = c.charge_sheet_id and c.is_deleted = 0 and c.status = 1 and c.compose_type != 1 and c.goods_fee_type != 1
                inner join ${goodsDb}.v2_goods_extend d on c.chain_id = d.chain_id and c.product_id = d.goods_id and d.shebao_code_national_code is not null
            where a.hospital_order_id in
                  <foreach collection="ids" index="id" item="id" open="(" separator="," close=")">
                      #{id}
                  </foreach>
                  and a.is_deleted = 0 group by shebao_code_national_code, code) a
            inner join ${shebaoDb}.shebao_national_dict_self_pay b on a.shebao_code_national_code = b.hilist_code and a.code = b.selfpay_prop_psn_type and b.region = 'shandong_qingdao'
        where b.selfpay_prop &lt; 1
    </select>

    <select id="selectTotalFee"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary">
        select sum(total_price) as inHospitalFee
        from ${chargedb}.v2_charge_hospital_sheet
        where
          chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        and hospital_order_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectPartInHospitalFee"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary">
        select sum(if(a.status = 1, ifnull(a.received_price, 0), ifnull(a.received_price, 0) * -1)) as partInHospitalFee
        from ${chargedb}.v2_charge_form_item a
             inner join ${chargedb}.v2_charge_sheet b
             on a.charge_sheet_id = b.id and b.is_deleted = 0
        where b.hospital_sheet_id is not null
          and a.chain_id = #{params.chainId}
          and b.chain_id = #{params.chainId}
          <if test="params.clinicId != null and params.clinicId != ''">
              and a.clinic_id = #{params.clinicId}
              and b.clinic_id = #{params.clinicId}
          </if>
          and b.is_deleted = 0
          and a.status in (1, 2)
          and a.is_deleted = 0
          and a.created between #{params.beginDate} and #{params.endDate}
          and b.hospital_order_id in
          <foreach collection="ids" item="id" open="(" separator="," close=")">
              #{id}
          </foreach>
    </select>
</mapper>