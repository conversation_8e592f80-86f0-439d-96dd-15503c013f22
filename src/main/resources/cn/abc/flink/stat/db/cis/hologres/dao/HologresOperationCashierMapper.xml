<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresOperationCashierMapper">

    <select id="fetchPayMode" parameterType="java.lang.String"
            resultType="cn.abc.flink.stat.service.cis.operation.domain.CashierPayMode">
    SELECT
        <choose>
            <when test="group=='cashier'">
                concat(clinicId,'-',cashierId) as pk,
                clinicId,
                cashierId,
            </when>
            <when test="group=='clinic'">
                clinicId as pk,
                clinicId,
            </when>
            <when test="group=='date'">
                pk,
                clinicId,
            </when>
        </choose>
        payMode,
        paySubMode,
        sum(value) as value,
        sum(tatolValue) as tatolValue,
        sum(principal) as principal,
        sum(present) as present,
        recordSourceType
    FROM
    (
        SELECT
        <choose>
            <when test="group=='cashier'">
                clinic_id as clinicId,
                cashier_id as cashierId,
            </when>
            <when test="group=='clinic'">
                clinic_id as clinicId,
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10) as pk,
                #{params.clinicId} as clinicId,
            </when>
        </choose>
        if(pay_type is null, -999, pay_type) as payMode,
        -- 将微信直付都处理为subtype=1,0 和null subtype=0（微信记账）
        if(pay_type=2,if(pay_sub_type is null or pay_sub_type=0,0,1),
            if(pay_type = 19 and pay_sub_type in (5,6,7,12), 12,
                if(pay_type = 19 and pay_sub_type in (8, 13), 13,
                    if(pay_type = 19 and pay_sub_type in (10,14), 14, pay_sub_type)))) as paySubMode,
        v2_transaction_id                         as transactionId,
        sum(received_price * if(type=-1, -1, 1))  as value,
        sum(received_price * if(type=1, 1, 0))  as tatolValue,
        sum(if(type=-1, -1, 1) * record_member_principal)                          as principal,
        sum(if(type=-1, -1, 1) * record_member_present)                            as present,
        record_source_type as recordSourceType
    FROM ${cisTable}.dwd_charge_transaction_record_v_partition
    WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and chain_id = #{params.chainId}
        and import_flag = 0
        and is_deleted = 0
        and pay_type != 100
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test='params.sellerId != null and params.sellerId != ""'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test="params.hisType != null and params.hisType == 100">
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                <!-- 只要收费项目，剔除套餐母项 -->
                and goods_fee_type in (0,2) and product_compose_type != 1
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
            </if>
        </if>
        <if test="params.hisType != null and params.hisType != 100">
            and goods_fee_type in(0,1)
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
        </if>
        <if test="params.isChargeDaily == 1">
            <if test="params.arrearsStatTiming == 1">
                and record_source_type != 2
            </if>
            <if test="params.arrearsStatTiming == 2">
                and record_source_type != 1
            </if>
        </if>
        <if test="params.payModeSql != null and params.payModeSql !=''">
            and (${params.payModeSql})
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.includeReg != null and params.includeReg == 0">
            and product_type!=5
        </if>
        <if test="params.feeTypeIds != null and params.feeTypeIds != ''">
            and ${params.feeTypeIds}
        </if>
        <if test="params.cashierSql != null and params.cashierSql != ''">
            and ${params.cashierSql}
        </if>
    GROUP BY
        <choose>
            <when test="group=='cashier'">
                clinic_id,cashier_id,payMode, paySubMode,transactionId,record_source_type
            </when>
            <when test="group=='clinic'">
                clinic_id,payMode, paySubMode,transactionId,record_source_type
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10),payMode, paySubMode,transactionId,record_source_type
            </when>
        </choose>
        ) t
    GROUP BY
        <choose>
            <when test="group=='cashier'">
                clinicId,cashierId,payMode, paySubMode,recordSourceType;
            </when>
            <when test="group=='clinic'">
                clinicId,payMode, paySubMode,recordSourceType;
            </when>
            <when test="group=='date'">
                pk,clinicId,payMode, paySubMode,recordSourceType;
            </when>
        </choose>
    </select>

    <select id="fetchChargeDailyPayMode" parameterType="java.lang.String" resultType="cn.abc.flink.stat.service.cis.operation.domain.CashierPayMode">
        SELECT
            clinicId as pk,
            clinicId,
            payMode,
            paySubMode,
            sum(value) as value,
            sum(tatolValue) as tatolValue,
            sum(principal) as principal,
            sum(present) as present
        FROM
        (
            SELECT
                clinic_id as clinicId,
                if(pay_type is null, -999, pay_type) as payMode,
                if(pay_type=2,if(pay_sub_type is null or pay_sub_type=0,0,1),
                if(pay_type = 19 and pay_sub_type in (5,6,7,12), 12,
                if(pay_type = 19 and pay_sub_type in (8, 13), 13,
                if(pay_type = 19 and pay_sub_type in (10,14), 14, pay_sub_type)))) as paySubMode,
                v2_transaction_id                         as transactionId,
                sum(received_price * if(type=-1, -1, 1))  as value,
                sum(received_price * if(type=1, 1, 0))  as tatolValue,
                sum(if(type=-1, -1, 1) * record_member_principal)                          as principal,
                sum(if(type=-1, -1, 1) * record_member_present)                            as present
            FROM ${cisTable}.dwd_charge_transaction_record_v_partition
            WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and chain_id = #{params.chainId}
            and import_flag = 0
            and is_deleted = 0
            and pay_type != 100
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.sellerId != null and params.sellerId == '00000000000000000000000000000000'">
                and (cashier_id is null or cashier_id = '' or cashier = '00000000000000000000000000000000')
            </if>
            <if test="params.sellerId != null and params.sellerId != '' and params.sellerId != '00000000000000000000000000000000'">
                and cashier_id = #{params.sellerId}
            </if>
            <if test="params.cashierSql != null and params.cashierSql != ''">
                and ${params.cashierSql}
            </if>
            <if test="params.hisType != null and params.hisType == 100">
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    and goods_fee_type in (0,2) and product_compose_type != 1
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
                </if>
            </if>
            <if test="params.hisType != null and params.hisType != 100">
                and goods_fee_type in(0,1)
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    and product_compose_type in (0,2)
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    and product_compose_type in (0,1)
                </if>
            </if>
            <if test="params.arrearsStatTiming == 1">
                and record_source_type != 2
            </if>
            <if test="params.arrearsStatTiming == 2">
                and record_source_type != 1
            </if>
            GROUP BY clinic_id,payMode, paySubMode,transactionId
        ) t
        GROUP BY clinicId,payMode, paySubMode;
    </select>

    <select id="getRepaymentOverall" parameterType="java.lang.String"
            resultType="cn.abc.flink.stat.service.cis.operation.domain.CashierOverall">
            SELECT
            <choose>
                <when test="group=='cashier'">
                    concat(clinic_id,'-',cashier_id) as pk,
                    clinic_id as clinicId,
                    cashier_id as cashierId,
                </when>
                <when test="group=='clinic'">
                    clinic_id as pk,
                    clinic_id as clinicId,
                </when>
                <when test="group=='date'">
                    substr(cast(create_time as VARCHAR),1,10) as pk,
                    #{params.clinicId} as clinicId,
                </when>
            </choose>
            sum(if(type=-1, -1 * received_price, 0.0))  as outcome,
            sum(if(type in(0,1), received_price, 0.0))  as income
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and chain_id = #{params.chainId}
            and import_flag = 0
            and is_deleted = 0
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test='params.sellerId != null and params.sellerId != ""'>
                and cashier_id = #{params.sellerId}
            </if>
            <if test="params.hisType != null and params.hisType == 100">
                <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    <!-- 只要收费项目，剔除套餐母项 -->
                    and goods_fee_type in (0,2) and product_compose_type != 1
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
                </if>
            </if>
            <if test="params.hisType != null and params.hisType != 100">
                and goods_fee_type in(0,1)
                <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    and product_compose_type in (0,2)
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    and product_compose_type in (0,1)
                </if>
            </if>
            and record_source_type = 2
            <if test="params.payModeSql != null and params.payModeSql !=''">
                and (${params.payModeSql})
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee2 != null and params.fee1 == null">
                and ${params.fee2}
            </if>
            <if test="params.includeReg != null and params.includeReg == 0">
                and product_type!=5
            </if>
            <if test="params.feeTypeIds != null and params.feeTypeIds != ''">
                and ${params.feeTypeIds}
            </if>
            <if test="params.cashierSql != null and params.cashierSql != ''">
                and ${params.cashierSql}
            </if>
        GROUP BY
            <choose>
                <when test="group=='cashier'">
                    clinic_id,cashier_id
                </when>
                <when test="group=='clinic'">
                    clinic_id
                </when>
                <when test="group=='date'">
                    substr(cast(create_time as VARCHAR),1,10)
                </when>
            </choose>
    </select>

    <select id="fetchMember" parameterType="java.lang.String"
            resultType="cn.abc.flink.stat.service.cis.operation.domain.CashierPayMode">
        SELECT
        t.pk as pk,
        6 as payMode,
        sum(principalRepayment) as principalRepayment,
        sum(presentRepayment) as presentRepayment,
        sum(member_principal) as principal,
        sum(member_present) as present
        FROM
        (SELECT
        <choose>
            <when test="group=='cashier'">
                concat(clinic_id,'-',cashier_id) as pk,
                clinic_id as clinicId,
                cashier_id as cashierId,
            </when>
            <when test="group=='clinic'">
                clinic_id as pk,
                clinic_id as clinicId,
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10) as pk,
                #{params.clinicId} as clinicId,
            </when>
        </choose>
        v2_transaction_id,
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
            sum(if(record_source_type = 2,if(type=-1, -1, 1) * record_member_principal,0.0)) as principalRepayment,
            sum(if(record_source_type = 2,if(type=-1, -1, 1) * record_member_present,0.0)) as presentRepayment,
            sum(if(record_source_type != 2,if(type=-1, -1, 1) * record_member_principal,0.0)) as member_principal,
            sum(if(record_source_type != 2,if(type=-1, -1, 1) * record_member_present,0.0)) as member_present
        </if>
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
            sum(0) as principalRepayment,
            sum(0) as presentRepayment,
            sum(if(pay_type != 20,if(type=-1, -1, 1) * record_member_principal,0.0)) as member_principal,
            sum(if(pay_type != 20,if(type=-1, -1, 1) * record_member_present,0.0)) as member_present
        </if>
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and chain_id = #{params.chainId}
        and import_flag = 0
        and is_deleted = 0
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.hisType != null and params.hisType == 100">
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                <!-- 只要收费项目，剔除套餐母项 -->
                and goods_fee_type in (0,2) and product_compose_type != 1
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
            </if>
        </if>
        <if test="params.hisType != null and params.hisType != 100">
            and goods_fee_type in(0,1)
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
        </if>
        and pay_type=6
        <if test="params.payModeSql != null and params.payModeSql !=''">
            and (${params.payModeSql})
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.includeReg != null and params.includeReg == 0">
            and product_type!=5
        </if>
        <if test="params.feeTypeIds != null and params.feeTypeIds != ''">
            and ${params.feeTypeIds}
        </if>
        <if test="params.cashierSql != null and params.cashierSql != ''">
            and ${params.cashierSql}
        </if>
        GROUP BY
        <choose>
            <when test="group=='cashier'">
                clinic_id,cashier_id,v2_transaction_id
            </when>
            <when test="group=='clinic'">
                clinic_id,v2_transaction_id
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10),v2_transaction_id
            </when>
        </choose>
        ) as t
    group by t.pk
    </select>


    <select id="fetchFeeClassify" parameterType="java.lang.String"
            resultType="cn.abc.flink.stat.service.cis.operation.domain.CashierFeeClassify">
    SELECT
        <choose>
            <when test="group=='cashier'">
                concat(clinic_id,'-',cashier_id) as pk,
                clinic_id as clinicId,
                cashier_id as cashierId,
            </when>
            <when test="group=='clinic'">
                clinic_id as pk,
                clinic_id as clinicId,
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10) as pk,
                #{params.clinicId} as clinicId,
            </when>
        </choose>
        if(classify_level_2_id is null, 0, classify_level_2_id) as level2,
        classify_level_1_id as level1,
        sum(received_price * if(type=-1, -1, 1))  as value
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and chain_id = #{params.chainId}
        and import_flag = 0
        and is_deleted = 0
        and goods_fee_type in(0,1)
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test='params.sellerId != null and params.sellerId != ""'>
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.departmentId != null and params.departmentId != ""'>
            and (seller_department_id = #{params.departmentId} or department_id = #{params.departmentId})
        </if>
        <if test="params.hisType != null and params.hisType == 100">
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                <!-- 只要收费项目，剔除套餐母项 -->
                and goods_fee_type in (0,2) and product_compose_type != 1
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
            </if>
        </if>
        <if test="params.hisType != null and params.hisType != 100">
            <if test="params.composeSql != null and params.composeSql != ''">
                and ${params.composeSql}
            </if>
        </if>
        <!-- 选择欠款时将欠费的数据排除掉 record_source_type 1欠费 2还款-->
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
        <if test="params.payModeSql != null and params.payModeSql !=''">
            and (${params.payModeSql})
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.includeReg != null and params.includeReg == 0">
            and product_type!=5
        </if>
        <if test="params.cashierSql != null and params.cashierSql != ''">
            and ${params.cashierSql}
        </if>
        GROUP BY
        <choose>
            <when test="group=='cashier'">
                clinic_id,cashier_id,level1,level2
            </when>
            <when test="group=='clinic'">
                clinic_id,level1,level2
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10),level1,level2
            </when>
        </choose>
    </select>

    <select id="fetchAdviceFeeClassify" parameterType="java.lang.String"
            resultType="cn.abc.flink.stat.service.cis.operation.domain.CashierFeeClassify">
    SELECT
        <choose>
            <when test="group=='cashier'">
                concat(clinic_id,'-',cashier_id) as pk,
                clinic_id as clinicId,
                cashier_id as cashierId,
            </when>
            <when test="group=='clinic'">
                clinic_id as pk,
                clinic_id as clinicId,
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10) as pk,
                #{params.clinicId} as clinicId,
            </when>
        </choose>
        fee_type_id as feeTypeId,
        sum(received_price * if(type=-1, -1, 1))  as value
    FROM ${cisTable}.dwd_charge_transaction_record_v_partition
    WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
        and chain_id = #{params.chainId}
        and import_flag = 0
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.sellerId != null and params.sellerId == '00000000000000000000000000000000'">
            and (cashier_id is null or cashier_id = '' or cashier_id = '00000000000000000000000000000000')
        </if>
        <if test="params.sellerId != null and params.sellerId != '' and params.sellerId != '00000000000000000000000000000000'">
            and cashier_id = #{params.sellerId}
        </if>
        <if test='params.departmentId != null and params.departmentId != ""'>
            and (seller_department_id = #{params.departmentId} or department_id = #{params.departmentId})
        </if>
        <if test="params.hisType != null and params.hisType == 100">
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                <!-- 只要收费项目，剔除套餐母项 -->
                and goods_fee_type in (0,2) and product_compose_type != 1
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
            </if>
        </if>
        <if test="params.hisType != null and params.hisType != 100">
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
        </if>
        <!-- 选择欠款时将欠费的数据排除掉 record_source_type 1欠费 2还款-->
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
            and record_source_type != 1
        </if>
        <if test="params.payModeSql != null and params.payModeSql !=''">
            and (${params.payModeSql})
        </if>
        <if test="params.feeTypeIds != null and params.feeTypeIds != ''">
            and ${params.feeTypeIds}
        </if>
        <if test="params.includeReg != null and params.includeReg == 0">
            and product_type!=5
        </if>
        <if test="params.cashierSql != null and params.cashierSql != ''">
            and ${params.cashierSql}
        </if>
        GROUP BY
        <choose>
            <when test="group=='cashier'">
                clinic_id,cashier_id,feeTypeId
            </when>
            <when test="group=='clinic'">
                clinic_id,feeTypeId
            </when>
            <when test="group=='date'">
                substr(cast(create_time as VARCHAR),1,10),feeTypeId
            </when>
        </choose>
    </select>


    <select id="fetchOverall" parameterType="java.lang.String"
            resultType="cn.abc.flink.stat.service.cis.operation.domain.CashierOverall">
        select
            <choose>
                <when test="group=='cashier'">
                    b.pk,
                    b.clinicId,
                    b.cashierId,
                </when>
                <when test="group=='clinic'">
                    b.pk,
                    b.clinicId,
                </when>
                <when test="group=='date'">
                    b.pk,
                    b.clinicId,
                </when>
            </choose>
            b.cost as cost,
            b.income as income,
            b.outcome as outcome,
            b.oweOutcome as oweOutcome,
            b.chargeCount as chargeCount,
            if(c.patientCount is null, 0::bigint, c.patientCount) as patientCount
        from (

        select
                <choose>
                    <when test="group=='cashier'">
                        pk,
                        clinicId,
                        cashierId,
                    </when>
                    <when test="group=='clinic'">
                        pk,
                        clinicId,
                    </when>
                    <when test="group=='date'">
                        pk,
                        #{params.clinicId} as clinicId,
                    </when>
                </choose>
                sum(cost) as cost,
                sum(income) as income,
                sum(outcome) as outcome,
                sum(oweOutcome) as oweOutcome,
                count(chargeCount)  as chargeCount
        from (
        select
                <choose>
                    <when test="group=='cashier'">
                        concat(clinic_id,'-',cashier_id) as pk,
                        clinic_id as clinicId,
                        cashier_id as cashierId,
                    </when>
                    <when test="group=='clinic'">
                        clinic_id as pk,
                        clinic_id as clinicId,
                    </when>
                    <when test="group=='date'">
                        substr(cast(create_time as VARCHAR),1,10) as pk,
                        #{params.clinicId} as clinicId,
                    </when>
                </choose>
                sum(if(product_type not in (1,2,7,11),if(type=-1, -cost_price, cost_price), 0.0)) as cost,
                sum(if(type>-1,received_price,0.0)) as income,
                sum(if(type=-1,-received_price,0.0)) as outcome,
                sum(if(type=-1 and record_source_type = 1,-received_price,0.0)) as oweOutcome,
                v2_patient_order_id as chargeCount
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and chain_id = #{params.chainId}
            and import_flag = 0
            and is_deleted = 0
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
        <if test="params.hisType != null and params.hisType == 100">
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                <!-- 只要收费项目，剔除套餐母项 -->
                and goods_fee_type in (0,2) and product_compose_type != 1
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
            </if>
        </if>
        <if test="params.hisType != null and params.hisType != 100">
            and goods_fee_type in(0,1)
            <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
        </if>
            <!-- 选择欠款时将欠费的数据排除掉 record_source_type 1欠费 2还款-->
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
                and record_source_type != 2
            </if>
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
                and record_source_type != 1
            </if>
            <if test="params.payModeSql != null and params.payModeSql !=''">
                and (${params.payModeSql})
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee2 != null and params.fee1 == null">
                and ${params.fee2}
            </if>
            <if test="params.includeReg != null and params.includeReg == 0">
                and product_type!=5
            </if>
            <if test="params.feeTypeIds != null and params.feeTypeIds != ''">
                and ${params.feeTypeIds}
            </if>
            <if test="params.cashierSql != null and params.cashierSql != ''">
                and ${params.cashierSql}
            </if>
        GROUP BY
            <choose>
                <when test="group=='cashier'">
                    pk, clinic_id,cashier_id, v2_patient_order_id
                </when>
                <when test="group=='clinic'">
                    pk,  clinic_id, v2_patient_order_id
                </when>
                <when test="group=='date'">
                    pk, v2_patient_order_id
                </when>
            </choose>
        ) a
        GROUP BY
            <choose>
                <when test="group=='cashier'">
                    pk, clinicId,cashierId
                </when>
                <when test="group=='clinic'">
                    pk, clinicId
                </when>
                <when test="group=='date'">
                    pk
                </when>
            </choose>
        ) b left join

        (select
            <choose>
                <when test="group=='cashier'">
                    pk,
                    clinicId,
                    cashierId,
                </when>
                <when test="group=='clinic'">
                    pk,
                    clinicId,
                </when>
                <when test="group=='date'">
                    pk,
                    #{params.clinicId} as clinicId,
                </when>
            </choose>
            count(v2_patient_order_id) as patientCount
        from (
        select
            <choose>
                <when test="group=='cashier'">
                    concat(clinic_id,'-',cashier_id) as pk,
                    clinic_id as clinicId,
                    cashier_id as cashierId,
                </when>
                <when test="group=='clinic'">
                    clinic_id as pk,
                    clinic_id as clinicId,
                </when>
                <when test="group=='date'">
                    substr(cast(create_time as VARCHAR),1,10) as pk,
                    #{params.clinicId} as clinicId,
                </when>
            </choose>
            v2_patient_order_id
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and chain_id = #{params.chainId}
            and import_flag = 0
            and is_deleted = 0
            and classify_level_1_id not in ('-2', '18-0')
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.hisType != null and params.hisType == 100">
                <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    <!-- 只要收费项目，剔除套餐母项 -->
                    and goods_fee_type in (0,2) and product_compose_type != 1
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0)  or (product_compose_type = 0 and goods_fee_type = 2))
                </if>
            </if>
            <if test="params.hisType != null and params.hisType != 100">
                and goods_fee_type in(0,1)
                <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    and product_compose_type in (0,2)
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    and product_compose_type in (0,1)
                </if>
            </if>
            <!-- 选择欠款时将欠费的数据排除掉 record_source_type 1欠费 2还款-->
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
                and record_source_type != 2
            </if>
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
                and record_source_type != 1
            </if>
            <if test="params.payModeSql != null and params.payModeSql !=''">
                and (${params.payModeSql})
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee2 != null and params.fee1 == null">
                and ${params.fee2}
            </if>
            <if test="params.includeReg != null and params.includeReg == 0">
                and product_type!=5
            </if>
            group by
                <choose>
                    <when test="group=='cashier'">
                        pk, clinic_id,cashier_id, v2_patient_order_id
                    </when>
                    <when test="group=='clinic'">
                        pk, clinic_id, v2_patient_order_id
                    </when>
                    <when test="group=='date'">
                        pk, v2_patient_order_id
                    </when>
                </choose>
            ) a
        group by
            <choose>
                <when test="group=='cashier'">
                    pk, clinicId,cashierId
                </when>
                <when test="group=='clinic'">
                    pk, clinicId
                </when>
                <when test="group=='date'">
                    pk
                </when>
            </choose>
        ) c on
            <choose>
                <when test="group=='cashier'">
                    b.pk = c.pk and b.clinicId = c.clinicId and b.cashierId = c.cashierId
                </when>
                <when test="group=='clinic'">
                    b.pk = c.pk and b.clinicId = c.clinicId
                </when>
                <when test="group=='date'">
                    b.pk = c.pk and b.clinicId = c.clinicId
                </when>
            </choose>
    </select>

    <select id="fetchDispensingCost" resultType="cn.abc.flink.stat.service.cis.operation.domain.DispensingLogCost">
        select
            a.pk as pk,
            if (a.hoverCode > 0, 1, 0) as hoverCode,
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                (COALESCE(a.cost,0.0) + COALESCE(b.cost,0.0)) as cost
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 1">
                a.cost as cost
            </if>
        from
        (SELECT
        <choose>
            <when test=" group =='clinic'">
                clinic_id as pk,
            </when>
            <when test=" group =='date'">
                substr(log_time::VARCHAR,1,10) as pk,
            </when>
        </choose>
        sum(if(type in(4,6),-cost_price,cost_price)) AS cost,
        sum(if (type in(5,6), 1, 0)) as hoverCode
        FROM ${cisTable}.dwd_dispensing_log_v_partition
        <where>
            and log_time between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and
            to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{params.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and
            to_char(to_timestamp(#{params.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and chain_id = #{params.chainId}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="((params.fee1 != null and params.fee1.contains('11-1')) or (params.fee2 != null and params.fee2.contains('11-1')))">
                and (product_compose_type = 2
                <if test="params.costFee1 != null and params.costFee2 != null">
                    or (product_compose_type = 0 and (${params.costFee1} or ${params.costFee2}))
                </if>
                <if test="params.costFee1 != null and params.costFee2 == null">
                    or (product_compose_type = 0 and ${params.costFee1})
                </if>
                <if test="params.costFee2 != null and params.costFee1 == null">
                    or (product_compose_type = 0 and ${params.costFee2})
                </if>)
            </if>
            <if test="!((params.fee1 != null and params.fee1.contains('11-1')) or (params.fee2 != null and params.fee2.contains('11-1')))">
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    and product_compose_type = 0
                </if>
                <if test="params.fee1 != null and params.fee2 != null">
                    and (${params.fee1} or ${params.fee2})
                </if>
                <if test="params.fee1 != null and params.fee2 == null">
                    and ${params.fee1}
                </if>
                <if test="params.fee2 != null and params.fee1 == null">
                    and ${params.fee2}
                </if>
            </if>

            <if test="params.feeTypeIds != null and params.feeTypeIds != ''">
                and ${params.feeTypeIds}
            </if>
            and product_id is not null
        </where>
        group by
        <choose>
            <when test=" group =='clinic'">
                clinic_id
            </when>
            <when test=" group =='date'">
                substr(log_time:: VARCHAR,1,10)</when>
        </choose>) a
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
        left join (
            select
                <choose>
                    <when test="group =='clinic'">
                        clinic_id as pk,
                    </when>
                    <when test=" group =='date'">
                        substr(create_time::VARCHAR,1,10) as pk,
                    </when>
                </choose>
                sum(if(product_type not in (1,2,7),if(type=-1, -cost_price, cost_price), 0.0)) as cost
            FROM ${cisTable}.dwd_charge_transaction_record_v_partition
            WHERE create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and chain_id = #{params.chainId}
            and import_flag = 0
            and is_deleted = 0
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            and ((product_compose_type = 2 and goods_fee_type = 0) or (product_compose_type = 3 and goods_fee_type = 2))
            and product_type not in (1,2,7)
            <!-- 选择欠款时将欠费的数据排除掉 record_source_type 1欠费 2还款-->
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
                and record_source_type != 2
            </if>
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
                and record_source_type != 1
            </if>
            <if test="params.payModeSql != null and params.payModeSql !=''">
                and (${params.payModeSql})
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee2 != null and params.fee1 == null">
                and ${params.fee2}
            </if>
            <if test="params.includeReg != null and params.includeReg == 0">
                and product_type!=5
            </if>
            <if test="params.feeTypeIds != null and params.feeTypeIds != ''">
                and ${params.feeTypeIds}
            </if>
            group by
            <choose>
                <when test=" group =='clinic'">
                    clinic_id
                </when>
                <when test=" group =='date'">
                    substr(create_time:: VARCHAR,1,10)
                </when>
            </choose>
        ) b on a.pk = b.pk
        </if>

    </select>
</mapper>