<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.StatConfigMapper">
    <insert id="insertClinicConfig">
        insert into ${db}.v2_stat_clinic_config
        (
        chain_id,
        clinic_id,
        `key`,
        value,
        created,
        created_by,
        last_modified,
        last_modified_by
        )
        values
        (
        #{po.chainId},
        #{po.clinicId},
        #{po.key},
        #{po.value},
        #{po.created},
        #{po.createdBy},
        #{po.lastModified},
        #{po.lastModifiedBy}
        )
    </insert>

    <update id="updateClinicConfig">
        update
            ${db}.v2_stat_clinic_config
        set
            `key` = #{po.key},
            value = #{po.value},
            last_modified = #{po.lastModified},
            last_modified_by = #{po.lastModifiedBy}
        where
            chain_id = #{po.chainId}
            <if test="po.clinicId != null and po.clinicId != ''">
                and clinic_id = #{po.clinicId}
            </if>
            <if test="po.clinicId == null or po.clinicId == ''">
                and clinic_id is null
            </if>
            and `key` = #{po.key}
    </update>

    <select id="selectClinicConfigByParam" resultType="cn.abc.flink.stat.service.cis.config.pojo.StatConfigPo">
        select
            *
        from ${db}.v2_stat_clinic_config
        where
            chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id = #{param.clinicId}
            </if>
            <if test="param.clinicId == null or param.clinicId == ''">
                and clinic_id is null
            </if>
            <if test="param.configKey != null and param.configKey != ''">
                and `key` = #{param.configKey}
            </if>
            <if test="param.configKeySet != null">
                and `key` in
                <foreach item="configKey" collection="param.configKeySet" separator="," open="(" close=")" index="">
                    #{configKey}
                </foreach>
            </if>
    </select>


    <select id="selectDefaultConfigByParam"
            resultType="cn.abc.flink.stat.service.cis.config.pojo.StatConfigPo">
        select
            *
        from ${db}.v2_stat_default_config
        where
            1 = 1
            and env = #{env}
            <if test="param.configKey != null and param.configKey != ''">
                and `key` = #{param.configKey}
            </if>
            <if test="param.clinicType != null">
                and clinic_type = #{param.clinicType}
            </if>
            <if test="param.configKeySet != null and param.configKeySet.size() > 0">
                and `key` in
                <foreach item="configKey" collection="param.configKeySet" separator="," open="(" close=")" index="">
                    #{configKey}
                </foreach>
            </if>
    </select>

    <select id="selectStatEvent"
            resultType="cn.abc.flink.stat.service.cis.config.pojo.StatEventRecordindParam">
        select id,
               dimension_id as dimensionId,
               dimension_type as dimensionType,
               stat_module as statModule,
               version,
               create_by as createBy,
               created,
               last_modified as lastModified
        from ${db}.v1_stat_event_recording
        where dimension_id = #{params.dimensionId}
          and stat_module = #{params.statModule}
          <!-- and version = #{params.version} -->
    </select>

    <insert id="insertStatEvent">
        insert into ${db}.v1_stat_event_recording
        (
             dimension_id,
             dimension_type,
             stat_module,
             version,
             create_by,
             created,
             last_modified
        )
        values
        (
            #{params.dimensionId},
            #{params.dimensionType},
            #{params.statModule},
            #{params.version},
            #{params.createBy},
            #{params.created},
            #{params.lastModified}
        )
    </insert>

    <update id="updateStatEvent">
        update ${db}.v1_stat_event_recording
        set
            version = #{params.version}
        where
            dimension_id = #{params.dimensionId}
            and stat_module = #{params.statModule}
    </update>

    <select id="selectV2StatOrganTableConfig"
            resultType="cn.abc.flink.stat.service.cis.config.pojo.StatOrganTableConfigDao">
        select
            id,
            clinic_id as clinicId,
            name,
            type,
            components as components
        from ${db}.v2_stat_organ_table_config
        where clinic_id = #{clinicId}
        and is_deleted = 0
        order by sort
    </select>

    <select id="selectV2StatComponentConfig"
            resultType="cn.abc.flink.stat.service.cis.config.pojo.StatComponentConfig">
        select
            id,
            `key`,
            name,
            table_key as tableKey
        from ${db}.v2_stat_component_config
        where
        is_deleted = 0
        <if test="components != null and components.size() > 0">
            and `key` in
            <foreach item="keyName" collection="components" separator="," open="(" close=")" index="">
                #{keyName}
            </foreach>
        </if>
    </select>
</mapper>