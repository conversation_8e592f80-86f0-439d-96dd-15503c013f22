<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresOperationFeeClassifyMapper">

    <select id="listFeeClassify" parameterType="java.lang.String"
            resultType="map">
        SELECT a.id, a.value,
            <choose>
                <when test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">(if(a.cost is null, 0.0, a.cost) - if(b.cost is null, 0.0, b.cost)) as cost</when>
               <otherwise>a.cost</otherwise>
            </choose>
        FROM (
            SELECT
                classify_level_1_id as id,
                sum(received_price * if(type=-1, -1, 1)) as value,
                sum( if( product_type not in (1,2,7), cost_price, 0.0) * if(type=-1, -1, 1) ) as cost
            FROM ${cisTable}.dwd_charge_transaction_record_v_partition
            WHERE create_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
              and ds between #{params.beginDateDs} and #{params.endDateDs}
            <if test="params.composeTypeSql != null and params.composeTypeSql != ''">
                and ${params.composeTypeSql}
            </if>
            and chain_id = #{params.chainId}
            and import_flag = 0
            and is_deleted = 0
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.oweSql != null and params.oweSql != ''">
                and ${params.oweSql}
            </if>
            GROUP BY classify_level_1_id
        ) as a
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            left join
            (
                SELECT
                    '11-1' as id1,
                    sum( cost_price * if(type=-1, -1, 1) ) as cost
                FROM ${cisTable}.dwd_charge_transaction_record_v_partition
                WHERE create_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                  and ds between #{params.beginDateDs} and #{params.endDateDs}
                and chain_id = #{params.chainId}
                and import_flag = 0
                and is_deleted = 0
                <if test="params.clinicId != null and params.clinicId !=''">
                    and clinic_id = #{params.clinicId}
                </if>
                <if test="params.oweSql != null and params.oweSql != ''">
                    and ${params.oweSql}
                </if>
                and product_compose_type = 2 and goods_fee_type = 0
                and product_type in (1, 2, 7, 24)
                GROUP BY id1
            ) as b
        on a.id = b.id1
        </if>

        ORDER BY value DESC
    </select>

    <select id="hospitalListFeeClassify" parameterType="java.lang.String"
            resultType="map">
        SELECT
            fee_type_id as id,
            sum(received_price * if(type=-1, -1, 1)) as value,
            sum( if( product_type not in (1,2,7), cost_price, 0.0 ) * if(type=-1, -1, 1) ) as cost
        FROM
            ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE
            create_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{params.beginDateDs} and #{params.endDateDs}
            and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 0 and goods_fee_type = 2) or (product_compose_type = 3 and goods_fee_type = 2) or (product_compose_type = 2 and goods_fee_type = 0))
            and chain_id = #{params.chainId}
            and import_flag = 0
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.oweSql != null and params.oweSql != ''">
                and ${params.oweSql}
            </if>
        GROUP BY
            fee_type_id
        ORDER BY
            value
        DESC
    </select>

    <select id="listFeeClassifyCost" parameterType="java.lang.String"
            resultType="map">
        SELECT
            classify_level_1_id as id,
            sum(cost_price* if(type in (3,5), 1, -1)) as cost,
            if (sum(if (type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        FROM ${cisTable}.dwd_dispensing_log_v_partition
        WHERE log_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
          and ds between #{params.beginDateDs} and #{params.endDateDs}
        <if test="params.isComposeShareEqually == null and params.isComposeShareEqually == 1">
            and product_type != 11
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type = 0
        </if>
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        GROUP BY classify_level_1_id
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            union all
        SELECT
        if(id is not null,'11-1',null) as id,
        sum(cost_price* if(type in (3,5), 1, -1)) as cost,
        if (sum(if (type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        FROM ${cisTable}.dwd_dispensing_log_v_partition
        WHERE log_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between #{params.beginDateDs} and #{params.endDateDs}
        and product_compose_type = 2
        and chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        group by if(id is not null,'11-1',null)
        </if>
    </select>

    <select id="hospitalListFeeClassifyCost" parameterType="java.lang.String"
            resultType="map">
        SELECT
            fee_type_id as id,
            sum(cost_price * if(type in (3,5), 1, -1)) as cost,
            if (sum(if (type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        FROM
            ${cisTable}.dwd_dispensing_log_v_partition
        WHERE
            log_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{params.beginDateDs} and #{params.endDateDs}
            and chain_id = #{params.chainId}
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
        GROUP BY
            fee_type_id
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            union all
            SELECT
                fee_type_id as id,
                sum(cost_price* if(type in (3,5), 1, -1)) as cost,
                if (sum(if (type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            FROM
                ${cisTable}.dwd_dispensing_log_v_partition
            WHERE
                log_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds between #{params.beginDateDs} and #{params.endDateDs}
                and product_compose_type = 2
                and chain_id = #{params.chainId}
                <if test="params.clinicId != null and params.clinicId !=''">
                    and clinic_id = #{params.clinicId}
                </if>
        </if>
    </select>

    <select id="listTreatFeeClassifyCost" resultType="map">
        SELECT
            classify_level_1_id as classifylevel1,
            classify_level_2_id as classifylevel2,
            sum(if(type=-1, -cost_price, cost_price)) as cost,
            sum(received_price * if(type=-1, -1, 1)) as value
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between #{params.beginDateDs} and #{params.endDateDs}
        <!-- 套餐是否拆分到子项，0不拆分 1拆分 -->
        <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
            and product_compose_type in (0,2)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
        </if>
        <if test="params.oweSql != null and params.oweSql != ''">
            and ${params.oweSql}
        </if>
        and chain_id = #{params.chainId}
        and import_flag = 0
        and is_deleted = 0
        and classify_level_1_id = '4-1'
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        GROUP BY classifyLevel1, classifyLevel2
    </select>
</mapper>