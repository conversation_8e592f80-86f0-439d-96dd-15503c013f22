<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.dao.OdsCisOutPatientMapper">
    <select id="selectConsultantNumber" resultType="java.lang.Long">
        select
            count(distinct patient_id) as consultantNumber
        from
            ${outpatientDb}.v2_outpatient_consultation_sheet
        where
            chain_id = #{params.chainId}
            and is_deleted = 0
            and created between #{params.beginDate} and #{params.endDate}
    </select>
</mapper>