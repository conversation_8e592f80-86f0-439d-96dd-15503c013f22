<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.MysqlOperationComposeMapper">

    <select id="listOperationCompose" parameterType="java.lang.String"
            resultType="map">
        select
        product_id,
        fee,
        count,
        cost,
        hoverCode
        from (
        select
            productId as product_id,
            ifnull(fee,0) as fee,
            ifnull(count,0) as count,
            ifnull(chargeCost,0) + ifnull(dispensingCost,0) as cost,
            hoverCode as hoverCode
        from (
            select
            a.productId,
            a.fee,
            a.count,
            a.cost as chargeCost,
            b.cost as dispensingCost,
            (0 + if (b.hoverCode is not null, b.hoverCode, 0)) as hoverCode
            from (
            SELECT
            product_id as productId,
            max(product_type) as productType,
            cast(sum(received_price * if(type=-1, -1, 1)) as double ) as fee,
            cast(sum(product_unit_count* if(type=-1, -1, 1)) as double ) as count,
            cast(sum(cost_price* if(type=-1, -1, 1)) as double ) as cost
            FROM ${cisTable}.dwd_charge_transaction_record_v_partition
            WHERE create_time BETWEEN #{params.beginDate} and #{params.endDate}
            and chain_id = #{params.chainId}
            and is_deleted = 0
            and product_compose_type = 1
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.composeId != null and params.composeId !=''">
                and product_id = #{params.composeId}
            </if>
            <!-- 欠费时计费 -->
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
                and record_source_type != 2
            </if>
            <!-- 还款时计费 -->
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
                and record_source_type != 1
            </if>
            GROUP BY product_id) a
        left join
        (
            SELECT
            compose_parent_product_id AS productId,
            sum(if(type in(4,6),-cost_price,cost_price)) AS cost,
            if (sum(if (type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            FROM ${cisTable}.dwd_dispensing_log_v_partition
            <where>
                and log_time between #{params.beginDate} and #{params.endDate}
                and product_type in (1,2,7)
                and form_type = 0
                <if test="params.chainId != null and params.chainId !=''">
                    and chain_id = #{params.chainId}
                </if>
                <if test="params.clinicId != null and params.clinicId != ''">
                    and clinic_id = #{params.clinicId}
                </if>
                <if test="params.composeId != null and params.composeId !=''">
                    and compose_parent_product_id = #{params.composeId}
                </if>
                and compose_parent_product_id is not null
            </where>
            group by compose_parent_product_id
        ) b on a.productId = b.productId
        union all
        select
            a.productId,
            a.fee,
            a.count,
            a.cost as chargeCost,
            b.cost as dispensingCost,
            (if(a.productId is null, 1, 0) * 2 + if (b.hoverCode is not null, b.hoverCode, 0)) as hoverCode
            from (SELECT
            product_id as productId,
            max(product_type) as productType,
            cast(sum(received_price * if(type=-1, -1, 1)) as double ) as fee,
            cast(sum(product_unit_count* if(type=-1, -1, 1)) as double ) as count,
            cast(sum(cost_price* if(type=-1, -1, 1)) as double ) as cost
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN #{params.beginDate} and #{params.endDate}
            and chain_id = #{params.chainId}
            and import_flag = 0
            and is_deleted = 0
            and product_compose_type = 1
            <if test="params.clinicId != null and params.clinicId !=''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.composeId != null and params.composeId !=''">
                and product_id = #{params.composeId}
            </if>
            <!-- 欠费时计费 -->
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
                and record_source_type != 2
            </if>
            <!-- 还款时计费 -->
            <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
                and record_source_type != 1
            </if>
        GROUP BY product_id) a
        right join
        (
        SELECT
            compose_parent_product_id AS productId,
            sum(if(type in (4,6),-cost_price,cost_price)) AS cost,
            if (sum(if (type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        FROM ${cisTable}.dwd_dispensing_log_v_partition
        <where>
            and log_time between #{params.beginDate} and #{params.endDate}
            and product_type in (1,2,7)
            and form_type = 0
            <if test="params.chainId != null and params.chainId !=''">
                and chain_id = #{params.chainId}
            </if>
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.composeId != null and params.composeId !=''">
                and compose_parent_product_id = #{params.composeId}
            </if>
            and compose_parent_product_id is not null
        </where>
        group by compose_parent_product_id
        ) b on a.productId = b.productId
        ) e) h group by product_id,fee,cost,count
        ORDER BY fee DESC
    </select>

    <select id="listOperationComposeCount" parameterType="java.lang.String"
            resultMap="mapResult">
        SELECT
        product_id,
        cast(sum(product_unit_count* if(type=-1, -1, 1)) as double ) as `count`
        FROM ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE create_time BETWEEN #{params.beginDate} and #{params.endDate}
        and chain_id = #{params.chainId}
        and is_deleted = 0
        <if test="params.clinicId != null and params.clinicId !=''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.composeId != null and params.composeId !=''">
            and product_id = #{params.composeId}
        </if>
        <!-- 欠费时计费 -->
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 1">
            and record_source_type != 2
        </if>
        <!-- 还款时计费 -->
        <if test="params.arrearsStatTiming != null and params.arrearsStatTiming == 2">
            and pay_type != 20
        </if>
        and product_type = 11
        and is_deleted = 0
        GROUP BY product_id
    </select>

    <select id="listOperationComposeExcludedDispensingCost" resultType="java.util.Map">
        SELECT
            compose_parent_product_id as product_id,
            cast(sum(cost_price* if(type=-1, -1, 1)) as double ) as cost
        FROM
            ${cisTable}.dwd_charge_transaction_record_v_partition
        WHERE
            create_time BETWEEN #{beginDate} and #{endDate}
            and chain_id = #{chainId}
            and is_deleted = 0
            and product_compose_type = 2
            and product_type in (1,2,7)
            <if test="clinicId != null and clinicId !=''">
                and clinic_id = #{clinicId}
            </if>
            <if test="goodsId != null and goodsId !=''">
                and compose_parent_product_id = #{goodsId}
            </if>
        GROUP BY compose_parent_product_id
    </select>

    <resultMap id="mapResult" type="java.util.HashMap">
        <result property="key" column="product_id"/>
        <result property="value" column="count"/>
    </resultMap>
</mapper>