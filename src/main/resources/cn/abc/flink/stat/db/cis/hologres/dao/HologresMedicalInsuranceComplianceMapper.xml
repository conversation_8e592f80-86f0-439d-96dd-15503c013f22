<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresMedicalInsuranceComplianceMapper">

    <select id="selectTotal" resultType="java.util.Map">
        select
            sum(restrict_count) as "restrictCount",
            sum(restrict_setting_count) as "restrictSettingCount"
        from ${env}.dws_shebao_restrict_rule_result
        where chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        and business_type in(0,2,3)
    </select>

    <select id="selectToDayTotal" resultType="java.util.Map">
        select
            sum(if (restrict_count > 0, 1.0, 0.0)) as "restrictCount",
            sum(if (restrict_setting_count > 0, 1.0, 0.0)) as "restrictSettingCount"
        from ${env}.shebao_restrict_rule_result
        where chain_id = #{params.chainId}
            and created between concat(date (now()), ' 00:00:00')::TIMESTAMP  and concat(date (now()), ' 23:59:59')::TIMESTAMP
            and ds = to_char(now(),'yyyy')::INT
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            and is_deleted = 0
            and business_type in(0,2,3)
    </select>

    <select id="selectList" resultType="java.util.Map">
        select
            created_by as "doctorId",
            sum(if (business_type in(0,3) and restrict_count > 0, 1, 0)) as "restrictCount",
            sum(if (business_type in(0,3) and restrict_setting_count > 0, 1, 0)) as "restrictSettingCount",
            sum(if (business_type = 2 and restrict_count > 0, 1, 0)) as "hospitalRestrictCount",
            sum(if (business_type = 2 and restrict_setting_count > 0, 1, 0)) as "hospitalRestrictSettingCount"
        from ${env}.shebao_restrict_rule_result
        where chain_id = #{params.chainId}
          and created between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
          and ds between to_char(#{params.beginDate}::date, 'YYYY')::NUMERIC and to_char(#{params.endDate}::date, 'YYYY')::NUMERIC
          <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
          </if>
          <if test="params.doctorId != null and params.doctorId != ''">
            and created_by = #{params.doctorId}
          </if>
          and is_deleted = 0
          and business_type in(0,2,3)
        group by created_by
        <if test="params.limit != null and params.offset != null">
            limit #{params.limit} offset #{params.offset}
        </if>
    </select>

    <select id="selectListTotal" resultType="java.lang.Long">
        select count(1) from (
         select
            created_by
        from ${env}.shebao_restrict_rule_result
        where chain_id = #{params.chainId}
        and created between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between to_char(#{params.beginDate}::date, 'YYYY')::NUMERIC and to_char(#{params.endDate}::date, 'YYYY')::NUMERIC
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.doctorId != null and params.doctorId != ''">
            and created_by = #{params.doctorId}
        </if>
        and is_deleted = 0
        and business_type in(0,2,3)
        group by created_by) a
    </select>

    <select id="selectPersonnel" resultType="cn.abc.flink.stat.service.cis.medical.entity.ChargeProportionPersonnelEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            sum(received_fee + refund_fee) as totalAmount,
            count(distinct patient_id) as totalPatientNum,
            count(distinct patient_order_id) as totalPatientCount,
            sum(shebao_received_fee) as shebaoReceivedFee,
            sum(fund_pay_sumamt) as fundPaySumamt,
            sum(acct_pay) as acctPay,
            round(if(sum(received_fee + refund_fee) != 0 and sum(fund_pay_sumamt) is not null, sum(fund_pay_sumamt) / sum(received_fee + refund_fee), 0.0), 2) as fundTotalAmountRatio,
            count(DISTINCT if(medfee_sumamt is null, null, patient_id)) as settlementPatientNum,
            count(DISTINCT if(medfee_sumamt is null, null, patient_order_id)) as settlementPatientCount
        from ${env}.dwd_shebao_charge_sheet
        where chain_id = #{params.chainId}
        and charge_time between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between to_char(#{params.beginDate}::date, 'YYYYMM')::NUMERIC and to_char(#{params.endDate}::date, 'YYYYMM')::NUMERIC
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test='params.employeeSql != null and params.employeeSql != ""'>
            ${params.employeeSql}
        </if>
        <if test="params.sheBaoChargeTypeSql != null and params.sheBaoChargeTypeSql != ''">
            ${params.sheBaoChargeTypeSql}
        </if>
        and is_deleted = 0
        and type != 5
        group by chain_id, clinic_id, employeeId, employeeName
        order by fundTotalAmountRatio desc, employeeId
    </select>

    <select id="selectPersonnelSummary" resultType="cn.abc.flink.stat.service.cis.medical.entity.ChargeProportionPersonnelEntity">
        select
            <choose>
                <when test="params.getDispensaryType != null and params.getDispensaryType == 1">
                    '合计' as clinicName,
                    '-' as employeeName,
                </when>
                <otherwise>
                    '合计' as employeeName,
                </otherwise>
            </choose>
            sum(received_fee + refund_fee) as totalAmount,
            count(distinct patient_id) as totalPatientNum,
            count(distinct patient_order_id) as totalPatientCount,
            sum(shebao_received_fee) as shebaoReceivedFee,
            sum(fund_pay_sumamt) as fundPaySumamt,
            sum(acct_pay) as acctPay,
            count(DISTINCT if(medfee_sumamt is null, null, patient_id)) as settlementPatientNum,
            count(DISTINCT if(medfee_sumamt is null, null, patient_order_id)) as settlementPatientCount
        from ${env}.dwd_shebao_charge_sheet
        where chain_id = #{params.chainId}
        and charge_time between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between to_char(#{params.beginDate}::date, 'YYYYMM')::NUMERIC and to_char(#{params.endDate}::date, 'YYYYMM')::NUMERIC
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test='params.employeeSql != null and params.employeeSql != ""'>
            ${params.employeeSql}
        </if>
        <if test="params.sheBaoChargeTypeSql != null and params.sheBaoChargeTypeSql != ''">
            ${params.sheBaoChargeTypeSql}
        </if>
        and is_deleted = 0
        and type != 5
    </select>

    <select id="selectTransaction" resultType="cn.abc.flink.stat.service.cis.medical.entity.ChargeProportionTransactionEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as employeeId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            charge_time as chargeTime,
            shebao_charge_type as shebaoChargeType,
            if(type !=1 and type != 2, 3, type::integer) as type,
            patientorder_no as patientorderNo,
            patient_id as patientId,
            case when revisit_status = 1 then '初诊'
                when revisit_status = 2 then '复诊'
                else '-' end as revisit,
            sum(coalesce(receivable_fee, 0.0)) as receivablePrice,
            sum(coalesce(received_fee, 0.0) + coalesce(refund_fee, 0.0)) as receivedPrice,
            sum(coalesce(shebao_received_fee, 0.0)) as shebaoReceivedFee,
            sum(coalesce(fund_pay_sumamt, 0.0)) as fundPaySumamt,
            sum(coalesce(acct_pay, 0.0)) as acctPay
        from ${env}.dwd_shebao_charge_sheet
        where chain_id = #{params.chainId}
        and charge_time between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between to_char(#{params.beginDate}::date, 'YYYYMM')::NUMERIC and to_char(#{params.endDate}::date, 'YYYYMM')::NUMERIC
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test='params.employeeSql != null and params.employeeSql != ""'>
            ${params.employeeSql}
        </if>
        <if test="params.sheBaoChargeTypeSql != null and params.sheBaoChargeTypeSql != ''">
            ${params.sheBaoChargeTypeSql}
        </if>
        <if test="params.patientId != null and params.patientId != ''">
            and patient_id = #{params.patientId}
        </if>
        and is_deleted = 0
        and type != 5
        group by chainId, clinicId, employeeId, employeeName, chargeTime, if(type !=1 and type != 2, 3, type::integer), shebaoChargeType, patientorderNo, patientId, revisit
        order by chargeTime desc, employeeId
        <if test="params.limit != null and params.offset != null">
            limit #{params.limit} offset #{params.offset}
        </if>
    </select>

    <select id="selectTransactionTotal" resultType="java.lang.Long">
        select
            count(1) as totalCount
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
                charge_time as chargeTime,
                shebao_charge_type as shebaoChargeType,
                if(type !=1 and type != 2, 3, type::integer) as type,
                patientorder_no as patientorderNo,
                patient_id as patientId,
                case when revisit_status = 1 then '初诊'
                when revisit_status = 2 then '复诊'
                else '-' end as revisit,
                sum(coalesce(receivable_fee, 0.0)) as receivablePrice,
                sum(coalesce(received_fee, 0.0) + coalesce(refund_fee, 0.0)) as receivedPrice,
                sum(coalesce(fund_pay_sumamt, 0.0)) as fundPaySumamt,
                sum(coalesce(acct_pay, 0.0)) as acctPay
            from ${env}.dwd_shebao_charge_sheet
            where chain_id = #{params.chainId}
            and charge_time between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between to_char(#{params.beginDate}::date, 'YYYYMM')::NUMERIC and to_char(#{params.endDate}::date, 'YYYYMM')::NUMERIC
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test='params.employeeSql != null and params.employeeSql != ""'>
                ${params.employeeSql}
            </if>
            <if test="params.sheBaoChargeTypeSql != null and params.sheBaoChargeTypeSql != ''">
                ${params.sheBaoChargeTypeSql}
            </if>
            <if test="params.patientId != null and params.patientId != ''">
                and patient_id = #{params.patientId}
            </if>
            and is_deleted = 0
            and type != 5
            group by chainId, clinicId, employeeId, employeeName, chargeTime, if(type !=1 and type != 2, 3, type::integer), shebaoChargeType, patientorderNo, patientId, revisit
        ) a
    </select>

    <select id="selectPatientChargeCount" resultType="cn.abc.flink.stat.service.cis.medical.entity.ChargeProportionTransactionEntity">
        select
            patient_id as patientId,
            count(1) as patientChargeCount
        from
        (
            select
                patient_id,
                patient_order_id
            from ${env}.dwd_shebao_charge_sheet
            where chain_id = #{params.chainId}
            and charge_time between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between to_char(#{params.beginDate}::date, 'YYYYMM')::NUMERIC and to_char(#{params.endDate}::date, 'YYYYMM')::NUMERIC
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test='params.employeeSql != null and params.employeeSql != ""'>
                ${params.employeeSql}
            </if>
            <if test="params.sheBaoChargeTypeSql != null and params.sheBaoChargeTypeSql != ''">
                ${params.sheBaoChargeTypeSql}
            </if>
            and is_deleted = 0
            and type != 5
            group by patient_id, patient_order_id
        ) a
        group by patient_id
    </select>

    <select id="selectMedicalInsChargeProportionSheBaoChargeType" resultType="cn.abc.flink.stat.service.cis.medical.entity.ChargeProportionTransactionEntity">
        select
            if(type !=1 and type != 2, 3, type::integer) as type,
            shebao_charge_type as shebaoChargeType
        from ${env}.dwd_shebao_charge_sheet
        where chain_id = #{params.chainId}
        and charge_time between TO_TIMESTAMP(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between to_char(#{params.beginDate}::date, 'YYYYMM')::NUMERIC and to_char(#{params.endDate}::date, 'YYYYMM')::NUMERIC
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        and is_deleted = 0
        and type != 5
        group by if(type !=1 and type != 2, 3, type::integer), shebao_charge_type
    </select>

</mapper>