<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.PatientArchivesMapper">

    <select id="selectPatientArchives" resultType="cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchives">
        select
        patient_id as patientId,
        ifnull(sum( case when type != -1 and record_source_type != 2  then received_price
        when type = -1 and record_source_type != 2  then received_price*-1 end
        ),0.00) AS cumulativeAmount,
        <if test="clinicId != null and clinicId != ''">
            sum(if(clinic_id = #{clinicId} and record_source_type = 1, if(type = -1, received_price * -1, received_price), 0)) AS owePayAmount,
            sum(if(clinic_id = #{clinicId} and record_source_type = 2, if(type = -1, received_price * -1, received_price), 0)) AS repayAmount,
        </if>
        <if test="clinicId == null or clinicId == ''">
            sum(if(record_source_type = 1, if(type = -1, received_price * -1, received_price), 0)) AS owePayAmount,
            sum(if(record_source_type = 2, if(type = -1, received_price * -1, received_price), 0)) AS repayAmount,
        </if>
        ifnull(count(distinct case when charge_sheet_type in (3,6,8,12,16,18)then v2_patient_order_id end ),0) as retailCount,
        ifnull(count(distinct v2_patient_order_id),0) payCount,
        ifnull(sum(if(product_type not in (1,2,7,24),if(type=-1, -cost_price, cost_price), 0)), 0.00) as cost
        from ${env}.dwd_charge_transaction_record_v_partition
        where
        chain_id = #{chainId}
        <if test="patientIds != null">
            and patient_id in (${patientIds})
        </if>
        and product_compose_type in (0,2,3)
        and goods_fee_type in (0,2)
        and charge_sheet_type !=5
        and import_flag = 0
        and is_deleted = 0
        and charge_sheet_type !=11
        and product_type != 11
        and create_time between #{beginDate} and #{endDate}
        group by patient_id
    </select>

    <select id="selectPatientDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchives">
        select
            patient_id as patientId,
            ifnull(sum(if(type in(4,6),-cost_price,cost_price)), 0.00) AS cost
        from
            ${env}.dwd_dispensing_log_v_partition
        where
            chain_id = #{chainId}
            <if test="patientIds != null">
                and patient_id in (${patientIds})
            </if>
            and log_time between #{beginDate} and #{endDate}
            and product_compose_type in (0,1)
            and product_id is not null
        group by
            patient_id
    </select>
</mapper>