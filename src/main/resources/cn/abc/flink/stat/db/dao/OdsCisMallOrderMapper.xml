<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.dao.OdsCisMallOrderMapper">

    <select id="selectMallOrderAmount" resultType="java.math.BigDecimal">
        select
            sum(payment_total_price) as mallOrderAmount
        from
            ${mallDb}.v1_order
        where
            chain_id = #{params.chainId}
            <if test="params.microMallOrganId != null and params.microMallOrganId != ''">
                and organ_id = #{params.microMallOrganId}
            </if>
            and is_deleted = 0
            and status in (10, 20, 30, 40)
            and created between #{params.beginDate} and #{params.endDate}
    </select>

    <select id="selectMallOrderNumber" resultType="java.lang.Long">
        select
            count(1) as mallOrderAmount
        from
            ${mallDb}.v1_order
        where
            chain_id = #{params.chainId}
            <if test="params.microMallOrganId != null and params.microMallOrganId != ''">
                and organ_id = #{params.microMallOrganId}
            </if>
            and is_deleted = 0
            and status in (10, 20, 30, 40)
            and created between #{params.beginDate} and #{params.endDate}
    </select>

    <select id="selectMallRefundOrderAmount" resultType="java.math.BigDecimal">
        select
            sum(total_price) as mallRefundAmount
        from
            ${mallDb}.v1_order_after_sale
        where
            chain_id = #{params.chainId}
            <if test="params.microMallOrganId != null and params.microMallOrganId != ''">
                and organ_id = #{params.microMallOrganId}
            </if>
            and is_deleted = 0
            and status in (30, 90)
            and created between #{params.beginDate} and #{params.endDate}
    </select>

    <select id="selectMallVerificationAmount" resultType="java.math.BigDecimal">
        select
            sum(verification_count * payment_unit_price) as mallVerificationAmount
        from
            ${mallDb}.v1_order_item
        where
            chain_id = #{params.chainId}
            and is_deleted = 0
            and created between #{params.beginDate} and #{params.endDate}
    </select>
</mapper>