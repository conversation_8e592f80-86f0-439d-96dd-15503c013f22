<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.dao.DiseaseMysqlMapper">


    <select id="selectDoctorInfo"
            resultType="cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfo">
        SELECT
        ce.chain_id AS chainId,
        ce.clinic_id AS clinicId,
        ce.employee_id doctorId,
        employee.name AS doctorName
        from ${basicDb}.clinic_employee AS ce
        INNER JOIN ${basicDb}.employee on ce.employee_id = employee.id
        <where>
            <if test="chainId != null and chainId !=''">
                and chain_id = #{chainId}
            </if>
            <if test="clinicId != null and clinicId !=''">
                and clinic_id = #{clinicId}
            </if>
            <if test="doctorId != null and doctorId != ''">
                and ce.employee_id = #{doctorId}
            </if>
        </where>

    </select>

    <select id="selectDoctorDeptInfo"
            resultType="cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfo">
        SELECT
        ce.chain_id AS chainId,
        ce.clinic_id AS clinicId,
        ce.employee_id doctorId,
        employee.name AS doctorName,
        if(de.department_id is null or de.department_id='','00000000000000000000000000000000',de.department_id) AS departmentId,
        d.name AS departmentName
        from ${basicDb}.clinic_employee AS ce
        INNER JOIN ${basicDb}.employee on ce.employee_id = employee.id
        LEFT JOIN ${basicDb}.department_employee de on ce.employee_id = de.employee_id
        LEFT JOIN ${basicDb}.department d on de.department_id=d.id
        <where>
            <if test="chainId != null and chainId != ''">
                and ce.chain_id = #{chainId}
            </if>
            <if test="clinicId != null and clinicId != ''">
                and ce.clinic_id = #{clinicId}
            </if>
            <if test="doctorId != null and doctorId != ''">
                and ce.employee_id = #{doctorId}
            </if>
        </where>

    </select>

    <select id="selectClinicName"
            resultType="cn.abc.flink.stat.service.cis.disease.domain.DoctorDisease">
        SELECT
        id AS clinicId,
        name AS clinicName
        FROM ${basicDb}.organ
        <where>
            <if test="clinicId != null and clinicId != ''">
                and id = #{clinicId}
            </if>
            <if test="chainId != null and chainId != ''">
                and parent_id = #{chainId}
            </if>
        </where>
    </select>

    <select id="selectWechatInfo"
                resultType="cn.abc.flink.stat.service.cis.member.domain.MemberWeChart">
           SELECT
               id as chargeTransactionId,
               pay_mode as payMode,
               pay_sub_mode as paySubMode,
               member_card_extra_info as extraInfoJson
           FROM ${chargeDb}.v2_charge_transaction
           WHERE id in
            <foreach collection="list" separator="," close=")" open=" (" item="id" >
                        #{id}
           </foreach>
        </select>
</mapper>