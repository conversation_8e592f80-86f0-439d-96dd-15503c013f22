<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.ChronicRecoveryTransactionMysqlMapper">
    <select id="selectTransactionSummaryList" resultType="cn.abc.flink.stat.service.cis.chronic.recovery.entity.TransactionSummaryEntity">
        select
            chainId,
            clinicId,
            patientId,
            patientName,
            sex as patientSex,
            age as patientAge,
            mobile as patientMobile,
            templateId,
            templateName,
            archivesClinicId,
            archivesCreated,
            archivesCreatedBy,
            sum(if(type=-1, -amount,amount)) as actualPrice
        from
            ${env}.dwd_chronic_recovery_transaction_record
        where chainId=#{param.chainId}
        and created between #{param.beginDate} and #{param.endDate}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinicId= #{param.clinicId}
        </if>
        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
            and productComposeType in (0,2)
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and productComposeType in (0,1)
        </if>
        <if test="param.projectId != null and param.projectId != ''">
            and templateId= #{param.projectId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patientId= #{param.patientId}
        </if>
        group by chainId,clinicId,patientId,patientName,sex,age,mobile,templateId,templateName,archivesClinicId,archivesCreated,archivesCreatedBy
        <if test="param.sort != null and param.sort != ''">
            order by ${param.sort} ${param.order}
        </if>
        <if test="param.sort == null or param.sort == ''">
            order by chainId,clinicId,actualPrice desc
        </if>
        <if test="param.limit != null and param.limit != 0">
            limit #{param.limit} offset #{param.offset}
        </if>
    </select>

    <select id="selectTransactionSummary" resultType="cn.abc.flink.stat.service.cis.chronic.recovery.entity.TransactionResp">
        select
            count(1) as count,
            sum(actualPrice) as amount
        from
        (
            select
                chainId,
                clinicId,
                patientId,
                patientName,
                sex as patientSex,
                age as patientAge,
                mobile as patientMobile,
                templateId,
                templateName,
                archivesClinicId,
                archivesCreated,
                archivesCreatedBy,
                sum(if(type=-1, -amount,amount)) as actualPrice
            from
                ${env}.dwd_chronic_recovery_transaction_record
            where chainId=#{param.chainId}
            and created between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinicId= #{param.clinicId}
            </if>
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                and productComposeType in (0,2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and productComposeType in (0,1)
            </if>
            <if test="param.projectId != null and param.projectId != ''">
                and templateId= #{param.projectId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patientId= #{param.patientId}
            </if>
            group by chainId,clinicId,patientId,patientName,sex,age,mobile,templateId,templateName,archivesClinicId,archivesCreated,archivesCreatedBy
        ) aa
    </select>

    <select id="selectTransactionDetailList" resultType="cn.abc.flink.stat.service.cis.chronic.recovery.entity.TransactionDetailEntity">
        select
            chainId,
            clinicId,
            created,
            if(type=-1,-amount,amount) as amount,
            unit,
            if(type=-1,-count, count) as count,
            createdBy,
            patientNo,
            productId as id,
            productName as name,
            feeType1,
            feeType2,
            originalPrice as unitPrice,
            if(type=-1,-count, count)*originalPrice as originalPrice,
            if(type=-1, -adjustmentPrice, adjustmentPrice) as adjustmentPrice,
            if(type=-1, -promotionPrice, promotionPrice)  as discountPrice,
            patientId,
            patientName,
            sex as patientSex,
            mobile as patientMobile,
            age as patientAge,
            templateId,
            templateName,
            spec,
            payMode,
            chargeSource as chargeSourceType,
            sellerId,
            copywriterId,
            doctorId,
            `comment`,
            departmentId
        from
            ${env}.dwd_chronic_recovery_transaction_record
        where chainId=#{param.chainId}
        and created between #{param.beginDate} and #{param.endDate}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinicId= #{param.clinicId}
        </if>
        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
            and productComposeType in (0,2)
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and productComposeType in (0,1)
        </if>
        <if test="param.projectId != null and param.projectId != ''">
            and templateId= #{param.projectId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patientId= #{param.patientId}
        </if>
        order by created desc
        <if test="param.limit != null and param.limit != 0">
            limit #{param.limit} offset #{param.offset}
        </if>
    </select>

    <select id="selectTransactionDetail" resultType="cn.abc.flink.stat.service.cis.chronic.recovery.entity.TransactionResp">
        select
            count(1) as count,
            sum(if(type=-1, -amount, amount)) as amount
        from
            ${env}.dwd_chronic_recovery_transaction_record
        where chainId=#{param.chainId}
        and created between #{param.beginDate} and #{param.endDate}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinicId= #{param.clinicId}
        </if>
        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
            and productComposeType in (0,2)
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and productComposeType in (0,1)
        </if>
        <if test="param.projectId != null and param.projectId != ''">
            and templateId= #{param.projectId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patientId= #{param.patientId}
        </if>
    </select>

    <select id="selectHospitalTransactionDetailList" resultType="cn.abc.flink.stat.service.cis.chronic.recovery.entity.TransactionDetailEntity">
        select
        chainId,
        clinicId,
        created,
        if(type=-1,-amount,amount) as amount,
        unit,
        if(type=-1,-count, count) as count,
        createdBy,
        patientNo,
        productId as id,
        productName as name,
        feeType1,
        feeType2,
        originalPrice as unitPrice,
        if(type=-1,-count, count)*originalPrice as originalPrice,
        if(type=-1, -adjustmentPrice, adjustmentPrice) as adjustmentPrice,
        if(type=-1, -promotionPrice, promotionPrice)  as discountPrice,
        patientId,
        patientName,
        sex as patientSex,
        mobile as patientMobile,
        age as patientAge,
        templateId,
        templateName,
        spec,
        payMode,
        chargeSource as chargeSourceType,
        sellerId,
        copywriterId,
        doctorId,
        `comment`,
        departmentId
        from
        ${env}.dwd_chronic_recovery_transaction_record
        where chainId=#{param.chainId}
        and created between #{param.beginDate} and #{param.endDate}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinicId= #{param.clinicId}
        </if>
        <if test="param.projectId != null and param.projectId != ''">
            and templateId= #{param.projectId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patientId= #{param.patientId}
        </if>
        order by created desc
        <if test="param.limit != null and param.limit != 0">
            limit #{param.limit} offset #{param.offset}
        </if>
    </select>

    <select id="selectHospitalTransactionDetail" resultType="cn.abc.flink.stat.service.cis.chronic.recovery.entity.TransactionResp">
        select
        count(1) as count,
        sum(if(type=-1, -amount, amount)) as amount
        from
        ${env}.dwd_chronic_recovery_transaction_record
        where chainId=#{param.chainId}
        and created between #{param.beginDate} and #{param.endDate}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinicId= #{param.clinicId}
        </if>
        <if test="param.projectId != null and param.projectId != ''">
            and templateId= #{param.projectId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patientId= #{param.patientId}
        </if>
    </select>
</mapper>