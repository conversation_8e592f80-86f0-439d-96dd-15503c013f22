<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HoloCisHospitalMapper">

    <select id="selectHospitalPatients"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalPatient">
        select
            basic.id,
            basic.patientId,
            basic.name,
            basic.userCategory,
            basic.personalNo,
            basic.chargeType,
            basic.directDoctorId,
            basic.registerDoctorId,
            basic.registerNurseId,
            basic.hospitalStatus,
            basic.registerTime,
            basic.dischargeTime,
            basic.idCardNo,
            basic.assessmentLevel,
            if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) as duraHospitalDays,
        case
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory !='职工'
          then if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) * 200
        when basic.chargeType != '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) * 50
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.duraHospitalDays=0,1,basic.duraHospitalDays) * 200
          else 0 end as duraMedicalServiceFee,
        if(basic.allHospitalDays=0,1,basic.allHospitalDays) as allHospitalDays,

        case
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory !='职工'
          then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
        when basic.chargeType != '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 50
        when basic.chargeType = '气管切开保留气管套管' and basic.userCategory ='职工'
          then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
        else 0 end as allMedicalServiceFee,
        basic.total_price as inHospitalFee,
        basic.cost_price as costPrice
        from
        (select
        id,
        chain_id,
        clinic_id,
        patient_id as patientId,
        TRIM(BOTH '"' FROM shebao_card_info->>'name') as name,
        TRIM(BOTH '"' FROM shebao_info->>'userCategory') as userCategory,
        TRIM(BOTH '"' FROM shebao_card_info->>'cardNo') as personalNo,
        TRIM(BOTH '"' FROM shebao_card_info->>'idCardNo') as idCardNo,
        TRIM(BOTH '"' FROM shebao_info->>'assessmentLevel') as assessmentLevel,
        charge_type as chargeType,
        direct_doctor_id as directDoctorId,
        register_doctor_id as registerDoctorId,
        register_nurse_id as registerNurseId,
        if(status !=1,'出院','在院') as hospitalStatus,
        TO_CHAR(register_time,'YYYY-MM-DD') as registerTime,
        TO_CHAR(discharge_time,'YYYY-MM-DD') as dischargeTime,
        case when TO_CHAR(register_time,'YYYY-MM-DD') &lt;=#{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD')  &gt;= #{param.endDate} then (date(#{param.endDate})-date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and discharge_time is not null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time)-date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time) - date(#{param.beginDate}))
        end as duraHospitalDays,
        (date(if(discharge_time is null,CAST(NOW() AS timestamp), discharge_time)) - date(register_time)) as allHospitalDays,
        total_price,
        cost_price,
        created


        from ${cisDB}.dwd_patientorder_hospital
        where chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patient_id = #{param.patientId}
        </if>
        <if test="param.chargeType != null and param.chargeType != ''">
            and charge_type = #{param.chargeType}
        </if>
        <if test="param.hospitalStatus == 1 ">
            and status = 1
        </if>
        <if test="param.hospitalStatus == 2 ">
            and status != 1
        </if>
        <if test="param.hospitalStatus == 4 ">
            and status = 4
        </if>
        <if test="param.directDoctorId != null and param.directDoctorId != ''">
            and direct_doctor_id = #{param.directDoctorId}
        </if>
        <if test="param.personalType != null and param.personalType != ''">
            and TRIM(BOTH '"' FROM shebao_info->>'userCategory') like concat('%', #{param.personalType},'%')
        </if>
        and status !=3
        and  (TO_CHAR(discharge_time, 'YYYY-MM-DD HH24:MI:SS') &gt;= #{param.beginDate} or discharge_time is null)
        and   TO_CHAR(register_time, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate}

        ) as basic
        order by basic.registerTime desc,basic.created desc
        <if test="param.offset != null and param.size != null">
            limit #{param.size} offset #{param.offset}
        </if>
    </select>

    <!--患者tab 记录数-->
    <select id="selectHospitalPatientsCount" resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCount">
        select
        count(id) as cnt,
        sum(if(status = 1, 1, 0)) as inCount,
        sum(if(status = 2, 1, 0)) as outCount,
        cast(sum(case when TO_CHAR(register_time,'YYYY-MM-DD') &lt;=#{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD')  &gt;= #{param.endDate} then (date(#{param.endDate})-date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and discharge_time is not null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time)-date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time) - date(#{param.beginDate}))
        end) as integer) as duraHospitalDays,
        cast(sum((date(if(discharge_time is null,CAST(NOW() AS timestamp), discharge_time)) - date(register_time))) as integer) as allHospitalDays
        from ${cisDB}.dwd_patientorder_hospital
        where chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patient_id = #{param.patientId}
        </if>
        <if test="param.chargeType != null and param.chargeType != ''">
            and charge_type = #{param.chargeType}
        </if>
        <if test="param.hospitalStatus == 1 ">
            and status = 1
        </if>
        <if test="param.hospitalStatus == 2 ">
            and status != 1
        </if>
        <if test="param.hospitalStatus == 4 ">
            and status = 4
        </if>
        <if test="param.directDoctorId != null and param.directDoctorId != ''">
            and direct_doctor_id = #{param.directDoctorId}
        </if>
        <if test="param.personalType != null and param.personalType != ''">
            and TRIM(BOTH '"' FROM shebao_info->>'userCategory') like concat('%', #{param.personalType},'%')
        </if>
        and status !=3
        and  (TO_CHAR(discharge_time, 'YYYY-MM-DD HH24:MI:SS') &gt;= #{param.beginDate} or discharge_time is null)
        and   TO_CHAR(register_time, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate}
    </select>

    <!--医生tab 责任医生-->
    <select id="selectHospitalDirectDoctor"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">

        select
        tmp.doctorId,
        tmp.role,
        sum(beginPatientTimes) as beginPatientTimes,
        sum(endPatientTimes) as endPatientTimes,
        sum(registerPatientTimes) as registerPatientTimes,
        sum(dischargePatientTimes) as dischargePatientTimes,
        sum(orderTimes) as orderTimes,
        count(tmp.id) as hospitalPatientTimes,
        count(distinct tmp.patient_id) as hospitalPatientCount,
        sum(if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays)) as duraHospitalDays,
        sum(if(tmp.allHospitalDays=0,1,tmp.allHospitalDays)) as allHospitalDays,

        sum(case
            when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory !='职工'
            then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 200
            when tmp.charge_type != '气管切开保留气管套管' and tmp.userCategory ='职工'
            then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 50
            when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory ='职工'
            then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 200
            else 0 end) as duraMedicalServiceFee,
        sum(tmp.arrearsAmount) as arrearsAmount,
        sum(tmp.settlementAmount) as settlementAmount
        from (select
        ph.direct_doctor_id as doctorId,
        ph.id,
        ph.chain_id,
        ph.charge_type,
        ph.shebao_info->>'userCategory' as userCategory,
        '责任医生' as role,
        ph.patient_id,
        sum(if(  TO_CHAR(ph.register_time,'YYYY-MM-DD')  =  TO_CHAR(date(#{param.beginDate}),'YYYY-MM-DD') ,1,0)) as beginPatientTimes,
        sum(if(  TO_CHAR(ph.register_time,'YYYY-MM-DD')  =  TO_CHAR(date(#{param.endDate}),'YYYY-MM-DD') ,1,0)) as endPatientTimes,
        sum(if(TO_CHAR(ph.register_time,'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate},1,0)) as registerPatientTimes,
        sum(if(TO_CHAR(ph.discharge_time,'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate},1,0)) as dischargePatientTimes,
        0 as orderTimes,
        cast(sum(
        case when TO_CHAR(register_time,'YYYY-MM-DD') &lt;=#{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD')  &gt;= #{param.endDate} then (date(#{param.endDate})-date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and discharge_time is not null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time)-date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time) - date(#{param.beginDate}))
        end
        ) as integer) as duraHospitalDays,
        cast(sum((date(if(discharge_time is null,CAST(NOW() AS timestamp), discharge_time)) - date(register_time))) as integer) as allHospitalDays,
        sum(total_price-received_price) as arrearsAmount,
        sum(received_price) as settlementAmount
        from ${cisDB}.dwd_patientorder_hospital as ph
        where not (#{param.beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{param.endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
        and ph.status !=3
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
        <if test="param.doctorId != null and param.doctorId != ''">
            and ph.direct_doctor_id = #{param.doctorId}
        </if>
        group by ph.direct_doctor_id,ph.id,ph.charge_type,ph.shebao_info->>'userCategory',ph.patient_id,ph.chain_id
        ) tmp
        group by tmp.doctorId, tmp.role

    </select>

    <!--医生tab 登记医生-->
    <select id="selectHospitalRegisterDoctor"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">




        select
        tmp.doctorId,
        tmp.role,
        sum(beginPatientTimes) as beginPatientTimes,
        sum(endPatientTimes) as endPatientTimes,
        sum(registerPatientTimes) as registerPatientTimes,
        sum(dischargePatientTimes) as dischargePatientTimes,
        sum(orderTimes) as orderTimes,
        count(tmp.id) as hospitalPatientTimes,
        count(distinct tmp.patient_id) as hospitalPatientCount,
        sum(if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays)) as duraHospitalDays,
        sum(if(tmp.allHospitalDays=0,1,tmp.allHospitalDays)) as allHospitalDays,

        sum(case
        when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory !='职工'
        then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 200
        when tmp.charge_type != '气管切开保留气管套管' and tmp.userCategory ='职工'
        then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays) * 50
        when tmp.charge_type = '气管切开保留气管套管' and tmp.userCategory ='职工'
        then if(tmp.duraHospitalDays=0,1,tmp.duraHospitalDays)* 200
        else 0 end) as duraMedicalServiceFee,
        sum(tmp.arrearsAmount) as arrearsAmount,
        sum(tmp.settlementAmount) as settlementAmount
        from (select
        ph.register_doctor_id as doctorId,
        ph.id,
        ph.chain_id,
        ph.charge_type,
        ph.shebao_info->>'userCategory' as userCategory,
        '登记医生' as role,
        ph.patient_id,
        sum(if(  TO_CHAR(ph.register_time,'YYYY-MM-DD')  =  TO_CHAR(date(#{param.beginDate}),'YYYY-MM-DD') ,1,0)) as beginPatientTimes,
        sum(if(  TO_CHAR(ph.register_time,'YYYY-MM-DD')  =  TO_CHAR(date(#{param.endDate}),'YYYY-MM-DD') ,1,0)) as endPatientTimes,
        sum(if(TO_CHAR(ph.register_time,'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate},1,0)) as registerPatientTimes,
        sum(if(TO_CHAR(ph.discharge_time,'YYYY-MM-DD HH24:MI:SS') between #{param.beginDate} and #{param.endDate},1,0)) as dischargePatientTimes,
        0 as orderTimes,
        cast(sum(
        case when TO_CHAR(register_time,'YYYY-MM-DD') &lt;=#{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD')  &gt;= #{param.endDate} then (date(#{param.endDate})-date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and discharge_time is not null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time)-date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &gt; #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(register_time))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and  ( discharge_time is null or  TO_CHAR(discharge_time,'YYYY-MM-DD') &gt;= #{param.endDate})  then (date(if(discharge_time is null and TO_CHAR(CAST(#{param.endDate} AS timestamp),'YYYY-MM-DD') = TO_CHAR(date(CAST(NOW() AS timestamp)),'YYYY-MM-DD') ,date(CAST(NOW() AS timestamp)),date(#{param.endDate})))- date(#{param.beginDate}))
        when TO_CHAR(register_time,'YYYY-MM-DD') &lt;= #{param.beginDate} and discharge_time is not  null and  TO_CHAR(discharge_time,'YYYY-MM-DD') &lt; #{param.endDate} then (date(discharge_time) - date(#{param.beginDate}))
        end
        ) as integer) as duraHospitalDays,
        cast(sum((date(if(discharge_time is null,CAST(NOW() AS timestamp), discharge_time)) - date(register_time))) as integer) as allHospitalDays,
        sum(total_price-received_price) as arrearsAmount,
        sum(received_price) as settlementAmount
        from ${cisDB}.dwd_patientorder_hospital as ph
        where not (#{param.beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{param.endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
        and ph.status !=3
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
        <if test="param.doctorId != null and param.doctorId != ''">
            and ph.register_doctor_id = #{param.doctorId}
        </if>
        group by ph.register_doctor_id,ph.id,ph.charge_type,ph.shebao_info->>'userCategory',ph.patient_id,ph.chain_id
        ) tmp
        group by tmp.doctorId,tmp.role

    </select>

    <!--医生tab 开单医生-->
    <select id="selectHospitalOrderDoctor"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">
        select
        os.doctor_id as doctorId,
        '开单医生' as role,
        count(os.patient_order_id) as orderTimes,
        count(distinct ph.id) as hospitalPatientTimes,
        count(distinct ph.patient_id) as hospitalPatientCount
        from ${cisDB}.dwd_patientorder_hospital as ph left join ${outpatientDB}.v2_outpatient_sheet as os
        on ph.id = os.hospital_patient_order_id
        where not (#{param.beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{param.endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
        <if test="param.doctorId != null and param.doctorId != ''">
            and os.doctor_id = #{param.doctorId}
        </if>
        and os.doctor_id is not null
        and ph.status !=3
        group by os.doctor_id;
    </select>

    <!--医生tab-汇总-->
    <select id="selectHospitalSummary"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor">
        select
        count(ph.id) as hospitalPatientTimes,
        count(distinct ph.patient_id) as hospitalPatientCount,
        sum((date(if(ph.discharge_time is null,CAST(NOW() AS timestamp), ph.discharge_time)) - date(ph.register_time))) as duraHospitalDays,
        sum(ph.total_price-ph.received_price) as arrearsAmount,
        sum(ph.received_price) as settlementAmount
        from ${cisDB}.dwd_patientorder_hospital as ph
        on ph.id = hs.hospital_order_id and ph.chain_id = hs.chain_id
        where not (#{param.beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{param.endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
        and ph.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and ph.clinic_id = #{param.clinicId}
        </if>
    </select>

    <!--筛选框-责任医生-->
    <select id="selectConditionDirectDoctor"
            resultType="java.lang.String">
        select
        direct_doctor_id
        from ${cisDB}.dwd_patientorder_hospital as ph
        where not (#{beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
        and ph.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and ph.clinic_id = #{clinicId}
        </if>
        group by ph.direct_doctor_id;
    </select>

    <!--筛选框-结算类型-->
    <select id="selectConditionChargeType"
            resultType="java.lang.String">
        select
        charge_type
        from ${cisDB}.dwd_patientorder_hospital as ph
        where not (#{beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
        and ph.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and ph.clinic_id = #{clinicId}
        </if>
        group by ph.charge_type;
    </select>

    <!--筛选框-个人类别-->
    <select id="selectPersonalType"
            resultType="java.lang.String">
        select left(a.userCategory,2) from (
            select
                TRIM(BOTH '"' FROM shebao_info->> 'userCategory') as userCategory
            from ${cisDB}.dwd_patientorder_hospital as ph
            where not (#{beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
                and ph.chain_id = #{chainId}
                <if test="clinicId != null and clinicId != ''">
                    and ph.clinic_id = #{clinicId}
                </if>
        ) a
        where userCategory != '' and userCategory != 'null'
        group by left(a.userCategory,2)
    </select>

    <select id="selectHospitalPatientOrderIds" resultType="java.lang.Long">
        select
        distinct ph.id
        from ${cisDB}.dwd_patientorder_hospital as ph
        where not (#{beginDate} &gt; TO_CHAR(if(ph.discharge_time is null,CAST(CAST(date('2100-01-01') AS timestamp) AS timestamp),ph.discharge_time), 'YYYY-MM-DD HH24:MI:SS') or #{endDate} &lt; TO_CHAR(ph.register_time, 'YYYY-MM-DD HH24:MI:SS'))
        and ph.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and ph.clinic_id = #{clinicId}
        </if>
        and ph.status !=3

    </select>
    <select id="selectHospitalPatientsSummy"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary">
        select
               sum(b.allMedicalServiceFee) as allMedicalServiceFee,
               sum(b.outHospitalStatus) as outHospitalStatus,
               sum(b.inHospitalStatus) as inHospitalStatus,
               sum(b.allHospitalDays) as allHospitalDays,
               sum(b.cost_price) as costPrice,
               min(register_time) as minDate
        from (
        select
            basic.id,
            case
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and strpos('院护(失智),院护(失能),家护(失能),日护(失智),日护(失能)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 50
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and strpos('院护(门诊慢特病),家护(门诊慢特病),日护(门诊慢特病)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 9.59
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and strpos('院护(气管切开保留气管套管),家护(气管切开保留气管套管)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
            when (basic.userCategory = '退休' or basic.userCategory = '在职')  and strpos('专护(过渡期)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 0

            when basic.userCategory = '居民' and strpos('院护(失智),院护(失能),家护(失能),日护(失智),日护(失能)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 13.7
            when basic.userCategory = '居民' and strpos('院护(门诊慢特病),家护(门诊慢特病),日护(门诊慢特病)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 8.22
            when basic.userCategory = '居民' and strpos('院护(气管切开保留气管套管),家护(气管切开保留气管套管)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
            when basic.userCategory = '居民' and strpos('专护(过渡期)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 0

            when basic.userCategory = '离休' and strpos('院护(失智),院护(失能),家护(失能),日护(失智),日护(失能)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 80
            when basic.userCategory = '离休' and strpos('院护(门诊慢特病),家护(门诊慢特病),日护(门诊慢特病)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * if(basic.assessmentLevel = '4' or
            basic.assessmentLevel = '5',16.44,8.22)
            when basic.userCategory = '离休' and strpos('院护(气管切开保留气管套管),家护(气管切开保留气管套管)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 200
            when basic.userCategory = '离休' and strpos('专护(过渡期)', basic.chargeType) != 0
            then if(basic.allHospitalDays=0,1,basic.allHospitalDays) * 0
            else 0 end as allMedicalServiceFee,
            if(basic.outHospitalStatus is null,0.0, basic.outHospitalStatus) as outHospitalStatus,
            if(basic.inHospitalStatus is null,0.0, basic.inHospitalStatus) as inHospitalStatus,
            if(basic.allHospitalDays is null,0.0, cast(basic.allHospitalDays as numeric)) as allHospitalDays,
            register_time,
            cost_price
        from
        (select
            id,
            chain_id,
            clinic_id,
            register_time,
            cost_price,
            left(if(TRIM(BOTH '"' FROM shebao_info->>'userCategory') is null,'', TRIM(BOTH '"' FROM shebao_info->>'userCategory')),2) as userCategory,
            TRIM(BOTH '"' FROM shebao_info->>'assessmentLevel') as assessmentLevel,
            charge_type as chargeType,
            if(status !=1,1.0,0.0) as outHospitalStatus,
            if(status = 1,1.0,0.0) as inHospitalStatus,
            cast(((date(if(discharge_time is null,CAST(NOW() AS timestamp), discharge_time)) - date(register_time))) as integer)  as allHospitalDays
            from ${cisDB}.dwd_patientorder_hospital a
            where chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id = #{param.clinicId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id = #{param.patientId}
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                and charge_type = #{param.chargeType}
            </if>
            <if test="param.hospitalStatus == 1 ">
                and status = 1
            </if>
            <if test="param.hospitalStatus == 2 ">
                and status != 1
            </if>
            <if test="param.hospitalStatus == 4 ">
                and status = 4
            </if>
            <if test="param.directDoctorId != null and param.directDoctorId != ''">
                and direct_doctor_id = #{param.directDoctorId}
            </if>
            <if test="param.personalType != null and param.personalType != ''">
                and TRIM(BOTH '"' FROM shebao_info->>'userCategory') like concat('%', #{param.personalType},'%')
            </if>
            and status !=3
            and  (TO_CHAR(discharge_time, 'YYYY-MM-DD HH24:MI:SS') &gt;= #{param.beginDate} or discharge_time is null)
            and   TO_CHAR(register_time, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate}
        ) as basic
        ) b
    </select>

    <select id="selectHospitalPatientsIds" resultType="java.lang.String">
        select
        id
        from ${cisDB}.dwd_patientorder_hospital a
        where chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.patientId != null and param.patientId != ''">
            and patient_id = #{param.patientId}
        </if>
        <if test="param.chargeType != null and param.chargeType != ''">
            and charge_type = #{param.chargeType}
        </if>
        <if test="param.hospitalStatus == 1 ">
            and status = 1
        </if>
        <if test="param.hospitalStatus == 2 ">
            and status != 1
        </if>
        <if test="param.hospitalStatus == 4 ">
            and status = 4
        </if>
        <if test="param.directDoctorId != null and param.directDoctorId != ''">
            and direct_doctor_id = #{param.directDoctorId}
        </if>
        <if test="param.personalType != null and param.personalType != ''">
            and TRIM(BOTH '"' FROM shebao_info->>'userCategory') like concat('%', #{param.personalType},'%')
        </if>
        and status !=3
        and  (TO_CHAR(discharge_time, 'YYYY-MM-DD HH24:MI:SS') &gt;= #{param.beginDate} or discharge_time is null)
        and   TO_CHAR(register_time, 'YYYY-MM-DD HH24:MI:SS') &lt;= #{param.endDate}
    </select>

    <select id="selectTotalFee"
            resultType="cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary">
        select sum(total_price) as inHospitalFee
        from ${cisDB}.dwd_patientorder_hospital
        where
          chain_id = #{params.chainId}
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        and id::text in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>