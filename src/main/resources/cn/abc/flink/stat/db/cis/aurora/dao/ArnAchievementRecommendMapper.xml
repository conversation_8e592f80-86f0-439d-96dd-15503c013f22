<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.aurora.dao.ArnAchievementRecommendMapper">

    <select id="selectPersonnelBase"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonnelBase">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
        case when visit_source_level_2_id is not null then visit_source_level_2_id else null end as sourceLevelTwo,
        if(visit_source_from_type is null or visit_source_from_type ='', null, visit_source_from_type) as visitSourceFromType,
        if(visit_source_from is null or visit_source_from ='', null, visit_source_from) as visitSourceFrom,
        count(distinct v2_patient_order_id) as patientCount,
        referral_doctor_id as referralDoctorId
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{params.chainId}
        and product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            <choose>
                <when test="params.hisType != 100">
                    and product_type!=5
                </when>
                <otherwise>
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </otherwise>
            </choose>
        </if>
        <if test="params.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="params.hisType != 100">
            <if test="params.composeSql != null and params.composeSql != ''">
                and ${params.composeSql}
            </if>
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom,referralDoctorId
    </select>

    <select id="selectPersonnelDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonnelDispensingCost">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
        case when visit_source_level_2_id is not null then visit_source_level_2_id else null end as sourceLevelTwo,
        if(visit_source_from_type is null or visit_source_from_type ='', null, visit_source_from_type) as visitSourceFromType,
        if(visit_source_from is null or visit_source_from ='', null, visit_source_from) as visitSourceFrom,
        0 as isCopywriter,
        sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
        if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{params.chainId}
        and log_time BETWEEN #{params.beginDate} and #{params.endDate}
        and form_type = 0
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and compose_parent_product_id is null
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, isCopywriter

        <!-- 套餐不分摊到子项时，套餐里面的成本只能通过子项计算 -->
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            union all
            select
            chain_id as chainId,
            clinic_id as clinicId,
            case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
            case when visit_source_level_2_id is not null then visit_source_level_2_id else null end as sourceLevelTwo,
            if(visit_source_from_type is null or visit_source_from_type ='', null, visit_source_from_type) as visitSourceFromType,
            if(visit_source_from is null or visit_source_from ='', null, visit_source_from) as visitSourceFrom,
            0 as isCopywriter,
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
            if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{params.chainId}
            and log_time BETWEEN #{params.beginDate} and #{params.endDate}
            and compose_parent_product_id is not null
            and form_type = 0
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee2 != null and params.fee1 == null">
                and ${params.fee2}
            </if>
            <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
                and ${params.feeTypeIdSql}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, isCopywriter
        </if>

    </select>

    <select id="selectPersonalFeeClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonalFeeAmountEntity">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
        case when visit_source_level_2_id is not null then visit_source_level_2_id else null end as sourceLevelTwo,
        visit_source_from as visitSourceFrom,
        0 as isCopywriter,
        classify_level_1_id as classifyLevel1,
        <if test="level == 2">
            ifnull(classify_level_2_id, 0) as classifyLevel2,
        </if>
        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0)) as costPrice,
        sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0),0)-ifnull(record_unit_adjustment_price,0))) as originPrice,
        sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
        from  ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{params.chainId}
        <if test="params.composeSql != null and params.composeSql != ''">
            and ${params.composeSql}
        </if>
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="params.arrearsCommissionTiming != 1">
            and record_source_type != 1
        </if>
        <if test="params.arrearsCommissionTiming != 2">
            and record_source_type != 2
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="level == 2">
            group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFrom, isCopywriter, classifyLevel1, classifyLevel2
        </if>
        <if test="level != 2">
            group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFrom, isCopywriter, classifyLevel1
        </if>

    </select>

    <select id="selectPersonalAdviceClassify" resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonalFeeAmountEntity">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
        case when visit_source_level_2_id is not null then visit_source_level_2_id else null end as sourceLevelTwo,
        visit_source_from as visitSourceFrom,
        0 as isCopywriter,
        fee_type_id as feeTypeId,
        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0)) as costPrice,
        sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0),0)-ifnull(record_unit_adjustment_price,0))) as originPrice,
        sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
        from  ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{params.chainId}
        <if test="params.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="params.hisType != 100">
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_type not in (11, 18)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_type not in (18)
                and product_compose_type in (0,1)
            </if>
        </if>
        and import_flag = 0
        and record_type not in (2, 3)
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="params.arrearsCommissionTiming != 1">
            and record_source_type != 1
        </if>
        <if test="params.arrearsCommissionTiming != 2">
            and record_source_type != 2
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
        </if>
        group by chainId, clinicId, sourceLevelOne,sourceLevelTwo, isCopywriter, feeTypeId
    </select>

    <select id="selectFeeSecondClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendFeeEntity">
        select distinct
        ifnull(classify_level_1_id, '0') as classifyLevel1Id,
        ifnull(classify_level_2_id, 0) as classifyLevel2Id
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{params.chainId}
        and import_flag = 0
        and is_deleted = 0
        and create_time between #{params.beginDate} and #{params.endDate}
        <if test="params.composeSql != null and params.composeSql != ''">
            and ${params.composeSql}
        </if>
        <if test="params.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="params.arrearsCommissionTiming != 1">
            and record_source_type != 1
        </if>
        <if test="params.arrearsCommissionTiming != 2">
            and record_source_type != 2
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
    </select>

    <select id="selectFeeFirstClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendFeeEntity">
        select distinct
        ifnull(classify_level_1_id, '0') as classifyLevel1Id
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{params.chainId}
        and import_flag = 0
        and is_deleted = 0
        and create_time between #{params.beginDate} and #{params.endDate}
        <if test="params.composeSql != null and params.composeSql != ''">
            and ${params.composeSql}
        </if>
        <if test="params.hisType != 100 and params.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and product_type!=5
        </if>
    </select>

    <select id="selectAdviceFeeClassify" resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeAdviceFeeEntity">
        select distinct
            ifnull(fee_type_id,-1) as feeTypeId
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{params.chainId}
        and import_flag = 0
        and create_time between #{params.beginDate} and #{params.endDate}
        <if test="params.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="params.hisType != 100">
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                and product_compose_type in (0,2)
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
            </if>
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id = #{params.clinicId}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
        </if>
    </select>

    <select id="selectPersonalAmount"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonalFeeAmountEntity">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
        case when visit_source_level_2_id is not null then visit_source_level_2_id else null end as sourceLevelTwo,
        visit_source_from as visitSourceFrom,
        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0)) as costPrice,
        sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0),0)-ifnull(record_unit_adjustment_price,0))) as originPrice,
        sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{params.chainId}
        and product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            <choose>
                <when test="params.hisType != 100">
                    and product_type!=5
                </when>
                <otherwise>
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </otherwise>
            </choose>
        </if>
        <if test="params.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="params.hisType != 100">
            and product_compose_type in (0,2)
            and goods_fee_type in (0,1)
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        group by chainId,clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFrom
    </select>


    <select id="selectGoods"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendGoodsEntity">
        select *
        from
        (
        select
        <if test="params.payModeSql == null or params.payModeSql == ''">
            ifnull(a.chainId, b.chainId) as chainId,
            ifnull(a.clinicId, b.clinicId) as clinicId,
            ifnull(a.sourceLevelOne, b.sourceLevelOne) as sourceLevelOne,
            ifnull(a.sourceLevelTwo, b.sourceLevelTwo) as sourceLevelTwo,
            ifnull(a.visitSourceFromType, b.visitSourceFromType) as visitSourceFromType,
            ifnull(a.visitSourceFrom, b.visitSourceFrom) as visitSourceFrom,
            ifnull(a.goodsId, b.goodsId) as goodsId,
            ifnull(a.classifyLevel1, b.classifyLevel1) as classifyLevel1,
            ifnull(a.classifyLevel2, b.classifyLevel2) as classifyLevel2,
            ifnull(a.unit, b.unit) as unit,
            ifnull(a.unitCount, 0) as count,
            ifnull(a.costPrice,0) + ifnull(b.costPrice,0) as cost,
            if(a.costPrice is null, 2, 0) + if(b.hoverCode is null, 0, b.hoverCode) as hoverCode,
            ifnull(a.receivedPrice, 0) as amount,
            ifnull(a.originPrice, 0) as originPrice,
            ifnull(a.deductPrice, 0) as deductPrice,
            ifnull(a.referralDoctorId, '') as referralDoctorId
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            a.chainId as chainId,
            a.clinicId as clinicId,
            a.sourceLevelOne as sourceLevelOne,
            a.sourceLevelTwo as sourceLevelTwo,
            a.visitSourceFromType as visitSourceFromType,
            a.visitSourceFrom as visitSourceFrom,
            a.goodsId as goodsId,
            a.classifyLevel1 as classifyLevel1,
            a.classifyLevel2 as classifyLevel2,
            a.unit as unit,
            a.unitCount as count,
            null as costPrice,
            0 as hoverCode,
            a.receivedPrice as receivedPrice,
            a.originPrice as originPrice,
            a.deductPrice as deductPrice,
            a.referralDoctorId as referralDoctorId
        </if>
        from
        (
        select
            chain_id as chainId,
            clinic_id as clinicId,
            case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
            case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
            if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
            if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit)) as unit,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice,
                sum(calc_count) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
            <!-- 套餐逻辑修改 -->
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null, 0, classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
                sum(if(product_compose_type != 1, if(type=-1, -received_price, received_price),0.0)) as receivedPrice,
                sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                sum(if(product_compose_type != 1,if(type=-1, deduct_promotion_price, -deduct_promotion_price),0.0)) as deductPrice,
                sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where
        product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and chain_id=#{params.chainId}
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1,2)
            and goods_fee_type in (0,1)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 1">
            <if test="params.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="params.hisType != 100">
                and product_compose_type in (0,2)
                and goods_fee_type in (0,1)
                <if test="params.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
        </if>
        group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId,
        classifyLevel1, classifyLevel2, unit,referralDoctorId
        ) a
        <if test="params.payModeSql == null or params.payModeSql == ''">
            left OUTER JOIN
            (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
                case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
                if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
                if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0, classify_level_2_id) as classifyLevel2,
                    if(calc_unit = '', null, calc_unit) as unit,
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                    if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                    if (product_compose_type = 2, 0, if(classify_level_2_id is null,0, classify_level_2_id)) as classifyLevel2,
                    if (product_compose_type = 2, '次', if(calc_unit = '', null, calc_unit)) as unit,
                </if>
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{params.chainId}
            and log_time BETWEEN #{params.beginDate} and #{params.endDate}
            and form_type = 0
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee2 != null and params.fee1 == null">
                and ${params.fee2}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.includeReg != 1">
                and product_type!=5
            </if>
            GROUP BY chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId,
            classifyLevel1, classifyLevel2, unit
            ) b
            on a.chainId=b.chainId and b.clinicId=a.clinicId and a.sourceLevelOne=b.sourceLevelOne and
            a.sourceLevelTwo=b.sourceLevelTwo and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and
            a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit and a.visitSourceFromType = b.visitSourceFromType and
            a.visitSourceFrom = b.visitSourceFrom
        </if>
        union all
        select
        <if test="params.payModeSql == null or params.payModeSql == ''">
            ifnull(a.chainId, b.chainId) as chainId,
            ifnull(a.clinicId, b.clinicId) as clinicId,
            ifnull(a.sourceLevelOne, b.sourceLevelOne) as sourceLevelOne,
            ifnull(a.sourceLevelTwo, b.sourceLevelTwo) as sourceLevelTwo,
            ifnull(a.visitSourceFromType, b.visitSourceFromType) as visitSourceFromType,
            ifnull(a.visitSourceFrom, b.visitSourceFrom) as visitSourceFrom,
            ifnull(a.goodsId, b.goodsId) as goodsId,
            ifnull(a.classifyLevel1, b.classifyLevel1) as classifyLevel1,
            ifnull(a.classifyLevel2, b.classifyLevel2) as classifyLevel2,
            ifnull(a.unit, b.unit) as unit,
            ifnull(a.unitCount, 0) as count,
            ifnull(a.costPrice,0) + ifnull(b.costPrice,0) as cost,
            if(a.costPrice is null, 2, 0) + if(b.hoverCode is null, 0, b.hoverCode) as hoverCode,
            ifnull(a.receivedPrice, 0) as amount,
            ifnull(a.originPrice, 0) as originPrice,
            ifnull(a.deductPrice, 0) as deductPrice,
            ifnull(a.referralDoctorId, '') as referralDoctorId
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            a.chainId as chainId,
            a.clinicId as clinicId,
            a.sourceLevelOne as sourceLevelOne,
            a.sourceLevelTwo as sourceLevelTwo,
            a.visitSourceFromType as visitSourceFromType,
            a.visitSourceFrom as visitSourceFrom,
            a.goodsId as goodsId,
            a.classifyLevel1 as classifyLevel1,
            a.classifyLevel2 as classifyLevel2,
            a.unit as unit,
            a.unitCount as count,
            null as costPrice,
            0 as hoverCode,
            a.receivedPrice as receivedPrice,
            a.originPrice as originPrice,
            a.deductPrice as deductPrice,
            a.referralDoctorId as referralDoctorId
        </if>
        from
        (
        select
            chain_id as chainId,
            clinic_id as clinicId,
            case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
            case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
            if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
            if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit)) as unit,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice,
                sum(calc_count) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
            <!-- 套餐逻辑修改 -->
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null, 0, classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
                sum(if(product_compose_type != 1, if(type=-1, -received_price, received_price),0.0)) as receivedPrice,
                sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                sum(if(product_compose_type != 1,if(type=-1, deduct_promotion_price, -deduct_promotion_price),0.0)) as deductPrice,
                sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where
        product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and chain_id=#{params.chainId}
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1,2)
            and goods_fee_type in (0,1)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 1">
            <if test="params.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="params.hisType != 100">
                and product_compose_type in (0,2)
                and goods_fee_type in (0,1)
                <if test="params.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
        </if>
        group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId,
        classifyLevel1, classifyLevel2, unit,referralDoctorId
        ) a
        <if test="params.payModeSql == null or params.payModeSql == ''">
            right OUTER JOIN
            (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
                case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
                if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
                if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
                <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0, classify_level_2_id) as classifyLevel2,
                    if(calc_unit = '', null, calc_unit) as unit,
                </if>
                <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                    if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                    if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                    if (product_compose_type = 2, 0, if(classify_level_2_id is null,0, classify_level_2_id)) as classifyLevel2,
                    if (product_compose_type = 2, '次', if(calc_unit = '', null, calc_unit)) as unit,
                </if>
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{params.chainId}
            and log_time BETWEEN #{params.beginDate} and #{params.endDate}
            and form_type = 0
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id=#{params.clinicId}
            </if>
            <if test="params.fee1 != null and params.fee2 != null">
                and (${params.fee1} or ${params.fee2})
            </if>
            <if test="params.fee1 != null and params.fee2 == null">
                and ${params.fee1}
            </if>
            <if test="params.fee2 != null and params.fee1 == null">
                and ${params.fee2}
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 != null">
                and (${params.visitSource1} or ${params.visitSource2})
            </if>
            <if test="params.visitSource1 != null and params.visitSource2 == null">
                and ${params.visitSource1}
            </if>
            <if test="params.visitSource2 != null and params.visitSource1 == null">
                and ${params.visitSource2}
            </if>
            <if test="params.includeReg != 1">
                and product_type!=5
            </if>
            GROUP BY chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId,
            classifyLevel1, classifyLevel2, unit
            ) b
            on a.chainId=b.chainId and b.clinicId=a.clinicId and a.sourceLevelOne=b.sourceLevelOne and
            a.sourceLevelTwo=b.sourceLevelTwo and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and
            a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit and a.visitSourceFromType = b.visitSourceFromType and
            a.visitSourceFrom = b.visitSourceFrom
            where a.chainId is null
        </if>
        ) c
        <if test="params.size != null and params.size != 0">
            order by chainId, clinicId, sourceLevelOne,sourceLevelTwo,goodsId
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectGoodsTotal"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendTotalEntity">
        select
            count(1) as count,
            sum(amount) as amount,
            sum(originPrice) as originPrice,
            sum(deductPrice) as deductPrice
        from
        (
        select
            ifnull(a.unitCount, 0) as count,
            ifnull(a.receivedPrice, 0) as amount,
            ifnull(a.originPrice, 0) as originPrice,
            ifnull(a.deductPrice, 0) as deductPrice
        from
        (
        select
            chain_id as chainId,
            clinic_id as clinicId,
            case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
            case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
            if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
            if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit)) as unit,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice,
                sum(calc_count) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
            <!-- 套餐逻辑修改 -->
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null, 0, classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
                sum(if(product_compose_type != 1, if(type=-1, -received_price, received_price),0.0)) as receivedPrice,
                sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)),0.0)) as originPrice,
                sum(if(product_compose_type != 1,if(type=-1, deduct_promotion_price, -deduct_promotion_price),0.0)) as deductPrice,
                sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where
        product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and chain_id=#{params.chainId}
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1,2)
            and goods_fee_type in (0,1)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 1">
            <if test="params.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="params.hisType != 100">
                and product_compose_type in (0,2)
                and goods_fee_type in (0,1)
                <if test="params.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
        </if>
        group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId, classifyLevel1, classifyLevel2, unit,referralDoctorId
        ) a
        left OUTER JOIN
        (
        select
            chain_id as chainId,
            clinic_id as clinicId,
            case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
            case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
            if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
            if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0, classify_level_2_id) as classifyLevel2,
                if(calc_unit = '', null, calc_unit) as unit,
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null,0, classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(calc_unit = '', null, calc_unit)) as unit,
            </if>
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{params.chainId}
        and log_time BETWEEN #{params.beginDate} and #{params.endDate}
        and form_type = 0
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != 1">
            and product_type!=5
        </if>
        GROUP BY chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId, classifyLevel1, classifyLevel2, unit
        ) b
        on a.chainId=b.chainId and b.clinicId=a.clinicId and a.sourceLevelOne=b.sourceLevelOne and a.sourceLevelTwo=b.sourceLevelTwo and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit and a.visitSourceFromType = b.visitSourceFromType and a.visitSourceFrom = b.visitSourceFrom
        union all
        select
            ifnull(a.unitCount, 0) as count,
            ifnull(a.receivedPrice, 0) as amount,
            ifnull(a.originPrice, 0) as originPrice,
            ifnull(a.deductPrice, 0) as deductPrice
        from
        (
        select
            chain_id as chainId,
            clinic_id as clinicId,
            case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
            case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
            if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
            if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2,
                if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit)) as unit,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice,
                sum(calc_count) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
            <!-- 套餐逻辑修改 -->
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null, 0, classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001', '次', if(calc_unit = '', null, calc_unit))) as unit,
                sum(if(product_compose_type != 1, if(type=-1, -received_price, received_price),0.0)) as receivedPrice,
                sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(product_compose_type != 1,if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                sum(if(product_compose_type != 1,if(type=-1, deduct_promotion_price, -deduct_promotion_price),0.0)) as deductPrice,
                sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount,
                referral_doctor_id as referralDoctorId
            </if>
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where
        product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and chain_id=#{params.chainId}
        and create_time BETWEEN #{params.beginDate} and #{params.endDate}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != 1">
            and product_type!=5
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
            and product_compose_type in (0,1,2)
            and goods_fee_type in (0,1)
        </if>
        <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 1">
            <if test="params.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="params.hisType != 100">
                and product_compose_type in (0,2)
                and goods_fee_type in (0,1)
                <if test="params.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
        </if>
        group by chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId, classifyLevel1, classifyLevel2, unit,referralDoctorId
        ) a
        right OUTER JOIN
        (
        select
            chain_id as chainId,
            clinic_id as clinicId,
            case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
            case when visit_source_level_2_id is not null then visit_source_level_2_id when visit_source_from is not null then visit_source_from else '00000000000000000000000000000000' end as sourceLevelTwo,
            if(visit_source_from_type is null,'00000000000000000000000000000000', visit_source_from_type) as visitSourceFromType,
            if(visit_source_from is null,'00000000000000000000000000000000', visit_source_from) as visitSourceFrom,
            <if test="params.isComposeShareEqually == null or params.isComposeShareEqually == 1">
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0, classify_level_2_id) as classifyLevel2,
                if(calc_unit = '', null, calc_unit) as unit,
            </if>
            <if test="params.isComposeShareEqually != null and params.isComposeShareEqually == 0">
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null,0, classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(calc_unit = '', null, calc_unit)) as unit,
            </if>
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{params.chainId}
        and log_time BETWEEN #{params.beginDate} and #{params.endDate}
        and form_type = 0
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != 1">
            and product_type!=5
        </if>
        GROUP BY chainId, clinicId, sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom, goodsId, classifyLevel1, classifyLevel2, unit
        ) b
        on a.chainId=b.chainId and b.clinicId=a.clinicId and a.sourceLevelOne=b.sourceLevelOne and a.sourceLevelTwo=b.sourceLevelTwo and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit and a.visitSourceFromType = b.visitSourceFromType and a.visitSourceFrom = b.visitSourceFrom
        where a.chainId is null
        ) c
    </select>

    <select id="selectTransaction"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendTransactionEntity">
        SELECT
        chain_id as chainId,
        clinic_id as clinicId,
        case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
        case when visit_source_level_2_id is not null then visit_source_level_2_id end as sourceLevelTwo,
        visit_source_from_type as visitSourceFromType,
        visit_source_from as visitSourceFrom,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as personnelId,
        if(doctor_id is not null and doctor_id != '', doctor_snap_id, null) as personnelSnapId,
        patient_id as patientId,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        v2_transaction_id as transId,
        pay_type as payMode1,
        pay_sub_type as payMode2,
        visit_source_remark as visitSourceRemark,
        max(create_time) as created,
        sum(if(type=-1, -received_price, received_price)) as amount,
        sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0),0)-ifnull(record_unit_adjustment_price,0))) as originPrice,
        sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice,
        referral_doctor_id as referralDoctorId
        FROM
        ${env}.dwd_charge_transaction_record_v_partition
        where create_time between #{params.beginDate} and #{params.endDate}
        and product_type not in (18)
        <if test="params.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="params.hisType != 100">
            <if test= "params.composeSql != null and params.composeSql != ''">
                and ${params.composeSql}
            </if>
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        and import_flag = 0
        and is_deleted = 0
        and record_type not in (2, 3)
        and chain_id = #{params.chainId}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            <choose>
                <when test="params.hisType != 100">
                    and product_type!=5
                </when>
                <otherwise>
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </otherwise>
            </choose>
        </if>
        GROUP BY chainId, clinicId,sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom,patientId,patientOrderId,type,transId,payMode1,payMode2,visitSourceRemark,personnelId,personnelSnapId,referralDoctorId
        order by created desc
        <if test="params.size != null and params.size != 0">
            limit #{params.size}
            offset #{params.offset}
        </if>
    </select>

    <select id="selectTransactionTotal"
            resultType="cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendTotalEntity">
        select
            count(1) as count,
            sum(amount) as amount,
            sum(originPrice) as originPrice,
            sum(deductPrice) as deductPrice
        from
        (
        SELECT
        chain_id as chainId,
        clinic_id as clinicId,
        case when visit_source_level_1_id is not null then  visit_source_level_1_id else '00000000000000000000000000000000' end as sourceLevelOne,
        case when visit_source_level_2_id is not null then visit_source_level_2_id end as sourceLevelTwo,
        visit_source_from_type as visitSourceFromType,
        visit_source_from as visitSourceFrom,
        if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as personnelId,
        if(doctor_id is not null and doctor_id != '', doctor_snap_id, null) as personnelSnapId,
        patient_id as patientId,
        v2_patient_order_id as patientOrderId,
        charge_sheet_type as type,
        v2_transaction_id as transId,
        pay_type as payMode1,
        pay_sub_type as payMode2,
        visit_source_remark as visitSourceRemark,
        sum(if(type=-1, -received_price, received_price)) as amount,
        sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0),0)-ifnull(record_unit_adjustment_price,0))) as originPrice,
        sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice,
        referral_doctor_id as referralDoctorId
        FROM
        ${env}.dwd_charge_transaction_record_v_partition
        where create_time between #{params.beginDate} and #{params.endDate}
        and product_type not in (18)
        <if test="params.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="params.hisType != 100">
            <if test= "params.composeSql != null and params.composeSql != ''">
                and ${params.composeSql}
            </if>
            <if test="params.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        and import_flag = 0
        and record_type not in (2, 3)
        and chain_id = #{params.chainId}
        <if test="params.arrearsCommissionTimingSql != null and params.arrearsCommissionTimingSql != ''">
            and ${params.arrearsCommissionTimingSql}
        </if>
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <if test="params.payModeSql != null and params.payModeSql != ''">
            and ${params.payModeSql}
        </if>
        <if test="params.fee1 != null and params.fee2 != null">
            and (${params.fee1} or ${params.fee2})
        </if>
        <if test="params.fee1 != null and params.fee2 == null">
            and ${params.fee1}
        </if>
        <if test="params.fee2 != null and params.fee1 == null">
            and ${params.fee2}
        </if>
        <if test="params.feeTypeIdSql != null and params.feeTypeIdSql != ''">
            and ${params.feeTypeIdSql}
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 != null">
            and (${params.visitSource1} or ${params.visitSource2})
        </if>
        <if test="params.visitSource1 != null and params.visitSource2 == null">
            and ${params.visitSource1}
        </if>
        <if test="params.visitSource2 != null and params.visitSource1 == null">
            and ${params.visitSource2}
        </if>
        <if test="params.includeReg != null and params.includeReg != 1">
            <choose>
                <when test="params.hisType != 100">
                    and product_type!=5
                </when>
                <otherwise>
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </otherwise>
            </choose>
        </if>
        GROUP BY chainId, clinicId,sourceLevelOne,sourceLevelTwo,visitSourceFromType,visitSourceFrom,personnelId,personnelSnapId,patientId,patientOrderId,type,transId,payMode1,payMode2,visitSourceRemark,referralDoctorId
        ) aa
    </select>
</mapper>