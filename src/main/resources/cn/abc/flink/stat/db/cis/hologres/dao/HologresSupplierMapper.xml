<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresSupplierMapper">

    <select id="getSalesForInKind" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            a.chain_id as chainId,
            <if test="param.clinicId != null and param.clinicId != ''">
                a.organ_id as clinicId,
            </if>
            a.supplier_id as supplierId,
            count(a.goods_id) as salesForInKind
        from
        (
            select
                distinct
                chain_id,
                <if test="param.clinicId != null and param.clinicId != ''">
                    organ_id,
                </if>
                supplier_id,
                goods_id
            from ${db}.dwd_goods_inventory
            where
                1=1
                and chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and action in ('发药', '修正发药', '退药', '修正退药')
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="supplierIds != null and supplierIds.size() > 0">
                    and supplier_id in
                    <foreach collection="supplierIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <include refid="stockIdIn"/>
        ) a
        group by
            a.chain_id,
            <if test="param.clinicId != null and param.clinicId != ''">
                a.organ_id,
            </if>
            a.supplier_id
    </select>

    <select id="getSalesForInKindSummary" resultType="integer">
        select
            count(a.goods_id) as salesForInKind
        from
            (
            select distinct
                goods_id
            from ${db}.dwd_goods_inventory
            where
                1=1
                and chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and action in ('发药', '修正发药', '退药', '修正退药')
                and create_date between #{param.beginDate} and #{param.endDate}

                <if test="supplierIds != null and supplierIds.size() > 0">
                    and supplier_id in
                    <foreach collection="supplierIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
                <include refid="stockIdIn"/>
            ) a
    </select>

    <!--供应商详情tab-记录数-->
    <select id="getSupplierDetailInfoCount" resultType="java.lang.Long">
        select
            count(goods_id) as cnt
        from(
            select
                goods_id
            from
                ${db}.dwd_goods_inventory
            where
                chain_id = #{param.chainId}
                and supplier_id = #{param.supplierId}
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                <if test="param.feeType1 != null and param.feeType2 != null">
                    and (${param.feeType1} or ${param.feeType2})
                </if>
                <if test="param.feeType1 != null and param.feeType2 == null">
                    and ${param.feeType1}
                </if>
                <if test="param.feeType2 != null and param.feeType1 == null">
                    and ${param.feeType2}
                </if>
                and action in ('采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
            group by
                goods_id
        ) tmp
    </select>

    <select id="getSupplierList" resultType="String">
        select
            supplier_id as supplierId
        from
            ${db}.dwd_goods_inventory
        where
            1=1
            and chain_id = #{param.chainId}
            and supplier_id is not null
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id = #{param.clinicId}
            </if>
            and action in ('采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.pharmacyType == 0 ">
                and pharmacy_type != 2
            </if>
            <if test="param.pharmacyType == 2 ">
                and pharmacy_type = 2
            </if>
        group by
            supplier_id
    </select>

    <sql id="stockIdIn">
        and stock_id in (
            select
                distinct
                stock_id
            from ${db}.dwd_goods_inventory
            where
                1=1
                and chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and action = '采购入库'
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.supplierId != null and param.supplierId != ''">
                    and supplier_id = #{param.supplierId}
                </if>
                <if test="param.pharmacyType == 0 ">
                    and pharmacy_type != 2
                </if>
                <if test="param.pharmacyType == 2 ">
                    and pharmacy_type = 2
                </if>
                <if test="param.feeType1 != null and param.feeType2 != null">
                    and (${param.feeType1} or ${param.feeType2})
                </if>
                <if test="param.feeType1 != null and param.feeType2 == null">
                    and ${param.feeType1}
                </if>
                <if test="param.feeType2 != null and param.feeType1 == null">
                    and ${param.feeType2}
                </if>
        )
    </sql>


    <select id="getResultSupplierList" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            a.chain_id as chainId,
            <if test="param.clinicId != null and param.clinicId != ''">
                a.organ_id as clinicId,
            </if>
            a.supplier_id as  supplierId,
            <if test="param.hisType != null and param.hisType == 10">
                sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_count, 0.0)) as inInitCount,
                sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost, 0.0)) as inInitAmount,
                sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) as inInitAmountExcludedTax,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) as inCount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) as inAmount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) as inAmountExcludedTax,
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_count, 0.0)) as inInitCount,
                sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) as inInitAmount,
                sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) as inInitAmountExcludedTax,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) as inCount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) as inAmount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) as inAmountExcludedTax,
            </if>
            abs(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13),action_count,0.0))) as inInitReturnCount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13),action_total_cost,0.0))) as inInitReturnAmount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13),action_total_cost_exclude_tax,0.0))) as inInitReturnAmountExcludedTax,
            abs(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null),action_count,0.0))) as outReturnCount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null),action_total_cost,0.0))) as outReturnAmount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null),action_total_cost_exclude_tax,0.0))) as outReturnAmountExcludedTax,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),action_count,0.0))) as salesCount,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0))) as salesAmount,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0))) as salesAmountExcludedTax
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classify_level1 as classifyLevel1Id
                ,concat(a.supplier_id, '-', a.classify_level1) as supplierIdConcatClassifyLevel1Id
            </if>
        from ${db}.dwd_goods_inventory a
        where
            1=1
            and a.chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and a.organ_id = #{param.clinicId}
            </if>
            and action in ('初始化入库' ,'采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.supplierId != null and param.supplierId != ''">
                and a.supplier_id = #{param.supplierId}
            </if>
            <if test="param.pharmacyType == 0 ">
                and pharmacy_type != 2
            </if>
            <if test="param.pharmacyType == 2 ">
                and pharmacy_type = 2
            </if>
        group by
            a.chain_id,
            <if test="param.clinicId != null and param.clinicId != ''">
                a.organ_id,
            </if>
            a.supplier_id
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classify_level1
            </if>
    </select>

    <select id="getSupplierListTotal" resultType="long">
        select
            count(1)
        from(
            select
                a.chain_id as chainId,
                <if test="param.clinicId != null and param.clinicId != ''">
                    a.organ_id as clinicId,
                </if>
                a.supplier_id as  supplierId,
                sum(if(action in ('采购入库', '修正入库'), action_count, 0.0)) as inCount,
                sum(if(action in ('采购入库', '修正入库'), action_total_cost, 0.0)) as inAmount,
                sum(if(action in ('采购入库', '修正入库'), action_total_cost_exclude_tax, 0.0)) as inAmountExcludedTax,
                abs(sum(if(action in ('退货出库', '修正退货出库'),action_count,0.0))) as outReturnCount,
                abs(sum(if(action in ('退货出库', '修正退货出库'),action_total_cost,0.0))) as outReturnAmount,
                abs(sum(if(action in ('退货出库', '修正退货出库'),action_total_cost_exclude_tax,0.0))) as outReturnAmountExcludedTax,
                abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),action_count,0.0))) as salesCount,
                abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0))) as salesAmount,
                abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0))) as salesAmountExcludedTax
            from ${db}.dwd_goods_inventory a
            where
                1=1
                and a.chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and a.organ_id = #{param.clinicId}
                </if>
                and action in ('初始化入库' ,'采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.supplierId != null and param.supplierId != ''">
                    and a.supplier_id = #{param.supplierId}
                </if>
                <if test="param.pharmacyType == 0 ">
                    and pharmacy_type != 2
                </if>
                <if test="param.pharmacyType == 2 ">
                    and pharmacy_type = 2
                </if>
            group by
                a.chain_id,
                <if test="param.clinicId != null and param.clinicId != ''">
                    a.organ_id,
                </if>
                a.supplier_id
        )t
    </select>

    <select id="getNewInOrderExcludedReturn"  resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            a.supplierId,
            count(stockId) as outReturnOrderCount
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classifyLevel1Id as classifyLevel1Id
                ,concat(a.supplierId, '-', a.classifyLevel1Id) as supplierIdConcatClassifyLevel1Id
            </if>
        from
        (
            select
                distinct
                gi.supplier_id as supplierId,
                gi.stock_id as stockId
                <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                    ,gi.classify_level1 as classifyLevel1Id
                </if>
            from ${db}.dwd_goods_inventory gi
            where
                action in ('退货出库', '修正退货出库')
                and chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.pharmacyType == 0 ">
                    and pharmacy_type != 2
                </if>
                <if test="param.pharmacyType == 2 ">
                    and pharmacy_type = 2
                </if>
                <if test="supplierIds != null and supplierIds.size() > 0">
                    and supplier_id in
                    <foreach collection="supplierIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
        )
        a
        group by
            a.supplierId
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classifyLevel1Id
            </if>
    </select>

    <select id="getNewListInOutReturnSalesKindAndAllStockCount" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            a.chain_id as chainId,
            <if test="param.clinicId != null and param.clinicId != ''">
                a.organ_id as clinicId,
            </if>
            a.supplier_id as supplierId,
            sum(inInitKindGoodsId) as inInitKind,
            sum(inKindGoodsId) as inKind,
            sum(inInitReturnKindGoodsId) as inInitReturnKind,
            sum(outReturnKindGoodsId) as outReturnKind,
            sum(salesKindGoodsId) as salesKind,
            sum(stockCount) as inOrderCount
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classifyLevel1Id as classifyLevel1Id
                ,concat(a.supplier_id, '-', a.classifyLevel1Id) as supplierIdConcatClassifyLevel1Id
            </if>
        from (
            select
                chain_id,
                <if test="param.clinicId != null and param.clinicId != ''">
                    organ_id,
                </if>
                supplier_id,
                goods_id,
                <if test="param.hisType != null and param.hisType == 10">
                    if(sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inInitKindGoodsId,
                    if(sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inKindGoodsId,
                </if>
                <if test="param.hisType != null and param.hisType != 10">
                    if(sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inInitKindGoodsId,
                    if(sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inKindGoodsId,
                </if>
                if(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13), 1.0, 0.0)) > 0, 1.0, 0.0) as inInitReturnKindGoodsId,
                if(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), 1.0, 0.0)) > 0, 1.0, 0.0) as outReturnKindGoodsId,
                if(sum(if(action in ('发药', '修正发药', '退药', '修正退药') and action_count != 0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as salesKindGoodsId,
                count(if(action in ('采购入库', '修正入库'), stock_id,null)) stockCount
                <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                    ,classify_level1 as classifyLevel1Id
                </if>
            from ${db}.dwd_goods_inventory
            where
                1=1
                and chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and action in ('初始化入库', '采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.supplierId != null and param.supplierId != ''">
                    and supplier_id = #{param.supplierId}
                </if>
                <if test="param.pharmacyType == 0 ">
                    and pharmacy_type != 2
                </if>
                <if test="param.pharmacyType == 2 ">
                    and pharmacy_type = 2
                </if>
                <if test="supplierIds != null and supplierIds.size() > 0">
                    and supplier_id in
                    <foreach collection="supplierIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
            group by
                chain_id,
                <if test="param.clinicId != null and param.clinicId != ''">
                    organ_id,
                </if>
                supplier_id,
                goods_id
                <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                    ,classify_level1
                </if>
        ) a
        group by
            chain_id,
            <if test="param.clinicId != null and param.clinicId != ''">
                organ_id,
            </if>
            supplier_id
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classifyLevel1Id
            </if>
    </select>

    <select id="getSupplierListSummary" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            a.chain_id as chainId,
            <if test="param.hisType != null and param.hisType == 10">
                sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_count, 0.0)) as inInitCount,
                sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost, 0.0)) as inInitAmount,
                sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) as inInitAmountExcludedTax,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) as inCount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) as inAmount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) as inAmountExcludedTax,
            </if>
            <if test="param.hisType != null and param.hisType != 10">
                sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_count, 0.0)) as inInitCount,
                sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost, 0.0)) as inInitAmount,
                sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22), action_total_cost_exclude_tax, 0.0)) as inInitAmountExcludedTax,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_count, 0.0)) as inCount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost, 0.0)) as inAmount,
                sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null), action_total_cost_exclude_tax, 0.0)) as inAmountExcludedTax,
            </if>
            abs(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13),action_count,0.0))) as inInitReturnCount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13),action_total_cost,0.0))) as inInitReturnAmount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13),action_total_cost_exclude_tax,0.0))) as inInitReturnAmountExcludedTax,
            abs(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null),action_count,0.0))) as outReturnCount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null),action_total_cost,0.0))) as outReturnAmount,
            abs(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null),action_total_cost_exclude_tax,0.0))) as outReturnAmountExcludedTax,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),action_count,0.0))) as salesCount,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),if(origin_flat_price is not null,origin_flat_price,origin_stock_price),0.0))) as salesAmount,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'),if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax),0.0))) as salesAmountExcludedTax
        from ${db}.dwd_goods_inventory a
        where
            1=1
            and a.chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and a.organ_id = #{param.clinicId}
            </if>
            and action in ('初始化入库' ,'采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and a.create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.supplierId != null and param.supplierId != ''">
                and a.supplier_id = #{param.supplierId}
            </if>
            <if test="param.pharmacyType == 0 ">
                and a.pharmacy_type != 2
            </if>
            <if test="param.pharmacyType == 2 ">
                and a.pharmacy_type = 2
            </if>
        group by
            a.chain_id
            <if test="param.clinicId != null and param.clinicId != ''">
                ,a.organ_id
            </if>
    </select>

    <select id="getNewSummaryInOutReturnSalesKindAndAllStockCount" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            sum(inInitKindGoodsId) as inInitKind,
            sum(inKindGoodsId) as inKind,
            sum(inInitReturnKindGoodsId) as inInitReturnKind,
            sum(outReturnKindGoodsId) as outReturnKind,
            sum(salesKindGoodsId) as salesKind,
            sum(stockCount) as inOrderCount
        from
        (
            select
                goods_id,
                <if test="param.hisType != null and param.hisType == 10">
                    if(sum(if(action in ('采购入库', '初始化入库', '修正入库') and scene in(3, 8, 20, 21, 22) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inInitKindGoodsId,
                    if(sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inKindGoodsId,
                </if>
                <if test="param.hisType != null and param.hisType != 10">
                    if(sum(if(action in ('采购入库', '修正入库', '退货出库', '修正退货出库') and scene in(3, 8, 12, 13, 20, 21, 22) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inInitKindGoodsId,
                    if(sum(if(action in ('采购入库', '修正入库') and (scene in (0, 1, 2, 4, 5 ,7) or scene is null) AND action_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inKindGoodsId,
                </if>
                if(sum(if(action in ('退货出库', '修正退货出库') and scene in (12, 13), 1.0, 0.0)) > 0, 1.0, 0.0) as inInitReturnKindGoodsId,
                if(sum(if(action in ('退货出库', '修正退货出库') and (scene in (10, 11) or scene is null), 1.0, 0.0)) > 0, 1.0, 0.0) as outReturnKindGoodsId,
                if(sum(if(action in ('发药', '修正发药', '退药', '修正退药') and action_count != 0.0, 1.0, 0.0)) > 0.0,1.0,0.0) as salesKindGoodsId,
                count(if(action in ('采购入库', '修正入库'), stock_id,null)) stockCount
            from
                ${db}.dwd_goods_inventory
            where
                1=1
                and chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and action in ('初始化入库' ,'采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.supplierId != null and param.supplierId != ''">
                    and supplier_id = #{param.supplierId}
                </if>
                <if test="param.pharmacyType == 0 ">
                    and pharmacy_type != 2
                </if>
                <if test="param.pharmacyType == 2 ">
                    and pharmacy_type = 2
                </if>
                <if test="supplierIds != null and supplierIds.size() > 0">
                    and supplier_id in
                    <foreach collection="supplierIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
            group by
                goods_id
        ) a
    </select>

    <select id="getNewRemainingInfoSummary"  resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            count(d.goods_id) as remainingKind,
            sum(d.endCount) as remainingCount
        from (
            select
                a.goods_id,
                sum(a.afterCount) as endCount
            from (
                select
                    goods_id,
                    coalesce(batch_after_count, after_count) as afterCount,
                    row_number() OVER (PARTITION BY organ_id,supplier_id,goods_id,batch_id ORDER BY create_date desc, id desc) as sort_num
                from
                    ${db}.dwd_goods_inventory
                where
                    chain_id = #{param.chainId}
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and organ_id = #{param.clinicId}
                    </if>
                    and ds between #{param.beginDateDs} and #{param.endDateDs}
                    and create_date between #{param.beginDate} and #{param.endDate}
                    <if test="param.pharmacyType == 0 ">
                        and pharmacy_type != 2
                    </if>
                    <if test="param.supplierId != null and param.supplierId != ''">
                        and supplier_id = #{param.supplierId}
                    </if>
                    <if test="param.pharmacyType == 2 ">
                        and pharmacy_type = 2
                    </if>
            ) a
            where
                a.sort_num = 1
                and a.afterCount != 0
            group by
                a.goods_id
        ) d
    </select>

    <select id="getNewRemainingInfo"  resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            a.supplier_id supplierId,
            approx_count_distinct(a.goods_id) as remainingKind,
            sum(a.endCount) as remainingCount
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classifyLevel1Id as classifyLevel1Id
                ,concat(a.supplier_id, '-', a.classifyLevel1Id) as supplierIdConcatClassifyLevel1Id
            </if>
        from (
            select
                supplier_id,
                goods_id,
                coalesce(batch_after_count, after_count) endCount,
                row_number() OVER (PARTITION BY organ_id,supplier_id,goods_id,batch_id ORDER BY create_date desc, id desc) as sort_num
                <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                    ,classify_level1 as classifyLevel1Id
                </if>
            from
                ${db}.dwd_goods_inventory
            where
                chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and create_date between #{param.beginDate} and #{param.endDate}
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.pharmacyType == 0 ">
                    and pharmacy_type != 2
                </if>
                <if test="param.pharmacyType == 2 ">
                    and pharmacy_type = 2
                </if>
                <if test="param.supplierId != null and param.supplierId != ''">
                    and supplier_id = #{param.supplierId}
                </if>
            ) a
        where
            a.sort_num = 1
            and a.endCount != 0
        group by
            a.supplier_id
            <if test="param.isDistinguishClassify != null and param.isDistinguishClassify == 1">
                ,a.classifyLevel1Id
            </if>
    </select>

    <select id="getSupplierListDetail" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierDetailInfo">
        select
            a.goods_id as goodsId,
            sum(if(action in ('采购入库', '修正入库'), action_count, 0.0)) as inCount,
            sum(if(action in ('采购入库', '修正入库'), action_total_cost, 0.0)) as inAmount,
            sum(if(action in ('采购入库', '修正入库'), action_total_cost_exclude_tax, 0.0)) as inAmountExcludedTax,
            abs(sum(if(action in ('退货出库', '修正退货出库'), action_count, 0.0))) as outReturnCount,
            abs(sum(if(action in ('退货出库', '修正退货出库'), action_total_cost, 0.0))) as outReturnAmount,
            abs(sum(if(action in ('退货出库', '修正退货出库'), action_total_cost_exclude_tax, 0.0))) as outReturnAmountExcludedTax,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'), action_count, 0.0))) as salesCount,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price is not null,origin_flat_price,origin_stock_price), 0.0))) as salesAmount,
            abs(sum(if(action in ('发药', '修正发药', '退药', '修正退药'), if(origin_flat_price_exclude_tax is not null,origin_flat_price_exclude_tax,origin_stock_price_exclude_tax), 0.0))) as salesAmountExcludedTax
        from
            ${db}.dwd_goods_inventory a
        where
            a.chain_id = #{param.chainId}
            and a.supplier_id = #{param.supplierId}
            and a.ds between #{param.beginDateDs} and #{param.endDateDs}
            and a.create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and a.organ_id = #{param.clinicId}
            </if>
            <if test="param.feeType1 != null and param.feeType2 != null">
                and (${param.feeType1} or ${param.feeType2})
            </if>
            <if test="param.feeType1 != null and param.feeType2 == null">
                and ${param.feeType1}
            </if>
            <if test="param.feeType2 != null and param.feeType1 == null">
                and ${param.feeType2}
            </if>
            and action in ('采购入库', '修正入库', '退货出库', '修正退货出库', '发药', '修正发药', '退药', '修正退药')
        group by
            a.goods_id
        order by
            a.goods_id
            <if test="param.offset != null and param.size != null ">
                limit #{param.size} offset #{param.offset}
            </if>
    </select>

    <select id="getNewSupplierDetailReturnGoodsCnt" resultType='cn.abc.flink.stat.service.cis.supplier.domain.SupplierCount'>
        select
            goods_id as goodsId,
            abs(sum(action_count)) count
        from
            ${db}.dwd_goods_inventory
        where
            action in ('退货出库', '修正退货出库')
            and chain_id = #{param.chainId}
            and supplier_id = #{param.supplierId}
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id = #{param.clinicId}
            </if>
            <if test="goodsIds != null and goodsIds.size() > 0">
                and goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <include refid="stockIdIn"/>
        group  by
            goods_id
    </select>

    <select id="getNewSupplierDetailSaleCnt" resultType='cn.abc.flink.stat.service.cis.supplier.domain.SupplierCount'>
        select
            gi.goods_id as goodsId,
            abs(sum(gi.action_count)) as count
        from
            ${db}.dwd_goods_inventory as gi
        where
            gi.chain_id = #{param.chainId}
            and gi.ds between #{param.beginDateDs} and #{param.endDateDs}
            and gi.create_date between #{param.beginDate} and #{param.endDate}
            and gi.supplier_id = #{param.supplierId}
            and gi.supplier_id =#{param.supplierId}
            and gi.action in ('发药', '修正发药', '退药', '修正退药')
            <if test="param.clinicId != null and param.clinicId != ''">
                and gi.organ_id = #{param.clinicId}
            </if>

            <if test="goodsIds != null and goodsIds.size() > 0">
                and gi.goods_id in
                <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                    #{id}
                </foreach>
            </if>
            <include refid="stockIdIn"/>
        group by
            gi.goods_id

    </select>

    <select id="getNewSupplierDetailRemainingCnt" resultType='cn.abc.flink.stat.service.cis.supplier.domain.SupplierCount'>
        select
            a.goods_id as goodsId,
            sum(a.afterCount) as count
        from (
            select
                goods_id,
                coalesce(batch_after_count, after_count) as afterCount,
                row_number() OVER (PARTITION BY organ_id,supplier_id,goods_id,batch_id ORDER BY create_date desc, id desc) as sort_num
            from
                ${db}.dwd_goods_inventory
            where
                chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                and supplier_id = #{param.supplierId}
                and ds between #{param.beginDateDs} and #{param.endDateDs}
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="goodsIds != null and goodsIds.size() > 0">
                    and goods_id in
                    <foreach collection="goodsIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
            ) a
        where
            a.sort_num = 1
            and a.afterCount != 0
        group by
            a.goods_id
    </select>

    <select id="getShenQiResultSupplierList" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
        a.chain_id as chainId,
        <if test="param.clinicId != null and param.clinicId != ''">
            a.organ_id as clinicId,
        </if>
        a.supplier_id as  supplierId,
        sum(if(action ='采购入库' and (supplier_name !='盘点入库' or supplier_name is null) ,stock_in_cost,0.0)) as inAmount,
        sum(if(action ='采购入库' and (supplier_name !='盘点入库' or supplier_name is null) and classify_level1 = '1-12',stock_in_cost,0.0)) as decoctionPieceInAmount,
        sum(if(action ='采购入库' and (supplier_name !='盘点入库' or supplier_name is null) and classify_level1 = '1-1',stock_in_cost,0.0)) as westernMedicineInAmount
        from ${db}.dwd_goods_inventory a
        where
        1=1
        and a.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and a.organ_id = #{param.clinicId}
        </if>
        and ( action in ('退货出库','发药','退药') or (action = '采购入库'  and (supplier_name !='盘点入库' or supplier_name is null)))
        and create_date between #{param.beginDate} and #{param.endDate}
        <if test="param.supplierId != null and param.supplierId != ''">
            and a.supplier_id = #{param.supplierId}
        </if>
        and supplier_name != '盘点入库'

        <if test="param.pharmacyType == 0 ">
            and pharmacy_type != 2
        </if>
        <if test="param.pharmacyType == 2 ">
            and pharmacy_type = 2
        </if>
        group by
        a.chain_id,
        <if test="param.clinicId != null and param.clinicId != ''">
            a.organ_id,
        </if>
        a.supplier_id
    </select>


    <select id="getShenQiInKind" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
            a.chain_id as chainId,
            <if test="param.clinicId != null and param.clinicId != ''">
                a.organ_id as clinicId,
            </if>
            a.supplier_id as supplierId,
            sum(inKindGoodsId) as inKind,
            sum(decoctionPieceInKindGoodsId) as decoctionPieceInKind,
            sum(westernMedicineInKindGoodsId) as westernMedicineInKind
        from (
            select
                chain_id,
                <if test="param.clinicId != null and param.clinicId != ''">
                    organ_id,
                </if>
                supplier_id,
                goods_id,
                if(sum(if(action = '采购入库' and (supplier_name != '盘点入库'OR supplier_name IS NULL)AND stock_in_count != 0.0, 1.0, 0.0))> 0.0, 1.0, 0.0) as inKindGoodsId,
                if(sum(if(action = '采购入库' and (supplier_name != '盘点入库'OR supplier_name IS NULL)AND stock_in_count != 0.0, 1.0, 0.0))> 0.0 and classify_level1 = '1-12' , 1.0, 0.0) as decoctionPieceInKindGoodsId,
                if(sum(if(action = '采购入库' and (supplier_name != '盘点入库'OR supplier_name IS NULL)AND stock_in_count != 0.0, 1.0, 0.0))> 0.0 and classify_level1 = '1-1', 1.0, 0.0) as westernMedicineInKindGoodsId
            from ${db}.dwd_goods_inventory
            where
                1=1
                and chain_id = #{param.chainId}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and organ_id = #{param.clinicId}
                </if>
                AND (action in ('退货出库', '发药', '退药') or (action = '采购入库' and (supplier_name != '盘点入库' or supplier_name is null)))
                and create_date between #{param.beginDate} and #{param.endDate}
                <if test="param.supplierId != null and param.supplierId != ''">
                    and supplier_id = #{param.supplierId}
                </if>
                and supplier_name != '盘点入库'

                <if test="param.pharmacyType == 0 ">
                    and pharmacy_type != 2
                </if>
                <if test="param.pharmacyType == 2 ">
                    and pharmacy_type = 2
                </if>
                <if test="supplierIds != null and supplierIds.size() > 0">
                    and supplier_id in
                    <foreach collection="supplierIds" separator="," close=")" open=" (" item="id">
                        #{id}
                    </foreach>
                </if>
            group by
                chain_id,
                <if test="param.clinicId != null and param.clinicId != ''">
                    organ_id,
                </if>
                supplier_id,
                goods_id,
                classify_level1
        ) a
        group by
            chain_id,
            <if test="param.clinicId != null and param.clinicId != ''">
                organ_id,
            </if>
            supplier_id
    </select>

    <select id="getShenQiSupplierListSummary" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
        a.chain_id as chainId,
        <if test="param.clinicId != null and param.clinicId != ''">
            a.organ_id as clinicId,
        </if>
        sum(if(action ='采购入库' and (supplier_name !='盘点入库' or supplier_name is null) ,stock_in_cost,0.0)) as inAmount,
        sum(if(action ='采购入库' and (supplier_name !='盘点入库' or supplier_name is null) and classify_level1 = '1-12',stock_in_cost,0.0)) as decoctionPieceInAmount,
        sum(if(action ='采购入库' and (supplier_name !='盘点入库' or supplier_name is null) and classify_level1 = '1-1',stock_in_cost,0.0)) as westernMedicineInAmount
        from ${db}.dwd_goods_inventory a
        where
        1=1
        and a.chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and a.organ_id = #{param.clinicId}
        </if>
        and ( a.action in ('退货出库','发药','退药') or (a.action = '采购入库'  and (a.supplier_name !='盘点入库' or a.supplier_name is null)))
        and a.create_date between #{param.beginDate} and #{param.endDate}
        and a.supplier_name != '盘点入库'
        <if test="param.supplierId != null and param.supplierId != ''">
            and a.supplier_id = #{param.supplierId}
        </if>
        <if test="param.pharmacyType == 0 ">
            and a.pharmacy_type != 2
        </if>
        <if test="param.pharmacyType == 2 ">
            and a.pharmacy_type = 2
        </if>
        group by
        a.chain_id
        <if test="param.clinicId != null and param.clinicId != ''">
            ,a.organ_id
        </if>
    </select>

    <select id="getShenQiSummaryInKind" resultType="cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo">
        select
        sum(inKindGoodsId) as inKind,
        sum(decoctionPieceInKindGoodsId) as decoctionPieceInKind,
        sum(westernMedicineInKindGoodsId) as westernMedicineInKind
        from
        (
        select
        goods_id,
        if(sum(if(action = '采购入库' and (supplier_name != '盘点入库'OR supplier_name IS NULL)AND stock_in_count != 0.0, 1.0, 0.0)) > 0.0, 1.0, 0.0) as inKindGoodsId,
        if(sum(if(action = '采购入库' and (supplier_name != '盘点入库'OR supplier_name IS NULL)AND stock_in_count != 0.0, 1.0, 0.0))> 0.0 and classify_level1 = '1-12' , 1.0, 0.0) as decoctionPieceInKindGoodsId,
        if(sum(if(action = '采购入库' and (supplier_name != '盘点入库'OR supplier_name IS NULL)AND stock_in_count != 0.0, 1.0, 0.0))> 0.0 and classify_level1 = '1-1', 1.0, 0.0) as westernMedicineInKindGoodsId
        from ${db}.dwd_goods_inventory
        where
        1=1
        and chain_id = #{param.chainId}
        <if test="param.clinicId != null and param.clinicId != ''">
            and organ_id = #{param.clinicId}
        </if>
        AND (action in ('退货出库', '发药', '退药') or (action = '采购入库' and (supplier_name != '盘点入库' or supplier_name is null)))
        and create_date between #{param.beginDate} and #{param.endDate}
        <if test="param.supplierId != null and param.supplierId != ''">
            and supplier_id = #{param.supplierId}
        </if>
        and supplier_name != '盘点入库'

        <if test="param.pharmacyType == 0 ">
            and pharmacy_type != 2
        </if>
        <if test="param.pharmacyType == 2 ">
            and pharmacy_type = 2
        </if>
        <if test="supplierIds != null and supplierIds.size() > 0">
            and supplier_id in
            <foreach collection="supplierIds" separator="," close=")" open=" (" item="id">
                #{id}
            </foreach>
        </if>
        group by
        goods_id,
        classify_level1
        ) a
    </select>
</mapper>
