<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresAllocationMapper">

    <select id="selectAllocationDetailsList" parameterType="cn.abc.flink.stat.pojo.allocation.AllocationParam"
            resultType="cn.abc.flink.stat.pojo.allocation.InventoryAllocationDetail">
        SELECT
            create_date AS allocationDate,
            <if test="param.allocationType != null">
                <if test="param.allocationType == 1">
                    from_organ_id AS fromOrganId,
                    to_organ_id AS toOrganId,
                </if>
                <if test="param.allocationType == 2">
                    out_pharmacy_no AS fromPharmacyNo,
                    in_pharmacy_no AS toPharmacyNo,
                </if>
            </if>
            <if test="param.allocationType == null">
                from_organ_id AS fromOrganId,
                to_organ_id AS toOrganId,
            </if>
            goods_id             as goodsId,
            classify_level1      as feeType1,
            batch_no             as batchNo,
            expiry_date          as expiryDate,
            action_piece_count   AS pieceCount,
            action_package_count AS packageCount,
            package_cost_price   AS inPrice,
            action_total_cost    AS inPriceTotal,
            package_price        AS outPrice,
            action_total_price   as outPriceTotal,
            created_user_id      as operatorId
        FROM
            ${db}.dwd_goods_inventory
        where
            1 = 1
            and chain_id = #{param.chainId}
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            and pharmacy_type = 0
            <if test="param.actionSql != null and param.actionSql != ''">
                ${param.actionSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id = #{param.clinicId}
            </if>
            <if test="param.fromClinicId != null and param.fromClinicId != ''">
                and from_organ_id = #{param.fromClinicId}
            </if>
            <if test="param.toClinicId != null and param.toClinicId != ''">
                and to_organ_id = #{param.toClinicId}
            </if>
            <if test="param.fromPharmacyNo != null and param.fromPharmacyNo != ''">
                and out_pharmacy_no = #{param.fromPharmacyNo}
            </if>
            <if test="param.fromPharmacyNosSql != null and param.fromPharmacyNosSql != ''">
                and ${param.fromPharmacyNosSql}
            </if>
            <if test="param.toPharmacyNo != null and param.toPharmacyNo != ''">
                and in_pharmacy_no = #{param.toPharmacyNo}
            </if>
            <if test="param.toPharmacyNosSql != null and param.toPharmacyNosSql != ''">
                and ${param.toPharmacyNosSql}
            </if>
            <if test="param.allocationType != null and  param.allocationType == 2">
                and (from_organ_id = to_organ_id)
            </if>
            <if test="param.allocationType != null and  param.allocationType == 1">
                and (from_organ_id != to_organ_id)
            </if>
            <if test="param.allocationType == null">
                and (from_organ_id != to_organ_id)
            </if>
        order by
            create_date desc
        <if test="param.limit != null and param.limit != ''">
            limit #{param.limit}
        </if>
        <if test="param.offset != null and param.offset != ''">
            offset #{param.offset}
        </if>
    </select>
    
    <select id="selectStockList" parameterType="java.lang.String" resultType="cn.abc.flink.stat.pojo.allocation.JoinHelper">
        select id as key, expiry_date as value, batch_no as value2 from ${db}.v2_goods_stock where 1=1
        <if test="stockIds != null">
            and id in
            <foreach item="item" collection="stockIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="stockIds == null">
            limit 1
        </if>
    </select>

    <select id="countAllocationInfo" resultType="cn.abc.flink.stat.pojo.allocation.InventoryAllocationSummary">
        SELECT
            <if test="param.allocationType != null">
                <if test="param.allocationType == 1">
                    from_organ_id as fromOrganId,
                    to_organ_id AS toOrganId,
                </if>
                <if test="param.allocationType == 2">
                    out_pharmacy_no AS fromPharmacyNo,
                    in_pharmacy_no AS toPharmacyNo,
                </if>
            </if>
            <if test="param.allocationType == null">
                from_organ_id as fromOrganId,
                to_organ_id AS toOrganId,
            </if>
            count(distinct goods_id) as kindCount,
            sum(action_count) as count,
            sum(action_total_cost) as amount,
            sum(action_total_cost_exclude_tax) as amountExcludingTax
        FROM
            ${db}.dwd_goods_inventory
        WHERE
            1 = 1
            and chain_id = #{param.chainId}
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            and pharmacy_type = 0
            <if test="param.actionSql != null and param.actionSql != ''">
                ${param.actionSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id = #{param.clinicId}
            </if>
            <if test="param.fromClinicId != null and param.fromClinicId != ''">
                and from_organ_id = #{param.fromClinicId}
            </if>
            <if test="param.toClinicId != null and param.toClinicId != ''">
                and to_organ_id = #{param.toClinicId}
            </if>
            <if test="param.fromPharmacyNo != null and param.fromPharmacyNo != ''">
                and out_pharmacy_no = #{param.fromPharmacyNo}
            </if>
            <if test="param.fromPharmacyNosSql != null and param.fromPharmacyNosSql != ''">
                and ${param.fromPharmacyNosSql}
            </if>
            <if test="param.toPharmacyNo != null and param.toPharmacyNo != ''">
                and in_pharmacy_no = #{param.toPharmacyNo}
            </if>
            <if test="param.toPharmacyNosSql != null and param.toPharmacyNosSql != ''">
                and ${param.toPharmacyNosSql}
            </if>
            <if test="param.allocationType != null and  param.allocationType == 2">
                and (from_organ_id = to_organ_id)
            </if>
            <if test="param.allocationType != null and  param.allocationType == 1">
                and (from_organ_id != to_organ_id)
            </if>
            <if test="param.allocationType == null">
                and (from_organ_id != to_organ_id)
            </if>
        GROUP BY
        <if test="param.allocationType != null">
            <if test="param.allocationType == 1">
                from_organ_id,to_organ_id
                order by from_organ_id
            </if>
            <if test="param.allocationType == 2">
                out_pharmacy_no,in_pharmacy_no
                order by out_pharmacy_no
            </if>
        </if>
        <if test="param.allocationType == null">
            from_organ_id,to_organ_id
            order by from_organ_id
        </if>

    </select>

    <select id="selectAllocationDetailTotal" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            ${db}.dwd_goods_inventory
        where
            1 = 1
            and chain_id = #{param.chainId}
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            and create_date between #{param.beginDate} and #{param.endDate}
            and pharmacy_type = 0
            <if test="param.actionSql != null and param.actionSql != ''">
                ${param.actionSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and organ_id = #{param.clinicId}
            </if>
            <if test="param.fromClinicId != null and param.fromClinicId != ''">
                and from_organ_id = #{param.fromClinicId}
            </if>
            <if test="param.toClinicId != null and param.toClinicId != ''">
                and to_organ_id = #{param.toClinicId}
            </if>
            <if test="param.fromPharmacyNo != null and param.fromPharmacyNo != ''">
                and out_pharmacy_no = #{param.fromPharmacyNo}
            </if>
            <if test="param.toPharmacyNo != null and param.toPharmacyNo != ''">
                and in_pharmacy_no = #{param.toPharmacyNo}
            </if>
            <if test="param.allocationType != null and  param.allocationType == 2">
                and (from_organ_id = to_organ_id)
            </if>
            <if test="param.allocationType != null and  param.allocationType == 1">
                and (from_organ_id != to_organ_id)
            </if>
            <if test="param.allocationType == null">
                and (from_organ_id != to_organ_id)
            </if>
    </select>

</mapper>