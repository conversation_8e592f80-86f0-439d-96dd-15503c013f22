<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresMemberMapper">
    <select id="selectMemberPeriodInfo" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberInfo">
        select t1.*, t2.beginPresent, t2.beginPrincipal, t3.endPrincipal, t3.endPresent
        from (
            select
            if(max(if(source_type=1,clinic_id,null)) is not null, max(if(source_type=1,clinic_id,null)), max(clinic_id)) AS clinicId,
            patient_id AS patientId,
            max(if(source_type = 1,member_type_id,'')) AS memberTypeId,
            if(max(member_created) is not null, max(member_created),min(member_bill_created)) memberRegDate,
            <if test="param.clinicId != null and param.clinicId != ''">
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.begindate} AND
                 #{param.enddate} and clinic_id=#{param.clinicId},member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePrincipal,
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.begindate} AND
                #{param.enddate} and clinic_id=#{param.clinicId},member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePresent,

                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.begindate} AND
                #{param.enddate} and clinic_id=#{param.clinicId}, member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePrincipal,
                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.begindate} AND
                #{param.enddate} and clinic_id=#{param.clinicId}, member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePresent
            </if>
            <if test="param.clinicId == null or param.clinicId == ''">
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.begindate} AND
                #{param.enddate}, member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePrincipal,
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.begindate} AND
                #{param.enddate}, member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePresent,

                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.begindate} AND
                #{param.enddate}, member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePrincipal,
                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.begindate} AND
                #{param.enddate}, member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePresent
            </if>
            from
                ${env}.dwd_member_bill_detail
            where
                is_deleted != 88
                <if test="param.chainId != null and param.chainId != ''">
                    and chain_id=#{param.chainId}
                </if>
                <if test="param.memberCreateClinicId != null and param.memberCreateClinicId != ''">
                    and member_create_clinic_id = #{param.memberCreateClinicId}
                </if>
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
            group by patient_id
        ) t1
        LEFT JOIN
        (
                -- 期初
                select
                t2.patient_id,
                t2.principal + t3.init_principal as beginPrincipal,
                t2.present + t3.init_present as beginPresent
                from
                (
                select
                patient_id,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_principal  when member_bill_action in ('充值','退费') then  member_bill_principal else 0.0 end) as principal,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_present  when member_bill_action in ('充值','退费') then  member_bill_present else 0.0 end) as present
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.begindate}
                and ds &lt;= #{param.beginDateDs}
                and source_type=2
                group by patient_id
                ) t2
                left join
                (
                select
                patient_id,
                case when action  in('消费','退储蓄金') then principal+principal_balance
                when action  in ('充值','退费') then principal_balance - principal
                end as init_principal,
                case when action  in('消费','退储蓄金') then present+present_balance
                when action  in ('充值','退费') then present_balance - present
                end as init_present
                from(
                select
                patient_id,
                member_bill_action as action,
                member_bill_principal as principal ,
                member_bill_present as present,
                member_bill_principal_balance as principal_balance,
                member_bill_present_balance as present_balance,
                row_number() over(partition by patient_id order by member_bill_created ) row_sort
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.begindate}
                and ds &lt;= #{param.beginDateDs}
                and source_type=2
                ) tmp
                where row_sort =1
                ) t3
                on t2.patient_id = t3.patient_id

        )t2
        on t1.patientId=t2.patient_id
        LEFT JOIN
        (  -- 期末
                select
                t2.patient_id,
                t2.principal + t3.init_principal as endPrincipal,
                t2.present + t3.init_present as endPresent
                from
                (
                select
                patient_id,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_principal  when member_bill_action in ('充值','退费') then  member_bill_principal else 0.0 end) as principal,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_present  when member_bill_action in ('充值','退费') then  member_bill_present else 0.0 end) as present
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.enddate}
                and ds &lt;= #{param.endDateDs}
                and source_type=2
                group by patient_id
                ) t2
                left join
                (
                select
                patient_id,
                case when action  in('消费','退储蓄金') then principal+principal_balance
                when action  in ('充值','退费') then principal_balance - principal
                end as init_principal,
                case when action  in('消费','退储蓄金') then present+present_balance
                when action  in ('充值','退费') then present_balance - present
                end as init_present
                from(
                select
                patient_id,
                member_bill_action as action,
                member_bill_principal as principal ,
                member_bill_present as present,
                member_bill_principal_balance as principal_balance,
                member_bill_present_balance as present_balance,
                row_number() over(partition by patient_id order by member_bill_created ) row_sort
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.enddate}
                  and ds &lt;= #{param.endDateDs}
                and source_type=2
                ) tmp
                where row_sort =1
                ) t3
                on t2.patient_id = t3.patient_id

        )t3
        on t1.patientId=t3.patient_id
        where
            1 = 1
            <if test="param.memberTypeId != null and param.memberTypeId != ''">
                and memberTypeId=#{param.memberTypeId}
            </if>
        order by memberRegDate desc
        <if test="param.pagesize != null and param.pagesize != ''">
            limit #{param.pagesize}
        </if>
        <if test="param.pageindex != null and param.pageindex != ''">
            offset #{param.pageindex}
        </if>


    </select>

    <select id="selectMemberPeriodInfoCount" resultType="java.lang.Long">
        select count(1) from(
        select
        if(max(if(source_type=1,clinic_id,null)) is not null, max(if(source_type=1,clinic_id,null)), max(clinic_id)) AS clinicId,
        max(member_type_id) AS memberTypeId,
        patient_id AS patientId
        from ${env}.dwd_member_bill_detail
        <where>
            is_deleted != 88
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id=#{param.patientId}
            </if>
            <if test="param.memberCreateClinicId != null and param.memberCreateClinicId != ''">
                and member_create_clinic_id = #{param.memberCreateClinicId}
            </if>
        </where>
        group by patient_id
        )t
        <where>
            <if test="param.memberTypeId != null and param.memberTypeId != ''">
                and memberTypeId=#{param.memberTypeId}
            </if>
        </where>
    </select>

    <select id="selectMemberFee" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberFee">
        select
            member_bill_pay_source as paySource,
            clinic_id as clinicId,
            member_create_clinic_id as memberCreateClinicId,
            patient_id as patientId,
            if(patient_order_customer_id is not null, patient_order_customer_id,patient_id) as customerId,
            member_bill_created as created,
            member_bill_action as tradeType,
            member_bill_type * (member_bill_principal + member_bill_present) as total,
            member_bill_type * member_bill_principal as principal,
            member_bill_type * member_bill_present as present,
            member_bill_pay_mode as payModeId,
            member_bill_charge_transaction_id as chargeTransactionId,
            member_bill_principal_balance as principalBalance,
            member_bill_present_balance as presentBalance,
            if(member_bill_pay_mode = 1,member_bill_principal,0.0)  as payCash,
            if(member_bill_pay_mode = 2,member_bill_principal,0.0)  as payWechat,
            if(member_bill_pay_mode = 3,member_bill_principal,0.0)  as payAlipay,
            if(member_bill_pay_mode = 4,member_bill_principal,0.0)  as payBankCard,
            if(member_bill_pay_mode = 5,member_bill_principal,0.0)  as paySocialSecurity,
            seller_index as sellUserId,
            member_bill_operator_id as operatorId,
            member_bill_remark AS chargeComment,
            member_type_id as memberTypeId
        from
            ${env}.dwd_member_bill_detail
        where
            source_type = 2
            and is_deleted != 88
            and date_index between to_timestamp(#{param.begindate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.enddate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id=#{param.patientId}
            </if>
            <if test="param.actionSql != null and param.actionSql != ''">
                and ${param.actionSql}
            </if>
            <if test="param.seller != null and param.seller != ''">
                and seller_index = #{param.seller}
            </if>
            <if test="param.operatorId != null and param.operatorId != ''">
                and member_bill_operator_id = #{param.operatorId}
            </if>
            <if test="param.memberTypeId != null and param.memberTypeId != ''">
                and member_type_id=#{param.memberTypeId}
            </if>
            <if test="param.memberCreateClinicId != null and param.memberCreateClinicId != ''">
                and member_create_clinic_id = #{param.memberCreateClinicId}
            </if>
        order by created desc
        <if test="param.pagesize != null and param.pagesize != ''">
            limit #{param.pagesize}
        </if>
        <if test="param.pageindex != null and param.pageindex != ''">
            offset #{param.pageindex}
        </if>
    </select>


    <select id="selectMemberFeeCount" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberFeeTotal">
        select
            count(1) as count,
            sum(member_bill_type * (member_bill_principal + member_bill_present)) as totalIncomeAmount,
            sum(if(member_bill_action = '充值', member_bill_principal + member_bill_present, 0.0)) as rechargeAmount,
            sum(if(member_bill_action = '退储蓄金', member_bill_principal + member_bill_present, 0.0)) as RefundSavingsFundsAmount,
            sum(if(member_bill_action = '消费', member_bill_principal + member_bill_present, 0.0)) as consumeAmount,
            sum(if(member_bill_action = '退费', member_bill_principal + member_bill_present, 0.0)) as refundAmount
        from
            ${env}.dwd_member_bill_detail
        where
            source_type = 2
            and is_deleted != 88
            and date_index between to_timestamp(#{param.begindate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.enddate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
            <if test="param.patientId != null and param.patientId != ''">
                and patient_id=#{param.patientId}
            </if>
            <if test="param.actionSql != null and param.actionSql != ''">
                and ${param.actionSql}
            </if>
            <if test="param.seller != null and param.seller != ''">
                and seller_index = #{param.seller}
            </if>
            <if test="param.operatorId != null and param.operatorId != ''">
                and member_bill_operator_id = #{param.operatorId}
            </if>
            <if test="param.memberTypeId != null and param.memberTypeId != ''">
                and member_type_id=#{param.memberTypeId}
            </if>
            <if test="param.memberCreateClinicId != null and param.memberCreateClinicId != ''">
                and member_create_clinic_id = #{param.memberCreateClinicId}
            </if>
    </select>

    <select id="selectMemberFeeEmployees" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberFlowSelectionEmployee">
        select
        concat(clinic_id, '-', seller_index) AS clinicSeller,
        clinic_id AS clinicId,
        seller_index AS sellerUserId,
        member_bill_operator_id as operatorId,
        min(member_bill_action)as action
        from ${env}.dwd_member_bill_detail
        <where>
            source_type = 2
            and is_deleted != 88
            and chain_id=#{param.chainId}
            and date_index between to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
        </where>
        group by clinic_id,seller_index,member_bill_operator_id
    </select>

    <select id="selectMemberTypeIdByMemberFee" resultType="java.lang.String">
        select
            distinct member_type_id as memberTypeId
        from ${env}.dwd_member_bill_detail
        where
            source_type = 2
            and is_deleted != 88
            and date_index between to_timestamp(#{param.begindate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.enddate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.chainId != null and param.chainId != ''">
                and chain_id=#{param.chainId}
            </if>
    </select>

    <select id="selectSellerActions" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberAllSelectionSeller">
        select
        seller_index AS sellerUserId,
        min(member_bill_action) as action
        from ${env}.dwd_member_bill_detail
        <where>
            source_type = 2
            and is_deleted != 88
            and chain_id=#{param.chainId}
            and date_index between to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
        </where>
        group by seller_index
    </select>

    <select id="selectMemberComsumePrice" resultType="BigDecimal">
        select
            if(sum(if(type=-1, -1, 1) * received_price) is not null, sum(if(type=-1, -1, 1) * received_price), 0.0)
        from
            ${env}.dwd_charge_transaction_record_v_partition
        where
            chain_id = #{chainId}
            and create_time between to_timestamp(#{beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and import_flag = 0
            and is_deleted = 0
            AND goods_fee_type in (0, 1)
            and product_compose_type in (0,1)
            and member_id is not null
            and member_id != ''
            and product_type not in (17, 18)
            and record_type != 2
            <if test="clinicId != null and clinicId != ''">
                and clinic_id = #{clinicId}
            </if>
            <if test="whereSqlMemberIds != null and whereSqlMemberIds != ''">
                and ${whereSqlMemberIds}
            </if>
    </select>

    <select id="selectMemberRechargeAndComsumePrice" resultType="cn.abc.flink.stat.service.cis.member.domain.RechargeAndComsumePriceDao">
        select
            sum(if(member_bill_action = '充值', 1, if(member_bill_action = '退储蓄金', -1, 0)) * (member_bill_principal + member_bill_present)) as rechargePrice,
            sum(if(member_bill_action = '充值', 1, if(member_bill_action = '退储蓄金', -1, 0)) * member_bill_principal) as rechargePrincipalPrice,
            sum(if(member_bill_action = '充值', 1, if(member_bill_action = '退储蓄金', -1, 0)) * member_bill_present) as rechargePresentPrice,
            sum(if(member_bill_action = '消费', 1, if(member_bill_action = '退费', -1, 0)) * (member_bill_principal + member_bill_present)) as consumePrice
        from
            ${env}.dwd_member_bill_detail
        where
            chain_id = #{chainId}
            and is_deleted != 88
            and date_index between to_timestamp(#{beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyy')::INT
            <if test="clinicId != null and clinicId != ''">
                and clinic_id = #{clinicId}
            </if>
            <if test="memberCreateClinicId != null and memberCreateClinicId != ''">
                and member_create_clinic_id = #{memberCreateClinicId}
            </if>
    </select>

    <select id="selectMemberTransactionRecord" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberTransactionRecord">
        select
            memberTypeId,
            sum(receivedPrice) as amount,
            count(1) as count
        from
        (
            select
                replace((replace(member_info,'\','')::json->>'memberType')::json->>'id','"','') as memberTypeId,
                v2_patient_order_id as patientOrderId,
                sum(if(type=-1, -1, 1) * received_price) as receivedPrice
            from
                ${env}.dwd_charge_transaction_record_v_partition
            where chain_id = #{chainId}
            and create_time between to_timestamp(#{beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and record_type != 2
            and import_flag = 0
            and is_deleted = 0
            and goods_fee_type in (0, 1)
            and product_compose_type in (0,1)
            and product_type not in (17, 18)
            and member_id is not null
            and member_id != ''
            <if test="clinicId != null and clinicId != ''">
                and clinic_id = #{clinicId}
            </if>
            <if test="whereSqlMemberIds != null and whereSqlMemberIds != ''">
                and ${whereSqlMemberIds}
            </if>
            group by memberTypeId, patientOrderId
        ) a
        group by memberTypeId
    </select>

    <select id="selectMemberSummary" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberSummary">
        --在memberPeriodInfo方法的结果上进行汇总(因此临时表存在多余的字段)
        with memberPeriodInfo as(
        select t1.*, t2.beginPresent, t2.beginPrincipal, t3.endPrincipal, t3.endPresent
        from (
            select
            if(max(if(source_type=1,clinic_id,null)) is not null, max(if(source_type=1,clinic_id,null)),max(clinic_id)) AS clinicId,
            max(if(source_type=1,clinic_id,null)) AS memberCreatedClinic,
            patient_id AS patientId,
            max(if(source_type = 1,member_type_id,'')) AS memberTypeId,
            if(max(member_created) is not null, max(member_created),min(member_bill_created)) memberRegDate,

            <if test="param.clinicId != null and param.clinicId != ''">
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate} and clinic_id=#{param.clinicId},member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePrincipal,
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate} and clinic_id=#{param.clinicId},member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePresent,

                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate} and clinic_id=#{param.clinicId}, member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePrincipal,
                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate} and clinic_id=#{param.clinicId}, member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePresent
            </if>
            <if test="param.clinicId == null or param.clinicId == ''">
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate}, member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePrincipal,
                sum(IF(member_bill_action in ('充值','退储蓄金') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate}, member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS memberChargePresent,

                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate}, member_bill_principal*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePrincipal,
                sum(IF(member_bill_action IN ('挂号费','消费','挂号退费','退费') AND member_bill_created BETWEEN #{param.beginDate} AND
                #{param.endDate}, member_bill_present*IF(member_bill_type&lt;0,-1,1),0.0)) AS chargePresent
            </if>

            from ${env}.dwd_member_bill_detail
            <where>
                is_deleted != 88
                <if test="param.chainId != null and param.chainId != ''">
                    and chain_id=#{param.chainId}
                </if>
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                <if test="param.memberCreateClinicId != null and param.memberCreateClinicId != ''">
                    and member_create_clinic_id = #{param.memberCreateClinicId}
                </if>
            </where>
            group by patient_id
        ) t1
        LEFT JOIN
        (

                -- 期初
                select
                t2.patient_id,
                t2.principal + t3.init_principal as beginPrincipal,
                t2.present + t3.init_present as beginPresent
                from
                (
                select
                patient_id,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_principal  when member_bill_action in ('充值','退费') then  member_bill_principal else 0.0 end) as principal,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_present  when member_bill_action in ('充值','退费') then  member_bill_present else 0.0 end) as present
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.beginDate}
                and ds &lt;= #{param.beginDateDs}
                and source_type=2
                group by patient_id
                ) t2
                left join
                (
                select
                patient_id,
                case when action  in('消费','退储蓄金') then principal+principal_balance
                when action  in ('充值','退费') then principal_balance - principal
                end as init_principal,
                case when action  in('消费','退储蓄金') then present+present_balance
                when action  in ('充值','退费') then present_balance - present
                end as init_present
                from(
                select
                patient_id,
                member_bill_action as action,
                member_bill_principal as principal ,
                member_bill_present as present,
                member_bill_principal_balance as principal_balance,
                member_bill_present_balance as present_balance,
                row_number() over(partition by patient_id order by member_bill_created ) row_sort
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.beginDate}
                and ds &lt;= #{param.beginDateDs}
                and source_type=2
                ) tmp
                where row_sort =1
                ) t3
                on t2.patient_id = t3.patient_id

        )t2
        on t1.patientId=t2.patient_id
        LEFT JOIN
        (
        -- 期末
                select
                t2.patient_id,
                t2.principal + t3.init_principal as endPrincipal,
                t2.present + t3.init_present as endPresent
                from
                (
                select
                patient_id,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_principal  when member_bill_action in ('充值','退费') then  member_bill_principal else 0.0 end) as principal,
                sum(case when member_bill_action  in('消费','退储蓄金') then -1* member_bill_present  when member_bill_action in ('充值','退费') then  member_bill_present else 0.0 end) as present
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.endDate}
                and ds &lt;= #{param.endDateDs}
                and source_type=2
                group by patient_id
                ) t2
                left join
                (
                select
                patient_id,
                case when action  in('消费','退储蓄金') then principal+principal_balance
                when action  in ('充值','退费') then principal_balance - principal
                end as init_principal,
                case when action  in('消费','退储蓄金') then present+present_balance
                when action  in ('充值','退费') then present_balance - present
                end as init_present
                from(
                select
                patient_id,
                member_bill_action as action,
                member_bill_principal as principal ,
                member_bill_present as present,
                member_bill_principal_balance as principal_balance,
                member_bill_present_balance as present_balance,
                row_number() over(partition by patient_id order by member_bill_created ) row_sort
                from ${env}.dwd_member_bill_detail
                where
                 chain_id=#{param.chainId}
                <if test="param.patientId != null and param.patientId != ''">
                    and patient_id=#{param.patientId}
                </if>
                and member_bill_created &lt;= #{param.endDate}
                and ds &lt;= #{param.endDateDs}
                and source_type=2
                ) tmp
                where row_sort =1
                ) t3
                on t2.patient_id = t3.patient_id

        )t3
        on t1.patientId=t3.patient_id
        where
            1 = 1
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinicId = #{param.clinicId}
            </if>
            <if test="param.memberTypeId != null and param.memberTypeId != ''">
                and memberTypeId = #{param.memberTypeId}
            </if>
        )

        select
        sum(if(memberRegDate BETWEEN #{param.beginDate} and #{param.endDate},1,0)) as newMemberCount,
        sum(memberChargePrincipal)+sum(memberChargePresent) as totalMemberChargePrincipalAndPresent,
        sum(endPrincipal)+sum(endPresent) as totalEndPrincipalAndPresent
        from memberPeriodInfo;
    </select>

    <select id="selectMemberChargeSummary" resultType="cn.abc.flink.stat.service.cis.member.domain.MemberSummary">
        select
            sum(if(member_bill_action = '消费', 1 , -1) * (member_bill_principal + member_bill_present)) as totalReceivedFee,
            count(1) as consumptionPersonTimeCount,
            sum(if(member_bill_action = '消费', 1 , -1) * (member_bill_principal + member_bill_present))/ count(1) as consumptionPerPersonTimeCount
        from
            ${env}.dwd_member_bill_detail
        where
            chain_id = #{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.memberCreateClinicId != null and param.memberCreateClinicId != ''">
                and member_create_clinic_id = #{param.memberCreateClinicId}
            </if>
            and member_bill_action in ('消费','退费')
            and member_bill_created between #{param.beginDate} and #{param.endDate}
            and ds between #{param.beginDateDs} and #{param.endDateDs}
    </select>


    <select id="selectNewMemberCount" resultType="java.lang.Integer">
        select
            count(1)
        from
            ${env}.dwd_member_bill_detail
        where
            chain_id=#{param.chainId}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.memberCreateClinicId != null and param.memberCreateClinicId != ''">
                and member_create_clinic_id=#{param.memberCreateClinicId}
            </if>
            and is_deleted != 88
            and source_type = 1
            and member_created between #{param.beginDate} and #{param.endDate}
            and ds between #{param.beginDateDs} and #{param.endDateDs}
    </select>

</mapper>