<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abc.flink.stat.db.cis.hologres.dao.HologresAchievementChargeMapper">
    <sql id="conditionForDepId">
        <if test="param.hisType != null and param.hisType != ''">
            <if test="param.hisType == 1">
                <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 1'>
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = '')
                    </if>
                </if>
                <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 2'>
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and ((item_doctor_department_id = #{param.departmentId} and (retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19)))
                        or (department_id=#{param.departmentId} and retail_type != 2 and charge_sheet_type not in (3,6,8,12,16,18) and product_type not in (2,3,4,11,19)))
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and (((item_doctor_department_id is null or item_doctor_department_id = '') and (retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19)))
                        or ((department_id is null or department_id = '') and retail_type != 2 and charge_sheet_type not in (3,6,8,12,16,18) and product_type not in (2,3,4,11,19)))
                    </if>
                </if>
            </if>
            <if test="param.hisType != 1">
                <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 1">
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and (department_id is null or department_id = '')
                        and (seller_department_id is null or seller_department_id = '')
                    </if>
                </if>
                <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 4">
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                        and copywriter_id is not null
                        and copywriter_id != ''
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and (department_id is null or department_id = '')
                        and (seller_department_id is null or seller_department_id = '')
                        and copywriter_id is not null
                        and copywriter_id != ''
                    </if>
                </if>
            </if>
        </if>
        <if test="param.hisType == null or param.hisType == ''">
            <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 1">
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and (department_id is null or department_id = '')
                    and (seller_department_id is null or seller_department_id = '')
                </if>
            </if>
            <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 4">
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                    and copywriter_id is not null
                    and copywriter_id != ''
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and (department_id is null or department_id = '')
                    and (seller_department_id is null or seller_department_id = '')
                    and copywriter_id is not null
                    and copywriter_id != ''
                </if>
            </if>
        </if>
    </sql>

    <sql id="employeeWhereSql">
        <if test="param.hisType != null and param.hisType != ''">
            <if test="param.hisType == 1">
                <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 1'>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                    </if>
                </if>
                <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 2'>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and ((item_doctor_id = #{param.employeeId} and (retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19)))
                        or (doctor_id=#{param.employeeId} and retail_type != 2 and charge_sheet_type not in (3,6,8,12,16,18) and product_type not in (2,3,4,11,19)))
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and (((item_doctor_id is null or item_doctor_id = '') and (retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19)))
                        or (doctor_id is null and doctor_id = '' and retail_type != 2 and charge_sheet_type not in (3,6,8,12,16,18) and product_type not in (2,3,4,11,19)))
                    </if>
                </if>
                <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 3'>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and item_nurse_id = #{param.employeeId}
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and (item_nurse_id is null or item_nurse_id = '')
                    </if>
                </if>
                <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 5'>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and consultant_id = #{param.employeeId}
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and (consultant_id is null or consultant_id = '')
                    </if>
                </if>
                <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 4'>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and copywriter_id = #{param.employeeId}
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and (copywriter_id is null or copywriter_id = '')
                    </if>
                </if>
            </if>
            <if test="param.hisType != 1">
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                </if>
                <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                    and copywriter_id = #{param.copyWriterId}
                </if>
                <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                    and (copywriter_id is null or copywriter_id = '')
                </if>
            </if>
        </if>
        <if test="param.hisType == null or param.hisType == ''">
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
            </if>
            <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                and copywriter_id = #{param.copyWriterId}
            </if>
            <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                and (copywriter_id is null or copywriter_id = '')
            </if>
        </if>
    </sql>

    <sql id="chargeEmployeeSelectSql">
        <choose>
            <when test="param.hisType != null and param.hisType == 1 and param.employeeTypeNumber != null and param.employeeTypeNumber != 1">
                <if test="param.employeeTypeNumber == 2">
                    if(case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_id else doctor_id end is not null
                    and case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_id else doctor_id end != '',
                    case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_id else doctor_id end,'00000000000000000000000000000000') as personnelId,
                    if(case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then '' else COALESCE(employee_snaps ->> 'doctorName', '') end is not null,
                    case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then '' else COALESCE(employee_snaps ->> 'doctorName', '') end, '') as employeeName,
                </if>
                <if test="param.employeeTypeNumber == 3">
                    if(item_nurse_id is not null and item_nurse_id != '', item_nurse_id, '00000000000000000000000000000000') as personnelId,
                    '' as employeeName,
                </if>
                <if test="param.employeeTypeNumber == 4">
                    if(copywriter_id is not null and copywriter_id != '', copywriter_id, '00000000000000000000000000000000') as personnelId,
                    '' as employeeName,
                </if>
                <if test="param.employeeTypeNumber == 5">
                    if(consultant_id is not null and consultant_id != '', consultant_id, '00000000000000000000000000000000') as personnelId,
                    '' as employeeName,
                </if>
            </when>
            <otherwise>
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as personnelId,
                if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            </otherwise>
        </choose>
    </sql>

    <sql id="dispensingEmployeeSelectSql">
        <choose>
            <when test="param.hisType != null and param.hisType == 1 and param.employeeTypeNumber != null and param.employeeTypeNumber != 1">
                <if test="param.employeeTypeNumber == 2">
                    if(case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_id else doctor_id end is not null
                    and case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_id else doctor_id end != '',
                    case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_id else doctor_id end,'00000000000000000000000000000000') as employeeId,
                    if(case when charge_sheet_type in (3,6,8,12,16,18) then '' else COALESCE(employee_snaps ->> 'doctorName', '') end is not null,
                    case when charge_sheet_type in (3,6,8,12,16,18) then '' else COALESCE(employee_snaps ->> 'doctorName', '') end, '') as employeeName,
                </if>
                <if test="param.employeeTypeNumber == 3">
                    if(item_nurse_id is not null and item_nurse_id != '', item_nurse_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                </if>
                <if test="param.employeeTypeNumber == 4">
                    if(copywriter_id is not null and copywriter_id != '', copywriter_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                </if>
                <if test="param.employeeTypeNumber == 5">
                    if(consultant_id is not null and consultant_id != '', consultant_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                </if>
            </when>
            <otherwise>
                if(transcribe_doctor_id=doctor_id, if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, if(seller_id is not null and seller_id != '',COALESCE(employee_snaps ->> 'sellerName', ''), ''), if(seller_id is not null and seller_id != '', COALESCE(employee_snaps ->> 'sellerName', ''), COALESCE(employee_snaps ->> 'doctorName', ''))) as employeeName,
            </otherwise>
        </choose>
    </sql>

    <sql id="copywriterWhereSql">
        <if test="param.hisType != null and param.hisType != ''">
            <if test="param.hisType == 1">
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (copywriter_id=#{param.employeeId})
                </if>
            </if>
            <if test="param.hisType != 1">
                <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                    and copywriter_id = #{param.copyWriterId}
                </if>
                <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                    and (copywriter_id is null or copywriter_id = '')
                </if>
            </if>
        </if>
        <if test="param.hisType == null or param.hisType == ''">
            <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                and copywriter_id = #{param.copyWriterId}
            </if>
            <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                and (copywriter_id is null or copywriter_id = '')
            </if>
        </if>
    </sql>

    <select id="selectPersonnelBase"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonnelBase">
        select
            chainId,
            clinicId,
            personnelId,
            employeeName,
            isCopywriter,
            patientCount,
            transFormPatientCount
        from
        (
            select
                chainId,
                clinicId,
                personnelId,
                employeeName,
                isCopywriter,
                count(distinct v2_patient_order_id) as patientCount,
                count(distinct if(recordSourceType != 2 and classify_level_1_id != '5-0', v2_patient_order_id, null)) as transFormPatientCount
            from
                (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="chargeEmployeeSelectSql"/>
                    0 as isCopywriter,
                    date(create_time) as created,
                    if(record_source_type = 1, 0,record_source_type) as recordSourceType,
                    classify_level_1_id,
                    v2_patient_order_id
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and import_flag = 0
                and record_type not in (2, 3)
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and is_deleted = 0
                and product_type != 18
                and is_deleted = 0
                <if test="param.hisType == 100">
                    and product_compose_type in (0,2,3)
                    and goods_fee_type in (0,2)
                </if>
                <if test="param.hisType != 100">
                     <if test="param.composeSql != null and param.composeSql != ''">
                        and ${param.composeSql}
                    </if>
                    <if test="param.isCardOpeningFee == 0">
                        and product_type != 17
                    </if>
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <include refid="employeeWhereSql"/>
                <if test="param.includeReg != null and param.includeReg != 1">
                    <choose>
                        <when test="param.hisType != 100">
                            and product_type!=5
                        </when>
                        <otherwise>
                            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                        </otherwise>
                    </choose>
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                group by chainId, clinicId, personnelId,employeeName, isCopywriter,v2_patient_order_id, created, recordSourceType, classify_level_1_id
            ) a
            group by chainId, clinicId, personnelId, employeeName, isCopywriter
            <if test="param.includeWriter == 1">
                union all
                select
                    chainId,
                    clinicId,
                    personnelId,
                    employeeName,
                    isCopywriter,
                    count(distinct v2_patient_order_id) as patientCount,
                    count(distinct if(recordSourceType != 2 and classify_level_1_id != '5-0', v2_patient_order_id, null)) as transFormPatientCount
                from
                (
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        copywriter_id as personnelId,
                        '' as employeeName,
                        1 as isCopywriter,
                        date(create_time) as created,
                        if(record_source_type = 1, 0,record_source_type) as recordSourceType,
                        classify_level_1_id,
                        v2_patient_order_id
                    from
                    ${env}.dwd_charge_transaction_record_v_partition
                    where chain_id=#{param.chainId}
                    and import_flag = 0
                    and record_type not in (2, 3)
                    and is_deleted = 0
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and copywriter_id is not null and copywriter_id != ''
                    and product_type != 18
                    and is_deleted = 0
                    <if test="param.hisType == 100">
                        and product_compose_type in (0,2,3)
                        and goods_fee_type in (0,2)
                    </if>
                    <if test="param.hisType != 100">
                        <if test="param.composeSql != null and param.composeSql != ''">
                            and ${param.composeSql}
                        </if>
                        <if test="param.isCardOpeningFee == 0">
                            and product_type != 17
                        </if>
                    </if>
                    <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                        and ${param.arrearsCommissionTimingSql}
                    </if>
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                    </if>
                    <if test="param.payModeSql != null and param.payModeSql != ''">
                        and ${param.payModeSql}
                    </if>
                    <if test="param.fee1 != null and param.fee2 != null">
                        and (${param.fee1} or ${param.fee2})
                    </if>
                    <if test="param.fee1 != null and param.fee2 == null">
                        and ${param.fee1}
                    </if>
                    <if test="param.fee2 != null and param.fee1 == null">
                        and ${param.fee2}
                    </if>
                    <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                        and ${param.feeTypeIdSql}
                    </if>
                    <if test='param.employeeSql != null and param.employeeSql != ""'>
                        ${param.employeeSql}
                    </if>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                    </if>
                    <include refid="copywriterWhereSql"/>
                    <if test="param.includeReg != null and param.includeReg != 1">
                        <choose>
                            <when test="param.hisType != 100">
                                and product_type!=5
                            </when>
                            <otherwise>
                                and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                            </otherwise>
                        </choose>
                    </if>
                    group by chainId, clinicId, personnelId, employeeName,isCopywriter, v2_patient_order_id, created, recordSourceType, classify_level_1_id
                ) a
                group by chainId, clinicId, personnelId, employeeName, isCopywriter
            </if>
        ) b
    </select>

    <select id="selectPersonnelTransformPatientCount"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonnelBase">
        WITH base_data AS (
            SELECT
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="chargeEmployeeSelectSql"/>
                0 as isCopywriter,
                patient_id,
                v2_patient_order_id,
                date(create_time) AS visit_date
            FROM ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and import_flag = 0
            and type != -1
            AND record_source_type != 2
            AND product_type!=5
            and record_type not in (2, 3)
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and is_deleted = 0
            and product_type != 18
            <if test="param.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="param.hisType != 100">
                <if test="param.composeSql != null and param.composeSql != ''">
                    and ${param.composeSql}
                </if>
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <include refid="employeeWhereSql"/>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            GROUP BY chainId, clinicId, personnelId,employeeName, isCopywriter, patient_id, v2_patient_order_id, date(create_time)
            <if test="param.includeWriter == 1">
                union all
                SELECT
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as personnelId,
                    '' as employeeName,
                    1 as isCopywriter,
                    patient_id,
                    v2_patient_order_id,
                    date(create_time) AS visit_date
                FROM ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and import_flag = 0
                and type != -1
                AND record_source_type != 2
                AND product_type!=5
                and record_type not in (2, 3)
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                and product_type != 18
                <if test="param.hisType == 100">
                    and product_compose_type in (0,2,3)
                    and goods_fee_type in (0,2)
                </if>
                <if test="param.hisType != 100">
                    <if test="param.composeSql != null and param.composeSql != ''">
                        and ${param.composeSql}
                    </if>
                    <if test="param.isCardOpeningFee == 0">
                        and product_type != 17
                    </if>
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                </if>
                <include refid="copywriterWhereSql"/>
                GROUP BY chainId, clinicId, personnelId,employeeName, isCopywriter, patient_id, v2_patient_order_id, date(create_time)
            </if>
        ),
        -- 识别跨天的订单
        multi_day_orders AS (
            SELECT
                chainId,
                clinicId,
                personnelId,
                employeeName,
                isCopywriter,
                patient_id,
                v2_patient_order_id,
                COUNT(DISTINCT visit_date) AS day_count
            FROM base_data
            GROUP BY chainId, clinicId, personnelId,employeeName, isCopywriter, patient_id, v2_patient_order_id
            HAVING COUNT(DISTINCT visit_date) > 1
        ),
        -- 计算跨天患者的就诊次数（每个患者只算1次）
        multi_day_patient_visits AS (
            SELECT
                chainId,
                clinicId,
                personnelId,
                employeeName,
                isCopywriter,
                COUNT(DISTINCT patient_id) AS multi_day_visit_count
            FROM multi_day_orders
            GROUP BY chainId, clinicId, personnelId,employeeName, isCopywriter
        ),
        -- 计算非跨天患者的就诊次数（按天去重）
        single_day_patient_visits AS (
            SELECT
                chainId,
                clinicId,
                personnelId,
                employeeName,
                isCopywriter,
                COUNT(1) AS single_day_visit_count
            FROM
            (
            SELECT
                base.chainId,
                base.clinicId,
                base.personnelId,
                base.employeeName,
                base.isCopywriter,
                base.patient_id,
                base.visit_date
            FROM base_data base
            LEFT JOIN multi_day_orders m
            ON base.chainId = m.chainId
            AND base.clinicId = m.clinicId
            AND base.personnelId = m.personnelId
            AND base.employeeName = m.employeeName
            AND base.isCopywriter = m.isCopywriter
            AND base.patient_id = m.patient_id
            AND base.v2_patient_order_id = m.v2_patient_order_id
            WHERE m.patient_id IS NULL -- 排除跨天患者的记录
            GROUP BY base.chainId, base.clinicId, base.personnelId, base.employeeName, base.isCopywriter, base.patient_id, base.visit_date
            ) t
            GROUP BY chainId, clinicId, personnelId, employeeName, isCopywriter
        )
        -- 合并两种结果，按开单人和门店分组
        SELECT
            COALESCE(m.chainId, s.chainId) AS chainId,
            COALESCE(m.clinicId, s.clinicId) AS clinicId,
            COALESCE(m.personnelId, s.personnelId) AS personnelId,
            COALESCE(m.employeeName, s.employeeName) AS employeeName,
            COALESCE(m.isCopywriter, s.isCopywriter) AS isCopywriter,
            COALESCE(m.multi_day_visit_count, 0) + COALESCE(s.single_day_visit_count, 0) AS transFormPatientCount
        FROM multi_day_patient_visits m
        FULL OUTER JOIN single_day_patient_visits s
        ON m.chainId = s.chainId
        AND m.clinicId = s.clinicId
        AND m.personnelId = s.personnelId
        AND m.employeeName = s.employeeName
        AND m.isCopywriter = s.isCopywriter
    </select>

    <select id="selectPersonnelDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonnelDispensingCost">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="dispensingEmployeeSelectSql"/>
            0 as isCopywriter,
            classify_level_1_id as classifyLevel1,
            if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
            if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{param.chainId}
        and form_type = 0
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and compose_parent_product_id is null
        </if>
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, ((department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.fee1 != null and param.fee2 != null">
            and (${param.fee1} or ${param.fee2})
        </if>
        <if test="param.fee1 != null and param.fee2 == null">
            and ${param.fee1}
        </if>
        <if test="param.fee2 != null and param.fee1 == null">
            and ${param.fee2}
        </if>
        <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
            ${param.dispensingEmployeeSql}
        </if>
        <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
        </if>
        <if test='param.employeeId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
        </if>
        <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
            and copywriter_id = #{param.copyWriterId}
        </if>
        <if test='param.copyWriterId == "00000000000000000000000000000000"'>
            and (copywriter_id is null or copywriter_id = '')
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, employeeId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
        <!-- 套餐不拆分时通过子项查询套餐的成本 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="dispensingEmployeeSelectSql"/>
                0 as isCopywriter,
                '11-1' as classifyLevel1,
                0 as classifyLevel2,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and compose_parent_product_id is not null
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, ((department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})))
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                and copywriter_id = #{param.copyWriterId}
            </if>
            <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                and (copywriter_id is null or copywriter_id = '')
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, employeeId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                copywriter_id as employeeId,
                '' as employeeName,
                1 as isCopywriter,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and compose_parent_product_id is null
            </if>
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and copywriter_id is not null and copywriter_id != ''
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <include refid="copywriterWhereSql"/>
            group by chainId, clinicId, employeeId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
            <!-- 套餐不拆分时通过子项查询套餐的成本 -->
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as employeeId,
                    '' as employeeName,
                    1 as isCopywriter,
                    '11-1' as classifyLevel1,
                    0 as classifyLevel2,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                    if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                from
                ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and form_type = 0
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and compose_parent_product_id is not null
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
                </if>
                <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                    ${param.dispensingEmployeeSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <include refid="copywriterWhereSql"/>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                group by chainId, clinicId, employeeId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
            </if>
        </if>
    </select>

    <select id="selectAdvicePersonnelDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonnelDispensingCost">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="dispensingEmployeeSelectSql"/>
            0 as isCopywriter,
            fee_type_id as feeTypeId,
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
            if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{param.chainId}
        and form_type = 0
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and compose_parent_product_id is null
        </if>
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
            and ${param.feeTypeIdSql}
        </if>
        <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
        </if>
        <if test='param.employeeId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
        </if>
        <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
            ${param.dispensingEmployeeSql}
        </if>
        <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
            and copywriter_id = #{param.copyWriterId}
        </if>
        <if test='param.copyWriterId == "00000000000000000000000000000000"'>
            and (copywriter_id is null or copywriter_id = '')
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, employeeId, employeeName, isCopywriter, feeTypeId

        <!-- 套餐不拆分时通过子项查询套餐的成本 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="dispensingEmployeeSelectSql"/>
                0 as isCopywriter,
                fee_type_id as feeTypeId,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and compose_parent_product_id is not null
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                and copywriter_id = #{param.copyWriterId}
            </if>
            <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                and (copywriter_id is null or copywriter_id = '')
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, employeeId, employeeName, isCopywriter, feeTypeId
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                copywriter_id as employeeId,
                '' as employeeName,
                1 as isCopywriter,
                fee_type_id as feeTypeId,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and compose_parent_product_id is null
            </if>
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and copywriter_id is not null and copywriter_id != ''
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, doctor_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), (doctor_id is null or doctor_id = ''))
            </if>
            <include refid="copywriterWhereSql"/>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            group by chainId, clinicId, employeeId, employeeName, isCopywriter, feeTypeId
            <!-- 套餐不拆分时通过子项查询套餐的成本 -->
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as employeeId,
                    '' as employeeName,
                    1 as isCopywriter,
                    fee_type_id as feeTypeId,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                    if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                from
                ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and form_type = 0
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and compose_parent_product_id is not null
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, doctor_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), (doctor_id is null or doctor_id = ''))
                </if>
                <include refid="copywriterWhereSql"/>
                <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                    ${param.dispensingEmployeeSql}
                </if>
                group by chainId, clinicId, employeeId, employeeName, isCopywriter, feeTypeId
            </if>
        </if>
    </select>

    <select id="selectPersonalAmount"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalFeeAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="chargeEmployeeSelectSql"/>
            0 as isCopywriter,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and import_flag = 0
        and record_type not in (2, 3)
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        and product_type != 18
        <if test="param.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="param.hisType != 100">
            and product_compose_type in (0,2)
            and goods_fee_type in (0,1)
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.payModeSql != null and param.payModeSql != ''">
            and ${param.payModeSql}
        </if>
        <if test="param.fee1 != null and param.fee2 != null">
            and (${param.fee1} or ${param.fee2})
        </if>
        <if test="param.fee1 != null and param.fee2 == null">
            and ${param.fee1}
        </if>
        <if test="param.fee2 != null and param.fee1 == null">
            and ${param.fee2}
        </if>
        <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
            and ${param.feeTypeIdSql}
        </if>
        <if test='param.employeeSql != null and param.employeeSql != ""'>
            ${param.employeeSql}
        </if>
        <include refid="employeeWhereSql"/>
        <if test="param.includeReg != null and param.includeReg != 1">
            <choose>
                <when test="param.hisType != 100">
                    and product_type!=5
                </when>
                <otherwise>
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </otherwise>
            </choose>
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, personnelId, employeeName, isCopywriter
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                copywriter_id as personnelId,
                '' as employeeName,
                1 as isCopywriter,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and record_type not in (2, 3)
            and import_flag = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and copywriter_id is not null and copywriter_id != ''
            and product_type != 18
            <if test="param.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="param.hisType != 100">
                and product_compose_type in (0,2)
                and goods_fee_type in (0,1)
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
            </if>
            <include refid="copywriterWhereSql"/>
            <if test="param.includeReg != null and param.includeReg != 1">
                <choose>
                    <when test="param.hisType != 100">
                        and product_type!=5
                    </when>
                    <otherwise>
                        and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                    </otherwise>
                </choose>
            </if>
            group by chainId, clinicId, personnelId, employeeName, isCopywriter
        </if>
    </select>

    <select id="selectPersonalFeeClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalFeeAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="chargeEmployeeSelectSql"/>
            0 as isCopywriter,
            classify_level_1_id as classifyLevel1,
            <if test="level == 2">
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
            </if>
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and product_type not in (11,18)
        and record_type not in (2, 3)
        and import_flag = 0
        and is_deleted = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="param.arrearsCommissionTiming != 1">
            and record_source_type != 1
        </if>
        <if test="param.arrearsCommissionTiming != 2">
            and record_source_type != 2
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.payModeSql != null and param.payModeSql != ''">
            and ${param.payModeSql}
        </if>
        <if test="feeType1 != null and feeType2 != null">
            and (${feeType1} or ${feeType2})
        </if>
        <if test="feeType1 != null and feeType2 == null">
            and ${feeType1}
        </if>
        <if test="feeType2 != null and feeType1 == null">
            and ${feeType2}
        </if>
        <if test='param.employeeSql != null and param.employeeSql != ""'>
            ${param.employeeSql}
        </if>
        <include refid="employeeWhereSql"/>
        <if test="param.includeReg != 1">
            and product_type!=5
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <if test="level == 2">
            group by chainId, clinicId, personnelId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
        </if>
        <if test="level != 2">
            group by chainId, clinicId, personnelId, employeeName,isCopywriter, classifyLevel1
        </if>
        <!-- 选择套餐之后就只能通过套餐子项来计算非发药的成本 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="chargeEmployeeSelectSql"/>
                0 as isCopywriter,
                '11-1' as classifyLevel1,
                <if test="level == 2">
                    0 as classifyLevel2,
                </if>
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_compose_type = 2
            and record_type not in (2, 3)
            and product_type != 18
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="param.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="feeType1 != null and feeType2 != null">
                and (${feeType1} or ${feeType2})
            </if>
            <if test="feeType1 != null and feeType2 == null">
                and ${feeType1}
            </if>
            <if test="feeType2 != null and feeType1 == null">
                and ${feeType2}
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <include refid="employeeWhereSql"/>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            <if test="level == 2">
                group by chainId, clinicId, personnelId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
            </if>
            <if test="level != 2">
                group by chainId, clinicId, personnelId, employeeName, isCopywriter, classifyLevel1
            </if>
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                copywriter_id as personnelId,
                '' as employeeName,
                1 as isCopywriter,
                classify_level_1_id as classifyLevel1,
                <if test="level == 2">
                    if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                </if>
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type not in (11,18)
            and record_type not in (2, 3)
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and copywriter_id is not null and copywriter_id != ''
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="param.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="feeType1 != null and feeType2 != null">
                and (${feeType1} or ${feeType2})
            </if>
            <if test="feeType1 != null and feeType2 == null">
                and ${feeType1}
            </if>
            <if test="feeType2 != null and feeType1 == null">
                and ${feeType2}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
            </if>
            <include refid="copywriterWhereSql"/>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="level == 2">
                group by chainId, clinicId, personnelId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
            </if>
            <if test="level != 2">
                group by chainId, clinicId, personnelId, employeeName, isCopywriter, classifyLevel1
            </if>
            <!-- 选择套餐之后就只能通过套餐子项来计算非发药的成本 -->
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as personnelId,
                    '' as employeeName,
                    1 as isCopywriter,
                    '11-1' as classifyLevel1,
                    <if test="level == 2">
                        0 as classifyLevel2,
                    </if>
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and product_compose_type = 2
                and product_type != 18
                and record_type not in (2, 3)
                and import_flag = 0
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
                <if test="param.arrearsCommissionTiming != 1">
                    and record_source_type != 1
                </if>
                <if test="param.arrearsCommissionTiming != 2">
                    and record_source_type != 2
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test="feeType1 != null and feeType2 != null">
                    and (${feeType1} or ${feeType2})
                </if>
                <if test="feeType1 != null and feeType2 == null">
                    and ${feeType1}
                </if>
                <if test="feeType2 != null and feeType1 == null">
                    and ${feeType2}
                </if>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                </if>
                <include refid="copywriterWhereSql"/>
                <if test="param.includeReg != 1">
                    and product_type!=5
                </if>
                <if test="level == 2">
                    group by chainId, clinicId, personnelId, employeeName, isCopywriter, classifyLevel1, classifyLevel2
                </if>
                <if test="level != 2">
                    group by chainId, clinicId, personnelId, employeeName, isCopywriter, classifyLevel1
                </if>
            </if>
        </if>
    </select>

    <select id="selectAdvicePersonalFeeClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalFeeAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="chargeEmployeeSelectSql"/>
            0 as isCopywriter,
            fee_type_id as feeTypeId,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and record_type not in (2, 3)
        and product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and product_compose_type = 0 and goods_fee_type in (0,2)
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
            and ${param.feeTypeIdSql}
        </if>
        <if test='param.employeeSql != null and param.employeeSql != ""'>
            ${param.employeeSql}
        </if>
        <include refid="employeeWhereSql"/>
        <if test="param.includeReg != null and param.includeReg != 1">
            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, personnelId, employeeName, isCopywriter, feeTypeId
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="chargeEmployeeSelectSql"/>
                0 as isCopywriter,
                8 as feeTypeId,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type != 18
            and product_compose_type in (2,3)
            and goods_fee_type in (0,2)
            and record_type not in (2, 3)
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <include refid="employeeWhereSql"/>
            <if test="param.includeReg != null and param.includeReg != 1">
                and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, personnelId, employeeName, isCopywriter, feeTypeId
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                copywriter_id as personnelId,
                '' as employeeName,
                1 as isCopywriter,
                fee_type_id as feeTypeId,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and record_type not in (2, 3)
            and product_type != 18
            and import_flag = 0
            and is_deleted = 0
            and copywriter_id is not null and copywriter_id != ''
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0 and goods_fee_type in (0,2)
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
            </if>
            <include refid="copywriterWhereSql"/>
            <if test="param.includeReg != null and param.includeReg != 1">
                and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
            </if>
            group by chainId, clinicId, personnelId, employeeName, isCopywriter, feeTypeId
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as personnelId,
                    '' as employeeName,
                    1 as isCopywriter,
                    8 as feeTypeId,
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and record_type not in (2, 3)
                and product_type != 18
                and product_compose_type in (2,3)
                and goods_fee_type in (0,2)
                and import_flag = 0
                and is_deleted = 0
                and copywriter_id is not null and copywriter_id != ''
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                </if>
                <include refid="copywriterWhereSql"/>
                <if test="param.includeReg != null and param.includeReg != 1">
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </if>
                group by chainId, clinicId, personnelId, employeeName, isCopywriter, feeTypeId
            </if>
        </if>
    </select>

    <select id="selectPersonalPrescription"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalPrescriptionEntity">
        select
            chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType,
            sum(if(amount>0, 1, if(amount &lt; 0, -1, 0))) as count
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="chargeEmployeeSelectSql"/>
                0 as isCopywriter,
                if(source_form_type = 6, prescription_type, source_form_type) as prescriptionType,
                v2_charge_form_id as chargeFromId,
                sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and  product_type not in (11, 18)
            and charge_sheet_type not in (3,6,8,12,16,18)
            and record_type not in (2, 3)
            and source_form_type in (4,5,6,16)
            and import_flag = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <include refid="employeeWhereSql"/>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType, chargeFromId
            <if test="param.includeWriter == 1">
                union all
                select
                chain_id as chainId,
                clinic_id as clinicId,
                copywriter_id as personnelId,
                '' as employeeName,
                1 as isCopywriter,
                if(source_form_type = 6, prescription_type, source_form_type) as prescriptionType,
                v2_charge_form_id as chargeFromId,
                sum(if(type=-1, -1, 1) * received_price) as amount
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and  product_type not in (11, 18)
                and charge_sheet_type not in (3,6,8,12,16,18)
                and record_type not in (2, 3)
                and source_form_type in (4,5,6,16)
                and import_flag = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                </if>
                <include refid="copywriterWhereSql"/>
                group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType, chargeFromId
            </if>
        )a
        group by chainId, clinicId, personnelId, isCopywriter, employeeName, prescriptionType
    </select>

    <sql id="chargeGoodsSelectSql">
        <choose>
            <when test="param.hisType != null and param.hisType == 1 and param.employeeTypeNumber != null and param.employeeTypeNumber != 1">
                <if test="param.employeeTypeNumber == 2">
                    if(case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_id else doctor_id end is not null
                    and case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_id else doctor_id end != '',
                    case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_id else doctor_id end,'00000000000000000000000000000000') as employeeId,
                    if(case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then null else COALESCE(employee_snaps ->> 'doctorName', '') end is not null,
                    case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then null else COALESCE(employee_snaps ->> 'doctorName', '') end, '') as employeeName,
                    if(case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_department_id else department_id end is not null
                    and case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_department_id else department_id end != '',
                    case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_department_id else department_id end,'00000000000000000000000000000000') as departmentId,
                </if>
                <if test="param.employeeTypeNumber == 3">
                    if(item_nurse_id is not null and item_nurse_id != '', item_nurse_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                    '' as departmentId,
                </if>
                <if test="param.employeeTypeNumber == 4">
                    if(copywriter_id is not null and copywriter_id != '', copywriter_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                    if(department_id is not null and department_id != '',department_id, '00000000000000000000000000000000') as departmentId,
                </if>
                <if test="param.employeeTypeNumber == 5">
                    if(consultant_id is not null and consultant_id != '', consultant_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                    '' as departmentId,
                </if>
            </when>
            <otherwise>
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id, '00000000000000000000000000000000')) as departmentId,
            </otherwise>
        </choose>
    </sql>

    <sql id="dispensingGoodsSelectSql">
        <choose>
            <when test="param.hisType != null and param.hisType == 1 and param.employeeTypeNumber != null and param.employeeTypeNumber != 1">
                <if test="param.employeeTypeNumber == 2">
                    if(case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_id else doctor_id end is not null
                    and case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_id else doctor_id end != '',
                    case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_id else doctor_id end,'00000000000000000000000000000000') as employeeId,
                    if(case when charge_sheet_type in (3,6,8,12,16,18) then null else COALESCE(employee_snaps ->> 'doctorName', '') end is not null,
                    case when charge_sheet_type in (3,6,8,12,16,18) then null else COALESCE(employee_snaps ->> 'doctorName', '') end, '') as employeeName,
                    if(case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_department_id else department_id end is not null
                    and case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_department_id else department_id end != '',
                    case when charge_sheet_type in (3,6,8,12,16,18) then item_doctor_department_id else department_id end,'00000000000000000000000000000000') as departmentId,
                </if>
                <if test="param.employeeTypeNumber == 3">
                    if(item_nurse_id is not null and item_nurse_id != '', item_nurse_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                    '' as departmentId,
                </if>
                <if test="param.employeeTypeNumber == 4">
                    if(copywriter_id is not null and copywriter_id != '', copywriter_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                    if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as departmentId,
                </if>
                <if test="param.employeeTypeNumber == 5">
                    if(consultant_id is not null and consultant_id != '', consultant_id, '00000000000000000000000000000000') as employeeId,
                    '' as employeeName,
                    '' as departmentId,
                </if>
            </when>
            <otherwise>
                if(transcribe_doctor_id=doctor_id, if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, if(seller_id is not null and seller_id != '',COALESCE(employee_snaps ->> 'sellerName', ''), ''), if(seller_id is not null and seller_id != '', COALESCE(employee_snaps ->> 'sellerName', ''), COALESCE(employee_snaps ->> 'doctorName', ''))) as employeeName,
                if(transcribe_doctor_id=doctor_id, if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
            </otherwise>
        </choose>
    </sql>

    <select id="selectGoods"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeGoodsEntity">
        select
        <if test="param.payModeSql == null or param.payModeSql == ''">
            if(x.chainId is null, y.chainId, x.chainId) as chainId,
            if(x.clinicId is null, y.clinicId, x.clinicId) as clinicId,
            if(x.employeeId is null, y.employeeId, x.employeeId) as personnelId,
            if(x.employeeName is null, y.employeeName, x.employeeName) as employeeName,
            if(x.departmentId is null, y.departmentId, x.departmentId) as departmentId,
            if(x.isWriter is null, y.isWriter, x.isWriter) as isWriter,
            if(x.goodsId is null, y.goodsId, x.goodsId) as goodsId,
            if(x.classifyLevel1 is null, y.classifyLevel1, x.classifyLevel1) as classifyLevel1,
            if(x.classifyLevel2 is null, y.classifyLevel2, x.classifyLevel2) as classifyLevel2,
            if(x.unit is null, y.unit, x.unit) as unit,
            if(x.unitCount is null, 0.0, x.unitCount) as count,
            if(x.costPrice is null,0.0,x.costPrice) + if(y.costPrice is null,0.0,y.costPrice) as cost,
            if(x.costPrice is null, 2, 0 ) + if(y.hoverCode is null, 0, y.hoverCode) as hoverCode,
            if(x.receivedPrice is null, 0.0, x.receivedPrice) as amount,
            if(x.originPrice is null, 0.0, x.originPrice) as originPrice,
            if(x.deductPrice is null, 0.0, x.deductPrice) as deductPrice
        </if>
        <if test="param.payModeSql != null and param.payModeSql != ''">
            x.chainId as chainId,
            x.clinicId as clinicId,
            x.employeeId as personnelId,
            x.employeeName as employeeName,
            x.departmentId as departmentId,
            x.isWriter as isWriter,
            x.goodsId as goodsId,
            x.classifyLevel1 as classifyLevel1,
            x.classifyLevel2 as classifyLevel2,
            x.unit as unit,
            x.unitCount as count,
            null as costPrice,
            0 as hoverCode,
            x.receivedPrice as amount,
            x.originPrice as originPrice,
            x.deductPrice as deductPrice
        </if>
        from
        (
            select
                a.chainId,
                a.clinicId,
                a.employeeId,
                a.employeeName,
                a.departmentId,
                a.isWriter,
                a.goodsId,
                a.classifyLevel1,
                a.classifyLevel2,
                a.unit,
                a.receivedPrice,
                a.costPrice,
                a.originPrice,
                a.deductPrice,
                coalesce(b.unitCount,0.0) as unitCount
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="chargeGoodsSelectSql"/>
                    0 as isWriter,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        product_id as goodsId,
                        classify_level_1_id as classifyLevel1,
                        if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                        if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                        sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                        <if test="param.hisType != null and param.hisType != 100">
                            if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                        </if>
                        <if test="param.hisType != null and param.hisType == 100">
                            if (product_compose_type = 2, '8', classify_level_1_id) as classifyLevel1,
                        </if>
                        if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                        sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                        sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                        sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
                    </if>
                from ${env}.dwd_charge_transaction_record_v_partition
                where record_type not in (2, 3)
                and product_type != 18
                and chain_id=#{param.chainId}
                and import_flag = 0
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.composeSql != null and param.composeSql != ''">
                    and ${param.composeSql}
                </if>
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
                <if test="param.arrearsCommissionTiming != 1">
                    and record_source_type != 1
                </if>
                <if test="param.arrearsCommissionTiming != 2">
                    and record_source_type != 2
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <!--departmentId条件筛选 -->
                <include refid="conditionForDepId"/>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <include refid="employeeWhereSql"/>
                <if test="param.includeReg != 1">
                    and product_type!=5
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            ) a
            left join
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="chargeGoodsSelectSql"/>
                    0 as isWriter,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        product_id as goodsId,
                        classify_level_1_id as classifyLevel1,
                        if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                        if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                        sum(calc_count) as unitCount
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                        <if test="param.hisType != null and param.hisType != 100">
                            if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                        </if>
                        <if test="param.hisType != null and param.hisType == 100">
                            if (product_compose_type = 2, '8', classify_level_1_id) as classifyLevel1,
                        </if>
                        if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                        sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount
                    </if>
                from ${env}.dwd_charge_transaction_record_v_partition
                where record_type not in (2, 3)
                and product_type != 18
                and chain_id=#{param.chainId}
                and import_flag = 0
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and record_source_type != 2
                <if test="param.composeSql != null and param.composeSql != ''">
                    and ${param.composeSql}
                </if>
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <!--departmentId条件筛选 -->
                <include refid="conditionForDepId"/>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <include refid="employeeWhereSql"/>
                <if test="param.includeReg != 1">
                    and product_type!=5
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            ) b
            on a.chainId = b.chainId and a.clinicId = b.clinicId and a.employeeId = b.employeeId and a.employeeName = b.employeeName and a.departmentId = b.departmentId
            and a.goodsId = b.goodsId and a.classifyLevel1 = b.classifyLevel1 and a.classifyLevel2 = b.classifyLevel2 and a.unit = b.unit
            <if test="param.includeWriter == 1">
                union all
                select
                    c.chainId,
                    c.clinicId,
                    c.employeeId,
                    c.employeeName,
                    c.departmentId,
                    c.isWriter,
                    c.goodsId,
                    c.classifyLevel1,
                    c.classifyLevel2,
                    c.unit,
                    c.receivedPrice,
                    c.costPrice,
                    c.originPrice,
                    c.deductPrice,
                    coalesce(d.unitCount,0.0) as unitCount
                from
                (
                    select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as employeeId,
                    '' as employeeName,
                    if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as departmentId,
                    1 as isWriter,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        product_id as goodsId,
                        classify_level_1_id as classifyLevel1,
                        if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                        if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                        sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                        <if test="param.hisType != null and param.hisType != 100">
                            if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                        </if>
                        <if test="param.hisType != null and param.hisType == 100">
                            if (product_compose_type = 2, '8', classify_level_1_id) as classifyLevel1,
                        </if>
                        if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                        sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                        sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                        sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
                    </if>
                    from ${env}.dwd_charge_transaction_record_v_partition
                    where record_type not in (2, 3)
                    and product_type != 18
                    and import_flag = 0
                    and is_deleted = 0
                    and chain_id=#{param.chainId}
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and copywriter_id is not null and copywriter_id != ''
                    <if test="param.composeSql != null and param.composeSql != ''">
                        and ${param.composeSql}
                    </if>
                    <if test="param.isCardOpeningFee == 0">
                        and product_type != 17
                    </if>
                    <if test="param.arrearsCommissionTiming != 1">
                        and record_source_type != 1
                    </if>
                    <if test="param.arrearsCommissionTiming != 2">
                        and record_source_type != 2
                    </if>
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                    </if>
                    <if test="param.payModeSql != null and param.payModeSql != ''">
                        and ${param.payModeSql}
                    </if>
                    <if test="param.fee1 != null and param.fee2 != null">
                        and (${param.fee1} or ${param.fee2})
                    </if>
                    <if test="param.fee1 != null and param.fee2 == null">
                        and ${param.fee1}
                    </if>
                    <if test="param.fee2 != null and param.fee1 == null">
                        and ${param.fee2}
                    </if>
                    <if test="param.productId != null and param.productId != ''">
                        and product_id = #{param.productId}
                    </if>
                    <if test='param.employeeSql != null and param.employeeSql != ""'>
                        ${param.employeeSql}
                    </if>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                    </if>
                    <include refid="copywriterWhereSql"/>
                    <if test="param.includeReg != 1">
                        and product_type!=5
                    </if>
                    group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
                ) c
                left join
                (
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        copywriter_id as employeeId,
                        '' as employeeName,
                        if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as departmentId,
                        1 as isWriter,
                        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                            product_id as goodsId,
                            classify_level_1_id as classifyLevel1,
                            if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                            if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                            sum(calc_count) as unitCount
                        </if>
                        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                            if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                            <if test="param.hisType != null and param.hisType != 100">
                                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                            </if>
                            <if test="param.hisType != null and param.hisType == 100">
                                if (product_compose_type = 2, '8', classify_level_1_id) as classifyLevel1,
                            </if>
                            if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                            if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                            sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount
                        </if>
                    from ${env}.dwd_charge_transaction_record_v_partition
                    where record_type not in (2, 3)
                    and product_type != 18
                    and import_flag = 0
                    and is_deleted = 0
                    and chain_id=#{param.chainId}
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and copywriter_id is not null and copywriter_id != ''
                    and record_source_type != 2
                    <if test="param.composeSql != null and param.composeSql != ''">
                        and ${param.composeSql}
                    </if>
                    <if test="param.isCardOpeningFee == 0">
                        and product_type != 17
                    </if>
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                    </if>
                    <if test="param.payModeSql != null and param.payModeSql != ''">
                        and ${param.payModeSql}
                    </if>
                    <if test="param.fee1 != null and param.fee2 != null">
                        and (${param.fee1} or ${param.fee2})
                    </if>
                    <if test="param.fee1 != null and param.fee2 == null">
                        and ${param.fee1}
                    </if>
                    <if test="param.fee2 != null and param.fee1 == null">
                        and ${param.fee2}
                    </if>
                    <if test="param.productId != null and param.productId != ''">
                        and product_id = #{param.productId}
                    </if>
                    <if test='param.employeeSql != null and param.employeeSql != ""'>
                        ${param.employeeSql}
                    </if>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                    </if>
                    <include refid="copywriterWhereSql"/>
                    <if test="param.includeReg != 1">
                        and product_type!=5
                    </if>
                    group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
                ) d
                on c.chainId = d.chainId and c.clinicId = d.clinicId and c.employeeId = d.employeeId and c.employeeName = d.employeeName and c.departmentId = d.departmentId
                and c.goodsId = d.goodsId and c.classifyLevel1 = d.classifyLevel1 and c.classifyLevel2 = d.classifyLevel2 and c.unit = d.unit
            </if>
        ) x
        <if test="param.payModeSql == null or param.payModeSql == ''">
            full OUTER JOIN
            (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="dispensingGoodsSelectSql"/>
                0 as isWriter,
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                    if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,

                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                    <if test="param.hisType != null and param.hisType != 100">
                        if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                    </if>
                    <if test="param.hisType != null and param.hisType == 100">
                        if (product_compose_type = 2, '8', classify_level_1_id) as classifyLevel1,
                    </if>
                    if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                    if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                </if>
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.productId != null and param.productId != ''">
                and product_id = #{param.productId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                and copywriter_id = #{param.copyWriterId}
            </if>
            <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                and (copywriter_id is null or copywriter_id = '')
            </if>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            <if test="param.includeWriter == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as employeeId,
                    '' as employeeName,
                    if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000') as departmentId,
                    1 as isWriter,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        product_id as goodsId,
                        classify_level_1_id as classifyLevel1,
                        if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                        if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                        <if test="param.hisType != null and param.hisType != 100">
                            if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                        </if>
                        <if test="param.hisType != null and param.hisType == 100">
                            if (product_compose_type = 2, '8', classify_level_1_id) as classifyLevel1,
                        </if>
                        if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                    </if>
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                    if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                from
                ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and form_type = 0
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                    ${param.dispensingEmployeeSql}
                </if>
                <include refid="copywriterWhereSql"/>
                <if test="param.includeReg != 1">
                    and product_type!=5
                </if>
                GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            </if>
            ) y
            on x.chainId=y.chainId and x.clinicId=y.clinicId and x.employeeId=y.employeeId and x.employeeName=y.employeeName and x.departmentId = y.departmentId
            and x.isWriter=y.isWriter and x.goodsId=y.goodsId and x.classifyLevel1=y.classifyLevel1 and x.classifyLevel2=y.classifyLevel2 and x.unit=y.unit
        </if>
        order by chainId, clinicId, personnelId, isWriter,goodsId
        <if test="param.size != null and param.size != 0">
            limit #{param.size}
            offset #{param.offset}
        </if>
    </select>

    <select id="selectGoodsTotal"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeTotalEntity">
            select
                count(1) as count,
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 1 and param.includeWriter == 1">
                sum(originPrice) as originPrice,
                sum(deductPrice) as deductPrice,
                sum(receivedPrice) as amount
            </if>
            <if test="param.isContainOthersWriterAchievement == null or (param.isContainOthersWriterAchievement != null
                            and ((param.isContainOthersWriterAchievement != 0) or (param.isContainOthersWriterAchievement != 1 and param.includeWriter == 0)))">
                sum(if(isWriter=0, originPrice,0.0)) as originPrice,
                sum(if(isWriter=0,deductPrice,0.0)) as deductPrice,
                sum(if(isWriter=0,receivedPrice,0.0)) as amount
            </if>
            from
            (
            select
            if(a.chainId is null, b.chainId, a.chainId) as chainId,
            if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
            if(a.employeeId is null, b.employeeId, a.employeeId) as personnelId,
            if(a.employeeName is null, b.employeeName, a.employeeName) as employeeName,
            if(a.departmentId is null, b.departmentId, a.departmentId) as departmentId,
            if(a.isWriter is null, b.isWriter, a.isWriter) as isWriter,
            if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
            if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
            if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
            if(a.unit is null, b.unit, a.unit) as unit,
            if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
            if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice,
            if(a.receivedPrice is null, 0.0, a.receivedPrice) as receivedPrice
            from
            (
            select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="chargeGoodsSelectSql"/>
            0 as isWriter,
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1,1,-1) * deduct_promotion_price) as deductPrice
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                sum(if(product_compose_type = 1, 0.0, if(type=-1, -received_price, received_price))) as receivedPrice,
                sum(if(product_compose_type != 1,if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)),0.0)) as originPrice,
                sum(if(product_compose_type != 1,if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
            </if>
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where record_type not in (2, 3)
            and product_type != 18
            and import_flag = 0
            and is_deleted = 0
            and chain_id=#{param.chainId}
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="param.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <include refid="conditionForDepId"/>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.productId != null and param.productId != ''">
                and product_id = #{param.productId}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <include refid="employeeWhereSql"/>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            <if test="param.includeWriter == 1">
                union all
                select
                chain_id as chainId,
                clinic_id as clinicId,
                copywriter_id as employeeId,
                '' as employeeName,
                if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as
                departmentId,
                1 as isWriter,
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                    if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                    if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                    if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                    if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                </if>
                <if test="param.hisType != null and param.hisType != ''">
                    <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 1 and param.includeWriter == 1">
                        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                            sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                            sum(if(type=-1,1,-1) * deduct_promotion_price) as deductPrice
                        </if>
                        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                            sum(if(product_compose_type = 1, 0.0, if(type=-1, -received_price, received_price))) as receivedPrice,
                            sum(if(product_compose_type != 1,if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)),0.0)) as originPrice,
                            sum(if(product_compose_type != 1,if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
                        </if>
                    </if>
                    <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 1 and param.includeWriter == 0">
                        0.0 as receivedPrice,
                        0.0 as originPrice,
                        0.0 as deductPrice
                    </if>
                    <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 0">
                        0.0 as receivedPrice,
                        0.0 as originPrice,
                        0.0 as deductPrice
                    </if>
                </if>
                <if test="param.hisType == null or param.hisType == ''">
                    <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 1 and param.includeWriter == 1">
                        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                            sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                            sum(if(type=-1,1,-1) * deduct_promotion_price) as deductPrice
                        </if>
                        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                            sum(if(product_compose_type = 1, 0.0, if(type=-1, -received_price, received_price))) as receivedPrice,
                            sum(if(product_compose_type != 1,if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)),0.0)) as originPrice,
                            sum(if(product_compose_type != 1,if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
                        </if>
                    </if>
                    <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 1 and param.includeWriter == 0">
                        0.0 as receivedPrice,
                        0.0 as originPrice,
                        0.0 as deductPrice
                    </if>
                    <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 0">
                        0.0 as receivedPrice,
                        0.0 as originPrice,
                        0.0 as deductPrice
                    </if>
                </if>
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where record_type not in (2, 3)
                and product_type != 18
                and import_flag = 0
                and is_deleted = 0
                and chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.composeSql != null and param.composeSql != ''">
                    and ${param.composeSql}
                </if>
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
                <if test="param.arrearsCommissionTiming != 1">
                    and record_source_type != 1
                </if>
                <if test="param.arrearsCommissionTiming != 2">
                    and record_source_type != 2
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                </if>
                <include refid="copywriterWhereSql"/>
                <if test="param.includeReg != 1">
                    and product_type!=5
                </if>
                group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            </if>
            ) a
            full OUTER JOIN
            (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="dispensingGoodsSelectSql"/>
                0 as isWriter,
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                    if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                    if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                    if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                    if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                </if>
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.productId != null and param.productId != ''">
                and product_id = #{param.productId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                and copywriter_id = #{param.copyWriterId}
            </if>
            <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                and (copywriter_id is null or copywriter_id = '')
            </if>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            <if test="param.includeWriter == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as employeeId,
                    '' as employeeName,
                    if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000') as departmentId,
                    1 as isWriter,
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    product_id as goodsId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                    if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                    if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                    if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                    if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
                </if>
                from
                ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and form_type = 0
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                    ${param.dispensingEmployeeSql}
                </if>
                <include refid="copywriterWhereSql"/>
                <if test="param.includeReg != 1">
                    and product_type!=5
                </if>
                GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit
            </if>
            ) b
            on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeName=b.employeeName and a.departmentId = b.departmentId
            and a.isWriter=b.isWriter and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit
            ) c
    </select>

    <select id="selectHospitalGoods" resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeGoodsEntity">
        select
            if(c.chainId is null, d.chainId, c.chainId) as chainId,
            if(c.clinicId is null, d.clinicId, c.clinicId) as clinicId,
            if(c.employeeId is null, d.employeeId, c.employeeId) as personnelId,
            if(c.employeeName is null, d.employeeName, c.employeeName) as employeeName,
            if(c.departmentId is null, d.departmentId, c.departmentId) as departmentId,
            if(c.isWriter is null, d.isWriter, c.isWriter) as isWriter,
            if(c.goodsId is null, d.goodsId, c.goodsId) as goodsId,
            if(c.classifyLevel1 is null, d.classifyLevel1, c.classifyLevel1) as classifyLevel1,
            if(c.classifyLevel2 is null, d.classifyLevel2, c.classifyLevel2) as classifyLevel2,
            c.feeTypeProductId as feeTypeProductId,
            if(c.feeTypeId is null, d.feeTypeId, c.feeTypeId) as feeTypeId,
            if(c.unit is null, d.unit, c.unit) as unit,
            if(c.unitCount is null, 0.0, c.unitCount) as count,
            if(c.costPrice is null,0.0,c.costPrice) + if(d.costPrice is null,0.0,d.costPrice) as cost,
            if(c.costPrice is null, 2, 0) + if(d.hoverCode is null, 0, d.hoverCode) as hoverCode,
            if(c.receivedPrice is null, 0.0, c.receivedPrice) as amount,
            if(c.originPrice is null, 0.0, c.originPrice) as originPrice,
            if(c.deductPrice is null, 0.0, c.deductPrice) as deductPrice
        from
        (
            select
                a.chainId,
                a.clinicId,
                a.employeeId,
                a.employeeName,
                a.departmentId,
                a.isWriter,
                a.goodsId,
                a.classifyLevel1,
                a.classifyLevel2,
                a.feeTypeProductId,
                a.feeTypeId,
                a.unit,
                a.receivedPrice,
                a.costPrice,
                a.originPrice,
                a.deductPrice,
                coalesce(b.unitCount,0.0) as unitCount
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="chargeGoodsSelectSql"/>
                    0 as isWriter,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        if(product_type not in (1,2,7),compose_parent_product_id,product_id) as goodsId,
                        if(product_type not in (1,2,7),compose_parent_classify_level_1_id,classify_level_1_id) as classifyLevel1,
                        if(product_type not in (1,2,7),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if(product_type not in (1,2,7),product_id,null) as feeTypeProductId,
                        fee_type_id as feeTypeId,
                        if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                        sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if ((product_compose_type = 2 or product_type not in (1,2,7,11)), compose_parent_product_id,product_id) as goodsId,
                        if (product_compose_type = 2, '11-1', if(product_type not in (1,2,7,11),compose_parent_classify_level_1_id,classify_level_1_id)) as classifyLevel1,
                        if (product_compose_type = 2, 0, if(product_type not in (1,2,7,11),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id))) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                        if (product_compose_type = 2 or product_type in (1,2,7,11), null, product_id) as feeTypeProductId,
                        if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                        sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                        sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                        sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
                    </if>
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and record_type not in (2, 3)
                and product_type != 18
                and import_flag = 0
                and is_deleted = 0
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    and product_compose_type in (0, 2, 3)
                    and goods_fee_type in (0, 2)
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <include refid="conditionForDepId"/>
                <if test="param.hospitalFee1 != null and param.hospitalFee2 != null">
                    and (${param.hospitalFee1} or ${param.hospitalFee2})
                </if>
                <if test="param.hospitalFee1 != null and param.hospitalFee2 == null">
                    and ${param.hospitalFee1}
                </if>
                <if test="param.hospitalFee2 != null and param.hospitalFee1 == null">
                    and ${param.hospitalFee2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <include refid="employeeWhereSql"/>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test="param.includeReg != null and param.includeReg != 1">
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
            ) a
            left join
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="chargeGoodsSelectSql"/>
                    0 as isWriter,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        if(product_type not in (1,2,7),compose_parent_product_id,product_id) as goodsId,
                        if(product_type not in (1,2,7),compose_parent_classify_level_1_id,classify_level_1_id) as classifyLevel1,
                        if(product_type not in (1,2,7),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if(product_type not in (1,2,7),product_id,null) as feeTypeProductId,
                        fee_type_id as feeTypeId,
                        if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                        sum(calc_count) as unitCount
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if ((product_compose_type = 2 or product_type not in (1,2,7,11)), compose_parent_product_id,product_id) as goodsId,
                        if (product_compose_type = 2, '11-1', if(product_type not in (1,2,7,11),compose_parent_classify_level_1_id,classify_level_1_id)) as classifyLevel1,
                        if (product_compose_type = 2, 0, if(product_type not in (1,2,7,11),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id))) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                        if (product_compose_type = 2 or product_type in (1,2,7,11), null, product_id) as feeTypeProductId,
                        if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                        sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount
                    </if>
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and record_type not in (2, 3)
                and product_type != 18
                and import_flag = 0
                and is_deleted = 0
                and record_source_type != 2
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    and product_compose_type in (0, 2, 3)
                    and goods_fee_type in (0, 2)
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <include refid="conditionForDepId"/>
                <if test="param.hospitalFee1 != null and param.hospitalFee2 != null">
                    and (${param.hospitalFee1} or ${param.hospitalFee2})
                </if>
                <if test="param.hospitalFee1 != null and param.hospitalFee2 == null">
                    and ${param.hospitalFee1}
                </if>
                <if test="param.hospitalFee2 != null and param.hospitalFee1 == null">
                    and ${param.hospitalFee2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <include refid="employeeWhereSql"/>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test="param.includeReg != null and param.includeReg != 1">
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
            ) b
            on a.chainId = b.chainId and a.clinicId = b.clinicId and a.employeeId = b.employeeId and a.employeeName = b.employeeName and a.departmentId = b.departmentId and a.feeTypeId=b.feeTypeId
            and a.feeTypeProductId = b.feeTypeProductId and a.goodsId = b.goodsId and a.classifyLevel1 = b.classifyLevel1 and a.classifyLevel2 = b.classifyLevel2 and a.unit = b.unit
            <if test="param.includeWriter == 1">
                union all
                select
                    e.chainId,
                    e.clinicId,
                    e.employeeId,
                    e.employeeName,
                    e.departmentId,
                    e.isWriter,
                    e.goodsId,
                    e.classifyLevel1,
                    e.classifyLevel2,
                    e.feeTypeProductId,
                    e.feeTypeId,
                    e.unit,
                    e.receivedPrice,
                    e.costPrice,
                    e.originPrice,
                    e.deductPrice,
                    coalesce(f.unitCount,0.0) as unitCount
                from
                (
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        copywriter_id as employeeId,
                        '' as employeeName,
                        if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as departmentId,
                        1 as isWriter,
                        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                            if(product_type not in (1,2,7),compose_parent_product_id,product_id) as goodsId,
                            if(product_type not in (1,2,7),compose_parent_classify_level_1_id,classify_level_1_id) as classifyLevel1,
                            if(product_type not in (1,2,7),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                            if(product_type not in (1,2,7),product_id,null) as feeTypeProductId,
                            fee_type_id as feeTypeId,
                            if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
                        </if>
                        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                            if ((product_compose_type = 2 or product_type not in (1,2,7,11)), compose_parent_product_id,product_id) as goodsId,
                            if (product_compose_type = 2, '11-1', if(product_type not in (1,2,7,11),compose_parent_classify_level_1_id,classify_level_1_id)) as classifyLevel1,
                            if (product_compose_type = 2, 0, if(product_type not in (1,2,7,11),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id))) as classifyLevel2,
                            if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                            if (product_compose_type = 2 or product_type in (1,2,7,11), null, product_id) as feeTypeProductId,
                            if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                            sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                            sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                            sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                            sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice
                        </if>
                    from ${env}.dwd_charge_transaction_record_v_partition
                    where record_type not in (2, 3)
                    and product_type != 18
                    and import_flag = 0
                    and is_deleted = 0
                    and chain_id=#{param.chainId}
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and copywriter_id is not null and copywriter_id != ''
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        and product_compose_type in (0, 2, 3)
                        and goods_fee_type in (0, 2)
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
                    </if>
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                        and ${param.arrearsCommissionTimingSql}
                    </if>
                    <include refid="conditionForDepId"/>
                    <if test="param.hospitalFee1 != null and param.hospitalFee2 != null">
                        and (${param.hospitalFee1} or ${param.hospitalFee2})
                    </if>
                    <if test="param.hospitalFee1 != null and param.hospitalFee2 == null">
                        and ${param.hospitalFee1}
                    </if>
                    <if test="param.hospitalFee2 != null and param.hospitalFee1 == null">
                        and ${param.hospitalFee2}
                    </if>
                    <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                        and ${param.feeTypeIdSql}
                    </if>
                    <if test="param.productId != null and param.productId != ''">
                        and product_id = #{param.productId}
                    </if>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                    </if>
                    <include refid="copywriterWhereSql"/>
                    <if test='param.employeeSql != null and param.employeeSql != ""'>
                        ${param.employeeSql}
                    </if>
                    <if test="param.includeReg != null and param.includeReg != 1">
                        and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                    </if>
                    group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
                ) e
                left join
                (
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        copywriter_id as employeeId,
                        '' as employeeName,
                        if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as departmentId,
                        1 as isWriter,
                        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                            if(product_type not in (1,2,7),compose_parent_product_id,product_id) as goodsId,
                            if(product_type not in (1,2,7),compose_parent_classify_level_1_id,classify_level_1_id) as classifyLevel1,
                            if(product_type not in (1,2,7),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                            if(product_type not in (1,2,7),product_id,null) as feeTypeProductId,
                            fee_type_id as feeTypeId,
                            if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                            sum(calc_count) as unitCount
                        </if>
                        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                            if ((product_compose_type = 2 or product_type not in (1,2,7,11)), compose_parent_product_id,product_id) as goodsId,
                            if (product_compose_type = 2, '11-1', if(product_type not in (1,2,7,11),compose_parent_classify_level_1_id,classify_level_1_id)) as classifyLevel1,
                            if (product_compose_type = 2, 0, if(product_type not in (1,2,7,11),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id))) as classifyLevel2,
                            if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                            if (product_compose_type = 2 or product_type in (1,2,7,11), null, product_id) as feeTypeProductId,
                            if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                            sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount
                        </if>
                    from ${env}.dwd_charge_transaction_record_v_partition
                    where chain_id=#{param.chainId}
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and record_type not in (2, 3)
                    and product_type != 18
                    and import_flag = 0
                    and is_deleted = 0
                    and record_source_type != 2
                    and copywriter_id is not null and copywriter_id != ''
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        and product_compose_type in (0, 2, 3)
                        and goods_fee_type in (0, 2)
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
                    </if>
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                        and ${param.arrearsCommissionTimingSql}
                    </if>
                    <include refid="conditionForDepId"/>
                    <if test="param.hospitalFee1 != null and param.hospitalFee2 != null">
                        and (${param.hospitalFee1} or ${param.hospitalFee2})
                    </if>
                    <if test="param.hospitalFee1 != null and param.hospitalFee2 == null">
                        and ${param.hospitalFee1}
                    </if>
                    <if test="param.hospitalFee2 != null and param.hospitalFee1 == null">
                        and ${param.hospitalFee2}
                    </if>
                    <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                        and ${param.feeTypeIdSql}
                    </if>
                    <if test="param.productId != null and param.productId != ''">
                        and product_id = #{param.productId}
                    </if>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                    </if>
                    <include refid="copywriterWhereSql"/>
                    <if test='param.employeeSql != null and param.employeeSql != ""'>
                        ${param.employeeSql}
                    </if>
                    <if test="param.includeReg != null and param.includeReg != 1">
                        and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                    </if>
                    group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
                ) f
                on e.chainId = f.chainId and e.clinicId = f.clinicId and e.employeeId = f.employeeId and e.employeeName = f.employeeName and e.departmentId = f.departmentId and e.feeTypeId=f.feeTypeId
                and e.feeTypeProductId = f.feeTypeProductId and e.goodsId = f.goodsId and e.classifyLevel1 = f.classifyLevel1 and e.classifyLevel2 = f.classifyLevel2 and e.unit = f.unit
            </if>
        ) c
        full OUTER JOIN
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="dispensingGoodsSelectSql"/>
                0 as isWriter,
                null as feeTypeProductId,
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    product_id as goodsId,
                    fee_type_id as feeTypeId,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                    if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                    if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                    if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                    if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                    if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                </if>
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.productId != null and param.productId != ''">
                and product_id = #{param.productId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                and copywriter_id = #{param.copyWriterId}
            </if>
            <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                and (copywriter_id is null or copywriter_id = '')
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, classifyLevel1, classifyLevel2, unit
            <if test="param.includeWriter == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as employeeId,
                    '' as employeeName,
                    if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000') as departmentId,
                    1 as isWriter,
                    null as feeTypeProductId,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        product_id as goodsId,
                        fee_type_id as feeTypeId,
                        classify_level_1_id as classifyLevel1,
                        if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                        if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                        if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                        if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                        if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                    </if>
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                    if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                from
                ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and form_type = 0
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
                </if>
                <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                    ${param.dispensingEmployeeSql}
                </if>
                <include refid="copywriterWhereSql"/>
                group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
            </if>
        ) d
        on c.chainId=d.chainId and c.clinicId=d.clinicId and c.employeeId=d.employeeId and c.employeeName=d.employeeName and c.departmentId = d.departmentId and c.classifyLevel1=d.classifyLevel1 and c.classifyLevel2=d.classifyLevel2 and c.unit=d.unit
        and c.goodsId=d.goodsId and c.feeTypeId=d.feeTypeId
        order by chainId, clinicId, personnelId, employeeName, departmentId, goodsId, feeTypeId
        <if test="param.size != null and param.size != 0">
            limit #{param.size} offset #{param.offset}
        </if>
    </select>

    <select id="selectHospitalGoodsTotal" resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeTotalEntity">
        select
            count(1) as count,
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement != 1 and param.includeWriter == 1">
                sum(originPrice) as originPrice,
                sum(deductPrice) as deductPrice,
                sum(amount) as amount
            </if>
            <if test="param.isContainOthersWriterAchievement == null or (param.isContainOthersWriterAchievement != null
                                and ((param.isContainOthersWriterAchievement != 0) or (param.isContainOthersWriterAchievement != 1 and param.includeWriter == 0)))">
                sum(if(isWriter=0, originPrice,0.0)) as originPrice,
                sum(if(isWriter=0,deductPrice,0.0)) as deductPrice,
                sum(if(isWriter=0,amount,0.0)) as amount
            </if>
        from
        (
            select
                if(a.chainId is null, b.chainId, a.chainId) as chainId,
                if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
                if(a.employeeId is null, b.employeeId, a.employeeId) as personnelId,
                if(a.employeeName is null, b.employeeName, a.employeeName) as employeeName,
                if(a.departmentId is null, b.departmentId, a.departmentId) as departmentId,
                if(a.isWriter is null, b.isWriter, a.isWriter) as isWriter,
                if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
                if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
                if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
                a.feeTypeProductId as feeTypeProductId,
                if(a.feeTypeId is null, b.feeTypeId, a.feeTypeId) as feeTypeId,
                if(a.unit is null, b.unit, a.unit) as unit,
                if(a.unitCount is null, 0.0, a.unitCount) as count,
                if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as cost,
                if(a.costPrice is null, 2, 0) + if(b.hoverCode is null, 0, b.hoverCode) as hoverCode,
                if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
                if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
                if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice
            from
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="chargeGoodsSelectSql"/>
                    0 as isWriter,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        if(product_type not in (1,2,7),compose_parent_product_id,product_id) as goodsId,
                        if(product_type not in (1,2,7),compose_parent_classify_level_1_id,classify_level_1_id) as classifyLevel1,
                        if(product_type not in (1,2,7),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if(product_type not in (1,2,7),product_id,null) as feeTypeProductId,
                        fee_type_id as feeTypeId,
                        if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                        sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice,
                        sum(calc_count) as unitCount
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if ((product_compose_type = 2 or product_type not in (1,2,7,11)), compose_parent_product_id,product_id) as goodsId,
                        if (product_compose_type = 2, '11-1', if(product_type not in (1,2,7,11),compose_parent_classify_level_1_id,classify_level_1_id)) as classifyLevel1,
                        if (product_compose_type = 2, 0, if(product_type not in (1,2,7,11),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id))) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                        if (product_compose_type = 2 or product_type in (1,2,7,11), null, product_id) as feeTypeProductId,
                        if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                        sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                        sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                        sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                        sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice,
                        sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount
                    </if>
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and record_type not in (2, 3)
                and product_type != 18
                and import_flag = 0
                and is_deleted = 0
                <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                    and product_compose_type in (0, 2, 3)
                    and goods_fee_type in (0, 2)
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <include refid="conditionForDepId"/>
                <if test="param.hospitalFee1 != null and param.hospitalFee2 != null">
                    and (${param.hospitalFee1} or ${param.hospitalFee2})
                </if>
                <if test="param.hospitalFee1 != null and param.hospitalFee2 == null">
                    and ${param.hospitalFee1}
                </if>
                <if test="param.hospitalFee2 != null and param.hospitalFee1 == null">
                    and ${param.hospitalFee2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <include refid="employeeWhereSql"/>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test="param.includeReg != null and param.includeReg != 1">
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
                <if test="param.includeWriter == 1">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        copywriter_id as employeeId,
                        '' as employeeName,
                        if(department_id is not null and department_id != '', department_id, '00000000000000000000000000000000') as departmentId,
                        1 as isWriter,
                        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                            if(product_type not in (1,2,7),compose_parent_product_id,product_id) as goodsId,
                            if(product_type not in (1,2,7),compose_parent_classify_level_1_id,classify_level_1_id) as classifyLevel1,
                            if(product_type not in (1,2,7),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                            if(product_type not in (1,2,7),product_id,null) as feeTypeProductId,
                            fee_type_id as feeTypeId,
                            if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice,
                            sum(calc_count) as unitCount
                        </if>
                        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                            if ((product_compose_type = 2 or product_type not in (1,2,7,11)), compose_parent_product_id,product_id) as goodsId,
                            if (product_compose_type = 2, '11-1', if(product_type not in (1,2,7,11),compose_parent_classify_level_1_id,classify_level_1_id)) as classifyLevel1,
                            if (product_compose_type = 2, 0, if(product_type not in (1,2,7,11),if(compose_parent_classify_level_2_id is null,0,compose_parent_classify_level_2_id),if(classify_level_2_id is null,0,classify_level_2_id))) as classifyLevel2,
                            if (product_compose_type = 2, '次', if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                            if (product_compose_type = 2 or product_type in (1,2,7,11), null, product_id) as feeTypeProductId,
                            if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                            sum(if(product_compose_type != 1,if(type=-1, -received_price, received_price), 0.0)) as receivedPrice,
                            sum(if(product_type not in (1,2,7,11) and product_compose_type != 1, if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                            sum(if(product_compose_type != 1, if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price)), 0.0)) as originPrice,
                            sum(if(product_compose_type != 1, if(type=-1,1,-1) * deduct_promotion_price, 0.0)) as deductPrice,
                            sum(if(product_compose_type = 2,0.0,calc_count)) as unitCount
                        </if>
                    from ${env}.dwd_charge_transaction_record_v_partition
                    where record_type not in (2, 3)
                    and product_type != 18
                    and import_flag = 0
                    and is_deleted = 0
                    and chain_id=#{param.chainId}
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and copywriter_id is not null and copywriter_id != ''
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        and product_compose_type in (0, 2, 3)
                        and goods_fee_type in (0, 2)
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
                    </if>
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                        and ${param.arrearsCommissionTimingSql}
                    </if>
                    <include refid="conditionForDepId"/>
                    <if test="param.hospitalFee1 != null and param.hospitalFee2 != null">
                        and (${param.hospitalFee1} or ${param.hospitalFee2})
                    </if>
                    <if test="param.hospitalFee1 != null and param.hospitalFee2 == null">
                        and ${param.hospitalFee1}
                    </if>
                    <if test="param.hospitalFee2 != null and param.hospitalFee1 == null">
                        and ${param.hospitalFee2}
                    </if>
                    <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                        and ${param.feeTypeIdSql}
                    </if>
                    <if test="param.productId != null and param.productId != ''">
                        and product_id = #{param.productId}
                    </if>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                    </if>
                    <include refid="copywriterWhereSql"/>
                    <if test='param.employeeSql != null and param.employeeSql != ""'>
                        ${param.employeeSql}
                    </if>
                    <if test="param.includeReg != null and param.includeReg != 1">
                        and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                    </if>
                    group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
                </if>
            ) a
            full OUTER JOIN
            (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="dispensingGoodsSelectSql"/>
                    0 as isWriter,
                    null as feeTypeProductId,
                    <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                        product_id as goodsId,
                        fee_type_id as feeTypeId,
                        classify_level_1_id as classifyLevel1,
                        if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                        if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                    </if>
                    <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                        if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                        if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                        if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                        if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                        if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                    </if>
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                    if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                from ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and form_type = 0
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.productId != null and param.productId != ''">
                    and product_id = #{param.productId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
                </if>
                <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                    ${param.dispensingEmployeeSql}
                </if>
                <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
                    and copywriter_id = #{param.copyWriterId}
                </if>
                <if test='param.copyWriterId == "00000000000000000000000000000000"'>
                    and (copywriter_id is null or copywriter_id = '')
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, classifyLevel1, classifyLevel2, unit
                <if test="param.includeWriter == 1">
                    union all
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        copywriter_id as employeeId,
                        '' as employeeName,
                        if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000') as departmentId,
                        1 as isWriter,
                        null as feeTypeProductId,
                        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                            product_id as goodsId,
                            fee_type_id as feeTypeId,
                            classify_level_1_id as classifyLevel1,
                            if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                            if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                        </if>
                        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                            if (product_compose_type = 2, compose_parent_product_id, product_id) as goodsId,
                            if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
                            if (product_compose_type = 2, '11-1', classify_level_1_id) as classifyLevel1,
                            if (product_compose_type = 2, 0, if(classify_level_2_id is null,0,classify_level_2_id)) as classifyLevel2,
                            if (product_compose_type = 2, '次', if(calc_unit = '' or calc_unit is null, '次', calc_unit)) as unit,
                        </if>
                        sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                        if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                    from ${env}.dwd_dispensing_log_v_partition
                    where chain_id=#{param.chainId}
                    and form_type = 0
                    and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and copywriter_id is not null and copywriter_id != ''
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <if test="param.productId != null and param.productId != ''">
                        and product_id = #{param.productId}
                    </if>
                    <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                        and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
                    </if>
                    <if test='param.departmentId == "00000000000000000000000000000000"'>
                        and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                    </if>
                    <if test="param.fee1 != null and param.fee2 != null">
                        and (${param.fee1} or ${param.fee2})
                    </if>
                    <if test="param.fee1 != null and param.fee2 == null">
                        and ${param.fee1}
                    </if>
                    <if test="param.fee2 != null and param.fee1 == null">
                        and ${param.fee2}
                    </if>
                    <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                        and ${param.feeTypeIdSql}
                    </if>
                    <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                        and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
                    </if>
                    <if test='param.employeeId == "00000000000000000000000000000000"'>
                        and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
                    </if>
                    <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                        ${param.dispensingEmployeeSql}
                    </if>
                    <include refid="copywriterWhereSql"/>
                    group by chainId, clinicId, employeeId, employeeName, departmentId, feeTypeId, feeTypeProductId, goodsId, unit, classifyLevel1, classifyLevel2
                </if>
            ) b
            on a.chainId=b.chainId and a.clinicId=b.clinicId and a.employeeId=b.employeeId and a.employeeName=b.employeeName and a.departmentId = b.departmentId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit
            and a.goodsId=b.goodsId and a.feeTypeId=b.feeTypeId
        ) e
    </select>

    <select id="selectPharmacyGoods" resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeGoodsEntity">
        select
            if(a.chainId is null, b.chainId, a.chainId) as chainId,
            if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
            if(a.employeeId is null, b.employeeId, a.employeeId) as personnelId,
            if(a.employeeName is null, b.employeeName, a.employeeName) as employeeName,
            if(a.departmentId is null, b.departmentId, a.departmentId) as departmentId,
            if(a.isWriter is null, b.isWriter, a.isWriter) as isWriter,
            if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
            if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
            if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
            if(a.unit is null, b.unit, a.unit) as unit,
            if(a.profitCategoryType is null, b.profitCategoryType, a.profitCategoryType) as profitCategoryType,
            if(a.unitCount is null, 0.0, a.unitCount) as count,
            if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as cost,
            if(a.costPrice is null, 2, 0 ) + if(b.hoverCode is null, 0, b.hoverCode) as hoverCode,
            if(a.receivedPrice is null, 0.0, a.receivedPrice) as amount,
            if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
            if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                0 as isWriter,
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                profit_category_type as profitCategoryType,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice,
                sum(calc_count) as unitCount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where record_type not in (2, 3)
            and product_type != 18
            and chain_id=#{param.chainId}
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="param.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
            </if>
            <if test="param.includeReg != null and param.includeReg != 1">
                <choose>
                    <when test="param.hisType != 100">
                        and product_type!=5
                    </when>
                    <otherwise>
                        and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                    </otherwise>
                </choose>
            </if>
            <if test="param.profitCategoryTypeIdSql != null and param.profitCategoryTypeIdSql != ''">
                and ${param.profitCategoryTypeIdSql}
            </if>
            group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit, profitCategoryType
        ) a
        full OUTER JOIN
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, if(seller_id is not null and seller_id != '',COALESCE(employee_snaps ->> 'sellerName', ''), ''), if(seller_id is not null and seller_id != '', COALESCE(employee_snaps ->> 'sellerName', ''), COALESCE(employee_snaps ->> 'doctorName', ''))) as employeeName,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                0 as isWriter,
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                profit_category_type as profitCategoryType,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test="param.profitCategoryTypeIdSql != null and param.profitCategoryTypeIdSql != ''">
                and ${param.profitCategoryTypeIdSql}
            </if>
            GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit, profitCategoryType
        ) b
        on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeName=b.employeeName and a.departmentId = b.departmentId and a.isWriter=b.isWriter and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit and a.profitCategoryType IS NOT DISTINCT FROM b.profitCategoryType
        order by chainId, clinicId, personnelId, isWriter,goodsId
        <if test="param.size != null and param.size != 0">
            limit #{param.size}
            offset #{param.offset}
        </if>
    </select>

    <select id="selectPharmacyGoodsTotal"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeTotalEntity">
        select
            count(1) as count,
            sum(originPrice) as originPrice,
            sum(deductPrice) as deductPrice,
            sum(receivedPrice) as amount
        from
        (
        select
            if(a.chainId is null, b.chainId, a.chainId) as chainId,
            if(a.clinicId is null, b.clinicId, a.clinicId) as clinicId,
            if(a.employeeId is null, b.employeeId, a.employeeId) as personnelId,
            if(a.employeeName is null, b.employeeName, a.employeeName) as employeeName,
            if(a.departmentId is null, b.departmentId, a.departmentId) as departmentId,
            if(a.isWriter is null, b.isWriter, a.isWriter) as isWriter,
            if(a.goodsId is null, b.goodsId, a.goodsId) as goodsId,
            if(a.classifyLevel1 is null, b.classifyLevel1, a.classifyLevel1) as classifyLevel1,
            if(a.classifyLevel2 is null, b.classifyLevel2, a.classifyLevel2) as classifyLevel2,
            if(a.unit is null, b.unit, a.unit) as unit,
            if(a.profitCategoryType is null, b.profitCategoryType, a.profitCategoryType) as profitCategoryType,
            if(a.unitCount is null, 0.0, a.unitCount) as count,
            if(a.costPrice is null,0.0,a.costPrice) + if(b.costPrice is null,0.0,b.costPrice) as cost,
            if(a.costPrice is null, 2, 0 ) + if(b.hoverCode is null, 0, b.hoverCode) as hoverCode,
            if(a.receivedPrice is null, 0.0, a.receivedPrice) as receivedPrice,
            if(a.originPrice is null, 0.0, a.originPrice) as originPrice,
            if(a.deductPrice is null, 0.0, a.deductPrice) as deductPrice
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as employeeId,
                if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                0 as isWriter,
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                if(product_id = '00000000000000000000000000000001' or calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                profit_category_type as profitCategoryType,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice,
                sum(calc_count) as unitCount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where record_type not in (2, 3)
            and product_type != 18
            and chain_id=#{param.chainId}
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="param.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
            </if>
            <if test="param.includeReg != null and param.includeReg != 1">
                <choose>
                    <when test="param.hisType != 100">
                        and product_type!=5
                    </when>
                    <otherwise>
                        and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                    </otherwise>
                </choose>
            </if>
            <if test="param.profitCategoryTypeIdSql != null and param.profitCategoryTypeIdSql != ''">
                and ${param.profitCategoryTypeIdSql}
            </if>
            group by chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit, profitCategoryType
        ) a
        full OUTER JOIN
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
                if(transcribe_doctor_id=doctor_id, if(seller_id is not null and seller_id != '',COALESCE(employee_snaps ->> 'sellerName', ''), ''), if(seller_id is not null and seller_id != '', COALESCE(employee_snaps ->> 'sellerName', ''), COALESCE(employee_snaps ->> 'doctorName', ''))) as employeeName,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                0 as isWriter,
                product_id as goodsId,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                if(calc_unit = '' or calc_unit is null, '次', calc_unit) as unit,
                profit_category_type as profitCategoryType,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test="param.profitCategoryTypeIdSql != null and param.profitCategoryTypeIdSql != ''">
                and ${param.profitCategoryTypeIdSql}
            </if>
            GROUP BY chainId, clinicId, employeeId, employeeName, departmentId, goodsId, classifyLevel1, classifyLevel2, unit, profitCategoryType
        ) b
        on a.chainId=b.chainId and b.clinicId=a.clinicId and a.employeeId=b.employeeId and a.employeeName=b.employeeName and a.departmentId = b.departmentId
        and a.isWriter=b.isWriter and a.goodsId=b.goodsId and a.classifyLevel1=b.classifyLevel1 and a.classifyLevel2=b.classifyLevel2 and a.unit=b.unit and a.profitCategoryType IS NOT DISTINCT FROM b.profitCategoryType
    ) c
    </select>


    <select id="selectTransaction"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeTransactionEntity">
        /*+MAX_WHERE_ITEMS_COUNT =512*/
        SELECT
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as personnelId,
            if(doctor_id is not null and doctor_id != '', COALESCE(employee_snaps ->> 'doctorName', ''), COALESCE(employee_snaps ->> 'sellerName', '')) as employeeName,
            if(copywriter_id is null, '-', copywriter_id) as copyWriterId,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            patient_id as patientId,
            v2_patient_order_id as patientOrderId,
            charge_sheet_type as chargeSheetType,
            type,
            record_source_type as recordSourceType,
            v2_transaction_id as transId,
            pay_type as payMode1,
            pay_sub_type as payMode2,
            if(visit_source_level_1_id='0', null, visit_source_level_1_id) as visitSourceId1,
            if(visit_source_level_2_id='0', null, visit_source_level_2_id) as visitSourceId2,
            visit_source_from_type as visitSourceFromType,
            visit_source_from as visitSourceFrom,
            visit_source_remark as visitSourceRemark,
            if(patient_source_id_1='0', null, patient_source_id_1) as patientSourceId1,
            if(patient_source_id_2='0', null, patient_source_id_2) as patientSourceId2,
            patient_source_from_type as patientSourceFromType,
            patient_source_form as patientSourceFrom,
            max(create_time) as created,
            sum(if(type=-1, -received_price, received_price)) as amount,
            sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price > 0, record_adjustment_price, 0.0)-if(record_source_type=2,(discount_price-if(adjustment_price>0,adjustment_price,0.0)),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice,
            referral_doctor_id as referralDoctorId
        FROM
        ${env}.dwd_charge_transaction_record_v_partition
        where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        and record_type not in (2, 3)
        and import_flag = 0
        and chain_id = #{param.chainId}
        and product_type != 18
        and is_deleted = 0
        <if test="param.hisType == 100">
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type in (0,1)
                and goods_fee_type in (0,2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
        </if>
        <if test="param.hisType != 100">
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
        </if>
        and record_type not in (2, 3)
        and import_flag = 0
        and is_deleted = 0
        and chain_id = #{param.chainId}
        <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
           and clinic_id=#{param.clinicId}
        </if>
        <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 1">
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and (department_id is null or department_id = '')
                and (seller_department_id is null or seller_department_id = '')
            </if>
        </if>
        <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 4">
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and department_id = #{param.departmentId}
                and copywriter_id is not null
                and copywriter_id != ''
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and (department_id is null or department_id = '')
                and copywriter_id is not null
                and copywriter_id != ''
            </if>
        </if>
        <if test="param.payModeSql != null and param.payModeSql != ''">
            and ${param.payModeSql}
        </if>
        <if test="param.fee1 != null and param.fee2 != null">
            and (${param.fee1} or ${param.fee2})
        </if>
        <if test="param.fee1 != null and param.fee2 == null">
            and ${param.fee1}
            and ${param.fee1}
        </if>
        <if test="param.fee2 != null and param.fee1 == null">
            and ${param.fee2}
        </if>
        <if test="param.visitSource1 != null and param.visitSource2 != null">
            and (${param.visitSource1} or ${param.visitSource2})
        </if>
        <if test="param.visitSource1 != null and param.visitSource2 == null">
            and ${param.visitSource1}
        </if>
        <if test="param.visitSource2 != null and param.visitSource1 == null">
            and ${param.visitSource2}
        </if>
        <if test='param.employeeSql != null and param.employeeSql != ""'>
            ${param.employeeSql}
        </if>
        <include refid="employeeWhereSql"/>
        <if test="param.includeReg != 1">
            and product_type!=5
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0 and param.includeWriter == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        GROUP BY chainId, clinicId, personnelId,employeeName,copyWriterId,patientId,departmentId,patientOrderId,chargeSheetType,type,transId,payMode1,payMode2,visitSourceId1,visitSourceId2,visitSourceFromType,visitSourceFrom,visitSourceRemark,patientSourceId1,patientSourceId2,referralDoctorId,recordSourceType,patientSourceId1,patientSourceId2,patientSourceFromType,patientSourceFrom
        order by created desc
        <if test="param.size != null and param.size != 0">
            limit #{param.size}
            offset #{param.offset}
        </if>
    </select>

    <select id="selectTransactionTotal"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeTotalEntity">
        /*+MAX_WHERE_ITEMS_COUNT =512*/
        select
            count(1) as count,
            sum(originPrice) as originPrice,
            sum(deductPrice) as deductPrice,
            sum(amount) as amount
        from (
            SELECT
                v2_transaction_id as transId,
                sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1,1,-1) * deduct_promotion_price) as deductPrice,
                sum(if(type=-1, -received_price, received_price)) as amount
            FROM
            ${env}.dwd_charge_transaction_record_v_partition
            where create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and record_type not in (2, 3)
            and import_flag = 0
            and is_deleted = 0
            and chain_id = #{param.chainId}
            and product_type != 18
            and is_deleted = 0
            <if test="param.hisType == 100">
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                    and product_compose_type in (0,1)
                    and goods_fee_type in (0,2)
                </if>
                <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
                    and product_compose_type in (0,2,3)
                    and goods_fee_type in (0,2)
                </if>
            </if>
            <if test="param.hisType != 100">
                <if test="param.composeSql != null and param.composeSql != ''">
                    and ${param.composeSql}
                </if>
            </if>
            <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 1">
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and (department_id is null or department_id = '')
                    and (seller_department_id is null or seller_department_id = '')
                </if>
            </if>
            <if test="param.employeeTypeNumber != null and param.employeeTypeNumber == 4">
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and department_id = #{param.departmentId}
                    and copywriter_id is not null
                    and copywriter_id != ''
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and (department_id is null or department_id = '')
                    and copywriter_id is not null
                    and copywriter_id != ''
                </if>
            </if>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.visitSource1 != null and param.visitSource2 != null">
                and (${param.visitSource1} or ${param.visitSource2})
            </if>
            <if test="param.visitSource1 != null and param.visitSource2 == null">
                and ${param.visitSource1}
            </if>
            <if test="param.visitSource2 != null and param.visitSource1 == null">
                and ${param.visitSource2}
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <include refid="employeeWhereSql"/>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0 and param.includeWriter == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by v2_transaction_id
            ) a
    </select>

    <select id="selectTransactionClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeTransactionClassifyEntity">
        SELECT
            v2_patient_order_id as patientOrderId,
            v2_transaction_id as transactionId,
            classify_level_1_id as classifyLevel1,
            <if test="param.level == 2">
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
            </if>
            sum(if(type=-1, -received_price, received_price)) as amount,
            sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as origin,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
        FROM
            ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{param.chainId}
        and import_flag = 0
        and product_type not in (18)
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        <if test="param.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.includeReg != 1">
            and product_type!=5
        </if>
        <if test="param.transIds != null and param.transIds!= ''">
            and ${param.transIds}
        </if>
        <if test="param.feeType1 != null and param.feeType2 != null">
            and (${param.feeType1} or ${param.feeType2})
        </if>
        <if test="param.feeType1 != null and param.feeType2 == null">
            and ${param.feeType1}
        </if>
        <if test="param.feeType2 != null and param.feeType1 == null">
            and ${param.feeType2}
        </if>
        <if test="param.level == 2">
            GROUP BY patientOrderId,transactionId,classifyLevel1,classifyLevel2
        </if>
        <if test="param.level != 2">
            GROUP BY patientOrderId,transactionId,classifyLevel1
        </if>
    </select>

    <select id="selectAdviceTransactionFeeClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeTransactionClassifyEntity">
        SELECT
            v2_patient_order_id as patientOrderId,
            v2_transaction_id as transactionId,
            fee_type_id as feeTypeId,
            sum(if(type=-1, -received_price, received_price)) as amount,
            sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as origin,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
        FROM
        ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{param.chainId}
        and import_flag = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and product_type != 18
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and product_compose_type in (0,1)
            and goods_fee_type in (0,2)
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 1">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.includeReg != null and param.includeReg != 1">
            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
        </if>
        GROUP BY transactionId,feeTypeId,patientOrderId
    </select>

    <select id="selectFeeFirstClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity">
        select distinct
            if(classify_level_1_id is null, '0', classify_level_1_id) as classifyLevel1Id
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{param.chainId}
        and import_flag = 0
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <include refid="conditionForDepId"/>
        <if test="param.feeType1 != null and param.feeType2 != null">
            and (${param.feeType1} or ${param.feeType2})
        </if>
        <if test="param.feeType1 != null and param.feeType2 == null">
            and ${param.feeType1}
        </if>
        <if test="param.feeType2 != null and param.feeType1 == null">
            and ${param.feeType2}
        </if>
        <if test='param.employeeSql != null and param.employeeSql != ""'>
            ${param.employeeSql}
        </if>
        <include refid="employeeWhereSql"/>
        <if test="param.includeReg != null and param.includeReg != 1">
            <choose>
                <when test="param.hisType != 100">
                    and product_type!=5
                </when>
                <otherwise>
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </otherwise>
            </choose>
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0 and param.includeWriter == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 1'>
            <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
        </if>
        order by classifyLevel1Id
    </select>

    <select id="selectAchievementFeeFirstClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity">
        select distinct
        <if test="param.hisType == 100">
            if(product_type not in (1,2,7,11), if(compose_parent_classify_level_1_id is null, '0', compose_parent_classify_level_1_id), if(classify_level_1_id is null, '0', classify_level_1_id)) as classifyLevel1Id
        </if>
        <if test="param.hisType != 100">
            if(classify_level_1_id is null, '0', classify_level_1_id) as classifyLevel1Id
        </if>
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{param.chainId}
        and import_flag = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        <if test="param.hisType == 100">
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                and product_compose_type in (0, 2, 3)
                and goods_fee_type in (0, 2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
            </if>
        </if>
        <if test="param.hisType != 100">
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.includeReg != null and param.includeReg != 1">
            and product_type!=5
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0 and param.includeWriter == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
    </select>

    <select id="selectFeeSecondClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity">
        select distinct
            if(classify_level_1_id is null,'0',classify_level_1_id) as classifyLevel1Id,
            if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2Id
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{param.chainId}
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and import_flag = 0
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <include refid="conditionForDepId"/>
        <if test="param.feeType1 != null and param.feeType2 != null">
            and (${param.feeType1} or ${param.feeType2})
        </if>
        <if test="param.feeType1 != null and param.feeType2 == null">
            and ${param.feeType1}
        </if>
        <if test="param.feeType2 != null and param.feeType1 == null">
            and ${param.feeType2}
        </if>
        <if test='param.employeeSql != null and param.employeeSql != ""'>
            ${param.employeeSql}
        </if>
        <include refid="employeeWhereSql"/>
        <if test="param.includeReg != null and param.includeReg != 1">
            and product_type!=5
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0 and param.includeWriter == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 1'>
            <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
        </if>
        order by classifyLevel1Id, classifyLevel2Id
    </select>

    <select id="selectAchievementFeeSecondClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity">
        select distinct
        <if test="param.hisType == 100">
            if(product_type not in (1,2,7,11), if(compose_parent_classify_level_1_id is null, '0', compose_parent_classify_level_1_id), if(classify_level_1_id is null, '0', classify_level_1_id)) as classifyLevel1Id,
            if(product_type not in (1,2,7,11), if(compose_parent_classify_level_2_id is null,0, compose_parent_classify_level_2_id), if(classify_level_2_id is null, 0, classify_level_2_id)) as classifyLevel2Id
        </if>
        <if test="param.hisType != 100">
            if(classify_level_1_id is null,'0',classify_level_1_id) as classifyLevel1Id,
            if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2Id
        </if>
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{param.chainId}
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and import_flag = 0
        <if test="param.hisType == 100">
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                and product_compose_type in (0, 2, 3)
                and goods_fee_type in (0, 2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and ((product_compose_type = 0 and goods_fee_type in (0, 2)) or (product_compose_type in (1, 2)))
            </if>
        </if>
        <if test="param.hisType != 100">
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.includeReg != null and param.includeReg != 1">
            and product_type!=5
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0 and param.includeWriter == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
    </select>

    <select id="selectDispensingFeeFirstClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity">
        select
            distinct classifyLevel1Id
        from
        (
        select
        if(classify_level_1_id is null, '0', classify_level_1_id) as classifyLevel1Id
        from ${env}.dwd_dispensing_log_v_partition
        where chain_id = #{param.chainId}
        and form_type = 0
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and compose_parent_product_id is null
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.feeType1 != null and param.feeType2 != null">
            and (${param.feeType1} or ${param.feeType2})
        </if>
        <if test="param.feeType1 != null and param.feeType2 == null">
            and ${param.feeType1}
        </if>
        <if test="param.feeType2 != null and param.feeType1 == null">
            and ${param.feeType2}
        </if>
        <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
        </if>
        <if test='param.employeeId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
        </if>
        <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
            ${param.dispensingEmployeeSql}
        </if>
        <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
            and copywriter_id = #{param.copyWriterId}
        </if>
        <if test='param.copyWriterId == "00000000000000000000000000000000"'>
            and (copywriter_id is null or copywriter_id = '')
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <!-- 套餐不拆分时需要加上套餐分类 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            <if test="param.feeType1 != null and param.feeType1.contains('11-1')">
                union all
                select '11-1' as classifyLevel1Id
            </if>
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
            if(classify_level_1_id is null, '0', classify_level_1_id) as classifyLevel1Id
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and copywriter_id is not null and copywriter_id != ''
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and compose_parent_product_id is null
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.feeType1 != null and param.feeType2 != null">
                and (${param.feeType1} or ${param.feeType2})
            </if>
            <if test="param.feeType1 != null and param.feeType2 == null">
                and ${param.feeType1}
            </if>
            <if test="param.feeType2 != null and param.feeType1 == null">
                and ${param.feeType2}
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <include refid="copywriterWhereSql"/>
        </if>
        ) a
        order by classifyLevel1Id
    </select>

    <select id="selectDispensingFeeSecondClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity">
        select
            distinct classifyLevel1Id, classifyLevel2Id
        from
        (
        select
            if(classify_level_1_id is null, '0', classify_level_1_id) as classifyLevel1Id,
            if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2Id
        from ${env}.dwd_dispensing_log_v_partition
        where chain_id = #{param.chainId}
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and form_type = 0
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and compose_parent_product_id is null
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.feeType1 != null and param.feeType2 != null">
            and (${param.feeType1} or ${param.feeType2})
        </if>
        <if test="param.feeType1 != null and param.feeType2 == null">
            and ${param.feeType1}
        </if>
        <if test="param.feeType2 != null and param.feeType1 == null">
            and ${param.feeType2}
        </if>
        <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
        </if>
        <if test='param.employeeId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
        </if>
        <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
            ${param.dispensingEmployeeSql}
        </if>
        <if test='param.copyWriterId != null and param.copyWriterId != "" and param.copyWriterId != "00000000000000000000000000000000"'>
            and copywriter_id = #{param.copyWriterId}
        </if>
        <if test='param.copyWriterId == "00000000000000000000000000000000"'>
            and (copywriter_id is null or copywriter_id = '')
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <!-- 套餐不拆分时套餐的成本 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            <!-- 套餐不拆分时需要加上套餐分类 -->
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                <if test="param.feeType1 == null and param.feeType2 == null">
                    union all
                    select '11-1' as classifyLevel1Id, 0 as classifyLevel2Id
                </if>
                <if test="param.feeType2 != null and param.feeType2.contains('11-1/0')">
                    union all
                    select '11-1' as classifyLevel1Id, 0 as classifyLevel2Id
                </if>
            </if>
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                if(classify_level_1_id is null, '0', classify_level_1_id) as classifyLevel1Id,
                if(classify_level_2_id is null, 0, classify_level_2_id) as classifyLevel2Id
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and copywriter_id is not null and copywriter_id != ''
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and compose_parent_product_id is null
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.feeType1 != null and param.feeType2 != null">
                and (${param.feeType1} or ${param.feeType2})
            </if>
            <if test="param.feeType1 != null and param.feeType2 == null">
                and ${param.feeType1}
            </if>
            <if test="param.feeType2 != null and param.feeType1 == null">
                and ${param.feeType2}
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
            </if>
            <include refid="copywriterWhereSql"/>
        </if>
        ) a
        order by classifyLevel1Id, classifyLevel2Id
    </select>

    <select id="selectPersonalWestPrescription"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalPrescriptionEntity">
        select
            chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType,
            sum(if(amount>0, 1, if(amount &lt; 0, -1, 0))) as count
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="chargeEmployeeSelectSql"/>
                0 as isCopywriter,
                2 as prescriptionType,
                v2_charge_form_id as chargeFormId,
                sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type!=11
            and charge_sheet_type not in (3,6,8,12,16,18)
            and record_type not in (2, 3)
            and source_form_type in (4,5)
            and import_flag = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <include refid="employeeWhereSql"/>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType, chargeFormId
            <if test="param.includeWriter == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    copywriter_id as personnelId,
                    '' as employeeName,
                    1 as isCopywriter,
                    2 as prescriptionType,
                    v2_charge_form_id as chargeFormId,
                    sum(if(type=-1, -1, 1) * received_price) as amount
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and product_type!=11
                and charge_sheet_type not in (3,6,8,12,16,18)
                and record_type not in (2, 3)
                and source_form_type in (4,5)
                and import_flag = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                    and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
                </if>
                <if test='param.employeeId == "00000000000000000000000000000000"'>
                    and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
                </if>
                <include refid="copywriterWhereSql"/>
                group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType, chargeFormId
            </if>
        )a
        group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType
    </select>

    <select id="selectOpticianPrescription"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalPrescriptionEntity">
        select
        chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType,
        sum(if(amount>0, 1, if(amount &lt; 0, -1, 0)) * num) as count
        from
        (
        select
        chain_id as chainId,
        clinic_id as clinicId,
        <include refid="chargeEmployeeSelectSql"/>
        0 as isCopywriter,
        24 as prescriptionType,
        cast(extend_prescription ->> 'eyeglassPrescriptionCount' as NUMERIC)  as num,
        sum(if(type=-1, -1, 1) * received_price) as amount
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and product_type!=11
        and charge_sheet_type not in (3,6,8,12,16,18)
        and record_type not in (2, 3)
        and extend_prescription is not null
        and import_flag = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test='param.employeeSql != null and param.employeeSql != ""'>
            ${param.employeeSql}
        </if>
        <include refid="employeeWhereSql"/>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType, v2_charge_sheet_id, num
        <if test="param.includeWriter == 1">
            union all
            select
            chain_id as chainId,
            clinic_id as clinicId,
            copywriter_id as personnelId,
            '' as employeeName,
            1 as isCopywriter,
            24 as prescriptionType,
            cast(extend_prescription ->> 'eyeglassPrescriptionCount' as NUMERIC) as num,
            sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type!=11
            and charge_sheet_type not in (3,6,8,12,16,18)
            and record_type not in (2, 3)
            and extend_prescription is not null
            and import_flag = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and copywriter_id is not null and copywriter_id != ''
            <if test="param.hisType != 100 and param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            <if test='param.employeeId != null and param.employeeId != "00000000000000000000000000000000"'>
                and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
            </if>
            <if test='param.employeeId == "00000000000000000000000000000000"'>
                and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
            </if>
            <include refid="copywriterWhereSql"/>
            group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType, v2_charge_sheet_id, num
        </if>
        )a
        group by chainId, clinicId, personnelId, employeeName, isCopywriter, prescriptionType
    </select>

    <sql id="departmentSelectSql">
        <if test="param.hisType != null and param.hisType != ''">
            <if test="param.hisType == 1">
                <choose>
                    <when test="param.employeeTypeNumber != null and param.employeeTypeNumber == 2">
                        if(case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_department_id else department_id end is not null
                        and case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_department_id else department_id end != '',
                        case when retail_type = 2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19) then item_doctor_department_id else department_id end,'00000000000000000000000000000000') as departmentId,
                    </when>
                    <otherwise>
                        if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                    </otherwise>
                </choose>
            </if>
            <if test="param.hisType != 1">
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            </if>
        </if>
        <if test="param.hisType == null or param.hisType == ''">
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
        </if>
    </sql>

    <select id="selectDepartmentBase"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeDepartmentBaseEntity">
        select
            chainId,
            clinicId,
            departmentId,
            isCopywriter,
            patientCount,
            transFormPatientCount
        from (
            select
                chainId,
                clinicId,
                departmentId,
                isCopywriter,
                count(distinct v2_patient_order_id) as patientCount,
                count(distinct if(recordSourceType != 2 and classify_level_1_id != '5-0', v2_patient_order_id, null)) as transFormPatientCount
            from (
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="departmentSelectSql"/>
                    0 as isCopywriter,
                    date (create_time) as created,
                    if(record_source_type = 1, 0,record_source_type) as recordSourceType,
                    classify_level_1_id,
                    v2_patient_order_id
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and record_type not in (2, 3)
                and import_flag = 0
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and product_type != 18
                <if test="param.hisType == 100">
                    and product_compose_type in (0,2,3)
                    and goods_fee_type in (0,2)
                </if>
                <if test="param.hisType != 100">
                    <if test="param.composeSql != null and param.composeSql != ''">
                        and ${param.composeSql}
                    </if>
                    <if test="param.isCardOpeningFee == 0">
                        and product_type != 17
                    </if>
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <include refid="conditionForDepId"/>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                <if test="param.includeReg != null and param.includeReg != 1">
                    <choose>
                        <when test="param.hisType != 100">
                            and product_type!=5
                        </when>
                        <otherwise>
                            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                        </otherwise>
                    </choose>
                </if>
                <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                    and (copywriter_id = '' or copywriter_id is null)
                </if>
                group by chainId, clinicId, departmentId, isCopywriter,v2_patient_order_id, created, recordSourceType, classify_level_1_id
            ) a
            group by chainId, clinicId, departmentId, isCopywriter
            <if test="param.includeWriter == 1">
                union all
                select
                    chainId,
                    clinicId,
                    departmentId,
                    isCopywriter,
                    count(distinct v2_patient_order_id) as patientCount,
                    count(distinct if(recordSourceType != 2 and classify_level_1_id != '5-0', v2_patient_order_id, null)) as transFormPatientCount
                from (
                    select
                        chain_id as chainId,
                        clinic_id as clinicId,
                        <include refid="departmentSelectSql"/>
                        1 as isCopywriter,
                        date(create_time) as created,
                        if(record_source_type = 1, 0,record_source_type) as recordSourceType,
                        classify_level_1_id,
                        v2_patient_order_id
                    from
                    ${env}.dwd_charge_transaction_record_v_partition
                    where chain_id=#{param.chainId}
                    and record_type not in (2, 3)
                    and import_flag = 0
                    and is_deleted = 0
                    and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                    and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                    and copywriter_id is not null and copywriter_id != ''
                    and product_type != 18
                    <if test="param.hisType == 100">
                        and product_compose_type in (0,2,3)
                        and goods_fee_type in (0,2)
                    </if>
                    <if test="param.hisType != 100">
                        <if test="param.composeSql != null and param.composeSql != ''">
                            and ${param.composeSql}
                        </if>
                        <if test="param.isCardOpeningFee == 0">
                            and product_type != 17
                        </if>
                    </if>
                    <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                        and ${param.arrearsCommissionTimingSql}
                    </if>
                    <if test="param.clinicId != null and param.clinicId != ''">
                        and clinic_id=#{param.clinicId}
                    </if>
                    <include refid="conditionForDepId"/>
                    <if test="param.payModeSql != null and param.payModeSql != ''">
                        and ${param.payModeSql}
                    </if>
                    <if test="param.fee1 != null and param.fee2 != null">
                        and (${param.fee1} or ${param.fee2})
                    </if>
                    <if test="param.fee1 != null and param.fee2 == null">
                        and ${param.fee1}
                    </if>
                    <if test="param.fee2 != null and param.fee1 == null">
                        and ${param.fee2}
                    </if>
                    <if test="param.includeReg != null and param.includeReg != 1">
                        <choose>
                            <when test="param.hisType != 100">
                                and product_type!=5
                            </when>
                            <otherwise>
                                and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                            </otherwise>
                        </choose>
                    </if>
                    group by chainId, clinicId, departmentId, isCopywriter,v2_patient_order_id, created, recordSourceType, classify_level_1_id
                ) a
                group by chainId, clinicId, departmentId, isCopywriter
            </if>
        ) b
    </select>

    <select id="selectDepartmentTransformPatientCount"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeDepartmentBaseEntity">
        WITH base_data AS (
            SELECT
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="departmentSelectSql"/>
                0 as isCopywriter,
                patient_id,
                v2_patient_order_id,
                date(create_time) AS visit_date
            FROM ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and import_flag = 0
            and type != -1
            AND record_source_type != 2
            AND product_type!=5
            and record_type not in (2, 3)
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and is_deleted = 0
            and product_type != 18
            <if test="param.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="param.hisType != 100">
                <if test="param.composeSql != null and param.composeSql != ''">
                    and ${param.composeSql}
                </if>
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <include refid="conditionForDepId"/>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            GROUP BY chainId, clinicId, departmentId, isCopywriter, patient_id, v2_patient_order_id, date(create_time)
            <if test="param.includeWriter == 1">
                union all
                SELECT
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="departmentSelectSql"/>
                    1 as isCopywriter,
                    patient_id,
                    v2_patient_order_id,
                    date(create_time) AS visit_date
                FROM ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and import_flag = 0
                and type != -1
                AND record_source_type != 2
                AND product_type!=5
                and record_type not in (2, 3)
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                and product_type != 18
                <if test="param.hisType == 100">
                    and product_compose_type in (0,2,3)
                    and goods_fee_type in (0,2)
                </if>
                <if test="param.hisType != 100">
                    <if test="param.composeSql != null and param.composeSql != ''">
                        and ${param.composeSql}
                    </if>
                    <if test="param.isCardOpeningFee == 0">
                        and product_type != 17
                    </if>
                </if>
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <include refid="conditionForDepId"/>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                GROUP BY chainId, clinicId, departmentId, isCopywriter, patient_id, v2_patient_order_id, date(create_time)
            </if>
        ),
        -- 识别跨天的订单
        multi_day_orders AS (
            SELECT
                chainId,
                clinicId,
                departmentId,
                isCopywriter,
                patient_id,
                v2_patient_order_id,
                COUNT(DISTINCT visit_date) AS day_count
            FROM base_data
            GROUP BY chainId, clinicId, departmentId, isCopywriter, patient_id, v2_patient_order_id
            HAVING COUNT(DISTINCT visit_date) > 1
        ),
        -- 计算跨天患者的就诊次数（每个患者只算1次）
        multi_day_patient_visits AS (
            SELECT
                chainId,
                clinicId,
                departmentId,
                isCopywriter,
                COUNT(DISTINCT patient_id) AS multi_day_visit_count
            FROM multi_day_orders
            GROUP BY chainId, clinicId, departmentId, isCopywriter
        ),
        -- 计算非跨天患者的就诊次数（按天去重）
        single_day_patient_visits AS (
            SELECT
                chainId,
                clinicId,
                departmentId,
                isCopywriter,
                COUNT(1) AS single_day_visit_count
            FROM
            (
                SELECT
                    base.chainId,
                    base.clinicId,
                    base.departmentId,
                    base.isCopywriter,
                    base.patient_id,
                    base.visit_date
                FROM base_data base
                LEFT JOIN multi_day_orders m
                ON base.chainId = m.chainId
                AND base.clinicId = m.clinicId
                AND base.departmentId = m.departmentId
                AND base.isCopywriter = m.isCopywriter
                AND base.patient_id = m.patient_id
                AND base.v2_patient_order_id = m.v2_patient_order_id
                WHERE m.patient_id IS NULL -- 排除跨天患者的记录
                GROUP BY base.chainId, base.clinicId, base.departmentId, base.isCopywriter, base.patient_id, base.visit_date
            ) t
            GROUP BY chainId, clinicId, departmentId, isCopywriter
        )
        -- 合并两种结果，按开单科室和门店分组
        SELECT
            COALESCE(m.chainId, s.chainId) AS chainId,
            COALESCE(m.clinicId, s.clinicId) AS clinicId,
            COALESCE(m.departmentId, s.departmentId) AS departmentId,
            COALESCE(m.isCopywriter, s.isCopywriter) AS isCopywriter,
            COALESCE(m.multi_day_visit_count, 0) + COALESCE(s.single_day_visit_count, 0) AS transFormPatientCount
        FROM multi_day_patient_visits m
        FULL OUTER JOIN single_day_patient_visits s
        ON m.chainId = s.chainId
        AND m.clinicId = s.clinicId
        AND m.departmentId = s.departmentId
        AND m.isCopywriter = s.isCopywriter
    </select>

    <select id="selectDepartmentDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeDepartmentDispensingCost">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
            0 as isCopywriter,
            classify_level_1_id as classifyLevel1,
            if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
            if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{param.chainId}
        and form_type = 0
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and compose_parent_product_id is null
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.fee1 != null and param.fee2 != null">
            and (${param.fee1} or ${param.fee2})
        </if>
        <if test="param.fee1 != null and param.fee2 == null">
            and ${param.fee1}
        </if>
        <if test="param.fee2 != null and param.fee1 == null">
            and ${param.fee2}
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
        <!-- 套餐不拆分时通过子项查询套餐的成本 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                0 as isCopywriter,
                '11-1' as classifyLevel1,
                0 as classifyLevel2,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and compose_parent_product_id is not null
            and form_type = 0
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                1 as isCopywriter,
                classify_level_1_id as classifyLevel1,
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and copywriter_id is not null and copywriter_id != ''
            and form_type = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and compose_parent_product_id is null
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
            <!-- 套餐不拆分时通过子项查询套餐的成本 -->
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                    1 as isCopywriter,
                    classify_level_1_id as classifyLevel1,
                    if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                    if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                from
                ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
                and copywriter_id is not null and copywriter_id != ''
                and compose_parent_product_id is not null
                and form_type = 0
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
                </if>
                <if test="param.fee1 != null and param.fee2 != null">
                    and (${param.fee1} or ${param.fee2})
                </if>
                <if test="param.fee1 != null and param.fee2 == null">
                    and ${param.fee1}
                </if>
                <if test="param.fee2 != null and param.fee1 == null">
                    and ${param.fee2}
                </if>
                group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
            </if>
        </if>
    </select>

    <select id="selectAdviceDepartmentDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeDepartmentDispensingCost">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
            0 as isCopywriter,
            fee_type_id as feeTypeId,
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
            if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{param.chainId}
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and form_type = 0
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and compose_parent_product_id is null
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test="param.feeType1 != null and param.feeType2 != null">
            and (${param.feeType1} or ${param.feeType2})
        </if>
        <if test="param.feeType1 != null and param.feeType2 == null">
            and ${param.feeType1}
        </if>
        <if test="param.feeType2 != null and param.feeType1 == null">
            and ${param.feeType2}
        </if>
        <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
            and ${param.feeTypeIdSql}
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, departmentId, isCopywriter, feeTypeId

        <!-- 套餐不拆分时通过子项查询套餐的成本 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                0 as isCopywriter,
                fee_type_id as feeTypeId,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and compose_parent_product_id is not null
            and form_type = 0
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test="param.feeType1 != null and param.feeType2 != null">
                and (${param.feeType1} or ${param.feeType2})
            </if>
            <if test="param.feeType1 != null and param.feeType2 == null">
                and ${param.feeType1}
            </if>
            <if test="param.feeType2 != null and param.feeType1 == null">
                and ${param.feeType2}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, departmentId, isCopywriter, feeTypeId
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                1 as isCopywriter,
                fee_type_id as feeTypeId,
                sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            and copywriter_id is not null and copywriter_id != ''
            and form_type = 0
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and compose_parent_product_id is null
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
            </if>
            <if test="param.feeType1 != null and param.feeType2 != null">
                and (${param.feeType1} or ${param.feeType2})
            </if>
            <if test="param.feeType1 != null and param.feeType2 == null">
                and ${param.feeType1}
            </if>
            <if test="param.feeType2 != null and param.feeType1 == null">
                and ${param.feeType2}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            group by chainId, clinicId, departmentId, isCopywriter, feeTypeId
            <!-- 套餐不拆分时通过子项查询套餐的成本 -->
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                    1 as isCopywriter,
                    fee_type_id as feeTypeId,
                    sum(if(type in (4,6), -cost_price, cost_price)) as costPrice,
                    if(sum(if(type in(5,6), 1, 0)) > 0, 1, 0) as hoverCode
                from
                ${env}.dwd_dispensing_log_v_partition
                where chain_id=#{param.chainId}
                and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
                and copywriter_id is not null and copywriter_id != ''
                and compose_parent_product_id is not null
                and form_type = 0
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = ''))
                </if>
                <if test="param.feeType1 != null and param.feeType2 != null">
                    and (${param.feeType1} or ${param.feeType2})
                </if>
                <if test="param.feeType1 != null and param.feeType2 == null">
                    and ${param.feeType1}
                </if>
                <if test="param.feeType2 != null and param.feeType1 == null">
                    and ${param.feeType2}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                group by chainId, clinicId, departmentId, isCopywriter, feeTypeId
            </if>
        </if>
    </select>


    <select id="selectDepartmentAmount"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeDepartmentFeeAmountEntity">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        <include refid="departmentSelectSql"/>
        0 as isCopywriter,
        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
        sum(if(type = -1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
        sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deductPrice
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and record_type not in (2, 3)
        and import_flag = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        and product_type != 18
        and is_deleted = 0
        <if test="param.hisType == 100">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="param.hisType != 100">
            and product_compose_type in (0,2)
            and goods_fee_type in (0,1)
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <include refid="conditionForDepId"/>
        <if test="param.payModeSql != null and param.payModeSql != ''">
            and ${param.payModeSql}
        </if>
        <if test="param.fee1 != null and param.fee2 != null">
            and (${param.fee1} or ${param.fee2})
        </if>
        <if test="param.fee1 != null and param.fee2 == null">
            and ${param.fee1}
        </if>
        <if test="param.fee2 != null and param.fee1 == null">
            and ${param.fee2}
        </if>
        <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
            and ${param.feeTypeIdSql}
        </if>
        <if test="param.includeReg != null and param.includeReg != 1">
            <choose>
                <when test="param.hisType != 100">
                    and product_type!=5
                </when>
                <otherwise>
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </otherwise>
            </choose>
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, departmentId, isCopywriter
        <if test="param.includeWriter == 1">
            union all
            select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="departmentSelectSql"/>
            1 as isCopywriter,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and record_type not in (2, 3)
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and copywriter_id is not null and copywriter_id != ''
            and product_type != 18
            <if test="param.hisType == 100">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="param.hisType != 100">
                and product_compose_type in (0,2)
                and goods_fee_type in (0,1)
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <include refid="conditionForDepId"/>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.fee1 != null and param.fee2 != null">
                and (${param.fee1} or ${param.fee2})
            </if>
            <if test="param.fee1 != null and param.fee2 == null">
                and ${param.fee1}
            </if>
            <if test="param.fee2 != null and param.fee1 == null">
                and ${param.fee2}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test="param.includeReg != null and param.includeReg != 1">
                <choose>
                    <when test="param.hisType != 100">
                        and product_type!=5
                    </when>
                    <otherwise>
                        and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                    </otherwise>
                </choose>
            </if>
            group by chainId, clinicId, departmentId, isCopywriter
        </if>
    </select>

    <select id="selectDepartmentFeeClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeDepartmentFeeAmountEntity">
        select
        chain_id as chainId,
        clinic_id as clinicId,
        <include refid="departmentSelectSql"/>
        0 as isCopywriter,
        classify_level_1_id as classifyLevel1,
        <if test="level == 2">
            if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
        </if>
        sum(if(type=-1, -received_price, received_price)) as receivedPrice,
        sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
        sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
        sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and product_type not in (11, 18)
        and record_type not in (2, 3)
        and import_flag = 0
        and is_deleted = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.composeSql != null and param.composeSql != ''">
            and ${param.composeSql}
        </if>
        <if test="param.isCardOpeningFee == 0">
            and product_type != 17
        </if>
        <if test="param.arrearsCommissionTiming != 1">
            and record_source_type != 1
        </if>
        <if test="param.arrearsCommissionTiming != 2">
            and record_source_type != 2
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <include refid="conditionForDepId"/>
        <if test="param.payModeSql != null and param.payModeSql != ''">
            and ${param.payModeSql}
        </if>
        <if test="fee1 != null and fee2 != null">
            and (${fee1} or ${fee2})
        </if>
        <if test="fee1 != null and fee2 == null">
            and ${fee1}
        </if>
        <if test="fee2 != null and fee1 == null">
            and ${fee2}
        </if>
        <if test="param.includeReg != 1">
            and product_type!=5
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <if test="level == 2">
            group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
        </if>
        <if test="level != 2">
            group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1
        </if>
        <!-- 选择套餐之后就只能通过套餐子项来计算非发药的成本 -->
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
        union all
            select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="departmentSelectSql"/>
            0 as isCopywriter,
            '11-1' as classifyLevel1,
            <if test="level == 2">
                0 as classifyLevel2,
            </if>
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type != 18
            and product_compose_type = 2
            and record_type not in (2, 3)
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="param.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="fee1 != null and fee2 != null">
                and (${fee1} or ${fee2})
            </if>
            <if test="fee1 != null and fee2 == null">
                and ${fee1}
            </if>
            <if test="fee2 != null and fee1 == null">
                and ${fee2}
            </if>
            <include refid="conditionForDepId"/>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            <if test="level == 2">
                group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
            </if>
            <if test="level != 2">
                group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1
            </if>
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="departmentSelectSql"/>
            1 as isCopywriter,
            classify_level_1_id as classifyLevel1,
            <if test="level == 2">
                if(classify_level_2_id is null,0,classify_level_2_id) as classifyLevel2,
            </if>
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type not in (11, 18)
            and record_type not in (2, 3)
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            and copywriter_id is not null and copywriter_id != ''
            <if test="param.composeSql != null and param.composeSql != ''">
                and ${param.composeSql}
            </if>
            <if test="param.isCardOpeningFee == 0">
                and product_type != 17
            </if>
            <if test="param.arrearsCommissionTiming != 1">
                and record_source_type != 1
            </if>
            <if test="param.arrearsCommissionTiming != 2">
                and record_source_type != 2
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <include refid="conditionForDepId"/>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="fee1 != null and fee2 != null">
                and (${fee1} or ${fee2})
            </if>
            <if test="fee1 != null and fee2 == null">
                and ${fee1}
            </if>
            <if test="fee2 != null and fee1 == null">
                and ${fee2}
            </if>
            <if test="param.includeReg != 1">
                and product_type!=5
            </if>
            <if test="level == 2">
                group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
            </if>
            <if test="level != 2">
                group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1
            </if>
            <!-- 选择套餐之后就只能通过套餐子项来计算非发药的成本 -->
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="departmentSelectSql"/>
                1 as isCopywriter,
                '11-1' as classifyLevel1,
                <if test="level == 2">
                    0 as classifyLevel2,
                </if>
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and product_type != 18
                and product_compose_type = 2
                and record_type not in (2, 3)
                and import_flag = 0
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.isCardOpeningFee == 0">
                    and product_type != 17
                </if>
                <if test="param.arrearsCommissionTiming != 1">
                    and record_source_type != 1
                </if>
                <if test="param.arrearsCommissionTiming != 2">
                    and record_source_type != 2
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="fee1 != null and fee2 != null">
                    and (${fee1} or ${fee2})
                </if>
                <if test="fee1 != null and fee2 == null">
                    and ${fee1}
                </if>
                <if test="fee2 != null and fee1 == null">
                    and ${fee2}
                </if>
                <include refid="conditionForDepId"/>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.includeReg != 1">
                    and product_type!=5
                </if>
                <if test="level == 2">
                    group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1, classifyLevel2
                </if>
                <if test="level != 2">
                    group by chainId, clinicId, departmentId, isCopywriter, classifyLevel1
                </if>
            </if>
        </if>
    </select>

    <select id="selectAdviceDepartmentFeeClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeDepartmentFeeAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            <include refid="departmentSelectSql"/>
            0 as isCopywriter,
            fee_type_id as feeTypeId,
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and record_type not in (2, 3)
        and product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and product_compose_type = 0 and goods_fee_type in (0,2)
        </if>
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <include refid="conditionForDepId"/>
        <if test="param.payModeSql != null and param.payModeSql != ''">
            and ${param.payModeSql}
        </if>
        <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
            and ${param.feeTypeIdSql}
        </if>
        <if test="param.includeReg != null and param.includeReg != 1">
            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        group by chainId, clinicId, departmentId, isCopywriter, feeTypeId
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="departmentSelectSql"/>
                0 as isCopywriter,
                8 as feeTypeId,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type != 18
            and product_compose_type in (2,3)
            and goods_fee_type in (0,2)
            and record_type not in (2, 3)
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <include refid="conditionForDepId"/>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.includeReg != null and param.includeReg != 1">
                and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
            </if>
            <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0">
                and (copywriter_id = '' or copywriter_id is null)
            </if>
            group by chainId, clinicId, departmentId, isCopywriter, feeTypeId
        </if>
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                <include refid="departmentSelectSql"/>
                1 as isCopywriter,
                fee_type_id as feeTypeId,
                sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
            from ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and record_type not in (2, 3)
            and product_type != 18
            and import_flag = 0
            and is_deleted = 0
            and copywriter_id is not null and copywriter_id != ''
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                and product_compose_type = 0 and goods_fee_type in (0,2)
            </if>
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <include refid="conditionForDepId"/>
            <if test="param.payModeSql != null and param.payModeSql != ''">
                and ${param.payModeSql}
            </if>
            <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                and ${param.feeTypeIdSql}
            </if>
            <if test="param.includeReg != null and param.includeReg != 1">
                and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
            </if>
            group by chainId, clinicId, departmentId, isCopywriter, feeTypeId
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    <include refid="departmentSelectSql"/>
                    1 as isCopywriter,
                    8 as feeTypeId,
                    sum(if(type=-1, -received_price, received_price)) as receivedPrice,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
                    sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
                from ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and record_type not in (2, 3)
                and product_type != 18
                and product_compose_type in (2,3)
                and goods_fee_type in (0,2)
                and import_flag = 0
                and is_deleted = 0
                and copywriter_id is not null and copywriter_id != ''
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
                    and ${param.feeTypeIdSql}
                </if>
                <include refid="conditionForDepId"/>
                <if test="param.payModeSql != null and param.payModeSql != ''">
                    and ${param.payModeSql}
                </if>
                <if test="param.includeReg != null and param.includeReg != 1">
                    and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
                </if>
                group by chainId, clinicId, departmentId, isCopywriter, feeTypeId
            </if>
        </if>
    </select>

    <select id="selectReportData"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeReportData">
        SELECT
               a.total_calc_count as count,
               b.*
        from(
            SELECT
                product_id as goodsId,
                goods_fee_type as goodsFeeType,
                if(sum(calc_count) is null,0.0,sum(calc_count)) as total_calc_count
            from ${env}.dwd_charge_transaction_record_v_partition
            WHERE chain_id=#{chainId}
            <if test="clinicId != null and clinicId != ''">
                and clinic_id=#{clinicId}
            </if>
            and (product_type=4 and product_sub_type in (1,2) or product_type in (3,5,14))
            and product_id is not NULL
            and product_id != ''
            and product_compose_type in (0,2)
            and goods_fee_type in (0,1)
            and record_source_type != 2
            and import_flag = 0
            and create_time BETWEEN to_timestamp(#{beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            GROUP BY product_id,goods_fee_type
        ) a
        right join
        (
        select
            goodsId,
            feeTypeId,
            goodsFeeType,
            composeParentProductId,
            price
         from
            (
            SELECT
                product_id as goodsId,
                fee_type_id as feeTypeId,
                if(goods_fee_type=2,1,goods_fee_type) as goodsFeeType,
                if(goods_fee_type = 0, product_id,compose_parent_product_id) as composeParentProductId,
                row_number() over(partition by product_id, if(goods_fee_type = 0, product_id,compose_parent_product_id) ORDER BY create_time desc) as rk,
                abs(product_unit_price) as price
            from ${env}.dwd_charge_transaction_record_v_partition
            WHERE chain_id=#{chainId}
            <if test="clinicId != null and clinicId != ''">
                and clinic_id=#{clinicId}
            </if>
            and product_id is not NULL
            and product_id != ''
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
            and record_source_type != 2
            and import_flag = 0
            and create_time BETWEEN to_timestamp(#{beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN to_char(to_timestamp(#{beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
            ) a where rk = 1
        ) b
        on a.goodsId = b.composeParentProductId and a.goodsFeeType=b.goodsFeeType
    </select>

    <select id="selectAdviceFeeClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeAdviceFeeEntity">
        select distinct
        coalesce(fee_type_id,-1) as feeTypeId
        from ${env}.dwd_charge_transaction_record_v_partition
        where chain_id = #{param.chainId}
        and import_flag = 0
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and is_deleted = 0
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.includeReg != null and param.includeReg != 1">
            and (compose_parent_classify_level_1_id != '5-0' or compose_parent_classify_level_1_id is null)
        </if>
        <if test="param.isContainOthersWriterAchievement != null and param.isContainOthersWriterAchievement == 0 and param.includeWriter == 0">
            and (copywriter_id = '' or copywriter_id is null)
        </if>
        <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
            and goods_fee_type in (0,2) and product_compose_type != 1
        </if>
        <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
            and ((product_compose_type = 0 and goods_fee_type = 0) or (product_compose_type = 1 and goods_fee_type = 0) or (product_compose_type = 0 and goods_fee_type = 2))
        </if>
        <if test='param.employeeTypeNumber != null and param.employeeTypeNumber == 1'>
            and product_type != 18
            and record_type not in (2, 3)
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
        </if>
        <if test="param.feeTypeIdSql != null and param.feeTypeIdSql != ''">
            and ${param.feeTypeIdSql}
        </if>
    </select>

    <select id="selectDispensingAdviceFeeSecondClassify"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeAdviceFeeEntity">
        select distinct
        coalesce(fee_type_id,-1) as feeTypeId
        from ${env}.dwd_dispensing_log_v_partition
        where chain_id = #{param.chainId}
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN to_char(to_timestamp(#{param.beginDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT and to_char(to_timestamp(#{param.endDate},'YYYY-MM-DD HH24:MI:SS'),'yyyyMM')::INT
        and product_type not in (11, 17, 18)
        and form_type = 0
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id = #{param.clinicId}
        </if>
        <if test="param.includeReg != null and param.includeReg != 1">
            and product_type!=5
        </if>
    </select>

    <select id="selectAchievementChargeSummary" resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalFeeAmountEntity">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id, '00000000000000000000000000000000')) as personnelId,
            if(doctor_id is not null and doctor_id != '', doctor_snap_id, 0::BIGINT) as personnelSnapId,
            if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                fee_type_id as feeTypeId,
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                if (product_compose_type in (2,3), cast(8 as bigInt), fee_type_id) as feeTypeId,
            </if>
            sum(if(type=-1, -received_price, received_price)) as receivedPrice,
            sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as costPrice,
            sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as originPrice,
            sum(if(type=-1, 1, -1) * deduct_promotion_price) as deductPrice
        from
        ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{param.chainId}
        and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        and record_type not in (2, 3)
        and product_type != 18
        and import_flag = 0
        and is_deleted = 0
        and product_compose_type in (0,2,3)
        and goods_fee_type in (0,2)
        <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
            and ${param.arrearsCommissionTimingSql}
        </if>
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
            and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = '')
        </if>
        <if test='param.employeeId != null and param.employeeId != "" and param.employeeId != "00000000000000000000000000000000"'>
            and (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId})
        </if>
        <if test='param.employeeId == "00000000000000000000000000000000"'>
            and ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = ''))
        </if>
        <if test="param.employeeSql != null and param.employeeSql != ''">
             ${param.employeeSql}
        </if>
        group by chainId, clinicId, personnelId, personnelSnapId,departmentId, feeTypeId
    </select>

    <select id="selectAchievementDispensingCostSummary" resultType="cn.abc.flink.stat.service.his.achievement.charge.domain.HisAchievementSummaryDAO">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as personnelId,
            if(transcribe_doctor_id=doctor_id,0::BIGINT, if(doctor_id is not null and doctor_id != '',doctor_snap_id, 0::BIGINT)) as personnelSnapId,
            if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
            <if test="param.isComposeShareEqually == null or param.isComposeShareEqually == 1">
                fee_type_id as feeTypeId,
            </if>
            <if test="param.isComposeShareEqually != null and param.isComposeShareEqually == 0">
                if (product_compose_type = 2, cast(8 as bigInt), fee_type_id) as feeTypeId,
            </if>
            sum(if(type in (4,6), -cost_price, cost_price)) as costPrice
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{param.chainId}
        and form_type = 0
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId}))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test='param.employeeId != null and param.employeeId != "" and param.employeeId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_id=#{param.employeeId}, (doctor_id=#{param.employeeId} or seller_id=#{param.employeeId}))
        </if>
        <if test='param.employeeId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_id is null or seller_id = ''), ((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '')))
        </if>
        <if test="param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ''">
            ${param.dispensingEmployeeSql}
        </if>
        group by chainId, clinicId, personnelId, personnelSnapId, departmentId, feeTypeId
    </select>

    <select id="selectOutpatientCountFromRegistration" resultType="cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAppResp">
        select
            <choose>
                <when test="params.departmentId != null and params.departmentId !=''">
                  if(doctor_id = '', null, doctor_id) as doctorId,
                </when>
                <otherwise>
                    if(department_id = '', null, department_id) as departmentId,
                </otherwise>
             </choose>
            count(1) as outpatientCount
        from ${env}.dwd_registration dr
        where
        dr.chain_id = #{params.chainId}
        and dr.reserve_date between to_date(#{params.beginDate}, 'yyyy-MM-DD') and to_date(#{params.endDate}, 'yyyy-MM-DD')
        and dr.ds between to_char(#{params.beginDate}::date, 'YYYY')::NUMERIC and to_char(#{params.endDate}::date, 'YYYY')::NUMERIC
        and status_v2 in (40, 41)
        and registration_type = 0
        <if test="params.clinicId != null and params.clinicId !='' ">
            and dr.clinic_id = #{params.clinicId}
        </if>
        <choose>
            <when test="params.departmentId != null and params.departmentId !=''">
                  and dr.department_id = #{params.departmentId}
                  group by dr.doctor_id
            </when>
            <otherwise>
                group by dr.department_id
            </otherwise>
        </choose>
    </select>

    <select id="selectHospitalCountFromPatientorderExtend" resultType="cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAppResp">
       select
            <choose>
               <when test="params.departmentId != null and params.departmentId !=''">
                  if(doctor_id = '', null, doctor_id) as doctorId,
                </when>
                <otherwise>
                    if(department_id = '', null, department_id) as departmentId,
                </otherwise>
            </choose>
            count(1) as hospitalCount from
        (
            select clinic_id, doctor_id, department_id, patient_id
            from ${env}.ods_patientorder_hospital_extend_snapshot
            where ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
            and create_date between to_char(#{params.beginDate}::date, 'YYYYMMDD')::NUMERIC and to_char(#{params.endDate}::date, 'YYYYMMDD')::NUMERIC
            and chain_id = #{params.chainId}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.departmentId != null and params.departmentId != ''">
                and department_id = #{params.departmentId}
            </if>
            group by clinic_id, doctor_id, department_id, patient_id
        ) as ddp
        <choose>
            <when test="params.departmentId != null and params.departmentId !=''">
              group by doctor_id
            </when>
            <otherwise>
                group by department_id
            </otherwise>
         </choose>
        union all
        select
            <choose>
                <when test="params.departmentId != null and params.departmentId !=''">
                  if(doctor_id = '', null, doctor_id) as doctorId,
                </when>
                <otherwise>
                    if(department_id = '', null, department_id) as departmentId,
                </otherwise>
             </choose>
             count(1) as hospitalCount from
        (
            select clinic_id, doctor_id, department_id, patient_id
            from ${env}.dwd_his_inpatient_work_report
            where ds = cast(to_char(NOW(), 'YYYYMM') as int)
            and created >= to_timestamp(to_char(NOW(), 'YYYY-MM-DD'), 'YYYY-MM-DD')
            and status = 20
            and chain_id = #{params.chainId}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <if test="params.departmentId != null and params.departmentId != ''">
                and department_id = #{params.departmentId}
            </if>
            group by clinic_id, doctor_id, department_id, patient_id
        ) as iwr
        <choose>
            <when test="params.departmentId != null and params.departmentId !=''">
              group by doctor_id
            </when>
            <otherwise>
                group by department_id
            </otherwise>
         </choose>
    </select>

    <select id="selectHospitalAmountFromHisChargeSettle" resultType="cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAppResp">
        select
            <choose>
               <when test="params.departmentId != null and params.departmentId !=''">
                  if(doctor_id = '', null, doctor_id) as doctorId,
                </when>
                <otherwise>
                    if(department_id = '', null, department_id) as departmentId,
                </otherwise>
            </choose>
            sum(if(charge_type = -1,-1,1) * total_price) as amount
        from ${env}.dwd_his_charge_settle_transaction_record
        where 1=1
        and created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds between #{params.beginDateDs} and #{params.endDateDs}
        and goods_fee_type in(0,2)
        and chain_id = #{params.chainId}
          and is_deleted = 0
        <if test="params.clinicId != null and params.clinicId != ''">
            and clinic_id=#{params.clinicId}
        </if>
        <choose>
            <when test="params.departmentId != null and params.departmentId !=''">
                  and department_id = #{params.departmentId}
                  group by doctor_id
            </when>
            <otherwise>
                group by department_id
            </otherwise>
        </choose>
    </select>

    <select id="selectPeChargeAmount" resultType="cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAppResp">
        select
            <choose>
               <when test="params.departmentId != null and params.departmentId !=''">
                 if(sales_employee_id = '', null, sales_employee_id) as doctorId,
               </when>
               <otherwise>
                   if(sales_department_id = '', null, sales_department_id) as departmentId,
               </otherwise>
            </choose>
            sum(if(charge_type = 1,total_price,-1 * total_price)) as amount
        from
            ${env}.dwd_pe_charge_sheet_transaction_record
        where
            1=1
            and fee_compose_type in (0,1)
            and chain_id = #{params.chainId}
            and created between to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds between #{params.beginDateDs} and #{params.endDateDs}
            <if test="params.clinicId != null and params.clinicId != ''">
                and clinic_id = #{params.clinicId}
            </if>
            <choose>
                <when test="params.departmentId != null and params.departmentId !=''">
                      and sales_department_id = #{params.departmentId}
                      group by sales_employee_id
                </when>
                <otherwise>
                    group by sales_department_id
                </otherwise>
            </choose>
    </select>

    <select id="selectOutpatientAmount" resultType="cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAppResp">
        select
            <choose>
               <when test="params.departmentId != null and params.departmentId !=''">
                 if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id = '', null, seller_id)) as doctorId,
               </when>
               <otherwise>
                   if(doctor_id is not null and doctor_id != '', department_id, if(seller_department_id = '', null, seller_department_id)) as departmentId,
               </otherwise>
            </choose>
            <choose>
               <when test="params.commissionType != null and params.commissionType == 2">
                 sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as amount
               </when>
               <otherwise>
                   sum(if(type=-1, -received_price, received_price)) as amount
               </otherwise>
            </choose>
        from
            ${env}.dwd_charge_transaction_record_v_partition
        where chain_id=#{params.chainId}
          and import_flag = 0
          and record_type not in (2, 3)
          and create_time BETWEEN to_timestamp(#{params.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{params.endDate}, 'YYYY-MM-DD HH24:MI:SS')
          and ds BETWEEN #{params.beginDateDs} and #{params.endDateDs}
          and product_type != 18
          <if test="params.clinicId != null and params.clinicId != ''">
              and clinic_id = #{params.clinicId}
          </if>
          <choose>
              <when test="params.arrearsCommissionTiming != null and params.arrearsCommissionTiming == 2">
                    and record_source_type in (0, 2)
              </when>
              <otherwise>
                  and record_source_type in (0, 1)
              </otherwise>
          </choose>
          <if test="params.arrearsCommissionTiming">
                and ((product_compose_type = 0 and goods_fee_type != 1) or (product_compose_type = 3 and goods_fee_type = 2) or (product_compose_type = 2 and goods_fee_type = 0))
          </if>
          <choose>
            <when test="params.departmentId != null and params.departmentId !=''">
                  and (department_id = #{params.departmentId} or seller_department_id = #{params.departmentId})
                  group by doctorId
            </when>
            <otherwise>
                group by departmentId
            </otherwise>
        </choose>
    </select>

    <select id="selectPersonnelDepartmentBase" resultType="cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementPerson">
        select *
        from
        (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as personnelId,
                if(doctor_id is not null and doctor_id != '', doctor_snap_id, 0::BIGINT) as personnelSnapId,
                0 as iswriter,
                count(distinct v2_patient_order_id) as patientCount,
                sum(if(type=-1, -received_price, received_price)) as amount,
                sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as cost,
                sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as origin,
                sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deduct
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and import_flag = 0
            and record_type not in (2, 3)
            and is_deleted = 0
            and product_type != 18
            and is_deleted = 0
            and product_compose_type in (0,2,3)
            and goods_fee_type in (0,2)
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            group by chainId, clinicId, departmentId, personnelId, personnelSnapId, isWriter
            <if test="param.includeWriter == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                    copywriter_id as personnelId,
                    0::BIGINT as personnelSnapId,
                    1 as isWriter,
                    count(distinct v2_patient_order_id) as patientCount,
                    sum(if(type=-1, -received_price, received_price)) as amount,
                    sum(if(product_type not in (1,2,7), if(type=-1, -cost_price, cost_price), 0.0)) as cost,
                    sum(if(type =-1, -1, 1) * (received_price-record_discount_price-if(record_adjustment_price>0, record_adjustment_price, 0.0)+if(record_source_type=2,-discount_price-if(adjustment_price>0,adjustment_price,0.0),0.0)-if(record_unit_adjustment_price is null,0.0,record_unit_adjustment_price))) as origin,
                    sum(if(type=-1, deduct_promotion_price, -deduct_promotion_price)) as deduct
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and import_flag = 0
                and record_type not in (2, 3)
                and is_deleted = 0
                and copywriter_id is not null and copywriter_id != ''
                and product_type != 18
                and is_deleted = 0
                and product_compose_type in (0,2,3)
                and goods_fee_type in (0,2)
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                group by chainId, clinicId, departmentId, personnelId, personnelSnapId, isWriter
            </if>
        ) a
    </select>

    <select id="selectPersonnelDepartmentDispensingCost"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonnelDispensingCost">
        select
            chain_id as chainId,
            clinic_id as clinicId,
            if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
            if(transcribe_doctor_id=doctor_id,if(seller_id is not null and seller_id != '',seller_id, '00000000000000000000000000000000'), if(seller_id is not null and seller_id != '', seller_id, if(doctor_id is not null and doctor_id != '',doctor_id, '00000000000000000000000000000000'))) as employeeId,
            if(transcribe_doctor_id=doctor_id, 0::BIGINT, if(doctor_id is not null and doctor_id != '',doctor_snap_id, 0::BIGINT)) as employeeSnapId,
            0 as isCopywriter,
            sum(if(type=4, -cost_price, cost_price)) as costPrice
        from
        ${env}.dwd_dispensing_log_v_partition
        where chain_id=#{param.chainId}
        and form_type = 0
        and compose_parent_product_id is null
        and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
        and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
        <if test="param.clinicId != null and param.clinicId != ''">
            and clinic_id=#{param.clinicId}
        </if>
        <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, ((department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})))
        </if>
        <if test='param.departmentId == "00000000000000000000000000000000"'>
            and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
        </if>
        <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
            ${param.dispensingEmployeeSql}
        </if>
        group by chainId, clinicId, departmentId, employeeId, employeeSnapId, isCopywriter
        <if test="param.includeWriter == 1">
            union all
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(transcribe_doctor_id=doctor_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000'),if(seller_department_id is not null and seller_department_id != '',seller_department_id, if(department_id is not null and department_id != '', department_id,  '00000000000000000000000000000000'))) as departmentId,
                copywriter_id as employeeId,
                0::BIGINT as employeeSnapId,
                1 as isCopywriter,
                sum(if(type=4, -cost_price, cost_price)) as costPrice
            from
            ${env}.dwd_dispensing_log_v_partition
            where chain_id=#{param.chainId}
            and form_type = 0
            and compose_parent_product_id is null
            and copywriter_id is not null and copywriter_id != ''
            and log_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, seller_department_id = #{param.departmentId}, ((department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})))
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and if(transcribe_doctor_id=doctor_id, (seller_department_id is null or seller_department_id = ''), (department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test='param.dispensingEmployeeSql != null and param.dispensingEmployeeSql != ""'>
                ${param.dispensingEmployeeSql}
            </if>
            group by chainId, clinicId, departmentId, employeeId, employeeSnapId, isCopywriter
        </if>
    </select>

    <select id="selectPersonnelDepartmentPrescription"
            resultType="cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalPrescriptionEntity">
        select
            chainId,
            clinicId,
            departmentId,
            personnelId,
            personnelSnapId,
            isCopywriter,
            sum(num) as count
        from
        (
            select
                chainId,
                clinicId,
                departmentId,
                personnelId,
                personnelSnapId,
                isCopywriter,
                sum(if(amount>0, 1, if(amount &lt; 0, -1, 0))) as num
            from
            (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as personnelId,
                if(doctor_id is not null and doctor_id != '', doctor_snap_id, 0::BIGINT) as personnelSnapId,
                0 as isCopywriter,
                v2_charge_form_id as chargeFromId,
                sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and  product_type not in (11, 18)
            and charge_sheet_type not in (3,6,8,12,16,18)
            and record_type not in (2, 3)
            and source_form_type in (4,5,6,16)
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            group by chainId, clinicId, departmentId, personnelId, personnelSnapId, isCopywriter, chargeFromId
            <if test="param.includeWriter == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                    copywriter_id as personnelId,
                    0::BIGINT as personnelSnapId,
                    1 as isCopywriter,
                    v2_charge_form_id as chargeFromId,
                    sum(if(type=-1, -1, 1) * received_price) as amount
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and  product_type not in (11, 18)
                and charge_sheet_type not in (3,6,8,12,16,18)
                and record_type not in (2, 3)
                and source_form_type in (4,5,6,16)
                and import_flag = 0
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                group by chainId, clinicId, departmentId, personnelId, personnelSnapId, isCopywriter, chargeFromId
            </if>
            ) a
            group by chainId, clinicId, departmentId, personnelId, isCopywriter, personnelSnapId
            union all
            select
                chainId,
                clinicId,
                departmentId,
                personnelId,
                personnelSnapId,
                isCopywriter,
                sum(if(amount>0, 1, if(amount &lt; 0, -1, 0)) * pnum) as num
            from
            (
            select
                chain_id as chainId,
                clinic_id as clinicId,
                if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                if(doctor_id is not null and doctor_id != '', doctor_id, if(seller_id is not null and seller_id != '', seller_id,  '00000000000000000000000000000000')) as personnelId,
                if(doctor_id is not null and doctor_id != '', doctor_snap_id, 0::BIGINT) as personnelSnapId,
                0 as isCopywriter,
                cast(extend_prescription ->> 'eyeglassPrescriptionCount' as NUMERIC)  as pnum,
                sum(if(type=-1, -1, 1) * received_price) as amount
            from
            ${env}.dwd_charge_transaction_record_v_partition
            where chain_id=#{param.chainId}
            and product_type !=11
            and charge_sheet_type not in (3,6,8,12,16,18)
            and record_type not in (2, 3)
            and extend_prescription is not null
            and import_flag = 0
            and is_deleted = 0
            and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
            and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
            <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                and ${param.arrearsCommissionTimingSql}
            </if>
            <if test="param.clinicId != null and param.clinicId != ''">
                and clinic_id=#{param.clinicId}
            </if>
            <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
                and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
            </if>
            <if test='param.departmentId == "00000000000000000000000000000000"'>
                and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
            </if>
            <if test='param.employeeSql != null and param.employeeSql != ""'>
                ${param.employeeSql}
            </if>
            group by chainId, clinicId, departmentId, personnelId, personnelSnapId, isCopywriter, v2_charge_sheet_id, pnum
            <if test="param.includeWriter == 1">
                union all
                select
                    chain_id as chainId,
                    clinic_id as clinicId,
                    if(department_id is not null and department_id != '',department_id,if(seller_department_id is not null and seller_department_id != '', seller_department_id,  '00000000000000000000000000000000')) as departmentId,
                    copywriter_id as personnelId,
                    0::BIGINT as personnelSnapId,
                    1 as isCopywriter,
                    cast(extend_prescription ->> 'eyeglassPrescriptionCount' as NUMERIC) as pnum,
                    sum(if(type=-1, -1, 1) * received_price) as amount
                from
                ${env}.dwd_charge_transaction_record_v_partition
                where chain_id=#{param.chainId}
                and product_type!=11
                and charge_sheet_type not in (3,6,8,12,16,18)
                and record_type not in (2, 3)
                and extend_prescription is not null
                and import_flag = 0
                and is_deleted = 0
                and create_time BETWEEN to_timestamp(#{param.beginDate}, 'YYYY-MM-DD HH24:MI:SS') and to_timestamp(#{param.endDate}, 'YYYY-MM-DD HH24:MI:SS')
                and ds BETWEEN #{param.beginDateDs} and #{param.endDateDs}
                and copywriter_id is not null and copywriter_id != ''
                <if test="param.arrearsCommissionTimingSql != null and param.arrearsCommissionTimingSql != ''">
                    and ${param.arrearsCommissionTimingSql}
                </if>
                <if test="param.clinicId != null and param.clinicId != ''">
                    and clinic_id=#{param.clinicId}
                </if>
                <if test='param.departmentId != null and param.departmentId != "" and param.departmentId != "00000000000000000000000000000000"'>
                    and (department_id = #{param.departmentId} or seller_department_id = #{param.departmentId})
                </if>
                <if test='param.departmentId == "00000000000000000000000000000000"'>
                    and ((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))
                </if>
                <if test='param.employeeSql != null and param.employeeSql != ""'>
                    ${param.employeeSql}
                </if>
                group by chainId, clinicId, departmentId, personnelId, personnelSnapId, isCopywriter, v2_charge_sheet_id, pnum
            </if>
            ) b
            group by chainId, clinicId, departmentId, personnelId, isCopywriter, personnelSnapId
        ) c
        group by chainId, clinicId, departmentId, personnelId, isCopywriter, personnelSnapId
    </select>
</mapper>