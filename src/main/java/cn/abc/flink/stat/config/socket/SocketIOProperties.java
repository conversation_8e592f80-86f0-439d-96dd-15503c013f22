package cn.abc.flink.stat.config.socket;

import com.corundumstudio.socketio.Configuration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2025-06-19 16:18
 */
@Data
@ConfigurationProperties(prefix = "socketio")
public class SocketIOProperties {

    private Server server;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Server extends Configuration {

    }
}
