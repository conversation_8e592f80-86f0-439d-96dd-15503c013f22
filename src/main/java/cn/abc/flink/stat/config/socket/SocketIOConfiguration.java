package cn.abc.flink.stat.config.socket;

import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.annotation.SpringAnnotationScanner;
import com.corundumstudio.socketio.protocol.JacksonJsonSupport;
import com.corundumstudio.socketio.store.RedissonStoreFactory;
import org.apache.commons.lang3.SystemUtils;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 套接字 io配置
 *
 * <AUTHOR>
 * @date 2025-06-19 15:41
 */
@Configuration
@EnableConfigurationProperties(value = {SocketIOProperties.class})
public class SocketIOConfiguration {

    @Bean(destroyMethod = "stop")
    public SocketIOServer socketIOServer(RedissonClient redissonClient, SocketIOProperties properties) {
        com.corundumstudio.socketio.Configuration configuration = properties.getServer();
        configuration.setUseLinuxNativeEpoll(SystemUtils.IS_OS_LINUX);
        configuration.setWorkerThreads(Math.max(Runtime.getRuntime().availableProcessors(), 8) * 2);
        configuration.setJsonSupport(new JacksonJsonSupport());
        configuration.setStoreFactory(new RedissonStoreFactory(redissonClient));
        //configuration.setAuthorizationListener();
        //configuration.setExceptionListener();
        //configuration.setSocketConfig();
        //configuration.setHttpRequestDecoderConfiguration();
        SocketIOServer socketio = new SocketIOServer(configuration);
        socketio.start();
        return socketio;
    }

    @Bean
    public SpringAnnotationScanner springAnnotationScanner(SocketIOServer socketio) {
        return new SpringAnnotationScanner(socketio);
    }
}
