package cn.abc.flink.stat.config;

import cn.abc.flink.stat.websocket.interceptor.RpcRequestInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    @Autowired
    private RpcRequestInterceptor rpcRequestInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册RPC请求拦截器，只拦截/rpc/路径下的请求
        registry.addInterceptor(rpcRequestInterceptor)
                .addPathPatterns("/rpc/**")
                .excludePathPatterns("/rpc/health", "/rpc/actuator/**"); // 排除健康检查等路径
    }
}
