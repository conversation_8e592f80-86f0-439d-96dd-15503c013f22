package cn.abc.flink.stat.dimension.items;

import cn.abc.flink.stat.common.model.ChargeSource;
import cn.abc.flink.stat.dimension.domain.ChargeSourceType;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DimensionChargeSourceType {
    public static Map<Integer, ChargeSourceType> map = new HashMap<>();

    static {
        map.put(ChargeSource.REGISTRATION_RESERVE, new ChargeSourceType(
                ChargeSource.REGISTRATION_RESERVE, "挂号预约", 1));
        map.put(ChargeSource.OUTPATIENT, new ChargeSourceType(
                ChargeSource.OUTPATIENT, "门诊", 2));
        map.put(ChargeSource.RETAIL, new ChargeSourceType(
                ChargeSource.RETAIL, "零售", 3));
        map.put(ChargeSource.MEMBER_RECHARGE, new ChargeSourceType(
                ChargeSource.MEMBER_RECHARGE, "会员充值", 7));
        map.put(ChargeSource.NETWORK_DIAG, new ChargeSourceType(
                ChargeSource.NETWORK_DIAG, "网诊", 4));
        map.put(ChargeSource.AUTO_CONTINUE_PRESCRIPTION, new ChargeSourceType(
                ChargeSource.AUTO_CONTINUE_PRESCRIPTION, "自助续方", 6));
        map.put(ChargeSource.FAMILY_SIGN, new ChargeSourceType(
                ChargeSource.FAMILY_SIGN, "家医签约", 9));
        map.put(ChargeSource.OPEN_PROMOTION_CARD, new ChargeSourceType(
                ChargeSource.OPEN_PROMOTION_CARD, "开卡", 5));
        map.put(ChargeSource.PROMOTION_CARD_RECHARGE, new ChargeSourceType(
                ChargeSource.PROMOTION_CARD_RECHARGE, "卡项充值", 8));
        map.put(ChargeSource.AIR_PHARMACY_ORDER, new ChargeSourceType(
                ChargeSource.AIR_PHARMACY_ORDER, "ABC空中药房", 100));
        map.put(ChargeSource.CONSULTING_FEES, new ChargeSourceType(
                ChargeSource.CONSULTING_FEES, "咨询", 15));
        map.put(ChargeSource.COOPERATION_ORDER, new ChargeSourceType(
                ChargeSource.COOPERATION_ORDER, "合作诊所", 17));
    }

    /**
     *
     * @param retailType 弃用，零售与业务保持一致即可
     */
    public static String query(Byte source, Byte retailType) {
        if (source == null) {
            source = 0;
        }
        int key = source;
        if (source == null) {
            // 返回"-"
            key = 0;
        } else {
            // 13为收费处门诊单补单也开单来源也显示门诊
            if (source == 4 || source == 13) {
                // 门诊 = 2/4
                key = 2;
            } else if (source == 6 || source == 8 || source == 12 || source == 16 || source == 18) {
                // 零售开单 = 3/6
                key = 3;
            }
        }
        ChargeSourceType cst = map.get(key);
        if (cst == null) {
            return "-";
        }
        return cst.getName();
    }
}
