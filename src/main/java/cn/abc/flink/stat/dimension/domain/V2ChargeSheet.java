package cn.abc.flink.stat.dimension.domain;



import java.math.BigDecimal;
import java.time.LocalDateTime;

public class V2ChargeSheet {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.patient_order_id
     *
     * @mbg.generated
     */
    private String patient_order_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.patient_id
     *
     * @mbg.generated
     */
    private String patient_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.chain_id
     *
     * @mbg.generated
     */
    private String chain_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.clinic_id
     *
     * @mbg.generated
     */
    private String clinic_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.member_id
     *
     * @mbg.generated
     */
    private String member_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.registration_charge_sheet_id
     *
     * @mbg.generated
     */
    private String registration_charge_sheet_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.therapy_sheet_id
     *
     * @mbg.generated
     */
    private String therapy_sheet_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.v1_id
     *
     * @mbg.generated
     */
    private String v1_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.total_fee
     *
     * @mbg.generated
     */
    private BigDecimal total_fee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.additional_fee
     *
     * @mbg.generated
     */
    private BigDecimal additional_fee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.discount_fee
     *
     * @mbg.generated
     */
    private BigDecimal discount_fee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.refund_fee
     *
     * @mbg.generated
     */
    private BigDecimal refund_fee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.receivableFee
     *
     * @mbg.generated
     */
    private BigDecimal receivableFee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.received_fee
     *
     * @mbg.generated
     */
    private BigDecimal receivedFee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.seller_id
     *
     * @mbg.generated
     */
    private String seller_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.doctor_id
     *
     * @mbg.generated
     */
    private String doctor_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.copywriter_id
     *
     * @mbg.generated
     */
    private String copywriter_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.charged_by
     *
     * @mbg.generated
     */
    private String charged_by;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.charged_time
     *
     * @mbg.generated
     */
    private LocalDateTime charged_time;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.first_charged_time
     *
     * @mbg.generated
     */
    private LocalDateTime first_charged_time;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.is_dispensing
     *
     * @mbg.generated
     */
    private Byte is_dispensing;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.member_discount_info
     *
     * @mbg.generated
     */
    private String member_discount_info;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.member_info
     *
     * @mbg.generated
     */
    private String member_info;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.promotion_info
     *
     * @mbg.generated
     */
    private String promotion_info;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.outpatient_status
     *
     * @mbg.generated
     */
    private Byte outpatient_status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.outpatient_adjustment_fee
     *
     * @mbg.generated
     */
    private BigDecimal outpatient_adjustment_fee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.execute_status
     *
     * @mbg.generated
     */
    private Byte execute_status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.import_flag
     *
     * @mbg.generated
     */
    private Byte import_flag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.order_by_date
     *
     * @mbg.generated
     */
    private LocalDateTime order_by_date;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.diagnosed_date
     *
     * @mbg.generated
     */
    private LocalDateTime diagnosed_date;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.data_version
     *
     * @mbg.generated
     */
    private Integer data_version;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.is_draft
     *
     * @mbg.generated
     */
    private Byte is_draft;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.is_deleted
     *
     * @mbg.generated
     */
    private Byte is_deleted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.created
     *
     * @mbg.generated
     */
    private LocalDateTime created;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.created_by
     *
     * @mbg.generated
     */
    private String created_by;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.last_modified_by
     *
     * @mbg.generated
     */
    private String last_modified_by;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.last_modified
     *
     * @mbg.generated
     */
    private LocalDateTime last_modified;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.expire_time
     *
     * @mbg.generated
     */
    private LocalDateTime expire_time;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.delivery_type
     *
     * @mbg.generated
     */
    private Byte delivery_type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.send_to_patient_status
     *
     * @mbg.generated
     */
    private Byte send_to_patient_status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.draft_adjustment_fee
     *
     * @mbg.generated
     */
    private BigDecimal draft_adjustment_fee;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.is_online
     *
     * @mbg.generated
     */
    private Byte is_online;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.check_status
     *
     * @mbg.generated
     */
    private Byte check_status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.is_decoction
     *
     * @mbg.generated
     */
    private Byte is_decoction;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.coupon_promotion_info
     *
     * @mbg.generated
     */
    private String coupon_promotion_info;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.gift_rule_promotion_info
     *
     * @mbg.generated
     */
    private String gift_rule_promotion_info;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_charge_sheet.reserve_date
     *
     * @mbg.generated
     */
    private LocalDateTime reserve_date;



    public String getId() {
        return this.id;
    }

    public String getPatient_order_id() {
        return this.patient_order_id;
    }

    public String getPatient_id() {
        return this.patient_id;
    }

    public String getChain_id() {
        return this.chain_id;
    }

    public String getClinic_id() {
        return this.clinic_id;
    }

    public String getMember_id() {
        return this.member_id;
    }

    public String getRegistration_charge_sheet_id() {
        return this.registration_charge_sheet_id;
    }

    public String getTherapy_sheet_id() {
        return this.therapy_sheet_id;
    }

    public String getV1_id() {
        return this.v1_id;
    }

    public BigDecimal getTotal_fee() {
        return this.total_fee;
    }

    public BigDecimal getAdditional_fee() {
        return this.additional_fee;
    }

    public BigDecimal getDiscount_fee() {
        return this.discount_fee;
    }

    public BigDecimal getRefund_fee() {
        return this.refund_fee;
    }

    public BigDecimal getReceivedFee() {
        return this.receivedFee;
    }

    public String getSeller_id() {
        return this.seller_id;
    }

    public String getDoctor_id() {
        return this.doctor_id;
    }

    public String getCopywriter_id() {
        return this.copywriter_id;
    }

    public String getCharged_by() {
        return this.charged_by;
    }

    public LocalDateTime getCharged_time() {
        return this.charged_time;
    }

    public LocalDateTime getFirst_charged_time() {
        return this.first_charged_time;
    }

    public Byte getIs_dispensing() {
        return this.is_dispensing;
    }

    public String getMember_discount_info() {
        return this.member_discount_info;
    }

    public String getMember_info() {
        return this.member_info;
    }

    public String getPromotion_info() {
        return this.promotion_info;
    }

    public Byte getOutpatient_status() {
        return this.outpatient_status;
    }

    public BigDecimal getOutpatient_adjustment_fee() {
        return this.outpatient_adjustment_fee;
    }

    public Byte getExecute_status() {
        return this.execute_status;
    }

    public Byte getStatus() {
        return this.status;
    }

    public Byte getType() {
        return this.type;
    }

    public Byte getImport_flag() {
        return this.import_flag;
    }

    public LocalDateTime getOrder_by_date() {
        return this.order_by_date;
    }

    public LocalDateTime getDiagnosed_date() {
        return this.diagnosed_date;
    }

    public Integer getData_version() {
        return this.data_version;
    }

    public Byte getIs_draft() {
        return this.is_draft;
    }

    public Byte getIs_deleted() {
        return this.is_deleted;
    }

    public LocalDateTime getCreated() {
        return this.created;
    }

    public String getCreated_by() {
        return this.created_by;
    }

    public String getLast_modified_by() {
        return this.last_modified_by;
    }

    public LocalDateTime getLast_modified() {
        return this.last_modified;
    }

    public LocalDateTime getExpire_time() {
        return this.expire_time;
    }

    public Byte getDelivery_type() {
        return this.delivery_type;
    }

    public Byte getSend_to_patient_status() {
        return this.send_to_patient_status;
    }

    public BigDecimal getDraft_adjustment_fee() {
        return this.draft_adjustment_fee;
    }

    public Byte getIs_online() {
        return this.is_online;
    }

    public Byte getCheck_status() {
        return this.check_status;
    }

    public Byte getIs_decoction() {
        return this.is_decoction;
    }

    public String getCoupon_promotion_info() {
        return this.coupon_promotion_info;
    }

    public String getGift_rule_promotion_info() {
        return this.gift_rule_promotion_info;
    }

    public LocalDateTime getReserve_date() {
        return this.reserve_date;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setPatient_order_id(String patient_order_id) {
        this.patient_order_id = patient_order_id;
    }

    public void setPatient_id(String patient_id) {
        this.patient_id = patient_id;
    }

    public void setChain_id(String chain_id) {
        this.chain_id = chain_id;
    }

    public void setClinic_id(String clinic_id) {
        this.clinic_id = clinic_id;
    }

    public void setMember_id(String member_id) {
        this.member_id = member_id;
    }

    public void setRegistration_charge_sheet_id(String registration_charge_sheet_id) {
        this.registration_charge_sheet_id = registration_charge_sheet_id;
    }

    public void setTherapy_sheet_id(String therapy_sheet_id) {
        this.therapy_sheet_id = therapy_sheet_id;
    }

    public void setV1_id(String v1_id) {
        this.v1_id = v1_id;
    }

    public void setTotal_fee(BigDecimal total_fee) {
        this.total_fee = total_fee;
    }

    public void setAdditional_fee(BigDecimal additional_fee) {
        this.additional_fee = additional_fee;
    }

    public void setDiscount_fee(BigDecimal discount_fee) {
        this.discount_fee = discount_fee;
    }

    public void setRefund_fee(BigDecimal refund_fee) {
        this.refund_fee = refund_fee;
    }

    public void setReceivedFee(BigDecimal receivedFee) {
        this.receivedFee = receivedFee;
    }

    public void setSeller_id(String seller_id) {
        this.seller_id = seller_id;
    }

    public void setDoctor_id(String doctor_id) {
        this.doctor_id = doctor_id;
    }

    public void setCopywriter_id(String copywriter_id) {
        this.copywriter_id = copywriter_id;
    }

    public void setCharged_by(String charged_by) {
        this.charged_by = charged_by;
    }

    public void setCharged_time(LocalDateTime charged_time) {
        this.charged_time = charged_time;
    }

    public void setFirst_charged_time(LocalDateTime first_charged_time) {
        this.first_charged_time = first_charged_time;
    }

    public void setIs_dispensing(Byte is_dispensing) {
        this.is_dispensing = is_dispensing;
    }

    public void setMember_discount_info(String member_discount_info) {
        this.member_discount_info = member_discount_info;
    }

    public void setMember_info(String member_info) {
        this.member_info = member_info;
    }

    public void setPromotion_info(String promotion_info) {
        this.promotion_info = promotion_info;
    }

    public void setOutpatient_status(Byte outpatient_status) {
        this.outpatient_status = outpatient_status;
    }

    public void setOutpatient_adjustment_fee(BigDecimal outpatient_adjustment_fee) {
        this.outpatient_adjustment_fee = outpatient_adjustment_fee;
    }

    public void setExecute_status(Byte execute_status) {
        this.execute_status = execute_status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public void setImport_flag(Byte import_flag) {
        this.import_flag = import_flag;
    }

    public void setOrder_by_date(LocalDateTime order_by_date) {
        this.order_by_date = order_by_date;
    }

    public void setDiagnosed_date(LocalDateTime diagnosed_date) {
        this.diagnosed_date = diagnosed_date;
    }

    public void setData_version(Integer data_version) {
        this.data_version = data_version;
    }

    public void setIs_draft(Byte is_draft) {
        this.is_draft = is_draft;
    }

    public void setIs_deleted(Byte is_deleted) {
        this.is_deleted = is_deleted;
    }

    public void setCreated(LocalDateTime created) {
        this.created = created;
    }

    public void setCreated_by(String created_by) {
        this.created_by = created_by;
    }

    public void setLast_modified_by(String last_modified_by) {
        this.last_modified_by = last_modified_by;
    }

    public void setLast_modified(LocalDateTime last_modified) {
        this.last_modified = last_modified;
    }

    public void setExpire_time(LocalDateTime expire_time) {
        this.expire_time = expire_time;
    }

    public void setDelivery_type(Byte delivery_type) {
        this.delivery_type = delivery_type;
    }

    public void setSend_to_patient_status(Byte send_to_patient_status) {
        this.send_to_patient_status = send_to_patient_status;
    }

    public void setDraft_adjustment_fee(BigDecimal draft_adjustment_fee) {
        this.draft_adjustment_fee = draft_adjustment_fee;
    }

    public void setIs_online(Byte is_online) {
        this.is_online = is_online;
    }

    public void setCheck_status(Byte check_status) {
        this.check_status = check_status;
    }

    public void setIs_decoction(Byte is_decoction) {
        this.is_decoction = is_decoction;
    }

    public void setCoupon_promotion_info(String coupon_promotion_info) {
        this.coupon_promotion_info = coupon_promotion_info;
    }

    public void setGift_rule_promotion_info(String gift_rule_promotion_info) {
        this.gift_rule_promotion_info = gift_rule_promotion_info;
    }

    public void setReserve_date(LocalDateTime reserve_date) {
        this.reserve_date = reserve_date;
    }

    public BigDecimal getReceivableFee() {
        return receivableFee;
    }

    public void setReceivableFee(BigDecimal receivableFee) {
        this.receivableFee = receivableFee;
    }
}