package cn.abc.flink.stat.dimension;


import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.GoodsFirstClassifyEnum;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.domain.V2PatientorderHospitalExtend;
import cn.abc.flink.stat.common.request.params.AbcScStatFilterEmployee;
import cn.abc.flink.stat.common.request.params.GoodsInfoParam;
import cn.abc.flink.stat.common.request.params.MemberParam;
import cn.abc.flink.stat.common.response.StatSelectionResponse;
import cn.abc.flink.stat.common.utils.CollectionUtils;
import cn.abc.flink.stat.config.SpecialRegisterProperties;
import cn.abc.flink.stat.dimension.domain.*;
import cn.abc.flink.stat.dimension.items.*;
import cn.abc.flink.stat.dimension.params.GoodsParams;
import cn.abc.flink.stat.service.cis.external.report.domain.StatExternalReportMapping;
import cn.abc.flink.stat.service.cis.external.report.domain.StatExternalReportParam;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryFee;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.V2GoodsClassify;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.V2GoodsCompose;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.handler.RevenueChargedDailyHandler;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/10/25 9:38 上午
 */
@Component
public class DimensionQuery {

    @Resource
    private DimensionHisWard hisWard;

    @Autowired
    private DimensionPatientorder dPatientorder;

    @Autowired
    private DimensionMember dMemberType;

    @Autowired
    private DimensionDepartment dDepartment;

    @Autowired
    private DimensionEmployee dEmployee;

    @Autowired
    private DimensionOrgan dOrgan;

    @Autowired
    private DimensionPatient dPatient;

    @Autowired
    private DimensionProductCustomType dCustomType;

    @Autowired
    private DimensionV2Goods dGoods;

    @Autowired
    private DimensionPayType dPayType;

    @Autowired
    private DimensionVendorOrgan dVendorOrgan;

    @Autowired
    private DimensionVisitSource dVisitSource;

    @Autowired
    private DimensionFamilyDoctorConfig dFamilyDoctorConfig;

    @Autowired
    private DimensionProductType dProductType;

    @Autowired
    private DimensionProductSysType dSystype;

    @Autowired
    private DimensionProductSupplier dSupplier;

    @Autowired
    DimensionPatientSourceType dSourceType;

    @Autowired
    DimensionPrescription dPrescription;

    @Autowired
    private DimensionMedicalRecord dMedicalRecord;

    @Autowired
    private DimensionPromotion dPromotion;

    @Autowired
    private DimensionPatientPoints dPatientPoints;

    @Autowired
    private DimensionDispensing dimensionDispensing;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private DimensionWard dimensionWard;

    @Autowired
    private DimensionProductSysType dimensionProductSysType;

    @Autowired
    private DimensionPromotionReferral dimensionPromotionReferral;

    @Autowired
    private DimensionHisAdvice dimensionHisAdvice;

    @Autowired
    private DimensionChargeSheet dimensionChargeSheet;

    @Autowired
    private DimensionPatientOrgan dimensionPatientOrgan;

    @Autowired
    private DimensionGoodsSysBusinessScope dimensionGoodsSysBusinessScope;

    @Autowired
    private DimensionEc dimensionEc;

    @Autowired
    private DimensionScrmCustomer dimensionScrmCustomer;

    @Autowired
    private DimensionScrmChannel dimensionScrmChannel;

    @Autowired
    private SpecialRegisterProperties specialRegisterProperties;

    @Resource
    private DimensionOutpatient dimensionOutpatient;

    @Autowired
    private DimensionEmrDiagnosis dimensionEmrDiagnosis;

    @Autowired
    private DimensionHistoryReportMapping dimensionHistoryReportMapping;

    @Value("${abc.async.export.env}")
    private String envType;

    /**
     * @param source     -
     * @param retailType -
     * @return -
     */
    public String queryChargeSourceTypeText(Byte source, Byte retailType) {
        return DimensionChargeSourceType.query(source, retailType);
    }

    /**
     * @param chainId 连锁id
     * @param ids     -
     * @return -
     */
    public Map<String, Department> queryDepartments(String chainId, Collection<String> ids) {
        return dDepartment.queryAndCache(chainId, ids);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public Map<String, Department> queryBatchDepartmentsByOrgan(String chainId, String clinicId) {
        return dDepartment.queryDepartmentByOrganId(chainId);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public Map<String, String> queryBatchDepartmentNamesByOrgan(String chainId, String clinicId) {
        return dDepartment.queryNameByOrganAndId(chainId, clinicId);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public Map<String, String> queryDepartmentIdByOrgan(String chainId, String clinicId) {
        return dDepartment.queryIdByOrgan(chainId, clinicId);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public Map<String, String> queryDepartmentNameByOrgan(String chainId, String clinicId) {
        return dDepartment.queryNameByOrganAndId(chainId, clinicId);
    }

    /**
     * @param employeeId -
     * @return -
     */
    public Employee queryEmployee(String chainId, String employeeId) {
        return dEmployee.queryEmployeeByChainAndId(chainId, employeeId);
    }

    public Map<Long, V2ClinicChainEmployeeSnap> queryEmployeeSnaps(Collection<Long> ids) {
        List<V2ClinicChainEmployeeSnap> list = dEmployee.querySnapByIdsAndCache(ids);
        if (list == null || list.isEmpty()) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(V2ClinicChainEmployeeSnap::getId, Function.identity(), (v1, v2) -> v2));
    }

    public List<SnapEmployee> querySnapByEmployeeAndName(String chainId, List<AbcScStatFilterEmployee> params) {
        if (params == null || params.size() == 0) {
            return new ArrayList<>();
        }
        Map<String, List<String>> doctorNameMap = new HashMap<>();
        boolean isNotSpecified = false;
        for (AbcScStatFilterEmployee param : params) {
            if (doctorNameMap.containsKey(param.getId())) {
                List<String> names = doctorNameMap.get(param.getId());
                names.add(param.getName());
            } else {
                List<String> names = new ArrayList<>();
                names.add(param.getName());
                doctorNameMap.put(param.getId(), names);
            }
            if ("00000000000000000000000000000000".equals(param.getId())) {
                isNotSpecified = true;
            }
        }
        List<V2ClinicChainEmployeeSnap> list = dEmployee.querySnapByEmployeeAndName(chainId, params);
        if ((list == null || list.isEmpty()) && !isNotSpecified) {
            return new ArrayList<>();
        }
        Map<String, Employee> employeeMap = dEmployee.queryByChainId(chainId);
        Map<String, SnapEmployee> snapMap = new HashMap<>();
        if (isNotSpecified) {
            SnapEmployee snapEmployee = new SnapEmployee();
            snapEmployee.setEmployeeId("00000000000000000000000000000000");
            snapMap.put(snapEmployee.getEmployeeId(), snapEmployee);
        }
        list.forEach(dto -> {
            SnapEmployee snapEmployee = new SnapEmployee(dto.getChainId(), dto.getClinicId(), dto.getEmployeeId(), dto.getName());
            snapEmployee.getSnapIds().add(dto.getId());
            Employee employee = employeeMap.get(dto.getEmployeeId());
            List<String> names = doctorNameMap.get(dto.getEmployeeId());
            if (employee != null && names != null) {
                if (names.contains(employee.getName())) {
                    snapEmployee.setIsIncludeTheLatest(1);
                }
            }
            if (snapMap.containsKey(dto.getEmployeeId())) {
                SnapEmployee snap = snapMap.get(dto.getEmployeeId());
                snap.getSnapIds().add(dto.getId());
            } else {
                snapMap.put(dto.getEmployeeId(), snapEmployee);
            }

        });
        return snapMap.values().stream().collect(Collectors.toList());
    }

    public SnapEmployee querySnapByEmployeeAndName(String chainId, AbcScStatFilterEmployee param) {
        if (param == null) {
            return new SnapEmployee();
        }
        List<V2ClinicChainEmployeeSnap> dto = dEmployee.querySnapByEmployeeAndName(chainId, param);
        Employee employee = dEmployee.queryEmployeeByChainAndId(chainId, param.getId());
        if (dto == null || dto.size() == 0) {
            return new SnapEmployee();
        }
        SnapEmployee employees = new SnapEmployee(dto.get(0).getChainId(), dto.get(0).getClinicId(), dto.get(0).getEmployeeId(), dto.get(0).getName());
        for (V2ClinicChainEmployeeSnap snap : dto) {
            employees.getSnapIds().add(snap.getId());
            if (dto != null && param.getName() != null && employee != null) {
                if (param.getName().equals(employee.getName())) {
                    employees.setIsIncludeTheLatest(1);
                }
            }
        }

        return employees;
    }

    /**
     * @param chainId -
     * @param empIds  -
     * @return -
     */
    public Map<String, List<V2ClinicChainEmployeeSnap>> queryEmployeeSnapByEmployeeIds(String chainId, List<String> empIds) {
        List<V2ClinicChainEmployeeSnap> list = dEmployee.queryEmployeeSnapByEmployeeIds(chainId, empIds);
        Map<String, List<V2ClinicChainEmployeeSnap>> employeeMap = new HashMap<>();
        for (V2ClinicChainEmployeeSnap e : list) {
            if (e == null || e.getEmployeeId() == null) {
                continue;
            }
            if (employeeMap.containsKey(e.getEmployeeId())) {
                employeeMap.get(e.getEmployeeId()).add(e);
            } else {
                List<V2ClinicChainEmployeeSnap> lst = new ArrayList<>();
                lst.add(e);
                employeeMap.put(e.getEmployeeId(), lst);
            }
        }
        return employeeMap;
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public List<String> queryDoctorIdsByOrganId(String chainId, String clinicId) {
        return dEmployee.queryDoctorIdsByOrganId(chainId, clinicId);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public List<String> queryNotDoctorIdsByOrganId(String chainId, String clinicId) {
        return dEmployee.queryNotDoctorIdsByOrganId(chainId, clinicId);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public List<String> queryTherapistIdsByOrganId(String chainId, String clinicId) {
        return dEmployee.queryTherapistIdsByOrganId(chainId, clinicId);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<String, Employee> queryEmployeeByChainId(String chainId) {
        return dEmployee.queryByChainId(chainId);
    }

    /**
     * @param parentId -
     * @return -
     */
    public Map<String, Organ> queryOrganByParentId(String parentId) {
        return dOrgan.queryByParentId(parentId);
    }

    /**
     * @param productId -
     * @return -
     */
    public V2Goods queryProduct(String productId) {
        return dGoods.query(productId);
    }

    /**
     * @param productIds -
     * @return -
     */
    public Map<String, V2Goods> queryProducts(Collection<String> productIds) {
        return dGoods.query(productIds);
    }

    /**
     * @param chainId    连锁id
     * @param productIds -
     * @return -
     */
    public Map<String, V2Goods> queryProducts(String chainId, Collection<String> productIds) {
        return dGoods.query(chainId, productIds);
    }

    /**
     * 根据chainId和batchid查询goodsstock信息
     *
     * @param chainId  连锁id
     * @param batchIds -
     * @return -
     */
    public Map<Long, V2GoodsStock> queryGoodsStocks(String chainId, Collection<String> batchIds) {
        return dGoods.queryGoodsStocks(chainId, batchIds);
    }

    /**
     * 根据chainId和batchid查询goodsstock信息
     *
     * @param chainId  连锁id
     * @param batchIds -
     * @return -
     */
    public Set<Long> queryGoodsStocksByExpiryDate(String chainId, String beginExpiryDate, String endExpiryDate) {
        List<V2GoodsStock> v2GoodsStocks = dGoods.queryGoodsStocksByExpiryDate(chainId, beginExpiryDate, endExpiryDate);
        Set<Long> batchIds = new HashSet<>();
        if (v2GoodsStocks != null && !v2GoodsStocks.isEmpty()) {
            for (V2GoodsStock stock:v2GoodsStocks) {
                if (stock.getBatchId() != null) {
                    batchIds.add(stock.getBatchId());
                }
            }
        }
        return batchIds;
    }

    /**
     * 根据stockId查询goodStock信息
     *
     * @param stockIdSet 库存idSet
     * @return goodStockMap
     */
    public Map<Long, V2GoodsStock> queryGoodsStocksByIds(Collection<Long> stockIdSet) {
        return dGoods.queryGoodsStocksByIds(stockIdSet);
    }

    /**
     * 根据stockId查询goodStock信息
     *
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @param batchNo  生产批号
     * @return goodStockMap
     */
    public List<Long> queryGoodsStockIdsByBatchNo(String chainId, String clinicId, String batchNo) {
        return dGoods.queryGoodsStockIdsByBatchNo(chainId, clinicId, batchNo);
    }

    /**
     * 根据chainId、clinicId和时间查询goodStock信息
     *
     * @param chainId   连锁id
     * @param clinicId  门店id
     * @param beginDate 开始时间
     * @param endDate   结束时间
     * @return goodStockMap
     */
    public Map<Long, V2GoodsStock> queryGoodsStocksByChainIdAndClinicIdAndTime(String chainId, String clinicId, String beginDate, String endDate) {
        return dGoods.queryGoodsStocksByChainIdAndClinicIdAndTime(chainId, clinicId, beginDate, endDate);
    }

    /**
     * @param productIds -
     * @return -
     */
    public Map<String, V2Goods> queryDedicateProducts(List<String> productIds) {
        return dGoods.query(productIds);
    }

    /**
     * @param productIds -
     * @return -
     */
    public Map<String, V2Goods> queryProducts(Set<String> productIds) {
        return dGoods.query(productIds);
    }

    /**
     * @param productIds -
     * @return -
     */
    public List<V2Goods> queryProductsByParams(String chainId, GoodsParams params) {
        //chainId必传避免查询超时
        if (chainId == null) {
            return new ArrayList<>();
        }
        return dGoods.queryProductsByParams(chainId, params);
    }

    /**
     * @param
     * @param chainId      连锁id
     * @param clinicId     子店ID
     * @param pharmacyType 药房类型 0 实体药房 1 空中药房 2 虚拟药房
     * @return
     * @return java.util.Map<java.lang.Integer, java.lang.String>
     * @Description: 根据门店ID获取对应的多药房名称
     * @Author: zs
     * @Date: 2022/10/18 11:46
     */
    public Map<String, Map<Integer, String>> queryPharmacyNameByNo(String chainId, String clinicId, Integer pharmacyType) {
        return dGoods.queryPharmacyNameByNo(chainId, clinicId, pharmacyType);
    }

    /**
     * @param
     * @param chainId      连锁id
     * @param clinicId     子店ID
     * @param pharmacyType 药房类型 0 实体药房 1 空中药房 2 虚拟药房
     * @return
     * @return java.util.Map<java.lang.Integer, java.lang.String>
     * @Description: 根据门店ID获取对应的多药房名称
     * @Author: zs
     * @Date: 2022/10/18 11:46
     */
    public Map<String, Map<Integer, String>> queryPharmacyNameByChainIdAndClinicId(String chainId, String clinicId, Integer pharmacyType) {
        return dGoods.queryPharmacyNameByChainIdAndClinicId(chainId, clinicId, pharmacyType);
    }

    /**
     * @param
     * @param chainId 连锁id
     * @param ids     利润分类ids
     * @return
     * @return java.util.Map<java.lang.Integer, java.lang.String>
     * @Description: 根据chainId和利润分类ids获取对应名称
     * @Author: zs
     * @Date: 2022/10/18 11:46
     */
    public Map<Long, V2GoodsProfitCategoryType> queryGoodsProfitCategoryTypeByChainIdAndId(String chainId, Collection<Long> ids) {
        return dGoods.queryGoodsProfitCategoryTypeByChainIdAndId(chainId, ids);
    }

    /**
     * @param organId 门店id
     * @return -
     */
    public Organ queryOrgan(String organId) {
        return dOrgan.query(organId);
    }

    /**
     * @param ids 门店ids
     * @return -
     */
    public Map<String, String> queryOrganByIds(Set<String> ids) {
        List<Organ> organs = dOrgan.queryByIds(ids);
        Map<String, String> organMap = new HashMap<>();
        for (Organ param : organs) {
            organMap.put(param.getId(), param.getShortName() == null || param.getShortName().trim().equals("")
                    ? param.getName() : param.getShortName());
        }
        return organMap;
    }

    /**
     * @param ids 门店ids
     * @return -
     */
    public Map<String, Organ> queryOrgansByIds(Set<String> ids) {
        List<Organ> organs = dOrgan.queryByIds(ids);
        Map<String, Organ> organMap = new HashMap<>();
        for (Organ organ : organs) {
            organMap.put(organ.getId(), organ);
        }
        return organMap;
    }

    /**
     * @param patientId -
     * @return -
     */
    public V2Patient queryPatient(String patientId) {
        return dPatient.query(patientId);
    }

    /**
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public List<V2Patient> queryPatient(String chainId, HashSet<String> patientIds) {
        return new ArrayList<>(dPatient.query(chainId, patientIds).values());
    }

    /**
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public Map<String, V2Patient> queryPatientAndMemberType(String chainId, Collection<String> patientIds) {
        Map<String, V2Patient> res = new HashMap<>();
        if (patientIds.size() > CommonConstants.NUMBER_TWO_THOUSAND) {
            //2021-01-05 需要考虑到参数为Set
            List<String> patientIds1 = new ArrayList<>(patientIds);

            int toIndex = CommonConstants.NUMBER_TWO_THOUSAND;
            for (int i = 0; i < patientIds1.size(); i += CommonConstants.NUMBER_TWO_THOUSAND) {
                if (i + CommonConstants.NUMBER_TWO_THOUSAND > patientIds1.size()) {
                    toIndex = patientIds.size() - i;
                }

                List<String> newPatientIds = patientIds1.subList(i, toIndex + i);
                Map<String, V2Patient> m = dPatient.queryPatientAndMemberType(chainId, newPatientIds);
                res.putAll(m);
            }
        } else {
            Map<String, V2Patient> m2 = dPatient.queryPatientAndMemberType(chainId, patientIds);
            res.putAll(m2);
        }
        return res;
    }

    @Deprecated
    public Map<String, V2Patient> queryPatient(String chainId, Collection<String> patientIds) {
        return getStringV2PatientMap(chainId, patientIds, null);
    }


    /**
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public Map<String, V2Patient> queryPatient(String chainId, Collection<String> patientIds, Integer enablePatientMobile) {
        return getStringV2PatientMap(chainId, patientIds, enablePatientMobile);
    }

    /**
     * 分批获取诊断相关数据，限制每批数据条数100条
     *
     * @param patientOrderIds patient_order_id
     * @return map -> key:patient_order_id
     */
    public Map<String, V2OutpatientMedicalRecord> batchQueryOutpatientMedicalRecord(Collection<String> patientOrderIds) {
        return batchQueryOutpatientMedicalRecord(patientOrderIds, 100);
    }

    /**
     * 分批获取诊断相关数据
     *
     * @param patientOrderIds patient_order_id
     * @param batchSize       每批数据条数
     * @return map -> key:patient_order_id
     */
    public Map<String, V2OutpatientMedicalRecord> batchQueryOutpatientMedicalRecord(Collection<String> patientOrderIds, int batchSize) {
        List<List<String>> partitionPatientOrderIds = CollectionUtils.splitCollection(patientOrderIds, batchSize);
        Map<String, V2OutpatientMedicalRecord> result = new HashMap<>();
        for (Collection<String> subPatientOrderIds : partitionPatientOrderIds) {
            Map<String, V2OutpatientMedicalRecord> subMap = dimensionOutpatient.queryOutpatientMedicalRecord(subPatientOrderIds);
            result.putAll(subMap);
        }
        return result;
    }


    private Map<String, V2Patient> getStringV2PatientMap(String chainId, Collection<String> patientIds, Integer enablePatientMobile) {
        Map<String, V2Patient> res = new HashMap<>();
        if (patientIds.size() > CommonConstants.NUMBER_TWO_THOUSAND) {
//            List<String> patientIds1 = (List<String>) patientIds;
            //2021-01-05 需要考虑到参数为Set
            List<String> patientIds1 = new ArrayList<>(patientIds);

            int toIndex = CommonConstants.NUMBER_TWO_THOUSAND;
            for (int i = 0; i < patientIds1.size(); i += CommonConstants.NUMBER_TWO_THOUSAND) {
                if (i + CommonConstants.NUMBER_TWO_THOUSAND > patientIds1.size()) {
                    toIndex = patientIds.size() - i;
                }

                List<String> newPatientIds = patientIds1.subList(i, toIndex + i);
                Map<String, V2Patient> m = dPatient.queryAndCache(chainId, newPatientIds, enablePatientMobile);
                res.putAll(m);

            }
        } else {
            Map<String, V2Patient> m2 = dPatient.queryAndCache(chainId, patientIds, enablePatientMobile);
            res.putAll(m2);
        }

        return res;
    }

    /**
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public Map<String, V2PatientExtend> queryPatientExtend(String chainId, Collection<String> patientIds) {
        Map<String, V2PatientExtend> res = new HashMap<>();
        if (patientIds.size() > CommonConstants.NUMBER_TWO_THOUSAND) {
//            List<String> patientIds1 = (List<String>) patientIds;
            //2021-01-05 需要考虑到参数为Set
            List<String> patientIds1 = new ArrayList<>(patientIds);

            int toIndex = CommonConstants.NUMBER_TWO_THOUSAND;
            for (int i = 0; i < patientIds1.size(); i += CommonConstants.NUMBER_TWO_THOUSAND) {
                if (i + CommonConstants.NUMBER_TWO_THOUSAND > patientIds1.size()) {
                    toIndex = patientIds.size() - i;
                }

                List<String> newPatientIds = patientIds1.subList(i, toIndex + i);
                Map<String, V2PatientExtend> m = dPatient.queryAndCachePatientExtend(chainId, newPatientIds);
                res.putAll(m);

            }
        } else {
            Map<String, V2PatientExtend> m2 = dPatient.queryAndCachePatientExtend(chainId, patientIds);
            res.putAll(m2);
        }

        return res;
    }

    /**
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public Map<String, V2Patient> queryPatientAndNoCache(String chainId, Collection<String> patientIds) {
        return dPatient.query(chainId, patientIds);
    }

    /**
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public Map<String, V2PatientClinic> selectPatientClinicByPatientIds(String chainId, String clinicId, List<String> patientIds) {
        return dPatient.selectPatientClinicByPatientIds(chainId, clinicId, patientIds);
    }

    /**
     * 根据连锁id以及患者ids查询patient_clinic信息， 返回map的key：patientId + '-' + clinicId
     *
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public Map<String, V2PatientClinic> queryPatientClinicByPatientIds(String chainId, String clinicId, List<String> patientIds) {
        return dPatient.queryPatientClinicByPatientIds(chainId, clinicId, patientIds);
    }

    /**
     * 根据连锁id以及患者ids查询patient_clinic信息
     *
     * @param chainId    连锁id
     * @param patientIds -
     * @return -
     */
    public Map<String, List<V2PatientClinic>> queryPatientClinicByPatientIds(String chainId, List<String> patientIds) {
        return dPatient.queryPatientClinicByPatientIds(chainId, patientIds);
    }

    /**
     * @param type    -
     * @param subType -
     * @return -
     */
    public String queryPaySubTypeText(Integer type, Integer subType) {
        return DimensionPaySubType.query(type, subType);
    }

    /**
     * @param type -
     * @return -
     */
    public String queryPayTypeText(Integer type) {
        return dPayType.query(type);
    }

    /**
     * 根据连锁id或门店id查询支付类型
     *
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return 支付类型
     */
    public Map<Integer, String> queryPayTypeTextByChainIdOrClinicId(String chainId, String clinicId) {
        return dPayType.selectPayConfigNameByChainIdOrClinicId(chainId, clinicId);
    }

    /**
     * 门店id查询支付类型
     *
     * @param clinicId 门店id
     * @return 支付类型
     */
    public Map<Integer, String> selectPayConfigNameByClinicId(String clinicId) {
        return dPayType.selectPayConfigNameByClinicId(clinicId);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<Integer, V2ChargePayModeConfig> queryUsingPayModeByChainId(String chainId) {
        return dPayType.selectUsingPayConfigByOrganId(chainId);
    }

    /**
     * @param type -
     * @return -
     */
    public V2GoodsCustomType queryProductCustomTypeText(Integer type) {
        return dCustomType.query(type);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<Integer, V2GoodsCustomType> queryProductCustomTypeTextByChainId(String chainId) {
        return dCustomType.queryByChainId(chainId);
    }

    /**
     * @param type -
     * @return -
     */
    public String queryProductTypeText(Byte type) {
        return DimensionProductType.queryType(type);
    }

    /**
     * @param fee1 -
     * @return -
     */
    public String queryProductClassifyLevel1(String fee1, String hisType) {
        return DimensionProductType.queryClassifyLevel1(fee1, hisType);
    }

    /**
     * @param fee1 -
     * @return -
     */
    public String queryProductClassifyLevel1(String chainId, String fee1, String hisType) {
        String reslut = DimensionProductType.queryClassifyLevel1(fee1, hisType);
        if (reslut != null && specialRegisterProperties.getChain().contains(chainId)) {
            if (reslut.equals(specialRegisterProperties.getRegisterTargetValue())) {
                reslut = specialRegisterProperties.getRegisterResultValue();
            }
        }
        return reslut;
    }

    /**
     * @param type -
     * @return -
     */
    public String queryOutpatientTypeText(Byte type) {
        return DimensionOutpatientType.query(type);
    }

    /**
     * @param membertypeIds -
     * @return -
     */
    public List<V2PatientMemberType> queryMemberType(HashSet<String> membertypeIds) {
        return dMemberType.queryMemberType(membertypeIds);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public Map<String, V2PatientMemberType> queryMemberType(String chainId, String clinicId) {
        return dMemberType.queryMemberType(chainId, clinicId);
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public Map<String, V2PatientMemberType> queryMemberTypeExcludeDelete(String chainId, String clinicId) {
        return dMemberType.queryMemberTypeExcludeDelete(chainId, clinicId);
    }

    /**
     * @param param param
     * @return List<String>
     */
    public List<String> queryMemberIdsByCreatedClinidAndMemberType(MemberParam param) {
        return dMemberType.queryMemberIdsByCreatedClinidAndMemberType(param);
    }

    /**
     * @param empSet -
     * @return -
     */
    @Deprecated
    public List<Employee> queryEmployeeByIds(HashSet<String> empSet) {
        return dEmployee.queryByIdsAndCache(empSet);
    }

    /**
     * @param empSet -
     * @return -
     */
    @Deprecated
    public Map<String, Employee> queryEmployeeByIds(Collection<String> empSet) {
        List<Employee> list = dEmployee.queryByIdsAndCache(empSet);
        Map<String, Employee> employeeMap = new HashMap<>();
        for (Employee e : list) {
            if (e != null && e.getId() != null) {
                employeeMap.put(e.getId(), e);
            }
        }
        return employeeMap;
    }

    /**
     * @param chainId -
     * @param empSet  -
     * @return -
     */
    public Map<String, Employee> queryEmployeeByChainAndIds(String chainId, Collection<String> empSet) {
        List<Employee> list = dEmployee.queryByChainAndIdsAndCache(chainId, empSet);
        Map<String, Employee> employeeMap = new HashMap<>();
        for (Employee e : list) {
            if (e != null && e.getId() != null) {
                employeeMap.put(e.getId(), e);
            }
        }
        return employeeMap;
    }

    /**
     * @param empSet -
     * @return -
     */
    @Deprecated
    public Map<String, String> queryEmployeeNameByIds(Set<String> empSet) {
        List<Employee> list = dEmployee.queryByIdsAndCache(empSet);
        Map<String, String> employeeMap = new HashMap<>();
        for (Employee e : list) {
            if (e != null && e.getId() != null) {
                employeeMap.put(e.getId(), e.getName());
            }
        }
        return employeeMap;
    }

    /**
     * @param chainId -
     * @param empSet  -
     * @return -
     */
    public Map<String, String> queryEmployeeNameByChainAndIds(String chainId, Set<String> empSet) {
        List<Employee> list = dEmployee.queryByChainAndIdsAndCache(chainId, empSet);
        Map<String, String> employeeMap = new HashMap<>();
        for (Employee e : list) {
            if (e != null && e.getId() != null) {
                employeeMap.put(e.getId(), e.getName());
            }
        }
        return employeeMap;
    }

    /**
     * @param payModeSet -
     * @return -
     */
    public Map<Integer, String> queryPayModeByIds(HashSet<Integer> payModeSet) {
        return dPayType.query(payModeSet);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<String, V2PatientorderVisitSource> queryVisitSourceByChainId(String chainId) {
        List<V2PatientorderVisitSource> list = dVisitSource.query(chainId);
        Map<String, V2PatientorderVisitSource> map = new HashMap<>();
        list.stream().forEach(vs -> {
            map.put(vs.getId(), vs);
        });
        return map;
    }

    /**
     * @param id -
     * @return -
     */
    public V2Patientorder queryPatientorderById(String id) {
        return dPatientorder.query(id);
    }

    /**
     * @param chainId 连锁id
     * @param ids     -
     * @return -
     */
    public Map<String, V2Patientorder> queryPatientorderByIds(String chainId, Collection<String> ids) {
        return dPatientorder.query(chainId, ids);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public List<FamilyDoctorConfig> queryFamilyDoctorConfig(String chainId) {
        return dFamilyDoctorConfig.queryByChainId(chainId);
    }

    /**
     * @param serviceIds -
     * @return -
     */
    public List<FamilyDoctorConfig> queryFamilyDoctorConfigByIds(HashSet<String> serviceIds) {
        return dFamilyDoctorConfig.queryFamilyDoctorConfigByServiceIds(serviceIds);
    }

    /**
     * @return -
     */
    public Map<String, V2GoodsType> queryV2GoodsTypeMap() {
        return dProductType.queryTypeSort();
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<String, V2PatientSourceType> queryPatientSourceType(String chainId) {
        return dSourceType.querySourceType(chainId);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<String, String> queryPatientSourceTypeIdByName(String chainId) {
        return dSourceType.querySourceTypeIdByName(chainId);
    }

    /**
     * @param chainId  连锁id
     * @param parentId -
     * @return -
     */
    public Map<String, V2PatientSourceType> queryPatientSourceTypeByParentId(String chainId, String parentId) {
        return dSourceType.querySourceTypeByParentId(chainId, parentId);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<String, List<String>> selectPatientTagByChainId(String chainId) {
        Map<String, List<String>> map = new HashMap<>();
        List<V2PatientTag> v2PatientTags = dSourceType.selectPatientTagByChainId(chainId);
        if (v2PatientTags != null && v2PatientTags.size() > 0) {
            v2PatientTags.forEach(x -> {
                if (map.containsKey(x.getPatientId())) {
                    map.get(x.getPatientId()).add(x.getName());
                } else {
                    List<String> strings = new ArrayList<>();
                    strings.add(x.getName());
                    map.put(x.getPatientId(), strings);
                }
            });
        }
        return map;
    }

    /**
     * @param chainId 连锁id
     * @param tags    tagIds
     * @return -
     */
    public Map<String, List<String>> selectPatientTagByIds(String chainId, Set<String> tags, List<String> patientIds) {
        Map<String, List<String>> map = new HashMap<>();
        if (tags == null || tags.size() == 0) {
            return map;
        }
        List<V2PatientTag> v2PatientTags = new ArrayList<>();
        if (tags.size() > 20000 || patientIds.size() > 20000) {
            v2PatientTags = dSourceType.selectPatientTagByChainId(chainId);
        } else {
            v2PatientTags = dSourceType.selectPatientTagByIds(chainId, tags, patientIds);
        }
        if (v2PatientTags != null && v2PatientTags.size() > 0) {
            v2PatientTags.forEach(x -> {
                if (map.containsKey(x.getPatientId())) {
                    map.get(x.getPatientId()).add(x.getName());
                } else {
                    List<String> strings = new ArrayList<>();
                    strings.add(x.getName());
                    map.put(x.getPatientId(), strings);
                }
            });
        }
        return map;
    }

    /**
     * @param chainId 连锁id
     * @param tags    tagIds
     * @return -
     */
    public Map<String, List<String>> selectPatientTagByIds(String chainId, Collection<String> patientIds) {
        Map<String, List<String>> map = new HashMap<>();
        if (chainId == null) {
            return map;
        }
        List<V2PatientTag> v2PatientTags = new ArrayList<>();
        if (patientIds == null || patientIds.isEmpty()) {
            v2PatientTags = dSourceType.selectPatientTagByChainId(chainId);
        } else {
            // 按照患者ID的大小1000分批次查询
            int batchSize = CommonConstants.NUMBER_ONE_THOUSAND;
            List<String> patientIdList = new ArrayList<>(patientIds);

            for (int i = 0; i < patientIdList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, patientIdList.size());
                List<String> batchPatientIds = new ArrayList<>(patientIdList.subList(i, endIndex));
                List<V2PatientTag> batchTags = dSourceType.selectPatientTagByIds(chainId, null, batchPatientIds);
                if (batchTags != null && !batchTags.isEmpty()) {
                    v2PatientTags.addAll(batchTags);
                }
            }
        }
        if (v2PatientTags != null && v2PatientTags.size() > 0) {
            v2PatientTags.forEach(x -> {
                if (map.containsKey(x.getPatientId())) {
                    map.get(x.getPatientId()).add(x.getName());
                } else {
                    List<String> strings = new ArrayList<>();
                    strings.add(x.getName());
                    map.put(x.getPatientId(), strings);
                }
            });
        }
        return map;
    }

    /**
     * @param chainId      连锁id
     * @param clinicId     门店id
     * @param departmentId 部门id
     * @return -
     */
    public List<String> queryEmployeeIdsByDepartmentId(String chainId, String clinicId, String departmentId) {
        return dEmployee.queryEmployeeIdsByDepartmentId(chainId, clinicId, departmentId);
    }

    /**
     * @param types -
     * @return -
     */
    public Map<Integer, V2GoodsCustomType> queryCustomTypeByIds(Set<Integer> types) {
        return dCustomType.queryByIds(types);
    }

    /**
     * @param chainId -
     * @return -
     */
    public List<V2GoodsCustomType> queryCustomTypeByIdsAndChainId(String chainId) {
        return dCustomType.queryCustomTypeByIdsAndChainId(chainId);
    }

    /**
     * @param types -
     * @return -
     */
    public Map<Integer, V2GoodsSysType> selectGoodsSysTypeByIds(Set<Integer> types) {
        return dSystype.queryByIds(types);
    }

    /**
     * @return -
     */
    public Map<Integer, V2GoodsSysType> selectGoodsSysTypeAll() {
        return dSystype.queryAll();
    }

    /**
     * @return -
     */
    public Map<Integer, V2GoodsSysType> selectGoodsSysTypeByIsSell(String hisType) {
        Map<Integer, V2GoodsSysType> sysTypeMap = dSystype.selectGoodsSysTypeByIsSell();
        for (Map.Entry<Integer, V2GoodsSysType> sysTypeEntry : sysTypeMap.entrySet()) {
            if (!StrUtil.isBlank(hisType)
                    && hisType.equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)
                    && Objects.equals(sysTypeEntry.getKey(), GoodsFirstClassifyEnum.TRADITIONAL_CHINESE_MEDICINES_PREPARED_IN_READY_TO_USE_FORMS.getTypeId())) {
                sysTypeEntry.getValue().setName("配方饮片");
            }
        }
        return sysTypeMap;
    }

    /**
     * 根据供应商ids查询供应商
     *
     * @param supplierIds 供应商ids
     * @return -
     */
    public Map<String, V2GoodsSupplier> querySupplierByIds(Set<String> supplierIds) {
        return dSupplier.queryByIds(supplierIds);
    }

    /**
     * @param chainId      chainId
     * @param pharmacyType pharmacyType
     * @return List<V2GoodsSupplier>
     */
    public List<V2GoodsSupplier> querySupplierByChainId(String chainId, Integer pharmacyType) {
        return dSupplier.queryByChainId(chainId, pharmacyType);
    }

    /**
     * @param outpatientSheetIds -
     * @return -
     */
    public List<V2OutpatientPrescriptionForm> queryPrescriptionForms(List<String> outpatientSheetIds) {
        return dPrescription.queryFormsByOutpatientIds(outpatientSheetIds);
    }

    /**
     * @param outpatientSheetIds -
     * @return -
     */
    public List<V2OutpatientPrescriptionFormItem> queryPrescriptionFormItems(List<String> outpatientSheetIds) {
        return dPrescription.queryFormsItemByOutpatientIds(outpatientSheetIds);
    }

    public List<DepartmentEmployee> queryDepartmentEmployee(String chainId, String clinicId) {
        return dEmployee.queryDepartmentEmployee(chainId, clinicId);
    }

    public List<ClinicEmployee> queryClinicEmployee(String chainId, String clinicId) {
        return dEmployee.queryClinicEmployee(chainId, clinicId);
    }

    public Map<String, ClinicEmployee> queryMedicalAssistanceByEmpIds(String chainId, String clinicId, Collection<String> ids) {
        return dEmployee.queryMedicalAssistanceByEmpIds(chainId, clinicId, ids);
    }

    /**
     * @param key -
     * @return -
     */
    public String queryPrescriptionFormFreq(String key) {
        return DimensionPrescription.getFreq(key);
    }

    /**
     * @param chainId          连锁id
     * @param medicalRecordIds -
     * @return -
     */
    public Map<String, String> queryMedicalDiagnosis(String chainId, List<String> medicalRecordIds) {
        Map<String, String> res = new HashMap<>();
        if (medicalRecordIds.size() > CommonConstants.NUMBER_TWO_THOUSAND) {
            int toIndex = CommonConstants.NUMBER_TWO_THOUSAND;
            for (int i = 0; i < medicalRecordIds.size(); i += CommonConstants.NUMBER_TWO_THOUSAND) {
                if (i + CommonConstants.NUMBER_TWO_THOUSAND > medicalRecordIds.size()) {
                    toIndex = medicalRecordIds.size() - i;
                }

                List<String> newMedicalRecordIds = medicalRecordIds.subList(i, toIndex + i);
                Map<String, String> m = dMedicalRecord.queryMedicalDiagnosis(chainId, newMedicalRecordIds);
                res.putAll(m);

            }
        } else {
            Map<String, String> m2 = dMedicalRecord.queryMedicalDiagnosis(chainId, medicalRecordIds);
            res.putAll(m2);
        }


        return res;
    }

    /**
     * @param chainId         连锁id
     * @param therapySheetIds -
     * @return -
     */
    public Map<Long, String> queryMedicalDiagnosisFromTherapySheet(String chainId, List<Long> therapySheetIds) {
        return dMedicalRecord.queryMedicalDiagnosisFromTherapySheet(chainId, therapySheetIds);
    }

    /**
     * @param id -
     * @return -
     */
    public V2PromotionCard queryPromotionCardById(String id) {
        return dPromotion.queryCardById(id);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public V2PatientPointsConfig queryPointsConfigByChainId(String chainId) {
        return dPatientPoints.queryPointsConfigByChainId(chainId);
    }

    /**
     * @param chainId 连锁id
     * @param type    -
     * @return -
     */
    public Map<Long, V2Promotion> queryPromotion(String chainId, Byte type) {
        return dPromotion.query(chainId, type);
    }

    /**
     * @param chainId 连锁id
     *                根据chainId查询优惠信息，不携带type
     * @return -
     */
    public Map<Long, V2Promotion> queryPromotion(String chainId) {
        return dPromotion.query(chainId);
    }

    /**
     * @param chainId 连锁id
     *                根据chainId查询优惠信息，不携带type
     * @return -
     */
    public Map<String, V2PromotionMain> queryPromotionMain(String chainId) {
        return dPromotion.queryPromotionMain(chainId);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<String, V2PromotionDiscount> queryPromotionDiscount(String chainId) {
        return dPromotion.queryDiscount(chainId);
    }

    /**
     * @param id -
     * @return -
     */
    public String queryPointsAction(Byte id) {
        return dPromotion.queryPointsAction(id);
    }

    /**
     * @param db       db
     * @param param    param
     * @param goodsIds goodsIds
     * @return Map<String, V2GoodsClassify>
     */
    public Map<String, V2GoodsClassify> queryGoodsInfo(String db, GoodsInfoParam param, List<String> goodsIds,
                                                       ArrayList<Long> feeTypeIdsList) {
        Set<String> goodsIdSet;
        if (!CollUtil.isEmpty(goodsIds)) {
            goodsIdSet = CollUtil.newHashSet(goodsIds);
        } else {
            goodsIdSet = CollUtil.newHashSet();
        }
        return dGoods.queryGoodsInfo(db, param, goodsIdSet, feeTypeIdsList);
    }

    /**
     * @param db      db
     * @param chainId chainId
     * @return Map<String, V2GoodsClassify>
     */
    public Map<Long, V2GoodsFeeType> queryGoodsFeeTypeInfo(String db, String chainId) {
        return dGoods.queryGoodsFeeTypeInfo(db, chainId);
    }

    /**
     * @param db      db
     * @param chainId chainId
     * @return Map<String, V2GoodsClassify>
     */
    public Map<Long, V2GoodsFeeType> queryCommissionGoodsFeeTypeInfo(String db, String chainId) {
        return dGoods.queryCommissionGoodsFeeTypeInfo(db, chainId);
    }

    /**
     * @param db       db
     * @param param    param
     * @param goodsIds goodsIds
     * @return Long
     */
    public Long queryGoodsInfoCount(String db, GoodsInfoParam param, List<String> goodsIds,
                                    List<Long> feeTypeIdsList) {
        return dGoods.queryGoodsInfoCount(db, param, goodsIds, feeTypeIdsList);
    }

    /**
     * @param db         库名
     * @param chainId    连锁id
     * @param clinicId   门店id
     * @param supplierId 供应商id
     * @return -
     */
    public List<InventoryFee> queryGoodsClassfi(String db, String chainId, String clinicId, String supplierId) {
        return dGoods.query(db, chainId, clinicId, supplierId);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<String, V2PromotionCard> queryPromotionCardBychainId(String chainId) {
        return dPromotion.queryPromotionCardByChain(chainId);
    }

    /**
     * @param chainId 连锁id
     * @return -
     */
    public Map<Long, V2PromotionCard> selectPromotionCardByChainIdJoinCardPatient(String chainId) {
        return dPromotion.selectPromotionCardByChainIdJoinCardPatient(chainId);
    }

    /**
     * @param employeeId     用户id
     * @param tableKey       表key
     * @param chainViewMode  连锁视图 0 正常视图；1：单店视图
     * @param clinicNodeType clinicNodeType
     * @param excludeHidden  是否排除隐藏字段：null或0 不排除；1：排除
     * @return -
     */
    public List<TableHeaderEmployeeItem> getTableHeaderEmployeeItems(String employeeId,
                                                                     String tableKey,
                                                                     Integer chainViewMode,
                                                                     Integer clinicNodeType,
                                                                     Integer excludeHidden) {
        JsonNode jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(employeeId,
                tableKey, envType, chainViewMode, clinicNodeType, null, excludeHidden, employeeId, null, null, null, null);
        String dump = RevenueChargedDailyHandler.dump(jsonNode);
        List<TableHeaderEmployeeItem> list = JSON.parseArray(dump, TableHeaderEmployeeItem.class);
        list.sort(new Comparator<TableHeaderEmployeeItem>() {
            @Override
            public int compare(TableHeaderEmployeeItem o1, TableHeaderEmployeeItem o2) {
                if (o1.getPosition() != null && o2.getPosition() != null) {
                    return o1.getPosition() - o2.getPosition();
                } else {
                    return 0;
                }
            }
        });

        return list;
    }

    /**
     * @param employeeId     用户id
     * @param tableKey       表key
     * @param chainViewMode  连锁视图 0 正常视图；1：单店视图
     * @param clinicNodeType clinicNodeType
     * @param excludeHidden  是否排除隐藏字段：null或0 不排除；1：排除
     * @param hisType        0:普通诊所， 1：口腔诊所，2：眼科诊所，100：医院
     * @return -
     */
    public List<TableHeaderEmployeeItem> getTableHeaderEmployeeItems(String employeeId,
                                                                     String tableKey,
                                                                     Integer chainViewMode,
                                                                     Integer clinicNodeType,
                                                                     Integer excludeHidden,
                                                                     Integer hisType) {
        JsonNode jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(employeeId,
                tableKey, envType, chainViewMode, clinicNodeType, hisType, excludeHidden, employeeId, null, null, null, null);
        String dump = RevenueChargedDailyHandler.dump(jsonNode);
        List<TableHeaderEmployeeItem> list = JSON.parseArray(dump, TableHeaderEmployeeItem.class);
        list.sort(new Comparator<TableHeaderEmployeeItem>() {
            @Override
            public int compare(TableHeaderEmployeeItem o1, TableHeaderEmployeeItem o2) {
                if (o1.getPosition() != null && o2.getPosition() != null) {
                    return o1.getPosition() - o2.getPosition();
                } else {
                    return 0;
                }
            }
        });

        return list;
    }

    /**
     * @param employeeId     用户id
     * @param tableKey       表key
     * @param chainViewMode  连锁视图 0 正常视图；1：单店视图
     * @param clinicNodeType clinicNodeType
     * @param excludeHidden  是否排除隐藏字段：null或0 不排除；1：排除
     * @param hisType        0:普通诊所， 1：口腔诊所，2：眼科诊所，100：医院
     * @param clinicId       子店id
     * @return -
     */
    public List<TableHeaderEmployeeItem> getTableHeaderEmployeeItems(String employeeId,
                                                                     String tableKey,
                                                                     Integer chainViewMode,
                                                                     Integer clinicNodeType,
                                                                     Integer excludeHidden,
                                                                     Integer hisType,
                                                                     String clinicId) {
        JsonNode jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(employeeId,
                tableKey, envType, chainViewMode, clinicNodeType, hisType, excludeHidden, employeeId, clinicId, null, null, null);
        String dump = RevenueChargedDailyHandler.dump(jsonNode);
        List<TableHeaderEmployeeItem> list = JSON.parseArray(dump, TableHeaderEmployeeItem.class);
        list.sort(new Comparator<TableHeaderEmployeeItem>() {
            @Override
            public int compare(TableHeaderEmployeeItem o1, TableHeaderEmployeeItem o2) {
                if (o1.getPosition() != null && o2.getPosition() != null) {
                    return o1.getPosition() - o2.getPosition();
                } else {
                    return 0;
                }
            }
        });

        return list;
    }

    /**
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return -
     */
    public Map<Integer, AchievementDispensingProcessType> queryDispensingProcessType(String chainId, String clinicId) {
        return dimensionDispensing.queryDispensingProcessType(chainId, clinicId);
    }

    /**
     * @param db       db
     * @param param    param
     * @param goodsIds goodsIds
     * @return Map<String, V2GoodsClassify>
     */
    public Map<String, V2GoodsClassify> queryGoodsInfoPrice(String db, GoodsInfoParam param, List<String> goodsIds) {
        return dGoods.queryGoodsInfoPrice(db, param, goodsIds);
    }

    /**
     * @param feeType1Id -
     * @return -
     */
    public Integer queryProductGoodsTypeIdByFeeType1Id(String feeType1Id) {
        return DimensionProductType.queryProductGoodsTypeIdByFeeType1Id(feeType1Id);
    }

    /**
     * @param chainId  -
     * @param clinicId -
     * @return -
     */
    public Map<String, String> queryWardNameByChainIdAndClinicId(String chainId, String clinicId) {
        return dimensionWard.queryWardNameByChainIdAndClinicId(chainId, clinicId);
    }

    /**
     * 根据套餐id查询子项数据
     *
     * @param chainId         -
     * @param clinicId        -
     * @param composeGoodsIds 套餐母项ID
     * @return -
     */
    public Map<String, List<V2GoodsCompose>> selectComposeDetail(String chainId,
                                                                 String clinicId,
                                                                 Collection<String> composeGoodsIds) {
        return dGoods.selectComposeDetail(chainId, clinicId, composeGoodsIds);
    }

    /**
     * 根据goods信息查询goodsIdList
     *
     * @param db       库名
     * @param param    goods信息param
     * @param goodsIds 查询的goodsIdList
     * @return goodsIdList
     */
    public List<String> queryGoodsIdList(String db, GoodsInfoParam param, List<String> goodsIds) {
        return dGoods.queryGoodsIdList(db, param, goodsIds);
    }

    /**
     * @param chainId -
     * @return -
     */
    public Map<Long, V2GoodsFeeType> selectAdviceFeeType(String chainId) {
        return dGoods.selectAdviceFeeType(chainId);
    }

    /**
     * @return -
     */
    public Map<String, V2GoodsSysType> selectSysFeeType() {
        return dGoods.selectSysFeeType();
    }

    /**
     * 根据连锁id以及活动id查询活动信息
     *
     * @param chainId        连锁id
     * @param referralIdList 活动idList
     * @return 活动信息map
     */
    public Map<String, V2PromotionReferral> queryPromotionReferralByIds(String chainId, Set<Long> referralIdList) {
        return dimensionPromotionReferral.queryPromotionReferralByIds(chainId, referralIdList);
    }

    public Map<Long, WardArea> queryWardAreas(String chainId, String cliniId, Set<Long> wardAreaIds) {
        return hisWard.selectHisWardArea(chainId, cliniId, wardAreaIds);
    }

    /**
     * 根据医嘱ids查询医嘱信息
     *
     * @param chainId 连锁id
     * @param cliniId 门店id
     * @param ids     医嘱ids
     * @return 医嘱信息list
     */
    public Map<Long, String> queryAdvice(String chainId, String cliniId, Set<Long> ids) {
        return dimensionHisAdvice.selectHisAdvice(chainId, cliniId, ids);
    }

    /**
     * 查询门店是否开启多药房
     *
     * @param db       db
     * @param chainId  chainId
     * @param clinicId clinicId
     * @return -
     */
    public Integer queryOpenPharmacyFlagNumberByOrgan(String db, String chainId, String clinicId) {
        return dGoods.queryOpenPharmacyFlagNumberByOrgan(db, chainId, clinicId);
    }


    /**
     * 根据主管医生护士查询住院单idSet
     *
     * @param chainId         连锁id
     * @param clinicId        门店id
     * @param managerDoctorId 主管医生id
     * @param managerNurseId  主管护士id
     * @return 住院单idSet
     */
    public Set<String> queryPatientOrderByEmployeeId(String chainId, String clinicId, String managerDoctorId, String managerNurseId) {
        return dPatientorder.queryPatientOrderByEmployeeId(chainId, clinicId, managerDoctorId, managerNurseId);
    }

    /**
     * 根据patientOrderIdSet查询住院单信息
     *
     * @param chainId         连锁id
     * @param patientOrderIds 住院单ids
     * @return 住院单map
     */
    public Map<String, V2PatientorderHospitalExtend> queryPatientOrderHospitalExtendById(String chainId, Set<String> patientOrderIds) {
        return dPatientorder.queryPatientOrderHospitalExtendById(chainId, patientOrderIds);
    }

    /**
     * 查询chargeSheet信息
     *
     * @param chainId          连锁id
     * @param chargeSheetIdSet chargeSheetId
     * @return chargeSheetMap
     */
    public Map<String, V2ChargeSheet> selectChargeSheetByIds(String chainId, Set<String> chargeSheetIdSet) {
        return dimensionChargeSheet.selectChargeSheetByIds(chainId, chargeSheetIdSet);
    }

    /**
     * 查询总部视图是否开启多库房
     *
     * @param db      db
     * @param chainId 连锁id
     * @return 总部视图是否开启多库房
     */
    public boolean queryIsOpenPharmacyByOrgan(String db, String chainId) {
        return dGoods.queryIsOpenPharmacyByOrgan(db, chainId);
    }

    /**
     * 根据连锁id以及机构客户ids查询机构客户信息
     *
     * @param chainId      连锁id
     * @param peOrganIdSet 机构客户ids
     * @return 机构客户信息map
     */
    public Map<Long, V2PatientOrgan> queryPatientOrgan(String chainId, Set<Long> peOrganIdSet) {
        return dimensionPatientOrgan.queryPatientOrgan(chainId, peOrganIdSet);
    }

    /**
     * 查询所有的经营范围
     *
     * @return 所有的经营范围
     */
    public Map<Integer, V2GoodsSysBusinessScope> queryAllGoodsBusinessScope() {
        return dimensionGoodsSysBusinessScope.queryAllGoodsBusinessScope();
    }

    /**
     * 根据电商网店ids查询网店信息
     *
     * @param mallIdSet 电商网店ids
     * @return 网店信息map
     */
    public Map<Long, V1EcMall> queryEcMallByIds(Set<Long> mallIdSet) {
        return dimensionEc.queryEcMallByIds(mallIdSet);
    }

    /**
     * 根据连锁id以及电商订单ids查询订单信息
     *
     * @param chainId  连锁id
     * @param orderSet 订单ids
     * @return 订单信息map
     */
    public Map<String, V1EcOrder> queryEcOrderByIds(String chainId, Set<String> orderSet) {
        return dimensionEc.queryEcOrderByIds(chainId, orderSet);
    }

    /**
     * 根据连锁id以及电商订单ids查询订单信息
     *
     * @param chainId         连锁id
     * @param ecGoodsSkuIdSet 订单商品skuIdSet
     * @return 订单信息map
     */
    public Map<Long, V1EcGoodsSku> queryEcGoodsSkuByIds(String chainId, Set<Long> ecGoodsSkuIdSet) {
        return dimensionEc.queryEcGoodsSkuByIds(chainId, ecGoodsSkuIdSet);
    }

    /**
     * 根据连锁id以及客户id查询客户信息
     *
     * @param chainId   -
     * @param customIds -
     * @return -
     */
    public Map<Long, ScrmCustomer> queryPromotionScrmCustomerByIds(String chainId, Set<Long> customIds) {
        return dimensionScrmCustomer.queryScrmCustomer(chainId, customIds);
    }

    /**
     * 根据连锁id以及渠道id查询渠道信息
     *
     * @param chainId    -
     * @param channelIds -
     * @return -
     */
    public Map<Long, ScrmChannelLiveCode> queryPromotionScrmChannelLiveCodeByIds(String chainId, Set<Long> channelIds) {
        return dimensionScrmChannel.queryScrmChannelLiveCode(chainId, channelIds);
    }

    /**
     * 根据连锁id以及渠道id查询渠道信息
     *
     * @param chainId -
     * @return -
     */
    public List<String> queryScrmPatientIds(String chainId, String clinicId, String employeeId) {
        return dimensionScrmChannel.queryScrmPatientIds(chainId, clinicId, employeeId);
    }

    /**
     * 根据连锁id以及渠道id查询渠道信息
     *
     * @param chainId -
     * @return -
     */
    public Integer queryScrmPatientByIds(String chainId, String clinicId, String employeeId, List<String> patientIds) {
        return dimensionScrmChannel.queryScrmPatientByIds(chainId, clinicId, employeeId, patientIds);
    }

    /**
     * 查询goods扩展字段
     *
     * @param db      db
     * @param chainId 连锁id
     * @param ids     goods扩展字段表ids
     * @return Map<String, V2GoodsClassify>
     */
    public Map<Long, V2GoodsClassify> selectV2GoodsSpuSpec(String db, String chainId, Collection<Long> ids) {
        return dGoods.selectV2GoodsSpuSpec(db, chainId, ids);
    }

    /**
     * 根据tagId查询商品标签
     *
     * @param chainId  连锁id
     * @param clinicId 为null就是查询总部
     * @param ids      -
     * @return -
     */
    public List<V2GoodsTag> queryGoodsTagByIds(String chainId, String clinicId, Collection<Long> ids) {
        return dGoods.queryGoodsTagByIds(chainId, clinicId, ids);
    }

    /**
     * 根据goodsId查询商品标签
     *
     * @param chainId  连锁id
     * @param clinicId 为null就是查询总部
     * @param ids      -
     * @return -
     */
    public Map<String, List<V2GoodsTag>> queryGoodsTagByGoodsIds(String chainId, String clinicId, Collection<String> ids) {
        List<V2GoodsTag> v2GoodsTags = dGoods.queryGoodsTagByGoodsIds(chainId, clinicId, ids);
        Map<String, List<V2GoodsTag>> reslut = new HashMap<>();
        if (v2GoodsTags != null && v2GoodsTags.size() > 0) {
            v2GoodsTags.forEach(x -> {
                if (reslut.containsKey(x.getGoodsId())) {
                    reslut.get(x.getGoodsId()).add(x);
                } else {
                    List<V2GoodsTag> tags = new ArrayList<>();
                    tags.add(x);
                    reslut.put(x.getGoodsId(), tags);
                }
            });
        }
        return reslut;
    }

    /**
     * 根据chainId查询商品标签
     *
     * @param chainId 连锁id
     * @return -
     */
    public List<StatSelectionResponse> queryGoodsTagByChainId(String chainId) {
        return dGoods.queryGoodsTagByChainId(chainId);
    }

    /**
     * 根据chainId以及patientOrderIds查询出院诊断
     *
     * @param chainId 连锁id
     * @param ids     门诊单ids
     * @return 出院诊断信息
     */
    public Map<String, V1EmrDiagnosis> queryDischargeDiagnosisByChainIdAndPatientOrderIds(String chainId, Collection<String> ids) {
        return dimensionEmrDiagnosis.queryDischargeDiagnosisByChainIdAndPatientOrderIds(chainId, ids);
    }

    public List<StatExternalReportMapping> queryHistoryReportMappingList(StatExternalReportParam param) {
        return dimensionHistoryReportMapping.queryHistoryReportMappingList(param.getChainId(), param.getClinicId());
    }

    public StatExternalReportMapping queryHistoryReportMappingById(String mappingId) {
        return dimensionHistoryReportMapping.queryHistoryReportMappingById(mappingId);
    }

    /**
     * 根据chainId以及goodsIds查询药品版本信息
     *
     * @param chainId  连锁id
     * @param goodsIds 药品ids
     * @return 药品版本信息map
     */
    public Map<String, List<V2GoodsHistoryVersion>> queryGoodsHisToryVersionByChainIdAndGoodsIds(String chainId, Collection<String> goodsIds) {
        return dGoods.queryGoodsHisToryVersionByChainIdAndGoodsIds(chainId, goodsIds);
    }

    public V2GoodsStockIn queryGoodsStockInByOrderId(Integer orderId) {
        return dGoods.queryGoodsStockInByOrderId(TableUtils.getCisGoodsTable(), orderId);
    }

    public V2GoodsStockIn queryGoodsStockInById(Integer orderId) {
        return dGoods.queryGoodsStockInById(TableUtils.getCisGoodsTable(), orderId);
    }

    /**
     * 根据clinicId查询地域
     * @param chainId -
     * @param clinicIds -
     * @return -
     */
    public Map<String, SheBaoClinicConfig> queryClinicRegion(String chainId, Set<String> clinicIds) {
        return dOrgan.queryClinicRegion(chainId, clinicIds);
    }

}
