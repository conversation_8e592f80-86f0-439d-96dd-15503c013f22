package cn.abc.flink.stat.dimension.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * @description:
 * @author: dy
 * @create: 2021-07-23 14:11
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class V2PatientPointsConfig {
    private Long id;
    private String chainId;
    private Byte pointsDeductionEnable;
    private Integer pointsDeductionRat;
    private Integer amountDeductionRat;

    public Long getId() {
        return this.id;
    }

    public String getChainId() {
        return this.chainId;
    }

    public Byte getPointsDeductionEnable() {
        return this.pointsDeductionEnable;
    }

    public Integer getPointsDeductionRat() {
        return this.pointsDeductionRat;
    }

    public Integer getAmountDeductionRat() {
        return this.amountDeductionRat;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setPointsDeductionEnable(Byte pointsDeductionEnable) {
        this.pointsDeductionEnable = pointsDeductionEnable;
    }

    public void setPointsDeductionRat(Integer pointsDeductionRat) {
        this.pointsDeductionRat = pointsDeductionRat;
    }

    public void setAmountDeductionRat(Integer amountDeductionRat) {
        this.amountDeductionRat = amountDeductionRat;
    }

}
