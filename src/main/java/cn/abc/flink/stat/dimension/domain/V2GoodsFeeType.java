package cn.abc.flink.stat.dimension.domain;

import cn.abc.flink.stat.common.SpecialUtil;
import cn.abc.flink.stat.config.SpecialRegisterProperties;


/**
 * <AUTHOR>
 * @date 2023/5/17 4:43 下午
 */
public class V2GoodsFeeType {
    private Long id;
    private Long feeTypeId;
    private String name;
    private Byte isDeleted;

    public V2GoodsFeeType() { }

    public V2GoodsFeeType(Long id, String name, Byte isDeleted) {
        this.id = id;
        this.feeTypeId = id;
        this.name = name;
        this.isDeleted = isDeleted;
    }
    public void specialV2GoodsFeeType(String chainId, String clinicId) {
        SpecialRegisterProperties specialRegisterProperties = SpecialUtil.getSpecialRegisterProperties();
        if (chainId != null) {
            if (specialRegisterProperties.getChain().contains(chainId) || specialRegisterProperties.getClinic().contains(clinicId)) {
                if (this.name != null && this.name.equals(specialRegisterProperties.getRegisterTargetValue())) {
                    this.name = specialRegisterProperties.getRegisterResultValue();
                }
            }
        }
    }

    public Long getId() {
        return this.id;
    }

    public Long getFeeTypeId() {
        return this.feeTypeId;
    }

    public String getName() {
        return this.name;
    }

    public Byte getIsDeleted() {
        return this.isDeleted;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public void setFeeTypeId(Long feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

}
