package cn.abc.flink.stat.dimension.items;


import cn.abc.flink.stat.common.AbcRedisTemplate;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.db.dao.DimensionMapper;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.SheBaoClinicConfig;
import cn.hutool.core.collection.CollUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * -
 */
@Component
public class DimensionOrgan {
    private static final Logger LOGGER = LoggerFactory.getLogger(DimensionOrgan.class);

    private static final String ORGAN_KEY_PREFIX = "organ";
    private static final String PARENT_ORGAN_KEY_PREFIX = "parent_organ";
    private static final String ORGAN_REGION_KEY_PREFIX = "organ_region";

    @Autowired
    private AbcRedisTemplate abcRedisTemplate;

    @Resource
    private DimensionMapper dimensionMapper;

    /**
     * @param id -
     * @return -
     */
    public Organ query(String id) {
        String key = ORGAN_KEY_PREFIX + ":" + id;
        Organ o = abcRedisTemplate.get(key, Organ.class);
        if (o != null) {
            return o;
        }
        o = dimensionMapper.selectOrganById(TableUtils.getCisBasicTable(), id);
        abcRedisTemplate.set(key, o);
        return o;
    }

    /**
     * @param ids -
     * @return -
     */
    public List<Organ> queryByIds(Set<String> ids) {
        List<Organ> result = new ArrayList<>();
        if (CollUtil.isEmpty(ids)) {
            return result;
        }
        List<String> notInCacheIds = new ArrayList<>();
        ids.forEach(id -> {
            String key = ORGAN_KEY_PREFIX + ":" + id;
            Organ organ = abcRedisTemplate.get(key, Organ.class);
            if (organ != null) {
                result.add(organ);
            } else {
                notInCacheIds.add(id);
            }
        });
        if (notInCacheIds.size() > 0) {
            List<Organ> queryList = dimensionMapper.selectOrganByIds(TableUtils.getCisBasicTable(), ids);
            result.addAll(queryList);
            queryList.forEach(t -> {
                if (t != null) {
                    String key = ORGAN_KEY_PREFIX + ":" + t.getId();
                    abcRedisTemplate.set(key, t);
                }
            });
        }
        return result;
    }

    /**
     * @param shortId -
     * @return -
     */
    public Organ queryByShortId(Long shortId) {
        String key = ORGAN_KEY_PREFIX + ":" + shortId;
        Organ o = abcRedisTemplate.get(key, Organ.class);
        if (o != null) {
            return o;
        }
        o = dimensionMapper.selectOrganByShortId(TableUtils.getCisBasicTable(), shortId);
        abcRedisTemplate.set(key, o);
        return o;
    }

    /**
     * 查询 organ 信息
     *
     * @param clinicIdList 诊所id list
     * @return -
     */
    public Map<Long, Organ> queryOrgans(List<Long> clinicIdList) {
        Map<Long, Organ> map = new HashMap<>();
        if (CollUtil.isEmpty(clinicIdList)) {
            return new HashMap<>();
        }
        List<Long> notInCacheIds = new ArrayList<>();
        clinicIdList.forEach(id -> {
            String key = ORGAN_KEY_PREFIX + ":" + id;
            Organ organ = abcRedisTemplate.get(key, Organ.class);
            if (organ != null) {
                map.put(id, organ);
            } else {
                notInCacheIds.add(id);
            }
        });
        if (notInCacheIds.size() > 0) {
            List<Organ> organs = dimensionMapper.selectOrganList(TableUtils.getCisBasicTable(), notInCacheIds);
            organs.forEach(t -> {
                if (t != null) {
                    String key = ORGAN_KEY_PREFIX + ":" + t.getShortId();
                    abcRedisTemplate.set(key, t);
                    map.put(t.getShortId(), t);
                }
            });
        }
        return map;
    }

    /**
     * @param parentId -
     * @return -
     */
    public Map<String, Organ> queryByParentId(String parentId) {
        String key = PARENT_ORGAN_KEY_PREFIX + ":" + parentId;
        Map<String, Organ> organMap = abcRedisTemplate.get(key, Map.class);
        if (organMap != null) {
            return organMap;
        }
        List<Organ> list = dimensionMapper.selectOrganByParentId(TableUtils.getCisBasicTable(), parentId);
        Map<String, Organ> map = new HashMap<>();
        for (Organ o : list) {
            map.put(o.getId(), o);
        }
        abcRedisTemplate.set(key, map);
        return map;
    }

    /**
     *
     * @param chainId -
     * @param clinicIds -
     * @return -
     */
    public Map<String, SheBaoClinicConfig> queryClinicRegion(String chainId, Set<String> clinicIds) {
        Map<String, SheBaoClinicConfig> result = new HashMap<>();
        if (CollUtil.isEmpty(clinicIds)) {
            return result;
        }
        List<String> notInCacheIds = new ArrayList<>();
        clinicIds.forEach(id -> {
            String key = ORGAN_REGION_KEY_PREFIX + ":" + id;
            SheBaoClinicConfig clinicConfig = abcRedisTemplate.get(key, SheBaoClinicConfig.class);
            if (clinicConfig != null) {
                result.put(id, clinicConfig);
            } else {
                notInCacheIds.add(id);
            }
        });
        if (!notInCacheIds.isEmpty()) {
            List<SheBaoClinicConfig> queryList = dimensionMapper.selectClinicRegion(TableUtils.getSheBaoTable(), chainId, notInCacheIds);
            queryList.forEach(t -> {
                if (t != null) {
                    result.put(t.getClinicId(), t);
                    String key = ORGAN_REGION_KEY_PREFIX + ":" + t.getClinicId();
                    abcRedisTemplate.set(key, t);
                }
            });
        }
        return result;
    }
}
