package cn.abc.flink.stat.dimension.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DomainManufacturer {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column domain_manufacturer.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column domain_manufacturer.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column domain_manufacturer.name_eng
     *
     * @mbg.generated
     */
    private String name_eng;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column domain_manufacturer.short_name
     *
     * @mbg.generated
     */
    private String short_name;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column domain_manufacturer.id
     *
     * @return the value of domain_manufacturer.id
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column domain_manufacturer.id
     *
     * @param id the value for domain_manufacturer.id
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column domain_manufacturer.name
     *
     * @return the value of domain_manufacturer.name
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column domain_manufacturer.name
     *
     * @param name the value for domain_manufacturer.name
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column domain_manufacturer.name_eng
     *
     * @return the value of domain_manufacturer.name_eng
     * @mbg.generated
     */
    public String getName_eng() {
        return name_eng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column domain_manufacturer.name_eng
     *
     * @param name_eng the value for domain_manufacturer.name_eng
     * @mbg.generated
     */
    public void setName_eng(String name_eng) {
        this.name_eng = name_eng;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column domain_manufacturer.short_name
     *
     * @return the value of domain_manufacturer.short_name
     * @mbg.generated
     */
    public String getShort_name() {
        return short_name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column domain_manufacturer.short_name
     *
     * @param short_name the value for domain_manufacturer.short_name
     * @mbg.generated
     */
    public void setShort_name(String short_name) {
        this.short_name = short_name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table domain_manufacturer
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", name_eng=").append(name_eng);
        sb.append(", short_name=").append(short_name);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table domain_manufacturer
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DomainManufacturer other = (DomainManufacturer) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
                && (this.getName_eng() == null ? other.getName_eng() == null : this.getName_eng().equals(other.getName_eng()))
                && (this.getShort_name() == null ? other.getShort_name() == null : this.getShort_name().equals(other.getShort_name()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table domain_manufacturer
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getName_eng() == null) ? 0 : getName_eng().hashCode());
        result = prime * result + ((getShort_name() == null) ? 0 : getShort_name().hashCode());
        return result;
    }
}