package cn.abc.flink.stat.dimension.domain;


import lombok.ToString;

import java.time.LocalDateTime;

/**
 * V2PatientSourceType
 */
@ToString
public class V2PatientSourceType {

    /**
     * This field was generated by MyBatis Generator.
     */
    public static class RelatedType {
        /**
         * 未关联
         */
        public static final int DEFAULT_VALUE = 0;

        /**
         * 关联全部医生
         */
        public static final int ALL_DOCTOR = 1;

        /**
         * 关联全部患者
         */
        public static final int ALL_PATIENT = 2;

        /**
         * 关联单个医生
         */
        public static final int SINGLE_DOCTOR = 3;

        /**
         * 关联单个患者
         */
        public static final int SINGLE_PATEINT = 4;

        /**
         * 关联角色
         */
        public static final int EITHER_ROLE = 5;
    }


    private String id;

    private String chainId;

    private String name;

    private Byte status;

    private String createdBy;

    private LocalDateTime created;

    private String lastModifiedBy;

    private LocalDateTime lastModified;

    private String parentId;

    private Integer level;

    private Integer relatedType;

    private String relatedId;

    public String getId() {
        return this.id;
    }

    public String getChainId() {
        return this.chainId;
    }

    public String getName() {
        return this.name;
    }

    public Byte getStatus() {
        return this.status;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public LocalDateTime getCreated() {
        return this.created;
    }

    public String getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    public LocalDateTime getLastModified() {
        return this.lastModified;
    }

    public String getParentId() {
        return this.parentId;
    }

    public Integer getLevel() {
        return this.level;
    }

    public Integer getRelatedType() {
        return this.relatedType;
    }

    public String getRelatedId() {
        return this.relatedId;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setCreated(LocalDateTime created) {
        this.created = created;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public void setLastModified(LocalDateTime lastModified) {
        this.lastModified = lastModified;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public void setRelatedType(Integer relatedType) {
        this.relatedType = relatedType;
    }

    public void setRelatedId(String relatedId) {
        this.relatedId = relatedId;
    }

}