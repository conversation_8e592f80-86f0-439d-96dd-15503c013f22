package cn.abc.flink.stat.dimension.items;

import cn.abc.flink.stat.db.cis.patient.dao.PatientDimensionMapper;
import cn.abc.flink.stat.dimension.domain.V2PatientPointsConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: dy
 * @create: 2021-07-23 14:23
 */
@Component
public class DimensionPatientPoints {
    private static final Logger logger = LoggerFactory.getLogger(DimensionDepartment.class);
    @Resource
    private PatientDimensionMapper patientDimensionMapper;

    public V2PatientPointsConfig queryPointsConfigByChainId(String chainId) {
        if (chainId == null || chainId.equals("")) {
            return null;
        }
        return patientDimensionMapper.selectPatientPointsConfigByChainId(chainId);
    }
}
