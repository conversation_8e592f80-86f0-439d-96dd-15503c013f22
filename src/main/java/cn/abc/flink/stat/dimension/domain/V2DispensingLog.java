package cn.abc.flink.stat.dimension.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class V2DispensingLog {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.id
     *
     * @mbg.generated
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.log_time
     *
     * @mbg.generated
     */
    private LocalDateTime log_time;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.patient_order_id
     *
     * @mbg.generated
     */
    private String patient_order_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.patient_id
     *
     * @mbg.generated
     */
    private String patient_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.chain_id
     *
     * @mbg.generated
     */
    private String chain_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.clinic_id
     *
     * @mbg.generated
     */
    private String clinic_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.dispensing_sheet_id
     *
     * @mbg.generated
     */
    private String dispensing_sheet_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.dispensing_form_item_id
     *
     * @mbg.generated
     */
    private String dispensing_form_item_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.charge_sheet_id
     *
     * @mbg.generated
     */
    private String charge_sheet_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.doctor_id
     *
     * @mbg.generated
     */
    private String doctor_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.seller_id
     *
     * @mbg.generated
     */
    private String seller_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.copywriter_id
     *
     * @mbg.generated
     */
    private String copywriter_id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.patient_order_source
     *
     * @mbg.generated
     */
    private Byte patient_order_source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.unit_count
     *
     * @mbg.generated
     */
    private BigDecimal unit_count;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.dose_count
     *
     * @mbg.generated
     */
    private BigDecimal dose_count;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_dispensing_log.operator_id
     *
     * @mbg.generated
     */
    private String operator_id;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.id
     *
     * @return the value of v2_dispensing_log.id
     *
     * @mbg.generated
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.id
     *
     * @param id the value for v2_dispensing_log.id
     *
     * @mbg.generated
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.log_time
     *
     * @return the value of v2_dispensing_log.log_time
     *
     * @mbg.generated
     */
    public LocalDateTime getLog_time() {
        return log_time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.log_time
     *
     * @param log_time the value for v2_dispensing_log.log_time
     *
     * @mbg.generated
     */
    public void setLog_time(LocalDateTime log_time) {
        this.log_time = log_time;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.patient_order_id
     *
     * @return the value of v2_dispensing_log.patient_order_id
     *
     * @mbg.generated
     */
    public String getPatient_order_id() {
        return patient_order_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.patient_order_id
     *
     * @param patient_order_id the value for v2_dispensing_log.patient_order_id
     *
     * @mbg.generated
     */
    public void setPatient_order_id(String patient_order_id) {
        this.patient_order_id = patient_order_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.patient_id
     *
     * @return the value of v2_dispensing_log.patient_id
     *
     * @mbg.generated
     */
    public String getPatient_id() {
        return patient_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.patient_id
     *
     * @param patient_id the value for v2_dispensing_log.patient_id
     *
     * @mbg.generated
     */
    public void setPatient_id(String patient_id) {
        this.patient_id = patient_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.chain_id
     *
     * @return the value of v2_dispensing_log.chain_id
     *
     * @mbg.generated
     */
    public String getChain_id() {
        return chain_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.chain_id
     *
     * @param chain_id the value for v2_dispensing_log.chain_id
     *
     * @mbg.generated
     */
    public void setChain_id(String chain_id) {
        this.chain_id = chain_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.clinic_id
     *
     * @return the value of v2_dispensing_log.clinic_id
     *
     * @mbg.generated
     */
    public String getClinic_id() {
        return clinic_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.clinic_id
     *
     * @param clinic_id the value for v2_dispensing_log.clinic_id
     *
     * @mbg.generated
     */
    public void setClinic_id(String clinic_id) {
        this.clinic_id = clinic_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.dispensing_sheet_id
     *
     * @return the value of v2_dispensing_log.dispensing_sheet_id
     *
     * @mbg.generated
     */
    public String getDispensing_sheet_id() {
        return dispensing_sheet_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.dispensing_sheet_id
     *
     * @param dispensing_sheet_id the value for v2_dispensing_log.dispensing_sheet_id
     *
     * @mbg.generated
     */
    public void setDispensing_sheet_id(String dispensing_sheet_id) {
        this.dispensing_sheet_id = dispensing_sheet_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.dispensing_form_item_id
     *
     * @return the value of v2_dispensing_log.dispensing_form_item_id
     *
     * @mbg.generated
     */
    public String getDispensing_form_item_id() {
        return dispensing_form_item_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.dispensing_form_item_id
     *
     * @param dispensing_form_item_id the value for v2_dispensing_log.dispensing_form_item_id
     *
     * @mbg.generated
     */
    public void setDispensing_form_item_id(String dispensing_form_item_id) {
        this.dispensing_form_item_id = dispensing_form_item_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.charge_sheet_id
     *
     * @return the value of v2_dispensing_log.charge_sheet_id
     *
     * @mbg.generated
     */
    public String getCharge_sheet_id() {
        return charge_sheet_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.charge_sheet_id
     *
     * @param charge_sheet_id the value for v2_dispensing_log.charge_sheet_id
     *
     * @mbg.generated
     */
    public void setCharge_sheet_id(String charge_sheet_id) {
        this.charge_sheet_id = charge_sheet_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.doctor_id
     *
     * @return the value of v2_dispensing_log.doctor_id
     *
     * @mbg.generated
     */
    public String getDoctor_id() {
        return doctor_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.doctor_id
     *
     * @param doctor_id the value for v2_dispensing_log.doctor_id
     *
     * @mbg.generated
     */
    public void setDoctor_id(String doctor_id) {
        this.doctor_id = doctor_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.seller_id
     *
     * @return the value of v2_dispensing_log.seller_id
     *
     * @mbg.generated
     */
    public String getSeller_id() {
        return seller_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.seller_id
     *
     * @param seller_id the value for v2_dispensing_log.seller_id
     *
     * @mbg.generated
     */
    public void setSeller_id(String seller_id) {
        this.seller_id = seller_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.copywriter_id
     *
     * @return the value of v2_dispensing_log.copywriter_id
     *
     * @mbg.generated
     */
    public String getCopywriter_id() {
        return copywriter_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.copywriter_id
     *
     * @param copywriter_id the value for v2_dispensing_log.copywriter_id
     *
     * @mbg.generated
     */
    public void setCopywriter_id(String copywriter_id) {
        this.copywriter_id = copywriter_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.patient_order_source
     *
     * @return the value of v2_dispensing_log.patient_order_source
     *
     * @mbg.generated
     */
    public Byte getPatient_order_source() {
        return patient_order_source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.patient_order_source
     *
     * @param patient_order_source the value for v2_dispensing_log.patient_order_source
     *
     * @mbg.generated
     */
    public void setPatient_order_source(Byte patient_order_source) {
        this.patient_order_source = patient_order_source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.unit_count
     *
     * @return the value of v2_dispensing_log.unit_count
     *
     * @mbg.generated
     */
    public BigDecimal getUnit_count() {
        return unit_count;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.unit_count
     *
     * @param unit_count the value for v2_dispensing_log.unit_count
     *
     * @mbg.generated
     */
    public void setUnit_count(BigDecimal unit_count) {
        this.unit_count = unit_count;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.dose_count
     *
     * @return the value of v2_dispensing_log.dose_count
     *
     * @mbg.generated
     */
    public BigDecimal getDose_count() {
        return dose_count;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.dose_count
     *
     * @param dose_count the value for v2_dispensing_log.dose_count
     *
     * @mbg.generated
     */
    public void setDose_count(BigDecimal dose_count) {
        this.dose_count = dose_count;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.type
     *
     * @return the value of v2_dispensing_log.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.type
     *
     * @param type the value for v2_dispensing_log.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_dispensing_log.operator_id
     *
     * @return the value of v2_dispensing_log.operator_id
     *
     * @mbg.generated
     */
    public String getOperator_id() {
        return operator_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_dispensing_log.operator_id
     *
     * @param operator_id the value for v2_dispensing_log.operator_id
     *
     * @mbg.generated
     */
    public void setOperator_id(String operator_id) {
        this.operator_id = operator_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table v2_dispensing_log
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", log_time=").append(log_time);
        sb.append(", patient_order_id=").append(patient_order_id);
        sb.append(", patient_id=").append(patient_id);
        sb.append(", chain_id=").append(chain_id);
        sb.append(", clinic_id=").append(clinic_id);
        sb.append(", dispensing_sheet_id=").append(dispensing_sheet_id);
        sb.append(", dispensing_form_item_id=").append(dispensing_form_item_id);
        sb.append(", charge_sheet_id=").append(charge_sheet_id);
        sb.append(", doctor_id=").append(doctor_id);
        sb.append(", seller_id=").append(seller_id);
        sb.append(", copywriter_id=").append(copywriter_id);
        sb.append(", patient_order_source=").append(patient_order_source);
        sb.append(", unit_count=").append(unit_count);
        sb.append(", dose_count=").append(dose_count);
        sb.append(", type=").append(type);
        sb.append(", operator_id=").append(operator_id);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table v2_dispensing_log
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        V2DispensingLog other = (V2DispensingLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLog_time() == null ? other.getLog_time() == null : this.getLog_time().equals(other.getLog_time()))
            && (this.getPatient_order_id() == null ? other.getPatient_order_id() == null : this.getPatient_order_id().equals(other.getPatient_order_id()))
            && (this.getPatient_id() == null ? other.getPatient_id() == null : this.getPatient_id().equals(other.getPatient_id()))
            && (this.getChain_id() == null ? other.getChain_id() == null : this.getChain_id().equals(other.getChain_id()))
            && (this.getClinic_id() == null ? other.getClinic_id() == null : this.getClinic_id().equals(other.getClinic_id()))
            && (this.getDispensing_sheet_id() == null ? other.getDispensing_sheet_id() == null : this.getDispensing_sheet_id().equals(other.getDispensing_sheet_id()))
            && (this.getDispensing_form_item_id() == null ? other.getDispensing_form_item_id() == null : this.getDispensing_form_item_id().equals(other.getDispensing_form_item_id()))
            && (this.getCharge_sheet_id() == null ? other.getCharge_sheet_id() == null : this.getCharge_sheet_id().equals(other.getCharge_sheet_id()))
            && (this.getDoctor_id() == null ? other.getDoctor_id() == null : this.getDoctor_id().equals(other.getDoctor_id()))
            && (this.getSeller_id() == null ? other.getSeller_id() == null : this.getSeller_id().equals(other.getSeller_id()))
            && (this.getCopywriter_id() == null ? other.getCopywriter_id() == null : this.getCopywriter_id().equals(other.getCopywriter_id()))
            && (this.getPatient_order_source() == null ? other.getPatient_order_source() == null : this.getPatient_order_source().equals(other.getPatient_order_source()))
            && (this.getUnit_count() == null ? other.getUnit_count() == null : this.getUnit_count().equals(other.getUnit_count()))
            && (this.getDose_count() == null ? other.getDose_count() == null : this.getDose_count().equals(other.getDose_count()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getOperator_id() == null ? other.getOperator_id() == null : this.getOperator_id().equals(other.getOperator_id()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table v2_dispensing_log
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLog_time() == null) ? 0 : getLog_time().hashCode());
        result = prime * result + ((getPatient_order_id() == null) ? 0 : getPatient_order_id().hashCode());
        result = prime * result + ((getPatient_id() == null) ? 0 : getPatient_id().hashCode());
        result = prime * result + ((getChain_id() == null) ? 0 : getChain_id().hashCode());
        result = prime * result + ((getClinic_id() == null) ? 0 : getClinic_id().hashCode());
        result = prime * result + ((getDispensing_sheet_id() == null) ? 0 : getDispensing_sheet_id().hashCode());
        result = prime * result + ((getDispensing_form_item_id() == null) ? 0 : getDispensing_form_item_id().hashCode());
        result = prime * result + ((getCharge_sheet_id() == null) ? 0 : getCharge_sheet_id().hashCode());
        result = prime * result + ((getDoctor_id() == null) ? 0 : getDoctor_id().hashCode());
        result = prime * result + ((getSeller_id() == null) ? 0 : getSeller_id().hashCode());
        result = prime * result + ((getCopywriter_id() == null) ? 0 : getCopywriter_id().hashCode());
        result = prime * result + ((getPatient_order_source() == null) ? 0 : getPatient_order_source().hashCode());
        result = prime * result + ((getUnit_count() == null) ? 0 : getUnit_count().hashCode());
        result = prime * result + ((getDose_count() == null) ? 0 : getDose_count().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getOperator_id() == null) ? 0 : getOperator_id().hashCode());
        return result;
    }
}