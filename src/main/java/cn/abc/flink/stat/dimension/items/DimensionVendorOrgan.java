package cn.abc.flink.stat.dimension.items;

import cn.abc.flink.stat.common.AbcRedisTemplate;
import cn.abc.flink.stat.db.bis.mysql.dao.DimensionBisMapper;
import cn.abc.flink.stat.dimension.domain.V1VendorOrgan;
import cn.hutool.core.collection.CollUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * -
 */
@Component
public class DimensionVendorOrgan {
    private static final Logger LOGGER = LoggerFactory.getLogger(DimensionV2Goods.class);

    private static final String VENDOR_ORGAN_KEY_PREFIX = "vendor_organ";

    private static final String VENDOR_ORGAN_PARENT_KEY_PREFIX = "vendor_organ_parent";

    @Autowired
    private AbcRedisTemplate abcRedisTemplate;

    @Resource
    private DimensionBisMapper dimensionBisMapper;

    /**
     * @param id -
     * @return -
     */
    public V1VendorOrgan query(Long id) {
        String key = VENDOR_ORGAN_KEY_PREFIX + ":" + id;
        V1VendorOrgan vo = abcRedisTemplate.get(key, V1VendorOrgan.class);
        if (vo != null) {
            return vo;
        }
        vo = dimensionBisMapper.selectVendorOrganById(id);
        abcRedisTemplate.set(key, vo);
        return vo;
    }

    /**
     * @param parentId -
     * @return -
     */
    public List<V1VendorOrgan> queryByParentId(Long parentId) {
        List<V1VendorOrgan> list = new ArrayList<>();
        if (parentId == null) {
            return list;
        }
        String key = VENDOR_ORGAN_PARENT_KEY_PREFIX + ":" + parentId;
        list = abcRedisTemplate.get(key, List.class);
        if (CollUtil.isEmpty(list)) {
            list = dimensionBisMapper.selectVendorOrganByParentId(parentId);
            abcRedisTemplate.set(key, list);
        }
        return list;
    }

    /**
     * 查询所有商家信息
     *
     * @param shopIdList 商家Id list
     * @return -
     */
    public Map<Long, V1VendorOrgan> queryVendorOrganList(List<Long> shopIdList) {
        Map<Long, V1VendorOrgan> map = new HashMap<>();
        if (CollUtil.isEmpty(shopIdList)) {
            return map;
        }
        List<Long> notInCacheIds = new ArrayList<>();
        shopIdList.forEach(id -> {
            String key = VENDOR_ORGAN_KEY_PREFIX + ":" + id;
            V1VendorOrgan v1VendorOrgan = abcRedisTemplate.get(key, V1VendorOrgan.class);
            if (v1VendorOrgan != null) {
                map.put(v1VendorOrgan.getId(), v1VendorOrgan);
            } else {
                notInCacheIds.add(id);
            }
        });
        if (notInCacheIds.size() > 0) {
            List<V1VendorOrgan> queryList = dimensionBisMapper.selectVendorOrganList(shopIdList);
            queryList.forEach(t -> {
                if (t != null) {
                    String key = VENDOR_ORGAN_KEY_PREFIX + ":" + t.getId();
                    abcRedisTemplate.set(key, t);
                    map.put(t.getId(), t);
                }
            });
        }
        return map;
    }
}
