package cn.abc.flink.stat.dimension.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class V2GoodsSysType {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.parent_id
     *
     * @mbg.generated
     */
    private Integer parent_id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.goods_type
     *
     * @mbg.generated
     */
    private Byte goods_type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.goods_sub_type
     *
     * @mbg.generated
     */
    private Byte goods_sub_type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.goods_c_m_spec
     *
     * @mbg.generated
     */
    private String goods_c_m_spec;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.is_sell
     *
     * @mbg.generated
     */
    private Byte is_sell;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.is_promotion
     *
     * @mbg.generated
     */
    private Byte is_promotion;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column v2_goods_sys_type.is_leaf
     *
     * @mbg.generated
     */
    private Integer is_leaf;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.id
     *
     * @return the value of v2_goods_sys_type.id
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.id
     *
     * @param id the value for v2_goods_sys_type.id
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.parent_id
     *
     * @return the value of v2_goods_sys_type.parent_id
     * @mbg.generated
     */
    public Integer getParent_id() {
        return parent_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.parent_id
     *
     * @param parent_id the value for v2_goods_sys_type.parent_id
     * @mbg.generated
     */
    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.name
     *
     * @return the value of v2_goods_sys_type.name
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.name
     *
     * @param name the value for v2_goods_sys_type.name
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.goods_type
     *
     * @return the value of v2_goods_sys_type.goods_type
     * @mbg.generated
     */
    public Byte getGoods_type() {
        return goods_type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.goods_type
     *
     * @param goods_type the value for v2_goods_sys_type.goods_type
     * @mbg.generated
     */
    public void setGoods_type(Byte goods_type) {
        this.goods_type = goods_type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.goods_sub_type
     *
     * @return the value of v2_goods_sys_type.goods_sub_type
     * @mbg.generated
     */
    public Byte getGoods_sub_type() {
        return goods_sub_type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.goods_sub_type
     *
     * @param goods_sub_type the value for v2_goods_sys_type.goods_sub_type
     * @mbg.generated
     */
    public void setGoods_sub_type(Byte goods_sub_type) {
        this.goods_sub_type = goods_sub_type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.goods_c_m_spec
     *
     * @return the value of v2_goods_sys_type.goods_c_m_spec
     * @mbg.generated
     */
    public String getGoods_c_m_spec() {
        return goods_c_m_spec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.goods_c_m_spec
     *
     * @param goods_c_m_spec the value for v2_goods_sys_type.goods_c_m_spec
     * @mbg.generated
     */
    public void setGoods_c_m_spec(String goods_c_m_spec) {
        this.goods_c_m_spec = goods_c_m_spec == null ? null : goods_c_m_spec.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.is_sell
     *
     * @return the value of v2_goods_sys_type.is_sell
     * @mbg.generated
     */
    public Byte getIs_sell() {
        return is_sell;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.is_sell
     *
     * @param is_sell the value for v2_goods_sys_type.is_sell
     * @mbg.generated
     */
    public void setIs_sell(Byte is_sell) {
        this.is_sell = is_sell;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.is_promotion
     *
     * @return the value of v2_goods_sys_type.is_promotion
     * @mbg.generated
     */
    public Byte getIs_promotion() {
        return is_promotion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.is_promotion
     *
     * @param is_promotion the value for v2_goods_sys_type.is_promotion
     * @mbg.generated
     */
    public void setIs_promotion(Byte is_promotion) {
        this.is_promotion = is_promotion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column v2_goods_sys_type.is_leaf
     *
     * @return the value of v2_goods_sys_type.is_leaf
     * @mbg.generated
     */
    public Integer getIs_leaf() {
        return is_leaf;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column v2_goods_sys_type.is_leaf
     *
     * @param is_leaf the value for v2_goods_sys_type.is_leaf
     * @mbg.generated
     */
    public void setIs_leaf(Integer is_leaf) {
        this.is_leaf = is_leaf;
    }
}