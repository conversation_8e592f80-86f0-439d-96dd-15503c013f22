package cn.abc.flink.stat.dimension.items;

import cn.abc.flink.stat.common.AbcRedisTemplate;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.db.dao.DimensionMapper;
import cn.abc.flink.stat.dimension.domain.MedicalRecordDiagnosis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class DimensionMedicalRecord {
    @Resource
    private DimensionMapper dimensionMapper;

    @Autowired
    private AbcRedisTemplate abcRedisTemplate;

    private static final String MEDICAL_DIAGNOSIS_KEY_PREFIX = "medical_diagnosis";

    private static final String therapySheetMEDICAL_DIAGNOSIS_KEY_PREFIX = "medical_diagnosis";

    public Map<String, String> queryMedicalDiagnosis(String chainId, List<String> medicalRecordIds) {
        Map<String, String> result = new HashMap<>();
        if (medicalRecordIds.isEmpty()) {
            return result;
        }
        List<String> redisSelectIds = new ArrayList<>();
        medicalRecordIds.forEach(id -> redisSelectIds.add(MEDICAL_DIAGNOSIS_KEY_PREFIX + ":" + id));
        List<MedicalRecordDiagnosis> redisList = abcRedisTemplate.batchGet(redisSelectIds, MedicalRecordDiagnosis.class);
        Set<String> redisIds = new HashSet<>();
        redisList.forEach(d -> {
            if (d != null && d.getId() != null) {
                redisIds.add(d.getId());
                result.put(d.getId(), d.getDiagnosis());
            }
        });
        List<String> notInCacheIds = new ArrayList<>();
        medicalRecordIds.forEach(id -> {
            if (id != null && !redisIds.contains(id)) {
                notInCacheIds.add(id);
            }
        });
        if (notInCacheIds.size() > 0) {
            List<MedicalRecordDiagnosis> diagnoses = dimensionMapper.selectMedicalRecordDiagnosis(TableUtils.getCisOutPatientRecordTable(), chainId, notInCacheIds);
            if (diagnoses != null) {
                Map<String, MedicalRecordDiagnosis> upsertMap = new HashMap<>(16);
                diagnoses.forEach(d -> {
                    if (d != null) {
                        String key = MEDICAL_DIAGNOSIS_KEY_PREFIX + ":" + d.getId();
                        upsertMap.put(key, d);
                        result.put(d.getId(), d.getDiagnosis());
                    }
                });
                abcRedisTemplate.batchSetAndExpire(upsertMap);
            }
        }
        return result;
    }

    public Map<Long, String> queryMedicalDiagnosisFromTherapySheet(String chainId, List<Long> therapySheetIds) {
        Map<Long, String> result = new HashMap<>();
        if (therapySheetIds.isEmpty()) {
            return result;
        }
        List<String> redisSelectIds = new ArrayList<>();
        therapySheetIds.forEach(id -> redisSelectIds.add(therapySheetMEDICAL_DIAGNOSIS_KEY_PREFIX + ":" + id));
        List<MedicalRecordDiagnosis> redisList = abcRedisTemplate.batchGet(redisSelectIds, MedicalRecordDiagnosis.class);
        Set<String> redisIds = new HashSet<>();
        redisList.forEach(d -> {
            if (d != null && d.getId() != null) {
                redisIds.add(d.getId());
                result.put(Long.valueOf(d.getId()), d.getDiagnosis());
            }
        });
        List<Long> notInCacheIds = new ArrayList<>();
        therapySheetIds.forEach(id -> {
            if (id != null && !redisIds.contains(String.valueOf(id))) {
                notInCacheIds.add(id);
            }
        });
        if (notInCacheIds.size() > 0) {
            List<MedicalRecordDiagnosis> diagnoses = dimensionMapper.selectMedicalRecordDiagnosisFromTherapySheet(TableUtils.getCisChargeTable(), chainId, notInCacheIds);
            if (diagnoses != null) {
                Map<String, MedicalRecordDiagnosis> upsertMap = new HashMap<>(16);
                diagnoses.forEach(d -> {
                    if (d != null) {
                        String key = therapySheetMEDICAL_DIAGNOSIS_KEY_PREFIX + ":" + d.getId();
                        upsertMap.put(key, d);
                        result.put(Long.valueOf(d.getId()), d.getDiagnosis());
                    }
                });
                abcRedisTemplate.batchSetAndExpire(upsertMap);
            }
        }
        return result;
    }
}
