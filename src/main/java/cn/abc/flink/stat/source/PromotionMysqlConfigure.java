package cn.abc.flink.stat.source;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * @description:
 * @author: dy
 * @create: 2021-07-21 16:46
 */
@Configuration
public class PromotionMysqlConfigure {
    @Value("${spring.datasource.promotion.url}")
    private String url;

    @Value("${spring.datasource.promotion.driver-class-name}")
    private String driver;

    @Value("${spring.datasource.promotion.username}")
    private String user;

    @Value("${spring.datasource.promotion.password}")
    private String password;

    /**
     * 生成数据源.  @Primary 注解声明为默认数据源
     */
    @Bean(name = "dbPromotionDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.promotion")
    public DataSource testDataSource() {
//        return DataSourceBuilder.create().build();
        DruidDataSource datasource = new DruidDataSource();

        datasource.setUrl(url);
        datasource.setUsername(user);
        datasource.setPassword(password);
        datasource.setDriverClassName(driver);

        //新增连接池配置，出现mysql服务连接已经断开但是连接池还在使用该连接出现报错
        datasource.setInitialSize(1);
        datasource.setMaxWait(60000);
        datasource.setMaxActive(20);
        datasource.setMinIdle(3);
//            dataSource.setMinEvictableIdleTimeMillis(300000);
        datasource.setMinEvictableIdleTimeMillis(160000);
        datasource.setMaxEvictableIdleTimeMillis(230000);
        datasource.setTimeBetweenEvictionRunsMillis(60000);
        datasource.setValidationQuery("select 1");
        datasource.setTestWhileIdle(true);
        datasource.setKeepAlive(false);
        datasource.setRemoveAbandoned(true);
        datasource.setRemoveAbandonedTimeout(180);
        return datasource;
    }


    /**
     * 创建 SqlSessionFactory
     */
    @Bean(name = "dbPromotionSqlSessionFactory")
    public SqlSessionFactory testSqlSessionFactory(@Qualifier("dbPromotionDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        return bean.getObject();
    }

    @Bean(name = "dbPromotionSqlSessionTemplate")
    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("dbPromotionSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
