package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.general.utils.R;
import cn.abc.flink.stat.service.cis.disease.DiseaseService;
import cn.abc.flink.stat.service.cis.disease.domain.DataTotal;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseCatgoryResult;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfoResult;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseParam;
import cn.abc.flink.stat.service.cis.disease.domain.DiseasePropResult;
import cn.abc.flink.stat.service.cis.disease.domain.DoctorDiseaseResult;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.jwt.CisJWTBody;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
/**
 * @Description:运营分析-病种分析
 * @param
 * @return
 * @Author: zs
 * @Date: 2022/8/2 20:03
 */
@RestController
@RequestMapping("/api/v2/sc/stat/disease")
public class DiseaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DiseaseController.class);

    @Autowired
    private DiseaseService diseaseService;

    /**
     * @Description:
     * @param
     * @param jwtEncodeStr -
     * @param param ：chainId -  clinicId - beginDate - endDate -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/2 20:03
     * @throws ParseException -
     */
    @RequestMapping("/proportion")
    public CisServiceResponse diseaseProportion(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param
    ) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        param.setOffset(param.getOffset() == null ? 0 : param.getOffset());
        param.setSize(param.getSize() == null ? CommonConstants.NUMBER_TEN : param.getSize());
        DiseasePropResult result = diseaseService.getDiseaseProp(param);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: 运营分析-病种分析-病种占比
     * @param
     * @param jwtEncodeStr -
     * @param param chainId -clinicId -beginDate -endDate -offset -size -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 19:59
     * @throws ParseException -
     */
    @RequestMapping("/proportion/detail")
    public CisServiceResponse diseaseProportionDetail(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        param.setOffset(param.getOffset() == null ? 0 : param.getOffset());
        param.setSize(param.getSize() == null ? CommonConstants.NUMBER_TEN : param.getSize());
        DiseasePropResult result = diseaseService.getDiseaseProp(param);
        return new CisServiceResponse(result);
    }


    /**
     * @Description: -
     * @param
     * @param response -
     * @param jwtEncodeStr -
     * @param param ：chainId:  clinicId:  beginDate:  endDate:
     * @return
     * @Author: zs
     * @Date: 2022/8/3 21:02
     * @throws IOException -
     * @throws ParseException -
     */
    @RequestMapping("/proportion/export")
    public void diseaseOrderProportionExport(
            HttpServletResponse response,
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws IOException, ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return;
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        diseaseService.exportDiseaseProportion(param, response);
    }

    /**
     * @Description: 病种分析-合计条数
     * @param
     * @param jwtEncodeStr -
     * @param param :chainId -clinicId -beginDate -endDate -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 20:16
     * @throws ParseException -
     */
    @RequestMapping("/proportion/total")
    public CisServiceResponse diseaseProportionTotal(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        DataTotal result = diseaseService.getDiseasePropTotal(param);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: -
     * @param
     * @param jwtEncodeStr  -
     * @param param chainId - clinicId - beginDate - endDate - category -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 21:09
     * @throws ParseException -
     */
    @RequestMapping("/doctor/order")
    public CisServiceResponse doctorDiseaseOrder(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        DoctorDiseaseResult result = diseaseService.getDoctorDiseaseOrder(param);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: 运营分析-病种分析-医生诊断病种
     * @param
     * @param jwtEncodeStr -
     * @param param chainId - clinicId - beginDate - endDate - category - offset - size -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 20:27
     * @throws ParseException -
     */
    @RequestMapping("/doctor/order/detail")
    public CisServiceResponse doctorDiseaseOrderDetail(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        param.setOffset(param.getOffset() == null ? CommonConstants.NUMBER_ZERO : param.getOffset());
        param.setSize(param.getSize() == null ? CommonConstants.NUMBER_TEN : param.getSize());
        DoctorDiseaseResult result = diseaseService.getDoctorDiseaseOrder(param);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: 运营分析-病种分析-医生诊断病种-导出
     * @param
     * @param response -
     * @param jwtEncodeStr -
     * @param param :chainId - clinicId - beginDate - endDate - category -
     * @return
     * @Author: zs
     * @Date: 2022/8/3 20:41
     * @throws IOException -
     * @throws ParseException -
     */
    @RequestMapping("/doctor/order/export")
    public void doctorDiseaseOrderExport(
            HttpServletResponse response,
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws IOException, ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return;
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        diseaseService.exportDoctorDiseaseOrder(param, response);
    }

    /**
     * @Description:-
     * @param
     * @param jwtEncodeStr -
     * @param param chainId:  clinicId:  beginDate:  endDate:  category:
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 21:13
     * @throws ParseException -
     */
    @RequestMapping("/doctor/order/total")
    public CisServiceResponse doctorDiseaseOrderTotal(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        DataTotal result = diseaseService.getDoctorDiseaseOrderTotal(param);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: 运营分析-病种分析-病种诊断明细
     * @param
     * @param jwtEncodeStr -
     * @param param chainId -clinicId -beginDate -endDate -doctorId -category -offset -size -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 19:09
     * @throws ParseException -
     */
    @RequestMapping("/doctor/detail")
    public CisServiceResponse doctorDiseaseDetail(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        param.setOffset(param.getOffset() == null ? CommonConstants.NUMBER_ZERO : param.getOffset());
        param.setSize(param.getSize() == null ? CommonConstants.NUMBER_TEN : param.getSize());
        param.init();
        DoctorDiseaseResult result = diseaseService.getDoctorDiseaseDetail(param);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: 运营分析-病种分析-病种诊断明细-导出
     * @param response -
     * @param jwtEncodeStr -
     * @param param chainId -clinicId -beginDate -endDate -doctorId -category
     * @return
     * @Author: zs
     * @Date: 2022/8/3 19:09
     * @throws ParseException -
     * @throws IOException -
     */
    @RequestMapping("/doctor/detail/export")
    @JwtPermissions
    public void doctorDiseaseDetailExport(
            HttpServletResponse response,
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws IOException, ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return;
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        param.init();
        diseaseService.exportDoctorDiseaseDetail(param, response);
    }

    /**
     * @Description: -
     * @param
     * @param jwtEncodeStr -
     * @param param chainId - clinicId - beginDate - endDate - doctorId - category -
     * @return
     * @Author: zs
     * @Date: 2022/8/3 21:19
     * @return -
     * @throws ParseException -
     */
    @RequestMapping("/doctor/detail/total")
    public CisServiceResponse doctorDiseaseDetailTotal(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @ModelAttribute DiseaseParam param) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(getIdFromReq(cisJWTBody, param.getChainId(), true));
        param.setClinicId(getIdFromReq(cisJWTBody, param.getClinicId(), false));
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        param.setBeginDate(param.getBeginDate() == null ? nowTime : param.getBeginDate());
        param.setEndDate(param.getEndDate() == null ? nowTime : param.getEndDate());
        param.init();
        DataTotal result = diseaseService.getDoctorDiseaseDetailTotal(param);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: 运营分析-病种分析-病种诊断明细（医生下拉框）
     * @param
     * @param jwtEncodeStr -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 18:48
     * @throws ParseException -
     */
    @RequestMapping("/select/doctor")
    public CisServiceResponse selectDoctor(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestParam(value = "chainId", required = false) String chainId,
            @RequestParam(value = "clinicId", required = false) String clinicId,
            @RequestParam(value = "beginDate", required = false) String beginDate,
            @RequestParam(value = "endDate", required = false) String endDate) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        chainId = getIdFromReq(cisJWTBody, chainId, true);
        clinicId = getIdFromReq(cisJWTBody, clinicId, false);
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        String begin = beginDate == null ? nowTime : beginDate;
        String end = endDate == null ? nowTime : endDate;
        DiseaseDoctorInfoResult result = diseaseService.getDoctorSelection(chainId, clinicId, begin, end);
        return new CisServiceResponse(result);
    }

    /**
     * @Description:
     * @param
     * @param jwtEncodeStr -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/3 21:25
     * @throws ParseException -
     */
    @RequestMapping("/select/category")
    public CisServiceResponse selectCategory(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestParam(value = "chainId", required = false) String chainId,
            @RequestParam(value = "clinicId", required = false) String clinicId,
            @RequestParam(value = "beginDate", required = false) String beginDate,
            @RequestParam(value = "endDate", required = false) String endDate) throws ParseException {
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        chainId = getIdFromReq(cisJWTBody, chainId, true);
        clinicId = getIdFromReq(cisJWTBody, clinicId, false);
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String nowTime = LocalDate.now().format(formater);
        String begin = beginDate == null ? nowTime : beginDate;
        String end = endDate == null ? nowTime : endDate;
        DiseaseCatgoryResult result = diseaseService.selectCategory(chainId, clinicId, begin, end);
        return new CisServiceResponse(result);
    }

    /**
     * @Description: -
     * @para -
     * @param cisJWTBody -
     * @param id -
     * @param isChain -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/3 21:28
     */
    private String getIdFromReq(CisJWTBody cisJWTBody, String id, boolean isChain) {
        if (id == null || id.equals("")) {
            if (isChain) {
                id = cisJWTBody.chainId;
            } else {
                if (cisJWTBody.clinicType == CommonConstants.NUMBER_ZERO
                        || cisJWTBody.clinicType == CommonConstants.NUMBER_TWO) {
                    id = cisJWTBody.clinicId;
                }
            }
        }
        return id;
    }

}
