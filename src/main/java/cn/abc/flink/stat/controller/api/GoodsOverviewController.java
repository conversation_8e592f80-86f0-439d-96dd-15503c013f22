package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.general.utils.R;
import cn.abc.flink.stat.service.cis.goods.overview.pojo.GoodsSellResp;
import cn.abc.flink.stat.service.cis.goods.overview.pojo.GoodsSellSummaryDto;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.concurrent.ExecutionException;

/**
 * @description: 进销存统计 - 药品概况
 * @author: dy
 * @create: 2021-07-02 10:14
 */

@RestController
@RequestMapping("/api/v2/sc/stat/goods")
@Deprecated
public class GoodsOverviewController {

    private static final Logger logger = LoggerFactory.getLogger(GoodsOverviewController.class);

    @GetMapping("/overview/summary")
    public CisServiceResponse selectSummary (
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestParam(value = "chainId", required = false) String chainId,
            @RequestParam(value = "clinicId", required = false) String clinicId,
            @RequestParam(value = "begindate", required = false) String begindate,
            @RequestParam(value = "enddate", required = false) String enddate) {
        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, chainId, clinicId);
//        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(chainId, clinicId);
        if (!params.isValid()) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        logger.info("chainId="+params.getChainId() + ", clinicId="+params.getClinicId() + ", beginDate="+begindate + ", endDate="+enddate);
        return new CisServiceResponse(new ArrayList<>());
    }

    @GetMapping("/overview/trend")
    public CisServiceResponse selectTrend (
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestParam(value = "chainId", required = false) String chainId,
            @RequestParam(value = "clinicId", required = false) String clinicId,
            @RequestParam(value = "begindate", required = false) String begindate,
            @RequestParam(value = "enddate", required = false) String enddate,
            @RequestParam(value = "groupBy", required = false) String groupBy) {
                AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, chainId, clinicId);
//        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(chainId, clinicId);
        if (!params.isValid()) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        logger.info("chainId="+params.getChainId() + ", clinicId="+params.getClinicId() + ", beginDate="+begindate + ", endDate="+enddate);
        return new CisServiceResponse(new ArrayList<>());
    }

    @GetMapping("/sell/summary")
    public CisServiceResponse selectSellSummary (
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestParam(value = "chainId", required = false) String chainId,
            @RequestParam(value = "clinicId", required = false) String clinicId,
            @RequestParam(value = "begindate", required = false) String begindate,
            @RequestParam(value = "enddate", required = false) String enddate) {
        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, chainId, clinicId);
//        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(chainId, clinicId);
        if (!params.isValid()) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        logger.info("chainId="+params.getChainId() + ", clinicId="+params.getClinicId() + ", beginDate="+begindate + ", endDate="+enddate);
        return new CisServiceResponse(new GoodsSellSummaryDto());
    }

    @GetMapping("/sell/topN")
    public CisServiceResponse selectSellTopN (
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestParam(value = "chainId", required = false) String chainId,
            @RequestParam(value = "clinicId", required = false) String clinicId,
            @RequestParam(value = "begindate", required = false) String begindate,
            @RequestParam(value = "enddate", required = false) String enddate,
            @RequestParam(value = "n", required = false) Integer n) throws ExecutionException, InterruptedException {
        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, chainId, clinicId);
//        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(chainId, clinicId);
        if (!params.isValid()) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        logger.info("chainId="+params.getChainId() + ", clinicId="+params.getClinicId() + ", beginDate="+begindate + ", endDate="+enddate);
        return new CisServiceResponse(new GoodsSellResp());
    }
}
