package cn.abc.flink.stat.controller.rpc;

import cn.abc.flink.stat.common.request.AbcPermission;
import cn.abc.flink.stat.common.request.params.MemberChargeParam;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.AchievementPromotionCardService;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievenmentDetailListRpcResp;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievenmentParam;
import cn.abc.flink.stat.service.cis.membercharge.MemberChargeService;
import cn.abc.flink.stat.service.cis.membercharge.domain.MemberChargeDetailRpcResp;
import cn.abcyun.common.model.AbcServiceResponseBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@ApiModel("营销rpc")
@RestController
@RequestMapping("/rpc/sc/stat/promotion")
@Api(value = "PromotionRpcController", description = "营销统计RPC接口", produces = "application/json")
public class PromotionRpcController {

    private static final Logger logger = LoggerFactory.getLogger(PromotionRpcController.class);

    @Autowired
    AchievementPromotionCardService achievementService;

    @Autowired
    MemberChargeService memberChargeService;

    @PostMapping(value = "/achievement/detail/list", name = "开卡充值业绩明细")
    @ResponseBody
    public AbcServiceResponseBody<AchievenmentDetailListRpcResp> daily(
            @RequestBody AchievenmentParam param) throws Exception {
        // 初始化时间
        param.initBeginDateAndEndDate();
        param.setPermission(new AbcPermission(1));
        return new AbcServiceResponseBody<>(achievementService.selectAchievementDetailByRpc(param));
    }

    @PostMapping(value = "/member/charge/detail", name = "会员充值业绩明细")
    @ResponseBody
    public AbcServiceResponseBody<MemberChargeDetailRpcResp> memberChargeDetail(
            @RequestBody MemberChargeParam param) throws Exception {
        // 初始化时间
        param.initBeginDateAndEndDate();
        param.initDs("yyyy");
        param.setPermission(new AbcPermission(1));
        return new AbcServiceResponseBody<>(memberChargeService.selectMemberChargeDetailByRpc(param));
    }

}
