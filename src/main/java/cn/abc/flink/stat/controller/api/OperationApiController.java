package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.StatResultUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.annotation.InterfaceCurrentLimiting;
import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.common.request.params.AbcCisRequestMagicValue;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.cis.charge.product.ChargeProductService;
import cn.abc.flink.stat.service.cis.charge.product.domain.ChargeProductSelectResult;
import cn.abc.flink.stat.service.cis.charge.product.pojos.OperationProductReqParams;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.operation.OperationStatService;
import cn.abc.flink.stat.service.cis.operation.domain.HospitalParams;
import cn.abc.flink.stat.service.cis.operation.domain.OperationChargeReqParams;
import cn.abc.flink.stat.service.cis.operation.domain.OperationComposeReqParams;
import cn.abc.flink.stat.service.cis.operation.domain.OperationFeeTypeReqParams;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.common.model.AbcServiceError;
import cn.abcyun.common.model.AbcServiceResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.Mapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 *
 */
@RestController
@RequestMapping("/api/v2/sc/stat/operation")
public class OperationApiController {
	private final Logger logger = LoggerFactory.getLogger(OperationApiController.class);
	@Autowired
	OperationStatService operationStatService;

	@Autowired
	ChargeProductService chargeProductService;

	/**
	 * 收费项目
	 *
	 * @param reqParams    参数
	 * @return ChargeProductResult
	 * @throws ExecutionException   exception
	 * @throws InterruptedException exception
	 */
	@InterfaceCurrentLimiting
	@PostMapping("/charge/product/list")
//	@JwtPermissions
	public AbcServiceResponseBody<V2StatResponse> productList(
			@RequestBody OperationProductReqParams reqParams) throws ExecutionException, InterruptedException {
		reqParams.initAbcPermission();
		if (reqParams.getDispensaryType().equals(HisTypeEnum.CLINIC.getTypeNumber())) {
			reqParams.setClinicId(reqParams.getHeaderClinicId());
		}
		StatConfigParam configParam = new StatConfigParam(reqParams.getChainId(), reqParams.getClinicId(),
				reqParams.getDispensaryType());
		return new AbcServiceResponseBody(chargeProductService.getChargeProductList(reqParams, configParam));
	}

	/**
	 * 收费项目导出
	 *
	 * @param reqParams    参数
	 * @param response     resp
	 * @throws ExecutionException   exception
	 * @throws InterruptedException exception
	 * @throws IOException          exception
	 */
	@PostMapping("/charge/product/list/export")
	@JwtPermissions
	public void productListExport(
			HttpServletResponse response,
			@RequestBody OperationProductReqParams reqParams
	) throws IOException, ExecutionException, InterruptedException {
		reqParams.initAbcPermission();
		if (reqParams.getDispensaryType().equals(HisTypeEnum.CLINIC.getTypeNumber())) {
			reqParams.setClinicId(reqParams.getHeaderClinicId());
		}
		reqParams.setIsExcport(1);
		chargeProductService.exportChargeProductList(response, reqParams);
	}

	/**
	 * 收费项目筛选框
	 *
	 * @param jwtEncodeStr jwt
	 * @param chainId      连锁id
	 * @param clinicId     门店id
	 * @param beginDate    开始时间
	 * @param endDate      结束时间
	 * @return AbcServiceResponseBody
	 * @throws ParseException       Exception
	 * @throws ExecutionException   Exception
	 * @throws InterruptedException Exception
	 */
	@RequestMapping("/charge/product/selection")
	public AbcServiceResponseBody<ChargeProductSelectResult> selectionList(
			@RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
			@RequestParam(value = "chainId", required = false) String chainId,
			@RequestParam(value = "clinicId", required = false) String clinicId,
			@RequestParam(value = "beginDate", required = false) String beginDate,
			@RequestParam(value = "endDate", required = false) String endDate
	) throws ParseException, ExecutionException, InterruptedException {
		AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, chainId, clinicId);
		if (!params.isValid()) {
			return new AbcServiceResponseBody<ChargeProductSelectResult>(null, new AbcServiceError(
					AbcCisRequestMagicValue.AUTH_ERROR_CODE,
					AbcCisRequestMagicValue.AUTH_ERROR));
		}
		endDate = TimeUtils.appendEnd(endDate);
		return new AbcServiceResponseBody<>(chargeProductService.getSelectionList(
				params.getChainId(),
				params.getClinicId(),
				beginDate,
				endDate));
	}

	/**
	 * 收费套餐接口
	 *
	 * @param reqParams    参数
	 * @return AbcServiceResponseBody
	 */
	@InterfaceCurrentLimiting
	@GetMapping("compose/list")
	@JwtPermissions
	public AbcServiceResponseBody selectComposeList(
			@Valid OperationComposeReqParams reqParams) {
		reqParams.initAbcPermission();
		return new AbcServiceResponseBody(operationStatService.selectComposeList(reqParams));
	}

	/**
	 * 收费套餐导出
	 *
	 * @param response     resp
	 * @param reqParams    reqParams
	 * @throws IOException IOException
	 */
	@GetMapping("compose/list/export")
	@JwtPermissions
	public void exportComposeList(
			HttpServletResponse response,
			@Valid OperationComposeReqParams reqParams) throws IOException {
		reqParams.initAbcPermission();
		if (!reqParams.getParams().isValid()) {
			return;
		}
		operationStatService.exportComposeList(response, reqParams);
	}

	/**
	 * 费用分类接口
	 *
	 * @param reqParams    reqParams
	 * @return feeType
	 */
	@GetMapping("fee/types/list")
	@JwtPermissions
	public AbcServiceResponseBody<Map<String, Object>> selectFeeClassifyList(
			@Valid OperationFeeTypeReqParams reqParams) {
		reqParams.initAbcPermission();
		Map<String, Object> body = new HashMap<>();
		List<Map<String, Object>> data = operationStatService.selectFeeClassifyList(reqParams.getHisType(), reqParams);
		body.put("list", data);
		return new AbcServiceResponseBody<Map<String, Object>>(body);
	}

	/**
	 * 费用分类导出接口
	 *
	 * @param response     resp
	 * @param reqParams    reqParams
	 * @throws IOException exception
	 */
	@GetMapping("fee/types/list/export")
	@JwtPermissions
	public void exportFeeClassifyList(
			HttpServletResponse response,
			@Valid OperationFeeTypeReqParams reqParams) throws IOException {
		reqParams.initAbcPermission();
		operationStatService.exportFeeClassifyList(response, reqParams.getHisType(), reqParams);
	}

	/**
	 * 20210805收费员统计接口需要增加 开票数量和作废发票数量统计
	 *
	 * @param reqParams    reqParams
	 * @return map
	 * @throws ExecutionException   ExecutionException
	 * @throws InterruptedException InterruptedException
	 */
	@InterfaceCurrentLimiting
	@PostMapping("charge/cashier/list")
	@JwtPermissions
	public AbcServiceResponseBody<V2StatResponse> selectChargeCashierList(
			@RequestBody OperationChargeReqParams reqParams
	) throws ExecutionException, InterruptedException {
		reqParams.initAbcPermission();
		reqParams.handleParams();
		reqParams.setEndDate(TimeUtils.appendEnd(reqParams.getEndDate()));
		return new AbcServiceResponseBody<>(operationStatService.selectChargeCashierList(reqParams));
	}

	/**
	 * 收费日报
	 * 20210805收费日报接口需要增加发票统计
	 *
	 * @param reqParams    reqParams
	 * @return AbcServiceResponseBody
	 * @throws ExecutionException   Exception
	 * @throws InterruptedException Exception
	 */
	@InterfaceCurrentLimiting
	@GetMapping("charge/report/list")
	@JwtPermissions
	public AbcServiceResponseBody<V2StatResponse> selectChargeReportList(
			@Valid OperationChargeReqParams reqParams
	) throws ExecutionException, InterruptedException {
		reqParams.initAbcPermission();
		reqParams.handleParams();
		if (reqParams.getClinicId() == null || reqParams.getClinicId().isEmpty()) {
			reqParams.setClinicId("");
			if (reqParams.getDispensaryType() == CommonConstants.NUMBER_TWO) {
				reqParams.setClinicId(reqParams.getHeaderClinicId());
			}
		}
		reqParams.setEndDate(TimeUtils.appendEnd(reqParams.getEndDate()));
		return new AbcServiceResponseBody<>(operationStatService.selectChargeReportList(reqParams));
	}

	/**
	 * 收费员统计筛选框
	 *
	 * @param jwtEncodeStr jwtEncodeStr
	 * @param viewMode     viewMode
	 * @param reqParams    reqParams
	 * @return map
	 * @throws InterruptedException Exception
	 * @throws ExecutionException   Exception
	 * @throws ParseException       Exception
	 */
	@GetMapping("charge/cashier/selection")
	public AbcServiceResponseBody<Map<String, Object>> selectChargeCashierSelection(
			@RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
			@RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode,
			@Valid OperationChargeReqParams reqParams
	) throws InterruptedException, ExecutionException, ParseException {
		reqParams.initAbcCisBaseQueryParams(jwtEncodeStr, viewMode,
				reqParams.getChainId(), reqParams.getClinicId());
		reqParams.initAbcPermission();
		if (!reqParams.getParams().isValid()) {
			return new AbcServiceResponseBody<Map<String, Object>>(null,
					new AbcServiceError(AbcCisRequestMagicValue.AUTH_ERROR_CODE,
							AbcCisRequestMagicValue.AUTH_ERROR));
		}
		reqParams.handleParams();
		reqParams.setEndDate(TimeUtils.appendEnd(reqParams.getEndDate()));
		return new AbcServiceResponseBody<>(operationStatService.selectChargeCashierSlection(reqParams));
	}

	/**
	 * 收费日报筛选框
	 *
	 * @param jwtEncodeStr jwtEncodeStr
	 * @param viewMode     viewMode
	 * @param reqParams    reqParams
	 * @return map
	 * @throws InterruptedException Exception
	 * @throws ExecutionException   Exception
	 * @throws ParseException       Exception
	 */
	@GetMapping("charge/report/selection")
	public AbcServiceResponseBody<Map<String, Object>> selectChargeReportSelection(
			@RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
			@RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode,
			@Valid OperationChargeReqParams reqParams
	) throws InterruptedException, ExecutionException, ParseException {
		reqParams.initAbcCisBaseQueryParams(jwtEncodeStr, viewMode,
				reqParams.getChainId(), reqParams.getClinicId());
		reqParams.initAbcPermission();
		if (!reqParams.getParams().isValid()) {
			return new AbcServiceResponseBody<Map<String, Object>>(null,
					new AbcServiceError(AbcCisRequestMagicValue.AUTH_ERROR_CODE,
							AbcCisRequestMagicValue.AUTH_ERROR));
		}
		reqParams.handleParams();
		reqParams.setEndDate(TimeUtils.appendEnd(reqParams.getEndDate()));
		return new AbcServiceResponseBody<>(operationStatService.selectChargeReportSlection(reqParams));
	}

	/**
	 * 收费员统计导出
	 * @param response  resp
	 * @param reqParams    reqParams
	 * @throws InterruptedException Exception
	 * @throws ExecutionException   Exception
	 * @throws IOException       Exception
	 */
	@GetMapping("charge/cashier/list/export")
	@JwtPermissions
	public void exportChargeCashierList(
	                                    HttpServletResponse response,
	                                    @Valid OperationChargeReqParams reqParams
	) throws IOException, ExecutionException, InterruptedException {
		reqParams.initAbcPermission();
		reqParams.handleParams();
		reqParams.setEndDate(TimeUtils.appendEnd(reqParams.getEndDate()));
		operationStatService.exportChargeCashierList(response, reqParams);
	}

	/**
	 * 收费日报导出
	 * 20210915 收费日报增加已开票金额字段和未开票金额
	 * @param response resp
	 * @param reqParams    reqParams
	 * @throws InterruptedException Exception
	 * @throws ExecutionException   Exception
	 * @throws IOException       Exception
	 */
	@GetMapping("charge/report/list/export")
	@JwtPermissions
	public void exportChargeReportList(
			HttpServletResponse response,
			@Valid OperationChargeReqParams reqParams
	) throws IOException, ExecutionException, InterruptedException {
		if (!reqParams.getParams().isValid()) {
			return;
		}
		reqParams.handleParams();
		reqParams.setEndDate(TimeUtils.appendEnd(reqParams.getEndDate()));
		if (reqParams.getClinicId() == null || reqParams.getClinicId().isEmpty()) {
			reqParams.setClinicId("");
			if (reqParams.getDispensaryType() == CommonConstants.NUMBER_TWO) {
				reqParams.setClinicId(reqParams.getHeaderClinicId());
			}
		}
		operationStatService.exportChargeReportList(response, reqParams.getViewMode(), reqParams);
	}

	/**
	 * 长护收费统计-结算
	 * @param params -
	 */
	@GetMapping("charge/hospital/settlement")
	@JwtPermissions
	public AbcServiceResponseBody<V2StatResponse> selectHospitalSettlement(@Valid HospitalParams params) {
		params.handleParams();
		return new AbcServiceResponseBody<V2StatResponse>(operationStatService.selectHospitalSettlement(params));
	}

	/**
	 * 长护收费统计-记账
	 * @param params -
	 */
	@GetMapping("charge/hospital/accounting")
	@JwtPermissions
	public AbcServiceResponseBody<V2StatResponse> selectHospitalAccounting(@Valid HospitalParams params) {
		return new AbcServiceResponseBody<V2StatResponse>(operationStatService.selectHospitalAccounting(params));
	}
}
