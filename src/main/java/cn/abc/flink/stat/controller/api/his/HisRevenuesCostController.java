package cn.abc.flink.stat.controller.api.his;

import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.cis.config.pojo.StatOrganTableConfig;
import cn.abc.flink.stat.service.his.achievement.cost.IHisRevenuesCostSevice;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisChargeSettlementSelecttionResp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisChargedSelectionReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisChargedWardSelectionResp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisIncomeSummaryReq;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisReciptSummaryReq;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostChargeReportSelectRsp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostChargeSettlePatientSelectRsp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostPhysicalExaminationDayReportRsp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargeReportDetailRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargeReportRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargedProductReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargedSettlePatientReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HospCashierDailyReportParams;
import cn.abc.flink.stat.service.his.revenue.product.HisRevenueOutpatientProductService;
import cn.abc.flink.stat.service.his.revenue.product.domain.HisRevenueOutpatientProductReq;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.Mapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.controller.api.his
 * @Author: zs
 * @CreateTime: 2023-12-02  10:35
 * @Description: 营收成本-结算统计
 * @Version: 1.0
 */
@RestController
@RequestMapping("/api/v2/sc/stat/his/revenues")
@Api(value = "HisRevenuesCostController", description = "医院统计-营收成本", produces = "application/json")
public class HisRevenuesCostController {

    private static final Logger logger = LoggerFactory.getLogger(HisRevenuesCostController.class);

    @Autowired
    private IHisRevenuesCostSevice sevice;

    @Resource
    private HisRevenueOutpatientProductService hisRevenueOutpatientProductService;

    @JwtPermissions
    @GetMapping("/cashier/daily/report")
    @ApiOperation(value = "收费员日报(医院)", produces = "application/json")
    public V2StatResponse getCashierDailyReport(@RequestBody HospCashierDailyReportParams params) throws ExecutionException, InterruptedException {
        logger.info("请求参数: {} ", JSON.toJSONString(params));
        params.setBeginDate(TimeUtils.appendBegin(params.getBeginDate()));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        params.initDs();
        return sevice.selectCashierDailyReport(params);
    }

    @JwtPermissions
    @PostMapping("/cashier/daily/report")
    @ApiOperation(value = "收费员日报(医院)", produces = "application/json")
    public V2StatResponse selectCashierDailyReport(@RequestBody HospCashierDailyReportParams params) throws ExecutionException, InterruptedException {
        logger.info("请求参数: {} ", JSON.toJSONString(params));
        params.setBeginDate(TimeUtils.appendBegin(params.getBeginDate()));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        params.initDs();
        return sevice.selectCashierDailyReport(params);
    }

    @JwtPermissions
    @GetMapping("/cost/settle/fee/type")
    @ApiOperation(value = "医院统计-营收成本-费用分类", produces = "application/json")
    public V2StatResponse selectFeeType(HisRevenuesCostReqParams params) {
        logger.info("/api/v2/sc/stat/his/revenues/cost/settle/fee/type请求参数: {} ", JSON.toJSONString(params));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        return sevice.selectFeeType(params);
    }

    @JwtPermissions
    @GetMapping("/cost/settle/transaction")
    @ApiOperation(value = "医院统计-营收成本-单据", produces = "application/json")
    public V2StatResponse selectTransaction(HisRevenuesCostReqParams params) {
        logger.info("/api/v2/sc/stat/his/revenues/cost/settle/transaction请求参数: {} ", JSON.toJSONString(params));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        return sevice.selectTransaction(params);
    }

    @JwtPermissions
    @PostMapping("/cost/settle/project")
    @ApiOperation(value = "医院统计-营收成本-项目", produces = "application/json")
    public V2StatResponse selectProject(@RequestBody HisRevenuesCostReqParams params) {
        logger.info("/api/v2/sc/stat/his/revenues/cost/project请求参数: {} ", JSON.toJSONString(params));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        return sevice.selectProject(params);
    }

    @JwtPermissions
    @GetMapping("/cost/charge/fee/type")
    @ApiOperation(value = "医院统计-营收成本-计费-费用分类", produces = "application/json")
    public V2StatResponse selectChargeFeeType(HisRevenuesCostReqParams params) {
        logger.info("/api/v2/sc/stat/his/revenues/cost/charg/fee/type请求参数: {} ", JSON.toJSONString(params));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        return sevice.selectChargeFeeType(params);
    }

    @JwtPermissions
    @GetMapping("/cost/charge/patient")
    @ApiOperation(value = "医院统计-营收成本-计费-患者", produces = "application/json")
    public V2StatResponse selectChargPatient(HisRevenuesCostReqParams params) {
        logger.info("/api/v2/sc/stat/his/revenues/cost/charg/patient请求参数: {} ", JSON.toJSONString(params));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        return sevice.selectChargPatient(params);
    }

    @JwtPermissions
    @GetMapping("/cost/charge/project")
    @ApiOperation(value = "医院统计-营收成本-计费-项目", produces = "application/json")
    public V2StatResponse selectChargProject(HisRevenuesCostReqParams params) {
        logger.info("/api/v2/sc/stat/his/revenues/cost/charg/project请求参数: {} ", JSON.toJSONString(params));
        params.setBeginDate(TimeUtils.appendBegin(params.getBeginDate()));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        params.setDimension();
        return sevice.selectChargProject(params);
    }

    @JwtPermissions
    @GetMapping("/charged/selection")
    @ApiOperation(value = "医院统计-计费相关统计-筛选框", produces = "application/json")
    public AbcServiceResponseBody<HisChargedWardSelectionResp> selectChargedWardSelection(HisChargedSelectionReqParams params) throws ExecutionException, InterruptedException {
        params.setBeginDate(TimeUtils.appendBegin(params.getBeginDate()));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        params.initDs();
        logger.info("/api/v2/sc/stat/his/revenues/cost/execute/ward请求参数: {} ", JSON.toJSONString(params));
        return new AbcServiceResponseBody<>(sevice.selectChargedSelection(params));
    }

    @JwtPermissions
    @GetMapping("/charged/settlement/selection")
    @ApiOperation(value = "医院统计-结算相关统计-筛选框", produces = "application/json")
    public HisChargeSettlementSelecttionResp selectChargedSettlementSelection(AbcScStatRequestParams params) throws ExecutionException, InterruptedException {
        params.setBeginDate(TimeUtils.appendBegin(params.getBeginDate()));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        params.initDs();
        logger.info("/api/v2/sc/stat/his/revenues/cost/execute/ward请求参数: {} ", JSON.toJSONString(params));
        return sevice.selectChargedSettlementSelection(params);
    }

    @JwtPermissions
    @GetMapping("/cost/cost/charge/report")
    @ApiOperation(value = "医院统计-营收成本-住院收费日月报", produces = "application/json")
    public HisRevenuesCostChargeReportRes selectChargeReport(HisRevenuesCostReqParams params) {
        logger.info("/api/v2/sc/stat/his/revenues/cost/charge/report请求参数: {} ", JSON.toJSONString(params));
        params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
        return sevice.selectChargeReport(params);
    }

    @JwtPermissions
    @GetMapping("/cost/charge/deposit")
    @ApiOperation(value = "医院统计-营收成本-住院押金统计", produces = "application/json")
    public V2StatResponse selectChargeDeposit(HisRevenuesCostReqParams params) throws Exception {
        logger.info("/api/v2/sc/stat/his/revenues/cost/charge/deposit请求参数: {} ", JSON.toJSONString(params));
        params.initBeginDateAndEndDate();
        return sevice.selectChargeDeposit(params);
    }

    @JwtPermissions
    @GetMapping("/cost/charge/deposit/type/selection")
    @ApiOperation(value = "医院统计-营收成本-住院押金统计-押金类型筛选", produces = "application/json")
    public V2StatResponse selectChargeDepositTypeSelection(HisRevenuesCostReqParams params) {
        return sevice.selectChargeDepositTypeSelection(params);
    }

    @PostMapping("/cost/charged/product/list")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收成本-计费项目统计", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> selectChargeProduct (@RequestBody HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException {
        return new AbcServiceResponseBody<>(sevice.selectChargedProduct(params));
    }

    @PostMapping("/outpatient/product/list")
    @JwtPermissions
    @ApiOperation(value = "医院统计-收费项目统计-门诊", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> getOuatpatientProductList(@RequestBody HisRevenueOutpatientProductReq params) throws ExecutionException, InterruptedException {
        return new AbcServiceResponseBody<>(hisRevenueOutpatientProductService.getOutpatientProduct(params));
    }


    @GetMapping("/cost/fee-types")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收统计-项目收入-按类型", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> selectChargedFeeTypes (HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException {
        params.initParams();
        return new AbcServiceResponseBody<>(sevice.selectChargedFeeTypes(params));
    }

    @GetMapping("/cost/products")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收统计-项目收入-按项目", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> selectChargedProducts (HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException {
        params.initParams();
        return new AbcServiceResponseBody<>(sevice.selectChargedProducts(params));
    }

    @GetMapping("/income/summary")
    @JwtPermissions
    @ApiOperation(value = "全业务收入汇总表", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> getIncomeSummary(HisIncomeSummaryReq params) throws ExecutionException, InterruptedException {
        return new AbcServiceResponseBody<>(sevice.selectInomeSummary(params));
    }

    @GetMapping("/receipt/summary")
    @JwtPermissions
    @ApiOperation(value = "全院收费汇总", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> getReceiptSummary(@RequestBody HisReciptSummaryReq params) throws ExecutionException, InterruptedException {
        return new AbcServiceResponseBody<>(sevice.selectReceiptSummary(params));
    }

    @PostMapping("/receipt/summary")
    @JwtPermissions
    @ApiOperation(value = "全院收费汇总", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> postReceiptSummary(@RequestBody HisReciptSummaryReq params) throws ExecutionException, InterruptedException {
        return new AbcServiceResponseBody<>(sevice.selectReceiptSummary(params));
    }

    @GetMapping("/cost/charged/settle/patient")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收统计-收费单据", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> selectChargedSettlePatient (HisRevenuesCostChargedSettlePatientReqParams params) throws ExecutionException, InterruptedException {
        params.initBeginDateAndEndDate();
        return new AbcServiceResponseBody<>(sevice.selectChargedSettlePatient(params));
    }

    @GetMapping("/cost/charged/settle/patient/select")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收统计-收费单据-筛选框", produces = "application/json")
    public AbcServiceResponseBody<HisRevenueCostChargeSettlePatientSelectRsp> selectChargedSettlePatientSelect (HisRevenuesCostChargedSettlePatientReqParams params) throws Exception {
        params.initBeginDateAndEndDate();
        return new AbcServiceResponseBody<>(sevice.selectChargedSettlePatientSelect(params));
    }

    @GetMapping("/cost/physical/examination/charge/day/report")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收成本-收款对账报表-体检收费日报表", produces = "application/json")
    public AbcServiceResponseBody<HisRevenueCostPhysicalExaminationDayReportRsp> selectPhysicalExaminationChargeDayReport (HisRevenuesCostReqParams params) throws Exception {
        params.initBeginDateAndEndDate();
        params.initDs();
        return new AbcServiceResponseBody<>(sevice.selectPhysicalExaminationChargeDayReport(params));
    }

    @GetMapping("/cost/charge/report/config")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收成本-收款对账报表-收费日报配置", produces = "application/json")
    public AbcServiceResponseBody<List<StatOrganTableConfig>> selectChargeDayReportConfig (HisRevenuesCostReqParams params) {
        return new AbcServiceResponseBody<>(sevice.selectChargeDayReportConfig(params));
    }

    @GetMapping("/cost/charge/report/detail")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收成本-收款对账报表-收费日报明细", produces = "application/json")
    public AbcServiceResponseBody<List<HisRevenuesCostChargeReportDetailRes>> selectChargeDayReportDetail (HisRevenuesCostReqParams params) throws Exception {
        return new AbcServiceResponseBody<>(sevice.selectChargeDayReportDetail(params));
    }

    @GetMapping("/cost/charge/report/select")
    @JwtPermissions
    @ApiOperation(value = "医院统计-营收成本-收款对账报表-收费日报-筛选框", produces = "application/json")
    public AbcServiceResponseBody<HisRevenueCostChargeReportSelectRsp> selectChargeDayReportSelect (HisRevenuesCostReqParams params) throws Exception {
        return new AbcServiceResponseBody<>(sevice.selectChargeDayReportSelect(params));
    }

    @GetMapping("/cost/charge/report/export/local")
    @ApiOperation(value = "医院统计-营收成本-收款对账报表-收费日报明细", produces = "application/json")
    public void selectChargeDayReportDetailExport(HttpServletResponse response,
                                                  HisRevenuesCostReqParams params) throws Exception {
        sevice.selectChargeDayReportDetailExport(response, params);
    }
}
