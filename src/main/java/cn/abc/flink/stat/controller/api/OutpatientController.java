package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.general.utils.R;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientFirstRevisitRsp;
import cn.abc.flink.stat.service.cis.outpatient.OutpatientService;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListResp;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientParam;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/18 11:21 上午
 * @modified ljc
 */
@RestController
@RequestMapping("/api/v2/sc/stat/")
public class OutpatientController {
    private static final Logger logger = LoggerFactory.getLogger(OutpatientController.class);
    @Autowired
    private OutpatientService outpatientService;

    /**
     * @Description: 运营分析-患者清单
     * @param
     * @param request -
     * @param jwtEncodeStr -
     * @param chainViewMode -
     * @param clinicNodeType -
     * @param param chainId -clinicId -beginDate -endDate -patientId -pageindex -pagesize -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/10 18:54
     * @throws ExecutionException -
     * @throws InterruptedException -
     */
    @GetMapping("/patient/list")
    public CisServiceResponse getPatientList(
            HttpServletRequest request,
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer chainViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) Integer clinicNodeType,
            @ModelAttribute OutpatientParam param
    ) throws ExecutionException, InterruptedException {
        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, param.getChainId(), param.getClinicId());
        if (!params.isValid()) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        param.setChainId(params.getChainId());
        param.setClinicId(params.getClinicId());
        param.setEndDate(TimeUtils.appendEnd(param.getEndDate()));
        String currentEmployeeId = params.getEmployeeId();
        if (param.getPageIndex() != null && param.getPageSize() != null) {
            param.setPageIndex(param.getPageIndex() * param.getPageSize());
        }
        logger.info("/api/v2/sc/stat/patient/list|" + MapUtils.toString(request.getParameterMap()));
        OutpatientListResp resp = outpatientService.
                selectPatientList(param, currentEmployeeId, chainViewMode, clinicNodeType);
        return new CisServiceResponse(resp);
    }

    /**
     * @Description: 运营分析-患者清单-导出
     * @param
     * @param response -
     * @param request -
     * @param jwtEncodeStr -
     * @param param chainId -clinicId -beginDate -endDate -patientId -
     * @return
     * @Author: zs
     * @Date: 2022/8/10 19:23
     * @throws ExecutionException  -
     * @throws InterruptedException -
     * @throws IOException -
     */
    @GetMapping("/patient/list/export")
    public void getPatientList(HttpServletResponse response,
                               HttpServletRequest request,
                               @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
                               @ModelAttribute OutpatientParam param)
            throws ExecutionException, InterruptedException, IOException {
        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, param.getChainId(), param.getClinicId());
        param.setChainId(params.getChainId());
        param.setClinicId(params.getClinicId());
        logger.info("/api/v2/sc/stat/patient/list/export|" + MapUtils.toString(request.getParameterMap()));
        outpatientService.exportPatientList(response, param);
    }

    /**
     * @Description: 运营分析-门诊日志
     * @param
     * @param param chainId -clinicId -beginDate -endDate -patientId -employeeId -type -pageindex -pagesize -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/10 19:37
     * @throws ExecutionException -
     * @throws InterruptedException -
     * @throws IllegalAccessException -
     * @throws NoSuchMethodException -
     * @throws InvocationTargetException -
     */
    @GetMapping("/outpatient/list")
    @JwtPermissions
    public CisServiceResponse getOutpatientList(
            @ModelAttribute OutpatientParam param) throws Exception {
        if (param.getPageIndex() != null && param.getPageSize() != null) {
            param.setPageIndex(param.getPageIndex() * param.getPageSize());
        }
        param.initParam();
        logger.info("/api/v2/sc/stat/outpatient/list|" + JSON.toJSONString(param));
        OutpatientListResp resp = outpatientService.
                selectOutpatientList(param, param.getHisType(), Integer.valueOf(param.getViewMode()), param.getParams().getNodeType(), param.getParams().getEmployeeId());
        return new CisServiceResponse(resp);
    }
    /**
     * @Description:
     * @param
     * @param response -
     * @param request -
     * @param jwtEncodeStr -
     * @param hisType -
     * @param param chainId -clinicId -beginDate -endDate -patientId -employeeId -type -pageindex -pagesize -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 18:50
     * @throws ExecutionException -
     * @throws InterruptedException -
     * @throws IllegalAccessException -
     * @throws NoSuchMethodException -
     * @throws InvocationTargetException -
     * @throws IOException -
     */
    @GetMapping("/outpatient/list/export")
    public void exportOutpatientList(
            HttpServletResponse response,
            HttpServletRequest request,
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE, defaultValue = "0") String hisType,
            @ModelAttribute OutpatientParam param)
            throws Exception {
        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, param.getChainId(), param.getClinicId());
        param.setChainId(params.getChainId());
        param.setClinicId(params.getClinicId());
        logger.info("/api/v2/sc/stat/outpatient/list|" + MapUtils.toString(request.getParameterMap()));
        outpatientService.exportOutpatientList(response, param, hisType);
    }

    /**
     * @Description:
     * @param
     * @param jwtEncodeStr -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @param employeeId -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/11 19:05
     */
    @GetMapping("/outpatient/summary")
    public CisServiceResponse getOutpatientSummary(
            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
            @RequestParam(value = "chainId", required = false) String chainId,
            @RequestParam(value = "clinicid", required = false) String clinicId,
            @RequestParam(value = "beginDate", required = true) String beginDate,
            @RequestParam(value = "endDate", required = true) String endDate,
            @RequestParam(value = "employeeId", required = false) String employeeId) {
        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(jwtEncodeStr, chainId, clinicId);
//        AbcCisBaseQueryParams params = new AbcCisBaseQueryParams(chainId, clinicId);
        if (!params.isValid()) {
            return new CisServiceResponse(R.buildError(R.AUTH_ERROR, "auth error"));
        }
        endDate = TimeUtils.appendEnd(endDate);
        return new CisServiceResponse(outpatientService.
                selectOutpatientSummary(params, beginDate, endDate, employeeId));
    }

    /**
     * @Description:
     * @param
     * @param param chainId -clinicId -beginDate -endDate -employeeId -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2022/8/11 19:08
     */
    @GetMapping("/outpatient-first-revisit/info")
    @JwtPermissions
    public CisServiceResponse getOutpatientFirstRevisitNum(
            @ModelAttribute OutpatientParam param
    ) {
        List<OutpatientFirstRevisitRsp> list = outpatientService.getOutpatientFirstRevisitNum(param.getChainId(),
                param.getClinicId(), param.getBeginDate(), TimeUtils.appendEnd(param.getEndDate()),
                param.getEmployeeId());
        Map<String, Object> map = new HashMap<>();
        list.stream().forEach(ofr -> {
            if (ofr != null) {
                map.put("outpatientCount", ofr.getOutpatientCount());
                map.put("firstVisitOutpatientCount", ofr.getFirstVisitCount());
                map.put("reVisitOutpatientCount", ofr.getReVisitCount());
            }
        });
        return new CisServiceResponse(map);
    }

//    @RequestMapping("/revisit")
//    public CisServiceResponse getRevisitStat(
//            HttpServletRequest request,
////            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
//            @RequestParam(value = "chainId", required = false) String chainId,
//            @RequestParam(value = "clinicId", required = false) String clinicId,
//            @RequestParam(value = "beginDate", required = true) String beginDate,
//            @RequestParam(value = "endDate", required = true) String endDate,
//            @RequestParam(value = "dimension", required = true) String dimension,
//            @RequestParam(value = "dateDimension", required = true) String dateDimension,
//            @RequestParam(value = "doctorIds", required = false) String doctorIds,
//            @RequestParam(value = "departmentIds", required = false) String departmentIds,
//            @RequestParam(value = "pageindex", required = false) Integer pageindex,
//            @RequestParam(value = "pagesize", required = false) Integer pagesize){
//        logger.info("/api/v2/sc/stat/outpatient/revisit|" + MapUtils.toString(request.getParameterMap()));
//
//        List<RevisitStatRsp> rs =
//                outpatientService.selectRevisitStat(chainId, clinicId, beginDate, endDate, dimension,dateDimension,
//                doctorIds, pageindex, pagesize,departmentIds);
//
//        return new CisServiceResponse(rs);
//    }
//
//    @RequestMapping("/visit/source")
//    public CisServiceResponse getVisitSource(
//            HttpServletRequest request,
////            @RequestHeader(value = CisJWTUtils.JWT_HEADER_KEY) String jwtEncodeStr,
//            @RequestParam(value = "chainId", required = false) String chainId,
//            @RequestParam(value = "clinicId", required = false) String clinicId,
//            @RequestParam(value = "beginDate", required = true) String beginDate,
//            @RequestParam(value = "endDate", required = true) String endDate,
//            @RequestParam(value = "dimension", required = true) String dimension,
//            @RequestParam(value = "dateDimension", required = true) String dateDimension,
//            @RequestParam(value = "sourceIds", required = true) String sourceIds,
//            @RequestParam(value = "isRevisit", required = false) Integer isRevisit,
//            @RequestParam(value = "pageindex", required = false) Integer pageindex,
//            @RequestParam(value = "pagesize", required = false) Integer pagesize){
//
//        logger.info("/api/v2/sc/stat/outpatient/visit/source|" + MapUtils.toString(request.getParameterMap()));
//        VisitSourceRsp rs =
//                outpatientService.selectVisitSource(chainId, clinicId, dimension,dateDimension,beginDate,
//                endDate, pageindex, pagesize,isRevisit,sourceIds);
//
//        return new CisServiceResponse(rs);
//    }

}
