package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.PromotionReferralReferrerRewardService;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralReferrerRewardParam;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.RevenueChargeDetailService;
import cn.abcyun.common.model.AbcServiceResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 营销统计-新老客统计controller
 * @author: lzq
 * @Date: 2023/10/24
 */
@RestController
@RequestMapping("/api/v2/sc/stat/promotion/referral")
public class PromotionReferralReferrerRewardController {

    private static final Logger logger = LoggerFactory.getLogger(PromotionReferralReferrerRewardController.class);

    @Autowired
    PromotionReferralReferrerRewardService promotionReferralReferrerRewardService;


    /**
     * @param param 老带新统计param
     * @return AbcServiceResponseBody 筛选框resp
     * @throws Exception e
     */
    @GetMapping(value = "/selection", name = "查询筛选框")
    @JwtPermissions
    public AbcServiceResponseBody selection(
            PromotionReferralReferrerRewardParam param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        // 初始化时间
        param.initBeginDateAndEndDate();
        return new AbcServiceResponseBody(promotionReferralReferrerRewardService.selectSelection(param));
    }

    /**
     * @param param 老带新统计param
     * @return AbcServiceResponseBody 奖励明细resp
     * @throws Exception e
     */
    @GetMapping(value = "/reward/detail", name = "奖励明细")
    @JwtPermissions
    public AbcServiceResponseBody rewardDetail(
            PromotionReferralReferrerRewardParam param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        // 初始化时间
        param.initBeginDateAndEndDate();
        return new AbcServiceResponseBody(promotionReferralReferrerRewardService.selectRewardDetail(param));
    }

    /**
     * @param param 老带新统计param
     * @return AbcServiceResponseBody 奖励明细resp
     * @throws Exception e
     */
    @GetMapping(value = "/referrer/achievement", name = "推荐人业绩")
    @JwtPermissions
    public AbcServiceResponseBody referrerAchievement(
            PromotionReferralReferrerRewardParam param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        // 初始化时间
        param.initBeginDateAndEndDate();
        return new AbcServiceResponseBody(promotionReferralReferrerRewardService.selectReferrerAchievement(param));
    }

    /**
     * @param param 老带新统计param
     * @return AbcServiceResponseBody 奖励明细resp
     * @throws Exception e
     */
    @GetMapping(value = "/overview/summary", name = "活动效果-汇总")
    @JwtPermissions
    public AbcServiceResponseBody overviewSummary(
            PromotionReferralReferrerRewardParam param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        // 初始化时间
        param.initBeginDateAndEndDate();
        logger.info("活动效果-汇总param:{}", param);
        return new AbcServiceResponseBody(promotionReferralReferrerRewardService.selectOverviewSummary(param));
    }

    /**
     * @param param 老带新统计param
     * @return AbcServiceResponseBody 奖励明细resp
     * @throws Exception e
     */
    @GetMapping(value = "/overview/trend", name = "活动效果-新客以及消费趋势图")
    @JwtPermissions
    public AbcServiceResponseBody overviewNewPatientTrend(
            PromotionReferralReferrerRewardParam param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        // 初始化时间
        param.initBeginDateAndEndDate();
        return new AbcServiceResponseBody(promotionReferralReferrerRewardService.selectOverviewTrend(param));
    }

    /**
     * @param param 老带新统计param
     * @return AbcServiceResponseBody 奖励明细resp
     * @throws Exception e
     */
    @GetMapping(value = "/overview/transform/analysis", name = "活动效果-新客消费转化分析")
    @JwtPermissions
    public AbcServiceResponseBody overviewNewPatientPayTransformAnalysis(
            PromotionReferralReferrerRewardParam param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        // 初始化时间 设置clinicId
        param.initBeginDateAndEndDate();
        return new AbcServiceResponseBody(promotionReferralReferrerRewardService.selectOverviewNewPatientPayTransformAnalysis(param));
    }

    /**
     * @param param 老带新统计param
     * @return AbcServiceResponseBody 奖励明细resp
     * @throws Exception e
     */
    @GetMapping(value = "/list/total", name = "活动列表-底部合计行")
    @JwtPermissions
    public AbcServiceResponseBody listTotal(
            PromotionReferralReferrerRewardParam param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        return new AbcServiceResponseBody(promotionReferralReferrerRewardService.selectListTotal(param));
    }

}
