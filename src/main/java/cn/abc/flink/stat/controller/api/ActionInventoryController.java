package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.cis.goods.ininventory.InInventoryService;
import cn.abc.flink.stat.service.cis.goods.ininventory.domain.InInventoryParams;
import cn.abc.flink.stat.service.cis.goods.outinventory.OutInventoryService;
import cn.abc.flink.stat.service.cis.goods.outinventory.domain.*;
import cn.abcyun.common.model.AbcServiceResponseBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/v2/sc/stat/inventory-action")
@Api(value = "ActionInventoryController", description = "出入库统计接口", produces = "application/json")
public class ActionInventoryController {

    @Autowired
    protected InInventoryService inInventoryService;

    @Autowired
    protected OutInventoryService outInventoryService;

    @JwtPermissions
    @GetMapping(value = "/in/info")
    @ApiOperation(value = "库存统计-入库统计-列表", produces = "application/json")
    public AbcServiceResponseBody<Object> inInventory(InInventoryParams param) {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        param.initParam();
        return new AbcServiceResponseBody<>(inInventoryService.inInventory(param));
    }

    @JwtPermissions
    @GetMapping(value = "/in/action")
    @ApiOperation(value = "库存统计-入库统计-筛选框", produces = "application/json")
    public AbcServiceResponseBody<Object> inAvailableAction(InInventoryParams param) {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        param.initParam();
        return new AbcServiceResponseBody<>(inInventoryService.availableAction(param));
    }

    @JwtPermissions
    @GetMapping(value = "/out/info")
    @ApiOperation(value = "库存统计-出库统计-列表", produces = "application/json")
    public AbcServiceResponseBody<Object> outInventory(
            InInventoryParams param,
            @RequestParam(value = "type", required = false) String type) {
        param.initBeginDateAndEndDate();
        return new AbcServiceResponseBody<>(outInventoryService.outInventory(param, type));
    }

    @JwtPermissions
    @GetMapping(value = "/out/action")
    @ApiOperation(value = "库存统计-出库统计-动作筛选框", produces = "application/json")
    public AbcServiceResponseBody<Object> outAvailableAction(InInventoryParams param) {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getHeaderClinicId());
        }
        return new AbcServiceResponseBody<>(outInventoryService.availableAction(param));
    }

    @JwtPermissions
    @GetMapping(value = "/damaged/out/list")
    @ApiOperation(value = "库存统计-报损出库-列表", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> damagedOutList(InInventoryParams param) throws Exception {
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(param.getParams().getHeaderClinicId());
        }
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.damagedOutList(param));
    }

    @JwtPermissions
    @GetMapping(value = "/consumption/out/list")
    @ApiOperation(value = "库存统计-消耗出库-列表", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> consumptionOutList(InInventoryParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.consumptionOutList(param));
    }

    @JwtPermissions
    @GetMapping(value = "/other/out/list")
    @ApiOperation(value = "库存统计-其他出库-列表", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> otherOutList(InInventoryParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.otherOutList(param));
    }

    @JwtPermissions
    @GetMapping(value = "/damaged/out/pharmacy/select")
    @ApiOperation(value = "库存统计-报损出库-药房筛选框", produces = "application/json")
    public AbcServiceResponseBody<List<Map<String, Object>>> damagedOutPharmacySelect(InInventoryParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.damagedOutPharmacySelect(param));
    }

    @JwtPermissions
    @GetMapping(value = "/consumption/out/pharmacy/select")
    @ApiOperation(value = "库存统计-消耗出库-药房筛选框", produces = "application/json")
    public AbcServiceResponseBody<List<Map<String, Object>>> consumptionOutPharmacySelect(InInventoryParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.consumptionOutPharmacySelect(param));
    }

    @JwtPermissions
    @GetMapping(value = "/other/out/pharmacy/select")
    @ApiOperation(value = "库存统计-其他出库-药房筛选框", produces = "application/json")
    public AbcServiceResponseBody<List<Map<String, Object>>> otherOutPharmacySelect(InInventoryParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.otherOutPharmacySelect(param));
    }

    @JwtPermissions
    @GetMapping(value = "/collect/list")
    @ApiOperation(value = "库存统计-领用统计-列表", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> collectList(InInventoryParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.collectList(param));
    }

    @JwtPermissions
    @GetMapping(value = "/collect/list/select")
    @ApiOperation(value = "库存统计-领用统计-筛选框", produces = "application/json")
    public AbcServiceResponseBody<CollectSelectionRsp> collectListSelect(InInventoryParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.collectListSelect(param));
    }

    @JwtPermissions
    @GetMapping(value = "/dispense/selection")
    @ApiOperation(value = "库存统计-发药统计-筛选框", produces = "application/json")
    public AbcServiceResponseBody<DispenseSelectionRsp> dispenseSelection(InventoryDispenseParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.dispenseSelection(param));
    }

    @JwtPermissions
    @GetMapping(value = "/dispense/summary")
    @ApiOperation(value = "库存统计-发药统计-汇总", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> dispenseSummary(InventoryDispenseParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.dispenseSummary(param));
    }

    @JwtPermissions
    @GetMapping(value = "/dispense/goods")
    @ApiOperation(value = "库存统计-发药统计-药品", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> dispenseGoods(InventoryDispenseParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.dispenseGoods(param));
    }

    @JwtPermissions
    @GetMapping(value = "/dispense/record")
    @ApiOperation(value = "库存统计-发药统计-流水", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> dispenseRecord(InventoryDispenseParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.dispenseRecord(param));
    }

    @JwtPermissions
    @GetMapping(value = "/production/list")
    @ApiOperation(value = "库存统计-生产出库-列表", produces = "application/json")
    public AbcServiceResponseBody<V2StatResponse> productionList(InventoryProductionOutParams param) throws Exception {
        param.initParam();
        param.setIsExport(false);
        return new AbcServiceResponseBody<>(outInventoryService.productionList(param));
    }

    @JwtPermissions
    @GetMapping(value = "/production/selection")
    @ApiOperation(value = "库存统计-生产出库-筛选框", produces = "application/json")
    public AbcServiceResponseBody<ProductionOutSelectionRsp> productionSelection(InventoryProductionOutParams param) throws Exception {
        param.initParam();
        return new AbcServiceResponseBody<>(outInventoryService.productionSelection(param));
    }
}
