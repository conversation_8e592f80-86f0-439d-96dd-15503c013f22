package cn.abc.flink.stat.controller.rpc;

import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.request.params.SupplierParam;
import cn.abc.flink.stat.common.response.StatResponse;
import cn.abc.flink.stat.service.cis.b2c.order.B2cOrderService;
import cn.abc.flink.stat.service.cis.b2c.order.domain.B2cOrderRpcReq;
import cn.abc.flink.stat.service.cis.supplier.SupplierService;
import cn.abc.flink.stat.service.cis.supplier.domain.SupplierInfo;
import cn.abc.flink.stat.service.cis.supplier.domain.SupplierRpcOrganReq;
import cn.abc.flink.stat.service.cis.supplier.domain.SupplierRpcReq;
import cn.abc.flink.stat.service.cis.supplier.domain.SupplierRpcResp;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.common.model.AbcServiceResponseBody;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/12 17:28
 */
@RestController
@RequestMapping("/rpc/sc/stat/b2c/ec/order")
@Api(value = "EcOrderRpcController", description = "电商订单统计RPC接口", produces = "application/json")
public class EcOrderRpcController {

    private static final Logger logger = LoggerFactory.getLogger(EcOrderRpcController.class);

    @Autowired
    private B2cOrderService b2cOrderService;

    /**
     * @param req 供应商param
     * @return 神奇供应商的统计数据
     * @throws Exception -
     */
    @ResponseBody
    @RequestMapping(value = "/daily", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public AbcServiceResponseBody selectEcOrderDaily(@RequestBody B2cOrderRpcReq req) throws Exception {
        logger.info("rpc接口调用:/sc/stat/ec/order/rpc/daily,接口入参数：{}", JSON.toJSONString(req));
        // 初始化时间
        req.setBeginDate(TimeUtils.appendBegin(req.getBeginDate()));
        req.setEndDate(TimeUtils.appendEnd(req.getEndDate()));
        req.setYesterdayBeginDate(TimeUtils.appendBegin(TimeUtils.addDay(req.getBeginDate(), -1)));
        req.setYesterdayEndDate(TimeUtils.appendEnd(TimeUtils.addDay(req.getEndDate(), -1)));
        return new AbcServiceResponseBody(b2cOrderService.selectDaily(req));
    }
}
