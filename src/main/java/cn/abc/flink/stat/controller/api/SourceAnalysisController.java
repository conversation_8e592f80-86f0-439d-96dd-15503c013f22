package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.cis.source.analysis.SourceAnalysisService;
import cn.abc.flink.stat.service.cis.source.analysis.domain.SourceAnalysisSumDto;
import cn.abc.flink.stat.service.cis.source.analysis.param.SourceAnalysisParam;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 门诊医务统计-来源分析
 */
@RestController
@RequestMapping("/api/v2/sc/stat/")
@Api(value = "SourceAnalysisController", description = "来源分析统计", produces = "application/json")
public class SourceAnalysisController {

    private static final Logger logger = LoggerFactory.getLogger(SourceAnalysisController.class);

    @Autowired
    private SourceAnalysisService service;

    @GetMapping("/source/analysis/count")
    @JwtPermissions
    public AbcServiceResponseBody<List<SourceAnalysisSumDto>> selectSourceAnalysisCount(SourceAnalysisParam param) throws ExecutionException, InterruptedException {
        logger.info("/source/analysis/count param:{}", JSON.toJSONString(param));
        param.init();
        return new AbcServiceResponseBody(service.selectSourceAnalysisCount(param));
    }

    @GetMapping("/source/analysis/item")
    @JwtPermissions
    public AbcServiceResponseBody<V2StatResponse> selectSourceAnalysisItem(SourceAnalysisParam param) throws ExecutionException, InterruptedException {
        logger.info("/source/analysis/item param:{}", JSON.toJSONString(param));
        param.init();
        return new AbcServiceResponseBody(service.selectSourceAnalysisItem(param));
    }
}
