package cn.abc.flink.stat.controller;

import cn.abc.flink.stat.websocket.service.WebSocketNotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/websocket")
@Api(value = "WebSocketController", description = "WebSocket管理接口", produces = "application/json")
public class WebSocketController {
	@Resource
	private SimpMessagingTemplate messagingTemplate;

	@GetMapping("/open-error-msg")
	public void openErrorMsg(@RequestParam(required = true) Boolean isOpen) {
		// 不需要处理前端发送过来的消息
		Map<String, Object> map = new HashMap<>();
		if (isOpen) {
			map.put("message", "目前统计数据存在延迟，工程师正在努力修复，请稍后再试。");
			map.put("open", true);
		} else {
			map.put("message", null);
			map.put("open", false);
		}

		messagingTemplate.convertAndSend("/topic/open-error-msg", map);
	}

	/**
	 * 处理客户端发送的消息
	 */
	@MessageMapping("/send")
	@SendTo("/topic/messages")
	public Map<String, Object> handleMessage(@Payload Map<String, Object> message, SimpMessageHeaderAccessor headerAccessor) {
		String sessionId = headerAccessor.getSessionId();
		log.info("收到客户端消息: sessionId={}, message={}", sessionId, message);

		Map<String, Object> response = new HashMap<>();
		response.put("type", "response");
		response.put("content", "消息已收到: " + message.get("content"));
		response.put("sessionId", sessionId);
		response.put("timestamp", System.currentTimeMillis());

		return response;
	}

	/**
	 * 处理用户注册消息
	 */
	@MessageMapping("/register")
	public void handleUserRegister(@Payload Map<String, String> userInfo, SimpMessageHeaderAccessor headerAccessor) {
		String sessionId = headerAccessor.getSessionId();
		String userId = userInfo.get("userId");
		String chainId = userInfo.get("chainId");
		String clinicId = userInfo.get("clinicId");

		log.info("用户注册: sessionId={}, userId={}, chainId={}", sessionId, userId, chainId);

		notificationService.registerUserSession(sessionId, userId, chainId, clinicId);

		// 发送注册成功消息
		notificationService.sendSystemNotification("用户注册成功: " + userId);
	}

	/**
	 * 获取在线用户统计信息
	 */
	@GetMapping("/stats")
	@ResponseBody
	@ApiOperation(value = "获取WebSocket连接统计信息")
	public Map<String, Object> getStats(@RequestParam(required = false) String chainId) {
		Map<String, Object> stats = new HashMap<>();
		stats.put("totalOnlineUsers", notificationService.getOnlineUserCount());

		if (chainId != null) {
			stats.put("chainOnlineUsers", notificationService.getOnlineUserCountByChain(chainId));
			stats.put("chainId", chainId);
		}

		stats.put("timestamp", System.currentTimeMillis());

		return stats;
	}

	/**
	 * 发送测试通知
	 */
	@GetMapping("/test/notification")
	@ResponseBody
	@ApiOperation(value = "发送测试通知")
	public Map<String, Object> sendTestNotification(@RequestParam(defaultValue = "这是一条测试通知") String message) {
		notificationService.sendSystemNotification(message);

		Map<String, Object> response = new HashMap<>();
		response.put("success", true);
		response.put("message", "测试通知已发送: " + message);
		response.put("timestamp", System.currentTimeMillis());

		return response;
	}

	/**
	 * 发送数据更新通知
	 */
	@GetMapping("/test/data-update")
	@ResponseBody
	@ApiOperation(value = "发送数据更新测试通知")
	public Map<String, Object> sendDataUpdateNotification(
			@RequestParam(defaultValue = "test-data") String dataType,
			@RequestParam(defaultValue = "数据已更新") String content) {

		notificationService.sendDataUpdateNotification(dataType, content);

		Map<String, Object> response = new HashMap<>();
		response.put("success", true);
		response.put("message", "数据更新通知已发送");
		response.put("dataType", dataType);
		response.put("content", content);
		response.put("timestamp", System.currentTimeMillis());

		return response;
	}
}
