package cn.abc.flink.stat.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/11 17:57
 */
@RestController
@RequestMapping("/rpc/sc/stat/ws")
@Api(value = "WebSocketController", description = "WebSocket管理接口", produces = "application/json")
public class WebSocketController {

	@GetMapping("/open-error-msg")
	public void openErrorMsg(@RequestParam(required = true) Boolean isOpen) {
		// 不需要处理前端发送过来的消息
		Map<String, Object> map = new HashMap<>();
		if (isOpen) {
			map.put("message", "目前统计数据存在延迟，工程师正在努力修复，请稍后再试。");
			map.put("open", true);
		} else {
			map.put("message", null);
			map.put("open", false);
		}
	}
}
