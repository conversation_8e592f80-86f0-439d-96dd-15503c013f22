package cn.abc.flink.stat.controller.api;

import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.annotation.JwtPermissions;
import cn.abc.flink.stat.service.cis.b2c.order.B2cOrderService;
import cn.abc.flink.stat.service.cis.b2c.order.domain.B2cOrderParams;
import cn.abc.flink.stat.service.cis.micro.mall.MicroMallService;
import cn.abc.flink.stat.service.cis.micro.mall.domain.MicroMallParams;
import cn.abcyun.cis.commons.CisServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/v2/sc/stat/micro/mall", name = "微商城统计")
@Api(value = "MicroMallController", description = "微商城统计前端接口", produces = "application/json")
public class MicroMallController {

    private static final Logger logger = LoggerFactory.getLogger(MicroMallController.class);

    @Autowired
    MicroMallService microMallService;

    @JwtPermissions
    @GetMapping(value = "/data/overview", name = "数据概况")
    @ApiOperation(value = "微商城统计-数据概况", produces = "application/json")
    public CisServiceResponse<Object> dataOverview(MicroMallParams params) throws Exception {
        params.initParams();
        return new CisServiceResponse(microMallService.dataOverview(params));
    }

    @JwtPermissions
    @GetMapping(value = "/today/summary", name = "汇总")
    @ApiOperation(value = "微商城统计-今日汇总", produces = "application/json")
    public CisServiceResponse<Object> todaySummary(MicroMallParams params) throws Exception {
        params.initParams();
        return new CisServiceResponse(microMallService.todaySummary(params));
    }
}
