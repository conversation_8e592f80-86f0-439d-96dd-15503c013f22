package cn.abc.flink.stat.service.cis.achievement.dispensing.entity;



/**
 * <AUTHOR>
 * @date 2022/4/11 9:30 上午
 */
public class AchievementDispensingGadgetSummaryDao {
    private String chainId;
    private String clinicId;
    private int dispensingNum;    //发药总单数
    private int westernPrescriptionNum;  //成药处方数
    private int chinesePrescriptionNum;  //中药处方数
    private int retailNum;      //零售单数
    private int processNum;      //加工单数


    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public int getDispensingNum() {
        return this.dispensingNum;
    }

    public int getWesternPrescriptionNum() {
        return this.westernPrescriptionNum;
    }

    public int getChinesePrescriptionNum() {
        return this.chinesePrescriptionNum;
    }

    public int getRetailNum() {
        return this.retailNum;
    }

    public int getProcessNum() {
        return this.processNum;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setDispensingNum(int dispensingNum) {
        this.dispensingNum = dispensingNum;
    }

    public void setWesternPrescriptionNum(int westernPrescriptionNum) {
        this.westernPrescriptionNum = westernPrescriptionNum;
    }

    public void setChinesePrescriptionNum(int chinesePrescriptionNum) {
        this.chinesePrescriptionNum = chinesePrescriptionNum;
    }

    public void setRetailNum(int retailNum) {
        this.retailNum = retailNum;
    }

    public void setProcessNum(int processNum) {
        this.processNum = processNum;
    }

}
