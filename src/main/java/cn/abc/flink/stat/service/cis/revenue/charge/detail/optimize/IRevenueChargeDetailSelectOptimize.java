package cn.abc.flink.stat.service.cis.revenue.charge.detail.optimize;

import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.stereotype.Service;

@Service
public interface IRevenueChargeDetailSelectOptimize {

    /**
     * @Description: 优化查询
     * @param
     * @param joinPoint: 方法实例
     * @param params: 每个实现类都有不同的参数
     * @return
     * @Author: zs
     * @Date: 2025/4/22 10:16
     */
    Object optimize(ProceedingJoinPoint joinPoint, Object params) throws Throwable;
}
