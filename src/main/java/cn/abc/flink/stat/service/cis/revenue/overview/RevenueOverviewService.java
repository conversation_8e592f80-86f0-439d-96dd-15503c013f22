package cn.abc.flink.stat.service.cis.revenue.overview;

import cn.abc.flink.stat.common.ABCNumberUtils;
import cn.abc.flink.stat.common.AbcDefaultValueUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.model.ChargeSource;
import cn.abc.flink.stat.common.model.PayMode;
import cn.abc.flink.stat.common.response.StatResponse;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnRevenueOverViewMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.RevenueSummaryMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresRevenueOverviewMapper;
import cn.abc.flink.stat.db.dao.OdsChargeMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.ChargeSourceType;
import cn.abc.flink.stat.dimension.domain.V2ChargePayModeConfig;
import cn.abc.flink.stat.dimension.domain.V2GoodsCustomType;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.items.DimensionChargeSourceType;
import cn.abc.flink.stat.service.cis.achievement.commission.AchievementCommissionService;
import cn.abc.flink.stat.service.cis.config.StatConfigService;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.outpatient.OutpatientService;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientFirstRevisitRsp;
import cn.abc.flink.stat.service.cis.registration.domain.RegistrationReqParams;
import cn.abc.flink.stat.service.cis.registration.domain.RegistrationSummary;
import cn.abc.flink.stat.service.cis.registration.handler.RegistrationDataHandler;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.JobSummaryIncomeDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.JobSummaryIncomeHospitalDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.RechargeDAO;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.RepaymentOverviewDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.RevenueOverviewAmountCountTrendDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.RevenueOverviewAmountDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.RevenueOverviewAmountTrendDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.RevenueOverviewFeeClassifyDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.RevenueOverviewPayModeDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.WeClinicOverviewDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.WorkBenchCashierViewAmountDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.WorkBenchCashierViewClassifyAmountDao;
import cn.abc.flink.stat.service.cis.revenue.overview.entity.WorkbenchDoctorViewFeeAmountDao;
import cn.abc.flink.stat.service.cis.revenue.overview.handler.GadgetHandler;
import cn.abc.flink.stat.service.cis.revenue.overview.handler.HeaderHandler;
import cn.abc.flink.stat.service.cis.revenue.overview.handler.WorkBenchHandler;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.JobSummaryInfoResp;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueGadgetCheckAccountResp;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueGadgetOverviewResp;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueGadgetReqParams;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueOperationSelectResp;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueOverviewAmountDto;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueOverviewDepartmentResp;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueOverviewHospitalAmountDto;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueOverviewSummaryReqParams;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueOverviewTrendReqParams;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueSelectionReqParams;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.WeClinicDailyDto;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.WorkBenchCashierViewFeeAmountDto;
import cn.abc.flink.stat.service.common.CisHeaderService;
import cn.abc.flink.stat.source.AbcThreadExecutor;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import cn.abcyun.cis.core.util.ExecutorUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class RevenueOverviewService {
    public static final Logger logger = LoggerFactory.getLogger(RevenueOverviewService.class);

    @Autowired
    private ArnRevenueOverViewMapper mysqlMapper;

    @Resource
    private HologresRevenueOverviewMapper hologresRevenueOverviewMapper;

    @Resource
    private RevenueSummaryMapper revenueSummaryMapper;

    @Autowired
    private DimensionQuery query;

    @Autowired
    private StoreUtils storeUtils;

    @Autowired
    private ExecutorService cacheExecutorService;

    @Autowired
    private CisHeaderService cisHeaderService;

    @Autowired
    private OutpatientService kuduOutpatientService;

    @Autowired
    private RegistrationDataHandler registrationDataHandler;

    @Autowired
    private OdsChargeMapper odsChargeMapper;

    @Autowired
    private AchievementCommissionService commissionService;

    @Resource
    private StatConfigService statConfigService;

    @Resource
    private AbcThreadExecutor abcyunExecutorPool;

    /**
     * selectSourceTypeSelection
     *
     * @param reqParams 参数
     * @return list
     */
    public List<ChargeSourceType> selectSourceTypeSelection(RevenueSelectionReqParams reqParams) {
        List<ChargeSourceType> cooperativeClinics = new ArrayList<>();
        if (CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(reqParams.getHisType())) {
            //查询合作诊所数据
            List<ChargeSourceType> list = odsChargeMapper.selectCooperativeClinic(TableUtils.getCisBasicTable(), reqParams.getChainId(), reqParams.getClinicId());
            if (list != null && list.size() > 0) {
                cooperativeClinics.addAll(list);
            }
        }
        return DimensionChargeSourceType.map.values()
                .stream()
                .map(x -> {
                    if (reqParams.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_DENTISTRY)) {
                        if (x.getId() == ChargeSource.AUTO_CONTINUE_PRESCRIPTION
                                || x.getId() == ChargeSource.NETWORK_DIAG
                                || x.getId() == ChargeSource.FAMILY_SIGN) {
                            return null;
                        }
                    }
                    if (!reqParams.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_DENTISTRY)) {
                        if (x.getId() == ChargeSource.CONSULTING_FEES) {
                            return null;
                        }
                    }
                    if (x.getId() == ChargeSource.AIR_PHARMACY_ORDER && !(
                            cn.abc.flink.stat.service.cis.revenue.charge
                                    .detail.handler.HeaderHandler.AIR_CHAIN.contains(reqParams.getChainId()))) {
                        return null;
                    }
                    if (!reqParams.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY) && x.getId() == ChargeSource.COOPERATION_ORDER) {
                        return null;
                    }
                    if (reqParams.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                        if (x.getId() == ChargeSource.COOPERATION_ORDER && cooperativeClinics.size() > 0) {
                            x.setChildren(cooperativeClinics);
                            return x;
                        }
                        if (!(x.getId() == ChargeSource.RETAIL || x.getId() == ChargeSource.MEMBER_RECHARGE)) {
                            return null;
                        }
                        if (reqParams.getIsRetail() != null && reqParams.getIsRetail() == 1 && x.getId() == ChargeSource.MEMBER_RECHARGE) {
                            return null;
                        }
                    }
                    return x;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(ChargeSourceType::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 操作类型
     *
     * @param reqParams 参数 暂时没有使用
     * @return RevenueOperationSelectResp
     */
    public RevenueOperationSelectResp selectOperationSelection(RevenueSelectionReqParams reqParams) {
        RevenueOperationSelectResp result = new RevenueOperationSelectResp();
        result.add(reqParams);
        return result;
    }

    public RevenueOverviewAmountDto selectSummaryStrategy(RevenueOverviewSummaryReqParams params) throws ExecutionException, InterruptedException, ParseException, CloneNotSupportedException {
        boolean newServiceProxyFlag = false;
        if (params.getEmployeeType() == null && params.getEmployeeId() == null && params.getIncludeReg() == null) {
            newServiceProxyFlag = true;
        }
        if (newServiceProxyFlag) {
            logger.info("proxy -> selectSummaryV2");
            return selectSummaryV2(params);
        }
        logger.info("proxy -> selectSummary");
        return selectSummary(params.getHisType(), params);
    }

    public RevenueOverviewAmountDto selectSummaryV2(RevenueOverviewSummaryReqParams params) throws ParseException, CloneNotSupportedException, ExecutionException, InterruptedException {
        String todayDate = TimeUtils.getTodayDate();
        StatConfigDto statConfig = null;
        if (params.getDispensaryType() == null) {
            statConfig = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getHeaderClinicId()));
        } else {
            statConfig = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getHeaderClinicId(), params.getDispensaryType()));
        }
        if (statConfig != null) {
            params.setIsComposeShareEqually(statConfig.getIsComposeShareEqually());
        }
        params.initParams(statConfig);

        int daysInt = TimeUtils.differentDaysByMillisecond(params.getBeginDate(), params.getEndDate()) + 1;
        CompletableFuture<Map<Integer, String>> payF = abcyunExecutorPool.supplyAsync(() -> query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture<RepaymentOverviewDao> repaymentF = abcyunExecutorPool.supplyAsync(() -> odsChargeMapper.getRepaymentOverview(TableUtils.getCisChargeTable(), params));
        CompletableFuture<RechargeDAO> promotionCardRechargeF = abcyunExecutorPool.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPromotionCardRechargeAmount(TableUtils.getCisTable(), params);
        });
        CompletableFuture<RechargeDAO> memberRechargeF = abcyunExecutorPool.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectMemberRechargeAmount(TableUtils.getCisTable(), params);
        });

        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = null;
        if (params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            feeTypeF = abcyunExecutorPool.supplyAsync(() -> query.selectAdviceFeeType(params.getChainId()));
        }
        CompletableFuture<List<WorkBenchCashierViewClassifyAmountDao>> treatFeeF = null;
        CompletableFuture<Map<Integer, V2GoodsCustomType>> subFeeConfigF = null;
        if ("1".equals(params.getHisType())) {
            treatFeeF = abcyunExecutorPool.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectTreatFeeClassifyGroupByEmployee(TableUtils.getCisTable(), params);
            });
            subFeeConfigF = abcyunExecutorPool.supplyAsync(() -> {
                return query.queryProductCustomTypeTextByChainId(params.getChainId());
            });
        }
        CompletableFuture<List<RevenueOverviewPayModeDao>> repayModeF = abcyunExecutorPool.supplyAsync(() -> odsChargeMapper.getRepaymentOverviewGroupByPayType(TableUtils.getCisChargeTable(), params));
        RevenueOverviewAmountDao todayAmount = null;
        RevenueOverviewAmountDao noTodayAmount = null;
        // count比较特殊，只要开始、结束时间不是今天，那么count都需要有值，并且count数据走的是这个
        RevenueOverviewAmountDao noTodayAmountCount = null;
        List<RevenueOverviewFeeClassifyDao> todayFeeClassify = null;
        List<RevenueOverviewFeeClassifyDao> noTodayFeeClassify = null;
        List<RevenueOverviewPayModeDao> todayPayMode = null;
        List<RevenueOverviewPayModeDao> noTodayPayMode = null;
        CompletableFuture<List<RevenueOverviewFeeClassifyDao>> feeClassifyF = null;
        // 查询当天的数据
        if (params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            feeClassifyF = abcyunExecutorPool.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeType(TableUtils.getCisTable(), params);
            });
        }
        if (TimeUtils.isToday(params.getEndDate())) {
            params.setTodayBegin(TimeUtils.appendBegin(todayDate));
            params.setTodayEnd(params.getEndDate());
            CompletableFuture<RevenueOverviewAmountDao> amountF = abcyunExecutorPool.supplyAsync(() -> mysqlMapper.selectAmount(TableUtils.getCisTable(), params));
            CompletableFuture<List<RevenueOverviewPayModeDao>> payModeF = abcyunExecutorPool.supplyAsync(() -> mysqlMapper.selectPayMode(TableUtils.getCisTable(), params));

            if (!params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                feeClassifyF = abcyunExecutorPool.supplyAsync(() -> mysqlMapper.selectFeeClassify(TableUtils.getCisTable(), params));
            }

            if (!TimeUtils.isToday(params.getBeginDate())) {
                params.setNoTodayBegin(TimeUtils.formatToInteger(params.getBeginDate(), "yyyyMMdd"));
                params.setNoTodayEnd(TimeUtils.formatToInteger(TimeUtils.dateSubDay(params.getEndDate(), -1) + " 23:59:59", "yyyyMMdd"));
                CompletableFuture<RevenueOverviewAmountDao> noTodayAmountF = abcyunExecutorPool.supplyAsync(() -> revenueSummaryMapper.selectAmountForClinic(TableUtils.getCisTable(), params));
                CompletableFuture<List<RevenueOverviewPayModeDao>> noTodayPayModeF = abcyunExecutorPool.supplyAsync(() -> revenueSummaryMapper.selectPayAmountForClinic(TableUtils.getCisTable(), params));
                CompletableFuture<RevenueOverviewAmountDao> noTodayAmountCountF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectAmountCount(TableUtils.getCisTable(), params));
                if (!params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                    CompletableFuture<List<RevenueOverviewFeeClassifyDao>> noTodayFeeClassifyF = abcyunExecutorPool.supplyAsync(() -> revenueSummaryMapper.selectClassifyAmountForClinic(TableUtils.getCisTable(), params));
                    noTodayFeeClassifyF.join();
                    noTodayFeeClassify = noTodayFeeClassifyF.get();
                }
                CompletableFuture.allOf(noTodayAmountF, noTodayPayModeF, noTodayAmountCountF).join();
                noTodayAmount = noTodayAmountF.get();
                noTodayPayMode = noTodayPayModeF.get();
                noTodayAmountCount = noTodayAmountCountF.get();
            }
            CompletableFuture.allOf(payF, repaymentF, promotionCardRechargeF, memberRechargeF, repayModeF, amountF, payModeF, feeClassifyF).join();
            todayAmount = amountF.get();
            todayPayMode = payModeF.get();
            todayFeeClassify = feeClassifyF.get();
        } else {
            // 开始和结束时间都不是今天
            params.setNoTodayBegin(TimeUtils.formatToInteger(params.getBeginDate(), "yyyyMMdd"));
            params.setNoTodayEnd(TimeUtils.formatToInteger(params.getEndDate(), "yyyyMMdd"));
            CompletableFuture<RevenueOverviewAmountDao> noTodayAmountF = abcyunExecutorPool.supplyAsync(() -> revenueSummaryMapper.selectAmountForClinic(TableUtils.getCisTable(), params));
            CompletableFuture<List<RevenueOverviewPayModeDao>> noTodayPayModeF = abcyunExecutorPool.supplyAsync(() -> revenueSummaryMapper.selectPayAmountForClinic(TableUtils.getCisTable(), params));
            CompletableFuture<RevenueOverviewAmountDao> noTodayAmountCountF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectAmountCount(TableUtils.getCisTable(), params));
            if (!params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                CompletableFuture<List<RevenueOverviewFeeClassifyDao>> noTodayFeeClassifyF = abcyunExecutorPool.supplyAsync(() -> revenueSummaryMapper.selectClassifyAmountForClinic(TableUtils.getCisTable(), params));
                noTodayFeeClassifyF.join();
                noTodayFeeClassify = noTodayFeeClassifyF.get();
            }
            CompletableFuture.allOf(noTodayAmountF, noTodayPayModeF, noTodayAmountCountF).join();
            noTodayAmount = noTodayAmountF.get();
            noTodayPayMode = noTodayPayModeF.get();
            noTodayAmountCount = noTodayAmountCountF.get();
        }

        RevenueOverviewAmountDto result = new RevenueOverviewAmountDto();
        result.dealSummaryData(todayAmount, noTodayAmount, noTodayAmountCount, repaymentF.get(), daysInt);
        result.initPromotion(promotionCardRechargeF.get(), memberRechargeF.get());
        result.dealPays(todayPayMode, noTodayPayMode, repayModeF.get(), payF.get());
        result.dealFeeClassify(todayFeeClassify, noTodayFeeClassify, query, params.getChainId(), params.getHisType(), feeTypeF);
        if ("1".equals(params.getHisType())) {
            List<RevenueOverviewFeeClassifyDao> treatmentFeeClassifyList = convertTreatmentFeeClassifyList(treatFeeF.get(), subFeeConfigF.get());
            if (treatmentFeeClassifyList != null) {
                result.getFeeTypes().addAll(treatmentFeeClassifyList);
            }
        }
        return result;
    }

    /**
     * 口腔管家 - 通过二级分类获取治疗的汇总值并将其对应的二级分类添加到feeClassifyList中
     * 口腔管家: 未指定二级分类的，统一展示一级分类
     *
     * @param list          treatList
     * @param customTypeMap customTypeMap
     */
    private List<RevenueOverviewFeeClassifyDao> convertTreatmentFeeClassifyList(List<WorkBenchCashierViewClassifyAmountDao> list, Map<Integer, V2GoodsCustomType> customTypeMap) {
        if (list == null) {
            return null;
        }
        List<RevenueOverviewFeeClassifyDao> results = new ArrayList<>();
        RevenueOverviewFeeClassifyDao undefinedFeeClassify = new RevenueOverviewFeeClassifyDao();
        undefinedFeeClassify.setValue(BigDecimal.ZERO);
        for (WorkBenchCashierViewClassifyAmountDao tf : list) {
            if (tf != null) {
                if (tf.getClassifyLevel2() != null && tf.getClassifyLevel2() != 0) {
                    V2GoodsCustomType ct = customTypeMap.get(tf.getClassifyLevel2());
                    if (ct != null) {
                        RevenueOverviewFeeClassifyDao fcd = new RevenueOverviewFeeClassifyDao();
                        fcd.setId(tf.getClassifyLevel1() + "/" + tf.getClassifyLevel2());
                        fcd.setName(ct.getName());
                        fcd.setValue(tf.getAmount().setScale(
                                ABCNumberUtils.DEFAULT_RESERVED_NUMBER_TWO,
                                RoundingMode.HALF_UP));
                        results.add(fcd);
                    }
                } else {
                    undefinedFeeClassify.setValue(undefinedFeeClassify.getValue().add(tf.getAmount()));
                }
            }
        }
        if (!undefinedFeeClassify.getValue().equals(BigDecimal.ZERO)) {
            undefinedFeeClassify.setId("4-1");
            undefinedFeeClassify.setName("治疗");
            results.add(undefinedFeeClassify);
        }
        return results;
    }

    /**
     * 营收概况-汇总
     *
     * @param hisType hisType
     * @param params  params
     * @return RevenueOverviewAmountDto
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public RevenueOverviewAmountDto selectSummary(
            String hisType, RevenueOverviewSummaryReqParams params
    ) throws ExecutionException, InterruptedException {
        StatConfigDto statConfig = null;
        if (params.getDispensaryType() == null) {
            statConfig = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getHeaderClinicId()));
        } else {
            statConfig = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getHeaderClinicId(), params.getDispensaryType()));
        }
        if (statConfig != null) {
            params.setIsComposeShareEqually(statConfig.getIsComposeShareEqually());
        }
        params.initParams(statConfig);
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        int daysInt = TimeUtils.differentDaysByMillisecond(params.getBeginDate(), params.getEndDate()) + 1;
        CompletableFuture<Map<Integer, String>> payF = abcyunExecutorPool.supplyAsync(() -> {
                    return query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
                }
        );
        CompletableFuture<RepaymentOverviewDao> repaymentF = abcyunExecutorPool.supplyAsync(() -> {
            return odsChargeMapper.getRepaymentOverview(TableUtils.getCisChargeTable(), params);
        });
        CompletableFuture<RevenueOverviewAmountDao> amountF = abcyunExecutorPool.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectAmount(TableUtils.getCisTable(), params);
        });
        CompletableFuture<RevenueOverviewAmountDao> amountCountF = abcyunExecutorPool.supplyAsync(() -> {
            if (isKudu) {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectAmountCount(TableUtils.getCisTable(), params);
            }
            return new RevenueOverviewAmountDao(0, 0, 0, 0, 0);
        });
        CompletableFuture<RechargeDAO> promotionCardRechargeF = abcyunExecutorPool.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPromotionCardRechargeAmount(TableUtils.getCisTable(), params);
        });
        CompletableFuture<RechargeDAO> memberRechargeF = abcyunExecutorPool.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectMemberRechargeAmount(TableUtils.getCisTable(), params);
        });
        CompletableFuture<List<RevenueOverviewFeeClassifyDao>> feeClassifyF = null;
        if (hisType.equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            feeClassifyF = abcyunExecutorPool.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeType(TableUtils.getCisTable(), params);
            });
        } else {
            feeClassifyF = abcyunExecutorPool.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeClassify(TableUtils.getCisTable(), params);
            });
        }
        CompletableFuture<List<RevenueOverviewPayModeDao>> payModeF =
                abcyunExecutorPool.supplyAsync(() -> {
                    return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPayMode(TableUtils.getCisTable(), params);
                });
        CompletableFuture<List<RevenueOverviewPayModeDao>> repayModeF =
                abcyunExecutorPool.supplyAsync(() -> {
                    return odsChargeMapper.getRepaymentOverviewGroupByPayType(TableUtils.getCisChargeTable(), params);
                });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = null;
        if (hisType.equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            feeTypeF = abcyunExecutorPool.supplyAsync(() -> {
                return query.selectAdviceFeeType(params.getChainId());
            });
        }
        CompletableFuture<List<WorkBenchCashierViewClassifyAmountDao>> treatFeeF = null;
        CompletableFuture<Map<Integer, V2GoodsCustomType>> subFeeConfigF = null;
        if ("1".equals(hisType)) {
            treatFeeF = abcyunExecutorPool.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectTreatFeeClassifyGroupByEmployee(TableUtils.getCisTable(), params);
            });
            subFeeConfigF = abcyunExecutorPool.supplyAsync(() -> {
                return query.queryProductCustomTypeTextByChainId(params.getChainId());
            });
        }
        feeClassifyF.join();
        List<RevenueOverviewFeeClassifyDao> feeClassifyList = new ArrayList<>();
        if (hisType.equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            feeTypeF.join();
            feeClassifyList = getFeeType(feeClassifyF.get(), feeTypeF.get(), false);
        } else {
            for (RevenueOverviewFeeClassifyDao fee : feeClassifyF.get()) {
                if (fee != null && !fee.getValue().equals(BigDecimal.ZERO)) {
                    if (!"1".equals(hisType) || (!"4-2".equals(fee.getId()) && !"4-1".equals(fee.getId()))) {
                        fee.setName(query.queryProductClassifyLevel1(params.getChainId(), fee.getId(), params.getHisType()));
                        feeClassifyList.add(fee);
                    }
                }
            }
        }
        if ("1".equals(hisType)) {
            CompletableFuture.allOf(treatFeeF, subFeeConfigF).join();
            Map<Integer, V2GoodsCustomType> customTypeMap = subFeeConfigF.get();
            List<RevenueOverviewFeeClassifyDao> treatmentFeeClassifyList = convertTreatmentFeeClassifyList(treatFeeF.get(), customTypeMap);
            if (treatmentFeeClassifyList != null) {
                feeClassifyList.addAll(treatmentFeeClassifyList);
            }
        }
        CompletableFuture.allOf(payModeF, repayModeF, payF, amountF, amountCountF, repaymentF, memberRechargeF, promotionCardRechargeF).join();
        Map<Integer, String> payConfigMap = payF.get();
        Map<String, RevenueOverviewPayModeDao> payModeMap = payModeF.get().stream()
                .filter(x -> x != null && x.getField() != null)
                .map(payMode -> {
                    payMode.setNameFromMap(payConfigMap);
                    return payMode;
                }).collect(Collectors.toMap(RevenueOverviewPayModeDao::getField,
                        RevenueOverviewPayModeDao -> RevenueOverviewPayModeDao));
        List<RevenueOverviewPayModeDao> repaymentPayModeList = repayModeF.get();
        repaymentPayModeList.stream().forEach(pm -> {
            if (pm != null) {
                pm.setNameFromMap(payConfigMap);
                pm.addToMap(payModeMap);
            }
        });
        RevenueOverviewAmountDto result = assembleOverviewSummaryInfo(amountF.get(), amountCountF.get(), daysInt, isKudu);
        assembleOverviewSummaryOther(statConfig, result, new ArrayList<>(payModeMap.values()), feeClassifyList,
                repaymentF.get(), daysInt);
        result.initPromotion(promotionCardRechargeF.get(), memberRechargeF.get());
        return result;
    }

    /**
     * 营收概况-汇总
     *
     * @param params params
     * @return RevenueOverviewAmountDto
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public RevenueOverviewHospitalAmountDto selectHopitalSummary(RevenueOverviewSummaryReqParams params) throws ExecutionException, InterruptedException {
        StatConfigDto statConfig = null;
        if (params.getDispensaryType() == null) {
            statConfig = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId()));
        } else {
            statConfig = statConfigService.selectConfig(new StatConfigParam(params.getChainId(),
                    params.getClinicId(), params.getDispensaryType()));
        }
        if (statConfig != null) {
            params.setIsComposeShareEqually(statConfig.getIsComposeShareEqually());
        }
        params.initParams(statConfig);
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        int daysInt = TimeUtils.differentDaysByMillisecond(params.getBeginDate(), params.getEndDate()) + 1;
        CompletableFuture<Map<Integer, String>> payF = abcyunExecutorPool.supplyAsync(() -> {
                    return query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
                }
        );
        CompletableFuture<RepaymentOverviewDao> repaymentF = abcyunExecutorPool.supplyAsync(() -> odsChargeMapper.getRepaymentOverview(TableUtils.getCisChargeTable(), params));
        CompletableFuture<RevenueOverviewAmountDao> amountF = abcyunExecutorPool.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectAmount(TableUtils.getCisTable(), params));
        CompletableFuture<RevenueOverviewAmountDao> amountCountF = abcyunExecutorPool.supplyAsync(() -> {
            if (isKudu) {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectAmountCount(TableUtils.getCisTable(), params);
            }
            return new RevenueOverviewAmountDao(0, 0, 0, 0, 0);
        });
        CompletableFuture<List<RevenueOverviewFeeClassifyDao>> feeClassifyF = abcyunExecutorPool.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeType(TableUtils.getCisTable(), params));
        CompletableFuture<List<RevenueOverviewFeeClassifyDao>> hospitalFeeClassifyF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectHospitalFeeType(TableUtils.getHisTable(), params));
        CompletableFuture<List<RevenueOverviewFeeClassifyDao>> peChargeFeeClassifyF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectPeChargeFeeType(TableUtils.getHisTable(), params));
        CompletableFuture<List<RevenueOverviewPayModeDao>> payModeF = abcyunExecutorPool.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPayMode(TableUtils.getCisTable(), params));
        CompletableFuture<List<RevenueOverviewPayModeDao>> hospitalPayModeF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectHospitalPayMode(TableUtils.getHisTable(), params));
        CompletableFuture<List<RevenueOverviewPayModeDao>> peChargePayModeF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectPeChargePayMode(TableUtils.getHisTable(), params));
        CompletableFuture<List<RevenueOverviewPayModeDao>> repayModeF = abcyunExecutorPool.supplyAsync(() -> odsChargeMapper.getRepaymentOverviewGroupByPayType(TableUtils.getCisChargeTable(), params));
        CompletableFuture<RechargeDAO> promotionCardRechargeF = abcyunExecutorPool.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPromotionCardRechargeAmount(TableUtils.getCisTable(), params));
        CompletableFuture<RechargeDAO> memberRechargeF = abcyunExecutorPool.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectMemberRechargeAmount(TableUtils.getCisTable(), params));
        CompletableFuture<List<RevenueOverviewDepartmentResp>> departmentF = abcyunExecutorPool.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectDepartmentAmount(TableUtils.getCisTable(), params));
        CompletableFuture<List<RevenueOverviewDepartmentResp>> hospitalDepartmentF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectHospitalDepartment(TableUtils.getHisTable(), params));
        CompletableFuture<List<RevenueOverviewDepartmentResp>> peChargeDepartmentF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectPeChargeDepartment(TableUtils.getHisTable(), params));
        CompletableFuture<Map<String, String>> departmentConfigF = abcyunExecutorPool.supplyAsync(() -> query.queryDepartmentNameByOrgan(params.getChainId(), params.getClinicId()));

        CompletableFuture<BigDecimal> depositF = abcyunExecutorPool.supplyAsync(() -> hologresRevenueOverviewMapper.selectDepositAmount(TableUtils.getHisTable(), params));

        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = abcyunExecutorPool.supplyAsync(() -> query.selectAdviceFeeType(params.getChainId()));
        CompletableFuture.allOf(feeClassifyF, hospitalFeeClassifyF, peChargeFeeClassifyF, feeTypeF).join();

        RevenueOverviewHospitalAmountDto result = new RevenueOverviewHospitalAmountDto();
        List<RevenueOverviewFeeClassifyDao> feeClassifyList = mergeFeeTypeForAllLine(feeClassifyF.get(), hospitalFeeClassifyF.get(), peChargeFeeClassifyF.get(), result);
        feeClassifyList = getFeeType(feeClassifyList, feeTypeF.get(), false);

        CompletableFuture.allOf(payModeF, repayModeF, hospitalPayModeF, peChargePayModeF, payF, amountF, amountCountF, repaymentF, departmentF, hospitalDepartmentF, peChargeDepartmentF, departmentConfigF, promotionCardRechargeF, memberRechargeF).join();
        Map<Integer, String> payConfigMap = payF.get();
        Map<String, String> departmentConfigMap = departmentConfigF.get();
        List<RevenueOverviewPayModeDao> payModeList = mergePayModeForAllLine(payModeF.get(), repayModeF.get(), hospitalPayModeF.get(), peChargePayModeF.get(), payConfigMap);
        List<RevenueOverviewDepartmentResp> departmentList = mergeDepartmentForAllLine(departmentF.get(), hospitalDepartmentF.get(), peChargeDepartmentF.get(), departmentConfigMap);
        result.copyProperties(amountF.get(), amountCountF.get(), daysInt, isKudu);
        assembleHospitalOverviewSummaryOther(result, payModeList, feeClassifyList, departmentList, repaymentF.get(), daysInt);
        result.initPreDepositAmount(depositF.get());
        // 增加体检和住院金额
        result.setTotalAmount(result.getTotalAmount().add(result.getPeChargeAmount()).add(result.getHospitalAmount()));
        result.initPromotion(promotionCardRechargeF.get(), memberRechargeF.get());
        return result;
    }

    private List<RevenueOverviewFeeClassifyDao> mergeFeeTypeForAllLine(List<RevenueOverviewFeeClassifyDao> feeClassifys, List<RevenueOverviewFeeClassifyDao> hospitalFeeClassify, List<RevenueOverviewFeeClassifyDao> peChargeFeeClassifys, RevenueOverviewHospitalAmountDto result) {
        Map<String, RevenueOverviewFeeClassifyDao> map = new HashMap<>();
        for (RevenueOverviewFeeClassifyDao fc : feeClassifys) {
            RevenueOverviewFeeClassifyDao mFee = map.get(fc.getId());
            if (mFee != null) {
                fc.setValue(fc.getValue().add(mFee.getValue()));
            }
            map.put(fc.getId(), fc);
        }
        for (RevenueOverviewFeeClassifyDao fc : hospitalFeeClassify) {
            result.setHospitalAmount(result.getHospitalAmount().add(fc.getValue()));
            RevenueOverviewFeeClassifyDao mFee = map.get(fc.getId());
            if (mFee != null) {
                fc.setValue(fc.getValue().add(mFee.getValue()));
            }
            map.put(fc.getId(), fc);
        }
        for (RevenueOverviewFeeClassifyDao fc : peChargeFeeClassifys) {
            result.setPeChargeAmount(result.getPeChargeAmount().add(fc.getValue()));
            RevenueOverviewFeeClassifyDao mFee = map.get(fc.getId());
            if (mFee != null) {
                fc.setValue(fc.getValue().add(mFee.getValue()));
            }
            map.put(fc.getId(), fc);
        }
        return new ArrayList<>(map.values());
    }

    private List<RevenueOverviewPayModeDao> mergePayModeForAllLine(List<RevenueOverviewPayModeDao> payModes, List<RevenueOverviewPayModeDao> repaymentPayMode, List<RevenueOverviewPayModeDao> hospitalPayModes, List<RevenueOverviewPayModeDao> peChargePayModes, Map<Integer, String> payConfigMap) {
        Map<String, RevenueOverviewPayModeDao> map = new HashMap<>();
        for (RevenueOverviewPayModeDao pm : payModes) {
            pm.setNameFromMap(payConfigMap);
            pm.addToMap(map);
        }
        for (RevenueOverviewPayModeDao pm : repaymentPayMode) {
            pm.setNameFromMap(payConfigMap);
            pm.addToMap(map);
        }
        for (RevenueOverviewPayModeDao pm : hospitalPayModes) {
            pm.setNameFromMap(payConfigMap);
            pm.addToMap(map);
        }
        for (RevenueOverviewPayModeDao pm : peChargePayModes) {
            pm.setNameFromMap(payConfigMap);
            pm.addToMap(map);
        }
        return new ArrayList<>(map.values());
    }

    private List<RevenueOverviewDepartmentResp> mergeDepartmentForAllLine(List<RevenueOverviewDepartmentResp> departments, List<RevenueOverviewDepartmentResp> hospitalDepartments, List<RevenueOverviewDepartmentResp> peChargeDepartments, Map<String, String> departmentMap) {
        Map<String, RevenueOverviewDepartmentResp> map = new HashMap<>();
        for (RevenueOverviewDepartmentResp d : departments) {
            d.setNameFromMap(departmentMap);
            d.addValueIntoMap(map);
        }
        for (RevenueOverviewDepartmentResp d : hospitalDepartments) {
            d.setNameFromMap(departmentMap);
            d.addValueIntoMap(map);
        }
        for (RevenueOverviewDepartmentResp d : peChargeDepartments) {
            d.setNameFromMap(departmentMap);
            d.addValueIntoMap(map);
        }
        return new ArrayList<>(map.values());

    }

    /**
     * 拼装收费概况-汇总数据
     *
     * @param road    road
     * @param daysInt daysInt
     * @param count   count
     * @param isKudu  isKudu
     * @return RevenueOverviewAmountDto
     */
    private RevenueOverviewAmountDto assembleOverviewSummaryInfo(
            RevenueOverviewAmountDao road,
            RevenueOverviewAmountDao count,
            int daysInt,
            boolean isKudu
    ) {
        RevenueOverviewAmountDto dto = new RevenueOverviewAmountDto();
        dto.copyProperties(road, count, daysInt, isKudu);
        return dto;
    }

    /**
     * 拼装收费概况-除汇总数据其余数据
     *
     * @param dto               dto
     * @param payModeList       payModeList
     * @param feeClassifyList   feeClassifyList
     * @param repaymentOverview repaymentOverview
     * @param daysInt           daysInt
     */
    private void assembleOverviewSummaryOther(
            StatConfigDto statConfig,
            RevenueOverviewAmountDto dto,
            List<RevenueOverviewPayModeDao> payModeList,
            List<RevenueOverviewFeeClassifyDao> feeClassifyList,
            RepaymentOverviewDao repaymentOverview,
            int daysInt) {
        BigDecimal days = BigDecimal.valueOf(daysInt);
        // 还款处理
        if (repaymentOverview != null) {
            if (repaymentOverview.getAmount() != null) {
                dto.setRepaymentAmount(repaymentOverview.getAmount());
                dto.setAvgRepaymentAmount(daysInt == 0 ? repaymentOverview.getAmount() : repaymentOverview.getAmount().divide(days, ABCNumberUtils.DEFAULT_RESERVED_NUMBER_TWO, RoundingMode.HALF_UP));
            }
            if (repaymentOverview.getPatientorderCount() != null) {
                dto.setRepaymentPatientorderCount(repaymentOverview.getPatientorderCount());
                dto.setAvgRepaymentPatientorderCount(daysInt == 0 ? new BigDecimal(repaymentOverview.getPatientorderCount()) : new BigDecimal(repaymentOverview.getPatientorderCount()).divide(days, ABCNumberUtils.DEFAULT_RESERVED_NUMBER_TWO, RoundingMode.HALF_UP));
            }
        }
        dto.setPayModes(payModeList);
        dto.setFeeTypes(feeClassifyList);
    }

    /**
     * 拼装收费概况-除汇总数据其余数据
     *
     * @param dto             dto
     * @param payModeList     payModeList
     * @param feeClassifyList feeClassifyList
     * @param departmentList  departmentList
     * @param daysInt         daysInt
     */
    private void assembleHospitalOverviewSummaryOther(
            RevenueOverviewHospitalAmountDto dto,
            List<RevenueOverviewPayModeDao> payModeList,
            List<RevenueOverviewFeeClassifyDao> feeClassifyList,
            List<RevenueOverviewDepartmentResp> departmentList,
            RepaymentOverviewDao repaymentOverview,
            int daysInt) {
        BigDecimal days = BigDecimal.valueOf(daysInt);
        // 还款处理
        if (repaymentOverview != null) {
            if (repaymentOverview.getAmount() != null) {
                dto.setRepaymentAmount(repaymentOverview.getAmount());
                dto.setAvgRepaymentAmount(daysInt == 0 ? repaymentOverview.getAmount() : repaymentOverview.getAmount().divide(days, ABCNumberUtils.DEFAULT_RESERVED_NUMBER_TWO, RoundingMode.HALF_UP));
            }
            if (repaymentOverview.getPatientorderCount() != null) {
                dto.setRepaymentPatientorderCount(repaymentOverview.getPatientorderCount());
                dto.setAvgRepaymentPatientorderCount(daysInt == 0 ? new BigDecimal(repaymentOverview.getPatientorderCount()) : new BigDecimal(repaymentOverview.getPatientorderCount()).divide(days, ABCNumberUtils.DEFAULT_RESERVED_NUMBER_TWO, RoundingMode.HALF_UP));
            }
        }
        dto.setPayModes(payModeList);
        dto.setFeeTypes(feeClassifyList);
        dto.setDepartments(departmentList);
    }

    /**
     * 收费趋势
     *
     * @param params 参数
     * @return list
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public List<RevenueOverviewAmountTrendDao> selectChargeAmountTrend(
            RevenueOverviewTrendReqParams params
    ) throws ExecutionException, InterruptedException {
        StatConfigDto statConfig = statConfigService.selectConfig(
                new StatConfigParam(params.getChainId(), params.getClinicId()));
        params.initParams(statConfig);

        Map<String, RevenueOverviewAmountTrendDao> map = TimeUtils.buildDailyMap(
                params.getBeginDate(), params.getEndDate(),
                params.getGroupBy().trim(), RevenueOverviewAmountTrendDao.class);
        map.forEach((k, v) -> {
            if (v != null) {
                v.setDate(k);
                v.setValue(BigDecimal.ZERO);
            }
        });
        //只要判断最后一天是当天就需要执行，因为kudu中是没有当天的数据的
        CompletableFuture<RevenueOverviewAmountTrendDao> todayF = null;
        if (params.isToday()) {
            //查询当天数据
            todayF = CompletableFuture.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectAmountTrendToday(
                        TableUtils.getCisTable(),
                        params.getChainId(), params.getClinicId(),
                        params.getEndStr(), TimeUtils.appendEnd(params.getEndStr()), params.getOweSql());
            }, ExecutorUtils.getInitedExecutorPool());
        }
        CompletableFuture<List<RevenueOverviewAmountTrendDao>> pastF = null;
        //跨天或者选择一天不是当天的情况
        if (params.isStat() || !params.isToday()) {
            pastF = CompletableFuture.supplyAsync(() -> {
                params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
                return hologresRevenueOverviewMapper.selectAmountTrend(TableUtils.getCisTable(), params);
            }, ExecutorUtils.getInitedExecutorPool());
        }
        if (params.isStat() || !params.isToday()) {
            List<RevenueOverviewAmountTrendDao> list = null;
            RevenueOverviewAmountTrendDao today = null;
            //判断是不是为当天
            if (params.isToday()) {
                CompletableFuture.allOf(todayF, pastF).join();
                list = pastF.get();
                today = todayF.get();
            } else {
                pastF.join();
                list = pastF.get();
            }
            String todayDate = TimeUtils.getTodayDate();
            if (today != null && today.getDate() != null) {
                todayDate = today.getDate();
            }
            if (params.isMonth()) {
                todayDate = todayDate.substring(0,
                        RevenueOverviewTrendReqParams.SUBSTRING_LENGTH_7);
            }
            if (list != null) {
                list.stream().forEach(dao -> {
                    if (map.containsKey(dao.getDate())) {
                        map.put(dao.getDate(), dao);
                    }
                });
            }
            if (today != null && today.getDate() != null) {
                logger.info(todayDate);
                RevenueOverviewAmountTrendDao d = map.get(todayDate);
                d.setValue(d.getValue().add(today.getValue()));
                d.setMemberContributionAmount(d.getMemberContributionAmount().add(today.getMemberContributionAmount()));
            }
        } else {
            todayF.join();
            RevenueOverviewAmountTrendDao todayPojo = todayF.get();
            if (todayPojo != null) {
                RevenueOverviewAmountTrendDao today = map.get(todayPojo.getDate());
                today.setValue(todayPojo.getValue());
                today.setMemberContributionAmount(todayPojo.getMemberContributionAmount());
            }
        }
        Map<String, RevenueOverviewAmountTrendDao> sortMap = MapUtils.sortMapByKey(
                map, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return o1.compareTo(o2);
                    }
                });
        return new ArrayList<>(sortMap.values());
    }

    /**
     * 收费人次趋势
     *
     * @param params 参数
     * @return list
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public List<RevenueOverviewAmountCountTrendDao> selectChargePatientorderCountTrend(
            RevenueOverviewTrendReqParams params
    ) throws ExecutionException, InterruptedException {
        StatConfigDto statConfig = statConfigService.selectConfig(
                new StatConfigParam(params.getChainId(), params.getClinicId()));
        params.initParams(statConfig);
        logger.info(params.toString());
        CompletableFuture<RevenueOverviewAmountCountTrendDao> todayF = null;
        if (params.isToday()) {
            todayF = CompletableFuture.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectAmountCountTrendToday(
                        TableUtils.getCisTable(),
                        params.getChainId(), params.getClinicId(),
                        params.getEndStr(), TimeUtils.appendEnd(params.getEndStr()), params.getOweSql());
            }, ExecutorUtils.getInitedExecutorPool());
        }
        CompletableFuture<List<RevenueOverviewAmountCountTrendDao>> pastF = null;
        if (params.isStat() || !params.isToday()) {
            pastF = CompletableFuture.supplyAsync(() -> {
                params.setEndDate(TimeUtils.appendEnd(params.getEndDate()));
                return hologresRevenueOverviewMapper.selectAmountCountTrend(TableUtils.getCisTable(), params);
            }, ExecutorUtils.getInitedExecutorPool());
        }
        List<RevenueOverviewAmountCountTrendDao> list = null;
        if (params.isStat() || !params.isToday()) {
            RevenueOverviewAmountCountTrendDao today = null;
            if (params.isToday()) {
                pastF.join();
                todayF.join();
                list = pastF.get();
                today = todayF.get();
            } else {
                pastF.join();
                list = pastF.get();
            }

            if (params.isMonth()) {
                if (today != null) {
                    String todayDate = today.getDate()
                            .substring(0, RevenueOverviewTrendReqParams.SUBSTRING_LENGTH_7);
                    int status = 1;
                    if (list == null || list.size() <= 0) {
                        list = new ArrayList<>();
                        status = CommonConstants.NUMBER_TWO;
                    }
                    if (!(status == 1)) {
                        status = getAmountCountTrend(list, todayDate, today, status);
                    }
                    if (status != 1) {
                        list.add(today);
                    }
                }
            } else {
                if (list == null) {
                    list = new ArrayList<>();
                }
                if (params.isToday()) {
                    todayF.join();
                    today = todayF.get();
                    if (today != null) {
                        list.add(today);
                    }
                }
            }
        } else {
            todayF.join();
            list = new ArrayList<>();
            list.add(todayF.get());
        }
        return list;
    }

    /**
     * @param list      -
     * @param todayDate -
     * @param today     -
     * @param status    -
     * @return -
     */
    private int getAmountCountTrend(List<RevenueOverviewAmountCountTrendDao> list,
                                    String todayDate, RevenueOverviewAmountCountTrendDao today,
                                    int status) {
        for (RevenueOverviewAmountCountTrendDao dao : list) {
            if (dao.getDate().equals(todayDate)) {
                dao.setValue(dao.getValue() + today.getValue());
                dao.setMemberChargedCount(dao.getMemberChargedCount() + dao.getMemberChargedCount());
                status = CommonConstants.NUMBER_THREE;
            }
        }
        return status;
    }


    /**
     * 工作台 - 医生收入
     *
     * @param params  参数
     * @param hisType hisType
     * @return map
     */
    public Map<String, List> selectWorkbenchDoctorViewFeeAmount(
            RevenueOverviewSummaryReqParams params, String hisType
    ) {
        StatConfigDto statConfigDto = statConfigService
                .selectConfig(new StatConfigParam(params.getChainId(),
                        params.getClinicId(), params.getDispensaryType()));
        params.setIsComposeShareEqually(statConfigDto.getIsComposeShareEqually());
        //不随配置修改变化
        params.setArrearsStatTiming(1);
        params.initParams(statConfigDto);
        List<WorkbenchDoctorViewFeeAmountDao> list = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectWorkbenchDoctorViewFeeAmount(TableUtils.getCisTable(), params);

        Map<String, List> result = new HashMap<>();
        List<Map<String, Object>> headerList = HeaderHandler.workbenchDoctorViewFeeAmountHandle(hisType, query, params.getChainId());
        List<Map<String, BigDecimal>> data = new ArrayList<>();
        Map<String, BigDecimal> map = new HashMap<>();
        list.stream().forEach(dao -> {
            if (dao != null && dao.getFeeType1() != null) {
                // data
                if ("1".equals(hisType)) {
                    List<String> headers = Arrays.asList(HeaderHandler
                            .ORAL_ORDERED_WORKBENCH_DOCTOR_FEE_CLASSIFY_HEADER);
                    if (headers.contains(dao.getFeeType1())) {
                        map.put(dao.getFeeType1(), dao.getAmount());
                        map.put("totalAmount", map.getOrDefault("totalAmount", BigDecimal.ZERO)
                                .add(dao.getAmount()));
                    }
                } else {
                    List<String> headers = Arrays.asList(
                            HeaderHandler.ORDERED_WORKBENCH_DOCTOR_FEE_CLASSIFY_HEADER);
                    if (headers.contains(dao.getFeeType1())) {
                        map.put(dao.getFeeType1(), dao.getAmount());
                        map.put("totalAmount", map.getOrDefault("totalAmount", BigDecimal.ZERO)
                                .add(dao.getAmount()));
                    }
                }

            }
        });
        headerList.stream().forEach(header -> {
            if (!map.containsKey(header.get("prop"))) {
                map.put((String) header.get("prop"), BigDecimal.ZERO);
            }
        });
        data.add(map);
        result.put("header", headerList);
        result.put("data", data);
        return result;
    }

    /**
     * 工作台 - 收费员收入
     *
     * @param params  参数
     * @param hisType hisType
     * @return list
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public List<WorkBenchCashierViewFeeAmountDto> selectWorkbenchCashierViewAmount(
            RevenueOverviewSummaryReqParams params, String hisType
    ) throws ExecutionException, InterruptedException {
        StatConfigDto statConfigDto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(),
                params.getClinicId(), params.getDispensaryType()));
        params.initParams(statConfigDto);
        List<WorkBenchCashierViewFeeAmountDto> list = new ArrayList<>();
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        CompletableFuture<List<WorkBenchCashierViewAmountDao>> amountF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectItemsTotal(TableUtils.getCisTable(), params);
        }, cacheExecutorService);
        CompletableFuture<List<WorkBenchCashierViewClassifyAmountDao>> feeF = abcyunExecutorPool.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeClassifyGroupByEmployee(TableUtils.getCisTable(), params);
        });
        CompletableFuture<List<WorkBenchCashierViewClassifyAmountDao>> payModeF = abcyunExecutorPool.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPayModeGroupByEmployee(TableUtils.getCisTable(), params);
        });
        CompletableFuture<List<WorkBenchCashierViewClassifyAmountDao>> treatFeeF = null;
        CompletableFuture<Map<Integer, V2GoodsCustomType>> subFeeConfigF = null;
        if ("1".equals(hisType)) {
            treatFeeF = abcyunExecutorPool.supplyAsync(() -> {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectTreatFeeClassifyGroupByEmployee(TableUtils.getCisTable(), params);
            });
            subFeeConfigF = abcyunExecutorPool.supplyAsync(() -> {
                return query.queryProductCustomTypeTextByChainId(params.getChainId());
            });
        }
        // 总计
        WorkBenchCashierViewFeeAmountDto totalAmountPojo = new WorkBenchCashierViewFeeAmountDto();
        totalAmountPojo.setEmployeeId("99999999999999999999999999999999");
        totalAmountPojo.setEmployeeName("总计");
        // 人员
        amountF.join();
        Map<String, WorkBenchCashierViewFeeAmountDto> map = WorkBenchHandler
                .handleCashierViewAmountWithEmployee(totalAmountPojo, amountF.get());
        // 费用分类饼状图
        feeF.join();
        List<Map<String, Object>> totalFee = WorkBenchHandler
                .handleCashierViewFeeAmountWithEmployee(hisType, query, map, feeF.get(), params.getChainId());
        if ("1".equals(hisType)) {
            CompletableFuture.allOf(treatFeeF, subFeeConfigF).join();
            Map<Integer, V2GoodsCustomType> feeConfigMap = subFeeConfigF.get();
            getOralFeeClassify(treatFeeF.get(), totalFee, feeConfigMap);
        }
        // 支付方式饼状图
        payModeF.join();
        List<Map<String, Object>> totalPayMode = WorkBenchHandler.handleCashierViewPayModeAmountWithEmployee(query, map, payModeF.get());
        // 填充name
        WorkBenchHandler.handleCashierViewFillName(query, map, params.getChainId());

        totalAmountPojo.getFeeTypes().addAll(totalFee);
        totalAmountPojo.getPayModes().addAll(totalPayMode);

        if (StringUtils.isBlank(params.getEmployeeId())) {
            list.add(totalAmountPojo);
        }
        list.addAll(map.values());
        return list;
    }

    /**
     * 口腔管家 - 通过二级分类获取治疗的汇总值并将其对应的二级分类添加到feeClassifyList中
     * 口腔管家: 未指定二级分类的，统一展示一级分类
     *
     * @param treatList    treatList
     * @param totalFee     totalFee
     * @param feeConfigMap feeConfigMap
     */
    private void getOralFeeClassify(
            List<WorkBenchCashierViewClassifyAmountDao> treatList,
            List<Map<String, Object>> totalFee,
            Map<Integer, V2GoodsCustomType> feeConfigMap) {
        RevenueOverviewFeeClassifyDao treatAmount = new RevenueOverviewFeeClassifyDao();
        treatAmount.setValue(BigDecimal.ZERO);
        treatList.stream().forEach(tf -> {
            if (tf != null && !tf.getAmount().equals(BigDecimal.ZERO)) {
                if (tf.getClassifyLevel2() != null && tf.getClassifyLevel2() != 0) {
                    V2GoodsCustomType gct = feeConfigMap.get(tf.getClassifyLevel2());
                    if (gct != null) {
                        Map<String, Object> m = new HashMap<>();
                        m.put("name", gct.getName());
                        m.put("field", tf.getClassifyLevel1() + "/" + tf.getClassifyLevel2());
                        m.put("value", tf.getAmount().setScale(
                                ABCNumberUtils.DEFAULT_RESERVED_NUMBER_TWO,
                                RoundingMode.HALF_UP));
                        totalFee.add(m);
                    }
                } else {
                    treatAmount.setValue(treatAmount.getValue().add(tf.getAmount()));
                }
            }
        });
        if (!treatAmount.getValue().equals(BigDecimal.ZERO)) {
            Map<String, Object> m = new HashMap<>();
            m.put("name", "治疗");
            m.put("field", "4-1");
            m.put("value", treatAmount.getValue().setScale(
                    ABCNumberUtils.DEFAULT_RESERVED_NUMBER_TWO, RoundingMode.HALF_UP));
            totalFee.add(m);
        }
    }

    /**
     * selectWeClinicOverview
     *
     * @param params AbcCisBaseQueryParams
     * @return WeClinicOverviewDao
     */
    public WeClinicOverviewDao selectWeClinicOverview(RevenueOverviewSummaryReqParams params) {
        StatConfigDto statConfigDto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(),
                params.getClinicId(), params.getDispensaryType()));
        params.initParams(statConfigDto);
        WeClinicOverviewDao dao = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectWeclinicOverview(
                TableUtils.getCisTable(), params.getChainId(), params.getClinicId(),
                params.getBeginDate(), params.getEndDate(), params.getOweSql());
        if (dao == null) {
            dao = new WeClinicOverviewDao();
        }
        dao.setCountPatient(AbcDefaultValueUtils.getDefaultInteger(dao.getCountPatient()));
        dao.setCountSales(AbcDefaultValueUtils.getDefaultInteger(dao.getCountSales()));
        dao.setSumFee(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumFee()));
        dao.setSumAdjustment(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumAdjustment()));
        dao.setSumWesternMedicine(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumWesternMedicine()));
        dao.setSumTreatment(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumTreatment()));
        dao.setSumRegistration(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumRegistration()));
        dao.setSumMaterial(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumMaterial()));
        dao.setSumExamination(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumExamination()));
        dao.setSumChineseMedicine(AbcDefaultValueUtils.getDefaultBigDecimal(dao.getSumChineseMedicine()));
        return dao;
    }

    /**
     * selectWeClinicDaily
     *
     * @param params RevenueMbReqParams
     * @return list
     */
    public List<WeClinicDailyDto> selectWeClinicDaily(
            RevenueOverviewTrendReqParams params
    ) {
        params.calaDailyDate();
        params.initParams();
        List<RevenueOverviewAmountTrendDao> data = null;
        List<WeClinicDailyDto> result = new ArrayList<>();
        try {
            data = selectChargeAmountTrend(params);
        } catch (Exception e) {
            logger.error("mb接口日报发生异常");
            e.printStackTrace();
        }
        if (data != null && data.size() > 0) {
            data.sort(new Comparator<RevenueOverviewAmountTrendDao>() {
                @Override
                public int compare(RevenueOverviewAmountTrendDao o1, RevenueOverviewAmountTrendDao o2) {
                    return -o1.getDate().compareTo(o2.getDate());
                }
            });
        }
        if (data != null && data.size() > 0) {
            for (RevenueOverviewAmountTrendDao dto : data) {
                WeClinicDailyDto weData = new WeClinicDailyDto();
                weData.setDay(dto.getDate());
                weData.setDailyFee(dto.getValue());
                result.add(weData);
            }
        }
        return result;
    }

    /**
     * selectJobSummaryIncomeByDoctorId
     *
     * @param params  params
     * @param hisType hisType
     * @return result
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public StatResponse selectJobSummaryIncomeByDoctorId(
            RevenueGadgetReqParams params, String hisType
    ) throws ExecutionException, InterruptedException {
        StatConfigDto statConfigDto = statConfigService
                .selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(), params.getDispensaryType()));
        params.setIsComposeShareEqually(statConfigDto.getIsComposeShareEqually());
        params.setArrearsStatTiming(statConfigDto.getArrearsStatTiming());
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        CompletableFuture<List<JobSummaryIncomeDao>> listF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectJobSummaryIncomeByDoctorId(
                    TableUtils.getCisTable(), hisType, params);
        }, cacheExecutorService);
        CompletableFuture<JobSummaryIncomeDao> totalF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectJobSummaryIncomeByDoctorIdTotal(
                    TableUtils.getCisTable(), hisType, params);
        }, cacheExecutorService);

        CompletableFuture.allOf(listF, totalF).join();

        List<JobSummaryIncomeDao> list = listF.get();

        Set<String> patientIds = new HashSet<>();
        logger.info(list.size() + "");
        list.stream().forEach(x -> {
            if (x != null && x.getPatientId() != null) {
                patientIds.add(x.getPatientId());
            }
        });

        Map<String, V2Patient> patientMap = query.queryPatient(params.getChainId(), patientIds, params.getEnablePatientMobile());

        StatResponse sr = new StatResponse();
        sr.setHeader(HeaderHandler.getJobSummaryIncomeByDoctorId(hisType, params));
        sr.setData(
                list.stream().map(x -> {
                    x.setPatientNameFromMap(patientMap);
                    return x;
                }).collect(Collectors.toList())
        );
        JobSummaryIncomeDao total = totalF.get();
        total.setPatientNameWhenTotalData();
        sr.setSummary(total);
        StatResponseTotal count = new StatResponseTotal();
        count.setCount(total.getCount() != null ? Long.parseLong(total.getCount().toString()) : 0);
        count.setOffset(params.getOffset());
        count.setSize(params.getSize());
        sr.setTotal(count);
        return sr;
    }

    /**
     * selectJobSummaryIncomeByDoctorId
     *
     * @param params  params
     * @param hisType hisType
     * @return result
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public V2StatResponse selectFeeTypeIdByDoctorId(RevenueGadgetReqParams params) throws ExecutionException, InterruptedException {
        V2StatResponse response = new V2StatResponse();
        StatConfigDto statConfigDto = statConfigService
                .selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId()));
        params.setIsComposeShareEqually(statConfigDto.getIsComposeShareEqually());
        params.setArrearsStatTiming(statConfigDto.getArrearsStatTiming());
        List<String> patientOrders = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPatientOrderIds(TableUtils.getCisTable(), params);
        List<TableHeaderEmployeeItem> items = HeaderHandler.getFeeTypeIdByDoctorIdTable();
        if (patientOrders == null || patientOrders.size() == 0) {
            response.setHeader(items);
            return response;
        }
        CompletableFuture<List<JobSummaryIncomeHospitalDao>> listF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeTypeIdByDoctorId(TableUtils.getCisTable(), params, patientOrders);
        }, cacheExecutorService);
        CompletableFuture<Long> totalF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeTypeIdByDoctorIdTotal(TableUtils.getCisTable(), params);
        }, cacheExecutorService);
        CompletableFuture<List<Map<String, Object>>> summaryF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeTypeIdByDoctorIdSummary(
                    TableUtils.getCisTable(), params);
        }, cacheExecutorService);
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = CompletableFuture.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        }, cacheExecutorService);

        CompletableFuture.allOf(listF, totalF, summaryF, feeTypeF).join();

        List<JobSummaryIncomeHospitalDao> list = listF.get();
        Long total = totalF.get();
        List<Map<String, Object>> summary = summaryF.get();
        Map<Long, V2GoodsFeeType> feeTypeMap = feeTypeF.get();

        Set<String> patientIds = new HashSet<>();
        list.stream().forEach(x -> {
            x.setSummaryAmount();
            String patientId = x.getPatientId();
            if (x != null && patientId != null) {
                patientIds.add(patientId);
            }
        });
        Map<String, V2Patient> patientMap = query.queryPatient(params.getChainId(), patientIds, params.getEnablePatientMobile());
        Map<Object, Object> summaryAmount = new HashMap<>();
        if (summary != null && summary.size() > 0) {
            BigDecimal zero = BigDecimal.ZERO;
            for (Map<String, Object> sum : summary) {
                BigDecimal amount = (BigDecimal) sum.get("amount");
                summaryAmount.put(String.valueOf(sum.get("feeTypeId")), amount);
                if (amount != null) {
                    zero = zero.add(amount);
                }
                V2GoodsFeeType feeType = feeTypeMap.get((Long) sum.get("feeTypeId"));
                TableHeaderEmployeeItem item = new TableHeaderEmployeeItem();
                if (feeType != null) {
                    item.setLabel(feeType.getName());
                } else {
                    item.setLabel("未指定");
                }
                item.setProp(String.valueOf(sum.get("feeTypeId")));
                item.setAlign("right");
                item.setType("money");
                item.setTitleAlign("right");
                items.add(item);
            }
            summaryAmount.put("summary", zero);
            summaryAmount.put("patientName", "共" + total + "条");
        }
        response.setHeader(items);
        response.setData(
                list.stream().map(x -> {
                    V2Patient v2Patient = patientMap.get(x.getPatientId());
                    if (v2Patient != null) {
                        x.setPatientName(v2Patient.getName());
                    }
                    Map<Object, Object> result = new HashMap<>();
                    result.put("patientName", x.getPatientName());
                    result.put("patientId", x.getPatientId());
                    result.put("summary", x.getSummary());
                    if (x.getFeeTypes() != null && x.getFeeTypes().size() > 0) {
                        x.getFeeTypes().forEach(k -> {
                            result.put(k.getFeeTypeId(), k.getAmount());
                        });
                    }
                    return result;
                }).collect(Collectors.toList())
        );
        response.setSummary(summaryAmount);
        StatResponseTotal statResponseTotal = new StatResponseTotal();
        statResponseTotal.setCount(total);
        response.setTotal(statResponseTotal);
        return response;
    }

    /**
     * selectJobSummaryInfo
     *
     * @param params  params
     * @param hisType hisType
     * @return JobSummaryInfoResp
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public JobSummaryInfoResp selectJobSummaryInfo(
            RevenueGadgetReqParams params, String hisType
    ) throws ExecutionException, InterruptedException {
        JobSummaryInfoResp resp = new JobSummaryInfoResp();
        StatConfigDto dto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(),
                params.getDispensaryType()));
        CompletableFuture<List<OutpatientFirstRevisitRsp>> outpatientvisitListF =
                CompletableFuture.supplyAsync(() -> {
                    return kuduOutpatientService.getOutpatientFirstRevisitNum(
                            params.getChainId(), params.getClinicId(),
                            params.getBeginDate(), params.getEndDate(),
                            params.getDoctorId());
                }, cacheExecutorService);
        CompletableFuture<List<RegistrationSummary>> registrationListF = CompletableFuture.supplyAsync(() -> {
            RegistrationReqParams registrationReqParams = new RegistrationReqParams();
            registrationReqParams.initAbcCisBaseQueryParams(params.getChainId(), params.getClinicId());
            registrationReqParams.setChainId(params.getChainId());
            registrationReqParams.setClinicId(params.getClinicId());
            registrationReqParams.setBeginDate(params.getBeginDate());
            registrationReqParams.setEndDate(params.getEndDate());
            registrationReqParams.setDoctorId(params.getDoctorId());
            return registrationDataHandler.fetchSummary(registrationReqParams, null, null, dto);
        }, cacheExecutorService);
        CompletableFuture<List<String>> outpatientPrescriptionF = CompletableFuture.supplyAsync(() -> {
            return kuduOutpatientService.selectPrescriptionCountByDoctorId(
                    params.getChainId(), params.getClinicId(),
                    params.getBeginDate(), params.getEndDate(),
                    params.getDoctorId());
        }, cacheExecutorService);
        CompletableFuture<JobSummaryIncomeDao> totalAmountF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectJobSummaryIncomeTotalByDoctorId(
                    TableUtils.getCisTable(), hisType, params);
        }, cacheExecutorService);

        CompletableFuture.allOf(outpatientvisitListF, registrationListF,
                outpatientPrescriptionF, totalAmountF).join();

        List<OutpatientFirstRevisitRsp> outpatientvisitList = outpatientvisitListF.get();
        if (outpatientvisitList != null && outpatientvisitList.size() > 0) {
            OutpatientFirstRevisitRsp ofrr = outpatientvisitList.get(0);
            resp.setFirstVisitCount(ofrr.getFirstVisitCount());
            resp.setRevisitCount(ofrr.getReVisitCount());
        }
        List<RegistrationSummary> registrationList = registrationListF.get();
        if (registrationList != null && registrationList.size() > 0) {
            RegistrationSummary rs = registrationList.get(0);
            resp.setRegistrationCount(rs.getTotalCount());
        }
        JobSummaryIncomeDao amountPojo = totalAmountF.get();
        if (amountPojo != null) {
            resp.setAmount(amountPojo.getTotalAmount());
        }
        List<String> prescriptionInfos = outpatientPrescriptionF.get();
        int num = 0;
        if (prescriptionInfos != null && prescriptionInfos.size() > 0) {
            for (String info : prescriptionInfos) {
                JSONObject jsonObject = JSONObject.parseObject(info);
                Object prescriptionNum = jsonObject.get("prescriptionNum");
                if (prescriptionNum != null) {
                    num += (int) prescriptionNum;
                }
            }
        }
        resp.setOutpatientPrescriptionCount(num);
        resp.setDefalutValue();
        return resp;
    }


    /**
     * selectRevenueGadgetForOverview
     *
     * @param params 参数
     * @return RevenueGadgetOverviewResp
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public RevenueGadgetOverviewResp selectRevenueGadgetForOverview(
            RevenueOverviewSummaryReqParams params
    ) throws ExecutionException, InterruptedException {
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        StatConfigDto statConfig = statConfigService.selectConfig(
                new StatConfigParam(params.getChainId(), params.getClinicId()));
        params.initParams(statConfig);
        CompletableFuture<RevenueGadgetOverviewResp> overviewFuture = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectRevenueGadgetForOverview(TableUtils.getCisTable(), params);
        });
        CompletableFuture<RepaymentOverviewDao> repaymentF = CompletableFuture.supplyAsync(() -> {
            return odsChargeMapper.getRepaymentOverview(TableUtils.getCisChargeTable(), params);
        }, cacheExecutorService);

        CompletableFuture.allOf(overviewFuture, repaymentF).join();

        RevenueGadgetOverviewResp resp = overviewFuture.get();
        resp.set();
        resp.setRepaymentAmount(repaymentF.get());
        resp.initTotalAmount();
        return resp;
    }

    /**
     * selectRevenueGadgetForCheckAccount
     *
     * @param params params
     * @return RevenueGadgetCheckAccountResp
     * @throws ExecutionException   Exception
     * @throws InterruptedException Exception
     */
    public RevenueGadgetCheckAccountResp selectRevenueGadgetForCheckAccount(
            RevenueOverviewSummaryReqParams params) throws ExecutionException, InterruptedException {
        StatConfigDto statConfigDto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(), params.getDispensaryType()));
        params.initParams(statConfigDto);

        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        CompletableFuture<List<RevenueOverviewFeeClassifyDao>> feeClassifyF = abcyunExecutorPool.supplyAsync(() -> {
            if (params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeType(TableUtils.getCisTable(), params);
            } else {
                return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectFeeClassify(TableUtils.getCisTable(), params);
            }
        });
        CompletableFuture<List<RevenueOverviewPayModeDao>> payModeF = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper).selectPayModeForGadget(TableUtils.getCisTable(), params);
        }, cacheExecutorService);
        CompletableFuture<List<RevenueOverviewPayModeDao>> repaymentPayModeF =
                CompletableFuture.supplyAsync(() -> {
                    return odsChargeMapper.getRepaymentGadgetGroupByPayType(TableUtils.getCisChargeTable(), params);
                }, cacheExecutorService);
        CompletableFuture<Map<Integer, String>> payConfigF = CompletableFuture.supplyAsync(() -> {
            return query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<Integer, V2ChargePayModeConfig>> usingPayModeF = CompletableFuture.supplyAsync(() -> {
            return query.queryUsingPayModeByChainId(params.getChainId());
        }, cacheExecutorService);
        CompletableFuture<RepaymentOverviewDao> repaymentF = CompletableFuture.supplyAsync(() -> {
            return odsChargeMapper.getRepaymentOverview(TableUtils.getCisChargeTable(), params);
        }, cacheExecutorService);
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = null;
        if (params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            feeTypeF = CompletableFuture.supplyAsync(() -> {
                return query.selectAdviceFeeType(params.getChainId());
            }, cacheExecutorService);
            feeTypeF.join();
        }

        CompletableFuture.allOf(feeClassifyF, payConfigF, payModeF, repaymentPayModeF,
                usingPayModeF, repaymentF).join();
        // 费用分类
        List<RevenueOverviewFeeClassifyDao> feeClassifyList = null;
        if (params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            feeClassifyList = getFeeType(feeClassifyF.get(), feeTypeF.get(), true);
        } else {
            feeClassifyList = getFeeClassifyForCheckAccount(
                    feeClassifyF.get(), repaymentF.get(), params, payModeF.get());
            if (CisJWTUtils.CIS_HIS_TYPE_DENTISTRY.equals(params.getHisType())) {
                feeClassifyList = feeClassifyList.stream().filter(x -> !("1-12".equals(x.getId())
                        || "1-13".equals(x.getId()))).collect(Collectors.toList());
            }
            if (CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(params.getHisType())) {
                feeClassifyList = feeClassifyList.stream().filter(x -> !("20".equals(x.getId()))).collect(Collectors.toList());
            }
        }

        // 支付方式
        List<RevenueOverviewPayModeDao> payModeList = getPayModesForCheckAccount(
                payModeF.get(), repaymentPayModeF.get(), payConfigF.get(), usingPayModeF.get());
        if (CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(params.getHisType())) {
            payModeList = payModeList.stream().filter(x -> !("20".equals(x.getField()))).collect(Collectors.toList());
        }
        RevenueGadgetCheckAccountResp resp = new RevenueGadgetCheckAccountResp();
        resp.setPayModes(payModeList);
        resp.setFees(feeClassifyList);
        return resp;
    }

    /**
     * 填补费用类型name
     *
     * @param feeClassifys   费用分类
     * @param feeTypeMap     费用类型
     * @param hasTotalAmount 是否需要总计
     * @return list
     */
    private List<RevenueOverviewFeeClassifyDao> getFeeType(
            List<RevenueOverviewFeeClassifyDao> feeClassifys,
            Map<Long, V2GoodsFeeType> feeTypeMap,
            boolean hasTotalAmount
    ) {
        RevenueOverviewFeeClassifyDao feeTotalAmount = null;
        if (hasTotalAmount) {
            feeTotalAmount = new RevenueOverviewFeeClassifyDao(
                    "totalAmount", "总计");
        }
        for (RevenueOverviewFeeClassifyDao ft : feeClassifys) {
            ft.setFeeTypeName(feeTypeMap);
            if (hasTotalAmount) {
                feeTotalAmount.setValue(feeTotalAmount.getValue().add(ft.getValue()));
            }
        }
        if (hasTotalAmount) {
            feeClassifys.add(feeTotalAmount);
        }
        return feeClassifys;
    }

    /**
     * getFeeClassifyForCheckAccount
     *
     * @param feeClassifys feeClassifys
     * @param repayment    repayment
     * @param params       params
     * @param list         list
     * @return list
     */
    private List<RevenueOverviewFeeClassifyDao> getFeeClassifyForCheckAccount(
            List<RevenueOverviewFeeClassifyDao> feeClassifys,
            RepaymentOverviewDao repayment,
            RevenueOverviewSummaryReqParams params,
            List<RevenueOverviewPayModeDao> list

    ) {
        List<String> revenueGadgetForCheckAccountFees = GadgetHandler
                .getRevenueGadgetForCheckAccountFeeHeaders(params);
        RevenueOverviewFeeClassifyDao feeTotalAmount = new RevenueOverviewFeeClassifyDao(
                "totalAmount", "总计");
        RevenueOverviewFeeClassifyDao otherFeeAmount = new RevenueOverviewFeeClassifyDao(
                "otherFeeAmount", "其他费用");
        Map<String, RevenueOverviewFeeClassifyDao> feeClassifyMap = new HashMap<>();
        for (RevenueOverviewFeeClassifyDao fee : feeClassifys) {
            if (fee != null && fee.getValue() != null) {
                String key = fee.getId();
                if ("7-1".equals(key) || "7-2".equals(key) || "7-3".equals(key) || "7-4".equals(key) || "7-5".equals(key)) {
                    key = "7-0";
                    fee.setId(key);
                }
                if ("13-1".equals(key)) {
                    key = "13-0";
                    fee.setId(key);
                }
                if (revenueGadgetForCheckAccountFees.contains(key)) {
                    RevenueOverviewFeeClassifyDao f = feeClassifyMap.get(key);
                    if (f == null) {
                        fee.setName(query.queryProductClassifyLevel1(params.getChainId(), key, params.getHisType()));
                        feeClassifyMap.put(key, fee);
                    } else {
                        f.setValue(f.getValue().add(fee.getValue()));
                    }
                } else {
                    otherFeeAmount.setValue(otherFeeAmount.getValue().add(fee.getValue()));
                }
                feeTotalAmount.setValue(feeTotalAmount.getValue().add(fee.getValue()));
            }
        }
        List<RevenueOverviewFeeClassifyDao> feeClassifyList = new ArrayList<>();
        for (String k : revenueGadgetForCheckAccountFees) {
            if (feeClassifyMap.containsKey(k)) {
                feeClassifyList.add(feeClassifyMap.get(k));
            } else {
                RevenueOverviewFeeClassifyDao f = new RevenueOverviewFeeClassifyDao(
                        k,
                        query.queryProductClassifyLevel1(params.getChainId(), k, params.getHisType()),
                        BigDecimal.ZERO
                );
                feeClassifyList.add(f);
            }
        }
        //将会员本金和开卡本金合并成充值本金
        RevenueOverviewFeeClassifyDao principalFee = null;
        Iterator<RevenueOverviewFeeClassifyDao> iterator = feeClassifyList.iterator();
        while (iterator.hasNext()) {
            RevenueOverviewFeeClassifyDao fee = iterator.next();
            if (fee != null && fee.getValue() != null) {
                if ("-2".equals(fee.getId()) || "18-0".equals(fee.getId())) {
                    if (principalFee == null) {
                        principalFee = JSON.parseObject(JSON.toJSONString(fee), RevenueOverviewFeeClassifyDao.class);
                    } else {
                        principalFee.setId(principalFee.getId() + "-" + fee.getId());
                        principalFee.setName("充值本金");
                        principalFee.setValue(principalFee.getValue().add(fee.getValue()));
                    }
                    iterator.remove();
                }
            }
        }

        RevenueOverviewFeeClassifyDao repaymentClassify = new RevenueOverviewFeeClassifyDao(
                "repaymentAmount", "还款");
        if (params.getArrearsStatTiming() != null && params.getArrearsStatTiming() == CommonConstants.NUMBER_TWO) {
            repaymentClassify = new RevenueOverviewFeeClassifyDao("20", "欠费");
            if (list != null && list.size() > 0) {
                for (RevenueOverviewPayModeDao payMode : list) {
                    if (payMode != null && "20".equals(payMode.getField()) && payMode.getValue() != null) {
                        repaymentClassify.setValue(repaymentClassify.getValue().add(payMode.getValue()));
                        feeTotalAmount.setValue(feeTotalAmount.getValue().add(payMode.getValue()));
                        break;
                    }
                }
            }
        } else {
            if (repayment != null && repayment.getAmount() != null) {
                repaymentClassify.setValue(repaymentClassify.getValue().add(repayment.getAmount()));
                feeTotalAmount.setValue(feeTotalAmount.getValue().add(repayment.getAmount()));
            }
        }
        feeClassifyList.add(principalFee);
        if (!CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(params.getHisType())) {
            feeClassifyList.add(otherFeeAmount);
            feeClassifyList.add(repaymentClassify);
        }
        feeClassifyList.add(feeTotalAmount);
        return feeClassifyList;
    }

    /**
     * getPayModesForCheckAccount
     *
     * @param list                 list
     * @param repaymentPayModeList repaymentPayModeList
     * @param payConfigMap         payConfigMap
     * @param usingPayMode         usingPayMode
     * @return list
     */
    private List<RevenueOverviewPayModeDao> getPayModesForCheckAccount(
            List<RevenueOverviewPayModeDao> list,
            List<RevenueOverviewPayModeDao> repaymentPayModeList,
            Map<Integer, String> payConfigMap,
            Map<Integer, V2ChargePayModeConfig> usingPayMode
    ) {
        RevenueOverviewPayModeDao totalPayAmount = new RevenueOverviewPayModeDao("totalAmount", "总计");
        Map<String, RevenueOverviewPayModeDao> payModeMap = list.stream()
                .filter(x -> x != null && x.getField() != null)
                .map(payMode -> {
                    payMode.setPayModeNameFromGadget(payConfigMap);
                    totalPayAmount.setValue(totalPayAmount.getValue().add(payMode.getValue()));
                    return payMode;
                }).collect(Collectors.toMap(
                        RevenueOverviewPayModeDao::getField,
                        RevenueOverviewPayModeDao -> RevenueOverviewPayModeDao
                ));

        repaymentPayModeList.stream().forEach(pm -> {
            if (pm != null) {
                pm.setPayModeNameFromGadget(payConfigMap);
                pm.addToMap(payModeMap);
                totalPayAmount.setValue(totalPayAmount.getValue().add(pm.getValue()));
            }
        });

        List<RevenueOverviewPayModeDao> payModeList = new ArrayList<>();
        Set<String> existPayMode = new HashSet<>();
        for (V2ChargePayModeConfig cpmc : usingPayMode.values()) {
            if (cpmc.getId() == PayMode.WECHART) {
                payModeList.add(getPayModeOrDefault(payModeMap, "2_0", "微信记账"));
                payModeList.add(getPayModeOrDefault(payModeMap, "2_1", "微信直付"));
                existPayMode.add("2_0");
                existPayMode.add("2_1");
                continue;
            }
            String key = cpmc.getId().toString();
            payModeList.add(getPayModeOrDefault(payModeMap, key, cpmc.getName()));
            existPayMode.add(key);
        }

        for (Map.Entry<String, RevenueOverviewPayModeDao> entry : payModeMap.entrySet()) {
            if (!existPayMode.contains(entry.getKey())) {
                payModeList.add(entry.getValue());
            }
        }
        payModeList.add(totalPayAmount);
        return payModeList;
    }

    /**
     * getPayModeOrDefault
     *
     * @param payModeMap payModeMap
     * @param key        key
     * @param name       name
     * @return RevenueOverviewPayModeDao
     */
    private RevenueOverviewPayModeDao getPayModeOrDefault(
            Map<String, RevenueOverviewPayModeDao> payModeMap, String key, String name) {
        RevenueOverviewPayModeDao pm = payModeMap.get(key);
        if (pm == null) {
            pm = new RevenueOverviewPayModeDao();
            pm.setField(key);
            pm.setName(name);
            pm.setValue(BigDecimal.ZERO);
        }
        return pm;
    }

    /**
     * @param
     * @param params -
     * @return
     * @return Integer
     * @Description: 获取零售人次
     * @Author: zs
     * @Date: 2023/2/13 16:35
     */
    public Integer getRetailPersonTime(RevenueOverviewSummaryReqParams params) {
        if (params == null) {
            throw new RuntimeException("getRetailPersonTime方法参数不能为空");
        }
        Integer result = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRevenueOverviewMapper)
                .getRetailPersonTime(TableUtils.getCisTable(), params);
        return result;
    }

    /**
     * @param
     * @param reqParams -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.ExcelUtils.AbcExcelSheet>
     * @Description: 门诊看板 - 费用明细异步导出
     * @Author: zs
     * @Date: 2024/7/16 17:11
     */
    public List<ExcelUtils.AbcExcelSheet> export(RevenueGadgetReqParams reqParams) throws ExecutionException, InterruptedException {
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();
        sheet.setName("费用明细");
        if (CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(reqParams.getHisType())) {
            V2StatResponse response = selectFeeTypeIdByDoctorId(reqParams);
            List data = response.getData();
            if (data != null) {
                data.add(response.getSummary());
            }
            sheet.setData(ExcelUtils.exportMapDataV2(data, response.getHeader()));
            sheet.setSheetDefinition(ExcelUtils.exportTableHeader(response.getHeader()));
            sheet.setTableHeaderEmployeeItems(response.getHeader());
        } else {
            StatResponse statResponse = selectJobSummaryIncomeByDoctorId(reqParams, reqParams.getHisType());
            sheet.setData(ExcelUtils.exportData(statResponse.getData(), statResponse.getHeader()));
            sheet.setSheetDefinition(ExcelUtils.exportHeader(statResponse.getHeader()));
        }
        sheets.add(sheet);
        return sheets;
    }
}
