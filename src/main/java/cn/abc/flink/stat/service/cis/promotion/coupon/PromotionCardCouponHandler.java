package cn.abc.flink.stat.service.cis.promotion.coupon;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.request.params.PromotionCardCouponExportParam;
import cn.abc.flink.stat.service.cis.promotion.coupon.domain.PromotionCardCouponListEntity;
import cn.abc.flink.stat.service.cis.promotion.coupon.domain.PromotionCardCouponListHeader;
import cn.abc.flink.stat.service.cis.promotion.coupon.domain.PromotionCardCouponTransactionFlowEntity;
import cn.abc.flink.stat.service.cis.promotion.coupon.domain.PromotionCardCouponTransactionFlowHeader;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2021-08-10 17:43
 **/
@Component
public class PromotionCardCouponHandler {
    private static List<PromotionCardCouponListHeader> couponListHeaders;
    private static List<PromotionCardCouponTransactionFlowHeader> couponTransactionFlowHeaders;
    private static List<String> couponListHeaderLabel;
    private static List<String> makeCouponTransactionFlowHeaderLabel;
    private static List<Map<String, Object>> chargeType;

    public static List<PromotionCardCouponListHeader> getCouponListHeaders() {
        return couponListHeaders;
    }

    public static List<Map<String, Object>> getChargeType() {
        return chargeType;
    }

    static final int TWO = 2;
    static final int NINETY = 90;
    static final int ONE_HUNDRED_AND_FOURTEEN = 114;
    static final int ONE_HUNDRED_AND_TWENTY = 120;
    static final int ONE_HUNDRED = 100;
    static final int ONE_HUNDRED_AND_SIXTY = 160;
    static final int SEVENTY_FIVE = 75;
    static final int SIXTY = 60;
    static final int ONE_HUNDRED_AND_FIVE = 105;


    static {
        couponListHeaders = new ArrayList<>();
        couponListHeaders.add(new PromotionCardCouponListHeader("持券人姓名", "patientName", "center", NINETY));
        couponListHeaders.add(new PromotionCardCouponListHeader("手机号", "patientMobile", "center",
                ONE_HUNDRED_AND_FOURTEEN));
        couponListHeaders.add(new PromotionCardCouponListHeader("优惠券名称", "promotionName", "center",
                ONE_HUNDRED_AND_TWENTY));
        couponListHeaders.add(new PromotionCardCouponListHeader("领取时间", "created", "center", ONE_HUNDRED_AND_SIXTY));
        couponListHeaders.add(new PromotionCardCouponListHeader("领取数量", "receivedQuantity", "center", SEVENTY_FIVE));
        couponListHeaders.add(new PromotionCardCouponListHeader("使用数量", "usedQuantity", "center", SEVENTY_FIVE));
        couponListHeaders.add(new PromotionCardCouponListHeader("过期/作废数量", "expiredQuantity", "center",
                ONE_HUNDRED_AND_FIVE));
        couponListHeaders.add(new PromotionCardCouponListHeader("剩余有效数量", "remainingQuantity", "center",
                ONE_HUNDRED_AND_FIVE));
        couponListHeaders.add(new PromotionCardCouponListHeader("到期时间", "validEnd", "center", ONE_HUNDRED_AND_SIXTY));


        couponListHeaderLabel = new ArrayList<>();
        couponListHeaderLabel.add("持券人姓名");
        couponListHeaderLabel.add("手机号");
        couponListHeaderLabel.add("优惠券名称");
        couponListHeaderLabel.add("领取时间");
        couponListHeaderLabel.add("领取数量");
        couponListHeaderLabel.add("使用数量");
        couponListHeaderLabel.add("过期/作废数量");
        couponListHeaderLabel.add("剩余有效数量");
        couponListHeaderLabel.add("到期时间");

        makeCouponTransactionFlowHeaderLabel = new ArrayList<>();
        makeCouponTransactionFlowHeaderLabel.add("持券人姓名");
        makeCouponTransactionFlowHeaderLabel.add("手机号");
        makeCouponTransactionFlowHeaderLabel.add("交易类型");
        makeCouponTransactionFlowHeaderLabel.add("券名称");
        makeCouponTransactionFlowHeaderLabel.add("使用数量");
        makeCouponTransactionFlowHeaderLabel.add("交易时间");
        makeCouponTransactionFlowHeaderLabel.add("消费门店");
        makeCouponTransactionFlowHeaderLabel.add("优惠券抵扣金额");
        makeCouponTransactionFlowHeaderLabel.add("实际收金额");
        makeCouponTransactionFlowHeaderLabel.add("开单人");
        makeCouponTransactionFlowHeaderLabel.add("操作人");


        chargeType = new ArrayList<>();
        HashMap<String, Object> m1 = new HashMap<>();
        m1.put("charge_no", 1);
        m1.put("charge_type", "消费抵扣");

        HashMap<String, Object> m2 = new HashMap<>();
        m2.put("charge_no", TWO);
        m2.put("charge_type", "退款");
        chargeType.add(m1);
        chargeType.add(m2);
    }

    /**
     * @param singleStore singleStore
     * @return List<PromotionCardCouponTransactionFlowHeader>
     */
    public static List<PromotionCardCouponTransactionFlowHeader> getCardCouponTransactionFlowHeader(Byte singleStore) {
        couponTransactionFlowHeaders = new ArrayList<>();
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("持券人姓名", "patientName",
                "center", NINETY));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("手机号", "patientMobile",
                "center", ONE_HUNDRED_AND_FOURTEEN));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("交易类型", "chargeType",
                "center", SEVENTY_FIVE));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("券名称", "couponName",
                "center", ONE_HUNDRED_AND_TWENTY));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("使用数量", "useCount",
                "center", SEVENTY_FIVE));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("交易时间", "usedDate",
                "center", ONE_HUNDRED_AND_SIXTY));
        if (singleStore != null && singleStore != 0) {
            couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("消费门店", "clinicName",
                    "center", SEVENTY_FIVE));
        }
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("优惠券抵扣金额",
                "discountPrice", "center", ONE_HUNDRED_AND_TWENTY, "money"));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("实收金额",
                "actualMoney", "center", SEVENTY_FIVE, "money"));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("开单人",
                "sellerName", "center", SIXTY));
        couponTransactionFlowHeaders.add(new PromotionCardCouponTransactionFlowHeader("操作人",
                "operator", "center", SIXTY));
        return couponTransactionFlowHeaders;
    }

    /**
     * @param singleStore singleStore
     * @return List<PromotionCardCouponTransactionFlowHeader>
     */
    public static List<TableHeaderEmployeeItem> getCardCouponTransactionFlowHeader(
            List<TableHeaderEmployeeItem> headerEmployeeItems, Byte singleStore) {
        if (singleStore != null && singleStore != 0) {
            return headerEmployeeItems;
        }
        return headerEmployeeItems.stream().filter(item -> !item.getKey().equals("clinicName"))
                .collect(Collectors.toList());
    }

    /**
     * @param summaryTurnoverFuture summaryTurnoverFuture
     * @param summaryNum            summaryNum
     * @return List<Map < String, Object>>
     */
    public List<Map<String, Object>> handlerSummary(Map<String, BigDecimal> summaryTurnoverFuture,
                                                    Map<String, Integer> summaryNum) {
        ArrayList<Map<String, Object>> result = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("######0.00");
        HashMap<String, Object> m1 = new HashMap<>();
        HashMap<String, Object> m2 = new HashMap<>();
        if (summaryNum != null) {
            m1.put("text", "领券总数");
            m1.put("value", summaryNum.getOrDefault("total_coupon_num", 0));
            m2.put("text", "用券总数");
            m2.put("value", summaryNum.getOrDefault("use_coupon_num", 0));
        } else {
            m1.put("text", "领券总数");
            m1.put("value", 0);
            m2.put("text", "用券总数");
            m2.put("value", 0);
        }
        HashMap<String, Object> m3 = new HashMap<>();
        HashMap<String, Object> m4 = new HashMap<>();
        HashMap<String, Object> m5 = new HashMap<>();
        if (summaryTurnoverFuture != null) {
            Double totalReceived = summaryTurnoverFuture.getOrDefault("total_received",
                    new BigDecimal(0)).doubleValue();
            m3.put("text", "用券总成交额");
            m3.put("value", totalReceived);
            Double totalDiscount = Math.abs(summaryTurnoverFuture.getOrDefault("total_discount",
                    new BigDecimal(0)).doubleValue());
            m4.put("text", "已用优惠券总金额");
            m4.put("value", totalDiscount);
            m5.put("text", "费效比");
            if (totalReceived != 0 && totalReceived != null) {
                m5.put("value", df.format(((totalReceived.doubleValue() / totalDiscount.doubleValue()) * ONE_HUNDRED))
                        + "%");
            } else {
                m5.put("value", "0.00%");
            }
        } else {
            m3.put("text", "用券总成交额");
            m3.put("value", 0);
            m4.put("text", "已用优惠券总金额");
            m4.put("value", 0);
            m5.put("text", "费效比");
            m5.put("value", "0.00%");
        }
        result.add(m1);
        result.add(m2);
        result.add(m3);
        result.add(m4);
        result.add(m5);
        return result;
    }

    /**
     * @param response response
     * @param param    param
     */
    public void export(HttpServletResponse response, PromotionCardCouponExportParam param) {
        param.setBeginDate(param.getBeginDate().substring(0, CommonConstants.DATE_LENGTH));
        param.setEndDate(param.getEndDate().substring(0, CommonConstants.DATE_LENGTH));
        String fileName = "优惠券统计" + param.getBeginDate() + "_" + param.getEndDate();
        byte[] bytes = fileName.getBytes(StandardCharsets.UTF_8);

        try {
            fileName = new String(bytes, "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList();


        ExcelUtils.AbcExcelSheet couponListSheet = new ExcelUtils.AbcExcelSheet();
        couponListSheet.setName("持券列表");
        couponListSheet.setData(makeCouponListExcelBody(param.getCouponList()));
        couponListSheet.setSheetDefinition(makeCouponListExcelHead());
        sheets.add(couponListSheet);

        ExcelUtils.AbcExcelSheet couponTransactionFloweet = new ExcelUtils.AbcExcelSheet();
        couponTransactionFloweet.setName("交易流水");
        couponTransactionFloweet.setData(makeCouponTransactionFlowExcelBody(param.getCouponTransactionFlow(),
                param.getSingleStore()));
        couponTransactionFloweet.setSheetDefinition(makeCouponTransactionFlowExcelHead(param.getSingleStore()));
        sheets.add(couponTransactionFloweet);
        try {
            ExcelUtils.export(fileName, response, sheets);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * @param couponList couponList
     * @return List
     */
    public List makeCouponListExcelBody(List<PromotionCardCouponListEntity> couponList) {
        List<List<Object>> lines = new ArrayList<>();
        if (couponList == null) {
            return lines;
        }

        for (PromotionCardCouponListEntity d : couponList) {
            List<Object> row = new ArrayList<>();
            row.add(d.getPatientName());
            row.add(d.getPatientMobile());
            row.add(d.getPromotionName());
            row.add(d.getCreated());
            row.add(d.getReceivedQuantity());
            row.add(d.getUsedQuantity());
            row.add(d.getExpiredQuantity());
            row.add(d.getRemainingQuantity());
            row.add(d.getValidEnd());
            lines.add(row);
        }
        return lines;
    }

    /**
     * @return List
     */
    public List makeCouponListExcelHead() {
        List<List<String>> header = new ArrayList<>();
        for (String label : PromotionCardCouponHandler.couponListHeaderLabel) {
            header.add(Lists.newArrayList(label));
        }
        return header;
    }

    /**
     * @param couponTransactionFlow couponTransactionFlow
     * @param singleStore           singleStore
     * @return List
     */
    public List makeCouponTransactionFlowExcelBody(List<PromotionCardCouponTransactionFlowEntity> couponTransactionFlow,
                                                   Byte singleStore) {
        List<List<Object>> lines = new ArrayList<>();
        if (couponTransactionFlow == null) {
            return lines;
        }

        for (PromotionCardCouponTransactionFlowEntity d : couponTransactionFlow) {
            List<Object> row = new ArrayList<>();
            row.add(d.getPatientName());
            row.add(d.getPatientMobile());
            row.add(d.getChargeType());
            row.add(d.getCouponName());
            row.add(d.getUseCount());
            row.add(d.getUsedDate());
            if (singleStore != null && singleStore != 0) {
                row.add(d.getClinicName());
            }
            row.add(d.getDiscountPrice());
            row.add(d.getActualMoney());
            row.add(d.getSellerName());
            row.add(d.getOperator());
            lines.add(row);
        }
        return lines;
    }

    /**
     * @param singleStore singleStore
     * @return List
     */
    public List makeCouponTransactionFlowExcelHead(Byte singleStore) {
        List<List<String>> header = new ArrayList<>();

        for (String label : PromotionCardCouponHandler.makeCouponTransactionFlowHeaderLabel) {
            if (singleStore != null && singleStore != 0) {

                header.add(Lists.newArrayList(label));
            } else {
                if (!"消费门店".equals(label)) {
                    header.add(Lists.newArrayList(label));
                }

            }
        }
        return header;
    }


}
