package cn.abc.flink.stat.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/12 11:37
 */
@Component
@ServerEndpoint("/ws/open-error-msg")
@Slf4j
public class WebSocketService {
	private String sid;

	private static final ConcurrentHashMap<String, Session> SESSION_MAP = new ConcurrentHashMap<>();

	private Boolean isOpen = false;

	/**
	 * 连接成功
	 */
	@OnOpen
	public void onOpen(Session session, @PathParam("sid") String sid) {
		this.sid = sid;
		SESSION_MAP.put(sid, session);
		log.info("有新连接：sid：{}，sessionId：{}，当前连接数：{}", sid, session.getId(), SESSION_MAP.size());
	}

	/**
	 * 连接关闭
	 */
	@OnClose
	public void onClose(Session session) {
		SESSION_MAP.remove(this.sid);
		log.info("连接关闭，sid：{}，session id：{}！当前连接数：{}", this.sid, session.getId(), SESSION_MAP.size());
	}

	/**
	 * 收到消息
	 */
	@OnMessage
	public void onMessage(String message) {
		log.info("收到消息：{}，内容：{}", sid, message);
	}


	/**
	 * 连接错误
	 */
	@OnError
	public void onError(Session session, Throwable error) {
		log.error("{} 发生错误", session.getId(), error);
	}

	/**
	 * 群发消息
	 */
	public void sendMeasure(String message) {
		for (String sid : SESSION_MAP.keySet()) {
			Session session = SESSION_MAP.get(sid);
			try {
				session.getBasicRemote().sendText(message);
			} catch (IOException e) {
				log.error("推送消息失败：{}，内容：{}", sid, message);
			}
			log.info("推送消息：{}，内容：{}", sid, message);
		}
	}
}
