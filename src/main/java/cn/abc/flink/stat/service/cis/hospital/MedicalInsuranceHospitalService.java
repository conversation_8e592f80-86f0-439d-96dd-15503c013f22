package cn.abc.flink.stat.service.cis.hospital;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.db.cis.hologres.dao.HoloCisHospitalMapper;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.db.dao.MedicalInsuranceHospitalMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCount;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalParam;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalPatient;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary;
import cn.abc.flink.stat.service.cis.hospital.handler.MedicalInsuranceHospitalHandler;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.QueryLoneCareChargeInfoListResBody;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-03-02 16:23
 * @Deac:长护统计
 **/
@Service
public class MedicalInsuranceHospitalService {

    static final String SHEBAODB = TableUtils.getSheBaoTable();
    static final String CHARGEDB = TableUtils.getCisChargeTable();
    static final String CHARGERECORDDB = TableUtils.getCisChargeRecordTable();
    static final String DISPENSINGDB = TableUtils.getCisDispensingTable();
    static final String OUTPATIENTDB = TableUtils.getCisOutPatientTable();
    static final String CISDB = TableUtils.getCisTable();
    static final String GOODSDB = TableUtils.getCisGoodsTable();
    static final String PATIENTORDERDB = TableUtils.getCisPatientorderTable();

    private static String price[][] = {};

    static {
        //11职工,12居民,14离休(14-0表示离休除四五级，14-45表示离休45级)
        String priceinit[][] = {
                {"院护(失智)院护(失能)家护(失能)日护(失智)日护(失能)退休or在职", "50"},
                {"院护(失智)院护(失能)家护(失能)日护(失智)日护(失能)居民", "13.7"},
                {"院护(失智)院护(失能)家护(失能)日护(失智)日护(失能)离休-0,离休-45", "80"},
                {"院护(门诊慢特病)家护(门诊慢特病)日护(门诊慢特病)退休or在职", "9.59"},
                {"院护(门诊慢特病)家护(门诊慢特病)日护(门诊慢特病)居民,离休-0", "8.22"},
                {"院护(门诊慢特病)家护(门诊慢特病)日护(门诊慢特病)离休-45", "16.44"},
                {"院护(气管切开保留气管套管)家护(气管切开保留气管套管)退休or在职,居民,离休-0,离休-45", "200"},
                {"专护(过渡期)退休or在职,居民,离休-0,离休-45", "0"}};
        price = priceinit;
    }
    private final ExecutorService batchExecutor = new ThreadPoolExecutor(
            5,          // 核心线程数（始终存活）
            10,          // 最大线程数（固定5，无动态扩容）
            60L,        // 空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100) // 任务队列（超过5个任务时先入队，避免拒绝）
    );
    @Autowired
    private HoloCisHospitalMapper medicalInsuranceHospitalMapper;
    @Autowired
    private MedicalInsuranceHospitalMapper odsMapper;

    @Autowired
    private ExecutorService cacheExecutorService;

    @Autowired
    private MedicalInsuranceHospitalHandler handler;

    @Autowired
    private DimensionQuery query;

    private Logger logger = LoggerFactory.getLogger(MedicalInsuranceHospitalService.class);


    /**
     * @param
     * @param param chainId-clinicId-beginDate-endDate-patientId-chargeType-hospitalStatus-directDoctorId-offset-size-
     * @return
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalRsp
     * @throws ExecutionException
     * @throws InterruptedException
     * @throws ExecutionException   -
     * @throws InterruptedException -
     * @Description: 长护统计-患者tab
     * @Author: zs
     * @Date: 2022/8/16 09:32
     */
    public V2StatResponse getHospitalPatients(MedicalInsuranceHospitalParam param)
            throws ExecutionException, InterruptedException {
        long begin1 = System.currentTimeMillis();
        CompletableFuture<List<MedicalInsuranceHospitalPatient>> baseFuture = CompletableFuture.supplyAsync(() -> {
            return medicalInsuranceHospitalMapper.selectHospitalPatients(CISDB, CHARGEDB, param);
        }, cacheExecutorService); // 基本信息(不包含成本)
        CompletableFuture<Map<String, Employee>> employeeFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryEmployeeByChainId(param.getChainId());
        }, cacheExecutorService); //员工信息
        CompletableFuture<MedicalInsuranceHospitalCount> patientsCountFuture = CompletableFuture.supplyAsync(() -> {
            return medicalInsuranceHospitalMapper.selectHospitalPatientsCount(CISDB, param);
        }, cacheExecutorService); // 记录数
        CompletableFuture.allOf(baseFuture, employeeFuture, patientsCountFuture).join();
        MedicalInsuranceHospitalCount count = patientsCountFuture.get();
        Map<String, Employee> employeeMap = employeeFuture.get();
        List<MedicalInsuranceHospitalPatient> base = baseFuture.get();
        long end1 = System.currentTimeMillis();
        logger.info("duraTime:" + (end1 - begin1));
        // 住院单Id
        Set<String> ids = new HashSet<>();
        List<String> hospitalIds = new ArrayList<>();
        logger.info("hospitalIdsService:" + hospitalIds);
        Map<String, QueryLoneCareChargeInfoListResBody.ChargeSheetItem> settlementInfo = new HashMap<>();
        // 结算信息
        long beginSettle = System.currentTimeMillis();
        if (hospitalIds.size() != 0) {
            settlementInfo = handler.getSettlementInfo(hospitalIds, param);
        }
        long endSettle = System.currentTimeMillis();
        logger.info("settleTime:" + (endSettle - beginSettle));
        Map<String, Double> hosptitalCost = new HashMap<>();
        long beginCost = System.currentTimeMillis();
        //成本
        if (ids.size() != 0) {
            hosptitalCost = handler.getHosptitalCost( 0, param, ids);
        }
        long endCost = System.currentTimeMillis();
        logger.info("costTime:" + (endCost - beginCost));
        extracted(employeeMap, base, settlementInfo, hosptitalCost, price);
        //获取表头
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(param.getParams().getEmployeeId(),
                        HeaderTableKeyConfig.MEDICAL_HOSPITAL_PATIENT, param.getParams().getViewModeInteger(),
                        param.getParams().getNodeType(), CommonConstants.NUMBER_ZERO);
        V2StatResponse v2StatResponse = new V2StatResponse();
        v2StatResponse.setData(base);
        v2StatResponse.setHeader(headerEmployeeItems);
        StatResponseTotal total = new StatResponseTotal();
        total.setTemplate("共 %s 条数据，在院 %s 人，出院 %s 人，期间在院 %s 天，总在院 %s 天 ");
        total.setCount(new Long(count.getCnt() == null ? 0 : count.getCnt()));
        total.setData(Arrays.asList(count.getCnt() == null ? 0 : count.getCnt(), count.getInCount(), count.getOutCount(), count.getDuraHospitalDays(),
                count.getAllHospitalDays()));
        v2StatResponse.setTotal(total);
        return v2StatResponse;
    }

    /**
     * @param
     * @param employeeMap    -
     * @param base           -
     * @param settlementInfo -
     * @param hosptitalCost  -
     * @param price  -
     * @return
     * @Description: 处理base数据
     * @Author: zs
     * @Date: 2022/8/16 10:55
     */
    private void extracted(Map<String, Employee> employeeMap, List<MedicalInsuranceHospitalPatient> base,
                           Map<String, QueryLoneCareChargeInfoListResBody.ChargeSheetItem> settlementInfo,
                           Map<String, Double> hosptitalCost, String[][] price) {
        for (MedicalInsuranceHospitalPatient b : base) {
            Employee directDoctor = employeeMap.get(b.getDirectDoctorId());
            Employee registerDoctor = employeeMap.get(b.getRegisterDoctorId());
            Employee registerNurse = employeeMap.get(b.getRegisterNurseId());
            if (directDoctor != null) {
                b.setDirectDoctorName(directDoctor.getName());
            }
            if (registerDoctor != null) {
                b.setRegisterDoctorName(registerDoctor.getName());
            }
            if (registerNurse != null) {
                b.setRegisterNurseName(registerNurse.getName());
            }
            b.setPartInHospitalFee(hosptitalCost.getOrDefault(b.getId() + "partFee", 0.0));
            //处理期间成本
            b.setPartCostPrice(new BigDecimal(hosptitalCost.getOrDefault(b.getId() + "part", 0.0))
                    .setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP).doubleValue());
            if (settlementInfo != null && !settlementInfo.isEmpty()) {
                QueryLoneCareChargeInfoListResBody.ChargeSheetItem csItem = settlementInfo.get(b.getId());
                if (csItem != null) {
                    b.setTotalAmount(csItem.getTotalAmount() != null ? csItem.getTotalAmount().doubleValue() : 0.0);
                    b.setFullFundPay(csItem.getFullFundPay() != null ? csItem.getFullFundPay().doubleValue() : 0.0);
                    b.setPartFundPay(csItem.getPartFundPay() != null ? csItem.getPartFundPay().doubleValue() : 0.0);
                    b.setTotalSelfPay(csItem.getTotalSelfPay() != null ? csItem.getTotalSelfPay().doubleValue() : 0.0);
                    b.setSecondSelfPay(csItem.getSecondSelfPay() != null
                            ? csItem.getSecondSelfPay().doubleValue() : 0.0);

                    b.setTotalPeriodExpenses(csItem.getTotalPeriodExpenses() != null
                            ? csItem.getTotalPeriodExpenses().doubleValue() : 0.0);
                    b.setTotalExpenses(csItem.getTotalExpenses() != null
                            ? csItem.getTotalExpenses().doubleValue() : 0.0);
                }
            }
            String userCategory = b.getUserCategory();
            if (userCategory == null || "null".equals(userCategory) || "".equals(userCategory)) {
                b.setUserCategory("-");
            }
            //处理期间医疗服务费和总医疗服务费
            if (b.getChargeType() != null && !"".equals(b.getChargeType())
                    && b.getUserCategory() != null && !"".equals(b.getUserCategory())) {
                for (int i = 0; i < price.length; i++) {
                    String name = price[i][0];
                    Double money = new Double(price[i][1]);
                    //将人员类别前两位值截取出来
                    String user = b.getUserCategory();
                    String chargeType = b.getChargeType();
                    if (!"-".equals(user)) {
                        user = user.substring(0, CommonConstants.NUMBER_TWO);
                    }
                    if ("离休".equals(user)) {
                        //评估等级 四级失能，五级失能
                        if (b.getAssessmentLevel() != null && (b.getAssessmentLevel().contains("四级失能")
                                || b.getAssessmentLevel().contains("五级失能"))) {
                            user = user + "-45";
                        } else {
                            user = user + "-0";
                        }
                    }
                    //同时满足
                    if (name.contains(chargeType) && name.contains(user)) {
                        //期间医疗费
                        b.setDuraMedicalServiceFee(b.getDuraHospitalDays() == null
                                ? 0.0 : new BigDecimal(b.getDuraHospitalDays() * money)
                                .setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP).doubleValue());
                        //总医疗费
                        b.setAllMedicalServiceFee(b.getAllHospitalDays() == null
                                ? 0.0 : new BigDecimal(b.getAllHospitalDays() * money)
                                .setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP).doubleValue());
                        break;
                    } else {
                        b.setDuraMedicalServiceFee(0.0);
                        b.setAllMedicalServiceFee(0.0);
                    }
                }
            } else {
                b.setDuraMedicalServiceFee(0.0);
                b.setAllMedicalServiceFee(0.0);
            }
            //计算总费用和期间费用
            b.setCumulativeFee((b.getInHospitalFee() == null ? 0.0 : b.getInHospitalFee())
                    + (b.getTotalExpenses() == null ? 0.0 : b.getTotalExpenses()));
            b.setPartFee((b.getPartInHospitalFee() == null ? 0.0 : b.getPartInHospitalFee())
                    + (b.getTotalPeriodExpenses() == null ? 0.0 : b.getTotalPeriodExpenses()));
            b.setDefault();
        }
    }

    /**
     * @Description: 长护统计-医生tab
     * @param 
     * @param param chainId -clinicId -beginDate -endDate -doctorId -role -
     * @return 
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalRsp
     * @Author: zs
     * @Date: 2022/8/16 11:08
     * @throws ExecutionException -
     * @throws InterruptedException -
     */
    public V2StatResponse getHospitalDoctors(MedicalInsuranceHospitalParam param)
            throws ExecutionException, InterruptedException {

        List<Map<String, Object>> hospitalDoctors =
                handler.getHospitalDoctors(CISDB, CHARGEDB, OUTPATIENTDB, param);
        //获取表头
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(param.getParams().getEmployeeId(),
                        HeaderTableKeyConfig.MEDICAL_HOSPITAL_DOCTOR, param.getParams().getViewModeInteger(),
                        param.getParams().getNodeType(), CommonConstants.NUMBER_ZERO);
        V2StatResponse v2StatResponse = new V2StatResponse();
        v2StatResponse.setData(hospitalDoctors);
        v2StatResponse.setHeader(headerEmployeeItems);
        StatResponseTotal total = new StatResponseTotal();
        total.setCount(new Long(hospitalDoctors.size()));
        v2StatResponse.setTotal(total);
        //暂时不加汇总
        //total.setTemplate(handler.getSummary(CISDB, CHARGEDB, chainId,
        // clinicId, beginDate, endDate, doctorId, role));
        return v2StatResponse;
    }


    /**
     * @Description: 长护统计-筛选框
     * @param
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @Author: zs
     * @Date: 2022/8/16 13:49
     * @throws ExecutionException -
     * @throws InterruptedException -
     */
    public Map<String, Object> getCondition(String chainId,
                                            String clinicId,
                                            String beginDate,
                                            String endDate) throws ExecutionException, InterruptedException {

        return handler.getCondition(CISDB, chainId, clinicId, beginDate, endDate);
    }


    /**
     * @Description: 长护统计-导出
     * @param
     * @param response -
     * @param param chainId-clinicId-beginDate-endDate-patientId-chargeType-hospitalStatus-directDoctorId-doctorId-role-
     * @return
     * @Author: zs
     * @Date: 2022/8/16 14:01
     * @throws ExecutionException -
     * @throws InterruptedException -
     */
    public void export(HttpServletResponse response, MedicalInsuranceHospitalParam param)
            throws ExecutionException, InterruptedException {
        V2StatResponse hospitalPatients = getHospitalPatients(param);
        V2StatResponse hospitalDoctors = getHospitalDoctors(param);
        List patientDataList = hospitalPatients.getData();
        List doctorDataList = hospitalDoctors.getData();
        handler.export(response, param.getBeginDate(), param.getEndDate(), patientDataList, doctorDataList);
    }

    /**
     * @Description: 长护统计-异步导出
     * @param
     * @param param chainId-clinicId-beginDate-endDate-patientId-chargeType-hospitalStatus-directDoctorId-doctorId-role-
     * @return
     * @Author: zs
     * @Date: 2022/8/16 14:01
     * @throws ExecutionException -
     * @throws InterruptedException -
     */
    public List<ExcelUtils.AbcExcelSheet> asynExport(MedicalInsuranceHospitalParam param)
            throws ExecutionException, InterruptedException {
        V2StatResponse hospitalDoctors = getHospitalDoctors(param);
        V2StatResponse hospitalPatients = getHospitalPatients(param);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();

        ExcelUtils.AbcExcelSheet patientSheet = new ExcelUtils.AbcExcelSheet();
        patientSheet.setName("患者");
        patientSheet.setData(ExcelUtils.exportDataV2(hospitalPatients.getData(), hospitalPatients.getHeader()));
        patientSheet.setSheetDefinition(ExcelUtils.exportTableHeader(hospitalPatients.getHeader()));
        sheets.add(patientSheet);

        ExcelUtils.AbcExcelSheet doctorSheet = new ExcelUtils.AbcExcelSheet();
        doctorSheet.setName("医生");
        doctorSheet.setData(ExcelUtils.exportMapData(hospitalDoctors.getData(), hospitalDoctors.getHeader()));
        doctorSheet.setSheetDefinition(ExcelUtils.exportTableHeader(hospitalDoctors.getHeader()));
        sheets.add(doctorSheet);
        return sheets;
    }


    /**
     * @Description: 长护统计患者tab表头合计
     * @param
     * @param param -
     * @return
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalRsp
     * @Author: zs
     * @Date: 2023/4/7 10:05
     * @throws ExecutionException -
     * @throws InterruptedException -
     */
    public MedicalInsuranceHospitalSummary getHospitalPatientsSummary(MedicalInsuranceHospitalParam param)
            throws InterruptedException, ExecutionException {
        logger.info("长护统计-患者tab-表头合计入参{}", JSON.toJSONString(param));
        //主体费用 查询收费信息,包含外诊费用
        CompletableFuture<MedicalInsuranceHospitalSummary> medicalInsuranceHospitalSummaryF =
                CompletableFuture.supplyAsync(() -> {
            MedicalInsuranceHospitalSummary medicalInsuranceHospitalSummary =
                    medicalInsuranceHospitalMapper.selectHospitalPatientsSummy(CISDB, CHARGEDB, SHEBAODB, param);
            return medicalInsuranceHospitalSummary;
        });
        //分两次查询，第一次查询数据汇总，第二次查询全部id
        CompletableFuture<Set<String>> idsF =
                CompletableFuture.supplyAsync(() -> {
            return medicalInsuranceHospitalMapper.selectHospitalPatientsIds(CISDB, CHARGEDB, SHEBAODB, param);
        });
        CompletableFuture.allOf(medicalInsuranceHospitalSummaryF, idsF).join();
        MedicalInsuranceHospitalSummary medicalInsuranceHospitalSummary = medicalInsuranceHospitalSummaryF.get();
        Set<String> ids = idsF.get();
        if (medicalInsuranceHospitalSummary == null) {
            MedicalInsuranceHospitalSummary result = new MedicalInsuranceHospitalSummary();
            return result;
        }
        //获取成本信息
        Map<String, Double> hosptitalCost = new HashMap<>();
        if (medicalInsuranceHospitalSummary != null && ids != null && ids.size() > 0) {
            //1.外诊费用数据
            CompletableFuture<MedicalInsuranceHospitalSummary> externalDiagnosisFeature =
                    CompletableFuture.supplyAsync(() -> {
                return odsMapper.selectExternalDiagnosis(SHEBAODB, param, ids);
            });
            //待结算总金额：对码项目 长护单维度：所有欠费金额 替换原来的医疗结算总金额（已跟产品确认过）
            CompletableFuture<Double> settlementAmountFeature =
                    CompletableFuture.supplyAsync(() -> {
                return calculateBatchSettlementAmount(ids, 200);
            });
            //2.全部费用
            CompletableFuture<MedicalInsuranceHospitalSummary> inHospitalFeeFeature =
                    CompletableFuture.supplyAsync(() -> {
                        return medicalInsuranceHospitalMapper.selectTotalFee(CISDB, param, ids);
                    });
            //4.成本获取
            CompletableFuture<Map<String, Double>> hosptitalCostFeature =
                    CompletableFuture.supplyAsync(() -> {
                        try {
                            MedicalInsuranceHospitalParam costParam =
                                    JSON.parseObject(JSON.toJSONString(param), MedicalInsuranceHospitalParam.class);
                            if (medicalInsuranceHospitalSummary.getMinDate() != null) {
                                costParam.setMiniDate(medicalInsuranceHospitalSummary.getMinDate());
                            }
                            return handler.getHosptitalCost( 1, costParam, ids);
                        } catch (ExecutionException e) {
                            e.printStackTrace();
                            return new HashMap<>();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                            return new HashMap<>();
                        }
                    });
            CompletableFuture.allOf(externalDiagnosisFeature, inHospitalFeeFeature, hosptitalCostFeature, settlementAmountFeature).join();
            MedicalInsuranceHospitalSummary externalDiagnosis
                    = externalDiagnosisFeature.get() == null ? new MedicalInsuranceHospitalSummary()
                    : externalDiagnosisFeature.get();
            MedicalInsuranceHospitalSummary inHospitalFee
                    = inHospitalFeeFeature.get() == null ? new MedicalInsuranceHospitalSummary()
                    : inHospitalFeeFeature.get();
            medicalInsuranceHospitalSummary.setExternalDiagnosisFee(externalDiagnosis.getExternalDiagnosisFee());
            medicalInsuranceHospitalSummary
                    .setPartExternalDiagnosisFee(externalDiagnosis.getPartExternalDiagnosisFee());
            medicalInsuranceHospitalSummary.setInHospitalFee(inHospitalFee.getInHospitalFee());
            hosptitalCost = hosptitalCostFeature.get() == null ? new HashMap<>() : hosptitalCostFeature.get();
            Double settlementAmount = settlementAmountFeature.get();
            medicalInsuranceHospitalSummary.setPartInHospitalFee(hosptitalCost.getOrDefault("partFee", 0.00));
            //settlementAmount保留两位小数
            medicalInsuranceHospitalSummary.setSettlementTotalAmount(settlementAmount == null ? 0.00 : Math.round(settlementAmount * 100.0) / 100.0);
        }
        //处理期间总费用和总费用
        medicalInsuranceHospitalSummary.setFee(medicalInsuranceHospitalSummary.getInHospitalFee()
                + medicalInsuranceHospitalSummary.getExternalDiagnosisFee());
        medicalInsuranceHospitalSummary.setPartFee(medicalInsuranceHospitalSummary.getPartInHospitalFee()
                + medicalInsuranceHospitalSummary.getPartExternalDiagnosisFee());
        //处理成本信息
        medicalInsuranceHospitalSummary.setPartCostPrice(hosptitalCost.get("partCost") == null
                ? 0.00 : new BigDecimal(hosptitalCost.get("partCost"))
                .setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP).doubleValue());
        //处理包干率
        String complianceRate = "0.00%";
        if (medicalInsuranceHospitalSummary.getAllMedicalServiceFee() != null
                && medicalInsuranceHospitalSummary.getAllMedicalServiceFee() != null
                && medicalInsuranceHospitalSummary.getAllMedicalServiceFee() != 0.0) {
            complianceRate = String.format("%.2f", medicalInsuranceHospitalSummary.getFee()
                    / medicalInsuranceHospitalSummary.getAllMedicalServiceFee() * CommonConstants.NUMBER_HUNDRED) + "%";
        }
        medicalInsuranceHospitalSummary.setComplianceRate(complianceRate);
        medicalInsuranceHospitalSummary.setIds("");
        return medicalInsuranceHospitalSummary;
    }

    // 核心：适配Set<String>的分批查询方法（单方法封装所有逻辑）
    private Double calculateBatchSettlementAmount(Set<String> idSet, int batchSize) {
        long l = System.currentTimeMillis();
        // 1. Set转List（解决Set无索引无法拆分的问题）
        List<String> idList = new ArrayList<>(idSet);
        int totalCount = idList.size();
        if (totalCount == 0) {
            return 0.0; // 空集合直接返回0，避免无效循环
        }

        // 2. 计算总批次数（向上取整，无浮点数运算）
        int totalBatches = (totalCount + batchSize - 1) / batchSize;
        double totalAmount = 0.0;

        // 3. 存储所有批次的异步任务
        List<CompletableFuture<Double>> batchFutures = new ArrayList<>();

        // 4. 拆分批次并创建异步查询
        for (int i = 0; i < totalBatches; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, totalCount);
            List<String> batchIds = idList.subList(start, end); // 基于List拆分批次

            // 异步执行单批SQL查询（Set的ID已转为List传入Mapper）
            CompletableFuture<Double> batchFuture = CompletableFuture.supplyAsync(() ->
                    odsMapper.selectPendingSettlementAmount(
                            CHARGEDB, CHARGERECORDDB, GOODSDB, PATIENTORDERDB, SHEBAODB, batchIds
                    )
            , batchExecutor);
            batchFutures.add(batchFuture);
        }

        // 5. 等待所有批次完成并汇总结果
        for (CompletableFuture<Double> future : batchFutures) {
            try {
                totalAmount += future.get(); // 累加每批结算金额
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态（并发编程最佳实践）
                throw new RuntimeException("批次查询被中断", e);
            } catch (ExecutionException e) {
                // 抛出原始异常（避免嵌套，便于定位SQL或Mapper问题）
                throw new RuntimeException("单批结算金额查询失败", e.getCause());
            }
        }
        long l1 = System.currentTimeMillis();
        System.out.println("结算使用耗时---====-==-=-=-=-=-=-=-=：" + (l1 - l));
        return totalAmount;
    }
}
