package cn.abc.flink.stat.service.cis.revenue.charge.detail.service.impl;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.handler.HeaderHandler;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.handler.RevenueChargeDetailPharmacyHandler;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailPharmacyTotalResp;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailReqParams;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.revenue.charge.detail.service.impl
 * @Author: zs
 * @CreateTime: 2023-11-21  15:28
 * @Description: 收费明细-药店实现类
 * @Version: 1.0
 */
@Service
public class RevenueChargeDetailPharmacyServiceImpl {

    private final Logger logger = LoggerFactory.getLogger(RevenueChargeDetailPharmacyServiceImpl.class);

    @Autowired
    private HeaderHandler tableHeaderHandler;

    @Autowired
    private RevenueChargeDetailPharmacyHandler revenueChargeDetailHandler;

    /**
     * @Description: 销售明细-单据
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Author: zs
     * @Date: 2023/11/21 15:54
     */
    public V2StatResponse selectTransaction(RevenueChargeDetailReqParams params) throws ExecutionException, InterruptedException {
        V2StatResponse v2StatResponse = new V2StatResponse();
        revenueChargeDetailHandler.setParam(params);
        List<TableHeaderEmployeeItem> header = tableHeaderHandler.getPharmacyRevenueChargeDetailHeader(params, "transaction");
        Map<String, String> payModeHeaderMap = new HashMap<>();
        v2StatResponse.setData(revenueChargeDetailHandler.selectTransaction(params, payModeHeaderMap));
        HeaderHandler.setPayModeHeader(payModeHeaderMap, header);
        v2StatResponse.setHeader(header);
        v2StatResponse.setTotal(revenueChargeDetailHandler.selectTransactionTotal(params));
        return v2StatResponse;
    }

    /**
     * @Description: 销售明细-单据total
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Author: zs
     * @Date: 2023/11/21 15:54
     */
    public RevenueChargeDetailPharmacyTotalResp selectTransactionTotal(RevenueChargeDetailReqParams params) throws ExecutionException, InterruptedException {
        revenueChargeDetailHandler.setParam(params);
        return revenueChargeDetailHandler.selectPharmacyTransactionTotal(params);
    }

    /**
     * @Description: 销售明细-商品
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Author: zs
     * @Date: 2023/11/21 15:54
     */
    public V2StatResponse selectPharmacyCommodity(RevenueChargeDetailReqParams params) throws Exception {
        V2StatResponse v2StatResponse = new V2StatResponse();
        revenueChargeDetailHandler.setParam(params);
        List<TableHeaderEmployeeItem> header = tableHeaderHandler.getPharmacyRevenueChargeDetailHeader(params, "commodity");
        Map<String, String> payModeHeaderMap = new HashMap<>();
        v2StatResponse.setData(revenueChargeDetailHandler.selectPharmacyCommodity(params, payModeHeaderMap));
        HeaderHandler.setPayModeHeader(payModeHeaderMap, header);
        v2StatResponse.setHeader(header);
        v2StatResponse.setTotal(revenueChargeDetailHandler.selectPharmacyCommodityTotal(params));
        return v2StatResponse;
    }


    public List<ExcelUtils.AbcExcelSheet> export(RevenueChargeDetailReqParams params) throws Exception {
        ArrayList<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        params.setDoYouNeedAHeader(CommonConstants.NUMBER_ONE);
        params.setIsExport(CommonConstants.NUMBER_ONE);
        ExcelUtils.AbcExcelSheet transaction = new ExcelUtils.AbcExcelSheet();
        ExcelUtils.AbcExcelSheet commodity = new ExcelUtils.AbcExcelSheet();
        V2StatResponse transactionRespone = selectTransaction(params);
        V2StatResponse commodityRespone = selectPharmacyCommodity(params);
        transaction.setName("单据");
        transaction.setData(ExcelUtils.exportMapData(transactionRespone.getData(), transactionRespone.getHeader()));
        transaction.setSheetDefinition(ExcelUtils.exportTableHeader(transactionRespone.getHeader()));
        commodity.setName("商品");
        commodity.setData(ExcelUtils.exportMapData(commodityRespone.getData(), commodityRespone.getHeader()));
        commodity.setSheetDefinition(ExcelUtils.exportTableHeader(commodityRespone.getHeader()));
        sheets.add(transaction);
        sheets.add(commodity);
        return sheets;
    }
}
