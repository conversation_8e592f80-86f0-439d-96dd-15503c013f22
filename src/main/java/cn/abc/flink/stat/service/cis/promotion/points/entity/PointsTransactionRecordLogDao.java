package cn.abc.flink.stat.service.cis.promotion.points.entity;

import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2Goods;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.domain.V2Promotion;
import cn.abc.flink.stat.service.cis.handler.GoodsHandler;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;


@ApiModel("积分流水实体")
public class PointsTransactionRecordLogDao {

    @ApiModelProperty("患者id")
    private String patientName;

    @ApiModelProperty("患者手机号")
    private String patientMobile;

    @ApiModelProperty("创建时间")
    private String created;

    @ApiModelProperty("动作类型")
    private Byte actionType;

    @ApiModelProperty("动作")
    private String action;

    @ApiModelProperty("积分数")
    private Long points;

    @ApiModelProperty("剩余积分")
    private Long residuePoints;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("患者id")
    private String clinicId;

    @ApiModelProperty("患者id")
    private String clinicName;

    @ApiModelProperty("患者id")
    private String patientId;

    @ApiModelProperty("操作人id")
    private String createdBy;

    @ApiModelProperty("积分流水积分构成记录字符串")
    private String recordsStr;

    @ApiModelProperty("积分流水积分构成记录")
    private List<PatientPointsLogRecord> records;

    @ApiModelProperty("收费金额")
    private BigDecimal amount;

    @ApiModelProperty("计费金额")
    private BigDecimal billAmount;

    @ApiModelProperty("积分构成记录描述")
    private String recordsDescribe;

    public void setPatientMessage(Map<String, V2Patient> v2PatientMap) {
        if (!BeanUtil.isEmpty(v2PatientMap.get(patientId))) {
            V2Patient v2Patient = v2PatientMap.get(patientId);
            this.patientName = v2Patient.getName();
            this.patientMobile = v2Patient.getMobile();
        }
    }

    public void setClinicMessage(Map<String, Organ> organMap) {
        if (!StrUtil.isBlank(clinicId)) {
            Organ o = organMap.get(clinicId);
            if (o != null) {
                this.clinicName = o.getShortName() == null || o.getShortName().equals("") ? o.getName()
                        : o.getShortName();
            } else {
                this.clinicName = "-";
            }
        } else {
            this.clinicName = "-";
        }
    }

    public void setEmployeeMessage(Map<String, Employee> employeeMap) {
        if (!StrUtil.isBlank(createdBy)) {
            Employee e = employeeMap.get(createdBy);
            if (e != null) {
                this.operator = e.getName();
            } else {
                this.operator = "-";
            }
        } else {
            this.operator = "-";
        }

    }

    /**
     * 设置积分构成记录信息
     *
     * @param promotionMap 活动信息map
     * @param goodsMap     项目信息map
     */
    public void setRecordsMessage(Map<Long, V2Promotion> promotionMap, Map<String, V2Goods> goodsMap) {
        if (!CollUtil.isEmpty(this.records)) {
            for (PatientPointsLogRecord record : this.records) {
                if (!BeanUtil.isEmpty(promotionMap.get(record.getPromotionId()))) {
                    record.setPromotionName(promotionMap.get(record.getPromotionId()).getName());
                }
                if (!BeanUtil.isEmpty(goodsMap.get(record.getGoodsId()))) {
                    record.setGoodsName(GoodsHandler.handleGoodsName(goodsMap.get(record.getGoodsId()).getName(), goodsMap.get(record.getGoodsId()).getMedicine_cadn()));
                }
                if (StrUtil.isBlank(record.getPromotionName())) {
                    record.setPromotionName(CommonConstants.WHIPPTREE);
                }
                if (StrUtil.isBlank(record.getGoodsName())) {
                    record.setGoodsName(CommonConstants.WHIPPTREE);
                }
            }
            if (this.billAmount != null) {
                this.recordsDescribe = "本次收费使用会员余额支付了" + this.billAmount.setScale(2, RoundingMode.HALF_UP).toPlainString()
                        + ",其中赠金支付的" + this.amount.subtract(this.billAmount).setScale(2, RoundingMode.HALF_UP).toPlainString() + "不积分";
            }

        }
    }

    public String getPatientName() {
        return this.patientName;
    }

    public String getPatientMobile() {
        return this.patientMobile;
    }

    public String getCreated() {
        return this.created;
    }

    public Byte getActionType() {
        return this.actionType;
    }

    public String getAction() {
        return this.action;
    }

    public Long getPoints() {
        return this.points;
    }

    public Long getResiduePoints() {
        return this.residuePoints;
    }

    public String getRemark() {
        return this.remark;
    }

    public String getOperator() {
        return this.operator;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public String getRecordsStr() {
        return this.recordsStr;
    }

    public List<PatientPointsLogRecord> getRecords() {
        return this.records;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public BigDecimal getBillAmount() {
        return this.billAmount;
    }

    public String getRecordsDescribe() {
        return this.recordsDescribe;
    }


    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setPatientMobile(String patientMobile) {
        this.patientMobile = patientMobile;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public void setActionType(Byte actionType) {
        this.actionType = actionType;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setPoints(Long points) {
        this.points = points;
    }

    public void setResiduePoints(Long residuePoints) {
        this.residuePoints = residuePoints;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setRecordsStr(String recordsStr) {
        this.recordsStr = recordsStr;
    }

    public void setRecords(List<PatientPointsLogRecord> records) {
        this.records = records;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public void setRecordsDescribe(String recordsDescribe) {
        this.recordsDescribe = recordsDescribe;
    }

}
