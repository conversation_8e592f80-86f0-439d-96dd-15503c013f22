package cn.abc.flink.stat.service.cis.promotion.activity;

import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.request.params.MarketingActivitiesParam;
import cn.abc.flink.stat.common.request.params.MemberDayDtoParam;
import cn.abc.flink.stat.common.request.params.MemberDayParam;
import cn.abc.flink.stat.common.request.params.PromotionActivityParam;
import cn.abc.flink.stat.common.response.StatResponseKeyDataItem;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresPromotionMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.V2Goods;
import cn.abc.flink.stat.dimension.domain.V2Promotion;
import cn.abc.flink.stat.dimension.domain.V2PromotionDiscount;
import cn.abc.flink.stat.dimension.domain.V2PromotionMain;
import cn.abc.flink.stat.service.cis.promotion.activity.entity.MemberDayEntity;
import cn.abc.flink.stat.service.cis.promotion.activity.entity.PromotionActivityEntity;
import cn.abc.flink.stat.service.cis.promotion.activity.entity.PromotionGeneralActivityEntity;
import cn.abc.flink.stat.service.cis.promotion.activity.entity.ProportionEntity;
import cn.abc.flink.stat.service.cis.promotion.activity.entity.RatioEntity;
import cn.abc.flink.stat.service.cis.promotion.activity.handler.PromotionActivityHandler;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.CisServiceResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

/**
 * @description: 营销活动
 * @author: dy
 * @create: 2021-07-21 14:40
 */

@Service
public class PromotionActivityService {
    @Resource
    private HologresPromotionMapper hologresPromotionMapper;

    @Resource
    private StoreUtils storeUtils;

    @Autowired
    private ExecutorService cacheExecutorService;

    @Autowired
    private DimensionQuery query;

    /**
     * @Description: 活动params
     * @param
     * @param params: -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Author: zs
     * @Date: 2024/4/30 11:28
     */
    public V2StatResponse selectMarketingActivities(MarketingActivitiesParam params) throws ExecutionException, InterruptedException{
        V2StatResponse response = new V2StatResponse();
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.STAT_CIS_MARKETING_ACTIVITY,
                        params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 1);
        response.setHeader(headerEmployeeItems);
        List<PromotionActivityEntity> list = hologresPromotionMapper.selectMarketingActivitiesList(TableUtils.getCisTable(), params);
        if (list == null || list.size() == 0) {
            PromotionActivityEntity keyData = new PromotionActivityEntity();
            keyData.set();
            List<StatResponseKeyDataItem> statResponseKeyDataItems = Arrays.asList(
            new StatResponseKeyDataItem("消费人次", keyData.getConsumptionPersonCount().toString()),
            new StatResponseKeyDataItem("销售额", keyData.getActivityConsumptionAmount().toString()),
            new StatResponseKeyDataItem("客单价", keyData.getCustomerOrder().toString()),
            new StatResponseKeyDataItem("平均毛利率", keyData.getGrossProfit()));
            response.setKeyData(statResponseKeyDataItems);
            return response;
        }
        Long count = hologresPromotionMapper.selectMarketingActivitiesCount(TableUtils.getCisTable(), params);
        Set<String> goodsIds = new HashSet<>();
        Set<Long> activityMainIds = new HashSet<>();
        Set<Long> activityIds = new HashSet<>();
        list.forEach(x -> {
            goodsIds.add(x.getProductId());
            activityMainIds.add(x.getActivityId());
            activityIds.add(x.getPromotionId());
        });
        CompletableFuture<Map<String, V2Goods>> goodsF = CompletableFuture.supplyAsync(() -> {
            return query.queryProducts(params.getChainId(), goodsIds);
        }, cacheExecutorService);
        CompletableFuture<Map<String, V2PromotionMain>> promotionMainF = CompletableFuture.supplyAsync(() -> {
            return query.queryPromotionMain(params.getChainId());
        }, cacheExecutorService);
        CompletableFuture.allOf(goodsF, promotionMainF).join();
        Map<String, V2Goods> v2GoodsMap = goodsF.get();
        Map<String, V2PromotionMain> promotionMain = promotionMainF.get();
        //获取蓝色区域合计
        PromotionActivityEntity keyData = hologresPromotionMapper.selectMarketingActivitiesKeyData(TableUtils.getCisTable(), params);
        Integer person = hologresPromotionMapper.selectMarketingActivitiesKeyCount(TableUtils.getCisTable(), params);
        keyData.setConsumptionPersonCount(person);
        for (PromotionActivityEntity dto:list) {
            dto.set();
            dto.setGoods(v2GoodsMap);
            dto.setPromotionName(promotionMain);
        }
        //获取表头
        response.setData(list);
        StatResponseTotal total = new StatResponseTotal();
        total.setCount(count);
        keyData.set();
        List<StatResponseKeyDataItem> statResponseKeyDataItems = Arrays.asList(
                new StatResponseKeyDataItem("消费人次", keyData.getConsumptionPersonCount().toString()),
                new StatResponseKeyDataItem("销售额", keyData.getActivityConsumptionAmount().toString()),
                new StatResponseKeyDataItem("客单价", keyData.getCustomerOrder().toString()),
                new StatResponseKeyDataItem("平均毛利率", keyData.getGrossProfit()));
        response.setTotal(total);
        response.setKeyData(statResponseKeyDataItems);
        return response;
    }

    /**
     * @Description: 营销概况-折扣/特价/买赠
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Author: zs
     * @Date: 2024/5/8 09:20
     */
    public PromotionGeneralActivityEntity selectGeneralActivities(MarketingActivitiesParam params) throws ExecutionException, InterruptedException{

        CompletableFuture<PromotionGeneralActivityEntity> sumF = CompletableFuture.supplyAsync(() -> {
            return hologresPromotionMapper.selectGeneralActivitiesSum(TableUtils.getCisTable(), params);
        }, cacheExecutorService);
        CompletableFuture<List<ProportionEntity>> listF = CompletableFuture.supplyAsync(() -> {
            return hologresPromotionMapper.selectGeneralActivitiesList(TableUtils.getCisTable(), params);
        }, cacheExecutorService);
        CompletableFuture<Map<Long, V2Promotion>> promotionF = CompletableFuture.supplyAsync(() -> {
            return query.queryPromotion(params.getChainId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, V2PromotionDiscount>> discountF = CompletableFuture.supplyAsync(() -> {
            return query.queryPromotionDiscount(params.getChainId());
        }, cacheExecutorService);
        CompletableFuture.allOf(sumF, listF, promotionF, discountF).join();
        PromotionGeneralActivityEntity sum = sumF.get();
        List<ProportionEntity> list = listF.get();
        Map<Long, V2Promotion> promotion = promotionF.get();
        Map<String, V2PromotionDiscount> discount = discountF.get();
        if (sum != null) {
            sum.setRatio();
        }
        if (list != null && sum != null) {
            list.forEach(x -> {
                x.setRatio();
                x.setPromotionName(promotion, discount);
                RatioEntity amount = PromotionActivityHandler.setProportion(x, sum, 0);
                RatioEntity count = PromotionActivityHandler.setProportion(x, sum, 1);
                RatioEntity ratio = PromotionActivityHandler.setProportion(x, sum, 2);
                sum.getConsumptionAmountProportion().add(amount);
                sum.getConsumersCountProportion().add(count);
                sum.getCustomerUnitPriceProportion().add(ratio);
            });
        }
        return sum;
    }

    /**
     * @Description: 会员日
     * @param
     * @param params -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2024/5/8 19:40
     */
    public CisServiceResponse selectMemberDay(MemberDayParam params) {
        if (params == null) {
            throw new RuntimeException("必要参数不能为null");
        }
        if (params.getMemberDays() == null) {
            params.setMemberDays(new ArrayList<>());
        }
        if (params.getNotMemberDates() == null) {
            params.setNotMemberDates(new ArrayList<>());
        }
        MemberDayEntity amount = new MemberDayEntity();
        MemberDayEntity count = new MemberDayEntity();
        if (params.getNotMemberDates() != null && params.getNotMemberDates().size() > 0) {
            amount = hologresPromotionMapper.selectNotMemberDay(TableUtils.getCisTable(), params);
            count = hologresPromotionMapper.selectNotMemberDayCount(TableUtils.getCisTable(), params);
        }
        MemberDayEntity notMember = PromotionActivityHandler.setMemberDayEntity(amount, count);
        List<Map<String, Object>> reslut = new ArrayList<>();
        //前端将begin和end时间传过来做ds
        for (MemberDayDtoParam param:params.getMemberDays()) {
            Map<String, Object> map = new HashMap<>();
            if (param.getMemberDates() != null && param.getMemberDates().size() > 0) {
                MemberDayEntity member = hologresPromotionMapper.selectMemberDay(TableUtils.getCisTable(), param, params);
                if (member != null) {
                    member.setRatio();
                }
                if (notMember != null) {
                    notMember.setRatio();
                }
                map.put("member", member);
            }
            map.put("notMember", notMember);
            map.put("activityId", param.getActivityId());
            reslut.add(map);
        }
        return new CisServiceResponse(reslut);
    }

    /**
     * @Description: 会员日list
     * @param
     * @param params -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2024/5/8 20:15
     */
    public CisServiceResponse selectMemberDayList(MemberDayParam params) {
        if (params == null || params.getMemberDays() == null) {
            throw new RuntimeException("必要参数不能为null");
        }
        List<Map<String, Object>> reslut = new ArrayList<>();
        //前端将begin和end时间传过来做ds
        for (MemberDayDtoParam param:params.getMemberDays()) {
            Map<String, Object> map = new HashMap<>();
            List<String> activityDates = param.getActivityDates();
            if (activityDates == null || activityDates.size() == 0) {
                activityDates = new ArrayList<>();
                continue;
            }
            //将时间list排序取最大和最小值
            activityDates.sort(Comparator.comparing(String::toString));
            params.setBeginDate(activityDates.get(0));
            params.setEndDate(activityDates.get(activityDates.size() - 1));
            List<MemberDayEntity> member = hologresPromotionMapper.selectMemberDayList(TableUtils.getCisTable(), param, params);
            if (member != null && member.size() > 0) {
                List<String> memberDates = new ArrayList<>();
                for (MemberDayEntity entity:member) {
                    entity.setRatio();
                    memberDates.add(entity.getDate());
                }
                for (String date:activityDates) {
                    if (!memberDates.contains(date)) {
                        MemberDayEntity entity = new MemberDayEntity();
                        entity.setDate(date);
                        member.add(entity);
                    }
                }
                //排序
                member.sort(Comparator.comparing(MemberDayEntity::getDate));
            } else {
                member = new ArrayList<>();
                for (String date:activityDates) {
                    MemberDayEntity entity = new MemberDayEntity();
                    entity.setDate(date);
                    member.add(entity);
                }
            }
            map.put("list", member);
            map.put("activityId", param.getActivityId());
            reslut.add(map);
        }
        return new CisServiceResponse(reslut);
    }

    /**
     * @Description: 促销活动查询
     * @param
     * @param params -
     * @return
     * @return cn.abcyun.cis.commons.CisServiceResponse
     * @Author: zs
     * @Date: 2024/5/8 20:29
     */
    public CisServiceResponse selectPromotionActivity(PromotionActivityParam params) {
        if (params.getPromotionIds() == null || params.getPromotionIds().size() == 0) {
            return new CisServiceResponse(new ArrayList<>());
        }
        Map<String, PromotionActivityParam.PromotionActivityDtoParam> paramHashMap = new HashMap<>();
        if (params.getPromotionActivityParams() != null && params.getPromotionActivityParams().size() > 0) {
            List<PromotionActivityParam.PromotionActivityDtoParam> dto = params.getPromotionActivityParams();
            for (PromotionActivityParam.PromotionActivityDtoParam param:dto) {
                paramHashMap.put(param.getPromotionId(), param);
            }
        }
        List<MemberDayEntity> reslut = new ArrayList<>();
        List<MemberDayEntity> member = hologresPromotionMapper.selectPromotionActivity(TableUtils.getCisTable(), params);
        List<MemberDayEntity> today = hologresPromotionMapper.selectPromotionActivityToDay(TableUtils.getCisTable(), params);
        Map<String, List<MemberDayEntity>> storage = new HashMap<>();
        if (member != null && member.size() > 0) {
            PromotionActivityHandler.setToDay(member,storage);
        }
        if (today != null && today.size() > 0) {
            PromotionActivityHandler.setToDay(today,storage);
        }
        if (storage.size() > 0) {
            reslut = PromotionActivityHandler.setMap(storage);
        }
        if (reslut != null && reslut.size() > 0) {
            for (MemberDayEntity entity:reslut) {
                entity.setRatio();
//                PromotionActivityParam.PromotionActivityDtoParam param = paramHashMap.get(entity.getPromotionId());
//                if (param != null) {
//                    entity.setBase(param);
//                }
            }
        }
        return new CisServiceResponse(reslut);
    }
}
