package cn.abc.flink.stat.service.cis.achievement.execute.domain;


import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/16 5:33 下午
 * @modified <PERSON><PERSON><PERSON>
 */
@NoArgsConstructor
public class ExecuteDetailTotal {
    private Integer count;
    private BigDecimal totalCount;
    private BigDecimal totalAmount;
    private BigDecimal totalActualAmount;
    private Integer executeItemCount;

    public Integer getCount() {
        return this.count;
    }

    public BigDecimal getTotalCount() {
        return this.totalCount;
    }

    public BigDecimal getTotalAmount() {
        return this.totalAmount;
    }

    public BigDecimal getTotalActualAmount() {
        return this.totalActualAmount;
    }

    public Integer getExecuteItemCount() {
        return this.executeItemCount;
    }


    public void setCount(Integer count) {
        this.count = count;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public void setTotalActualAmount(BigDecimal totalActualAmount) {
        this.totalActualAmount = totalActualAmount;
    }

    public void setExecuteItemCount(Integer executeItemCount) {
        this.executeItemCount = executeItemCount;
    }

}
