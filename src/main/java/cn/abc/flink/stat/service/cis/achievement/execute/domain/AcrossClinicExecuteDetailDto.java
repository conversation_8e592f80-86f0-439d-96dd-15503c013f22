package cn.abc.flink.stat.service.cis.achievement.execute.domain;

import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.domain.V2Patientorder;
import cn.abc.flink.stat.service.cis.handler.PatientHandler;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.util.Map;

import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_NINETEEN;

/**
 * @description;
 * @author;
 * @create;
 */
public class AcrossClinicExecuteDetailDto {
    private String chargeSheetCreated;
    private String clinicId;
    private BigDecimal countNum;
    private String createClinicName;
    private String createdBy;
    private String createdByName;
    private String executeClinicId;
    private String executeClinicName;
    private String executeCreated;
    private String executeItemName;
    private String executorId;
    private String executorNames;
    private String goodsId;
    private String goodsTypeId;
    private String goodsTypeName;
    private String patientName;
    private Long recordItemId;
    private String sellerId;
    private String sellerName;
    private BigDecimal unitPrice;

    private BigDecimal commissionAmount;
    private BigDecimal deductPrice;

    /**
     * 所属套餐
     */
    private String belongToComposeName;

    /**
     * 患者id
     */
    private String patientId;
    private String patientOrderId;

    /**
     * 患者性别
     */
    private String patientSex;
    /**
     * 患者档案号
     */
    private String patientSn;

    /**
     * 患者年龄
     */
    private String patientAge;

    /**
     * 患者手机号
     */
    private String patientMobile;

    /**
     * @param dao -
     * @return -
     */
    public AcrossClinicExecuteDetailDto pretty(AcrossClinicExecuteDetailBaseDao dao) {
        this.setChargeSheetCreated(dao.getSheetCreated().substring(0, NUMBER_NINETEEN));
        this.setClinicId(dao.getSheetClinicId());
        this.setExecuteClinicId(dao.getExecuteClinicId());
        this.setExecutorId(dao.getExecutorIds());
        this.setExecuteCreated(dao.getCreated().substring(0, NUMBER_NINETEEN));
        this.setGoodsId(dao.getGoodsId());
        this.setGoodsTypeId(dao.getClassifyLevel1());
        this.setCountNum(dao.getCount());
        this.setCreatedBy(dao.getCreatedBy());
        this.setUnitPrice(dao.getReceivedFee());
        this.setSellerId(dao.getEmployeeId());
        return this;
    }

    /**
     * 设置患者基本信息
     *
     * @param patientMap    -
     * @param enablePatient 是否展示患者手机号
     */
    public void setPatient(Map<String, V2Patient> patientMap, Boolean enablePatient, Map<String, V2Patientorder> patientorderMap) {
        V2Patient v2Patient = patientMap.get(patientId);
        V2Patientorder v2Patientorder = patientorderMap.get(patientOrderId);
        if (!BeanUtil.isEmpty(v2Patient)) {
            this.patientName = PatientHandler.handlePatientNameById(patientMap, this.patientId);
            this.patientSex = v2Patient.getSex();
            this.patientSn = (v2Patient.getSn() == null || v2Patient.getSn().isEmpty()) ? "-" : v2Patient.getSn();
            if (enablePatient) {
                if (!StrUtil.isBlank(v2Patient.getMobile())) {
                    this.patientMobile = v2Patient.getMobile();
                } else {
                    this.patientMobile = "-";
                }
            } else {
                this.patientMobile = "-";
            }
            this.patientName = v2Patient.getName();
            if (!StrUtil.isBlank(v2Patient.getBirthday())
                    && v2Patient.getBirthday().length() > CommonConstants.NUMBER_EIGHT) {
                this.patientAge = PatientHandler.getBirthdayByV2Patient(v2Patient, TimeUtils.selectLockTime(v2Patientorder, executeCreated));
            }
        } else {
            this.patientSex = "-";
            this.patientMobile = "-";
            this.patientName = "-";
            this.patientAge = "-";
            this.patientSn = "-";
        }
    }

    public String getChargeSheetCreated() {
        return this.chargeSheetCreated;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public BigDecimal getCountNum() {
        return this.countNum;
    }

    public String getCreateClinicName() {
        return this.createClinicName;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public String getCreatedByName() {
        return this.createdByName;
    }

    public String getExecuteClinicId() {
        return this.executeClinicId;
    }

    public String getExecuteClinicName() {
        return this.executeClinicName;
    }

    public String getExecuteCreated() {
        return this.executeCreated;
    }

    public String getExecuteItemName() {
        return this.executeItemName;
    }

    public String getExecutorId() {
        return this.executorId;
    }

    public String getExecutorNames() {
        return this.executorNames;
    }

    public String getGoodsId() {
        return this.goodsId;
    }

    public String getGoodsTypeId() {
        return this.goodsTypeId;
    }

    public String getGoodsTypeName() {
        return this.goodsTypeName;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public Long getRecordItemId() {
        return this.recordItemId;
    }

    public String getSellerId() {
        return this.sellerId;
    }

    public String getSellerName() {
        return this.sellerName;
    }

    public BigDecimal getUnitPrice() {
        return this.unitPrice;
    }

    public BigDecimal getCommissionAmount() {
        return this.commissionAmount;
    }

    public BigDecimal getDeductPrice() {
        return this.deductPrice;
    }

    public String getBelongToComposeName() {
        return this.belongToComposeName;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getPatientSex() {
        return this.patientSex;
    }

    public String getPatientAge() {
        return this.patientAge;
    }

    public String getPatientMobile() {
        return this.patientMobile;
    }


    public void setChargeSheetCreated(String chargeSheetCreated) {
        this.chargeSheetCreated = chargeSheetCreated;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setCountNum(BigDecimal countNum) {
        this.countNum = countNum;
    }

    public void setCreateClinicName(String createClinicName) {
        this.createClinicName = createClinicName;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    public void setExecuteClinicId(String executeClinicId) {
        this.executeClinicId = executeClinicId;
    }

    public void setExecuteClinicName(String executeClinicName) {
        this.executeClinicName = executeClinicName;
    }

    public void setExecuteCreated(String executeCreated) {
        this.executeCreated = executeCreated;
    }

    public void setExecuteItemName(String executeItemName) {
        this.executeItemName = executeItemName;
    }

    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }

    public void setExecutorNames(String executorNames) {
        this.executorNames = executorNames;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsTypeId(String goodsTypeId) {
        this.goodsTypeId = goodsTypeId;
    }

    public void setGoodsTypeName(String goodsTypeName) {
        this.goodsTypeName = goodsTypeName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setRecordItemId(Long recordItemId) {
        this.recordItemId = recordItemId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public void setDeductPrice(BigDecimal deductPrice) {
        this.deductPrice = deductPrice;
    }

    public void setBelongToComposeName(String belongToComposeName) {
        this.belongToComposeName = belongToComposeName;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setPatientSex(String patientSex) {
        this.patientSex = patientSex;
    }

    public void setPatientAge(String patientAge) {
        this.patientAge = patientAge;
    }

    public void setPatientMobile(String patientMobile) {
        this.patientMobile = patientMobile;
    }

    public String getPatientOrderId() {
        return patientOrderId;
    }

    public void setPatientOrderId(String patientOrderId) {
        this.patientOrderId = patientOrderId;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }
}
