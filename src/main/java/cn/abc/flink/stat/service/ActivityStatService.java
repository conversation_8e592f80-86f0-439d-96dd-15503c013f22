package cn.abc.flink.stat.service;

import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.db.dao.MysqlMapper;
import cn.abc.flink.stat.service.es.medicine.domain.*;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ActivityStatService {

    @Autowired
    private MysqlMapper mysqlMapper;

    public StatMarketingActivityItem getActivityResult(String id) {
        return mysqlMapper.selectActivityResult(TableUtils.getCisChargeTable(), id);
    }

    public StatMarketingCouponItem getCouponResult(String id) {
        return mysqlMapper.selectCouponResult(TableUtils.getCisChargeTable(), id);
    }

    public List<StatMarketingCouponItem> getCouponResults(List<String> ids) {
        List<StatMarketingCouponItem> list = new ArrayList<>();
        List<ChargeCoupon> chargeCouponList = mysqlMapper.selectCouponInfo(TableUtils.getCisChargeTable(), ids);
        Map<String, List<ChargeCoupon>> map = chargeCouponList.stream().collect(Collectors.groupingBy(ChargeCoupon::getPromotionId));
        for (Map.Entry<String, List<ChargeCoupon>> entry : map.entrySet()) {
            Double useCouponAmount = 0.0;
            Double discountAmount = 0.0;
            Double refundFee = 0.0;
            Set<String> patientSet = new HashSet<>();
            Set<String> orderSet = new HashSet<>();

            for (ChargeCoupon item : entry.getValue()) {
                if (item != null && item.getStatus() != null && item.getStatus() != 0 && item.getStatus() != 4) {
                    discountAmount += item.getDiscountPrice();
                    useCouponAmount += item.getReceivedFee();
                    refundFee += item.getRefundFee();
                    patientSet.add(item.getChargeId());
                    orderSet.add(item.getPatientOrderId());
                }
            }
            StatMarketingCouponItem item = new StatMarketingCouponItem();
            double costBenefitRatio = discountAmount == 0 ? 0 : useCouponAmount / Math.abs(discountAmount);
            double unitPrice = patientSet.size() == 0 ? 0 : useCouponAmount / patientSet.size();
            item.setCouponId(entry.getKey());
            item.setDiscountAmount(Math.abs(discountAmount));
            item.setUseCouponAmount(useCouponAmount + refundFee);
            item.setOrderNum(orderSet.size());
            item.setCostBenefitRatio(costBenefitRatio);
            item.setUnitPrice(unitPrice);
            list.add(item);
        }
        return list;

    }

    public List<StatMarketingActivityItem> getActivityResults(List<String> ids) {
        List<StatMarketingActivityItem> list = new ArrayList<>();
        List<ChargeActivity> chargeActivityList = mysqlMapper.selectActivityInfo(TableUtils.getCisChargeTable(), ids);
        Map<String, List<ChargeActivity>> map = chargeActivityList.stream().collect(Collectors.groupingBy(ChargeActivity::getPromotionId));
        for (Map.Entry<String, List<ChargeActivity>> entry : map.entrySet()) {
            Double receiveAmount = 0.0;
            Double refundFee = 0.0;
            int count = 0;
            Set<String> patientSet = new HashSet<>();
            Set<String> orderSet = new HashSet<>();
            for (ChargeActivity item : entry.getValue()) {
                if (item != null && item.getStatus() != 0 && item.getStatus() != 4) {
                    receiveAmount += item.getReceivedFee();
                    refundFee += item.getRefundFee();
                    if (!item.getPatientId().equals("00000000000000000000000000000000")) {
                        patientSet.add(item.getPatientId());
                    } else {
                        count += 1;
                        patientSet.add(Integer.toString(count));
                    }
                    orderSet.add(item.getPatientOrderId());
                }
            }
            StatMarketingActivityItem item = new StatMarketingActivityItem();
            item.setActivityId(entry.getKey());
            item.setAmount(receiveAmount + refundFee);
            item.setCustomerNum(patientSet.size());
            item.setOrderNum(orderSet.size());
            list.add(item);
        }
        return list;


    }


    public StatMarketingActivityItem getAcResult(String id) {
        List<StatActivity> ac = mysqlMapper.selectAcResult(TableUtils.getCisChargeTable(), id);

        Gson gson = new Gson();
        Double receiveAmount = 0.0;
        Double refundFee = 0.0;
        Set<String> patientSet = new HashSet<>();
        Set<String> orderSet = new HashSet<>();
        Integer count = 0;

        for (StatActivity statActivity : ac) {

            PromotionAcJson promotionJ = gson.fromJson(statActivity.getGiftPromotionInfo(), PromotionAcJson.class);
            if (promotionJ != null) {
                List<ActivityPromotions> acPromotions = promotionJ.getPromotions();
                for (ActivityPromotions promotions : acPromotions) {
                    if (id.equals(promotions.getId()) && promotions.isChecked() && statActivity.getStatus() != 0 && statActivity.getStatus() != 4) {
                        receiveAmount += statActivity.getReceivedFee();
                        refundFee += statActivity.getRefundFee();
                        if (!statActivity.getPatientId().equals("00000000000000000000000000000000")) {
                            patientSet.add(statActivity.getPatientId());
                        } else {
                            count += 1;
                            patientSet.add(count.toString());
                        }

                        orderSet.add(statActivity.getPatientOrderId());
                    }
                }
            }

        }
        StatMarketingActivityItem item = new StatMarketingActivityItem();
        item.setActivityId(id);
        item.setAmount(receiveAmount + refundFee);
        item.setCustomerNum(patientSet.size());
        item.setOrderNum(orderSet.size());

        return item;
    }

    public StatMarketingCouponItem getCpResult(String id) {
        List<StatCoupon> cp = mysqlMapper.selectCpResult(TableUtils.getCisChargeTable(), id);

        Gson gson = new Gson();
        Double useCouponAmount = 0.0;
        Double discountAmount = 0.0;
        Double refundFee = 0.0;
        Double costBenefitRatio = 0.0;
        Double unitPrice = 0.0;
        Set<String> patientSet = new HashSet<>();
        Set<String> orderSet = new HashSet<>();

        for (StatCoupon statCoupon : cp) {

            PromotionCpJson promotionJ = gson.fromJson(statCoupon.getCouponPromotionInfo(), PromotionCpJson.class);
            if (promotionJ != null) {
                for (CouponPromotions promotions : promotionJ.getPromotions()) {
                    if (id.equals(promotions.getId()) && promotions.isChecked() && statCoupon.getStatus() != 0 && statCoupon.getStatus() != 4) {
                        discountAmount += promotions.getDiscountPrice();
                        useCouponAmount += statCoupon.getReceivedFee();
                        refundFee += statCoupon.getRefundFee();
                        patientSet.add(statCoupon.getChargeId());
                        orderSet.add(statCoupon.getPatientOrderId());
                    }
                }
            }
        }
        StatMarketingCouponItem item = new StatMarketingCouponItem();
        costBenefitRatio = discountAmount == 0 ? 0 : useCouponAmount / Math.abs(discountAmount);
        unitPrice = patientSet.size() == 0 ? 0 : useCouponAmount / patientSet.size();


        item.setCouponId(id);
        item.setDiscountAmount(Math.abs(discountAmount));
        item.setUseCouponAmount(useCouponAmount + refundFee);
        item.setOrderNum(orderSet.size());
        item.setCostBenefitRatio(costBenefitRatio);
        item.setUnitPrice(unitPrice);

        return item;
    }


}
