package cn.abc.flink.stat.service.his.achievement.cost.domain;

import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/19 3:46 下午
 */
public class HisRevenueCostReportPayModeRes {
    private List<TableHeaderEmployeeItem> header;
    private List data;
    private Object summary;
    private Object total;

    public void init() {
        HisRevenueCostReportPayModeDao res = new HisRevenueCostReportPayModeDao();
        this.setTotal(new HisRevenueCostReportPayModeTotalRes(0, 0));
        this.setData(res.initData());
    }

    public List<TableHeaderEmployeeItem> getHeader() {
        return this.header;
    }

    public List getData() {
        return this.data;
    }

    public Object getSummary() {
        return this.summary;
    }

    public Object getTotal() {
        return this.total;
    }


    public void setHeader(List<TableHeaderEmployeeItem> header) {
        this.header = header;
    }

    public void setData(List data) {
        this.data = data;
    }

    public void setSummary(Object summary) {
        this.summary = summary;
    }

    public void setTotal(Object total) {
        this.total = total;
    }

}
