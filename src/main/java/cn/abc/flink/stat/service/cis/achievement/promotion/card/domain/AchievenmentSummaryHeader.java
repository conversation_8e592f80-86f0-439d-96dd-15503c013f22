package cn.abc.flink.stat.service.cis.achievement.promotion.card.domain;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2021-07-21 14:03
 **/
public class AchievenmentSummaryHeader {

    private String label; // 渲染字段名
    private String prop; // 协议中字段名
    private String align; // 文本位置 ： center->居中
    private Integer width; //单元格宽度
    private String groupBy; // 营销卡项业绩汇总时使用 聚合字段
    private String type;

    public AchievenmentSummaryHeader(String label, String prop, String align, Integer width, String groupBy, String type) {
        this.label = label;
        this.prop = prop;
        this.align = align;
        this.width = width;
        this.groupBy = groupBy;
        this.type = type;
    }

    public String getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(String groupBy) {
        this.groupBy = groupBy;
    }

    public AchievenmentSummaryHeader(String label, String prop, String align, Integer width) {
        this.label = label;
        this.prop = prop;
        this.align = align;
        this.width = width;
    }

    public AchievenmentSummaryHeader(String label, String prop, String align, Integer width, String groupBy) {
        this.label = label;
        this.prop = prop;
        this.align = align;
        this.width = width;
        this.groupBy = groupBy;
    }

    public AchievenmentSummaryHeader() {
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getProp() {
        return prop;
    }

    public void setProp(String prop) {
        this.prop = prop;
    }

    public String getAlign() {
        return align;
    }

    public void setAlign(String align) {
        this.align = align;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }


}
