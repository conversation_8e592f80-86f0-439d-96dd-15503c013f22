//package cn.abc.flink.stat.service.es.test;
//
//import cn.abc.flink.stat.service.es.achievement.entity.AchievementProject;
//import cn.abc.flink.stat.service.es.achievement.entity.DispensingCost;
//import cn.abc.flink.stat.service.es.achievement.entity.OutpatientEntity;
//import cn.abc.flink.stat.service.es.achievement.pojo.AchievementProjectPojo;
//import cn.hutool.core.date.DateField;
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import com.alibaba.fastjson.JSON;
//import org.apache.commons.lang.StringUtils;
//import org.apache.lucene.search.join.ScoreMode;
//import org.apache.poi.ss.formula.functions.Na;
//import org.elasticsearch.action.index.IndexRequest;
//import org.elasticsearch.action.search.*;
//import org.elasticsearch.client.Client;
//import org.elasticsearch.client.RequestOptions;
//import org.elasticsearch.client.transport.TransportClient;
//import org.elasticsearch.common.settings.Settings;
//import org.elasticsearch.common.transport.TransportAddress;
//import org.elasticsearch.index.query.BoolQueryBuilder;
//import org.elasticsearch.index.query.MatchAllQueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.search.SearchHit;
//import org.elasticsearch.search.SearchHits;
//import org.elasticsearch.search.aggregations.AbstractAggregationBuilder;
//import org.elasticsearch.search.aggregations.Aggregation;
//import org.elasticsearch.search.aggregations.AggregationBuilders;
//import org.elasticsearch.search.aggregations.Aggregations;
//import org.elasticsearch.search.aggregations.bucket.terms.Terms;
//import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
//import org.elasticsearch.search.aggregations.metrics.cardinality.Cardinality;
//import org.elasticsearch.search.aggregations.metrics.cardinality.CardinalityAggregationBuilder;
//import org.elasticsearch.search.aggregations.metrics.sum.Sum;
//import org.elasticsearch.search.aggregations.metrics.tophits.TopHits;
//import org.elasticsearch.search.aggregations.metrics.tophits.TopHitsAggregationBuilder;
//import org.elasticsearch.search.aggregations.metrics.valuecount.ValueCountAggregationBuilder;
//import org.elasticsearch.search.builder.SearchSourceBuilder;
//import org.elasticsearch.search.collapse.CollapseBuilder;
//import org.elasticsearch.transport.client.PreBuiltTransportClient;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
//import org.springframework.data.elasticsearch.core.ResultsExtractor;
//import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
//import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
//import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
//
//import java.io.IOException;
//import java.math.BigDecimal;
//import java.net.InetAddress;
//import java.net.UnknownHostException;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
//public class EsTest {
//
//    public static TransportClient getClient() throws UnknownHostException {
//
//        //1、指定es集群  cluster.name 是固定的key值，my-application是ES集群的名称
//        Settings settings = Settings.builder().put("cluster.name", "elasticsearch").build();
//        //2.创建访问ES服务器的客户端
//        TransportClient client = new PreBuiltTransportClient(settings)
//                //获取es主机中节点的ip地址及端口号(以下是单个节点案例)  *************
////                .addTransportAddress(new InetSocketTransportAddress(InetAddress.getByName("localhost"), 9300));
//                .addTransportAddress(new TransportAddress(InetAddress.getByName("localhost"), 9300));
//
//        return client;
//    }
//
//    public static void main(String[] args) throws UnknownHostException, ParseException {
//
//        String startParam = "2020-07-19";
//        String endParam = "2020-07-21";
////        String indexName = args[2];
////        String[] chainId = args[3].split(",");
//
//        DateTime startTime = DateUtil.parse(startParam);
//        DateTime endTime = DateUtil.parse(endParam);
//
//        DateField hour = DateField.HOUR_OF_DAY;
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//        List<DateTime> dateTimeList = DateUtil.rangeToList(startTime,endTime,hour);
//        for(int i=0; i<dateTimeList.size()-1;i++){
//
//                System.out.println("=>>"+dateTimeList.get(i)+"===>"+dateTimeList.get(i+1));
////
//
//
//        }
////
////        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd ");
////
////        Calendar nowTime = Calendar.getInstance();
////        System.out.println("now time: "+sdf.format(nowTime.getTime()));
////        Calendar startTime = Calendar.getInstance();
////
////        String endDate = sdf.format(nowTime.getTime());
////
////        startTime.add(Calendar.DAY_OF_MONTH,-1);
////
////        String beginDate = sdf.format(startTime.getTime());
////
////        System.out.println("startTime "+beginDate+"==> endTime: "+endDate);
//
////        String beginDate = "2020-06-01";
////        String endDate = "2020-07-05";
////
////        String ss = "";
////
////        cn.hutool.core.date.DateTime startTime = DateUtil.parse(beginDate);
////        DateTime endTime = DateUtil.parse(endDate);
////
////        DateField day = DateField.DAY_OF_MONTH;
////        List<DateTime> dateTimeList = DateUtil.rangeToList(startTime,endTime,day);
////        for(int i=0; i<=dateTimeList.size()-1;i++) {
////
////            String start = dateTimeList.get(i).toString();
////            String end;
////            if(i+30>=dateTimeList.size()-1){
////                end = dateTimeList.get(dateTimeList.size()-1).toString();
////            }else{
////                end = dateTimeList.get(i+30).toString();
////            }
////
////            System.out.println(start+ "==> "+end);
////            i += 30;
////        }
//
//
//            Date date = new Date();
//        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
//        System.out.println(formatter1.format(date));
//
////        Calendar calendar1   =   new GregorianCalendar();
////        calendar1.setTime(formatter1.parse("2020-07-08"));
////        calendar1.add(calendar.DATE,1);//把日期往后增加一天.整数往后推,负数往前移动
////        date=calendar1.getTime();   //这个时间就是日期往后推一天的结果
////        System.out.println(formatter.format(date));
////        System.out.println(calendar1.toString());
////
////        System.out.println("2020-07-06".equals(formatter.format(date)));
//
//
//        double costT = 89.393;
//
//        costT = new BigDecimal(costT).setScale(2,BigDecimal.ROUND_UP).doubleValue();
//
//        double pcost = new BigDecimal(44).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
//        costT = costT + pcost;
//
//        Client client = getClient();
//        List list = new ArrayList();
//        //聚合
////        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("secondClassfy").field("secondClassfy"+".keyword");
//        TermsAggregationBuilder aggregationBuilderW = AggregationBuilders.terms("employeeName").field("employeeName").size(1000);
//        TermsAggregationBuilder aggregationBuilders = AggregationBuilders.terms("secondClassfy").field("secondClassfy").size(1000);
//        TermsAggregationBuilder aggregationBuilders2 = AggregationBuilders.terms("personnel").field("personnel").size(1000);
//        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("chainId").field("chainId").size(2000);
//        TermsAggregationBuilder aggregationBuilderC = AggregationBuilders.terms("clinicId").field("clinicId").size(1000);
//        TermsAggregationBuilder aggregationBuilderP = AggregationBuilders.terms("personnelId").field("employeeName").size(1000);
//        TermsAggregationBuilder aggregationBuilderN = AggregationBuilders.terms("name").field("name").size(1000);
//
//        String[] includesProjectR = new String[]{"id","chainId","clinicId","clinicName","employeeName","feeType","grade","isWriter","manufacturer","name","personnelId","secondClassfy","shortId","spec","unit"};
//        TopHitsAggregationBuilder topHitsAggregationBuilder = AggregationBuilders.topHits("result").fetchSource(includesProjectR,new String[]{}).size(1);
//
//
//        SearchRequest searchRequestP = new SearchRequest("d_v1_dwd_charge_20000101_21000101_a_v_project_d");
//        searchRequestP.types("doc");
//        BoolQueryBuilder boolQueryBuilderP = QueryBuilders.boolQuery();
//        boolQueryBuilderP.filter(QueryBuilders.termQuery("chainId","97d1a6decfc84b2883fa30ca54a88efc")).filter(QueryBuilders.termsQuery("clinicId","826429ce446a4276923fd3c486902817")).must(QueryBuilders.rangeQuery("created").gte("2020-05-01").lte("2020-07-09"));
//        boolQueryBuilderP.filter(QueryBuilders.nestedQuery("feeTypes",QueryBuilders.termQuery("",""), ScoreMode.None));
//
//
//        String[] includesProject = new String[]{"chainId","clinicId","amount","name","personnelId",};
//        SearchSourceBuilder sourceBuilderP = new SearchSourceBuilder();
//
//        sourceBuilderP.query(boolQueryBuilderP);
//        sourceBuilderP.fetchSource(includesProject,new String[]{});
//        sourceBuilderP.from(0);
//        sourceBuilderP.size(30);
//
////        aggregationBuilderN.subAggregation(AggregationBuilders.sum("amount")).field("amount");
////        aggregationBuilderN.subAggregation(AggregationBuilders.sum("cost")).field("cost");
////        aggregationBuilderN.subAggregation(AggregationBuilders.sum("count")).field("count");
//
//        aggregationBuilderN.subAggregation(topHitsAggregationBuilder);
//        aggregationBuilderP.subAggregation(aggregationBuilderN);
//        aggregationBuilderN.subAggregation(AggregationBuilders.sum("amount").field("amount")).subAggregation(AggregationBuilders.sum("cost").field("cost")).subAggregation(AggregationBuilders.sum("count").field("count"));
//        aggregationBuilderC.subAggregation(aggregationBuilderP);
////        aggregationBuilderP.subAggregation(AggregationBuilders.sum("amount")).field("amount");
////        aggregationBuilderP.subAggregation(topHitsAggregationBuilder);
////        TermsAggregationBuilder pagg = aggregationBuilderC.subAggregation(aggregationBuilderP);
//
//        sourceBuilderP.aggregation(aggregationBuilderC);//聚合查询
//        searchRequestP.source(sourceBuilderP);
//
//        System.out.println(searchRequestP.source().query().toString());
//        System.out.println(searchRequestP.source().aggregations().toString());
//
//        SearchResponse searchResponseP = client.search(searchRequestP).actionGet();
//
//        Aggregations aggregationsP = searchResponseP.getAggregations();
//        Aggregation tagsP = aggregationsP.asMap().get("clinicId");
//
//        Terms termsP = (Terms)tagsP;
////        List<? extends Terms.Bucket> bucketsP = termsP.getBuckets();
//        for (Terms.Bucket entry : termsP.getBuckets()) {
//            Terms clinicAgg = entry.getAggregations().get("personnelId");
//            for (Terms.Bucket entry2 : clinicAgg.getBuckets()) {
//                Terms personnelAgg = entry2.getAggregations().get("name");
//                for(Terms.Bucket entry3 : personnelAgg.getBuckets()){
////                    entry3.getAggregations().get("result");
//                    TopHits topHits = entry3.getAggregations().get("result");
//                    SearchHits hits = topHits.getHits();
//                    SearchHit[] hitArray = hits.getHits();
//                    Map<String,Object> map = hitArray[0].getSourceAsMap();
//                    for(String s: map.keySet()){
//                        System.out.println(s+": "+map.get(s));
//                    }
//                    Sum amount = entry3.getAggregations().get("amount");
//                    Sum cost = entry3.getAggregations().get("cost");
//                    Sum count = entry3.getAggregations().get("count");
//
//                    System.out.println("clinic_id:" + entry.getKey()+"personnel: "+entry2.getKey() +
//                            "name: "+entry3.getKey() );
//                    System.out.println("amount: =>"+amount+" cost:=>"+cost+" count:=>"+count);
//
//                }
//
//            }
//        }
////        for (Terms.Bucket bucket : bucketsP) {
////            List<? extends Terms.Bucket> bucketsC = bucket.();
////            System.out.println(bucket.getKeyAsString());
////        }
//
//
//        SearchRequest searchRequest = new SearchRequest("d_v1_dwd_charge_20000101_21000101_a_v_project_d");
//        searchRequest.types("doc");
//        String[] includesProjectP = new String[]{"id","clinic_id","secondClassfy","chain_id","personnel","feeType"};
//
//
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
////        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
//
//
////        boolQueryBuilder.must(QueryBuilders.matchAllQuery());//查询所有 此处为匹配所有文档
//        //elasticsearch 里默认的IK分词器是会将每一个中文都进行了分词的切割，所以你直接想查一整个词  加上.keyword
////        boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("personnel.keyword", "personnel"));
//        boolQueryBuilder.filter(QueryBuilders.termQuery("chain_id","97d1a6decfc84b2883fa30ca54a88efc")).must(QueryBuilders.rangeQuery("created").gte("2020-01-01").lte("2020-07-09"));
////        boolQueryBuilder2.filter(QueryBuilders.termQuery("chain_id","97d1a6decfc84b2883fa30ca54a88efc")).must(QueryBuilders.rangeQuery("created").gte("2020-01-01").lte("2020-07-09"));
//
//        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();// 1.创建并设置SearchSourceBuilder对象
////        SearchSourceBuilder sourceBuilder2 = new SearchSourceBuilder();// 1.创建并设置SearchSourceBuilder对象
//
//
//        sourceBuilder.query(boolQueryBuilder);
////        sourceBuilder.fetchSource(includesProject,new String[]{});
//        sourceBuilder.from(0);
//        sourceBuilder.size(1000);
//
////        sourceBuilder2.query(boolQueryBuilder2);
////        sourceBuilder2.fetchSource(includesProject,new String[]{});
////        sourceBuilder2.from(0);
////        sourceBuilder2.size(3000);
//
//        sourceBuilder.aggregation(aggregationBuilderW);//聚合查询
//        searchRequest.source(sourceBuilder);
//
//        SearchResponse searchResponse = client.search(searchRequest).actionGet();
//
//        Aggregations aggregations = searchResponse.getAggregations();
//        Aggregation tags = aggregations.asMap().get("employeeName");
//
//        Terms terms = (Terms)tags;
//        List<? extends Terms.Bucket> buckets = terms.getBuckets();
//        for (Terms.Bucket bucket : buckets) {
//            System.out.println(bucket.getKeyAsString());
//        }
//
////        sourceBuilder2.aggregation(aggregationBuilders2);//聚合查询
////        searchRequest.source(sourceBuilder2);
//
//        SearchResponse searchResponse2 = client.search(searchRequest).actionGet();
//
//        Aggregations aggregations2 = searchResponse2.getAggregations();
//        Aggregation tags2 = aggregations2.asMap().get("personnel");
//
//        Terms terms2 = (Terms)tags2;
//        List<? extends Terms.Bucket> buckets2 = terms2.getBuckets();
//        for (Terms.Bucket bucket : buckets2) {
//            System.out.println(bucket.getKeyAsString());
//        }
//
//
//
//
//
//        Client client1 = getClient();
////
////        SearchRequestBuilder srb2=client.prepareSearch("testc").setTypes("doc");
////        String chainId = "0e450a5046e241c2bbc836dde3c4daa9";
////        Integer includeReg = 1;
//////        SearchRequestBuilder srb = client.prepareSearch("testc").setTypes("doc");
////        srb2.setQuery(QueryBuilders.termQuery("chain_id",chainId));
////        srb2.setCollapse(new CollapseBuilder("clinic_id"));
////        if(includeReg == 0){
////            srb2.setQuery(QueryBuilders.boolQuery().mustNot(QueryBuilders.termQuery("feeType","5_1")));
////        }
////        srb2.setFetchSource(new String[]{"clinic_id"},new String[]{});
////        srb2.setQuery(QueryBuilders.rangeQuery("created").gte("2020-05-06").lte("2020-05-08"));
//////        SearchResponse sr2 = srb2.execute().actionGet();
//////        for (SearchHit hit: sr2.getHits()){
//////            System.out.println(hit.getSourceAsString());
//////        }
////
////
////
//////        elasticsearchTemplate.deleteIndex("dev.d_v1_dwd_charge_20000101_21000101_a");
//////
////
////        System.out.println("=======");
////
////        // 搜索请求对象
////        SearchRequest searchRequest = new SearchRequest("testb");
////        // 指定类型
////        searchRequest.types("doc");
////        // 搜索源构建对象
////        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
////        // 搜索方式
////        // matchAllQuery搜索全部
//////        searchSourceBuilder.query(QueryBuilders.matchAllQuery());
//////        searchSourceBuilder.query(QueryBuilders.termQuery("chain_id","0e450a5046e241c2bbc836dde3c4daa9"));
////        // 设置源字段过虑,第一个参数结果集包括哪些字段，第二个参数表示结果集不包括哪些字段
////        searchSourceBuilder.fetchSource(new String[]{"clinic_id","personnel","copywriter_id","amount","created"},new String[]{});
////        searchSourceBuilder.query(QueryBuilders.boolQuery().mustNot(QueryBuilders.termQuery("copywriter_id","")
////        ).filter(QueryBuilders.termQuery("personnel","6e45706922a74966ab51e4ed1e604641")));
////        // 向搜索请求对象中设置搜索源
////        searchRequest.source(searchSourceBuilder);
////        // 执行搜索,向ES发起http请求
////        SearchResponse searchResponse = client.search(searchRequest).actionGet();
////        // 搜索结果
////        SearchHits hits = searchResponse.getHits();
////        // 匹配到的总记录数
////        long totalHits = hits.getTotalHits();
////        SearchHit[] searchHits = hits.getHits();
////
//////        for(SearchHit hit: searchHits){
//////            System.out.println(hit.getSourceAsMap());
//////        }
////
////        System.out.println("======");
////
////        SearchRequestBuilder srb=client.prepareSearch("testb").setTypes("doc");
//////        QueryBuilders builders = new QueryBuilders();
//////        srb.setQuery(QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("chain_id","0e450a5046e241c2bbc836dde3c4daa9")).filter(QueryBuilders.rangeQuery("created").gte("2020-05-06").lte("2020-05-08"))).setFrom(0).setSize(15);
//////
////        srb.setQuery(QueryBuilders.termQuery("chain_id","0e450a5046e241c2bbc836dde3c4daa9")).setFrom(0).setSize(15);
////        srb.setQuery(QueryBuilders.rangeQuery("created").gte("2020-05-06").lte("2020-05-08"));
////        srb.setFetchSource(new String[]{"id","chain_id","clinic_id","personnel","copywriter_id","amount","created","patient_id","patient_name"},new String[]{});
////        SearchResponse sr = srb.execute().actionGet();
////        for (SearchHit hit: sr.getHits()){
////            AchievementVoucher achievementVoucher = JSON.parseObject(hit.getSourceAsString(),AchievementVoucher.class);
////            System.out.println(achievementVoucher.toString());
////        }
////
////        SearchRequestBuilder builder=client.prepareSearch("dev.v3_outpatient_prescription_form").setTypes("stat");
////
////        builder.setQuery(QueryBuilders.termQuery("v2_charge_sheet.chain_id","6a869c22abee4ffbaef3e527bbb70aeb")).setFrom(0).setSize(15);
//////        builder.setFetchSource(new String[]{"_flink","v2_outpatient_prescription_form.doctor_id","v2_charge_sheet.chain_id"},new String[]{});
////        SearchResponse response = builder.execute().actionGet();
////        System.out.println("==========");
////        for (SearchHit hit: response.getHits()){
////            System.out.println(hit.getSourceAsString());
////        }
//
//        System.out.println("=========");
//
//        ElasticsearchTemplate elasticsearchTemplate = new ElasticsearchTemplate(client);
//
//        String[] includes = new String[]{"id","clinic_id","secondClassfy","chain_id","v2ct_id","personnel","copywriter_id","charge_record_amount","created","patient_id","patient_name","feeType"};
//        String[] includesProject1 = new String[]{"id","clinic_id","secondClassfy","chain_id","v2ct_id","personnel","copywriter_id","charge_record_amount","created","patient_id","patient_name","feeType","v2g_id","v2g_name","name","short_id","manufacturer","grade","Specifications","number","unit"};
//        String[] includesDis = new String[]{"copywriter_id","chain_id","goods_id","clinic_id","type","log_time","cost","doctor_id","seller_id"};
//        String[] includesOp = new String[]{"v2_outpatient_prescription_form","v2_charge_sheet","_flink"};
//
//
////        TermsBuilder tb=
//        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
////                .withQuery(QueryBuilders.termQuery("chain_id","97d1a6decfc84b2883fa30ca54a88efc"))
//                .withQuery(QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("chain_id","97d1a6decfc84b2883fa30ca54a88efc")).must(QueryBuilders.rangeQuery("created").gte("2020-06-17").lte("2020-06-17")))
////                .withQuery(QueryBuilders.termQuery("clinic_id","d5e8ebe50d754f16bbda6fd9d438406d"))
////                .withQuery(QueryBuilders.rangeQuery("created").gte("2020-06-09").lte("2020-06-20"))
//                .withPageable(PageRequest.of(0,15))
//                .withSourceFilter(new FetchSourceFilter(includesProject,new String[]{}))
////                .addAggregation(AggregationBuilders.terms("group_name").field("v2ct_id"))
//                .withIndices("d_v1_dwd_charge_20000101_21000101_a")
//                .withTypes("doc")
//                .build();
//
//        NativeSearchQuery nativeSearchQueryDis = new NativeSearchQueryBuilder()
//                .withQuery(QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("clinic_id","5e7505c90d7d4f97b02505e91d5fd30b")).must(QueryBuilders.rangeQuery("log_time").gte("2020-06-01").lte("2020-07-17")))
//                .withPageable(PageRequest.of(0,66))
//                .withSourceFilter(new FetchSourceFilter(includesDis,new String[]{}))
//                .withIndices("t_v1_dwd_dispensing_20000101_21000101")
//                .withTypes("doc")
//                .build();
//
//        NativeSearchQuery nativeSearchQueryOp = new NativeSearchQueryBuilder()
//                .withQuery(QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("v2_charge_sheet.chain_id","97d1a6decfc84b2883fa30ca54a88efc")).must(QueryBuilders.rangeQuery("v2_charge_sheet.first_charged_time").gte("2020-05-13").lte("2020-05-13")))
//                .withPageable(PageRequest.of(0,15))
//                .withSourceFilter(new FetchSourceFilter(includesOp,new String[]{}))
//                .withIndices("test.v3_outpatient_prescription_form")
//                .withTypes("stat")
//                .build();
//
//
//
//        List<AchievementProject> entityList = elasticsearchTemplate.queryForList(nativeSearchQuery, AchievementProject.class);
//        entityList.forEach(e-> System.out.println(e.toString()));
////        for(AchievementProject entity: entityList){
////            System.out.println(entity.toString());
////        }
//        System.out.println("============");
//
//        List<OutpatientEntity> entityListOp = elasticsearchTemplate.queryForList(nativeSearchQueryOp, OutpatientEntity.class);
//        for(OutpatientEntity t: entityListOp){
//            System.out.println(t.toString());
//        }
//
//        List<DispensingCost> entityListDis = elasticsearchTemplate.queryForList(nativeSearchQueryDis, DispensingCost.class);
//        for(DispensingCost t: entityListDis){
//            System.out.println(t.toString());
//        }
//        for(OutpatientEntity outpatientEntity: entityListOp){
////            String projectId = outpatientEntity.get
//        }
//
//        Map<String,Double> costMap = new HashMap<>();
//        for(DispensingCost dispensingCost: entityListDis){
//            String personnel = "";
//
//            if(StringUtils.isNotBlank(dispensingCost.getDoctor_id())){
//                personnel = dispensingCost.getDoctor_id();
//            }else if(StringUtils.isNotBlank(dispensingCost.getSeller_id())){
//                personnel = dispensingCost.getSeller_id();
//            }
//
//            String projectId = dispensingCost.getCopywriter_id()+dispensingCost.getChain_id()
//                    + dispensingCost.getClinic_id() + dispensingCost.getGoods_id()
//                    + personnel;
//            Double cost = dispensingCost.getCost();
//            if(dispensingCost.getType().equals("4")){
//                cost = -1 * cost;
//            }
//            costMap.put(projectId,cost);
//
//        }
//
//
//        Map<String, AchievementProjectPojo> projectMap = new HashMap<>();
//        for(AchievementProject project: entityList){
//            String projectId =project.getCopywriter_id() + project.getChain_id() + project.getClinic_id()
//                    + project.getV2g_id() + project.getPersonnel();
////            String projectWriterId = project.getChain_id() + project.getClinic_id() + project.getV2g_id() + project.getCopywrite_id();
//
//            AchievementProjectPojo pojo = new AchievementProjectPojo();
//            pojo.setAmount(project.getCharge_record_amount());
//            pojo.setClinicId(project.getClinic_id());
//            pojo.setClinicName("");
//            pojo.setName(project.getProject());
//            pojo.setFeeType1(project.getFeeType());
//            pojo.setShortId(project.getShort_id());
//            pojo.setManufacturer(project.getManufacturer());
//            pojo.setGrade(project.getGrade());
//            pojo.setSpec(project.getSpecifications());
//            pojo.setCount(project.getNumber());
//            pojo.setUnit(project.getUnit());
//            // todo
//            Double cost = 0.0;
//            String feeType = project.getFeeType();
//
//            if(costMap.containsKey(projectId) && feeType.equals("1_1")
//            || feeType.equals("1_2") || feeType.equals("1_3")|| feeType.equals("2") || feeType.equals("7")){
//                cost = costMap.get(projectId);
//            }else if(project.getCost() != null){
//                cost = project.getCost();
//            }
//
//            pojo.setCost(cost);
//
//            // 毛利、毛利率
//            Double cos = 0.0;
//            Double grossMargin = null;
//            Double profit = null;
//            if(project.getCharge_record_amount() != null){
//                profit = project.getCharge_record_amount() - cos;
//                if(project.getCharge_record_amount() != 0.0 && profit >= 0){
//                    grossMargin = profit/project.getCharge_record_amount();
//                }
//            }
//
//            if(grossMargin != null){
//                pojo.setProfit(grossMargin.toString());
//            }
//
//        }
//        Collections.sort(entityList);
////        projectMap.put();
//
//
////        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
//////        builder.addAggregation(ValueCountAggregationBuilder.parse())
//////        builder.wh
////
////
////        Aggregations aggregations = elasticsearchTemplate.query(nativeSearchQuery, new ResultsExtractor<Aggregations>() {
////
////            @Override
////            public Aggregations extract(SearchResponse response) {
////                return response.getAggregations();
////            }
////        });
//
//
//    }
//
//
//    private static void aggDemo(){
//
//    }
//}
