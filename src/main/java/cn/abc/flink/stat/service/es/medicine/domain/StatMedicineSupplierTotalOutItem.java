package cn.abc.flink.stat.service.es.medicine.domain;

public class StatMedicineSupplierTotalOutItem {
    private double totalOutBatchCount;

    private double totalOutKindCount;

    public double getTotalOutBatchCount() {
        return totalOutBatchCount;
    }

    public void setTotalOutBatchCount(double totalOutBatchCount) {
        this.totalOutBatchCount = totalOutBatchCount;
    }

    public double getTotalOutKindCount() {
        return totalOutKindCount;
    }

    public void setTotalOutKindCount(double totalOutKindCount) {
        this.totalOutKindCount = totalOutKindCount;
    }

    @Override
    public String toString() {
        return "StatMedicineSupplierTotalOutItem{" +
                "totalOutBatchCount=" + totalOutBatchCount +
                ", totalOutKindCount=" + totalOutKindCount +
                '}';
    }
}
