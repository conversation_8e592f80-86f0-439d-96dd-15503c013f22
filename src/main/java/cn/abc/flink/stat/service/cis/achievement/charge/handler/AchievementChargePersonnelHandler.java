package cn.abc.flink.stat.service.cis.achievement.charge.handler;

import cn.abc.flink.stat.common.BeanUtils;
import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.request.AbcPermission;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.common.utils.MathUtil;
import cn.abc.flink.stat.db.cis.common.AchievementChargeMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2GoodsCustomType;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.service.cis.achievement.charge.AchievementChargeService;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeAdviceFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalFeeAmountEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonalPrescriptionEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonnelBase;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargePersonnelDispensingCost;
import cn.abc.flink.stat.service.cis.achievement.charge.impl.AchievementChargePersonnelComparator;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAdviceFeeClassifyReqParams;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeDataFee;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeFeeClassify;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargePersonnelReqParams;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementKeyValuePojo;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementPerson;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.DispensingFeeFirstClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.DispensingFeeSecondClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.FeeFirstClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.FeeSecondClassifyParam;
import cn.abc.flink.stat.service.cis.config.handler.StatConfigHandler;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.handler.EmployeeHandler;
import cn.abc.flink.stat.service.his.achievement.charge.domain.HisAchievementSummaryDAO;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import com.alibaba.fastjson.JSON;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static cn.abcyun.cis.commons.jwt.CisJWTUtils.CIS_HIS_TYPE_DENTISTRY;
import static cn.abcyun.cis.commons.jwt.CisJWTUtils.CIS_HIS_TYPE_PHARMACY;

/**
 * 人员handler
 *
 * <AUTHOR>
 */
public class AchievementChargePersonnelHandler {

    private static final Integer PRECISION_DEFAULT_VALUE = 4;

    private static final Integer CLASSIFY_LEVEL_1 = 1;

    private static final Integer CLASSIFY_LEVEL_2 = 2;

    private static final String EYEGLASS_PRESCRIPTION = "24";

    private static Map<Byte, String> prescriptionMap = new HashMap<>();

    private DimensionQuery query;

    private ExecutorService cacheExecutorService;

    private AchievementChargeMapper mapper;

    private CompletableFuture<List<AchievementChargePersonalFeeAmountEntity>> amountFuture;

    private CompletableFuture<List<AchievementChargePersonnelBase>> transformPatientCountFuture;

    private CompletableFuture<List<AchievementChargePersonalPrescriptionEntity>> opticianPrescriptionFuture;

    private CompletableFuture<List<AchievementChargePersonalPrescriptionEntity>> prescriptionFuture;

    private CompletableFuture<List<AchievementChargePersonalFeeAmountEntity>> classify1Future;

    private CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeFirstClassifyTotal;

    private CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeSecondClassifyTotal;

    private CompletableFuture<List<AchievementChargeFeeEntity>> listFutureDispensingFeeSecondClassifyTotal;

    private CompletableFuture<List<AchievementChargeFeeEntity>> listFutureDispensingFeeFirstClassifyTotal;

    private CompletableFuture<List<AchievementChargePersonalFeeAmountEntity>> classify2Future;

    private CompletableFuture<List<AchievementChargePersonalFeeAmountEntity>> adviceClassifyFuture;

    private CompletableFuture<List<AchievementChargeAdviceFeeEntity>> listFutureAdviceFeeClassifyTotal;

    private CompletableFuture<Map<String, Organ>> listFutureOrgan;

    private CompletableFuture<Map<String, Employee>> listFutureEmployee;

    private CompletableFuture<Map<Integer, V2GoodsCustomType>> listFutureCustomType;

    private CompletableFuture<Map<Long, V2GoodsFeeType>> listFutureAdviceFeeType;

    private Map<String, Organ> organMap;

    private Map<String, Employee> employeeMap;

    private Map<Integer, V2GoodsCustomType> customTypeMap;

    private List<Byte> prescriptionNameList;

    private static Byte[] sortPrescriptionTitle = new Byte[]{
            Byte.valueOf("4"),
            Byte.valueOf("5"),
            Byte.valueOf("61"),
            Byte.valueOf("62"),
            Byte.valueOf("16"),
            Byte.valueOf("24")
    };

    static {
        prescriptionMap.put(Byte.valueOf("4"), "中西成药处方");
        prescriptionMap.put(Byte.valueOf("5"), "输注处方");
        prescriptionMap.put(Byte.valueOf("61"), "饮片处方");
        prescriptionMap.put(Byte.valueOf("62"), "颗粒处方");
        prescriptionMap.put(Byte.valueOf("16"), "外治处方");
        prescriptionMap.put(Byte.valueOf("24"), "配镜处方");
    }

    public AchievementChargePersonnelHandler(DimensionQuery query,
                                             AchievementChargeMapper mapper,
                                             ExecutorService cacheExecutorService) {
        this.query = query;
        this.cacheExecutorService = cacheExecutorService;
        this.mapper = mapper;
    }

    /**
     * 处理Data
     *
     * @param params   参数
     * @param baseList 收费数据
     * @param costList 发药数据
     * @param dto      计提配置
     * @return AchievementChargePersonRsp
     * @throws ExecutionException   exception
     * @throws InterruptedException exception
     */
    public V2StatResponse handleData(AchievementChargePersonnelReqParams params,
                                     List<AchievementChargePersonnelBase> baseList,
                                     List<AchievementChargePersonnelDispensingCost> costList,
                                     StatConfigDto dto) throws ExecutionException, InterruptedException {
        CompletableFuture.allOf(this.listFutureCustomType, this.listFutureOrgan, this.listFutureEmployee).join();
        this.organMap = listFutureOrgan.get();
        this.employeeMap = listFutureEmployee.get();
        this.customTypeMap = listFutureCustomType.get();
        // 合并 开单数据和发药成本，包括当天只有发药没有开单的情况
        Map<String, AchievementPerson> mergeDispensingCost = mergeDispensingCost(baseList, costList);
        Map<String, AchievementPerson> sortMap = MapUtils.sortMapByKey(mergeDispensingCost, new AchievementChargePersonnelComparator());
        mergeAmount(sortMap, this.amountFuture.join(), dto);
//        mergeTransformPatientCount(sortMap, this.transformPatientCountFuture.join());
        List<AchievementChargeFeeClassify> feeList = null;
        if ("100".equals(params.getHisType())) {
            CompletableFuture.allOf(adviceClassifyFuture, listFutureAdviceFeeType, listFutureAdviceFeeClassifyTotal).join();
            mergeAndFlatAdviceFeeClassify(sortMap, adviceClassifyFuture.get(), costList, listFutureAdviceFeeType.get(), dto);
            feeList = AchievementChargeHandler.getAdviceFeeListTotal(listFutureAdviceFeeClassifyTotal.get(),
                    listFutureAdviceFeeType.get());
        } else {
            if (classify1Future != null && classify2Future != null) {
                CompletableFuture.allOf(classify1Future, classify2Future).join();
                mergeAndFlatFeeClassify(sortMap, classify1Future.get(), classify2Future.get(), customTypeMap, costList, dto, params.getHisType());
            } else if (classify1Future == null && classify2Future != null) {
                CompletableFuture.allOf(classify2Future).join();
                mergeAndFlatFeeClassify(sortMap, new ArrayList<>(), classify2Future.get(), customTypeMap, costList, dto, params.getHisType());
            } else if (classify1Future != null && classify2Future == null) {
                CompletableFuture.allOf(classify1Future).join();
                mergeAndFlatFeeClassify(sortMap, classify1Future.get(), new ArrayList<>(), customTypeMap, costList, dto, params.getHisType());
            }
            feeList = AchievementChargeHandler.getFeeListTotal(query, listFutureFeeFirstClassifyTotal,
                    listFutureFeeSecondClassifyTotal, listFutureDispensingFeeFirstClassifyTotal,
                    listFutureDispensingFeeSecondClassifyTotal, customTypeMap, false, params.getChainId(), params.getHisType());
        }
        List<AchievementKeyValuePojo> prescriptionTitleList = new ArrayList<>();
        if (!CIS_HIS_TYPE_DENTISTRY.equals(params.getHisType())) {
            CompletableFuture.allOf(prescriptionFuture, opticianPrescriptionFuture).join();
            this.prescriptionNameList = mergeAndFlatPrescription(sortMap, prescriptionFuture.get()
                    , opticianPrescriptionFuture.get(), prescriptionMap);
            for (Byte title : sortPrescriptionTitle) {
                if (prescriptionNameList.contains(title)) {
                    prescriptionTitleList.add(getKeyValuePojo(prescriptionMap.get(title), String.valueOf(title)));
                }
            }
        }
        List<AchievementPerson> data = new ArrayList<>(sortMap.values());
        data = assembleMergeSamePerson(data);
        data.sort(Comparator.comparing(p -> p.getClinicName() + p.getEmployeeName() + p.getPersonnelId()));
        fillPersonnelNullValue(data, params.getPermission());
        Map<String, BigDecimal> totalFee = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        // 数据扁平化和求费用总和
        for (AchievementPerson datum : data) {
            list.add(achievementChargePersonnel2Map(datum, totalFee, params));
        }
        V2StatResponse result = new V2StatResponse();
        result.setHeader(getPersonnelHeader(params, dto, feeList, prescriptionTitleList));
        result.setData(list);
        result.setSummary(achievementPersonSumRow(data, totalFee, params));
        result.setTotal(new StatResponseTotal(ConvertUtils.getAsLong(data.size())));
        return result;
    }

    /**
     * 组装人员数据
     *
     * @param personList -
     * @return -
     */
    private List<AchievementPerson> assembleMergeSamePerson(List<AchievementPerson> personList) {
        Map<String, AchievementPerson> mergeMap = new HashMap<>();
        for (AchievementPerson person : personList) {
            String key = person.getChainId() + "-" + person.getClinicId() + "-" + person.getPersonnelId() + "-"
                    + person.getEmployeeName();
            if (person.getHoverCode() == null) {
                person.setHoverCode(0);
            }
            if (mergeMap.containsKey(key)) {
                AchievementPerson merge = mergeMap.get(key);
                merge.setPatientCount(merge.getPatientCount() + person.getPatientCount());
                merge.setAmount(merge.getAmount().add(person.getAmount()));
                merge.setCommissionAmount(merge.getCommissionAmount().add(person.getCommissionAmount()));
                if (merge.getPatientCount() != null && merge.getPatientCount() != 0) {
                    merge.setAvgPatientAmount(merge.getCommissionAmount().divide(new BigDecimal(merge.getPatientCount()),
                            PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP));
                }
                merge.setCost(merge.getCost().add(person.getCost()));
                merge.setOrigin(merge.getOrigin().add(person.getOrigin()));
                merge.setDeduct(merge.getDeduct().add(person.getDeduct()));
                if (person.getPrescriptions() != null) {
                    merge.getPrescriptions().addAll(person.getPrescriptions());
                }
                if (person.getHoverCode() > merge.getHoverCode()) {
                    merge.setHoverCode(person.getHoverCode());
                }
                merge.getFeeTypes().addAll(person.getFeeTypes());
            } else {
                mergeMap.put(key, person);
            }
        }
        return new ArrayList<>(mergeMap.values());
    }

    /**
     * 将人员对象转换为map，并且记录总金额
     *
     * @param resp     科室对象
     * @param totalFee 总金额map
     * @param params   请求参数
     * @return map
     */
    private Map<String, Object> achievementChargePersonnel2Map(
            AchievementPerson resp,
            Map<String, BigDecimal> totalFee,
            AchievementChargePersonnelReqParams params) {
        Map<String, Object> obj = BeanUtils.toMap(AchievementPerson.class, resp);
        //处理费用分类data
        obj.remove("feeTypes");
        if (resp.getFeeTypes() != null) {
            for (AchievementChargeDataFee f : resp.getFeeTypes()) {
                obj.put("fee-" + f.getField() + "-" + "feeCost", new BigDecimal(obj.getOrDefault("fee-"
                        + f.getField() + "-" + "feeCost", BigDecimal.ZERO).toString()).add(f.getCost()));
                obj.put("fee-" + f.getField() + "-" + "feeGross", new BigDecimal(obj.getOrDefault("fee-"
                        + f.getField() + "-" + "feeGross", BigDecimal.ZERO).toString()).add(f.getGross()));
                obj.put("fee-" + f.getField() + "-" + "feeCommissionAmt", new BigDecimal(obj.getOrDefault("fee-"
                        + f.getField() + "-" + "feeCommissionAmt", BigDecimal.ZERO).toString()).add(f.getCommissionAmt()));

                if (params.getIsContainOthersWriterAchievement() != null
                        && params.getIsContainOthersWriterAchievement() == 0 && params.getIncludeWriter() == 1) {
                    totalFee.put("fee-" + f.getField() + "-feeCost", totalFee.getOrDefault("fee-" + f.getField()
                            + "-" + "feeCost", BigDecimal.ZERO).add(f.getCost()));
                    totalFee.put("fee-" + f.getField() + "-feeGross", totalFee.getOrDefault("fee-" + f.getField()
                            + "-" + "feeGross", BigDecimal.ZERO).add(f.getGross()));
                    totalFee.put("fee-" + f.getField() + "-feeCommissionAmt", totalFee.getOrDefault("fee-" + f.getField()
                            + "-" + "feeCommissionAmt", BigDecimal.ZERO).add(f.getCommissionAmt()));
                } else {
                    if (resp.getIsWriter() != 1) {
                        totalFee.put("fee-" + f.getField() + "-feeCost",
                                totalFee.getOrDefault("fee-" + f.getField() + "-" + "feeCost",
                                        BigDecimal.ZERO).add(f.getCost()));
                        totalFee.put("fee-" + f.getField() + "-feeGross",
                                totalFee.getOrDefault("fee-" + f.getField() + "-" + "feeGross",
                                        BigDecimal.ZERO).add(f.getGross()));
                        totalFee.put("fee-" + f.getField() + "-feeCommissionAmt",
                                totalFee.getOrDefault("fee-" + f.getField() + "-" + "feeCommissionAmt",
                                        BigDecimal.ZERO).add(f.getCommissionAmt()));
                    }
                }
            }
        }
        obj.put("prescriptions", "-");
        //处理处方数据
        if (resp.getPrescriptions() != null) {
            for (AchievementKeyValuePojo p : resp.getPrescriptions()) {
                BigDecimal value = ConvertUtils.getAsBigDecimal(p.getValue()) == null ? BigDecimal.ZERO
                        : ConvertUtils.getAsBigDecimal(p.getValue()).setScale(0);
                obj.put("pres-" + p.getName(), new BigDecimal(obj.getOrDefault("pres-" + p.getName(), BigDecimal.ZERO).toString()).add(value).setScale(0));
                if (!params.getHisType().equals(CIS_HIS_TYPE_DENTISTRY)
                        && params.getIsContainOthersWriterAchievement() != null
                        && params.getIsContainOthersWriterAchievement() == 0 && params.getIncludeWriter() == 1) {
                    totalFee.put("pres-" + p.getName(), totalFee.getOrDefault("pres-" + p.getName(), BigDecimal.ZERO).add(value));
                } else {
                    if (resp.getIsWriter() != 1) {
                        totalFee.put("pres-" + p.getName(), totalFee.getOrDefault("pres-" + p.getName(), BigDecimal.ZERO).add(value));
                    }
                }
            }
        }
        return obj;
    }

    /**
     * @param data     列表数据
     * @param totalFee 总费用
     * @param params   -
     * @return sumRow
     */
    public Map<String, Object> achievementPersonSumRow(List<AchievementPerson> data,
                                                       Map<String, BigDecimal> totalFee,
                                                       AchievementChargePersonnelReqParams params) {
        AchievementPerson sumRow = new AchievementPerson();
        for (AchievementPerson datum : data) {
            if (params.getIsContainOthersWriterAchievement() != null
                    && params.getIsContainOthersWriterAchievement() == 0 && params.getIncludeWriter() == 1) {
                sumRow.setPatientCount(sumRow.getPatientCount() + datum.getPatientCount());
                sumRow.setTransFormPatientCount(sumRow.getTransFormPatientCount() + datum.getTransFormPatientCount());
                if (datum.getAmount() != null) {
                    sumRow.setAmount(sumRow.getAmount().add(datum.getAmount()));
                }
                if (datum.getCost() != null) {
                    sumRow.setCost(sumRow.getCost().add(datum.getCost()));
                }
                if (datum.getCommissionAmount() != null) {
                    sumRow.setCommissionAmount(sumRow.getCommissionAmount().add(datum.getCommissionAmount()));
                }
            } else {
                if (datum.getIsWriter() != 1) {
                    sumRow.setPatientCount(sumRow.getPatientCount() + datum.getPatientCount());
                    sumRow.setTransFormPatientCount(sumRow.getTransFormPatientCount() + datum.getTransFormPatientCount());
                    if (datum.getAmount() != null) {
                        sumRow.setAmount(sumRow.getAmount().add(datum.getAmount()));
                    }
                    if (datum.getCost() != null) {
                        sumRow.setCost(sumRow.getCost().add(datum.getCost()));
                    }
                    if (datum.getCommissionAmount() != null) {
                        sumRow.setCommissionAmount(sumRow.getCommissionAmount().add(datum.getCommissionAmount()));
                    }
                }
            }

        }
        sumRow.calcSummary();
        if (!params.getPermission().isEnableCost()) {
            sumRow.setCost(null);
        }
        if (!params.getPermission().isEnableGross()) {
            sumRow.setGross(null);
            sumRow.setProfit(null);
            sumRow.setProfitText("-");
        }
        sumRow.prettyMoney();
        Map<String, Object> total = BeanUtils.toMap(AchievementPerson.class, sumRow);
        total.remove("feeTypes");
//        amountTransferredStr(totalFee, total);
        //处方数据为null时给默认值
        total.put("prescriptions", "-");
        total.putAll(totalFee);
        return total;
    }

    /**
     * @param
     * @param totalFee 费用分类合计map
     * @param total    完整汇总行map
     * @return
     * @Description: 将费用分类map金额转为string
     * @Author: zs
     * @Date: 2023/1/9 14:52
     */
    public static void amountTransferredStr(Map<String, BigDecimal> totalFee, Map<String, Object> total) {
        //循环遍历将汇总行金额相关保留两位小数
        Set<Map.Entry<String, BigDecimal>> entries = totalFee.entrySet();
        Iterator<Map.Entry<String, BigDecimal>> iterator = entries.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, BigDecimal> next = iterator.next();
            total.put(next.getKey(), next.getValue().toString());
        }
    }

    /**
     * 获取人员表头
     *
     * @param params                -
     * @param dto                   -
     * @param feeList               -
     * @param prescriptionTitleList -
     * @return -
     */
    public List<TableHeaderEmployeeItem> getPersonnelHeader(AchievementChargePersonnelReqParams params,
                                                            StatConfigDto dto,
                                                            List<AchievementChargeFeeClassify> feeList,
                                                            List<AchievementKeyValuePojo> prescriptionTitleList) {
        List<TableHeaderEmployeeItem> tableHeaderEmployeeItems = query.getTableHeaderEmployeeItems(
                params.getParams().getEmployeeId(), HeaderTableKeyConfig.ACHIEVEMENT_CHARGE_PERSONNEL,
                params.getParams().getViewModeInteger(), params.getParams().getNodeType(),
                HeaderTableKeyConfig.EXCLUDE_HIDDEN_1);
        if (params.getHisType().equals(CIS_HIS_TYPE_PHARMACY)) {
            tableHeaderEmployeeItems = query.getTableHeaderEmployeeItems(
                    params.getParams().getEmployeeId(), HeaderTableKeyConfig.ACHIEVEMENT_CHARGE_PERSONNEL_PHARMACY,
                    params.getParams().getViewModeInteger(), params.getParams().getNodeType(),
                    HeaderTableKeyConfig.EXCLUDE_HIDDEN_1);
        }
        tableHeaderEmployeeItems = AchievementChargeHandler
                .handleHeaderItemOfFeeType(tableHeaderEmployeeItems, feeList, prescriptionTitleList, params.getHisType());
        tableHeaderEmployeeItems = AchievementChargeHandler
                .handleHeaderItemOfCommission(tableHeaderEmployeeItems, dto);
        AchievementChargeHandler
                .handleHeaderItemOfEmpName(tableHeaderEmployeeItems, params.getHisType(), params.getEmployeeTypeEnum());
        if (params.getHisType().equals(CIS_HIS_TYPE_DENTISTRY) || params.getHisType().equals(CIS_HIS_TYPE_PHARMACY)) {
            return tableHeaderEmployeeItems.stream().filter(x -> (!("prescriptions").equals(x.getProp())))
                    .collect(Collectors.toList());
        } else {
            return tableHeaderEmployeeItems;
        }
    }

    /**
     * 触发future，查询mysql
     *
     * @param params 参数
     */
    public void fetchFuture(AchievementChargePersonnelReqParams params) {
        // 实收,成本(除了发药成本意外的其他成本)，原价，折扣
        this.amountFuture = CompletableFuture.supplyAsync(() ->
                mapper.selectPersonalAmount(TableUtils.getCisTable(), params), cacheExecutorService);
        // 计算转化收费人次
//        this.transformPatientCountFuture = CompletableFuture.supplyAsync(() ->
//                mapper.selectPersonnelTransformPatientCount(TableUtils.getCisTable(), params), cacheExecutorService);
        //处方
        if (!CisJWTUtils.CIS_HIS_TYPE_DENTISTRY.equals(params.getHisType())) {
            this.prescriptionFuture = CompletableFuture.supplyAsync(() ->
                    mapper.selectPersonalPrescription(TableUtils.getCisTable(), params), cacheExecutorService);
            //配镜处方
            this.opticianPrescriptionFuture = CompletableFuture.supplyAsync(() ->
                    mapper.selectOpticianPrescription(TableUtils.getCisTable(), params), cacheExecutorService);
        }
        fetchClassifyFuture(params);
        fetchDimensionFuture(params);
    }

    /**
     * 触发future，查询分类数据
     *
     * @param params 参数
     */
    private void fetchClassifyFuture(AchievementChargePersonnelReqParams params) {
        if (params.getFee2() != null) {
            classify2Future = CompletableFuture.supplyAsync(() ->
                    mapper.selectPersonalFeeClassify(
                            TableUtils.getCisTable(),
                            params,
                            null,
                            params.getFee2(),
                            CLASSIFY_LEVEL_2), cacheExecutorService);
            listFutureFeeSecondClassifyTotal = CompletableFuture.supplyAsync(() -> mapper.selectFeeSecondClassify(
                    TableUtils.getCisTable(),
                    new FeeSecondClassifyParam(params.getChainId(),
                            params.getClinicId(),
                            params.getDepartmentId(),
                            params.getEmployeeSql(),
                            params.getCopyWriterId(),
                            params.getIsContainOthersWriterAchievement(),
                            params.getIncludeWriter(),
                            params.getBeginDate(),
                            params.getEndDate(),
                            null,
                            params.getFee2(),
                            params.getIncludeReg(),
                            params.getHisType(),
                            params.getEmployeeTypeNumber(),
                            params.getConfig())), cacheExecutorService);
            listFutureDispensingFeeSecondClassifyTotal = CompletableFuture.supplyAsync(() -> mapper.selectDispensingFeeSecondClassify(
                    TableUtils.getCisTable(),
                    new DispensingFeeSecondClassifyParam(params.getChainId(),
                            params.getClinicId(),
                            params.getDepartmentId(),
                            params.getDispensingEmployeeSql(),
                            params.getCopyWriterId(),
                            params.getIsContainOthersWriterAchievement(),
                            params.getIncludeWriter(),
                            params.getBeginDate(),
                            params.getEndDate(),
                            null,
                            params.getFee2(),
                            params.getHisType(),
                            params.getConfig())), cacheExecutorService);
        }
        if (params.getFee1() != null || params.getFee2() == null) {
            classify1Future = CompletableFuture.supplyAsync(() -> mapper.selectPersonalFeeClassify(
                    TableUtils.getCisTable(),
                    params,
                    params.getFee1(),
                    null,
                    CLASSIFY_LEVEL_1));
            listFutureFeeFirstClassifyTotal = CompletableFuture.supplyAsync(() -> mapper.selectFeeFirstClassify(
                    TableUtils.getCisTable(),
                    new FeeFirstClassifyParam(params.getChainId(),
                            params.getClinicId(),
                            params.getDepartmentId(),
                            params.getEmployeeSql(),
                            params.getCopyWriterId(),
                            params.getBeginDate(),
                            params.getEndDate(),
                            params.getFee1(),
                            null,
                            params.getIncludeReg(),
                            params.getIsContainOthersWriterAchievement(),
                            params.getIncludeWriter(),
                            params.getHisType(),
                            params.getEmployeeTypeNumber(),
                            params.getConfig())), cacheExecutorService);
            listFutureDispensingFeeFirstClassifyTotal = CompletableFuture.supplyAsync(() -> mapper.selectDispensingFeeFirstClassify(
                    TableUtils.getCisTable(),
                    new DispensingFeeFirstClassifyParam(params.getChainId(),
                            params.getClinicId(),
                            params.getDepartmentId(),
                            params.getDispensingEmployeeSql(),
                            params.getCopyWriterId(),
                            params.getIsContainOthersWriterAchievement(),
                            params.getIncludeWriter(),
                            params.getBeginDate(),
                            params.getEndDate(),
                            params.getFee1(),
                            null,
                            params.getHisType(),
                            params.getConfig())), cacheExecutorService);
        }
        if ("100".equals(params.getHisType())) {
            adviceClassifyFuture = CompletableFuture.supplyAsync(() -> mapper.selectAdvicePersonalFeeClassify(
                    TableUtils.getCisTable(),
                    params), cacheExecutorService);
            listFutureAdviceFeeClassifyTotal = CompletableFuture.supplyAsync(() ->
                    mapper.selectAdviceFeeClassify(TableUtils.getCisTable(),
                            new AchievementChargeAdviceFeeClassifyReqParams(params.getChainId(),
                                    params.getClinicId(),
                                    params.getDepartmentId(),
                                    params.getEmployeeId(),
                                    params.getCopyWriterId(),
                                    params.getBeginDate(),
                                    params.getEndDate(),
                                    params.getFeeTypeIds(),
                                    params.getIncludeReg(),
                                    params.getIsContainOthersWriterAchievement(),
                                    params.getIncludeWriter(),
                                    params.getHisType(),
                                    params.getEmployeeTypeNumber(),
                                    params.getConfig())), cacheExecutorService);
        }
    }

    /**
     * 触发future，查询维度数据
     *
     * @param params 参数
     */
    private void fetchDimensionFuture(AchievementChargePersonnelReqParams params) {
        this.listFutureOrgan = CompletableFuture.supplyAsync(() -> {
            return query.queryOrganByParentId(params.getChainId());
        }, cacheExecutorService);
        this.listFutureEmployee = CompletableFuture.supplyAsync(() -> {
            return query.queryEmployeeByChainId(params.getChainId());
        }, cacheExecutorService);
        this.listFutureCustomType = CompletableFuture.supplyAsync(() -> {
            return query.queryProductCustomTypeTextByChainId(params.getChainId());
        }, cacheExecutorService);
        this.listFutureAdviceFeeType = CompletableFuture.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        }, cacheExecutorService);
    }

    /**
     * 合并发药成本，并处理只有发药没有实收的场景
     *
     * @param baseList 收费数据
     * @param costList 发药数据
     * @return map
     */
    public Map<String, AchievementPerson> mergeDispensingCost(List<AchievementChargePersonnelBase> baseList,
                                                              List<AchievementChargePersonnelDispensingCost> costList) {
        Map<String, AchievementPerson> map = new HashMap<>();
        for (AchievementChargePersonnelBase base : baseList) {
            AchievementPerson person = new AchievementPerson();
            // 人员
            if (base != null) {
                person.setBaseField(employeeMap, organMap, base);
                String key = person.getChainId() + "-" + person.getClinicId() + "-"
                        + person.getPersonnelId() + "-" + person.getEmployeeName() + "-" + person.getIsWriter();
                if (map.containsKey(key)) {
                    AchievementPerson existPerson = map.get(key);
                    existPerson.setPatientCount(existPerson.getPatientCount() + person.getPatientCount());
                    existPerson.setTransFormPatientCount(existPerson.getTransFormPatientCount() + person.getTransFormPatientCount());
                } else {
                    map.put(key, person);
                }
            }
        }
        List<AchievementChargePersonnelDispensingCost> dispensingCostList = new ArrayList<>();
        // 根据人员分组，计算成本
        groupCost(dispensingCostList, costList);
        for (AchievementChargePersonnelDispensingCost dispensingCost : dispensingCostList) {
            AchievementPerson person = null;
            String key = dispensingCost.getChainId() + "-" + dispensingCost.getClinicId() + "-"
                    + dispensingCost.getEmployeeId() + "-" + dispensingCost.getEmployeeName() + "-" + dispensingCost.getIsCopywriter();
            if (map.containsKey(key)) {
                person = map.get(key);
                person.setHoverCode(MathUtil.bitRepresentation(0, dispensingCost.getHoverCode()));
            } else {
                person = new AchievementPerson();
                person.setCostField(organMap, dispensingCost);
                person.setPatientCount(0L);
                String key1 = person.getChainId() + "-" + person.getClinicId() + "-" + person.getPersonnelId() + "-" + person.getEmployeeName() + "-" + person.getIsWriter();
                person.setHoverCode(MathUtil.bitRepresentation(1, dispensingCost.getHoverCode()));
                map.put(key1, person);
            }
            if (person.getCost() == null || person.getCost().compareTo(BigDecimal.ZERO) == 0) {
                person.setCost(dispensingCost.getCostPrice());
            } else {
                person.setCost(person.getCost().add(dispensingCost.getCostPrice()));
            }
        }
        return map;
    }

    /**
     * 根据人员分组，计算成本
     *
     * @param dispensingCostList -
     * @param costList           -
     */
    public void groupCost(List<AchievementChargePersonnelDispensingCost> dispensingCostList,
                          List<AchievementChargePersonnelDispensingCost> costList) {
        HashMap<String, AchievementChargePersonnelDispensingCost> groupMap = new HashMap<>();
        for (AchievementChargePersonnelDispensingCost dispensingCost : costList) {
            dispensingCost.setEmployeeName(handleEmployeeName(dispensingCost.getEmployeeName(), dispensingCost.getEmployeeId(), dispensingCost.getIsCopywriter()));
            String key = dispensingCost.getChainId() + "-" + dispensingCost.getClinicId() + "-"
                    + dispensingCost.getEmployeeId() + "-" + dispensingCost.getEmployeeName() + "-" + dispensingCost.getIsCopywriter();
            AchievementChargePersonnelDispensingCost personnelDispensingCost = null;
            if (groupMap.get(key) != null) {
                personnelDispensingCost = JSON.parseObject(JSON.toJSONString(groupMap.get(key)),
                        AchievementChargePersonnelDispensingCost.class);
            }
            if (personnelDispensingCost != null) {
                BigDecimal costPrice = personnelDispensingCost.getCostPrice();
                if (costPrice == null) {
                    costPrice = new BigDecimal("0.0");
                }
                if (dispensingCost.getCostPrice() != null) {
                    costPrice = costPrice.add(dispensingCost.getCostPrice());
                }
                //需要重新的对象地址，不然costList里面的数据会跟着变
                personnelDispensingCost.setCostPrice(costPrice);
                groupMap.put(key, personnelDispensingCost);
            } else {
                groupMap.put(key, dispensingCost);
            }
        }
        dispensingCostList.addAll(groupMap.values());
    }

    /**
     * 合并实收和非发药成本
     *
     * @param map        base数据
     * @param amountList 实收
     * @param dto        计提配置
     */
    public void mergeAmount(Map<String, AchievementPerson> map,
                            List<AchievementChargePersonalFeeAmountEntity> amountList,
                            StatConfigDto dto) {
        for (AchievementChargePersonalFeeAmountEntity cost : amountList) {
            cost.setEmployeeName(handleEmployeeName(cost.getEmployeeName(), cost.getPersonnelId(), cost.getIsCopywriter()));
            String key = cost.getChainId() + "-" + cost.getClinicId() + "-"
                    + cost.getPersonnelId() + "-" + cost.getEmployeeName() + "-" + cost.getIsCopywriter();
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                person.setCost(person.getCost() != null ? person.getCost().add(cost.getCostPrice())
                        : cost.getCostPrice());
                person.setAmount(person.getAmount() != null ? person.getAmount().add(cost.getReceivedPrice())
                        : cost.getReceivedPrice());
                person.setOrigin(person.getOrigin() != null ? person.getOrigin().add(cost.getOriginPrice())
                        : cost.getOriginPrice());
                person.setDeduct(person.getDeduct() != null ? person.getDeduct().add(cost.getDeductPrice())
                        : cost.getDeductPrice());
                // 计提金额
                person.setCommissionAmount(StatConfigHandler.getCommissionAmount(dto, person.getOrigin(),
                        person.getAmount(), null, person.getDeduct()));
                if (person.getPatientCount() != null && person.getPatientCount() != 0) {
                    person.setAvgPatientAmount(person.getCommissionAmount().
                            divide(new BigDecimal(person.getPatientCount()), PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP));
                }
                BigDecimal profit;
                BigDecimal gross = person.getCommissionAmount().subtract(person.getCost());
                if (person.getCommissionAmount().compareTo(BigDecimal.ZERO) == 0) {
                    profit = BigDecimal.ONE.negate();
                } else {
                    profit = gross.divide(person.getCommissionAmount(), PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP);
                }
                person.setGross(gross);
                person.setProfit(profit);
                person.setFeeTypes(new ArrayList<>());
            }
        }
    }

    /**
     * 转换收费人次 -
     * @param map -
     * @param transFormPatientCountInfos -
     */
    private void mergeTransformPatientCount(Map<String, AchievementPerson> map, List<AchievementChargePersonnelBase> transFormPatientCountInfos) {
        for (AchievementChargePersonnelBase base : transFormPatientCountInfos) {
            base.setEmployeeName(handleEmployeeName(base.getEmployeeName(), base.getPersonnelId(), base.getIsCopywriter()));
            String key = base.getChainId() + "-" + base.getClinicId() + "-" + base.getPersonnelId() + "-"
                    + base.getEmployeeName() + "-" + base.getIsCopywriter();
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                person.setTransFormPatientCount(person.getTransFormPatientCount() != null
                        ? person.getTransFormPatientCount() + base.getTransFormPatientCount()
                        : base.getTransFormPatientCount());
            }
        }
    }


    /**
     * 将费用分类拉平，并且合并
     *
     * @param map           base数据
     * @param classify1List 一级分类
     * @param classify2List 二级分类
     * @param customTypeMap 自定义分类
     * @param dto           计提配置
     * @param costList      成本信息
     */
    public void mergeAndFlatFeeClassify(
            Map<String, AchievementPerson> map,
            List<AchievementChargePersonalFeeAmountEntity> classify1List,
            List<AchievementChargePersonalFeeAmountEntity> classify2List,
            Map<Integer, V2GoodsCustomType> customTypeMap,
            List<AchievementChargePersonnelDispensingCost> costList, StatConfigDto dto,
            String hisType) {
        HashMap<String, BigDecimal> firstFeeCostMap = new HashMap<>();
        HashMap<String, BigDecimal> secondFeeCostMap = new HashMap<>();
        // 获取一二级分类成本map
        getFeeCostMap(firstFeeCostMap, secondFeeCostMap, costList);
        for (AchievementChargePersonalFeeAmountEntity fee1 : classify1List) {
            fee1.setEmployeeName(handleEmployeeName(fee1.getEmployeeName(), fee1.getPersonnelId(), fee1.getIsCopywriter()));
            String key = fee1.getChainId() + "-" + fee1.getClinicId() + "-" + fee1.getPersonnelId() + "-" + fee1.getEmployeeName() + "-" + fee1.getIsCopywriter();
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                AchievementChargeDataFee fee = new AchievementChargeDataFee();
                getFirstFeeType(query, fee, fee1, firstFeeCostMap, dto, hisType);
                if (person.getFeeTypes() != null) {
                    person.getFeeTypes().add(fee);
                } else {
                    List<AchievementChargeDataFee> feeList = new ArrayList<>();
                    feeList.add(fee);
                    person.setFeeTypes(feeList);
                }
            }
        }
        for (AchievementChargePersonalFeeAmountEntity fee2 : classify2List) {
            fee2.setEmployeeName(handleEmployeeName(fee2.getEmployeeName(), fee2.getPersonnelId(), fee2.getIsCopywriter()));
            String key = fee2.getChainId() + "-" + fee2.getClinicId() + "-" + fee2.getPersonnelId() + "-" + fee2.getEmployeeName() + "-" + fee2.getIsCopywriter();
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                if (fee2.getClassifyLevel2() != null) {
                    AchievementChargeDataFee fee = new AchievementChargeDataFee();
                    String fee1 = query.queryProductClassifyLevel1(fee2.getChainId(), fee2.getClassifyLevel1(), hisType);
                    if (!AchievementChargeService.NOT_ADD_UNSPECIFIED_SECONNDARY_CLASSIFY_LIST.contains(fee1)) {
                        getSecondFeeType(fee, fee2, secondFeeCostMap, customTypeMap, dto);
                    }
                    if (person.getFeeTypes() != null) {
                        person.getFeeTypes().add(fee);
                    } else {
                        List<AchievementChargeDataFee> feeList = new ArrayList<>();
                        feeList.add(fee);
                        person.setFeeTypes(feeList);
                    }
                }
            }
        }

        if (!firstFeeCostMap.isEmpty()) {
            processFeeCostMapEntries(firstFeeCostMap, map, new HashMap<>(), true, hisType);
        }

        if (!secondFeeCostMap.isEmpty()) {
            processFeeCostMapEntries(secondFeeCostMap, map, customTypeMap, false, hisType);
        }
    }

    /**
     * 处理只有发药的费用分类
     *
     * @param feeCostMap    -
     * @param map           -
     * @param customTypeMap -
     * @param isFirstLevel  -
     */
    private void processFeeCostMapEntries(Map<String, BigDecimal> feeCostMap, Map<String, AchievementPerson> map,
                                          Map<Integer, V2GoodsCustomType> customTypeMap, boolean isFirstLevel,
                                          String hisType) {
        for (Map.Entry<String, BigDecimal> entry : feeCostMap.entrySet()) {
            String[] split = entry.getKey().split("#");
            BigDecimal cost = entry.getValue();
            String key = split[0] + "-" + split[1] + "-" + split[2] + "-" + split[3] + "-" + split[4];
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                AchievementChargeDataFee fee = new AchievementChargeDataFee();
                if (isFirstLevel) {
                    fee.setName(query.queryProductClassifyLevel1(split[0], split[5], hisType));
                    fee.setField(split[5]);
                } else {
                    V2GoodsCustomType customType = customTypeMap.get(Integer.parseInt(split[6]));
                    fee.setName(customType != null ? customType.getName() : "未指定");
                    fee.setField(split[5] + "/" + split[6]);
                }
                fee.setCost((cost != null ? cost : BigDecimal.ZERO));
                fee.setGross(fee.getCommissionAmt().subtract(fee.getCost()));
                if (person.getFeeTypes() != null) {
                    person.getFeeTypes().add(fee);
                } else {
                    List<AchievementChargeDataFee> feeList = new ArrayList<>();
                    feeList.add(fee);
                    person.setFeeTypes(feeList);
                }
            }
        }
    }

    /**
     * 将医嘱费用分类拉平，并且合并
     *
     * @param map                base数据
     * @param adviceClassifyList 费用分类
     * @param costList           -
     * @param feeTypeMap         费用分类
     * @param dto                计提配置
     * @param costList           成本信息
     */
    public void mergeAndFlatAdviceFeeClassify(
            Map<String, AchievementPerson> map,
            List<AchievementChargePersonalFeeAmountEntity> adviceClassifyList,
            List<AchievementChargePersonnelDispensingCost> costList,
            Map<Long, V2GoodsFeeType> feeTypeMap,
            StatConfigDto dto) {
        Map<String, BigDecimal> adviceFeeCostMap = new HashMap<>();
        // 获取费用分类成本map
        for (AchievementChargePersonnelDispensingCost dispensingCost : costList) {
            String key = dispensingCost.getChainId() + "-" + dispensingCost.getClinicId() + "-"
                    + dispensingCost.getEmployeeId() + "-" + dispensingCost.getEmployeeName() + "-" + dispensingCost.getIsCopywriter()
                    + "-" + dispensingCost.getFeeTypeId();
            BigDecimal feeCost = (dispensingCost.getCostPrice() != null ? dispensingCost.getCostPrice()
                    : new BigDecimal("0.0")).add(adviceFeeCostMap.getOrDefault(key, new BigDecimal("0.0")));
            adviceFeeCostMap.put(key, feeCost);
        }

        for (AchievementChargePersonalFeeAmountEntity fee : adviceClassifyList) {
            fee.setEmployeeName(handleEmployeeName(fee.getEmployeeName(), fee.getPersonnelId(), fee.getIsCopywriter()));
            String key = fee.getChainId() + "-" + fee.getClinicId() + "-" + fee.getPersonnelId() + "-" + fee.getEmployeeName() + "-" + fee.getIsCopywriter();
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                AchievementChargeDataFee dataFee = new AchievementChargeDataFee();
                getAdviceFeeType(feeTypeMap, dataFee, fee, adviceFeeCostMap, dto);
                if (person.getFeeTypes() != null) {
                    person.getFeeTypes().add(dataFee);
                } else {
                    List<AchievementChargeDataFee> feeList = new ArrayList<>();
                    feeList.add(dataFee);
                    person.setFeeTypes(feeList);
                }
            }
        }
    }

    /**
     * 获取一二级分类成本等信息
     *
     * @param firstFeeCostMap  一级分类成本
     * @param secondFeeCostMap 二级分类成本
     * @param costList         成本list
     */
    public void getFeeCostMap(Map<String, BigDecimal> firstFeeCostMap,
                              Map<String, BigDecimal> secondFeeCostMap,
                              List<AchievementChargePersonnelDispensingCost> costList) {
        for (AchievementChargePersonnelDispensingCost dispensingCost : costList) {
            dispensingCost.setEmployeeName(handleEmployeeName(dispensingCost.getEmployeeName(), dispensingCost.getEmployeeId(), dispensingCost.getIsCopywriter()));
            String firstFeeKey = dispensingCost.getChainId() + "#" + dispensingCost.getClinicId() + "#"
                    + dispensingCost.getEmployeeId() + "#" + dispensingCost.getEmployeeName() + "#" + dispensingCost.getIsCopywriter()
                    + "#" + dispensingCost.getClassifyLevel1();
            String secondFeeKey = dispensingCost.getChainId() + "#" + dispensingCost.getClinicId() + "#" + dispensingCost.getEmployeeId()
                    + "#" + dispensingCost.getEmployeeName() + "#" + dispensingCost.getIsCopywriter()
                    + "#" + dispensingCost.getClassifyLevel1() + "#" + dispensingCost.getClassifyLevel2();

            BigDecimal costPrice = dispensingCost.getCostPrice() != null ? dispensingCost.getCostPrice() : BigDecimal.ZERO;
            BigDecimal firstCost = firstFeeCostMap.getOrDefault(firstFeeKey, BigDecimal.ZERO).add(costPrice);
            BigDecimal secondCost = secondFeeCostMap.getOrDefault(secondFeeKey, BigDecimal.ZERO).add(costPrice);
            firstFeeCostMap.put(firstFeeKey, firstCost);
            secondFeeCostMap.put(secondFeeKey, secondCost);
        }
    }


    /**
     * 拉平处方类型，并合并
     *
     * @param map                  base数据
     * @param prescriptionList     处方 (和门诊处保持一致 中西成药，输注，外治，中药饮片，中药颗粒)
     * @param prescriptionMap      处方map
     * @return 数据中有的处方，处方header
     */
    public List<Byte> mergeAndFlatPrescription(
            Map<String, AchievementPerson> map,
            List<AchievementChargePersonalPrescriptionEntity> prescriptionList,
            List<AchievementChargePersonalPrescriptionEntity> opticianPrescriptionList,
            Map<Byte, String> prescriptionMap) {
        List<Byte> prescriptionNames = new ArrayList<>();
        for (AchievementChargePersonalPrescriptionEntity prescription : prescriptionList) {
            if (prescription == null) {
                continue;
            }
            prescription.setEmployeeName(handleEmployeeName(prescription.getEmployeeName(), prescription.getPersonnelId(), prescription.getIsCopywriter()));
            String key = prescription.getChainId() + "-" + prescription.getClinicId() + "-"
                    + prescription.getPersonnelId() + "-" + prescription.getEmployeeName() + "-" + prescription.getIsCopywriter();
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                AchievementKeyValuePojo kv = getKeyValuePojo(
                        String.valueOf(prescription.getPrescriptionType()),
                        String.valueOf(prescription.getCount()));

                if (person.getPrescriptions() == null) {
                    List<AchievementKeyValuePojo> list = new ArrayList<>();
                    list.add(kv);
                    person.setPrescriptions(list);
                } else {
                    person.getPrescriptions().add(kv);
                }

                if (prescriptionMap.containsKey(prescription.getPrescriptionType())
                        && !prescriptionNames.contains(prescription.getPrescriptionType())) {
                    prescriptionNames.add(prescription.getPrescriptionType());
                }
            }
        }
        setWestAndEyePrescription(map, opticianPrescriptionList, prescriptionNames, EYEGLASS_PRESCRIPTION);
        return prescriptionNames;
    }

    /**
     * 处理配镜处方，西药处方
     *
     * @param map                  -
     * @param westPrescriptionList -
     * @param prescriptionNames    -
     * @param prescriptionType     -
     */
    private void setWestAndEyePrescription(Map<String, AchievementPerson> map,
                                           List<AchievementChargePersonalPrescriptionEntity> westPrescriptionList,
                                           List<Byte> prescriptionNames,
                                           String prescriptionType) {
        for (AchievementChargePersonalPrescriptionEntity west : westPrescriptionList) {
            if (west == null) {
                continue;
            }
            west.setEmployeeName(handleEmployeeName(west.getEmployeeName(), west.getPersonnelId(), west.getIsCopywriter()));
            String key = west.getChainId() + "-" + west.getClinicId() + "-"
                    + west.getPersonnelId() + "-" + west.getEmployeeName() + "-" + west.getIsCopywriter();
            AchievementPerson person = map.getOrDefault(key, null);
            if (person != null) {
                AchievementKeyValuePojo kv = getKeyValuePojo(
                        prescriptionType,
                        west.getCount()
                );
                if (person.getPrescriptions() == null) {
                    List<AchievementKeyValuePojo> list = new ArrayList<>();
                    list.add(kv);
                    person.setPrescriptions(list);
                } else {
                    person.getPrescriptions().add(kv);
                }
            }
            if (!prescriptionNames.contains(Byte.valueOf(prescriptionType))) {
                prescriptionNames.add(Byte.valueOf(prescriptionType));
            }
        }
    }

    /**
     * 从内存中查询一级分类，并set到对象中
     *
     * @param query  查询对象
     * @param fee    set对象
     * @param detail 分类数据
     */
    public void setFeeType1(DimensionQuery query, AchievementChargeDataFee fee,
                            AchievementChargePersonalFeeAmountEntity detail,
                            String hisType) {
        fee.setName(query.queryProductClassifyLevel1(detail.getChainId(), detail.getClassifyLevel1(), hisType));

        fee.setField(detail.getClassifyLevel1());
        fee.setValue(detail.getReceivedPrice());
    }

    /**
     * 填充一级分类金额信息
     *
     * @param query           查询对象
     * @param fee             set对象
     * @param detail          分类数据
     * @param firstFeeCostMap 一级分类数据
     * @param dto             -
     */
    public void getFirstFeeType(DimensionQuery query, AchievementChargeDataFee fee,
                                AchievementChargePersonalFeeAmountEntity detail,
                                HashMap<String, BigDecimal> firstFeeCostMap,
                                StatConfigDto dto, String hisType) {
        fee.setName(query.queryProductClassifyLevel1(detail.getChainId(), detail.getClassifyLevel1(), hisType));
        fee.setField(detail.getClassifyLevel1());
        String firstFeeKey = detail.getChainId() + "#" + detail.getClinicId() + "#" + detail.getPersonnelId() + "#"
                + detail.getEmployeeName() + "#" + detail.getIsCopywriter() + "#" + detail.getClassifyLevel1();
        BigDecimal cost = firstFeeCostMap.get(firstFeeKey);
        firstFeeCostMap.remove(firstFeeKey);
        fee.setCost((cost != null ? cost : BigDecimal.ZERO).add(detail.getCostPrice()));
        fee.setOrigin(detail.getOriginPrice());
        fee.setDeduct(detail.getDeductPrice());
        fee.setValue(detail.getReceivedPrice());
        // 计提金额
        fee.setCommissionAmt(StatConfigHandler.getCommissionAmount(dto, fee.getOrigin(),
                fee.getValue(), null, fee.getDeduct()));
        fee.setGross(fee.getCommissionAmt().subtract(fee.getCost()));
    }

    /**
     * 填充二级分类金额信息
     *
     * @param fee              set对象
     * @param detail           分类数据
     * @param secondFeeCostMap 二级分类数据
     * @param customTypeMap    customTypeMap
     * @param dto              -
     */
    public void getSecondFeeType(AchievementChargeDataFee fee,
                                 AchievementChargePersonalFeeAmountEntity detail,
                                 HashMap<String, BigDecimal> secondFeeCostMap,
                                 Map<Integer, V2GoodsCustomType> customTypeMap, StatConfigDto dto) {
        String secondFeeKey = detail.getChainId() + "#" + detail.getClinicId() + "#" + detail.getPersonnelId() + "#"
                + detail.getEmployeeName() + "#" + detail.getIsCopywriter()
                + "#" + detail.getClassifyLevel1() + "#" + detail.getClassifyLevel2();
        V2GoodsCustomType customType = customTypeMap.get(detail.getClassifyLevel2());
        fee.setName(customType != null ? customType.getName() : "未指定");
        fee.setField(detail.getClassifyLevel1() + "/" + detail.getClassifyLevel2());
        BigDecimal cost = secondFeeCostMap.get(secondFeeKey);
        secondFeeCostMap.remove(secondFeeKey);
        fee.setCost((cost != null ? cost : BigDecimal.ZERO).add(detail.getCostPrice()));
        fee.setOrigin(detail.getOriginPrice());
        fee.setDeduct(detail.getDeductPrice());
        fee.setValue(detail.getReceivedPrice());
        // 计提金额
        fee.setCommissionAmt(StatConfigHandler.getCommissionAmount(dto, fee.getOrigin(),
                fee.getValue(), null, fee.getDeduct()));
        fee.setGross(fee.getCommissionAmt().subtract(fee.getCost()));
    }

    /**
     * 填充医嘱费用分类金额信息
     *
     * @param feeTypeMap       -
     * @param fee              set对象
     * @param detail           分类数据
     * @param adviceFeeCostMap 分类数据
     * @param dto              -
     */
    public void getAdviceFeeType(Map<Long, V2GoodsFeeType> feeTypeMap, AchievementChargeDataFee fee,
                                 AchievementChargePersonalFeeAmountEntity detail,
                                 Map<String, BigDecimal> adviceFeeCostMap,
                                 StatConfigDto dto) {
        if (feeTypeMap.containsKey(detail.getFeeTypeId())) {
            fee.setName(feeTypeMap.getOrDefault(detail.getFeeTypeId(), new V2GoodsFeeType()).getName());
        }
        fee.setField(detail.getFeeTypeId() == null || detail.getFeeTypeId() == 0 ? "-1" : detail.getFeeTypeId().toString());
        String feeKey = detail.getChainId() + "-" + detail.getClinicId() + "-"
                + detail.getPersonnelId() + "-" + detail.getEmployeeName() + "-" + detail.getIsCopywriter() + "-" + detail.getFeeTypeId();
        BigDecimal cost = adviceFeeCostMap.get(feeKey);
        fee.setCost((cost != null ? cost : BigDecimal.ZERO).add(detail.getCostPrice()));
        fee.setOrigin(detail.getOriginPrice());
        fee.setDeduct(detail.getDeductPrice());
        fee.setValue(detail.getReceivedPrice());
        // 计提金额
        fee.setCommissionAmt(StatConfigHandler.getCommissionAmount(dto, fee.getOrigin(), fee.getValue(), null, fee.getDeduct()));
        fee.setGross(fee.getCommissionAmt().subtract(fee.getCost()));
    }

    /**
     * 人员tab设置默认值
     *
     * @param personList 人员数据
     * @param permission 数据权限
     */
    public void fillPersonnelNullValue(List<AchievementPerson> personList, AbcPermission permission) {
        for (AchievementPerson person : personList) {
            if (person.getCommissionAmount() != null && person.getCost() != null) {
                person.setGross(person.getCommissionAmount().subtract(person.getCost()));
                if (person.getCommissionAmount().compareTo(BigDecimal.ZERO) != 0) {
                    person.setProfit(person.getGross().divide(
                            person.getCommissionAmount(),
                            PRECISION_DEFAULT_VALUE,
                            RoundingMode.HALF_UP
                    ));
                } else if (!person.getCost().equals(AchievementChargeCalaHandler.DEFAULT_VALUE)) {
                    person.setProfit(BigDecimal.ONE.negate());
                }
            } else if (person.getCommissionAmount() == null && person.getCost() != null
                    && person.getCost().compareTo(BigDecimal.ZERO) != 0) {
                person.setCommissionAmount(AchievementChargeCalaHandler.DEFAULT_VALUE);
                person.setGross(person.getCost().negate());
                person.setProfit(BigDecimal.ONE.negate());
            } else if (person.getCommissionAmount() != null && person.getCost() == null
                    && person.getCommissionAmount().compareTo(BigDecimal.ZERO) != 0) {
                person.setGross(person.getCommissionAmount());
                person.setProfit(BigDecimal.ONE);
            } else {
                person.setCommissionAmount(AchievementChargeCalaHandler.DEFAULT_VALUE);
                person.setGross(AchievementChargeCalaHandler.DEFAULT_VALUE);
            }
            // 格式化毛利率
            person.setProfitText();
            if (person.getAvgPatientAmount() == null) {
                person.setAvgPatientAmount(AchievementChargeCalaHandler.DEFAULT_VALUE);
            }
            if (person.getPatientCount() == null) {
                person.setPatientCount(0L);
            }
            if (person.getCommissionAmount() == null) {
                person.setCommissionAmount(AchievementChargeCalaHandler.DEFAULT_VALUE);
            }
            if (!permission.isEnableCost()) {
                person.setCost(null);
            }
            if (!permission.isEnableGross()) {
                person.setGross(null);
                person.setProfit(null);
                person.setProfitText("-");
            }
            person.prettyMoney();
        }
    }

    /**
     * 组装用法和处方的key/value
     *
     * @param name  key
     * @param value value
     * @return AchievementKeyValuePojo
     */
    public AchievementKeyValuePojo getKeyValuePojo(String name, Object value) {
        AchievementKeyValuePojo pojo = new AchievementKeyValuePojo();
        pojo.setName(name);
        pojo.setValue(value);
        return pojo;
    }

    /**
     * 构建prescriptionList
     *
     * @param map    map
     * @param kvList kv
     */
    private void addPrescriptionToMap(Map<String, Map<String, String>> map,
                                      List<AchievementKeyValuePojo> kvList) {
        for (AchievementKeyValuePojo pojo : kvList) {
            Map<String, String> m = new HashMap<>();
            m.put("name", pojo.getName());
            m.put("value", pojo.getName());
            map.put(pojo.getName(), m);
        }
    }

    /**
     * 从baseList中获取employeeId
     *
     * @param baseList base数据
     * @return employeeIds
     */
    public List<String> buildPresonCostEmploeeIdsByPrescription(
            List<AchievementChargePersonnelBase> baseList) {
        List<String> employeeIds = new ArrayList<>();
        for (AchievementChargePersonnelBase entity : baseList) {
            employeeIds.add(entity.getPersonnelId());
        }
        return employeeIds;
    }

    /**
     * 合并发药成本，并处理只有发药没有实收的场景
     *
     * @param chargeFeeAmountList 收费数据
     * @param costList            发药数据
     * @param dto                 -
     * @return map
     */
    public static Map<String, HisAchievementSummaryDAO> mergeHisDispensingCost(List<AchievementChargePersonalFeeAmountEntity> chargeFeeAmountList,
                                                                               List<HisAchievementSummaryDAO> costList,
                                                                               StatConfigDto dto) {
        Map<String, HisAchievementSummaryDAO> baseMap = new HashMap<>();
        if (chargeFeeAmountList != null) {
            for (AchievementChargePersonalFeeAmountEntity amountEntity : chargeFeeAmountList) {
                String key = amountEntity.getChainId() + "-" + amountEntity.getClinicId() + "-" + amountEntity.getPersonnelId()
                        + "-" + amountEntity.getPersonnelSnapId() + "-" + amountEntity.getDepartmentId() + "-" + amountEntity.getFeeTypeId();
                HisAchievementSummaryDAO dao = new HisAchievementSummaryDAO();
                dao.setChainId(amountEntity.getChainId());
                dao.setClinicId(amountEntity.getClinicId());
                dao.setDepartmentId(amountEntity.getDepartmentId());
                dao.setPersonnelId(amountEntity.getPersonnelId());
                dao.setPersonnelSnapId(amountEntity.getPersonnelSnapId());
                dao.setFeeTypeId(amountEntity.getFeeTypeId());
                dao.setCostPrice(amountEntity.getCostPrice());
                dao.setReceivedPrice(StatConfigHandler.getCommissionAmount(dto, amountEntity.getOriginPrice(),
                        amountEntity.getReceivedPrice(), null, amountEntity.getDeductPrice()));
                baseMap.put(key, dao);
            }
        }

        if (costList != null) {
            for (HisAchievementSummaryDAO dao : costList) {
                if (dao != null) {
                    String key = dao.getChainId() + "-" + dao.getClinicId() + "-" + dao.getPersonnelId() + "-" + dao.getPersonnelSnapId()
                            + "-" + dao.getDepartmentId() + "-" + dao.getFeeTypeId();
                    ;
                    if (baseMap.containsKey(key)) {
                        baseMap.get(key).setCostPrice(baseMap.get(key).getCostPrice().add(dao.getCostPrice()));
                    } else {
                        baseMap.put(key, dao);
                    }
                }
            }
        }
        return baseMap;
    }

    /**
     * 处理人员名字
     * @param employeeName -
     * @param employeeId -
     * @param isCopywriter -
     * @return -
     */
    private String handleEmployeeName(String employeeName, String employeeId, Byte isCopywriter) {
        String empName = employeeName;
        if (empName.isEmpty()) {
            empName = EmployeeHandler.handleEmployeeNameById(employeeMap, employeeId);
        }
        empName += (isCopywriter != null && isCopywriter == 1) ? "-代录" : "";
        return empName;
    }

}
