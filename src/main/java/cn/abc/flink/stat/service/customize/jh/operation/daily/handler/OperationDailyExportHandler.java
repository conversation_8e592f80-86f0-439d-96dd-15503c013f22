package cn.abc.flink.stat.service.customize.jh.operation.daily.handler;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.mq.rabbit.send.RabbitMqSendAsyncExport;
import cn.abc.flink.stat.service.customize.pojo.CustomizedResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/22 8:08 下午
 * @modified ljc
 */
@Component
public class OperationDailyExportHandler {
    @Autowired
    private RabbitMqSendAsyncExport rabbitMqSendAsyncExport;
    /**
     * @Description:
     * @param
     * @param response -
     * @param data -
     * @param beginDate -
     * @param endDate -
     * @return
     * @Author: zs
     * @Date: 2022/8/23 09:03
     * @throws IOException -
     */
    public void exportOperationDaily(HttpServletResponse response, CustomizedResp data, String beginDate,
                                     String endDate, String chainId, String clinicId,String employeeId, String loginWay) throws IOException {
        beginDate = beginDate.substring(0, CommonConstants.NUMBER_TEN);
        endDate = endDate.substring(0, CommonConstants.NUMBER_TEN);
        String fileName = "运营日报" + beginDate + "_" + endDate;
        try {
            rabbitMqSendAsyncExport.sendOperatorLogMessages(chainId, clinicId, employeeId, fileName, loginWay);
        } catch (Exception e) {
            e.printStackTrace();
        }

        byte[] bytes = fileName.getBytes(StandardCharsets.UTF_8);
//        fileName = new String(bytes, "ISO8859-1");
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList();
        ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();
        List<String> prop = new ArrayList<>();
        sheet.setName("每日报表");
        sheet.setSheetDefinition(makeExcelHead(data.getHeader(), prop));
        sheet.setData(makeExcelBody(data.getData(), prop));
        sheets.add(sheet);
        ExcelUtils.export(fileName, response, sheets, false);
    }

    /**
     * @Description:
     * @param
     * @param header -
     * @param props -
     * @return
     * @return java.util.List
     * @Author: zs
     * @Date: 2022/8/23 10:16
     */
    private List makeExcelHead(List header, List<String> props) {
        List<List<String>> excel = new ArrayList<>();
        for (Object o : header) {
            Map<String, Object> map = (Map) o;
            String name = (String) map.get("label");
            recursion(excel, map, props, name, new ArrayList<>());
        }
        return excel;
    }

    /**
     * @Description:
     * @param
     * @param data -
     * @param props -
     * @return
     * @return java.util.List
     * @Author: zs
     * @Date: 2022/8/23 10:16
     */
    private List makeExcelBody(List data, List<String> props) {
        List<List<Object>> lines = new ArrayList<>();
        for (Object each : data) {
            Map<String, Object> map = (Map) each;
            List<Object> row = new ArrayList<>();
            for (String prop : props) {
                row.add(map.get(prop));
            }
            lines.add(row);
        }
        return lines;
    }


    /**
     * @Description:
     * @param
     * @param excel -
     * @param map -
     * @param props -
     * @param thisName -
     * @param curHead -
     * @return
     * @Author: zs
     * @Date: 2022/8/23 10:17
     */
    private void recursion(List<List<String>> excel, Map<String, Object> map, List<String> props, String thisName,
                           List<String> curHead) {
        Object child = map.get("children");
        curHead.add(thisName);
        //将最后一层加入属性list
        if (child == null) {
            String prop = (String) map.get("prop");
            props.add(prop);
            excel.add(curHead);
            return;
        }
        // 否则递归
        List children = (List) child;
        for (Object o : children) {
            List<String> subHead = new ArrayList<>(curHead);
            Map<String, Object> subMap = (Map) o;
            String subName = (String) subMap.get("label");
            recursion(excel, subMap, props, subName, subHead);
        }

    }
}
