package cn.abc.flink.stat.service.cis.patient.order.referral.domain;

import cn.abc.flink.stat.common.BeanUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.dto.AmountDto;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2ClinicChainEmployeeSnap;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/12/27
 */
public class PatientOrderReferralCavityPersonnelVo {

    /**
     * 门店id
     */
    private String clinicId;
    private String clinicName;

    /**
     * 转出医生科室id
     */
    private String departmentId;
    /**
     * 转出医生
     */
    private String outDoctorId;
    private String outDoctorName;

    @ApiModelProperty("转出医生id拼接名称")
    private String outDoctorIdConcatName;

    /**
     * 转出医生快照id
     */
    private Long outDoctorSnapId;

    /**
     * 医生id 转入医生加转出医生 使用，分割
     */
    private String doctorId;

    /**
     * 医生id 转入医生加转出医生 使用，分割
     */
    private String doctorSnapId;

    /**
     * 转ru医生
     */
    private String doctorName;

    /**
     * 门诊次数
     */
    private Integer outpatientCounts = 0;

    /**
     * 总转出次数
     */
    private Integer allOutCounts = 0;

    /**
     * 转出率
     */
    private String ratio;

    /**
     * 转出次数
     */
    private Integer outCounts = 0;

    /**
     * 转出成交次数
     */
    private Integer outDealCounts = 0;

    /**
     * 成交金额
     */
    private BigDecimal dealAmount = BigDecimal.ZERO;

    /**
     * patientOrderId
     */
    private List<String> patientOrderIds;

    /**
     * patientOrderId
     */
    private List<String> referralPatientOrderIds;

    /**
     * 治疗方案list
     */
    private List<AmountDto> planAmounts;

    /**
     * 治疗方案 当没有治疗方案时的默认值
     */
    private String treatmentPlan = "-";

    public void add(PatientOrderReferralCavityPersonnelVo vo) {
        if (vo != null) {
            if (vo.getOutCounts() != null) {
                this.setOutCounts(vo.getOutCounts() + this.getOutCounts());
            }
            if (vo.getOutDealCounts() != null) {
                this.setOutDealCounts(this.outDealCounts + vo.getOutDealCounts());
            }
            if (vo.getDealAmount() != null) {
                this.setDealAmount(this.dealAmount.add(vo.getDealAmount()));
            }
        }
    }

    /**
     * @param
     * @param outpatientCounts -
     * @param allOutCounts     -
     * @param employeeMap      -
     * @param organMap         -
     * @return
     * @Description: 设置结果值
     * @Author: zs
     * @Date: 2024/6/29 17:07
     */
    public void set(Integer outpatientCounts, Integer allOutCounts, Map<String, Organ> organMap, int isSum) {
        String ratio = "0%";
        if (allOutCounts != null && outpatientCounts != null && outpatientCounts > 0) {
            BigDecimal redioDec = (new BigDecimal(allOutCounts).divide(new BigDecimal(outpatientCounts), CommonConstants.BIG_DECIMAL_SCALE_LENGTH_FOUR, RoundingMode.HALF_UP)).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            ratio = redioDec.toString() + "%";
        }
        this.outpatientCounts = outpatientCounts;
        this.allOutCounts = allOutCounts;
        this.ratio = ratio;
        Organ organ = organMap.get(this.clinicId);
        if (isSum == 1) {
            this.doctorName = "汇总";
        }
        if (organ != null) {
            this.clinicName = organ.getName();
        }
    }

    /**
     * @param
     * @param vos -
     * @return
     * @Description: 计算最下层合计
     * @Author: zs
     * @Date: 2024/6/29 17:29
     */
    public void summy(Collection<PatientOrderReferralCavityPersonnelVo> vos, Set<Integer> typeSet) {
        this.doctorName = "-";
        this.outDoctorName = "合计";
        this.outDoctorId = "合计";
        this.ratio = "0%";
        if (vos != null && vos.size() > 0) {
            for (PatientOrderReferralCavityPersonnelVo vo : vos) {
                if (vo.getOutpatientCounts() != null) {
                    this.outpatientCounts = this.outpatientCounts + vo.getOutpatientCounts();
                }
                if (vo.getOutCounts() != null) {
                    this.outCounts = this.outCounts + vo.getOutCounts();
                }
                if (vo.getAllOutCounts() != null) {
                    this.allOutCounts = this.allOutCounts + vo.getAllOutCounts();
                }
                if (vo.getOutDealCounts() != null) {
                    this.outDealCounts = this.outDealCounts + vo.getOutDealCounts();
                }
                if (vo.getDealAmount() != null) {
                    this.dealAmount = this.dealAmount.add(vo.getDealAmount());
                }
                if (vo.getPlanAmounts() != null && vo.getPlanAmounts().size() > 0) {
                    vo.getPlanAmounts().stream().forEach(x -> {
                        Object objKey = x.getKey();
                        Integer key = null;
                        if (objKey instanceof Long) {
                            Long logKey = (Long) x.getKey();
                            key = logKey.intValue();
                        } else {
                            key = (Integer) x.getKey();
                        }
                        typeSet.add(key);
                    });
                    if (this.planAmounts == null) {
                        this.planAmounts = new ArrayList<>();
                    }
                    this.planAmounts.addAll(vo.getPlanAmounts());
                }
            }
        }
        if (this.allOutCounts != null && this.outpatientCounts != null && this.outpatientCounts > 0) {
            BigDecimal redioDec = (new BigDecimal(this.allOutCounts).divide(new BigDecimal(this.outpatientCounts), 6, RoundingMode.HALF_UP)).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            ratio = redioDec.toString() + "%";
        }
    }

    /**
     * @param
     * @return
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Description: 对象转map 并将治疗方案打平
     * @Author: zs
     * @Date: 2024/7/13 11:04
     */
    public Map<String, Object> voToMap(PatientOrderReferralParam param) {
        List<AmountDto> planAmounts = this.getPlanAmounts();
        Map<String, Object> voMap = BeanUtils.toMap(PatientOrderReferralCavityPersonnelVo.class, this);
        voMap.put("planAmounts", null);
        if (planAmounts != null && planAmounts.size() > 0) {
            Map<Integer, AmountDto> sumMap = new HashMap<>();
            //将相同类型的方案进行求和
            for (AmountDto dto : planAmounts) {
                Object objKey = dto.getKey();
                Integer key = null;
                if (objKey instanceof Long) {
                    Long logKey = (Long) dto.getKey();
                    key = logKey.intValue();
                } else {
                    key = (Integer) dto.getKey();
                }
                if (sumMap.containsKey(key)) {
                    AmountDto amountDto = sumMap.get(key);
                    if (dto != null && dto.getAllAmount() != null) {
                        amountDto.setAllAmount(amountDto.getAllAmount().add(dto.getAllAmount()));
                    }
                    if (dto != null && dto.getCounts() != null) {
                        amountDto.setCounts(amountDto.getCounts() + dto.getCounts());
                    }
                } else {
                    sumMap.put(key, dto);
                }
            }
            sumMap.values().stream().forEach(x -> {
                if (x.getAllAmount() != null) {
                    if (param.getIsExport() == 1) {
                        voMap.put(x.getKey() + "-amount", x.getAllAmount().setScale(2, RoundingMode.HALF_UP));
                    } else {
                        voMap.put(x.getKey() + "-amount", x.getAllAmount().setScale(2, RoundingMode.HALF_UP).toString());
                    }

                }
                voMap.put(x.getKey() + "-count", x.getCounts());
            });
        }
        return voMap;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public String getDepartmentId() {
        return this.departmentId;
    }

    public String getOutDoctorId() {
        return this.outDoctorId;
    }

    public String getOutDoctorName() {
        return this.outDoctorName;
    }

    public Long getOutDoctorSnapId() {
        return this.outDoctorSnapId;
    }

    public String getDoctorId() {
        return this.doctorId;
    }

    public String getDoctorSnapId() {
        return this.doctorSnapId;
    }

    public String getDoctorName() {
        return this.doctorName;
    }

    public String getRatio() {
        return this.ratio;
    }

    public List<String> getPatientOrderIds() {
        return this.patientOrderIds;
    }

    public List<String> getReferralPatientOrderIds() {
        return this.referralPatientOrderIds;
    }

    public List<AmountDto> getPlanAmounts() {
        return this.planAmounts;
    }


    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public void setOutDoctorId(String outDoctorId) {
        this.outDoctorId = outDoctorId;
    }

    public void setOutDoctorName(String outDoctorName) {
        this.outDoctorName = outDoctorName;
    }

    public void setOutDoctorSnapId(Long outDoctorSnapId) {
        this.outDoctorSnapId = outDoctorSnapId;
    }

    public void setDoctorId(String doctorId) {
        this.doctorId = doctorId;
    }

    public void setDoctorSnapId(String doctorSnapId) {
        this.doctorSnapId = doctorSnapId;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public void setRatio(String ratio) {
        this.ratio = ratio;
    }

    public void setPatientOrderIds(List<String> patientOrderIds) {
        this.patientOrderIds = patientOrderIds;
    }

    public void setReferralPatientOrderIds(List<String> referralPatientOrderIds) {
        this.referralPatientOrderIds = referralPatientOrderIds;
    }

    public void setPlanAmounts(List<AmountDto> planAmounts) {
        this.planAmounts = planAmounts;
    }

    public Integer getOutpatientCounts() {
        return outpatientCounts;
    }

    public void setOutpatientCounts(Integer outpatientCounts) {
        this.outpatientCounts = outpatientCounts;
    }

    public Integer getAllOutCounts() {
        return allOutCounts;
    }

    public void setAllOutCounts(Integer allOutCounts) {
        this.allOutCounts = allOutCounts;
    }

    public Integer getOutCounts() {
        return outCounts;
    }

    public void setOutCounts(Integer outCounts) {
        this.outCounts = outCounts;
    }

    public Integer getOutDealCounts() {
        return outDealCounts;
    }

    public void setOutDealCounts(Integer outDealCounts) {
        this.outDealCounts = outDealCounts;
    }

    public BigDecimal getDealAmount() {
        return dealAmount;
    }

    public void setDealAmount(BigDecimal dealAmount) {
        this.dealAmount = dealAmount;
    }

    public String getTreatmentPlan() {
        return treatmentPlan;
    }

    public void setTreatmentPlan(String treatmentPlan) {
        this.treatmentPlan = treatmentPlan;
    }

    public String getOutDoctorIdConcatName() {
        return outDoctorIdConcatName;
    }

    public void setOutDoctorIdConcatName(String outDoctorIdConcatName) {
        this.outDoctorIdConcatName = outDoctorIdConcatName;
    }
}
