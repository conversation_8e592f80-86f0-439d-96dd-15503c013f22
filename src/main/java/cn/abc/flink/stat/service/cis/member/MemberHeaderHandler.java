package cn.abc.flink.stat.service.cis.member;

import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.request.params.MemberParam;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import io.swagger.annotations.ApiModel;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@ApiModel("会员表头处理器")
public class MemberHeaderHandler {

    /**
     * 表头处理 单店不展示开卡门店
     *
     * @param tableHeaderEmployeeItems 表头
     * @param param                    会员统计param
     * @return 表头
     */
    public List<TableHeaderEmployeeItem> makeExcelHead(List<TableHeaderEmployeeItem> tableHeaderEmployeeItems,
                                                       MemberParam param) {
        if (Integer.parseInt(param.getParams().getClinicType()) == 0) {
            tableHeaderEmployeeItems = tableHeaderEmployeeItems.stream()
                    .filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getLabel().equals("开卡门店"))
                    .collect(Collectors.toList());
        }
        if (param.getDispensaryType().equals(HisTypeEnum.CLINIC.getTypeNumber())) {
            tableHeaderEmployeeItems = tableHeaderEmployeeItems.stream().filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getLabel().equals("期初")
                    && !tableHeaderEmployeeItem.getLabel().equals("期末")).collect(Collectors.toList());
        }
        return tableHeaderEmployeeItems;
    }

    /**
     * 表头处理 单店不展示消费门店
     *
     * @param tableHeaderEmployeeItems 表头
     * @param param                    会员param
     * @return 表头
     */
    public List<TableHeaderEmployeeItem> makeFeeExcelHead(List<TableHeaderEmployeeItem> tableHeaderEmployeeItems,
                                                          MemberParam param) {
        if (param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
            for (TableHeaderEmployeeItem tableHeaderEmployeeItem : tableHeaderEmployeeItems) {
                if (tableHeaderEmployeeItem.getProp().equals("customerName")) {
                    tableHeaderEmployeeItem.setLabel("交易顾客");
                }
                if (tableHeaderEmployeeItem.getProp().equals("sellUserName")) {
                    tableHeaderEmployeeItem.setLabel("销售人");
                }
            }
        }
        if (Integer.parseInt(param.getParams().getClinicType()) == 0) {
            return tableHeaderEmployeeItems.stream()
                    .filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getLabel().equals("消费门店"))
                    .collect(Collectors.toList());
        }
        return tableHeaderEmployeeItems;
    }
}
