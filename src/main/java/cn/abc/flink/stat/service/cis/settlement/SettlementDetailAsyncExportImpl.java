package cn.abc.flink.stat.service.cis.settlement;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.service.cis.outpatient.OutpatientAsynExportService;
import cn.abc.flink.stat.service.cis.settlement.domain.SettlementParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-04-11 13:39
 **/
@Component
public class SettlementDetailAsyncExportImpl implements BaseAsyncExportInterface {
    @Autowired
    private SettlementService settlementService;

    @Override
    public String getKey() {
        return "settlement-detail";
    }

    private static final Logger logger = LoggerFactory.getLogger(OutpatientAsynExportService.class);


    @Override
    public String setFileName(Map<String, Object> params) {
        return "结算明细.xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String supplierId = (String) MapUtils.isExistsAndReturn(params, "supplierId");
        String stockOrderNo = (String) MapUtils.isExistsAndReturn(params, "stockOrderNo");
        String employeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String chainViewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        String hisType = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerHisType"));
        SettlementParam param = new SettlementParam();
        param.initAbcCisBaseQueryParams(chainId, clinicId, employeeId, chainViewMode, clinicNodeType);
        param.initBeginDateAndEndDate(beginDate, endDate);
        param.setSupplierId(supplierId);
        param.setExport(true);
        param.setStockOrderNo(stockOrderNo);
        param.setHisType(hisType);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        List<ExcelUtils.AbcExcelSheet> sheets = settlementService.asyncSettlementDetailExport(param);
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
