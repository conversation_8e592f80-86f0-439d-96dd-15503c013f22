package cn.abc.flink.stat.service.cis.promotion.scrm.pojo;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/18 2:57 下午
 */
public class LiveCodePatient {
    private Long liveCodeId;
    private String liveCodeName;
    private Integer totalCount = 0;
    private Integer notConsumedCount = 0;
    private Integer consumedCount = 0;
    private List<String> patientIds;

    public Long getLiveCodeId() {
        return this.liveCodeId;
    }

    public String getLiveCodeName() {
        return this.liveCodeName;
    }

    public List<String> getPatientIds() {
        return this.patientIds;
    }


    public void setLiveCodeId(Long liveCodeId) {
        this.liveCodeId = liveCodeId;
    }

    public void setLiveCodeName(String liveCodeName) {
        this.liveCodeName = liveCodeName;
    }

    public void setPatientIds(List<String> patientIds) {
        this.patientIds = patientIds;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getNotConsumedCount() {
        return notConsumedCount;
    }

    public void setNotConsumedCount(Integer notConsumedCount) {
        this.notConsumedCount = notConsumedCount;
    }

    public Integer getConsumedCount() {
        return consumedCount;
    }

    public void setConsumedCount(Integer consumedCount) {
        this.consumedCount = consumedCount;
    }
}
