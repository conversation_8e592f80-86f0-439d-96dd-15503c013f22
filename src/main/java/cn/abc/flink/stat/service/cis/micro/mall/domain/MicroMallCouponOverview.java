package cn.abc.flink.stat.service.cis.micro.mall.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.math.BigDecimal;

@ApiModel(value = "微商城-活动效果概览")
public class MicroMallCouponOverview {

    @ApiModelProperty("活动id")
    private Long promotionId;

    @ApiModelProperty("用券总成交额/实付金额")
    private BigDecimal useCouponChargeTotalAmount;

    @ApiModelProperty("优惠券总金额")
    private BigDecimal couponTotalAmount;

    @ApiModelProperty("费效比")
    private BigDecimal costEffectRatio;

    @ApiModelProperty("用券订单数")
    private Long useCouponOrderNumber;

    @ApiModelProperty("用券订单数/收费订单/参与客户")
    private BigDecimal customerOrder;

    public void pretty() {
        if (this.useCouponChargeTotalAmount == null) {
            this.useCouponChargeTotalAmount = BigDecimal.ZERO;
        }
        if (this.couponTotalAmount == null) {
            this.couponTotalAmount = BigDecimal.ZERO;
        }
        if (this.costEffectRatio == null) {
            this.costEffectRatio = BigDecimal.ZERO;
        }
        if (this.useCouponOrderNumber == null) {
            this.useCouponOrderNumber = 0L;
        }
        if (this.customerOrder == null) {
            this.customerOrder = BigDecimal.ZERO;
        }

    }

    public Long getPromotionId() {
        return this.promotionId;
    }

    public BigDecimal getUseCouponChargeTotalAmount() {
        return this.useCouponChargeTotalAmount;
    }

    public BigDecimal getCouponTotalAmount() {
        return this.couponTotalAmount;
    }

    public BigDecimal getCostEffectRatio() {
        return this.costEffectRatio;
    }

    public Long getUseCouponOrderNumber() {
        return this.useCouponOrderNumber;
    }

    public BigDecimal getCustomerOrder() {
        return this.customerOrder;
    }


    public void setPromotionId(Long promotionId) {
        this.promotionId = promotionId;
    }

    public void setUseCouponChargeTotalAmount(BigDecimal useCouponChargeTotalAmount) {
        this.useCouponChargeTotalAmount = useCouponChargeTotalAmount;
    }

    public void setCouponTotalAmount(BigDecimal couponTotalAmount) {
        this.couponTotalAmount = couponTotalAmount;
    }

    public void setCostEffectRatio(BigDecimal costEffectRatio) {
        this.costEffectRatio = costEffectRatio;
    }

    public void setUseCouponOrderNumber(Long useCouponOrderNumber) {
        this.useCouponOrderNumber = useCouponOrderNumber;
    }

    public void setCustomerOrder(BigDecimal customerOrder) {
        this.customerOrder = customerOrder;
    }

}
