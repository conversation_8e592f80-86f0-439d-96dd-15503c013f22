package cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralDaily;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralOverviewSummary;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralOverviewTrend;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralReferrerRewardParam;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralReferrerRewardSelectionResp;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralRpcParam;

import java.util.List;

/**
 * @description: 营销分析-新老客统计service
 * @author: lzq
 * @Date: 2023/03/07
 */
public interface PromotionReferralReferrerRewardService {

    /**
     * 查询筛选框数据
     *
     * @param param 老带新统计param
     * @return 筛选框result
     */
    PromotionReferralReferrerRewardSelectionResp selectSelection(PromotionReferralReferrerRewardParam param) throws Exception;

    /**
     * 查询奖励明细数据
     *
     * @param param 老带新统计param
     * @return 奖励明细result
     */
    V2StatResponse selectRewardDetail(PromotionReferralReferrerRewardParam param) throws Exception;

    /**
     * 查询推荐人业绩数据
     *
     * @param param 老带新统计param
     * @return 推荐人业绩result
     */
    V2StatResponse selectReferrerAchievement(PromotionReferralReferrerRewardParam param) throws Exception;

    /**
     * 异步导出查询奖励明细数据
     *
     * @param param 老带新统计param
     * @return 奖励明细result
     */
    ExcelUtils.AbcExcelSheet asyncRewardDetailExport(PromotionReferralReferrerRewardParam param) throws Exception;

    /**
     * 异步导出查询推荐人业绩数据
     *
     * @param param 老带新统计param
     * @return 推荐人业绩result
     */
    ExcelUtils.AbcExcelSheet asyncReferrerAchievementExport(PromotionReferralReferrerRewardParam param) throws Exception;

    /**
     * 查询活动效果-汇总数据
     *
     * @param param 老带新统计param
     * @return 活动效果-汇总result
     */
    PromotionReferralOverviewSummary selectOverviewSummary(PromotionReferralReferrerRewardParam param) throws Exception;

    /**
     * 查询活动效果-趋势图
     *
     * @param param 老带新统计param
     * @return 活动效果-趋势图result
     */
    PromotionReferralOverviewTrend selectOverviewTrend(PromotionReferralReferrerRewardParam param) throws Exception;

    /**
     * 查询活动效果-新客消费转化分析
     *
     * @param param 老带新统计param
     * @return 活动效果-新客消费转化分析result
     */
    V2StatResponse selectOverviewNewPatientPayTransformAnalysis(PromotionReferralReferrerRewardParam param);

    /**
     * 查询活动日报
     *
     * @param param 老带新统计param
     * @return 活动日报
     */
    List<PromotionReferralDaily> selectDaily(PromotionReferralRpcParam param) throws Exception;

    /**
     * 查询活动列表底部合计行
     *
     * @param param 老带新统计param
     * @return 活动日报
     * @throws Exception e
     */
    PromotionReferralOverviewSummary selectListTotal(PromotionReferralReferrerRewardParam param) throws Exception;
}
