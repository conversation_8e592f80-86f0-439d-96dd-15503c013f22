package cn.abc.flink.stat.service.cis.promotion.activity.entity;



import java.math.BigDecimal;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.promotion.activity.entity
 * @Author: zs
 * @CreateTime: 2024-05-08  10:13
 * @Description: 比率实体类
 * @Version: 1.0
 */
public class RatioEntity {
    /**
     * ID
     */
    private String id;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 分类值
     */
    private BigDecimal value;
    /**
     * 所占比率
     */
    private BigDecimal ratio;

    public String getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public BigDecimal getValue() {
        return this.value;
    }

    public BigDecimal getRatio() {
        return this.ratio;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public void setRatio(BigDecimal ratio) {
        this.ratio = ratio;
    }

}
