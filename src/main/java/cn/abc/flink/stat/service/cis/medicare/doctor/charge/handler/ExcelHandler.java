package cn.abc.flink.stat.service.cis.medicare.doctor.charge.handler;

import cn.abc.flink.stat.service.cis.medicare.doctor.charge.pojo.MedicareDoctorChargeSummaryDto;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

public class ExcelHandler {
    public static List<List<String>> handleHead () {
        List<List<String>> headList = new ArrayList<>();
        headList.add(Lists.newArrayList("医生"));
        headList.add(Lists.newArrayList("人次"));
        headList.add(Lists.newArrayList("人头"));
        headList.add(Lists.newArrayList("人次人头比"));
        headList.add(Lists.newArrayList("医疗费用总额"));
        headList.add(Lists.newArrayList("次均"));
        headList.add(Lists.newArrayList("人均"));
        headList.add(Lists.newArrayList("中药帖均"));
        return headList;
    }

    public  static List<List<Object>> handleData (List<MedicareDoctorChargeSummaryDto> list) {
        List<List<Object>> data = new ArrayList<>();
        list.stream().forEach( dto -> {
            List<Object> raw = new ArrayList<>();
            raw.add(dto.getDoctorName());
            raw.add(dto.getPersonTime());
            raw.add(dto.getPersonCount());
            raw.add(dto.getPersonRate());
            raw.add(dto.getAmount());
            raw.add(dto.getTimeAmountAvg());
            raw.add(dto.getCountAmountAvg());
            raw.add(dto.getDoseAvg());
            data.add(raw);
        });
        return data;
    }
}
