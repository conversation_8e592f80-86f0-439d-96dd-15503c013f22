package cn.abc.flink.stat.service.cis.selection;


import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnAchievementChargeMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresAchievementChargeMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresSelectionMapper;
import cn.abc.flink.stat.db.his.hologres.dao.HoloHisAchievementChargeMapping;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.V2GoodsCustomType;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeAdviceFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.handler.AchievementChargeHandler;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAdviceFeeSelectRsp;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeFeeClassify;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.DispensingFeeFirstClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.DispensingFeeSecondClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.FeeFirstClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.FeeSecondClassifyParam;
import cn.abc.flink.stat.service.cis.config.StatConfigService;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.selection.entity.CommSelectParams;
import cn.abc.flink.stat.service.cis.selection.entity.FeeClassifyParam;
import cn.abc.flink.stat.service.cis.selection.handler.FeeTypeHandler;
import cn.abc.flink.stat.service.cis.selection.pojo.FeeClassifyResp;
import cn.abc.flink.stat.service.his.achievement.charge.domain.HisAchievementChargeReqParams;
import cn.abc.flink.stat.source.AbcThreadExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

/**
 * -FeeClassifyService
 */
@Service
public class FeeClassifyService {

    @Autowired
    private DimensionQuery query;

    @Autowired
    private ExecutorService cacheExecutorService;

    @Autowired
    private StoreUtils storeUtils;

    @Resource
    private HologresAchievementChargeMapper hologresAchievementChargeMapper;

    @Resource
    private ArnAchievementChargeMapper arnMapper;

    @Autowired
    private StatConfigService statConfigService;

    @Resource
    private HoloHisAchievementChargeMapping holoHisAchievementChargeMapping;

    @Resource
    private AbcThreadExecutor executor;

    @Resource
    private FeeTypeHandler feeTypeHandler;

    @Resource
    private HologresSelectionMapper hologresSelectionMapper;

    /**
     * @param params                -
     * @param includeDispensingCost -
     * @param isRevenue             -
     * @return -
     * @throws ExecutionException   -
     * @throws InterruptedException -
     * @throws ParseException       -
     */
    public List<AchievementChargeFeeClassify> selectFeeClassifyFromChargeTransaction(
            FeeClassifyParam params, boolean includeDispensingCost, boolean isRevenue)
            throws ExecutionException, InterruptedException, ParseException {
        String beginDate = params.getBeginDate();
        String endDate = params.getEndDate();
        StatConfigDto config = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId()));
        // 费用分类
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeFirstClassify = CompletableFuture
                .supplyAsync(() -> {
                    return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                            .selectFeeFirstClassify(TableUtils.getCisTable(), new FeeFirstClassifyParam(
                                    params.getChainId(), params.getClinicId(),
                                    null, null, null,
                                    params.getBeginDate(), params.getEndDate(), null, null,
                                    params.getIncludeReg(), null, null, null,
                                    params.getEmployeeType(), config));
                }, cacheExecutorService);
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeSecondClassify = CompletableFuture
                .supplyAsync(() -> {
                    return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                            .selectFeeSecondClassify(TableUtils.getCisTable(),
                                    new FeeSecondClassifyParam(params.getChainId(), params.getClinicId(),
                                            null, null, null, null, null, params.getBeginDate(), params.getEndDate(),
                                            null, null, params.getIncludeReg(), null, params.getEmployeeType(), config));
                }, cacheExecutorService);

        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureDispensingFeeSecondClassify = null;
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureDispensingFeeFirstClassify = null;
        if (includeDispensingCost) {
            listFutureDispensingFeeSecondClassify = CompletableFuture.supplyAsync(() -> {
                return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                        .selectDispensingFeeSecondClassify(TableUtils.getCisTable(),
                                new DispensingFeeSecondClassifyParam(params.getChainId(), params.getClinicId(), null,
                                        null, null, null, null
                                        , params.getBeginDate(), params.getEndDate(), null, null, params.getHisType(), config));
            }, cacheExecutorService);
            listFutureDispensingFeeFirstClassify = CompletableFuture.supplyAsync(() -> {
                return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                        .selectDispensingFeeFirstClassify(TableUtils.getCisTable(),
                                new DispensingFeeFirstClassifyParam(params.getChainId(), params.getClinicId(),
                                        null, null, null, null,
                                        null, params.getBeginDate(), params.getEndDate(), null, null, params.getHisType(), config));
            }, cacheExecutorService);
        }

        // 二级分类
        Map<Integer, V2GoodsCustomType> customTypeMap = query.queryProductCustomTypeTextByChainId(params.getChainId());

        if (includeDispensingCost) {
            CompletableFuture.allOf(listFutureDispensingFeeSecondClassify, listFutureDispensingFeeFirstClassify).join();
        }
        CompletableFuture.allOf(listFutureFeeFirstClassify, listFutureFeeSecondClassify).join();


        List<AchievementChargeFeeEntity> recordFeeFirstClassify = listFutureFeeFirstClassify.get();
        List<AchievementChargeFeeEntity> recordFeeSecondClassify = listFutureFeeSecondClassify.get();
        List<AchievementChargeFeeEntity> dispensingFirstClassify = listFutureDispensingFeeFirstClassify == null
                ? null : listFutureDispensingFeeFirstClassify.get();
        List<AchievementChargeFeeEntity> dispensingSecondClassify = listFutureDispensingFeeSecondClassify == null
                ? null : listFutureDispensingFeeSecondClassify.get();

        HashSet<String> firstSet = new HashSet<>();
        HashSet<String> secondSet = new HashSet<>();

        if (includeDispensingCost) {
            recordFeeFirstClassify.forEach((v) -> firstSet.add(v.getClassifyLevel1Id()));
            recordFeeSecondClassify.forEach((v) -> secondSet
                    .add(v.getClassifyLevel1Id() + "_" + v.getClassifyLevel2Id()));
            dispensingFirstClassify.forEach((v) -> {
                if (!firstSet.contains(v.getClassifyLevel1Id())) {
                    recordFeeFirstClassify.add(v);
                }
            });
            dispensingSecondClassify.forEach((v) -> {
                if (!secondSet.contains(v.getClassifyLevel1Id() + "_" + v.getClassifyLevel2Id())) {
                    recordFeeSecondClassify.add(v);
                }
            });
        }

        return AchievementChargeHandler.buildFeeList(
                query,
                recordFeeFirstClassify,
                recordFeeSecondClassify,
                customTypeMap,
                isRevenue, params.getChainId(), params.getHisType());
    }


    /**
     * 开单业绩专用的费用类型筛选
     *
     * @param params
     * @return
     * @throws Exception
     */
    public List<AchievementChargeFeeClassify> selectAchievementChargeFeeClassify(FeeClassifyParam params) throws Exception {
        String beginDate = params.getBeginDate();
        String endDate = params.getEndDate();
        // 费用分类
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeFirstClassify = CompletableFuture
                .supplyAsync(() -> {
                    return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                            .selectAchievementFeeFirstClassify(TableUtils.getCisTable(), new FeeFirstClassifyParam(
                                    params.getChainId(), params.getClinicId(),
                                    null, null, null,
                                    params.getBeginDate(), params.getEndDate(), null, null,
                                    params.getIncludeReg(), null, null, params.getHisType(),
                                    params.getEmployeeType(), params.getConfig()));
                }, cacheExecutorService);
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeSecondClassify = CompletableFuture
                .supplyAsync(() -> {
                    return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                            .selectAchievementFeeSecondClassify(TableUtils.getCisTable(),
                                    new FeeSecondClassifyParam(params.getChainId(), params.getClinicId(),
                                            null, null, null, null, null,
                                            params.getBeginDate(), params.getEndDate(),
                                            null, null, params.getIncludeReg(), params.getHisType(),
                                            params.getEmployeeType(), params.getConfig()));
                }, cacheExecutorService);

        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureDispensingFeeSecondClassify = null;
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureDispensingFeeFirstClassify = null;
        listFutureDispensingFeeSecondClassify = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                    .selectDispensingFeeSecondClassify(TableUtils.getCisTable(),
                            new DispensingFeeSecondClassifyParam(params.getChainId(), params.getClinicId(), null,
                                    null, null, null, null
                                    , params.getBeginDate(), params.getEndDate(), null, null, params.getHisType(), params.getConfig()));
        }, cacheExecutorService);
        listFutureDispensingFeeFirstClassify = CompletableFuture.supplyAsync(() -> {
            return storeUtils.getMapper(beginDate, endDate, arnMapper, hologresAchievementChargeMapper)
                    .selectDispensingFeeFirstClassify(TableUtils.getCisTable(),
                            new DispensingFeeFirstClassifyParam(params.getChainId(), params.getClinicId(),
                                    null, null, null, null,
                                    null, params.getBeginDate(), params.getEndDate(), null, null,
                                    params.getHisType(), params.getConfig()));
        }, cacheExecutorService);

        // 二级分类
        Map<Integer, V2GoodsCustomType> customTypeMap = query.queryProductCustomTypeTextByChainId(params.getChainId());

        CompletableFuture.allOf(listFutureDispensingFeeSecondClassify, listFutureDispensingFeeFirstClassify).join();

        List<AchievementChargeFeeEntity> recordFeeFirstClassify = listFutureFeeFirstClassify.get();
        List<AchievementChargeFeeEntity> recordFeeSecondClassify = listFutureFeeSecondClassify.get();
        List<AchievementChargeFeeEntity> dispensingFirstClassify = listFutureDispensingFeeFirstClassify == null
                ? null : listFutureDispensingFeeFirstClassify.get();
        List<AchievementChargeFeeEntity> dispensingSecondClassify = listFutureDispensingFeeSecondClassify == null
                ? null : listFutureDispensingFeeSecondClassify.get();

        HashSet<String> firstSet = new HashSet<>();
        HashSet<String> secondSet = new HashSet<>();

        recordFeeFirstClassify.forEach((v) -> firstSet.add(v.getClassifyLevel1Id()));
        recordFeeSecondClassify.forEach((v) -> secondSet
                .add(v.getClassifyLevel1Id() + "_" + v.getClassifyLevel2Id()));
        dispensingFirstClassify.forEach((v) -> {
            if (!firstSet.contains(v.getClassifyLevel1Id())) {
                recordFeeFirstClassify.add(v);
            }
        });
        dispensingSecondClassify.forEach((v) -> {
            if (!secondSet.contains(v.getClassifyLevel1Id() + "_" + v.getClassifyLevel2Id())) {
                recordFeeSecondClassify.add(v);
            }
        });

        return AchievementChargeHandler.buildFeeList(
                query,
                recordFeeFirstClassify,
                recordFeeSecondClassify,
                customTypeMap,
                true, params.getChainId(), params.getHisType());
    }

    public List<AchievementChargeFeeClassify> selectAdviceFromHisChargeProduct(HisAchievementChargeReqParams params) {
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeFirstClassify = CompletableFuture
                .supplyAsync(() -> holoHisAchievementChargeMapping.selectHisAdviceFirstClassifyFromChargeProduct(TableUtils.getHisTable(),
                        params), cacheExecutorService);
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeSecondClassify = CompletableFuture
                .supplyAsync(() -> holoHisAchievementChargeMapping.selectHisAdviceSecondClassifyFromChargeProduct(TableUtils.getHisTable(),
                        params), cacheExecutorService);
        CompletableFuture<Map<Integer, V2GoodsCustomType>> customTypeMapF = CompletableFuture
                .supplyAsync(() -> query.queryProductCustomTypeTextByChainId(params.getChainId()), cacheExecutorService);

        CompletableFuture.allOf(listFutureFeeFirstClassify, listFutureFeeSecondClassify, customTypeMapF).join();

        List<AchievementChargeFeeEntity> recordFeeFirstClassify = new ArrayList<>();
        List<AchievementChargeFeeEntity> recordFeeSecondClassify = new ArrayList<>();
        Map<Integer, V2GoodsCustomType> customTypeMap = new HashMap<>();
        try {
            recordFeeFirstClassify = listFutureFeeFirstClassify.get();
            recordFeeSecondClassify = listFutureFeeSecondClassify.get();
            customTypeMap = customTypeMapF.get();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AchievementChargeHandler.buildFeeList(query, recordFeeFirstClassify, recordFeeSecondClassify,
                customTypeMap, false, params.getChainId(), params.getHisType());
    }

    public List<AchievementChargeFeeClassify> selectAdviceFromChargeSettle(HisAchievementChargeReqParams params) {
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeFirstClassify = CompletableFuture
                .supplyAsync(() -> holoHisAchievementChargeMapping.selectHisAdviceFirstClassifyFromChargeSettle(TableUtils.getHisTable(),
                        params), cacheExecutorService);
        CompletableFuture<List<AchievementChargeFeeEntity>> listFutureFeeSecondClassify = CompletableFuture
                .supplyAsync(() -> holoHisAchievementChargeMapping.selectHisAdviceSecondClassifyFromChargeSettle(TableUtils.getHisTable(),
                        params), cacheExecutorService);
        CompletableFuture<Map<Integer, V2GoodsCustomType>> customTypeMapF = CompletableFuture
                .supplyAsync(() -> query.queryProductCustomTypeTextByChainId(params.getChainId()), cacheExecutorService);

        CompletableFuture.allOf(listFutureFeeFirstClassify, listFutureFeeSecondClassify, customTypeMapF).join();

        List<AchievementChargeFeeEntity> recordFeeFirstClassify = new ArrayList<>();
        List<AchievementChargeFeeEntity> recordFeeSecondClassify = new ArrayList<>();
        Map<Integer, V2GoodsCustomType> customTypeMap = new HashMap<>();
        try {
            recordFeeFirstClassify = listFutureFeeFirstClassify.get();
            recordFeeSecondClassify = listFutureFeeSecondClassify.get();
            customTypeMap = customTypeMapF.get();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AchievementChargeHandler.buildFeeList(query, recordFeeFirstClassify, recordFeeSecondClassify,
                customTypeMap, false, params.getChainId(), params.getHisType());
    }

    public List<FeeClassifyResp> selectChargeFeeType(CommSelectParams params) throws Exception {
        CompletableFuture<Map<Long, V2GoodsFeeType>> goodsFeeTypeF = executor.supplyAsync(() ->
                query.selectAdviceFeeType(params.getChainId()));

        if (params.getScope() == 1) {
            // 结算费用分类
            CompletableFuture<List<Long>> hisSettleFeeTypeF = executor.supplyAsync(() ->
                    selectHisSettleFeeTypeIds(params));
            CompletableFuture.allOf(goodsFeeTypeF, hisSettleFeeTypeF).join();
            return feeTypeHandler.handleFeeTypeData(goodsFeeTypeF.get(), hisSettleFeeTypeF.get());
        }
        return null;
    }
    
    public List<FeeClassifyResp> selectAllChargeFeeType(AbcScStatRequestParams params) throws Exception {
        params.initBeginDateAndEndDate();
        params.initDs();
        CompletableFuture<Map<Long, V2GoodsFeeType>> goodsFeeTypeF = executor.supplyAsync(() -> query.selectAdviceFeeType(params.getChainId()));
        CompletableFuture<List<Long>> feeTypeIdsF = executor.supplyAsync(() -> hologresSelectionMapper.selectAllChargeFeeTypeIds(TableUtils.getHisTable(), TableUtils.getCisTable(), params));
        CompletableFuture.allOf(goodsFeeTypeF, feeTypeIdsF).join();
        return feeTypeHandler.handleFeeTypeData(goodsFeeTypeF.get(), feeTypeIdsF.get());
    }

    private List<Long> selectHisSettleFeeTypeIds(CommSelectParams params) {
        return hologresSelectionMapper.selectHisSettleFeeTypeIds(TableUtils.getHisTable(), params);
    }

    public List<FeeClassifyResp> selectAdviceFeeType(CommSelectParams params) throws Exception {
        CompletableFuture<Map<Long, V2GoodsFeeType>> goodsFeeTypeF = executor.supplyAsync(() ->
                query.selectAdviceFeeType(params.getChainId()));

        if (params.getScope() == 1) {
            // 护理费用分类
            CompletableFuture<List<Long>> hisAdviceExecuteFeeTypeF = executor.supplyAsync(() ->
                    selectHisAdviceExecuteFeeTypeIds(params));
            CompletableFuture.allOf(goodsFeeTypeF, hisAdviceExecuteFeeTypeF).join();
            return feeTypeHandler.handleFeeTypeData(goodsFeeTypeF.get(), hisAdviceExecuteFeeTypeF.get());
        }
        return null;
    }

    private List<Long> selectHisAdviceExecuteFeeTypeIds(CommSelectParams params) {
        return hologresSelectionMapper.selectHisAdviceExecuteFeeTypeIds(TableUtils.getHisTable(), params);
    }

}
