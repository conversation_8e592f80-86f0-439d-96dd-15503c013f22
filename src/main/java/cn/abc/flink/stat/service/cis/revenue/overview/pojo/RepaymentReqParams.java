package cn.abc.flink.stat.service.cis.revenue.overview.pojo;

import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.service.cis.handler.SelectHandler;

import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
public class RepaymentReqParams extends AbcScStatRequestParams {
	private Integer dateType;
	/**
	 * 时间类型 默认为0.按欠费时间筛选，1.按还款时间筛选
	 */
	private String beginDate;
	private String endDate;
	private Integer status;
	private String cashierId;
	private String patientId;
	private String payModes;
	private Integer offset;
	private Integer limit;
	private String  sort;
	private String order;

	private String chargeSheetId;
	private BigInteger oweSheetId;

	private String payModeSql;

	/**
	 * 构建支付方式sql
	 */
	public void setPayModeSql() {
		payModeSql = SelectHandler.buildPayModes(payModes, "coctr.pay_mode", "coctr.pay_sub_mode");
	}

	/**
	 * 处理参数
	 */
	public void handleParams() {
		if (StringUtils.isBlank(cashierId)) {
			cashierId = null;
		}
		if (StringUtils.isBlank(patientId)) {
			patientId = null;
		}
		if (StringUtils.isBlank(payModes)) {
			payModes = null;
		}
		if (StringUtils.isBlank(sort)) {
			sort = null;
		}
		if (StringUtils.isBlank(order)) {
			order = null;
		}
		if (StringUtils.isBlank(chargeSheetId)) {
			chargeSheetId = null;
		}
		if (dateType == null) {
			dateType = 1;
		}
	}

    public Integer getDateType() {
        return this.dateType;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public Integer getStatus() {
        return this.status;
    }

    public String getCashierId() {
        return this.cashierId;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getPayModes() {
        return this.payModes;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public Integer getLimit() {
        return this.limit;
    }

    public String getSort() {
        return this.sort;
    }

    public String getOrder() {
        return this.order;
    }

    public String getChargeSheetId() {
        return this.chargeSheetId;
    }

    public BigInteger getOweSheetId() {
        return this.oweSheetId;
    }

    public String getPayModeSql() {
        return this.payModeSql;
    }


    public void setDateType(Integer dateType) {
        this.dateType = dateType;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setCashierId(String cashierId) {
        this.cashierId = cashierId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setPayModes(String payModes) {
        this.payModes = payModes;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public void setChargeSheetId(String chargeSheetId) {
        this.chargeSheetId = chargeSheetId;
    }

    public void setOweSheetId(BigInteger oweSheetId) {
        this.oweSheetId = oweSheetId;
    }

    public void setPayModeSql(String payModeSql) {
        this.payModeSql = payModeSql;
    }

}
