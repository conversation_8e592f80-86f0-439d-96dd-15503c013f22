package cn.abc.flink.stat.service.cis.hospital.revenue.charge.handler;

import cn.abc.flink.stat.common.HospitalRevenueChargeSettleTypeEnum;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.db.dao.HospitalRevenueChargeMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Department;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2Goods;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.pojo.EmployeeResp;
import cn.abc.flink.stat.pojo.KeyValuePojo;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAdviceFeeSelectRsp;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.HospitalAchievementChargeCustomType;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.HospitalAchievementChargeType;
import cn.abc.flink.stat.service.cis.handler.BaseInfoFuture;
import cn.abc.flink.stat.service.cis.handler.ChargeHandler;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeClassify;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeDetail;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeParam;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeSelectionResp;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeSettleTypeResp;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeTransaction;
import cn.abc.flink.stat.service.cis.selection.entity.PayModeDao;
import cn.abc.flink.stat.service.cis.selection.pojo.DepartmentResp;
import cn.abc.flink.stat.service.cis.selection.pojo.PayModeResp;
import cn.abc.flink.stat.service.cis.selection.pojo.WardResp;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @description: 医院管家-住院收费-数据处理器
 * @author: lzq
 * @Date: 2023/03/07
 */
@Service
public class HospitalRevenueChargeDataHandler {

    @Resource
    private HospitalRevenueChargeMapper mapper;

    @Autowired
    private DimensionQuery query;

    @Autowired
    private ExecutorService cacheExecutorService;


    /**
     * 医院管家-住院收费-筛选框
     *
     * @param param 住院收费param
     * @return 筛选框resp
     * @throws Exception e
     */
    public HospitalRevenueChargeSelectionResp selection(HospitalRevenueChargeParam param) throws Exception {
        HospitalRevenueChargeSelectionResp resp = new HospitalRevenueChargeSelectionResp();
        List<HospitalRevenueChargeSettleTypeResp> typeRespList = new ArrayList<>();
        List<EmployeeResp> employeeRespList = new ArrayList<>();
        List<DepartmentResp> departmentRespList = new ArrayList<>();
        List<WardResp> wardRespList = new ArrayList<>();
        List<AchievementChargeAdviceFeeSelectRsp> adviceFeeSelectRsp = new ArrayList<>();
        // 查询全部类型
        CompletableFuture<List<Integer>> typeIdListFuture = CompletableFuture.supplyAsync(() -> {
            return mapper.selectionTypeIdByTransaction(TableUtils.getHisChargeTable(), param);
        }, cacheExecutorService);
        // 查询全部支付方式
        CompletableFuture<List<PayModeDao>> payModeDaoListFuture = CompletableFuture.supplyAsync(() -> {
            return mapper.selectionPayModeIdByTransaction(TableUtils.getHisChargeTable(), param);
        }, cacheExecutorService);
        // 查询全部收费员
        CompletableFuture<List<String>> employeeIdListFuture = CompletableFuture.supplyAsync(() -> {
            return mapper.selectionEmployeeIdByTransaction(TableUtils.getHisChargeTable(), param);
        }, cacheExecutorService);
        // 查询全部科室
        CompletableFuture<List<String>> departmentIdListFuture = CompletableFuture.supplyAsync(() -> {
            return mapper.selectionDepartmentIdByTransaction(TableUtils.getHisChargeTable(),
                    TableUtils.getCisPatientorderTable(), param);
        }, cacheExecutorService);
        // 查询全部病区
        CompletableFuture<List<String>> wardIdListFuture = CompletableFuture.supplyAsync(() -> {
            return mapper.selectionWardIdByTransaction(TableUtils.getHisChargeTable(),
                    TableUtils.getCisPatientorderTable(), param);
        }, cacheExecutorService);
        // 查询全部分类
        CompletableFuture<List<HospitalAchievementChargeType>> feeTypesIdListFuture
                = CompletableFuture.supplyAsync(() -> {
            return mapper.selectionFeeTypesIdByTransaction(TableUtils.getHisChargeTable(),
                    TableUtils.getCisGoodsTable(), param);
        }, cacheExecutorService);
        CompletableFuture<Map<String, Employee>> employeeFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryEmployeeByChainId(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, String>> departmentFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryDepartmentNameByOrgan(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, String>> wardFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<List<Long>> feeTypeIdsFuture = CompletableFuture.supplyAsync(() -> {
            return mapper.selectFeeTypeIds(TableUtils.getHisChargeTable(), param);
        }, cacheExecutorService);
        CompletableFuture<Map<Long, V2GoodsFeeType>> v2GoodsFeeTypeMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.selectAdviceFeeType(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture.allOf(typeIdListFuture, payModeDaoListFuture, employeeIdListFuture, departmentIdListFuture,
                wardIdListFuture, employeeFuture, departmentFuture, wardFuture, feeTypesIdListFuture,
                v2GoodsFeeTypeMapFuture, feeTypeIdsFuture).join();
        List<HospitalAchievementChargeType> recordFeeSecondClassify = feeTypesIdListFuture.get();
        List<Long> feeTypeIds = feeTypeIdsFuture.get();
        Map<Long, V2GoodsFeeType> v2GoodsFeeTypeMap = v2GoodsFeeTypeMapFuture.get();
        setTypeResp(typeRespList, typeIdListFuture);
        setEmployeeResp(employeeRespList, employeeIdListFuture, employeeFuture);
        setDepartmentResp(departmentRespList, departmentIdListFuture, departmentFuture);
        setWardResp(wardRespList, wardIdListFuture, wardFuture);
        List<HospitalAchievementChargeType> feeClassifyList = setFeeClassifyResp(recordFeeSecondClassify);
        Map<Integer, PayModeResp> map = new HashMap<>();
        setPayModeResp(payModeDaoListFuture, map);
        setFeeTypeResp(adviceFeeSelectRsp, feeTypeIds, v2GoodsFeeTypeMap);
        resp.setTypeRespList(typeRespList);
        resp.setPayModeRespList(new ArrayList<>(map.values()));
        resp.setEmployeeRespList(employeeRespList);
        resp.setDepartmentRespList(departmentRespList);
        resp.setWardRespList(wardRespList);
        resp.setFeeClassifyList(feeClassifyList);
        resp.setAdviceFeeSelectRsp(adviceFeeSelectRsp);
        return resp;
    }

    /**
     * @param adviceFeeSelectRsp 费用分类返回值
     * @param feeTypeIds         费用分类ids
     * @param v2GoodsFeeTypeMap  费用分类map
     */
    private void setFeeTypeResp(List<AchievementChargeAdviceFeeSelectRsp> adviceFeeSelectRsp,
                                List<Long> feeTypeIds,
                                Map<Long, V2GoodsFeeType> v2GoodsFeeTypeMap) {
        for (Long feeTypeId : feeTypeIds) {
            if (!BeanUtil.isEmpty(v2GoodsFeeTypeMap.get(feeTypeId))) {
                adviceFeeSelectRsp.add(new AchievementChargeAdviceFeeSelectRsp(v2GoodsFeeTypeMap.get(feeTypeId).getName(), String.valueOf(feeTypeId)));
            }
        }
    }

    /**
     * 根据类型id组装收费类型
     *
     * @param recordFeeSecondClassify 收费类型list
     * @return 组装好的收费类型list
     */
    private List<HospitalAchievementChargeType> setFeeClassifyResp(
            List<HospitalAchievementChargeType> recordFeeSecondClassify) {
        List<HospitalAchievementChargeType> result = new ArrayList<>();
        Set<Integer> typeSet = new HashSet<>();
        Set<Integer> customTypeSet = new HashSet<>();
        for (HospitalAchievementChargeType achievementChargeFeeEntity : recordFeeSecondClassify) {
            // 先从已有的list获取 有的话直接设置二级分类 没有的话设置一二级分类
            List<HospitalAchievementChargeType> entities = result.stream()
                    .filter(chargeFeeEntity -> chargeFeeEntity.getId()
                            .equals(achievementChargeFeeEntity.getId())).collect(Collectors.toList());
            if (!CollUtil.isEmpty(entities) && achievementChargeFeeEntity.getCustomTypeId() == null) {
                continue;
            }
            if (!CollUtil.isEmpty(entities)) {
                result.forEach(achievementChargeFeeEntity1 -> {
                    List<Integer> list = achievementChargeFeeEntity1.getCustomTypeList().stream()
                            .map(HospitalAchievementChargeCustomType::getId).collect(Collectors.toList());
                    if (achievementChargeFeeEntity1.getId().equals(achievementChargeFeeEntity.getId())
                            && !CollUtil.isEmpty(list)
                            && !list.contains(achievementChargeFeeEntity.getCustomTypeId())) {
                        HospitalAchievementChargeCustomType chargeCustomType
                                = new HospitalAchievementChargeCustomType();
                        chargeCustomType.setId(achievementChargeFeeEntity.getCustomTypeId());
                        chargeCustomType.setTypeId(achievementChargeFeeEntity.getId());
                        chargeCustomType.setName(achievementChargeFeeEntity.getCustomTypeName());
                        chargeCustomType.setCustomTypeSort(achievementChargeFeeEntity.getCustomTypeSort());
                        List<HospitalAchievementChargeCustomType> customTypeList
                                = new ArrayList<>(achievementChargeFeeEntity1.getCustomTypeList());
                        customTypeList.add(chargeCustomType);
                        achievementChargeFeeEntity1.setCustomTypeList(
                                customTypeList.stream()
                                        .sorted(Comparator.comparing(
                                                HospitalAchievementChargeCustomType::getCustomTypeSort))
                                        .collect(Collectors.toList()));
                    }
                });
            } else {
                HospitalAchievementChargeType entity = new HospitalAchievementChargeType();
                entity.setId(achievementChargeFeeEntity.getId());
                entity.setName(achievementChargeFeeEntity.getName());
                entity.setGoodsType(achievementChargeFeeEntity.getGoodsType());
                entity.setGoodsSubType(achievementChargeFeeEntity.getGoodsSubType());
                entity.setSort(achievementChargeFeeEntity.getSort());
                if (achievementChargeFeeEntity.getCustomTypeId() != null) {
                    HospitalAchievementChargeCustomType chargeCustomType = new HospitalAchievementChargeCustomType();
                    chargeCustomType.setId(achievementChargeFeeEntity.getCustomTypeId());
                    chargeCustomType.setTypeId(achievementChargeFeeEntity.getId());
                    chargeCustomType.setName(achievementChargeFeeEntity.getCustomTypeName());
                    chargeCustomType.setCustomTypeSort(achievementChargeFeeEntity.getCustomTypeSort());
                    entity.setCustomTypeList(Collections.singletonList(chargeCustomType));
                } else {
                    entity.setCustomTypeList(Collections.emptyList());
                }
                result.add(entity);
            }
            typeSet.add(achievementChargeFeeEntity.getId());
            customTypeSet.add(achievementChargeFeeEntity.getCustomTypeId());
        }
        return result.stream().sorted(Comparator.comparing(HospitalAchievementChargeType::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 根据支付方式id设置支付方式
     *
     * @param payModeDaoListFuture 支付方式List
     * @param map                  支付方式idMap
     * @throws Exception e
     */
    private void setPayModeResp(CompletableFuture<List<PayModeDao>> payModeDaoListFuture,
                                Map<Integer, PayModeResp> map) throws Exception {
        payModeDaoListFuture.get().forEach(payMode -> {
            if (payMode != null) {
                if (payMode.getPayMode1() != null && payMode.getPayMode1() != CommonConstants.NUMBER_ZERO) {
                    if (map.containsKey(payMode.getPayMode1())) {
                        if (payMode.getPayMode2() != null && (payMode.getPayMode1() == CommonConstants.NUMBER_TWO
                                || payMode.getPayMode2() != CommonConstants.NUMBER_ZERO)) {
                            map.get(payMode.getPayMode1()).getChildren()
                                    .add(buildKeyValue(payMode.getPayMode1(), payMode.getPayMode2()));
                        }

                    } else {
                        PayModeResp payModeDto = new PayModeResp();
                        payModeDto.setName(query.queryPayTypeText(payMode.getPayMode1()));
                        payModeDto.setValue(payMode.getPayMode1() + "");
                        if (payMode.getPayMode2() != null && (payMode.getPayMode1() == CommonConstants.NUMBER_TWO
                                || payMode.getPayMode1() == CommonConstants.NUMBER_NINETEEN
                                || (payMode.getPayMode1() == CommonConstants.NUMBER_FIVE
                                && payMode.getPayMode2() != CommonConstants.NUMBER_ZERO))) {
                            payModeDto.getChildren().add(buildKeyValue(payMode.getPayMode1(), payMode.getPayMode2()));
                        }
                        map.put(payMode.getPayMode1(), payModeDto);
                    }

                }
            }
        });
    }

    /**
     * 根据病区id设置病区
     *
     * @param wardRespList     返回的病区resp
     * @param wardIdListFuture 病区idList
     * @param wardFuture       病区信息Map
     * @throws Exception e
     */
    private void setWardResp(List<WardResp> wardRespList,
                             CompletableFuture<List<String>> wardIdListFuture,
                             CompletableFuture<Map<String, String>> wardFuture) throws Exception {
        for (String wardId : wardIdListFuture.get()) {
            Map<String, String> wardMap = wardFuture.get();
            if (!StrUtil.isBlank(wardMap.get(wardId))) {
                WardResp wardResp = new WardResp();
                wardResp.setId(wardId);
                wardResp.setName(wardMap.get(wardId));
                wardRespList.add(wardResp);
            }
        }
    }

    /**
     * 根据科室id设置科室resp
     *
     * @param departmentRespList     返回科室List
     * @param departmentIdListFuture 科室idList
     * @param departmentFuture       科室信息List
     * @throws Exception e
     */
    private void setDepartmentResp(List<DepartmentResp> departmentRespList,
                                   CompletableFuture<List<String>> departmentIdListFuture,
                                   CompletableFuture<Map<String, String>> departmentFuture) throws Exception {
        for (String departmentId : departmentIdListFuture.get()) {
            Map<String, String> departmentMap = departmentFuture.get();
            if (!StrUtil.isBlank(departmentMap.get(departmentId))) {
                DepartmentResp departmentResp = new DepartmentResp();
                departmentResp.setId(departmentId);
                departmentResp.setName(departmentMap.get(departmentId));
                departmentRespList.add(departmentResp);
            }
        }
    }

    /**
     * 根据收费员id构造收费员信息
     *
     * @param employeeRespList     收费员resp
     * @param employeeIdListFuture 收费员idList
     * @param employeeFuture       人员List
     * @throws Exception e
     */
    private void setEmployeeResp(List<EmployeeResp> employeeRespList,
                                 CompletableFuture<List<String>> employeeIdListFuture,
                                 CompletableFuture<Map<String, Employee>> employeeFuture) throws Exception {
        for (String employeeId : employeeIdListFuture.get()) {
            Map<String, Employee> employeeMap = employeeFuture.get();
            Employee employee = employeeMap.get(employeeId);
            if (!BeanUtil.isEmpty(employee)) {
                EmployeeResp employeeResp = new EmployeeResp();
                employeeResp.setId(employeeId);
                employeeResp.setName(employee.getName());
                employeeResp.setNamePy(employee.getNamePy());
                employeeResp.setNamePyFirst(employee.getNamePyFirst());
                employeeRespList.add(employeeResp);
            }
        }
    }

    /**
     * 根据结算id设置结算类型resp
     *
     * @param typeRespList     结算类型resp
     * @param typeIdListFuture 结算类型idList
     * @throws Exception e
     */
    private void setTypeResp(List<HospitalRevenueChargeSettleTypeResp> typeRespList,
                             CompletableFuture<List<Integer>> typeIdListFuture) throws Exception {
        for (Integer typeId : typeIdListFuture.get()) {
            HospitalRevenueChargeSettleTypeResp typeResp = new HospitalRevenueChargeSettleTypeResp();
            typeResp.setType(typeId);
            typeResp.setName(HospitalRevenueChargeSettleTypeEnum.getNameByTypeId(typeId));
            typeRespList.add(typeResp);
        }
    }

    /**
     * 构建支付方式pojo
     *
     * @param payMode1 一级支付方式Id
     * @param payMode2 一级支付方式Id
     * @return 二级收费方式
     */
    private KeyValuePojo buildKeyValue(Integer payMode1, Integer payMode2) {
        KeyValuePojo kv = new KeyValuePojo();
        kv.setName(query.queryPaySubTypeText(payMode1, payMode2));
        kv.setValue(payMode1 + "_" + payMode2);
        return kv;
    }

    /**
     * 查询住院收费单据维度数据并构造返回数据
     *
     * @param param 住院收费param
     * @return 单据维度数据resp
     * @throws Exception e
     */
    public List<HospitalRevenueChargeTransaction> selectTransaction(HospitalRevenueChargeParam param) throws Exception {
        CompletableFuture<List<HospitalRevenueChargeTransaction>> transactionListFuture
                = CompletableFuture.supplyAsync(() -> {
            return mapper.selectTransaction(TableUtils.getHisChargeTable(),
                    TableUtils.getCisPatientorderTable(), param);
        }, cacheExecutorService);
        List<HospitalRevenueChargeTransaction> transactionList = transactionListFuture.get();
        if (CollUtil.isEmpty(transactionList)) {
            return new ArrayList<>();
        }
        Set<String> patientIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        for (HospitalRevenueChargeTransaction transaction : transactionList) {
            patientIds.add(transaction.getPatientId());
            departmentIds.add(transaction.getLeaveHospitalDepartmentId());
        }
        CompletableFuture<Map<String, V2Patient>> patientMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryPatient(param.getChainId(), patientIds, param.getEnablePatientMobile());
        }, cacheExecutorService);
        CompletableFuture<Map<String, Department>> departmentMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryDepartments(param.getChainId(), departmentIds);
        }, cacheExecutorService);
        CompletableFuture<Map<String, String>> wardMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, Employee>> employeeMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryEmployeeByChainId(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture<Map<Integer, String>> payMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryPayTypeTextByChainIdOrClinicId(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, Organ>> organMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryOrganByParentId(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture.allOf(patientMapFuture, departmentMapFuture, wardMapFuture, employeeMapFuture,
                payMapFuture, organMapFuture).join();
        for (HospitalRevenueChargeTransaction transaction : transactionList) {
            transaction.setPatientName(patientMapFuture.get().get(transaction.getPatientId()).getName());
            if (!BeanUtil.isEmpty(departmentMapFuture.get()
                    .get(transaction.getLeaveHospitalDepartmentId()))) {
                transaction.setLeaveHospitalDepartment(departmentMapFuture.get()
                        .get(transaction.getLeaveHospitalDepartmentId()).getName());
            }
            if (!StrUtil.isEmpty(wardMapFuture.get().get(transaction.getLeaveHospitalWardId()))) {
                transaction.setLeaveHospitalWard(wardMapFuture.get().get(transaction.getLeaveHospitalWardId()));
            }
            if (!BeanUtil.isEmpty(employeeMapFuture.get().get(transaction.getSellerId()))) {
                transaction.setSellerName(employeeMapFuture.get().get(transaction.getSellerId()).getName());
            }
            if (!BeanUtil.isEmpty(organMapFuture.get().get(transaction.getClinicId()))) {
                transaction.setClinicName(organMapFuture.get().get(transaction.getClinicId()).getName());
            }
            transaction.setPayModeName(ChargeHandler.handlePayMode(query, transaction.getPayMode(),
                    transaction.getPaySubMode(), payMapFuture.get()));
        }
        return transactionList;
    }

    /**
     * 查询住院收费分类维度数据并构造返回数据
     *
     * @param param 住院收费param
     * @return 分类维度数据resp
     * @throws Exception e
     */
    public List<HospitalRevenueChargeClassify> selectClassify(HospitalRevenueChargeParam param) throws Exception {
        CompletableFuture<List<HospitalRevenueChargeClassify>> classifyListFuture
                = CompletableFuture.supplyAsync(() -> {
            return mapper.selectClassify(TableUtils.getHisChargeTable(), TableUtils.getCisPatientorderTable(),
                    TableUtils.getCisGoodsTable(), param);
        }, cacheExecutorService);
        List<HospitalRevenueChargeClassify> classifyList = classifyListFuture.get();
        if (CollUtil.isEmpty(classifyList)) {
            return new ArrayList<>();
        }
        Set<String> patientIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        for (HospitalRevenueChargeClassify classify : classifyList) {
            patientIds.add(classify.getPatientId());
            departmentIds.add(classify.getLeaveHospitalDepartmentId());
        }
        BaseInfoFuture baseFuture = new BaseInfoFuture(query, cacheExecutorService,
                param.getChainId(), false, true);
        CompletableFuture<Map<String, Employee>> employeeMapFuture = baseFuture.getEmployeeFuture();
        CompletableFuture<Map<String, V2Patient>> patientMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryPatient(param.getChainId(), patientIds, param.getEnablePatientMobile());
        }, cacheExecutorService);
        CompletableFuture<Map<String, Department>> departmentMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryDepartments(param.getChainId(), departmentIds);
        }, cacheExecutorService);
        CompletableFuture<Map<String, String>> wardMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<Integer, String>> payMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryPayTypeTextByChainIdOrClinicId(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, Organ>> organMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryOrganByParentId(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture<Map<Long, V2GoodsFeeType>> v2GoodsFeeTypeMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.selectAdviceFeeType(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture.allOf(patientMapFuture, departmentMapFuture, wardMapFuture, employeeMapFuture,
                payMapFuture, organMapFuture, v2GoodsFeeTypeMapFuture).join();
        for (HospitalRevenueChargeClassify classify : classifyList) {
            classify.setPatientName(patientMapFuture.get().get(classify.getPatientId()).getName());
            if (!BeanUtil.isEmpty(departmentMapFuture.get()
                    .get(classify.getLeaveHospitalDepartmentId()))) {
                classify.setLeaveHospitalDepartment(departmentMapFuture.get()
                        .get(classify.getLeaveHospitalDepartmentId()).getName());
            }
            if (!StrUtil.isBlank(wardMapFuture.get().get(classify.getLeaveHospitalWardId()))) {
                classify.setLeaveHospitalWard(wardMapFuture.get().get(classify.getLeaveHospitalWardId()));
            }
            if (!BeanUtil.isEmpty(employeeMapFuture.get().get(classify.getSellerId()))) {
                classify.setSellerName(employeeMapFuture.get().get(classify.getSellerId()).getName());
            }
            if (!BeanUtil.isEmpty(organMapFuture.get().get(classify.getClinicId()))) {
                classify.setClinicName(organMapFuture.get().get(classify.getClinicId()).getName());
            }
            if (!BeanUtil.isEmpty(v2GoodsFeeTypeMapFuture.get().get(classify.getFeeTypeId()))) {
                classify.setChargeClassify(v2GoodsFeeTypeMapFuture.get().get(classify.getFeeTypeId()).getName());
            } else {
                classify.setChargeClassify("-");
            }
            classify.setPayModeName(ChargeHandler.handlePayMode(query, classify.getPayMode(),
                    classify.getPaySubMode(), payMapFuture.get()));
        }
        return classifyList;
    }


    /**
     * 查询住院收费明细维度数据并构造返回数据
     *
     * @param param 住院收费param
     * @return 明细维度数据resp
     * @throws Exception e
     */
    public List<HospitalRevenueChargeDetail> selectDetail(HospitalRevenueChargeParam param) throws Exception {
        CompletableFuture<List<HospitalRevenueChargeDetail>> detailListFuture
                = CompletableFuture.supplyAsync(() -> {
            return mapper.selectDetail(TableUtils.getHisChargeTable(), TableUtils.getCisPatientorderTable(),
                    TableUtils.getCisGoodsTable(), param);
        }, cacheExecutorService);
        List<HospitalRevenueChargeDetail> detailList = detailListFuture.get();
        if (CollUtil.isEmpty(detailList)) {
            return new ArrayList<>();
        }
        Set<String> patientIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        Set<String> goodsIds = new HashSet<>();
        for (HospitalRevenueChargeDetail detail : detailList) {
            patientIds.add(detail.getPatientId());
            departmentIds.add(detail.getLeaveHospitalDepartmentId());
            goodsIds.add(detail.getProductId());
        }
        BaseInfoFuture baseFuture = new BaseInfoFuture(query, cacheExecutorService,
                param.getChainId(), false, true);
        CompletableFuture<Map<String, Employee>> employeeMapFuture = baseFuture.getEmployeeFuture();
        CompletableFuture<Map<String, V2Patient>> patientMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryPatient(param.getChainId(), patientIds, param.getEnablePatientMobile());
        }, cacheExecutorService);
        CompletableFuture<Map<Long, V2GoodsFeeType>> v2GoodsFeeTypeMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.selectAdviceFeeType(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, Department>> departmentMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryDepartments(param.getChainId(), departmentIds);
        }, cacheExecutorService);
        CompletableFuture<Map<String, String>> wardMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<Integer, String>> payMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryPayTypeTextByChainIdOrClinicId(param.getChainId(), param.getClinicId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, V2Goods>> productMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryProducts(param.getChainId(), goodsIds);
        }, cacheExecutorService);
        CompletableFuture<Map<String, Organ>> organMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryOrganByParentId(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture.allOf(patientMapFuture, departmentMapFuture, wardMapFuture, employeeMapFuture,
                payMapFuture, productMapFuture, organMapFuture, v2GoodsFeeTypeMapFuture).join();
        for (HospitalRevenueChargeDetail detail : detailList) {
            if (!BeanUtil.isEmpty(patientMapFuture.get().get(detail.getPatientId()))) {
                detail.setPatientName(patientMapFuture.get().get(detail.getPatientId()).getName());
            }
            if (!BeanUtil.isEmpty(departmentMapFuture.get().get(detail.getLeaveHospitalDepartmentId()))) {
                detail.setLeaveHospitalDepartment(departmentMapFuture.get()
                        .get(detail.getLeaveHospitalDepartmentId()).getName());
            }
            if (!StrUtil.isBlank(wardMapFuture.get().get(detail.getLeaveHospitalWardId()))) {
                detail.setLeaveHospitalWard(wardMapFuture.get().get(detail.getLeaveHospitalWardId()));
            }
            if (!BeanUtil.isEmpty(employeeMapFuture.get().get(detail.getSellerId()))) {
                detail.setSellerName(employeeMapFuture.get().get(detail.getSellerId()).getName());
            }
            if (!BeanUtil.isEmpty(organMapFuture.get().get(detail.getClinicId()))) {
                detail.setClinicName(organMapFuture.get().get(detail.getClinicId()).getName());
            }
            if (!BeanUtil.isEmpty(v2GoodsFeeTypeMapFuture.get().get(detail.getFeeTypeId()))) {
                detail.setFeeTypeName(v2GoodsFeeTypeMapFuture.get().get(detail.getFeeTypeId()).getName());
            } else {
                detail.setFeeTypeName("-");
            }
            detail.setPayModeName(ChargeHandler.handlePayMode(query, detail.getPayMode(),
                    detail.getPaySubMode(), payMapFuture.get()));
            detail.setProductNameByMap(productMapFuture.get());
        }
        return detailList;
    }


}
