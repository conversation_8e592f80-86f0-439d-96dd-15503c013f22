package cn.abc.flink.stat.service.cis.commission.handler;

import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.service.cis.commission.domain.CommissionParam;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/27 下午7:06
 */
@Component
public class CommissionHeaderHandler {

    @Autowired
    private DimensionQuery query;

    public List<TableHeaderEmployeeItem> initCommissionHeader(CommissionParam param) {
        List<TableHeaderEmployeeItem> headerList = new ArrayList<>();
        if (param.getDispensaryType() != 0) {
            TableHeaderEmployeeItem item = new TableHeaderEmployeeItem();
            item.setProp("clinicName");
            item.setLabel("门店名称");
            item.setAlign("left");
            item.setWidth(CommonConstants.NUMBER_ONE_HUNDRED_FORTY);
            headerList.add(item);
        }
        TableHeaderEmployeeItem item = new TableHeaderEmployeeItem();
        item.setProp("employeeName");
        item.setLabel("人员名称");
        item.setAlign("left");
        item.setWidth(CommonConstants.NUMBER_ONE_HUNDRED_FORTY);
        headerList.add(item);
        return headerList;
    }

    /**
     * 初始化响应头部
     */
    public void initRevenueHeader(V2StatResponse response, CommissionParam param, String statCommissionRevenueDetail) {
        response.setHeader(query.getTableHeaderEmployeeItems(
                param.getParams().getEmployeeId(), statCommissionRevenueDetail,
                param.getParams().getViewModeInteger(), param.getParams().getNodeType(),
                HeaderTableKeyConfig.EXCLUDE_HIDDEN_1));
        response.setTotal(new StatResponseTotal(0L));
    }

    public List<TableHeaderEmployeeItem> initCommissionSummaryHeader(CommissionParam param,
                                                                     List<TableHeaderEmployeeItem> headerList,
                                                                     Map<String, Object> summaryMap) {
        if (param.getDispensaryType() != 0) {
            TableHeaderEmployeeItem item = new TableHeaderEmployeeItem();
            item.setProp("clinicName");
            item.setLabel("门店名称");
            item.setAlign("left");
            item.setPosition(1);
            item.setWidth(CommonConstants.NUMBER_ONE_HUNDRED_FORTY);
            headerList.add(item);
            summaryMap.put("clinicName", "合计");
        } else {
            summaryMap.put("employeeName", "合计");
        }
        TableHeaderEmployeeItem item = new TableHeaderEmployeeItem();
        item.setProp("employeeName");
        item.setLabel("人员名称");
        item.setAlign("left");
        item.setPosition(2);
        item.setWidth(CommonConstants.NUMBER_ONE_HUNDRED_FORTY);
        headerList.add(item);
        TableHeaderEmployeeItem item1 = new TableHeaderEmployeeItem();
        item1.setProp("totalAmt");
        item1.setLabel("提成汇总");
        item1.setType("money");
        item1.setAlign("right");
        item.setPosition(3);
        item1.setWidth(CommonConstants.NUMBER_NINETY);
        headerList.add(item1);
        return headerList;
    }

    public void initCommissionSummaryName(List<Map<String, Object>> commissionList,
                                          List<TableHeaderEmployeeItem> headerList,
                                          Map<String, Object> summaryMap) {
        int i = 4;
        for (Map<String, Object> map : commissionList) {
            if (!"00000000000000000000000000000000".equals(map.get("id"))) {
                TableHeaderEmployeeItem item = new TableHeaderEmployeeItem();
                item.setProp(map.get("id").toString());
                item.setLabel(map.get("name").toString());
                item.setType("money");
                item.setAlign("right");
                item.setPosition(i);
                item.setWidth(CommonConstants.NUMBER_HUNDRED);
                headerList.add(item);
                summaryMap.put(map.get("id").toString(), BigDecimal.ZERO);
                i++;
            }
        }
    }
}
