package cn.abc.flink.stat.service.his.achievement.cost;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisChargeSettlementSelecttionResp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisChargedSelectionReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisChargedWardSelectionResp;
import cn.abc.flink.stat.service.cis.config.pojo.StatOrganTableConfig;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisIncomeSummaryReq;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisReciptSummaryReq;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostChargeReportSelectRsp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostChargeSettlePatientSelectRsp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostPhysicalExaminationDayReportRsp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargeReportDetailRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargeReportRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargedProductReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargedSettlePatientReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HospCashierDailyReportParams;
import cn.abc.flink.stat.service.his.revenue.product.domain.HisRevenueOutpatientProductReq;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

public interface IHisRevenuesCostSevice {

    V2StatResponse selectFeeType(HisRevenuesCostReqParams params);

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 结算统计-单据
     * @Author: zs
     * @Date: 2023/12/4 09:54
     */
    V2StatResponse selectTransaction(HisRevenuesCostReqParams params);

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 结算统计-项目
     * @Author: zs
     * @Date: 2023/12/5 15:23
     */
    V2StatResponse selectProject(HisRevenuesCostReqParams params);

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 计费-费用分类
     * @Author: zs
     * @Date: 2023/12/11 11:35
     */
    V2StatResponse selectChargeFeeType(HisRevenuesCostReqParams params);

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 计费-患者
     * @Author: zs
     * @Date: 2023/12/11 11:35
     */
    V2StatResponse selectChargPatient(HisRevenuesCostReqParams params);

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 计费-项目
     * @Author: zs
     * @Date: 2023/12/13 10:13
     */
    V2StatResponse selectChargProject(HisRevenuesCostReqParams params);

    /**
     * @param
     * @param reqParams -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.ExcelUtils.AbcExcelSheet>
     * @Description: 结算统计-异步导出
     * @Author: zs
     * @Date: 2023/12/11 17:42
     */
    List<ExcelUtils.AbcExcelSheet> asyncExport(HisRevenuesCostReqParams reqParams);

    /**
     * @param
     * @param reqParams -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.ExcelUtils.AbcExcelSheet>
     * @Description: 结算统计-异步导出
     * @Author: zs
     * @Date: 2023/12/11 17:42
     */
    List<ExcelUtils.AbcExcelSheet> chargeAsyncExport(HisRevenuesCostReqParams reqParams);

    /**
     * 住院收费报表
     *
     * @param params -
     * @return -
     */
    HisRevenuesCostChargeReportRes selectChargeReport(HisRevenuesCostReqParams params);

    List<ExcelUtils.AbcExcelSheet> chargeReportAsyncExport(HisRevenuesCostReqParams params);
    /**
     * @param params 营收成本param
     * @return 押金统计data
     */
    V2StatResponse selectChargeDeposit(HisRevenuesCostReqParams params) throws Exception;

    List<ExcelUtils.AbcExcelSheet> chargeDepositAsyncExport(HisRevenuesCostReqParams reqParams) throws Exception;

    @Deprecated
    V2StatResponse selectChargedProduct(HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException;

    /**
     * @Description: 营收费/成本统计-收费项目统计-汇总
     * @param
     * @param params:
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Author: zs
     * @Date: 2024/3/21 10:52
     */
    V2StatResponse selectChargedFeeTypes(HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException;

    V2StatResponse selectChargedProducts(HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException;

    @Deprecated
    List<ExcelUtils.AbcExcelSheet> exportChargedProduct(HisRevenuesCostChargedProductReqParams params, HisRevenueOutpatientProductReq outpatientParams) throws ExecutionException, InterruptedException;

    V2StatResponse selectInomeSummary(HisIncomeSummaryReq params) throws ExecutionException, InterruptedException;

    List<ExcelUtils.AbcExcelSheet> exportInomeSummary(HisIncomeSummaryReq params) throws ExecutionException, InterruptedException;

    V2StatResponse selectReceiptSummary(HisReciptSummaryReq params) throws ExecutionException, InterruptedException;

    List<ExcelUtils.AbcExcelSheet> exportReceiptSummary(HisReciptSummaryReq params) throws ExecutionException, InterruptedException;

    V2StatResponse selectChargeDepositTypeSelection(HisRevenuesCostReqParams params);

    V2StatResponse selectChargedSettlePatient(HisRevenuesCostChargedSettlePatientReqParams params) throws ExecutionException, InterruptedException;

    HisRevenueCostChargeSettlePatientSelectRsp selectChargedSettlePatientSelect(HisRevenuesCostChargedSettlePatientReqParams params) throws Exception;

    List<ExcelUtils.AbcExcelSheet> exportHisRevenueCostChargeSettlePatient(HisRevenuesCostChargedSettlePatientReqParams reqParams) throws ExecutionException, InterruptedException;

    HisRevenueCostPhysicalExaminationDayReportRsp selectPhysicalExaminationChargeDayReport(HisRevenuesCostReqParams params);

    List<ExcelUtils.AbcExcelSheet> peChargeDayReportAsyncExport(HisRevenuesCostReqParams reqParams) throws Exception;

    HisChargedWardSelectionResp selectChargedSelection(HisChargedSelectionReqParams params) throws ExecutionException, InterruptedException;

    HisChargeSettlementSelecttionResp selectChargedSettlementSelection(AbcScStatRequestParams params) throws ExecutionException, InterruptedException;

    V2StatResponse selectCashierDailyReport(HospCashierDailyReportParams params) throws ExecutionException, InterruptedException;

    List<ExcelUtils.AbcExcelSheet> exportCashierDailyReport(HospCashierDailyReportParams params) throws ExecutionException, InterruptedException;

    List<StatOrganTableConfig> selectChargeDayReportConfig(HisRevenuesCostReqParams params);

    List<HisRevenuesCostChargeReportDetailRes> selectChargeDayReportDetail(HisRevenuesCostReqParams params) throws Exception;

    List<ExcelUtils.AbcExcelSheet> exportRevenueCostChargeDaily(HisRevenuesCostReqParams reqParams) throws Exception;

    void selectChargeDayReportDetailExport(HttpServletResponse response, HisRevenuesCostReqParams params) throws Exception;

    HisRevenueCostChargeReportSelectRsp selectChargeDayReportSelect(HisRevenuesCostReqParams params) throws Exception;

}
