package cn.abc.flink.stat.service.cis.revenue.overview;

import cn.abc.flink.stat.common.EmployeeTypeEnum;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.AbcPermission;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.RevenueChargeDetailService;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.handler.RevenueChargeDetailHandler;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailReqParams;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueGadgetReqParams;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @description:
 * @author: dy
 * @create: 2021-10-15 16:47
 */
@Component
public class RevenueOverviewAsyncExportService implements BaseAsyncExportInterface {
	private final Logger logger = LoggerFactory.getLogger(RevenueOverviewAsyncExportService.class);
	@Autowired
	RevenueOverviewService revenueChargeDetailService;

	@Override
	public String getKey() {
		return "revenue-overview";
	}

	@Override
	public String setFileName(Map<String, Object> params) {
		String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
		String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
		return "费用明细" + beginDate + "_" + endDate + ".xlsx";
	}

	@Override
	public OutputStream export(Map<String, Object> params)
			throws ParseException, ExecutionException, InterruptedException {
		// 参数构造
		String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
		String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
		String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
		String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
		String doctorId = (String) MapUtils.isExistsAndReturn(params, "doctorId");

		String employeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
		String hisType = (String) MapUtils.isExistsAndReturn(params, "headerHisType");

		Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
		String chainViewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");

		RevenueGadgetReqParams reqParams = new RevenueGadgetReqParams();
		reqParams.setChainId(chainId);
		reqParams.setClinicId(clinicId);
		reqParams.setBeginDate(beginDate);
		reqParams.setEndDate(endDate);
		reqParams.setDoctorId(doctorId);
		reqParams.setParams(new AbcCisBaseQueryParams());
		reqParams.getParams().setEmployeeId(employeeId);
		reqParams.getParams().setViewModeInteger(Integer.valueOf(chainViewMode));
		reqParams.getParams().setNodeType(clinicNodeType);
		reqParams.setHisType(hisType);
		reqParams.setSingleStore(chainViewMode, clinicNodeType.toString());
		reqParams.handleParams();
		List<ExcelUtils.AbcExcelSheet> sheets = revenueChargeDetailService.export(reqParams);
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		ExcelUtils.export(baos, sheets);
		return baos;
	}
}
