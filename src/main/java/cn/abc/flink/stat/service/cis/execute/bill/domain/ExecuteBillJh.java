package cn.abc.flink.stat.service.cis.execute.bill.domain;

import io.swagger.annotations.ApiModelProperty;


import java.util.List;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.execute.bill.domain
 * @Author: zs
 * @CreateTime: 2022-08-29  11:11
 * @Description: 治疗理疗济华参数类
 * @Version: 1.0
 */
public class ExecuteBillJh {
    /**
     * 连锁id 前端不一定传，需要先从header中获取
     */
    protected String chainId;

    /**
     * 门店id 前端不一定传，需要先从header中获取
     */
    protected String clinicId;

    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 医生ID
     */
    private List<String> doctorIds;

    /**
     * 理疗师ID
     */
    private List<String> llsIds;

    /**
     * 收费状态
     */
    @ApiModelProperty(value = "收费状态")
    private String chargeStatus;

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public List<String> getDoctorIds() {
        return this.doctorIds;
    }

    public List<String> getLlsIds() {
        return this.llsIds;
    }

    public String getChargeStatus() {
        return this.chargeStatus;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setDoctorIds(List<String> doctorIds) {
        this.doctorIds = doctorIds;
    }

    public void setLlsIds(List<String> llsIds) {
        this.llsIds = llsIds;
    }

    public void setChargeStatus(String chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

}
