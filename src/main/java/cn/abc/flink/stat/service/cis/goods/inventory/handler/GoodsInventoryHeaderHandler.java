package cn.abc.flink.stat.service.cis.goods.inventory.handler;

import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.controller.api.GoodsInventoryController;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.GoodsInventoryParam;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryClassify;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryReport;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 进销存表头处理器
 * @author: lzq
 * @Date: 2022/10/24 4:11 下午
 */
@Component
public class GoodsInventoryHeaderHandler {
    private static final Logger logger = LoggerFactory.getLogger(GoodsInventoryHeaderHandler.class);
    @Autowired
    private DimensionQuery dimensionQuery;

    @Value("${abc.goods-inventory.purchaseActionClinic}")
    private String goodsInventoryPurchaseActionClinic;

    /**
     * 进销存统计-汇总维度表头处理(公用):
     * 代煎代配只有 采购入库，退药入库，发药出库，入库合计，出库合计
     * 单店和空中药房没有调拨
     * 库存-进销存统计-眼科:
     * 药品信息只有sku编码和规格 其他一致
     *
     * @param headerEmployeeItems 原始表头
     * @param param               进销存统计param
     * @return 处理后表头
     */
    public List<TableHeaderEmployeeItem> goodsV1(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                                 GoodsInventoryParam param, InventoryGoods summary) {
        boolean isOpenPharmacyFlagNumber;
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            isOpenPharmacyFlagNumber = dimensionQuery.queryOpenPharmacyFlagNumberByOrgan(TableUtils.getCisGoodsTable(), param.getChainId(), param.getClinicId()) == 20;
        } else {
            isOpenPharmacyFlagNumber = dimensionQuery.queryIsOpenPharmacyByOrgan(TableUtils.getCisGoodsTable(), param.getChainId());
        }
        DecimalFormat decimalFormat = new DecimalFormat(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR);
        if (decimalFormat.format(summary.getInInitCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("入库")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("初始化入库")
                            ).collect(Collectors.toList()));
                }
            }
        }
        if (decimalFormat.format(summary.getInInitReturnCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("出库")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("初始化退货")
                            ).collect(Collectors.toList()));
                }
            }
        }
        if (decimalFormat.format(summary.getInSpecificationModificationCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)
                && summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax() != null
                && decimalFormat.format(summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem ->
                    !tableHeaderEmployeeItem.getLabel().equals("修正")).collect(Collectors.toList());
        } else if (decimalFormat.format(summary.getInSpecificationModificationCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("修正")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("规格修改")
                            ).collect(Collectors.toList()));
                }
            }
        } else if (summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax() != null
                && decimalFormat.format(summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("修正")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("税率修改")
                            ).collect(Collectors.toList()));
                }
            }
        }
        // 诊所单店没有开多库房就不要领用入库
        if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)
                && !isOpenPharmacyFlagNumber) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("入库")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("领用入库")
                            ).collect(Collectors.toList()));
                }
            }
        }
        processingAllocationHeader(headerEmployeeItems, param, isOpenPharmacyFlagNumber);
        // 不是本地药房的只保存退药和发药
        if (param.getPharmacyType() != 0) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && (headerEmployeeItem.getLabel().equals("入库")
                        || headerEmployeeItem.getLabel().equals("出库"))) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    tableHeaderEmployeeItem.getLabel().equals("发药出库")
                                            || tableHeaderEmployeeItem.getLabel().equals("退药入库")
                                            || tableHeaderEmployeeItem.getLabel().equals("采购入库")
                                            || tableHeaderEmployeeItem.getLabel().equals("入库合计")
                                            || tableHeaderEmployeeItem.getLabel().equals("出库合计")
                            ).collect(Collectors.toList()));
                    for (TableHeaderEmployeeItem childItem : headerEmployeeItem.getColumnChildren()) {
                        if (childItem.getLabel().equals("发药出库")
                                || childItem.getLabel().equals("退药入库")) {
                            childItem.setColumnChildren(childItem.getColumnChildren()
                                    .stream().filter(tableHeaderEmployeeItem ->
                                            !tableHeaderEmployeeItem.getLabel().equals("成本(不含税)")
                                                    && !tableHeaderEmployeeItem.getLabel().equals("成本(含税)"))
                                    .collect(Collectors.toList()));
                        }
                    }
                }
            }
        }
        if (!StrUtil.isBlank(param.getHeaderType())
                && param.getHeaderType().equals(CommonConstants.CONTAINS_EYEGLASS_HEADER_TYPE)) {
            headerEmployeeItems = headerEmployeeItems.stream()
                    .filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getParentKey().equals("goodsMessage"))
                    .collect(Collectors.toList());
            List<TableHeaderEmployeeItem> list = new ArrayList<>();
            TableHeaderEmployeeItem goodsShortIdItem = new TableHeaderEmployeeItem();
            goodsShortIdItem.setLabel("SKU编码");
            goodsShortIdItem.setKey("goodsShortId");
            goodsShortIdItem.setProp("goodsShortId");
            goodsShortIdItem.setWidth(CommonConstants.NUMBER_ONE_HUNDRED_TWENTY);
            list.add(goodsShortIdItem);
            TableHeaderEmployeeItem specificationItem = new TableHeaderEmployeeItem();
            specificationItem.setLabel("规格");
            specificationItem.setKey("specification");
            specificationItem.setProp("specification");
            specificationItem.setWidth(CommonConstants.NUMBER_ONE_HUNDRED_THIRTY_TWO);
            list.add(specificationItem);
            list.addAll(headerEmployeeItems);
            return list;
        }
        List<String> clinicIdList = CollUtil.newArrayList(goodsInventoryPurchaseActionClinic.split(","));
        if (clinicIdList.contains(param.getChainId())) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("入库")) {
                    for (TableHeaderEmployeeItem columnChild : headerEmployeeItem.getColumnChildren()) {
                        if (columnChild.getLabel().equals("采购入库")) {
                            columnChild.setLabel("购进入库");
                        }
                    }
                }
            }
        }
        return headerEmployeeItems;
    }

    /**
     * 是否筛选库房 则只展示店内调拨
     * 1.筛选 只展示店内调拨
     * (1).单店只展示店内调拨
     * 2.不筛选判断是否开通多库房
     * (1).开通 判断是否是单店 单店只有店内调拨 否则展示店间调拨
     * (2).没开通  单店不展示 其他展示店间调拨
     *
     * @param headerEmployeeItems      表头
     * @param param                    进销存param
     * @param isOpenPharmacyFlagNumber 是否开通多库房
     */
    private static void processingAllocationHeader(List<TableHeaderEmployeeItem> headerEmployeeItems, GoodsInventoryParam param, boolean isOpenPharmacyFlagNumber) {

        if (!CollUtil.isEmpty(param.getPharmacyNos())) {
            if (param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                    if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                            && (headerEmployeeItem.getLabel().equals("入库")
                            || headerEmployeeItem.getLabel().equals("出库"))) {
                        headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                                .stream().filter(tableHeaderEmployeeItem ->
                                        !tableHeaderEmployeeItem.getLabel().equals("调拨入库(店间调拨)")
                                                && !tableHeaderEmployeeItem.getLabel().equals("调拨出库(店间调拨)")
                                ).collect(Collectors.toList()));
                    }
                }
            }
        } else {
            if (isOpenPharmacyFlagNumber) {
                if (param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                    for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                        if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                                && (headerEmployeeItem.getLabel().equals("入库")
                                || headerEmployeeItem.getLabel().equals("出库"))) {
                            headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                                    .stream().filter(tableHeaderEmployeeItem ->
                                            !tableHeaderEmployeeItem.getLabel().equals("调拨入库(店间调拨)")
                                                    && !tableHeaderEmployeeItem.getLabel().equals("调拨出库(店间调拨)")
                                    ).collect(Collectors.toList()));
                        }
                    }
                } else {
                    for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                        if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                                && (headerEmployeeItem.getLabel().equals("入库")
                                || headerEmployeeItem.getLabel().equals("出库"))) {
                            headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                                    .stream().filter(tableHeaderEmployeeItem ->
                                            !tableHeaderEmployeeItem.getLabel().equals("调拨入库(店内调拨)")
                                                    && !tableHeaderEmployeeItem.getLabel().equals("调拨出库(店内调拨)")
                                    ).collect(Collectors.toList()));
                        }
                    }
                }
            } else {
                if (param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                    for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                        if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                                && (headerEmployeeItem.getLabel().equals("入库")
                                || headerEmployeeItem.getLabel().equals("出库"))) {
                            headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                                    .stream().filter(tableHeaderEmployeeItem ->
                                            !tableHeaderEmployeeItem.getLabel().equals("调拨入库(店内调拨)")
                                                    && !tableHeaderEmployeeItem.getLabel().equals("调拨出库(店内调拨)")
                                                    && !tableHeaderEmployeeItem.getLabel().equals("调拨入库(店间调拨)")
                                                    && !tableHeaderEmployeeItem.getLabel().equals("调拨出库(店间调拨)")
                                    ).collect(Collectors.toList()));
                        }
                    }
                } else {
                    for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                        if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                                && (headerEmployeeItem.getLabel().equals("入库")
                                || headerEmployeeItem.getLabel().equals("出库"))) {
                            headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                                    .stream().filter(tableHeaderEmployeeItem ->
                                            !tableHeaderEmployeeItem.getLabel().equals("调拨入库(店内调拨)")
                                                    && !tableHeaderEmployeeItem.getLabel().equals("调拨出库(店内调拨)")
                                    ).collect(Collectors.toList()));
                        }
                    }
                }
            }

        }
    }

    /**
     * 进销存单据表头处理(公用):
     * 如果是药品维度过滤掉生产批号和供应商;
     * 库存-进销存统计-眼科:
     * 类型后添加规格表头;
     * 如果是医院：
     * 判断是否有未结算数量给提示;
     *
     * @param headerEmployeeItems headerEmployeeItems
     * @param param               进销存统计param
     * @param notSettleCount      未结算数量
     * @return -
     */
    public List<TableHeaderEmployeeItem> recordV1(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                                  GoodsInventoryParam param,
                                                  Long notSettleCount) {
        List<String> list = new ArrayList<>();
        if (param.getDimension() == 1) {
            list.add("供应商");
            list.add("生产批号");
            if (!StrUtil.isBlank(param.getHeaderType()) && param.getHeaderType().equals("statistics")) {
                list.add("生产日期");
            } else {
                list.add("效期");
            }
        }
        for (String label : list) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem ->
                    !tableHeaderEmployeeItem.getLabel().equals(label)
            ).collect(Collectors.toList());
        }
        //如果不是医院，则隐藏费用分类选项
        if (param.getHisType() != null && !param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(header ->
                    !header.getLabel().equalsIgnoreCase("费用类型")).collect(Collectors.toList());
            if (notSettleCount != null && notSettleCount > 0) {
                headerEmployeeItems.forEach(tableHeaderEmployeeItem -> {
                    if (tableHeaderEmployeeItem.getProp().equals("actionPrice")
                            || tableHeaderEmployeeItem.getProp().equals("actionPriceText")) {
                        tableHeaderEmployeeItem.setDescription("部分住院药品未结算");
                    }
                });
            }
        } else {
            if (notSettleCount != null && notSettleCount > 0) {
                headerEmployeeItems.forEach(tableHeaderEmployeeItem -> {
                    if (tableHeaderEmployeeItem.getProp().equals("actionPrice")
                            || tableHeaderEmployeeItem.getProp().equals("actionPriceText")) {
                        tableHeaderEmployeeItem.setDescription("部分住院药品未结算");
                    }
                });
            }
        }
        if (!StrUtil.isBlank(param.getHeaderType()) && param.getHeaderType().equals("stock")) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(header ->
                    !header.getLabel().equalsIgnoreCase("费用类型")).collect(Collectors.toList());
        }
        if (param.getDimension() == 1) {
            //药品维度去掉库房名字
            headerEmployeeItems = headerEmployeeItems.stream().filter(header ->
                    !header.getLabel().equalsIgnoreCase("库房")).collect(Collectors.toList());
        }
        if (!StrUtil.isBlank(param.getHeaderType())
                && param.getHeaderType().equals(CommonConstants.CONTAINS_EYEGLASS_HEADER_TYPE)) {
            List<TableHeaderEmployeeItem> result = new ArrayList<>();
            headerEmployeeItems.forEach(tableHeaderEmployeeItem -> {
                result.add(tableHeaderEmployeeItem);
                if (tableHeaderEmployeeItem.getKey().equals("action")) {
                    TableHeaderEmployeeItem headerEmployeeItem = new TableHeaderEmployeeItem();
                    headerEmployeeItem.setLabel("规格");
                    headerEmployeeItem.setProp("specification");
                    headerEmployeeItem.setKey("specification");
                    headerEmployeeItem.setWidth(CommonConstants.ONE_HUNDRED);
                    result.add(headerEmployeeItem);
                }
            });
            return result;
        }
        if (param.getChainId().equals("ffffffff000000001dbce25809ffc000")
                || param.getChainId().equals("ffffffff00000000347170353eb54000")
                || param.getChainId().equals("ffffffff0000000034a2db005f8ac000")) {
            TableHeaderEmployeeItem batchIdHeaderItem = new TableHeaderEmployeeItem();
            batchIdHeaderItem.setLabel("批次id");
            batchIdHeaderItem.setProp("batchId");
            batchIdHeaderItem.setKey("batchId");
            batchIdHeaderItem.setWidth(CommonConstants.ONE_HUNDRED);
            headerEmployeeItems.add(batchIdHeaderItem);
            TableHeaderEmployeeItem patientOrderIdHeaderItem = new TableHeaderEmployeeItem();
            patientOrderIdHeaderItem.setLabel("就诊单id");
            patientOrderIdHeaderItem.setProp("patientOrderId");
            patientOrderIdHeaderItem.setKey("patientOrderId");
            patientOrderIdHeaderItem.setWidth(CommonConstants.ONE_HUNDRED);
            headerEmployeeItems.add(patientOrderIdHeaderItem);
        }
        return headerEmployeeItems;
    }

    /**
     * 如果有未结算药品表头提示处理
     *
     * @param headerEmployeeItems 表头data
     * @param noSettleData        未结算数据
     * @param param               进销存报表param
     * @return 处理后表头
     */
    public List<TableHeaderEmployeeItem> handlerReportHeader(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                                             InventoryReport noSettleData,
                                                             GoodsInventoryParam param) {
        if (noSettleData != null) {
            if (noSettleData.getHospitalPharmacyNoSettleDispenseCount() != null
                    && !noSettleData.getHospitalPharmacyNoSettleDispenseCount().equals(BigDecimal.ZERO)) {
                headerEmployeeItems.forEach(tableHeaderEmployeeItem -> {
                    if (!CollUtil.isEmpty(tableHeaderEmployeeItem.getColumnChildren())) {
                        tableHeaderEmployeeItem.getColumnChildren().forEach(headerEmployeeItem -> {
                            if (headerEmployeeItem.getProp().equals("hospitalPharmacyDispensePrice")) {
                                headerEmployeeItem.setDescription("部分住院药品未结算");
                            }
                        });
                    }
                });
            }
            if (noSettleData.getHospitalAutomaticNoSettleDispenseCount() != null
                    && !noSettleData.getHospitalAutomaticNoSettleDispenseCount().equals(BigDecimal.ZERO)) {
                headerEmployeeItems.forEach(tableHeaderEmployeeItem -> {
                    if (!CollUtil.isEmpty(tableHeaderEmployeeItem.getColumnChildren())) {
                        tableHeaderEmployeeItem.getColumnChildren().forEach(headerEmployeeItem -> {
                            if (headerEmployeeItem.getProp().equals("hospitalAutomaticDispensePrice")) {
                                headerEmployeeItem.setDescription("部分住院药品未结算");
                            }
                        });
                    }
                });
            }
        }
        if (param.getClassifyStatisticsType() != 2) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getLabel().equals("二级分类")).collect(Collectors.toList());
        }
        return headerEmployeeItems;
    }

    /**
     * 处理分类表头
     *
     * @param headerEmployeeItems 表头
     * @param param               param
     * @param summary             合计行数据
     * @return
     */
    public List<TableHeaderEmployeeItem> handlerClassifyHeader(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                                               GoodsInventoryParam param,
                                                               InventoryClassify summary) {
        boolean isOpenPharmacyFlagNumber;
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            isOpenPharmacyFlagNumber = dimensionQuery.queryOpenPharmacyFlagNumberByOrgan(TableUtils.getCisGoodsTable(), param.getChainId(), param.getClinicId()) == 20;
        } else {
            isOpenPharmacyFlagNumber = dimensionQuery.queryIsOpenPharmacyByOrgan(TableUtils.getCisGoodsTable(), param.getChainId());
        }
        DecimalFormat decimalFormat = new DecimalFormat(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR);
        if (decimalFormat.format(summary.getInInitCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("入库")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("初始化入库")
                            ).collect(Collectors.toList()));
                }
            }
        }
        if (decimalFormat.format(summary.getInInitReturnCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("出库")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("初始化退货")
                            ).collect(Collectors.toList()));
                }
            }
        }
        if (decimalFormat.format(summary.getInSpecificationModificationCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)
                && summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax() != null
                && decimalFormat.format(summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem ->
                    !tableHeaderEmployeeItem.getLabel().equals("修正")).collect(Collectors.toList());
        } else if (decimalFormat.format(summary.getInSpecificationModificationCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("修正")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("规格修改")
                            ).collect(Collectors.toList()));
                }
            }
        } else if (summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax() != null
                && decimalFormat.format(summary.getGoodsPharmacyAfterTotalCostModifyExcludeTax()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("修正")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("税率修改")
                            ).collect(Collectors.toList()));
                }
            }
        }
        // 诊所没有开多库房就不要领用入库
        if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)
                && !isOpenPharmacyFlagNumber) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (!CollUtil.isEmpty(headerEmployeeItem.getColumnChildren())
                        && headerEmployeeItem.getLabel().equals("入库")) {
                    headerEmployeeItem.setColumnChildren(headerEmployeeItem.getColumnChildren()
                            .stream().filter(tableHeaderEmployeeItem ->
                                    !tableHeaderEmployeeItem.getLabel().equals("领用入库")
                            ).collect(Collectors.toList()));
                }
            }
        }
        // 是否筛选库房 则只展示店内调拨
        // 1.筛选 只展示店内调拨
        // 2.不筛选判断是否开通多库房
        //    1.开通 判断是否是单店 单店只有店内调拨 否则两个都展示
        //    2.没开通  单店不展示 其他展示店间调拨
        processingAllocationHeader(headerEmployeeItems, param, isOpenPharmacyFlagNumber);
        return headerEmployeeItems;
    }

    /**
     * 南昌进销存导出需要添加医疗机构编码、医疗机构名称、国家贯标名称
     *
     * @param summaryHeaderList 汇总表头
     * @return 处理后表头
     */
    public List<TableHeaderEmployeeItem> nanChangHeaderHandler(List<TableHeaderEmployeeItem> summaryHeaderList) {
        TableHeaderEmployeeItem nationalStandardImplementationNameItem = new TableHeaderEmployeeItem();
        nationalStandardImplementationNameItem.setLabel("国家贯标名称");
        nationalStandardImplementationNameItem.setKey("nationalStandardImplementationName");
        nationalStandardImplementationNameItem.setProp("nationalStandardImplementationName");
        nationalStandardImplementationNameItem.setWidth(CommonConstants.NUMBER_ONE_HUNDRED_TWENTY);
        summaryHeaderList.add(nationalStandardImplementationNameItem);
        return summaryHeaderList;
    }
}
