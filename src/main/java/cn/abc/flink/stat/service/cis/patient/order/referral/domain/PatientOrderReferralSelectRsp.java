package cn.abc.flink.stat.service.cis.patient.order.referral.domain;



import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/24 2:03 下午
 */
public class PatientOrderReferralSelectRsp {
    /**
     * 转出医生
     */
    private List<PatientOrderReferralDoctor> referralOutDoctorList;
    /**
     * 转入医生
     */
    private List<PatientOrderReferralDoctor> referralInDoctorList;

    public PatientOrderReferralSelectRsp() {
        this.referralOutDoctorList = new ArrayList<>();
        this.referralInDoctorList = new ArrayList<>();
    }

    public List<PatientOrderReferralDoctor> getReferralOutDoctorList() {
        return this.referralOutDoctorList;
    }

    public List<PatientOrderReferralDoctor> getReferralInDoctorList() {
        return this.referralInDoctorList;
    }


    public void setReferralOutDoctorList(List<PatientOrderReferralDoctor> referralOutDoctorList) {
        this.referralOutDoctorList = referralOutDoctorList;
    }

    public void setReferralInDoctorList(List<PatientOrderReferralDoctor> referralInDoctorList) {
        this.referralInDoctorList = referralInDoctorList;
    }

}
