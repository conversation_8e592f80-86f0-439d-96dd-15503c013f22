package cn.abc.flink.stat.service.cis.check;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.CheckParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-04-11 13:39
 **/
@Component
public class CheckListAsyncExportImpl implements BaseAsyncExportInterface {
    @Autowired
    private CheckService checkService;

    @Override
    public String getKey() {
        return "check-list";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        return "盘点盈亏" + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String headerClinicId = (String) MapUtils.isExistsAndReturn(params, "headerClinicId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String employeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String chainViewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        //视图模式，是否多药房
        Double isMultiPharmacy = (Double) MapUtils.isExistsAndReturn(params, "isMultiPharmacy");
        int isMultiPharmacy1 = isMultiPharmacy.intValue();
        CheckParam param = new CheckParam();
        param.initAbcCisBaseQueryParams(chainId, clinicId);
        param.setHeaderClinicId(headerClinicId);
        param.getParams().setEmployeeId(employeeId);
        param.getParams().setNodeType(clinicNodeType);
        param.setIsMultiPharmacy(isMultiPharmacy1);
        if (chainViewMode != null) {
            param.getParams().setViewMode(chainViewMode);
        }
        param.setSingleStore(chainViewMode, String.valueOf(clinicNodeType));
        param.setBeginDate(beginDate);
        param.setEndDate(endDate);
        param.initParam();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        List<ExcelUtils.AbcExcelSheet> sheets = checkService.asyncCheckListExport(param);
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
