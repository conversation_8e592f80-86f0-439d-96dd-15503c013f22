package cn.abc.flink.stat.service.cis.disease.domain;
/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/2 19:19
 */
public class DiseaseProp {
    private String chainId;
    private String clinicId;
    private String clinicName;
    private String category;
    private String diagnose;
    private Integer summary;
    private double percent;


    public double getPercent() {
        return percent;
    }

    public void setPercent(double percent) {
        this.percent = percent;
    }

    public String getClinicName() {
        return clinicName;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public String getDiagnose() {
        return diagnose;
    }

    public void setDiagnose(String diagnose) {
        this.diagnose = diagnose;
    }

    public Integer getSummary() {
        return summary;
    }

    public void setSummary(Integer summary) {
        this.summary = summary;
    }


    public String getChainId() {
        return chainId;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public String getClinicId() {
        return clinicId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    @Override
    public String toString() {
        return "DiseaseProp{"
                + "chainId='" + chainId + '\''
                + ", clinicId='" + clinicId + '\''
                + ", diagnose='" + diagnose + '\''
                + ", summary=" + summary
                + ", percent='" + percent + '\''
                + '}';
    }
}
