package cn.abc.flink.stat.service.cis.commission.domain;

import cn.abc.flink.stat.service.cis.commission.pojos.V3CommissionOperationLogBase;

import lombok.EqualsAndHashCode;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/22 16:13
 */
@EqualsAndHashCode(callSuper = false)
public class V3CommissionOperationLog extends V3CommissionOperationLogBase {
    protected String oldLog;

    public String getOldLog() {
        return this.oldLog;
    }


    public void setOldLog(String oldLog) {
        this.oldLog = oldLog;
    }

}
