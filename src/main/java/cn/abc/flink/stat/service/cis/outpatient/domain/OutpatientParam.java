package cn.abc.flink.stat.service.cis.outpatient.domain;

import cn.abc.flink.stat.common.ObjectUtils;
import cn.abc.flink.stat.common.request.params.AbcScStatFilterEmployee;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.dimension.domain.SnapEmployee;
import cn.abc.flink.stat.service.cis.handler.EmployeeHandler;

import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.outpatient.domain
 * @Author: zs
 * @CreateTime: 2022-08-10  18:57
 * @Description: 患者清单参数实体类
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = false)
public class OutpatientParam extends AbcScStatRequestParams {

    private String patientId;
    private Integer pageIndex;
    private Integer pageSize;
    private String type;
    /**
     * 是否只看传染病 1是 0否
     */
    private Integer isInfectiousDiseases;
    private String employeeId;

    private List<AbcScStatFilterEmployee> filterDoctorParams;
    private String employees;
    private String searchDoctorSql;
    /**
     * 科室id
     */
    private String departmentId;

    public OutpatientParam() {
    }

    public OutpatientParam(String chainId, String clinicId, String beginDate, String endDate, String employee) {
        this.chainId = chainId;
        this.clinicId = clinicId;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.employeeId = employee;
        this.initDs("yyyy");
    }
    public void initParam() {
        this.initBeginDateAndEndDate();
        this.initDs("yyyy");
        if (this.employees != null) {
            this.filterDoctorParams = ObjectUtils.jsonStrList2Object(this.employees, AbcScStatFilterEmployee.class);
        }
    }

    public String getPatientId() {
        return this.patientId;
    }

    public Integer getPageIndex() {
        return this.pageIndex;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public String getType() {
        return this.type;
    }

    public Integer getIsInfectiousDiseases() {
        return this.isInfectiousDiseases;
    }

    public String getEmployeeId() {
        return this.employeeId;
    }

    public List<AbcScStatFilterEmployee> getFilterDoctorParams() {
        return this.filterDoctorParams;
    }

    public String getEmployees() {
        return this.employees;
    }

    public String getSearchDoctorSql() {
        return this.searchDoctorSql;
    }


    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setIsInfectiousDiseases(Integer isInfectiousDiseases) {
        this.isInfectiousDiseases = isInfectiousDiseases;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public void setFilterDoctorParams(List<AbcScStatFilterEmployee> filterDoctorParams) {
        this.filterDoctorParams = filterDoctorParams;
    }

    public void setEmployees(String employees) {
        this.employees = employees;
    }

    public void setSearchDoctorSql(String searchDoctorSql) {
        this.searchDoctorSql = searchDoctorSql;
    }

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }
}
