package cn.abc.flink.stat.service.customize.revenue.entity;



import java.math.BigDecimal;

/**
 * @description:
 * @author: dy
 * @create: 2021-08-04 10:39
 */
public class GoodsChargeTransactionAmountByChargeFormItemIdsDao {
    private String goodsId;
    private String itemId;
    private BigDecimal amount;
    private BigDecimal deductAmount;

    public String getGoodsId() {
        return this.goodsId;
    }

    public String getItemId() {
        return this.itemId;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public BigDecimal getDeductAmount() {
        return this.deductAmount;
    }


    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setDeductAmount(BigDecimal deductAmount) {
        this.deductAmount = deductAmount;
    }

}
