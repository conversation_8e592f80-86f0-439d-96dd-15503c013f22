package cn.abc.flink.stat.service.cis.selection.entity;

import cn.abc.flink.stat.common.EmployeeTypeEnum;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;

import lombok.ToString;

/**
 * 筛选框入参
 */
@EqualsAndHashCode(callSuper = true)
@ToString
public class CommSelectParams extends AbcScStatRequestParams {
    /**
     * 是否包含代录人
     */
    private Integer includeWriter;

    /**
     * payMode是否包含还款的部分
     */
    private Integer includeRepayment;

    /**
     * 包含卡项
     */
    private Integer includeCard;

    /**
     * 费用类型是否需要排序
     */
    private Integer needSort;
    /**
     * 是否包含挂号费
     */
    private Integer includeReg;
    /**
     * 是否包含发药的部分
     */
    private Integer includeDispensingCost;
    private EmployeeTypeEnum employeeTypeEnum;

    /**
     * 人员类型
     */
    private Integer employeeTypeNumber;

    /**
     * 组合来源
     * 科室筛选框：
     * 收费相关 1：结算下达科室(营收成本统计-收款对账报表-住院收费日报-科室/营收成本统计-住院结算统计-项目-下达科室)
     * 2：结算科室(营收成本统计-住院结算统计-单据-科室/营收成本统计-收费项目统计-住院-科室)
     * 3：收费charge_sheet科室(营收成本统计-门诊患者费用查询-开单科室)
     * 4：收费charge transaction科室(营收成本统计-收费项目统计-门诊-科室)
     * 5：收费charge transaction科室 + 结算科室(营收成本统计-收费项目统计-汇总-科室)
     * 药房相关 1：住院发药科室(业绩统计-住院药房业绩-药房)
     * 住院相关 1：住院工作报表科室(住院医务统计-住院工作报表-科室)
     * 人员筛选框：
     *      收费相关 1：结算收费员(营收成本统计-收款对账报表-住院收费日报-收费员/营收成本统计-住院结算统计-单据-收费员/营收成本统计-住院结算统计-项目-收费员)
     *              2：体检收费员(营收成本统计-收款对账报表-体检收费日报-收费员)
     *              3：收费开单人医生(营收成本统计-门诊患者费用查询-开单人)
     *              4：护理护士(业绩统计-住院护理业绩-护士)
     *              5：门诊收费员+结算收费员+体检收费员+医保收费员(营收成本统计-收款对账报表-收费员日报-收费员)
     *      药房相关 1：住院发药人(业绩统计-住院药房业绩-操作人)
     *      门诊相关 1：门诊日志医生(门诊医务统计-门诊日志-医生)
     *              2：住院日志入院时间医生(住院医务统计-住院日志-入院时间-医生)
     *              3：住院日志出院时间医生(住院医务统计-住院日志-出院时间-医生)
     *      营销相关 1：卡项开单人(营销统计-卡项统计-已抵扣项目-开单人)
     *              2：未抵扣项目开卡人(营销统计-卡项统计-未抵扣项目-开卡人)
     *              3：老带新推荐人(营销统计-老带新-推荐人)
     * 病区筛选框：
     * 收费相关 1：结算执行病区(营收成本统计-住院结算统计-项目-执行病区)
     * 2：结算病区(营收成本统计-住院结算统计-单据-病区)
     * 3：计费病区(营收成本统计-住院计费统计-患者-病区/营收成本统计-住院计费统计-项目-病区)
     * 4：医嘱统计病区(住院医务统计-医嘱统计-明细-病区)
     * 5：护理病区(业绩统计-住院护理业绩-病区)
     * 药房相关 1：住院药房病区(业绩统计-住院药房业绩-病区)
     * 住院相关 1：住院工作报表病区(住院医务统计-住院工作报表-病区)
     * 费用分类筛选框：
     * 收费相关 1：结算费用分类(营收成本统计-收费项目统计-住院-费用分类)
     * 护理相关 1：护理费用分类(业绩统计-住院护理业绩-费用分类)
     * 项目分类筛选框：
     * 药房相关 1：住院项目分类(业绩统计-住院药房业绩-项目分类)
     * 支付方式筛选框：
     * 收费相关 1：结算支付方式(营收成本统计-住院结算统计-单据-收费员)
     */
    private Integer scope;

    /**
     * 套餐是否平摊到项目 1：是 0：否
     */
    private Integer isComposeShareEqually;

    /**
     * 欠费收入统计时机 1.欠费时计入收入 2.还款时计入收入
     */
    private Integer arrearsStatTiming;

    /**
     * 卡项费用计提：开卡费用 1是 0否
     * ：卡项抵扣金额（按原价计提） 这个上面有
     */
    private Integer isCardOpeningFee;
    /**
     * 是否计提金额包含卡项扣除金额 1.是 0.否
     */
    private Integer isIncludePromotionCard;
    private String composeSql;

    private StatConfigDto dto;

    @ApiModelProperty(value = "类型(检查：2，检验：1)")
    private Integer subType;

    public CommSelectParams() {
    }

    public void setStatConfigDto(StatConfigDto config) {
        this.isComposeShareEqually = config.getIsComposeShareEqually();
        this.arrearsStatTiming = config.getArrearsStatTiming();
        this.composeSql = config.buildSqlAboutComposeType();
    }

    public CommSelectParams(String chainId, String clinicId, String beginDate, String endDate,
                            String hisType, Integer employeeTypeNumber, StatConfigDto dto) {
        this.chainId = chainId;
        this.clinicId = clinicId;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.hisType = hisType;
        this.employeeTypeNumber = employeeTypeNumber;
        this.dto = dto;
    }

    public CommSelectParams(String chainId, String clinicId, Integer includeWriter, String beginDate, String endDate,
                            Integer includeReg, Integer dispensaryType,  String hisType, Integer employeeTypeNumber) {
        this.chainId = chainId;
        this.clinicId = clinicId;
        this.includeWriter = includeWriter;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.includeReg = includeReg;
        this.dispensaryType = dispensaryType;
        this.hisType = hisType;
        this.employeeTypeNumber = employeeTypeNumber;
    }

    public Integer getIncludeWriter() {
        return this.includeWriter;
    }

    public Integer getIncludeRepayment() {
        return this.includeRepayment;
    }

    public Integer getIncludeCard() {
        return this.includeCard;
    }

    public Integer getNeedSort() {
        return this.needSort;
    }

    public Integer getIncludeReg() {
        return this.includeReg;
    }

    public Integer getIncludeDispensingCost() {
        return this.includeDispensingCost;
    }

    public EmployeeTypeEnum getEmployeeTypeEnum() {
        return this.employeeTypeEnum;
    }

    public Integer getEmployeeTypeNumber() {
        return this.employeeTypeNumber;
    }

    public Integer getScope() {
        return this.scope;
    }

    public Integer getIsComposeShareEqually() {
        return this.isComposeShareEqually;
    }

    public Integer getArrearsStatTiming() {
        return this.arrearsStatTiming;
    }

    public Integer getIsCardOpeningFee() {
        return this.isCardOpeningFee;
    }

    public Integer getIsIncludePromotionCard() {
        return this.isIncludePromotionCard;
    }

    public String getComposeSql() {
        return this.composeSql;
    }

    public StatConfigDto getDto() {
        return this.dto;
    }


    public void setIncludeWriter(Integer includeWriter) {
        this.includeWriter = includeWriter;
    }

    public void setIncludeRepayment(Integer includeRepayment) {
        this.includeRepayment = includeRepayment;
    }

    public void setIncludeCard(Integer includeCard) {
        this.includeCard = includeCard;
    }

    public void setNeedSort(Integer needSort) {
        this.needSort = needSort;
    }

    public void setIncludeReg(Integer includeReg) {
        this.includeReg = includeReg;
    }

    public void setIncludeDispensingCost(Integer includeDispensingCost) {
        this.includeDispensingCost = includeDispensingCost;
    }

    public void setEmployeeTypeEnum(EmployeeTypeEnum employeeTypeEnum) {
        this.employeeTypeEnum = employeeTypeEnum;
    }

    public void setEmployeeTypeNumber(Integer employeeTypeNumber) {
        this.employeeTypeNumber = employeeTypeNumber;
    }

    public void setScope(Integer scope) {
        this.scope = scope;
    }

    public void setIsComposeShareEqually(Integer isComposeShareEqually) {
        this.isComposeShareEqually = isComposeShareEqually;
    }

    public void setArrearsStatTiming(Integer arrearsStatTiming) {
        this.arrearsStatTiming = arrearsStatTiming;
    }

    public void setIsCardOpeningFee(Integer isCardOpeningFee) {
        this.isCardOpeningFee = isCardOpeningFee;
    }

    public void setIsIncludePromotionCard(Integer isIncludePromotionCard) {
        this.isIncludePromotionCard = isIncludePromotionCard;
    }

    public void setComposeSql(String composeSql) {
        this.composeSql = composeSql;
    }

    public void setDto(StatConfigDto dto) {
        this.dto = dto;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }
}
