package cn.abc.flink.stat.service.cis.outpatient;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.common.request.params.AbcScStatFilterEmployee;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientParam;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2021-12-21 15:47
 **/
@Component
public class OutpatientAsynExportService implements BaseAsyncExportInterface {
    private static final Logger LOGGER = LoggerFactory.getLogger(OutpatientAsynExportService.class);
    @Autowired
    private OutpatientService service;

    @Override
    public String getKey() {
        return "outpatient-list";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String fileName = "门诊日志" + beginDate + "_" + endDate + ".xlsx";
        return fileName;
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        LOGGER.info("门诊日志异步导出key：outpatient-list 参数:{}", JSON.toJSONString(params));
        OutpatientParam param = new OutpatientParam();
        param.setChainId((String) MapUtils.isExistsAndReturn(params, "chainId"));
        param.setClinicId((String) MapUtils.isExistsAndReturn(params, "clinicId"));
        param.setBeginDate((String) MapUtils.isExistsAndReturn(params, "beginDate"));
        param.setEndDate((String) MapUtils.isExistsAndReturn(params, "endDate"));
        param.setPatientId((String) MapUtils.isExistsAndReturn(params, "patientId"));
        param.setEmployeeId((String) MapUtils.isExistsAndReturn(params, "employeeId"));
        param.setDepartmentId((String) MapUtils.isExistsAndReturn(params, "departmentId"));
        param.setType((String) MapUtils.isExistsAndReturn(params, "type"));
        param.setIsInfectiousDiseases(Double.valueOf((double) params.get("isInfectiousDiseases")).intValue());
        param.setEnablePatientMobile(ConvertUtils.getAsInteger(params.get("enablePatientMobile")));

        String headerClinicId = (String) MapUtils.isExistsAndReturn(params, "headerClinicId");
        Integer clinicType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String viewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        String currentEmployeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");

        Object objHeaderHisType = MapUtils.isExistsAndReturn(params, "headerHisType");
        List<AbcScStatFilterEmployee> employees = JSON.parseArray(JSON.toJSONString(MapUtils.isExistsAndReturn(params, "employees")), AbcScStatFilterEmployee.class);
        String hisType = "0";
        if (objHeaderHisType != null) {
            hisType = (String) objHeaderHisType;
        }
        AbcCisBaseQueryParams p = new AbcCisBaseQueryParams(param.getChainId(),
                param.getClinicId(), headerClinicId, clinicType, viewMode);
        param.setChainId(p.getChainId());
        param.setClinicId(p.getClinicId());
        param.setFilterDoctorParams(employees);
        param.initParam();
        param.setIsExport(1);
        List<ExcelUtils.AbcExcelSheet> sheeets = service.
                export(param, hisType, currentEmployeeId, Integer.parseInt(viewMode), clinicType);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtils.export(baos, sheeets);
        return baos;
    }
}
