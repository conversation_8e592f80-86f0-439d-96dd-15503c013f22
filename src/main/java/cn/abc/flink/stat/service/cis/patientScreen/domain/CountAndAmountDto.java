package cn.abc.flink.stat.service.cis.patientScreen.domain;



import java.math.BigDecimal;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.patientScreen.domain
 * @Author: zs
 * @CreateTime: 2024-04-16  17:31
 * @Description: -
 * @Version: 1.0
 */
public class CountAndAmountDto {
    /**
     * 患者id
     */
    private String patientId;
    /**
     * 次数
     */
    private Integer count;
    /**
     * 金额
     */
    private BigDecimal amount;

    public String getPatientId() {
        return this.patientId;
    }

    public Integer getCount() {
        return this.count;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }


    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

}
