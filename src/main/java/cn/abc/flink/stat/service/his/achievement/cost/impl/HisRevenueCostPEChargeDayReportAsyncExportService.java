package cn.abc.flink.stat.service.his.achievement.cost.impl;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.service.his.achievement.cost.IHisRevenuesCostSevice;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisChargedWardSelectionResp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostReqParams;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * @description: 营收成本统计-住院押金统计-异步导出
 * @author: lzq
 * @Date: 2023/12/11 20:05
 */
@Component
public class HisRevenueCostPEChargeDayReportAsyncExportService implements BaseAsyncExportInterface {

    @Autowired
    private IHisRevenuesCostSevice hisRevenuesCostSevice;

    @Override
    public String getKey() {
        return "his-revenue-cost-pe-charge-day-report";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        return "体检收费报表" + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params)
            throws Exception {
        // 参数构造
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String viewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        String headerEmployeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String chargeId = (String) MapUtils.isExistsAndReturn(params, "chargeId");

        HisRevenuesCostReqParams reqParams = new HisRevenuesCostReqParams();
        reqParams.initAbcCisBaseQueryParams(chainId, clinicId, headerEmployeeId, viewMode, clinicNodeType);
        reqParams.initBeginDateAndEndDate(beginDate, endDate);
        reqParams.setSingleStore(viewMode, String.valueOf(clinicNodeType));
        reqParams.setChargeId(chargeId);
        if (!reqParams.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            reqParams.setClinicId(reqParams.getHeaderClinicId());
        }
        List<ExcelUtils.AbcExcelSheet> sheets = hisRevenuesCostSevice.peChargeDayReportAsyncExport(reqParams);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelUtils.export(byteArrayOutputStream, sheets);
        return byteArrayOutputStream;
    }
}
