package cn.abc.flink.stat.service.cis.hospital.revenue.charge.impl;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.service.cis.handler.GoodsHandler;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.HospitalRevenueChargeService;
import cn.abc.flink.stat.service.cis.hospital.revenue.charge.domain.HospitalRevenueChargeParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * @description: 医院管家-住院收费-异步导出
 * @author: lzq
 * @Date: 2023-03-07
 */
@Component
public class HospitalRevenueChargeAsyncExportService implements BaseAsyncExportInterface {


    @Autowired
    private HospitalRevenueChargeService hospitalRevenueChargeService;

    @Resource
    private GoodsHandler goodsHandler;

    @Override
    public String getKey() {
        return "hospital-revenue-charge";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        return "住院收费" + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params)
            throws Exception {
        HospitalRevenueChargeParam param = new HospitalRevenueChargeParam();
        String viewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        Integer nodeType = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "headerClinicType"));
        String employeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        String headerClinicId = (String) MapUtils.isExistsAndReturn(params, "headerClinicId");
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        Integer type = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "type"));
        String payModes = (String) MapUtils.isExistsAndReturn(params, "payModes");
        String sellerId = (String) MapUtils.isExistsAndReturn(params, "sellerId");
        String departmentId = (String) MapUtils.isExistsAndReturn(params, "departmentId");
        String wardId = (String) MapUtils.isExistsAndReturn(params, "wardId");
        String patientId = (String) MapUtils.isExistsAndReturn(params, "patientId");
        String productId = (String) MapUtils.isExistsAndReturn(params, "productId");
        String keyword = (String) MapUtils.isExistsAndReturn(params, "keyword");
        String feeTypeIds = ((String) MapUtils.isExistsAndReturn(params, "feeTypeIds"));
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        param.initAbcCisBaseQueryParams(chainId, clinicId, employeeId, viewMode, nodeType);
        if (viewMode != null) {
            param.setSingleStore(viewMode, String.valueOf(nodeType));
        }
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(headerClinicId);
        }
        param.initBeginDateAndEndDate(beginDate, endDate);
        param.setType(type);
        param.setPayModes(payModes);
        param.setSellerId(sellerId);
        param.setDepartmentId(departmentId);
        param.setWardId(wardId);
        param.setPatientId(patientId);
        param.setProductId(productId);
        param.setKeyword(keyword);
        param.setProductSearch(goodsHandler);
        param.setPayModes();
        param.setFeeTypeIds(feeTypeIds);
        param.setFeeTypeIds();
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet transactionSheet =
                hospitalRevenueChargeService.asyncTransactionExport(param);
        ExcelUtils.AbcExcelSheet classifySheet =
                hospitalRevenueChargeService.asyncClassifyExport(param);
        ExcelUtils.AbcExcelSheet detailSheet =
                hospitalRevenueChargeService.asyncDetailExport(param);
        sheets.add(transactionSheet);
        sheets.add(classifySheet);
        sheets.add(detailSheet);
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
