package cn.abc.flink.stat.service.cis.patient.order.referral.handler;

import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.db.cis.hologres.dao.HoloPatientOrderReferralMapper;
import cn.abc.flink.stat.db.dao.PatientOrderReferralMapper;
import cn.abc.flink.stat.service.cis.patient.order.referral.domain.PatientOrderReferralParam;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 转诊统计-数据处理器
 * @author: lzq
 * @Date: 2022/12/27
 */
@Service
public class PatientOrderReferralTotalHandler {

    @Resource
    private PatientOrderReferralMapper mapper;

    @Resource
    private HoloPatientOrderReferralMapper holoMapper;


    /**
     * @param param -
     * @return -
     */
    public StatResponseTotal selectDetailCount(PatientOrderReferralParam param) {
        StatResponseTotal total = new StatResponseTotal();
        Long count = holoMapper.selectReferralDetailTotal(TableUtils.getCisTable(), param);
        total.setCount(count);
        return total;
    }

    /**
     * @param param -
     * @return -
     */
    public StatResponseTotal selectDetailCavityCount(PatientOrderReferralParam param) {
        StatResponseTotal total = new StatResponseTotal();
        Long count = holoMapper.selectReferralDetailTotal(TableUtils.getCisTable(), param);
        total.setCount(count);
        return total;
    }
}
