package cn.abc.flink.stat.service.bis.export;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.controller.api.CustemrHandler;
import cn.abc.flink.stat.controller.api.FreezeAndFilter;
import com.alibaba.excel.EasyExcel;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class GoodsDetailExporter {

    private static String fileNamePrefix = "商品销售明细";

    private static Map<String, String> vendorTitles;
    private static Map<String, String> platformTitles;
    private static List<String> vendorHeads;
    private static List<String> platformHeads;

    static {
        platformTitles = new HashMap<>();
        platformTitles.put("sku_code", "商品编号");
        platformTitles.put("goods_type_id", "商品类型");
        platformTitles.put("name", "商品名称");
        platformTitles.put("vendor_name", "门店");
        platformTitles.put("organ_name", "供应商");
        platformTitles.put("sold_count", "成交数量");
        platformTitles.put("order_count", "成交订单数");
        platformTitles.put("patient_paid", "患者成交金额");
        platformTitles.put("clinic_paid", "诊所支付金额");
        platformTitles.put("total_price", "供应商结算金额");
        platformTitles.put("gross", "平台商品毛利");
        platformTitles.put("gross_rate", "平台商品毛利率");

        vendorTitles = new HashMap<>();
        vendorTitles.put("sku_code", "商品编号");
        vendorTitles.put("goods_type_id", "商品类型");
        vendorTitles.put("name", "商品名称");
        vendorTitles.put("vendor_name", "门店");
        vendorTitles.put("organ_name", "供应商");
        vendorTitles.put("sold_count", "成交数量");
        vendorTitles.put("order_count", "成交订单数");
        vendorTitles.put("patient_paid", "患者成交金额");
        vendorTitles.put("clinic_paid", "诊所支付金额");
        vendorTitles.put("total_price", "销售收入");
        vendorTitles.put("gross", "平台商品毛利");
        vendorTitles.put("gross_rate", "平台商品毛利率");

        vendorHeads = new ArrayList<>();
        vendorHeads.add("sku_code");
        vendorHeads.add("name");
        vendorHeads.add("goods_type_id");
        vendorHeads.add("sold_count");
        vendorHeads.add("order_count");
        vendorHeads.add("total_price");

        platformHeads = new ArrayList<>();
        platformHeads.add("organ_name");
        platformHeads.add("vendor_name");
        platformHeads.add("sku_code");
        platformHeads.add("name");
        platformHeads.add("goods_type_id");
        platformHeads.add("sold_count");
        platformHeads.add("order_count");
        platformHeads.add("patient_paid");
        platformHeads.add("clinic_paid");
        platformHeads.add("total_price");
        platformHeads.add("gross");
        platformHeads.add("gross_rate");
    }

    public static void export(HttpServletResponse response,
                              List<Map<String, Object>> data,
                              Boolean isVendor,
                              String beginDate,
                              String endDate) throws IOException {
        if (data == null || data.size() <= 0) {
            return;
        }

        String fileName = "export";
        try {
            fileName = URLEncoder.encode(fileNamePrefix + beginDate + "_" + endDate, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(ExcelUtils.buildHorizontalCellStyleStrategy())
                .registerWriteHandler(new FreezeAndFilter())
                .registerWriteHandler(new CustemrHandler())
                .head(buildHead(chooseHead(isVendor), chooseTitle(isVendor)))
                .sheet(0, fileNamePrefix)
                .doWrite(buildContent(data, chooseHead(isVendor)));
    }

    private static List<String> chooseHead(Boolean isVendor) {
        return isVendor ? vendorHeads : platformHeads;
    }

    private static Map<String, String> chooseTitle(Boolean isVendor) {
        return isVendor ? vendorTitles : platformTitles;
    }

    private static List<List<String>> buildHead(List<String> heads, Map<String, String> titles) {
        List<List<String>> finalHead = new ArrayList<>();
        for (String k : heads) {
            List<String> subHead = new ArrayList<>();
            subHead.add(titles.get(k));
            finalHead.add(subHead);
        }

        return finalHead;
    }

    private static List<List<Object>> buildContent(List<Map<String, Object>> data, List<String> heads) {
        List<List<Object>> finalContent = new ArrayList<>();
        for (Map<String, Object> o : data) {
            List<Object> line = new ArrayList<>();
            for (String k : heads) {
                if (k.equalsIgnoreCase("goods_type_id")) {
                    String v = String.valueOf(o.get(k));
                    if (v.equalsIgnoreCase("15")) {
                        line.add("中药颗粒");
                    } else if (v.equalsIgnoreCase("14")) {
                        line.add("中药饮片");
                    } else {
                        line.add("");
                    }
                } else {
                    line.add(o.get(k));
                }
            }
            if (line.size() > 0) {
                finalContent.add(line);
            }
        }

        return finalContent;
    }
}
