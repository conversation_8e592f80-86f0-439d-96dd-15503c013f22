package cn.abc.flink.stat.service.export;

import cn.abc.flink.stat.common.FileUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.db.cis.aurora.dao.AsyncExportMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.AsyncExportWriteMapper;
import cn.abc.flink.stat.export.entity.AsyncExportBase;
import cn.abc.flink.stat.export.entity.AsyncExportTask;
import cn.abc.flink.stat.mq.rabbit.send.RabbitMqSendAsyncExport;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisMonitorFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.monitor.ServiceAlertMessage;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.OSSObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.*;

@Service
public class AsyncExportService {
    private Logger logger = LoggerFactory.getLogger(AsyncExportService.class);

    @Autowired
    private AsyncExportMapper asyncExportMapping;

    @Autowired
    private AsyncExportWriteMapper writeMapper;

    @Resource
    private AbcCisMonitorFeignClient abcCisMonitorFeignClient;

    @Autowired
    private OSS ossClient;

    @Autowired
    private RabbitMqSendAsyncExport rabbitMqSendAsyncExport;

//    @Autowired
//    private AsyncExportThreadImpl asyncExportThread;

    @Value("${aliyun.bucket}")
    private String bucketName;

    @Value("${abc.async.export.env}")
    private String env;

    private String rootDir = "achievement";

    private static List<Integer> unallowInsertStatusList = new ArrayList<>();

    static {
        unallowInsertStatusList.add(0);
        unallowInsertStatusList.add(1);
        unallowInsertStatusList.add(2);
    }

    public AsyncExportBase getExportInfo(String chainId, String clinicId, String employId, String taskType) {
        AsyncExportBase aeb = asyncExportMapping.selectTaskInfo(TableUtils.getCisTable(), chainId, clinicId, employId, taskType);
        // 通过oss路径设置文件名字
        if (aeb != null) {
            String fileName = aeb.getName();
            if (fileName != null) {
                aeb.setName(FileUtils.getFileName(fileName));
            }
            //检查文件是否过期
            if (aeb.getStatus() == 2) {
                Boolean ossObject = ossClient.doesObjectExist(bucketName, fileName);
                if (!ossObject) {
                    aeb.setStatus(6);
                    //回写状态
                    writeMapper.updateTaksById(TableUtils.getCisTable(), aeb.getId(), aeb.getStatus(), null, null, null);
                }
            }
            if (aeb.getStatus() == 0) {
                int count = asyncExportMapping.selectTaskInfoCount(TableUtils.getCisTable(), null, 0, env, aeb.getCreated());
                if (count > 0) {
                    count = count - 1;
                }
                aeb.setQueuesNumber(count);
            }
            return aeb;
        }
        AsyncExportBase aebNull = new AsyncExportBase();
        aebNull.setStatus(-1);
        return aebNull;
    }

    public Map<String, String> urlDownload(String chainId, String clinicId, String employId, String taskType, String loginWay) {
        // 查新任务获取oss地址
        AsyncExportTask aet = asyncExportMapping.selectDownloadTask(TableUtils.getCisTable(), chainId, clinicId, employId, taskType);
        try {
            if (aet != null) {
                rabbitMqSendAsyncExport.sendOperatorLogMessage(aet, loginWay);
            }
        } catch (Exception e) {
            logger.error("sendOperatorLogMessage error", e);
        }
        URL url = null;
        if (aet != null) {
            // 返回数据流
            String fullPath = aet.getOss();
            // 设置签名URL过期时间，单位为毫秒。
            try {
                Date expiration = new Date(new Date().getTime() + 300 * 1000L);
                url = ossClient.generatePresignedUrl(bucketName, fullPath, expiration);
            } catch (OSSException e) {
                aet.setStatus(6);
                writeMapper.updateTaksById(TableUtils.getCisTable(), aet.getId(), aet.getStatus(), aet.getRetry(), aet.getOss(), aet.getHeartbeat());
                throw new RuntimeException("文件已失效或已被删除");
            }
            aet.setStatus(4);
            writeMapper.updateTaksById(TableUtils.getCisTable(), aet.getId(), aet.getStatus(), aet.getRetry(), aet.getOss(), aet.getHeartbeat());
        }
        Map<String, String> map = new HashMap<>();
        if (url != null) {
            map.put("url", String.valueOf(url).replace("http://", "https://"));
        }
        return map;
    }

    public void download(HttpServletResponse response, String chainId, String clinicId, String employId, String taskType, String loginWay) throws IOException {
        // 查新任务获取oss地址
        AsyncExportTask aet = asyncExportMapping.selectDownloadTask(TableUtils.getCisTable(), chainId, clinicId, employId, taskType);
        try {
            if (aet != null) {
                rabbitMqSendAsyncExport.sendOperatorLogMessage(aet, loginWay);
            }
        } catch (Exception e) {
            logger.error("sendOperatorLogMessage error", e);
        }
        if (aet != null) {
            // 返回数据流
            String fullPath = aet.getOss();
            OSSObject ossObject =null;
            try{
                ossObject=ossClient.getObject(bucketName, fullPath);
            }catch (OSSException e){
                //设置响应头和响应类型
                response.setContentType("application/json;charset=utf-8");
                response.setCharacterEncoding("utf-8");
                String message = e.getMessage();
                PrintWriter writer = response.getWriter();
                //json返回
                HashMap<String, Object> stringObjectHashMap = new HashMap<>();
                HashMap<String, Object> result = new HashMap<>();
                if(message.contains("NoSuchKey")){
                    //文件路径不存在
                    stringObjectHashMap.put("code","4001");
                    stringObjectHashMap.put("message","文件已失效或已被删除");
                }else{
                    //其他情况
                    stringObjectHashMap.put("code","4002");
                    stringObjectHashMap.put("message","其他情况");
                }
                result.put("status",stringObjectHashMap);
                writer.write(JSON.toJSONString(result));
                aet.setStatus(6);
                writeMapper.updateTaksById(TableUtils.getCisTable(), aet.getId(), aet.getStatus(), aet.getRetry(), aet.getOss(), aet.getHeartbeat());
                if(writer!=null){
                    writer.close();
                }
                if(ossObject!=null){
                    ossObject.close();
                }
                return;
            }
            String fileName = URLEncoder.encode(FileUtils.getFileName(aet.getOss()), "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            InputStream in = ossObject.getObjectContent();
            byte[] buffer = new byte[1024];
            int lenght;
            try {
                while ((lenght = in.read(buffer)) != -1) {
                    response.getOutputStream().write(buffer, 0, lenght);
                }
                aet.setStatus(4);
                writeMapper.updateTaksById(TableUtils.getCisTable(), aet.getId(), aet.getStatus(), aet.getRetry(), aet.getOss(), aet.getHeartbeat());
            } catch (IOException e) {
                logger.error(e.getMessage());
            } finally {
                if (in != null) {
                    in.close();
                }
            }
        }
    }

    public Map<String, Boolean> commitTask(String chainId, String clinicId, String employeeId, String taskType, Map params) {
        Map<String, Boolean> resultMap = new HashMap<String, Boolean>();
        // TODO 需要先校验是否临期门店，临期门店不让导出
        boolean disableExportForHeader = false;
        disableExportForHeader = HeaderTableKeyConfig.STAT_EYE_OUTPATIENT_LIST.equals(taskType) || HeaderTableKeyConfig.STAT_PATIENT_LIST.equals(taskType);
        if (disableExportForHeader) {
            logger.error("用户临期，不允许导出");
            resultMap.put("sucess", false);
            return resultMap;
        }
        AsyncExportBase base = asyncExportMapping.selectTaskInfo(TableUtils.getCisTable(), chainId, clinicId, employeeId, taskType);
        if (base != null && unallowInsertStatusList.contains(base.getStatus())) {
            resultMap.put("sucess", false);
            return resultMap;
        }
        int count = asyncExportMapping.selectTaskInfoCount(TableUtils.getCisTable(), clinicId, null, null, null);
        if (count > 100) {
            logger.warn("该门店今日导出超过一百次 clinicId: {}", clinicId);
            resultMap.put("sucess", false);
            return resultMap;
        }
        AsyncExportTask aet = new AsyncExportTask();
        aet.setChainId(chainId);
        aet.setClinicId(clinicId);
        aet.setEmployeeId(employeeId);
        aet.setEnv(env);
        aet.setStatus(0);
        aet.setTaskType(taskType);
        aet.setParams(MapUtils.map2String(params));
        try {
            writeMapper.insertTask(
                    TableUtils.getCisTable(),
                    aet.getChainId(),
                    aet.getClinicId(),
                    aet.getEmployeeId(),
                    aet.getEnv(),
                    aet.getTaskType(),
                    aet.getParams(),
                    aet.getStatus()
            );
            resultMap.put("sucess", true);
        } catch (Exception e) {
            resultMap.put("sucess", false);
            e.printStackTrace();
        }
//        asyncExportThread.notifyWorker();
        return resultMap;
    }

    public void sendMessage(String content) {
        ServiceAlertMessage msg = new ServiceAlertMessage();
        msg.setServiceName("sc-stat-async-export");
        msg.setTitle("异步导出异常任务");
        msg.setContent(content);
        msg.setCreated(Instant.now());
        abcCisMonitorFeignClient.sendServiceAlertMessage(msg);
    }
}
