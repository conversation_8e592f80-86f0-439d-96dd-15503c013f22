package cn.abc.flink.stat.service.cis.outpatient;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.SqlUtils;
import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.db.cis.aurora.dao.MysqlOutpatientMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresOutpatientMapper;
import cn.abc.flink.stat.db.dao.DimensionMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Department;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.domain.V2PatientExtend;
import cn.abc.flink.stat.dimension.domain.V2PatientSourceType;
import cn.abc.flink.stat.dimension.domain.V2PatientorderVisitSource;
import cn.abc.flink.stat.service.cis.handler.EmployeeSnapHandler;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientFirstRevisitRsp;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientIdDto;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListResp;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientParam;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientProductFormItem;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientSummaryDao;
import cn.abc.flink.stat.service.cis.outpatient.domain.PatientListInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.RevisitStatRsp;
import cn.abc.flink.stat.service.cis.outpatient.domain.VisitSourceDateInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.VisitSourceInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.VisitSourceRsp;
import cn.abc.flink.stat.service.cis.outpatient.handler.OutpatientExportHandler;
import cn.abc.flink.stat.service.cis.outpatient.handler.OutpatientHandler;
import cn.abc.flink.stat.service.customize.jh.operation.daily.JhParam;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/18 11:22 上午
 * @modified ljc
 */
@Service
public class OutpatientService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OutpatientService.class);

    @Resource
    HologresOutpatientMapper hologresOutpatientMapper;

    @Resource
    MysqlOutpatientMapper mysqlOutpatientMapper;

    @Resource
    DimensionQuery query;

    @Autowired
    OutpatientExportHandler exportHandler;

    @Autowired
    OutpatientHandler handler;

    @Autowired
    private StoreUtils storeUtils;

    @Resource
    private DimensionMapper dimensionMapper;

    @Resource
    private EmployeeSnapHandler employeeSnapHandler;

    private static final String ENV = TableUtils.getCisTable();

    private static final String DOCTOR_FILED = "outpatient_sheet_snap_name";
    private static final String DOCTOR_FILED_NAME = "outpatient_sheet_doctor_id";
    private static final String DOCTOR_SNAP_FILED_NAME = "doctorName";

    /**
     * 济华报表，初复诊
     *
     * @param param     -
     *                  revisitDimension 初复诊维度，chain维度传入（'1'或者'chain'）,clinic维度（'2'或者'clinic'）
     *                  ，dept维度('3'或者'department')，doctor维度('4'或者'doctor')
     *                  doctorIds        只计算docotorIds中的数据，传入的id字符串，逗号分隔
     *                  dateDimension    日期维度，传入 day or month，展示时是以日为维度还是月
     *                  chainId-clinicId-beginDate-endDate-pageindex-pagesize-departIds-
     * @param doctorIds -
     * @return -
     */
    public List<RevisitStatRsp> selectRevisitStat(JhParam param, String doctorIds) {
        LOGGER.info("selectRevisitStat:chainId:" + param.getChainId() + ",clinic:" + param.getClinicId()
                + ",beginDate:" + param.getBeginDate() + ",endDate:" + param.getEndDate() + ",revisitDimension:"
                + param.getRevisitDimension() + ",dateDimension:" + param.getDateDimension() + ",doctors:"
                + param.getDoctorIds() + ",departIds:" + param.getDepartIds());
        param.setRevisitCol(handler.getRevisitCol(param.getRevisitDimension()));
        param.setDoctorIdIn(SqlUtils.buildInSqlString(doctorIds));
        param.setDeptIds(SqlUtils.buildInSqlString(param.getDepartIds()));
        param.setDateSql(handler.getDateSql(param.getDateDimension(), storeUtils.getMapperName(param.getBeginDate(), param.getEndDate())));
        List<RevisitStatRsp> revisitStatRsps = storeUtils
                .getMapper(param.getBeginDate(), param.getEndDate(), mysqlOutpatientMapper, hologresOutpatientMapper)
                .selectRevisitStat(ENV, param);
        return revisitStatRsps;
    }

    /**
     * 济华报表：专家门诊初复诊
     * 专家门诊：除去中医理疗科（不统计本次门诊的科室为"中医理疗科"）
     */
    /**
     * @param
     * @param param     chainId -clinicId -beginDate -endDate -
     * @param pageindex -
     * @param pagesize  -
     * @param departId  -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.RevisitStatRsp>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 22:57
     */
    public List<RevisitStatRsp> selectExpertOutpatientRevisitStat(
            JhParam param, Integer pageindex, Integer pagesize, String departId) {
        List<RevisitStatRsp> revisitStatRsps = storeUtils
                .getMapper(param.getBeginDate(), param.getEndDate(), mysqlOutpatientMapper, hologresOutpatientMapper)
                .selectExpertOutpatientRevisitStat(ENV, param, pageindex, pagesize, departId);
        return revisitStatRsps;
    }


    /**
     * 济华报表，就诊来源
     *
     * @param param -
     *              revisitDimension 初复诊维度，chain维度传入（'1'或者'chain'）,clinic维度（'2'或者'clinic'），dept维度('3'或者'department')，
     *              doctor维度('4'或者'doctor')
     *              dateDimension    日期维度，传入 day or month，展示时是以日为维度还是月
     *              isRevisit        患者来源是否限制为初诊或者复诊，1 复诊，0 初诊，null 全部患者
     *              sourceIds        患者来源id，逗号分隔
     *              chainId        -
     *              clinicId        -
     *              beginDate        -
     *              endDate        -
     *              pageindex        -
     *              pagesize        -
     * @return -
     */
    public VisitSourceRsp selectVisitSource(JhParam param) {
        LOGGER.info("selectRevisitStat:chainId:" + param.getChainId() + ",clinic:" + param.getClinicId() + ",beginDate:"
                + param.getBeginDate() + ",endDate:" + param.getEndDate() + ",revisitDimension:"
                + param.getRevisitDimension() + ",dateDimension:" + param.getDateDimension() + ",isRevisit:"
                + param.getIsRevisit() + ",sourceIds:" + param.getSourceIds());
        //获取初复诊字段
        String revisitCol = handler.getRevisitCol(param.getRevisitDimension());
        String visitFilterSql = handler.getVisitFilterSql(revisitCol, param.getIsRevisit()); //初诊还是复诊
        String dateSql = handler.getDateSql(param.getDateDimension(), storeUtils.getMapperName(param.getBeginDate(), param.getEndDate())); //月维度还是日维度
        List<VisitSourceInfo> sources = storeUtils
                .getMapper(param.getBeginDate(), param.getEndDate(), mysqlOutpatientMapper, hologresOutpatientMapper)
                .selectVisitSourceStat(ENV, param, visitFilterSql, dateSql); //就诊来源数据
        List<RevisitStatRsp> revisitCount = selectRevisitStat(param, null); //初（复）诊人数
        Map<String, V2PatientorderVisitSource> typeMap = query.queryVisitSourceByChainId(param.getChainId());
        //保存每个日期下的就诊来源数据，key：日期，value：当日的就诊来源
        Map<String, VisitSourceDateInfo> dateInfoMap = new HashMap<>();
        HashSet<String> sourceNames = new HashSet<>(); //保存所有已有就诊来源
        HashSet<String> dates = new HashSet<>(); //保存所有已存在日期
        revisitCount.forEach(x -> {
            //这个对象中加一个未指定的数据
            VisitSourceDateInfo info = new VisitSourceDateInfo();
            //初诊总数
            info.setDataCount(x.getFirstVisitCount() == null ? 0 : x.getFirstVisitCount());
            //初诊未指定来源总数
            info.setDataNotSpecifiedCount(x.getNotSpecifiedVisitCount() == null ? 0 : x.getNotSpecifiedVisitCount());
            //时间
            info.setDate(x.getDate());
            dateInfoMap.put(x.getDate(), info);
            dates.add(x.getDate());
        });
        sources.forEach(x -> {
            //就诊来源维度拼接
            String name = typeMap.getOrDefault(x.getSourceId(), new V2PatientorderVisitSource()).getName();
            if (name != null) {
                x.setSourceName(name);
                sourceNames.add(name);
            }
            //获取当日对象
            VisitSourceDateInfo dateInfo = dateInfoMap.get(x.getDate());
            if (dateInfo != null) {
                List<VisitSourceInfo> sourceInfo = dateInfo.getSourceInfo();
                if (sourceInfo == null) {
                    List<VisitSourceInfo> infoList = new ArrayList<>();
                    dateInfo.setSourceInfo(infoList);
                }
                x.setSourceRatio(handler.getRatio(x.getSourceCount(), dateInfo.getDataCount()));
                dateInfo.getSourceInfo().add(x);
            }
        });
        ArrayList<String> dateList = new ArrayList<>(dates);
        ArrayList<String> sourceList = new ArrayList<>(sourceNames);
        dateList.sort(String::compareTo);
        sourceList.sort(String::compareTo);
        List<VisitSourceDateInfo> infoList = new ArrayList<>();
        dateList.forEach(x -> infoList.add(dateInfoMap.get(x)));
        return new VisitSourceRsp(sourceList, dateList, infoList);
    }

    /**
     * 患者清单
     */
    /**
     * @param
     * @param param          chainId-clinicId-beginDate-endDate-patientId-pageindex-pagesize-
     * @param employeeId     -
     * @param chainViewMode  -
     * @param clinicNodeType -
     * @return
     * @return cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListResp
     * @throws ExecutionException   -
     * @throws InterruptedException -
     * @Description: 运营分析-患者清单
     * @Author: zs
     * @Date: 2022/8/10 19:08
     */
    public OutpatientListResp selectPatientList(OutpatientParam param, String employeeId, Integer chainViewMode,
                                                Integer clinicNodeType)
            throws ExecutionException, InterruptedException {
        CompletableFuture<List<PatientListInfo>> patientFuture =
                CompletableFuture.supplyAsync(() -> storeUtils.getMapper(param.getBeginDate(), param.getEndDate(), mysqlOutpatientMapper, hologresOutpatientMapper)
                        .selectPatientList(ENV, param));
        CompletableFuture<Long> countFuture =
                CompletableFuture.supplyAsync(() -> storeUtils.getMapper(param.getBeginDate(), param.getEndDate(), mysqlOutpatientMapper, hologresOutpatientMapper)
                        .getPatientListCount(ENV, param));

        CompletableFuture.allOf(patientFuture, countFuture).join();
        List<PatientListInfo> patients = patientFuture.get();
        Long patientCount = countFuture.get();

        List<Map<String, Object>> data = handler.genPatientDataMap(patients, param.getChainId());
        OutpatientListResp res = new OutpatientListResp(patientCount, data);
        List<TableHeaderEmployeeItem> header = handler.getPatientHeader(employeeId,
                HeaderTableKeyConfig.STAT_PATIENT_LIST, chainViewMode,
                HeaderTableKeyConfig.EXCLUDE_HIDDEN_1, clinicNodeType);
        res.setHeader(header);
        return res;
    }

    /**
     * 患者清单导出
     */
    /**
     * @param
     * @param response -
     * @param param    chainId -clinicId -beginDate -endDate -patientId -
     * @return
     * @throws ExecutionException   -
     * @throws InterruptedException -
     * @throws IOException          -
     * @Description:
     * @Author: zs
     * @Date: 2022/8/10 19:28
     */
    public void exportPatientList(HttpServletResponse response, OutpatientParam param)
            throws ExecutionException, InterruptedException, IOException {
        OutpatientListResp data = selectPatientList(param, null, null, null);
        List header = data.getHeader();

        // 特殊处理
        for (Map<String, Object> map : data.getList()) {
            handler.replaceAgeAndName(map, "age", "name", "isMember");
            handler.replaceFee(map, "fee");
        }
        exportHandler.exportOutpatient(response, "患者清单", data.getList(),
                header, param);
    }


    /**
     * @param
     * @param param             chainId -clinicId -beginDate -endDate -patientId -employeeId -type -pageindex -pagesize -
     *                          type 是否网诊 0 否，1是， undefined 所有
     * @param hisType           -
     * @param chainViewMode     -
     * @param clinicNodeType    -
     * @param currentEmployeeId -
     * @return
     * @return cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListResp
     * @throws ExecutionException        -
     * @throws InterruptedException      -
     * @throws IllegalAccessException    -
     * @throws NoSuchMethodException     -
     * @throws InvocationTargetException -
     * @Description:门诊日志
     * @Author: zs
     * @Date: 2022/8/11 09:36
     */
    public OutpatientListResp selectOutpatientList(OutpatientParam param, String hisType, Integer chainViewMode,
                                                   Integer clinicNodeType, String currentEmployeeId)
            throws Exception {
        if (param != null && param.getFilterDoctorParams() != null) {
            param.setSearchDoctorSql(employeeSnapHandler.initEmployeeSnapNameSql(DOCTOR_FILED, DOCTOR_FILED_NAME, DOCTOR_SNAP_FILED_NAME, true, param.getFilterDoctorParams(), !storeUtils.isUseKudu(param.getBeginDate(), param.getEndDate())));
        }
        Integer isOnline = param.getType() != null && param.getType().matches("^[0-9]$")
                ? Integer.parseInt(param.getType()) : null;
        List<OutpatientListInfo> list = getOutpatientListData(param, isOnline); //事实数据
        List<TableHeaderEmployeeItem> header =
                getTableHeaderEmployeeItems(hisType, chainViewMode, clinicNodeType, currentEmployeeId);
        if (list == null || list.isEmpty()) {
            return new OutpatientListResp(0L, null, header);
        }
        List<String> patientIds = handler.getIds("patient", list);
        List<String> outpatientSheetIds = handler.getIds("outpatientSheet", list);
        List<String> departmentIds = handler.getIds("department", list);
        CompletableFuture<Map<String, Organ>> organF =
                CompletableFuture.supplyAsync(() -> query.queryOrganByParentId(param.getChainId())); //门店维度
        CompletableFuture<Map<String, Employee>> empF =
                CompletableFuture.supplyAsync(() -> query.queryEmployeeByChainId(param.getChainId())); //员工维度
        CompletableFuture<Map<String, V2Patient>> patientF =
                CompletableFuture.supplyAsync(() -> query.queryPatient(param.getChainId(), patientIds, param.getEnablePatientMobile())); //患者维度
        CompletableFuture<Map<String, V2PatientExtend>> patientExtendF =
                CompletableFuture.supplyAsync(() -> query.queryPatientExtend(param.getChainId(), patientIds)); //患者维度
        CompletableFuture<Map<String, Department>> departmentFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryDepartments(param.getChainId(), departmentIds);
        });
        CompletableFuture<Long> countF =
                CompletableFuture.supplyAsync(() -> storeUtils.getMapper(param.getBeginDate(), param.getEndDate(),
                        mysqlOutpatientMapper, hologresOutpatientMapper).getOutpatientListCount(ENV, param, isOnline)); //数据条数
        //患者来源
        CompletableFuture<Map<String, V2PatientSourceType>> sourceF =
                CompletableFuture.supplyAsync(() -> query.queryPatientSourceType(param.getChainId())); //患者来源
        //诊疗项目
        CompletableFuture<Map<String, List<OutpatientProductFormItem>>> outpatientProductF = CompletableFuture.
                supplyAsync(() -> outpatientProductItem(outpatientSheetIds, param, hisType));
        CompletableFuture.allOf(organF, empF, patientF, sourceF, outpatientProductF, patientExtendF);
        Map<String, Organ> organs = organF.get();
        Map<String, Employee> emps = empF.get();
        Map<String, V2Patient> patients = patientF.get();
        Map<String, V2PatientSourceType> sources = sourceF.get();
        Map<String, List<OutpatientProductFormItem>> outpatientProductMap = outpatientProductF.get();
        Map<String, V2PatientExtend> v2PatientExtendMap = patientExtendF.get();
        Map<String, Department> department = departmentFuture.get();
        //数据组装
        List<Map<String, Object>> data = handler.
                genOutpatientDataMap(list, organs, emps, patients, sources, outpatientProductMap, hisType, v2PatientExtendMap, department);
        CompletableFuture.allOf(countF);
        Long count = countF.get();
        OutpatientListResp rsp = new OutpatientListResp(count, data);
        rsp.setHeader(header);
        return rsp;
    }

    public List<OutpatientListInfo> getOutpatientListData (OutpatientParam param, Integer isOnline) throws ParseException {
        //判断是否为导出 1.导出就按原来的逻辑 2.非导出就先查询id再根据id反查其余数据，避免内存消耗过大
        if (param.getIsExport() == 0 && !(TimeUtils.isToday(param.getBeginDate()) && TimeUtils.isToday(param.getEndDate()))) {
            //查询ID
            List<OutpatientIdDto> idList = storeUtils.getMapper(param.getBeginDate(), param.getEndDate(), mysqlOutpatientMapper, hologresOutpatientMapper).selectOutpatientIds(ENV, param, isOnline);
            if (idList != null && !idList.isEmpty()) {
                List<String> ids = new ArrayList<>();
                String maxCreated = null;
                String minCreated = null;
                for (OutpatientIdDto dto : idList) {
                    ids.add(dto.getId());
                    String created = dto.getCreated();
                    if (created != null) {
                        if (maxCreated == null || created.compareTo(maxCreated) > 0) {
                            maxCreated = created;
                        }
                        if (minCreated == null || created.compareTo(minCreated) < 0) {
                            minCreated = created;
                        }
                    }
                }
                return storeUtils.getMapper(minCreated, maxCreated, mysqlOutpatientMapper, hologresOutpatientMapper).selectOutpatientListByIds(ENV, ids, minCreated, TimeUtils.appendEnd(maxCreated));
            } else {
                return null;
            }
        } else {
            return storeUtils.getMapper(param.getBeginDate(), param.getEndDate(), mysqlOutpatientMapper, hologresOutpatientMapper).selectOutpatientList(ENV, param, isOnline);
        }
    }
    /**
     * @param
     * @param hisType           -
     * @param chainViewMode     -
     * @param clinicNodeType    -
     * @param currentEmployeeId -
     * @return
     * @return java.util.List<cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/15 09:32
     */
    private List<TableHeaderEmployeeItem> getTableHeaderEmployeeItems(String hisType,
                                                                      Integer chainViewMode, Integer clinicNodeType,
                                                                      String currentEmployeeId) {
        String tableKey = HeaderTableKeyConfig.STAT_OUTPATIENT_LIST;
        //口腔管家表头
        if ("1".equals(hisType)) {
            tableKey = HeaderTableKeyConfig.STAT_ORAL_OUTPATIENT_LIST;
            //眼科管家新增表头
        } else if ("2".equals(hisType)) {
            tableKey = HeaderTableKeyConfig.STAT_EYE_OUTPATIENT_LIST;
        } else if ("100".equals(hisType)) {
            tableKey = HeaderTableKeyConfig.STAT_HOSPITAL_OUTPATIENT_LIST;
        }
        List<TableHeaderEmployeeItem> header = handler.getOutPatientHeader(currentEmployeeId, tableKey, chainViewMode,
                HeaderTableKeyConfig.EXCLUDE_HIDDEN_1, clinicNodeType);
        return header;
    }


    /**
     * @param
     * @param response -
     * @param param    chainId -clinicId -beginDate -endDate -patientId -employeeId -type -
     * @param hisType  -
     * @return
     * @throws InterruptedException      -
     * @throws ExecutionException        -
     * @throws InvocationTargetException -
     * @throws NoSuchMethodException     -
     * @throws IllegalAccessException    -
     * @throws IOException               -
     * @Description: 门诊清单导出-不知道什么地方在调用页面现在用的异步导出
     * @Author: zs
     * @Date: 2022/8/11 18:56
     */
    public void exportOutpatientList(HttpServletResponse response, OutpatientParam param, String hisType)
            throws Exception {
        OutpatientListResp data =
                selectOutpatientList(param, hisType, null, null, null);
        List header = data.getHeader();
        //特殊处理
        for (Map<String, Object> map : data.getList()) {
            handler.replaceAgeAndName(map, "patientAge", "patientName", "isMember");
            handler.replaceFee(map, "fee");
            handler.replaceOutpatientListOther(map);
        }
        exportHandler.exportOutpatient(response, "门诊清单", data.getList(), header, param);
    }

    /**
     * @param
     * @param params     -
     * @param beginDate  -
     * @param endDate    -
     * @param employeeId -
     * @return
     * @return cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientSummaryDao
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 19:07
     */
    public OutpatientSummaryDao selectOutpatientSummary(AbcCisBaseQueryParams params, String beginDate,
                                                        String endDate, String employeeId) {
        return hologresOutpatientMapper.selectOutpatientSummary(TableUtils.getCisTable(), params.getChainId(),
                        params.getClinicId(), beginDate, endDate, employeeId);
    }

    /**
     * @param
     * @param param             chainId -clinicId -beginDate -endDate -patientId -employeeId -type -
     * @param hisType           -
     * @param currentEmployeeId -
     * @param chainViewMode     -
     * @param clinicNodeType    -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.ExcelUtils.AbcExcelSheet>
     * @throws ExecutionException        -
     * @throws InterruptedException      -
     * @throws IllegalAccessException    -
     * @throws NoSuchMethodException     -
     * @throws InvocationTargetException -
     * @Description: 门诊日志-异步导出
     * @Author: zs
     * @Date: 2022/8/11 19:23
     */
    public List<ExcelUtils.AbcExcelSheet> export(OutpatientParam param, String hisType, String currentEmployeeId,
                                                 Integer chainViewMode, Integer clinicNodeType) throws Exception {
        OutpatientListResp data =
                selectOutpatientList(param, hisType, chainViewMode, clinicNodeType, currentEmployeeId);
        for (Map<String, Object> map : data.getList()) {
            handler.replaceAgeAndName(map, "patientAge", "patientName", "isMember");
            handler.replaceFee(map, "fee");
            handler.replaceOutpatientListOther(map);
            handler.replaceObstetricalHistory(map);
            handler.replaceOutpatientProductItem(map);
        }
        LOGGER.info("outpatientData:" + data.getList().size());
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();

        ExcelUtils.AbcExcelSheet outpatientSheet = new ExcelUtils.AbcExcelSheet();
        outpatientSheet.setName("门诊日志");
        List<String> prop = new ArrayList<>();
        outpatientSheet.setSheetDefinition(exportHandler.makeExcelHead(data.getHeader(), prop));
        outpatientSheet.setData(exportHandler.makeExcelBody(data.getList(), prop));
        sheets.add(outpatientSheet);
        return sheets;
    }

    /**
     * @param
     * @param param             chainId -clinicId -beginDate -endDate -patientId -
     * @param currentEmployeeId -
     * @param chainViewMode     -
     * @param clinicNodeType    -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.ExcelUtils.AbcExcelSheet>
     * @throws ExecutionException   -
     * @throws InterruptedException -
     * @Description: 患者清单-异步导出
     * @Author: zs
     * @Date: 2022/8/11 19:34
     */
    public List<ExcelUtils.AbcExcelSheet> exportAsynPatientList(OutpatientParam param, String currentEmployeeId,
                                                                Integer chainViewMode, Integer clinicNodeType)
            throws ExecutionException, InterruptedException {
        OutpatientListResp data = selectPatientList(param, currentEmployeeId, chainViewMode, clinicNodeType);
        List header = data.getHeader();
        // 特殊处理
        for (Map<String, Object> map : data.getList()) {
            handler.replaceAgeAndName(map, "age", "name", "isMember");
            handler.replaceFee(map, "fee");
        }
        LOGGER.info("data" + data.getList().size());
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();

        ExcelUtils.AbcExcelSheet patientList = new ExcelUtils.AbcExcelSheet();
        patientList.setName("患者清单");
        List<String> prop = new ArrayList<>();
        patientList.setSheetDefinition(exportHandler.makeExcelHead(header, prop));
        patientList.setData(exportHandler.makeExcelBody(data.getList(), prop));
        sheets.add(patientList);
        return sheets;
    }

    /**
     * @param
     * @param chainId    -
     * @param clinicId   -
     * @param beginDate  -
     * @param endDate    -
     * @param employeeId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientFirstRevisitRsp>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 19:11
     */
    public List<OutpatientFirstRevisitRsp> getOutpatientFirstRevisitNum(String chainId, String clinicId,
                                                                        String beginDate, String endDate,
                                                                        String employeeId) {
        if (beginDate != null) {
            beginDate = TimeUtils.appendBegin(beginDate);
        }
        if (endDate != null) {
            endDate = TimeUtils.appendEnd(endDate);
        }
        List<OutpatientFirstRevisitRsp> rsp = storeUtils.getMapper(beginDate, endDate, mysqlOutpatientMapper, hologresOutpatientMapper).
                getOutpatientFirstRevisitNum(TableUtils.getCisTable(), chainId,
                        clinicId, beginDate, endDate, employeeId);

        return rsp;

    }

    /**
     * @param
     * @param chainId    -
     * @param clinicId   -
     * @param beginDate  -
     * @param endDate    -
     * @param employeeId -
     * @return
     * @return java.util.List<java.lang.String>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 19:19
     */
    public List<String> selectPrescriptionCountByDoctorId(String chainId, String clinicId, String beginDate,
                                                          String endDate, String employeeId) {
        if (beginDate != null && beginDate.length() < 11) {
            beginDate = TimeUtils.appendBegin(beginDate);
        }
        if (endDate != null && endDate.length() < 11) {
            endDate = TimeUtils.appendEnd(endDate);
        }
        return hologresOutpatientMapper.selectPrescriptionCountByDoctorId(TableUtils.getCisTable(), chainId, clinicId,
                        beginDate, endDate, employeeId);
    }

    /**
     * @param outpatientSheetIds -
     * @param param            param
     * @param hisType           门店类型
     * @return -
     * 诊疗项目bug替换成statadb查询
     */
    public Map<String, List<OutpatientProductFormItem>> outpatientProductItem(List<String> outpatientSheetIds,
                                                                              OutpatientParam param,
                                                                              String hisType) {

        Map<String, List<OutpatientProductFormItem>> res1 = new HashMap<>();
        List<Map<String, Object>> res = new ArrayList<>();
        if (outpatientSheetIds.size() > 0) {
            try {
                // 一次最多查询1000条
                if (outpatientSheetIds.size() > cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_TEN_THOUSAND) {
                    List<List<String>> lists = handler.splitList(outpatientSheetIds,
                            cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_TEN_THOUSAND);
                    //超过一万之后就循环查询
                    for (List<String> list : lists) {
                        List<Map<String, Object>> outpatientProductFormItem = new ArrayList<>();
                        if (storeUtils.isUseKudu(param.getBeginDate(), param.getEndDate())) {
                            outpatientProductFormItem = hologresOutpatientMapper
                                    .outpatientProductFormItem(TableUtils.getCisTable(), list, param.getChainId(), param.getClinicId(), hisType);
                        } else {
                            outpatientProductFormItem =
                                    dimensionMapper.selectOutpatientProductFormItem(TableUtils.getCisOutPatientTable(), TableUtils.getCisOutPatientRecordTable(), list, param.getChainId(), param.getClinicId(), hisType);
                        }
                        if (outpatientProductFormItem != null && outpatientProductFormItem.size() > 0) {
                            res.addAll(outpatientProductFormItem);
                        }
                    }

                } else {
                    if (storeUtils.isUseKudu(param.getBeginDate(), param.getEndDate())) {
                        res = hologresOutpatientMapper.outpatientProductFormItem(TableUtils.getCisTable(),
                                outpatientSheetIds, param.getChainId(), param.getClinicId(), hisType);
                    } else {
                        res = dimensionMapper.selectOutpatientProductFormItem(TableUtils.getCisOutPatientTable(), TableUtils.getCisOutPatientRecordTable(),
                                outpatientSheetIds, param.getChainId(), param.getClinicId(), hisType);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("门诊日志-诊疗项目出现异常：", e);
            }
        }
        if (res != null && res.size() > 0) {
            for (Map<String, Object> re : res) {
                Object obj = re.get("outpatientProductFormItem");

                List<OutpatientProductFormItem> list = new ArrayList<OutpatientProductFormItem>();
                if (obj instanceof ArrayList<?>) {
                    for (Object o : (List<?>) obj) {
                        list.add(OutpatientProductFormItem.class.cast(o));
                    }
                }
                res1.put(re.get("outpatient_sheet_id").toString(), list);


            }
        }

        return res1;

    }

    /**
     * 获取今日工作台数据(处方量)
     * @param cisTable -
     * @param param -
     * @return -
     */
    public Integer getPrescriptionCount(String cisTable, OutpatientParam param) {
        return storeUtils.getMapper(param.getBeginDate(), param.getEndDate(),
                mysqlOutpatientMapper, hologresOutpatientMapper).getPrescriptionCount(cisTable, param);
    }
}
