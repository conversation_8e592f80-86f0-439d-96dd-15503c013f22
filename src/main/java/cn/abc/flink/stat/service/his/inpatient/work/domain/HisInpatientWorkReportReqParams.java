package cn.abc.flink.stat.service.his.inpatient.work.domain;

import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.ToString;

/**
 * @description: 医院统计-医嘱统计-请求param
 * @author: lzq
 * @Date: 2023/12/2 14:42
 */
@ToString
@ApiModel("医院统计-医嘱统计-请求param")
public class HisInpatientWorkReportReqParams extends AbcScStatRequestParams {
    @ApiModelProperty(value = "时间标记：0：入院日期，1：出院日期")
    private Integer signal;

    @ApiModelProperty(value = "门店id")
    private String clinicId;

    @ApiModelProperty(value = "科室id")
    private String departmentId;

    @ApiModelProperty(value = "病区id")
    private String wardId;

    @ApiModelProperty(value = "医生id")
    private String doctorId;

    @ApiModelProperty(value = "入院日期")
    private String hospitalAdmissionDate;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "分页起始", required = true)
    private Integer limit;

    @ApiModelProperty(value = "分页偏移量", required = true)
    private Integer offset;

    public Integer getSignal() {
        return this.signal;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getDepartmentId() {
        return this.departmentId;
    }

    public String getWardId() {
        return this.wardId;
    }

    public String getDoctorId() {
        return this.doctorId;
    }

    public String getHospitalAdmissionDate() {
        return this.hospitalAdmissionDate;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public Integer getLimit() {
        return this.limit;
    }

    public Integer getOffset() {
        return this.offset;
    }


    public void setSignal(Integer signal) {
        this.signal = signal;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public void setWardId(String wardId) {
        this.wardId = wardId;
    }

    public void setDoctorId(String doctorId) {
        this.doctorId = doctorId;
    }

    public void setHospitalAdmissionDate(String hospitalAdmissionDate) {
        this.hospitalAdmissionDate = hospitalAdmissionDate;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

}
