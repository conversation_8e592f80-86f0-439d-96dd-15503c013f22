package cn.abc.flink.stat.service.customize.revenue.pojo;

import cn.abc.flink.stat.common.TableUtils;


/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/27 16:15
 */
public class RevenueAmountByChargeSheetIdsParamDto {
	private String env = TableUtils.getCisTable();

	private String chainId;

	private String clinicId;

	private String beginDate;

	private String endDate;

	private String chargeSheetIdsSql;

	public RevenueAmountByChargeSheetIdsParamDto(String chainId, String clinicId, String beginDate, String endDate, String chargeSheetIdsSql) {
		this.chainId = chainId;
		this.clinicId = clinicId;
		this.beginDate = beginDate;
		this.endDate = endDate;
		this.chargeSheetIdsSql = chargeSheetIdsSql;
	}

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public String getChargeSheetIdsSql() {
        return this.chargeSheetIdsSql;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setChargeSheetIdsSql(String chargeSheetIdsSql) {
        this.chargeSheetIdsSql = chargeSheetIdsSql;
    }

}
