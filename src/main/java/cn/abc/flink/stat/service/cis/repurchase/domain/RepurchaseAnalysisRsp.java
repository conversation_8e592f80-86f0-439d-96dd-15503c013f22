package cn.abc.flink.stat.service.cis.repurchase.domain;
import java.util.List;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-01-24 14:52
 **/
public class RepurchaseAnalysisRsp {
    private List<RepurchaseAnalysisHeader> header;
    private List<RepurchaseDoctor> data;
    private RepurchaseAnalysisTotal total;

    public List<RepurchaseAnalysisHeader> getHeader() {
        return header;
    }
    public void setHeader(List<RepurchaseAnalysisHeader> header) {
        this.header = header;
    }
    public List<RepurchaseDoctor> getData() {
        return data;
    }
    public void setData(List<RepurchaseDoctor> data) {
        this.data = data;
    }
    public RepurchaseAnalysisTotal getTotal() {
        return total;
    }
    public void setTotal(RepurchaseAnalysisTotal total) {
        this.total = total;
    }
}
