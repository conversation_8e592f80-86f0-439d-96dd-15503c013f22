package cn.abc.flink.stat.service.cis.revenue.overview.pojo;

import cn.abc.flink.stat.service.cis.revenue.overview.entity.RepaymentOverviewDao;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class RevenueGadgetOverviewResp {
	/**
	 * 总单数，只是记录了发生收退费的单子
	 */
    private Integer sheetCount;

    /**
     * amount - returnAmount
     */
    private BigDecimal totalAmount = BigDecimal.ZERO;

	/**
	 * 收费
	 */
	private BigDecimal amount = BigDecimal.ZERO;

	/**
	 * 退费金额
	 */
	private BigDecimal returnAmount = BigDecimal.ZERO;

	/**
	 * 会员充值
	 */
	private BigDecimal memberAmount;

	/**
	 * 卡项充值
	 */
	private BigDecimal promotionCardAmount;

	/**
	 * 充值收费
	 */
	private BigDecimal rechargeAmount = BigDecimal.ZERO;

	/**
	 * 欠费
	 */
	private BigDecimal oweAmount = BigDecimal.ZERO;


	/**
	 * 还款收费
	 */
	private BigDecimal repaymentAmount = BigDecimal.ZERO;

	/**
	 * 设置对象值
	 */
	public void set() {
		if (this.memberAmount != null) {
			this.rechargeAmount = this.rechargeAmount.add(this.memberAmount);
		}
		if (this.promotionCardAmount != null) {
			this.rechargeAmount = this.rechargeAmount.add(this.promotionCardAmount);
		}
	}

	/**
	 * 设置还款金额
	 * @param dao RepaymentOverviewDao
	 */
    public void setRepaymentAmount(RepaymentOverviewDao dao) {
        if (dao != null && dao.getAmount() != null) {
            this.repaymentAmount = dao.getAmount();
        }
    }

    public void initTotalAmount() {
        this.totalAmount = this.amount.subtract(this.returnAmount);
    }

    public Integer getSheetCount() {
        return sheetCount;
    }

    public void setSheetCount(Integer sheetCount) {
        this.sheetCount = sheetCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    public BigDecimal getMemberAmount() {
        return memberAmount;
    }

    public void setMemberAmount(BigDecimal memberAmount) {
        this.memberAmount = memberAmount;
    }

    public BigDecimal getPromotionCardAmount() {
        return promotionCardAmount;
    }

    public void setPromotionCardAmount(BigDecimal promotionCardAmount) {
        this.promotionCardAmount = promotionCardAmount;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public BigDecimal getOweAmount() {
        return oweAmount;
    }

    public void setOweAmount(BigDecimal oweAmount) {
        this.oweAmount = oweAmount;
    }

    public BigDecimal getRepaymentAmount() {
        return repaymentAmount;
    }

    public void setRepaymentAmount(BigDecimal repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }
}
