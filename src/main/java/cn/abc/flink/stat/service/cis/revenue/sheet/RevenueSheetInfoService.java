package cn.abc.flink.stat.service.cis.revenue.sheet;

import cn.abc.flink.stat.common.BeanUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.common.utils.MathUtil;
import cn.abc.flink.stat.common.utils.TableHeaderUtils;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresRevenueMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2ClinicChainEmployeeSnap;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.dimension.domain.V2OutpatientMedicalRecord;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.domain.V2PatientClinic;
import cn.abc.flink.stat.dimension.domain.V2PatientSourceType;
import cn.abc.flink.stat.service.cis.revenue.sheet.domain.RevenueSheetInfoDAO;
import cn.abc.flink.stat.service.cis.revenue.sheet.domain.RevenueSheetInfoParams;
import cn.abc.flink.stat.service.cis.revenue.sheet.domain.RevenueSheetInfoTotal;
import cn.abc.flink.stat.source.AbcThreadExecutor;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/8 15:16
 */
@Service
public class RevenueSheetInfoService {

	@Resource
	private DimensionQuery query;

	@Resource
	private HologresRevenueMapper hologresRevenueMapper;

	@Resource
	AbcThreadExecutor executor;

	@Value("${customized.patient-therapist-chains}")
	private List<String> patientTherapistChains;

	public V2StatResponse selectSheetInfo(RevenueSheetInfoParams params) throws ExecutionException, InterruptedException {
		params.init();
		CompletableFuture<List<RevenueSheetInfoDAO>> listF = executor.supplyAsync(() -> hologresRevenueMapper.getSheetInfoList(TableUtils.getCisTable(), params));
		CompletableFuture<RevenueSheetInfoTotal> totalF = executor.supplyAsync(() -> hologresRevenueMapper.getSheetInfoTotal(TableUtils.getCisTable(), params));

		listF.join();

		List<RevenueSheetInfoDAO> list = listF.get();
		Set<String> patientIds = new HashSet<>();
		Set<String> patientorderIds = new HashSet<>();
		Set<Long> employeeSnapIds = new HashSet<>();
		for (RevenueSheetInfoDAO info : list) {
			patientIds.add(info.getPatientId());
			patientorderIds.add(info.getPatientOrderId());
			employeeSnapIds.add(info.getDoctorSnapId());
		}

		CompletableFuture<Map<String, Organ>> organF = executor.supplyAsync(() -> query.queryOrganByParentId(params.getChainId()));
		CompletableFuture<Map<String, V2Patient>> patientMapF = executor.supplyAsync(() -> query.queryPatient(params.getChainId(), patientIds, params.getEnablePatientMobile()));
		CompletableFuture<Map<String, List<String>>> patientTagMapF = executor.supplyAsync(() -> query.selectPatientTagByIds(params.getChainId(), params.getIsExport() == 1 ? null:patientIds));
		CompletableFuture<Map<String, String>> departmentF = executor.supplyAsync(() -> query.queryDepartmentNameByOrgan(params.getChainId(), params.getClinicId()));
		CompletableFuture<Map<String, V2OutpatientMedicalRecord>> outpatientMedicalRecordMapF = executor.supplyAsync(() -> query.batchQueryOutpatientMedicalRecord(patientorderIds));
		CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() -> query.queryEmployeeByChainId(params.getChainId()));
		CompletableFuture<Map<Long, V2ClinicChainEmployeeSnap>> employeeSnapF = executor.supplyAsync(() -> query.queryEmployeeSnaps(employeeSnapIds));
		CompletableFuture<Map<String, V2PatientSourceType>> sourceTypeF = executor.supplyAsync(() -> query.queryPatientSourceType(params.getChainId()));
		CompletableFuture<Map<Integer, String>> payModeF = executor.supplyAsync(() -> query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId()));
		CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> query.selectAdviceFeeType(params.getChainId()));
		CompletableFuture<List<TableHeaderEmployeeItem>> headerF = executor.supplyAsync(() -> query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.STAT_COMMON_CHARGE_FEE_QUERY, params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0, Integer.valueOf(params.getHisType())));
		CompletableFuture<Map<String, V2PatientClinic>> patietnClinicF = null;
		Map<String, V2PatientClinic> patientClinicMap = null;
		if (patientTherapistChains.contains(params.getChainId())) {
			patietnClinicF =  executor.supplyAsync(() -> query.selectPatientClinicByPatientIds(params.getChainId(), params.getClinicId(), new ArrayList<>(patientIds)));
			CompletableFuture.allOf(totalF, patientMapF, patietnClinicF, patientTagMapF, payModeF, feeTypeF, departmentF, organF, employeeF, employeeSnapF, sourceTypeF, headerF, outpatientMedicalRecordMapF).join();
			patientClinicMap = patietnClinicF.get();
		} else {
			CompletableFuture.allOf(totalF, patientMapF, patientTagMapF, payModeF, feeTypeF, departmentF, organF, employeeF, employeeSnapF, sourceTypeF, headerF, outpatientMedicalRecordMapF).join();
		}

		Map<String, Organ> organMap = organF.get();
		Map<String, V2Patient> patientMap = patientMapF.get();
		Map<String, List<String>> patientTagMap = patientTagMapF.get();
		Map<String, String> departmentMap = departmentF.get();
		Map<String, Employee> employeeMap = employeeF.get();
		Map<Long, V2ClinicChainEmployeeSnap> employeeSnapMap = employeeSnapF.get();
		Map<String, V2PatientSourceType> sourceTypeMap = sourceTypeF.get();
		Map<String, V2OutpatientMedicalRecord> outpatientMedicalRecordMap = outpatientMedicalRecordMapF.get();
		List<TableHeaderEmployeeItem> headers = headerF.get();

		Set<String> paySet = new HashSet<>();
		Set<String> feeTypeIds = new HashSet<>();
		List<Map<String, Object>> data = new ArrayList<>();
		for (RevenueSheetInfoDAO info : list) {
			info.setClinicName(organMap);
			if (info.getSourceType() != null && info.getSourceType() == 1) {
				info.goodsAmend();
				Map<String, Object> map = BeanUtils.toMap(RevenueSheetInfoDAO.class, info);
				map.put("patientTags", CommonConstants.WHIPPTREE);
				map.put("gross", info.getCostPrice().negate().setScale(2, RoundingMode.UP));
				map.put("profitRate", "-");
				map.putAll(info.parseFeeTypeJson(feeTypeIds, params.getHisType()));
				data.add(map);
			} else {
				info.setSource(query.queryChargeSourceTypeText(info.getType(), info.getRetailType()));
				info.initOutpatient(outpatientMedicalRecordMap.get(info.getPatientOrderId()));
				info.pretty(patientMap, employeeMap, departmentMap, sourceTypeMap, employeeSnapMap);
				Map<String, Object> map = BeanUtils.toMap(RevenueSheetInfoDAO.class, info);
				List<String> tags = patientTagMap.get(info.getPatientId());
				if (tags != null) {
					map.put("patientTags", String.join(",", tags));
				}
				// 治疗师
				if (patientClinicMap != null) {
					String dutyTherapistName = "-";
					String primaryTherapistName = "-";
					V2PatientClinic v2PatientClinic = patientClinicMap.get(info.getPatientId());
					if (v2PatientClinic != null) {
						Employee dutyMap = employeeMap.get(v2PatientClinic.getDutyTherapistId());
						Employee primaryMap = employeeMap.get(v2PatientClinic.getPrimaryTherapistId());
						if (dutyMap != null) {
							dutyTherapistName = dutyMap.getName();
						}
						if (primaryMap != null) {
							primaryTherapistName = primaryMap.getName();
						}
					}
					map.put("dutyTherapistName", dutyTherapistName);
					map.put("primaryTherapistName", primaryTherapistName);
				}

				BigDecimal gross = MathUtil.subtract(info.getReceivedPrice(), info.getCostPrice());
				map.put("gross", gross);
				map.put("profitRate", MathUtil.calculateRatio(gross, info.getReceivedPrice(), 4, new BigDecimal(100)) + "%");
				Map<String, Object> payMap = info.getPayModeMap();
				if (payMap != null) {
					paySet.addAll(payMap.keySet());
					for (Map.Entry<String, Object> entry : payMap.entrySet()) {
						map.put("payMode_" + entry.getKey(), entry.getValue());
					}
				}
				map.putAll(info.parseFeeTypeJson(feeTypeIds, params.getHisType()));
				data.add(map);
			}
		}
		handleRevenueSheetInfoList(params.getChainId(), params.getHisType(), headers, paySet, feeTypeIds, payModeF.get(), feeTypeF.get());

		V2StatResponse response = new V2StatResponse();
		response.setData(data);
		response.setHeader(headers);
		RevenueSheetInfoTotal totalInfo = totalF.get();
		totalInfo.clacProfit();
		StatResponseTotal total = new StatResponseTotal();
		total.setCount(totalInfo.getCount());
		total.setSize(params.getLimit());
		total.setOffset(params.getOffset());
		total.setTemplate("共 %s 条数据，总收入 %s，成本合计 %s，毛利合计 %s，毛利率 %s");
		total.setData(Arrays.asList(totalInfo.getCount(), totalInfo.getReceivedPrice(), totalInfo.getCostPrice(), totalInfo.getProfit(), totalInfo.getProfitRateStr()));
		response.setTotal(total);
		return response;
	}

	public List<ExcelUtils.AbcExcelSheet> export(RevenueSheetInfoParams params) throws ExecutionException, InterruptedException {
		V2StatResponse response = selectSheetInfo(params);
		List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
		ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();
		sheet.setName("门诊收费");
		sheet.setData(ExcelUtils.exportMapData(response.getData(), response.getHeader()));
		sheet.setSheetDefinition(ExcelUtils.exportTableHeader(response.getHeader()));
		sheets.add(sheet);
		return sheets;
	}

	private void handleRevenueSheetInfoList(String chainId, String hisType, List<TableHeaderEmployeeItem> headers, Set<String> pays, Set<String> feeTypeIds, Map<Integer, String> payModeMap, Map<Long, V2GoodsFeeType> feeTypeMap) {
		for (TableHeaderEmployeeItem header : headers) {
			if ("payModes".equals(header.getProp())) {
				List<TableHeaderEmployeeItem> children = new ArrayList<>();
				int idx = 0;
				for (String pm : pays) {
					children.add(TableHeaderUtils.genTableHeaderEmployeeItem("payMode_" + pm, payModeMap.getOrDefault(Integer.valueOf(pm), "-"), header.getWidth(), idx, header.getType()));
					idx ++;
				}
				header.setColumnChildren(children);
			}
			if ("feeTypes".equals(header.getProp())) {
				List<TableHeaderEmployeeItem> children = new ArrayList<>();
				int idx = 0;
				for (String ft : feeTypeIds) {
					String name = query.queryProductClassifyLevel1(ft, hisType);
					if ("100".equals(hisType)) {
						V2GoodsFeeType g = feeTypeMap.get(Long.valueOf(ft));
						name = g != null ? g.getName() : "-";
					}

					TableHeaderEmployeeItem child= TableHeaderUtils.genTableHeaderEmployeeItem("feeType_" + ft, name, header.getWidth(), idx, header.getType());
					List<TableHeaderEmployeeItem> subChildren = new ArrayList<>();
					subChildren.add(TableHeaderUtils.genTableHeaderEmployeeItem("feeType_" + ft + "_amount", "实收", header.getWidth(), idx, header.getType()));
					subChildren.add(TableHeaderUtils.genTableHeaderEmployeeItem("feeType_" + ft + "_cost", "成本", header.getWidth(), idx, header.getType()));
					subChildren.add(TableHeaderUtils.genTableHeaderEmployeeItem("feeType_" + ft + "_profit", "毛利率", header.getWidth(), idx));
					child.setColumnChildren(subChildren);
					children.add(child);
					idx ++;
				}
				header.setColumnChildren(children);
			}
		}
		if ("".equals(chainId)) {
			headers.add(TableHeaderUtils.genTableHeaderEmployeeItem("dutyTherapistName", "责任治疗师", 120, 998, null));
			headers.add(TableHeaderUtils.genTableHeaderEmployeeItem("primaryTherapistName", "首席治疗师", 120, 998, null));
		}
	}
}
