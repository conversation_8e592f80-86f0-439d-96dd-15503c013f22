package cn.abc.flink.stat.service.socket;

import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.annotation.OnConnect;
import com.corundumstudio.socketio.annotation.OnDisconnect;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/12 15:56
 */
@Service
@Slf4j
public class OpenErrorMsgSocketIOServiceImpl implements ISocketIOService {
	@Autowired
	private SocketIOServer socketIoServer;

	@Override
	public void start() {
		try {
			socketIoServer.start();
		}catch (Exception e){
			log.error(e.getMessage(), e);
		}
	}

	@Override
	public void stop() {
		try {
			socketIoServer.stop();
		}catch (Exception e){
			log.error(e.getMessage(), e);
		}
	}

	@OnConnect
	public void connect(SocketIOClient client) {
		String userFlag = client.getHandshakeData().getSingleUrlParam("userFlag");
		SocketUtil.connectMap.put(userFlag, client);
		log.info("客户端userFlag: "+ userFlag+ "已连接");
	}
	@OnDisconnect
	public void onDisconnect(SocketIOClient client) {
		String userFlag = client.getHandshakeData().getSingleUrlParam("userFlag");
		log.info("客户端userFlag:" + userFlag + "断开连接");
		SocketUtil.connectMap.remove(userFlag, client);
	}

	@Override
	public void pushMessageToUser(String userId, String msgContent) {

	}
}
