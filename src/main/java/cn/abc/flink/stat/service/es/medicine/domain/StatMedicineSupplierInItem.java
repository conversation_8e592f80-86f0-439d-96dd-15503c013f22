package cn.abc.flink.stat.service.es.medicine.domain;

public class StatMedicineSupplierInItem {
    private String supplierId;
    private String supplier;

    private double inBatchCount;
    private double inKindCount;
    private double inCount;

    private double inAmount;
    private double inAmountExcludeTax;

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public double getInBatchCount() {
        return inBatchCount;
    }

    public void setInBatchCount(double inBatchCount) {
        this.inBatchCount = inBatchCount;
    }

    public double getInKindCount() {
        return inKindCount;
    }

    public void setInKindCount(double inKindCount) {
        this.inKindCount = inKindCount;
    }

    public double getInCount() {
        return inCount;
    }

    public void setInCount(double inCount) {
        this.inCount = inCount;
    }

    public double getInAmount() {
        return inAmount;
    }

    public void setInAmount(double inAmount) {
        this.inAmount = inAmount;
    }

    public double getInAmountExcludeTax() {
        return inAmountExcludeTax;
    }

    public void setInAmountExcludeTax(double inAmountExcludeTax) {
        this.inAmountExcludeTax = inAmountExcludeTax;
    }

    @Override
    public String toString() {
        return "StatMedicineSupplierInItem{" +
                "supplierId='" + supplierId + '\'' +
                ", supplier='" + supplier + '\'' +
                ", inBatchCount=" + inBatchCount +
                ", inKindCount=" + inKindCount +
                ", inCount=" + inCount +
                ", inAmount=" + inAmount +
                ", inAmountExcludeTax=" + inAmountExcludeTax +
                '}';
    }
}
