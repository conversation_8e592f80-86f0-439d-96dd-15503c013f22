package cn.abc.flink.stat.service.cis.pharmacy.clinic.vo;

import cn.abc.flink.stat.common.StringUtils;
import cn.abc.flink.stat.common.domain.V2ChargeCooperationOrder;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.service.cis.handler.PatientHandler;
import cn.abc.flink.stat.service.cis.outpatient.handler.OutpatientHandler;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.pharmacy.clinic.vo
 * @Author: zs
 * @CreateTime: 2024-08-06  13:58
 * @Description: 药诊互通实体
 * @Version: 1.0
 */
public class PharmacyClinicItemVo {

    private static final Logger logger = LoggerFactory.getLogger(PharmacyClinicItemVo.class);

    /**
     * 诊所ID
     */
    private String clinicId;
    private String clinicName;

    /**
     * 药店ID
     */
    private String pharmacyId;
    private String pharmacyName;

    /**
     * 收费时间
     */
    private String createTime;

    /**
     * 类型
     */
    private String type;

    /**
     * 诊所诊号
     */
    private String patientorderNo;
    /**
     * 药店销售单号
     */
    private String sellNo;

    /**
     * 医生
     */
    private String doctorId;
    private String doctorName;

    /**
     * 患者
     */
    private String patientId;
    private String patientName;
    private String sex;
    private String age;
    private String mobile;

    /**
     * 诊所患者
     */
    private String cooperationPatientName;

    /**
     * 诊断
     */
    private String diagnosis;

    /**
     * 实收
     */
    private BigDecimal receivedPrice;

    /**
     * 支付方式
     */
    private Integer payType;
    private Integer paySubType;
    private String payMode;

    /**
     * 收费员
     */
    private String cashierId;
    private String cashierName;

    /**
     * 药师
     */
    private String pharmacistId;
    private String pharmacistName;
    /**
     * 药诊互通订单id
     */
    private Long cooperationOrderId;
    /**
     * 药诊互通-诊所患者json
     */
    private String cooperationPatientInfo;

    @ApiModelProperty("零售备注")
    private String chargeSheetComment;


    public void set(Map<String, Organ> organMap, Map<String, String> empMap, Map<String, V2Patient> v2PatientMap) {
        this.clinicName = organMap.get(this.clinicId) == null ? null : organMap.get(this.clinicId).getName();
        this.pharmacyName = organMap.get(this.pharmacyId) == null ? null : organMap.get(this.pharmacyId).getName();
        this.cashierName = empMap.get(this.cashierId);
        this.doctorName = empMap.get(this.doctorId);
        this.pharmacistName = empMap.get(this.pharmacistId);
        V2Patient v2Patient = v2PatientMap.get(this.patientId);
        if (v2Patient != null) {
            this.patientName = v2Patient.getName();
            this.sex = v2Patient.getSex();
            this.mobile = v2Patient.getMobile();
            this.age = PatientHandler.getBirthdayByV2Patient(v2Patient);
        }
    }

    /**
     * @param
     * @param v2ChargeCooperationOrdersMap -
     * @return
     * @Description: 设置诊所患者信息和诊断信息
     * @Author: zs
     * @Date: 2024/8/8 14:52
     */
    public void setCooperation(Map<Long, V2ChargeCooperationOrder> v2ChargeCooperationOrdersMap, OutpatientHandler outpatientHandler) {
        V2ChargeCooperationOrder v2ChargeCooperationOrder = v2ChargeCooperationOrdersMap.get(this.cooperationOrderId);
        if (this.cooperationPatientInfo != null && !"".equals(this.cooperationPatientInfo)) {
            //设置患者信息
            PatientInfo patientInfo = JSON.parseObject(this.cooperationPatientInfo, PatientInfo.class);
            this.cooperationPatientName = PatientHandler.getPatientInfo(patientInfo.getName(), patientInfo.getSex(), patientInfo.getBirthday(), patientInfo.getMobile(), 1);
        }
        if (v2ChargeCooperationOrder != null) {
            this.doctorName = v2ChargeCooperationOrder.getSourceDoctorName();
            JSONArray dentistryDiagnosisArray = null;
            JSONObject sourcePatientOrder = null;
            try {
                dentistryDiagnosisArray = JSONArray.parseArray(v2ChargeCooperationOrder.getExtendDiagnosisInfos());
                sourcePatientOrder = JSONArray.parseObject(v2ChargeCooperationOrder.getSourcePatientOrder());
            } catch (JSONException e) {
                e.printStackTrace();
                logger.error("药诊互通json解析失败setCooperation方法extendDiagnosisInfos字段，报错信息{}",
                        e.getLocalizedMessage());
            }
            if (dentistryDiagnosisArray != null) {
                String dentistryDiagnosis = outpatientHandler.handleJsonArr(dentistryDiagnosisArray, true);
                this.diagnosis = dentistryDiagnosis;
            }
            if (sourcePatientOrder != null) {
                this.patientorderNo = StringUtils.formatNumberWithLeadingZeros(sourcePatientOrder.getInteger("no"), 8);
            }
        }
        this.cooperationPatientInfo = null;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public String getPharmacyId() {
        return this.pharmacyId;
    }

    public String getPharmacyName() {
        return this.pharmacyName;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public String getType() {
        return this.type;
    }

    public String getPatientorderNo() {
        return this.patientorderNo;
    }

    public String getSellNo() {
        return this.sellNo;
    }

    public String getDoctorId() {
        return this.doctorId;
    }

    public String getDoctorName() {
        return this.doctorName;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public String getSex() {
        return this.sex;
    }

    public String getAge() {
        return this.age;
    }

    public String getMobile() {
        return this.mobile;
    }

    public String getCooperationPatientName() {
        return this.cooperationPatientName;
    }

    public String getDiagnosis() {
        return this.diagnosis;
    }

    public BigDecimal getReceivedPrice() {
        return this.receivedPrice;
    }

    public Integer getPayType() {
        return this.payType;
    }

    public Integer getPaySubType() {
        return this.paySubType;
    }

    public String getPayMode() {
        return this.payMode;
    }

    public String getCashierId() {
        return this.cashierId;
    }

    public String getCashierName() {
        return this.cashierName;
    }

    public String getPharmacistId() {
        return this.pharmacistId;
    }

    public String getPharmacistName() {
        return this.pharmacistName;
    }

    public Long getCooperationOrderId() {
        return this.cooperationOrderId;
    }

    public String getCooperationPatientInfo() {
        return this.cooperationPatientInfo;
    }


    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setPharmacyId(String pharmacyId) {
        this.pharmacyId = pharmacyId;
    }

    public void setPharmacyName(String pharmacyName) {
        this.pharmacyName = pharmacyName;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setPatientorderNo(String patientorderNo) {
        this.patientorderNo = patientorderNo;
    }

    public void setSellNo(String sellNo) {
        this.sellNo = sellNo;
    }

    public void setDoctorId(String doctorId) {
        this.doctorId = doctorId;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setCooperationPatientName(String cooperationPatientName) {
        this.cooperationPatientName = cooperationPatientName;
    }

    public void setDiagnosis(String diagnosis) {
        this.diagnosis = diagnosis;
    }

    public void setReceivedPrice(BigDecimal receivedPrice) {
        this.receivedPrice = receivedPrice;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public void setPaySubType(Integer paySubType) {
        this.paySubType = paySubType;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public void setCashierId(String cashierId) {
        this.cashierId = cashierId;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public void setPharmacistId(String pharmacistId) {
        this.pharmacistId = pharmacistId;
    }

    public void setPharmacistName(String pharmacistName) {
        this.pharmacistName = pharmacistName;
    }

    public void setCooperationOrderId(Long cooperationOrderId) {
        this.cooperationOrderId = cooperationOrderId;
    }

    public void setCooperationPatientInfo(String cooperationPatientInfo) {
        this.cooperationPatientInfo = cooperationPatientInfo;
    }

    public String getChargeSheetComment() {
        return chargeSheetComment;
    }

    public void setChargeSheetComment(String chargeSheetComment) {
        this.chargeSheetComment = chargeSheetComment;
    }
}
