package cn.abc.flink.stat.service.cis.goods.inventory.domain;



import java.util.List;

/**
 * @description: 套餐内容
 * @author: lzq
 * @Date: 2022/10/24 6:55 下午
 */
public class V2GoodsCompose {

    /**
     * goods id
     */
    private String goodId;

    /**
     * goods name
     */
    private String name;

    /**
     * 数量
     */
    private double count;

    /**
     * 单位
     */
    private String unit;

    /**
     * 子节点
     */
    private List<V2GoodsCompose> children;



    public String getGoodId() {
        return this.goodId;
    }

    public String getName() {
        return this.name;
    }

    public double getCount() {
        return this.count;
    }

    public String getUnit() {
        return this.unit;
    }

    public List<V2GoodsCompose> getChildren() {
        return this.children;
    }


    public void setGoodId(String goodId) {
        this.goodId = goodId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCount(double count) {
        this.count = count;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setChildren(List<V2GoodsCompose> children) {
        this.children = children;
    }

}
