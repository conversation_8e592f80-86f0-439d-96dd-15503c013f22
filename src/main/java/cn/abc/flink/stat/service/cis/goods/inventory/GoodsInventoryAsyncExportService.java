package cn.abc.flink.stat.service.cis.goods.inventory;

import cn.abc.flink.stat.common.*;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.service.cis.execute.bill.handler.ExecuteBillCommonHandler;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.GoodsInventoryParam;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryGoods;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.InventoryRecord;
import cn.abc.flink.stat.service.cis.goods.inventory.handler.GoodsInventoryDataHandler;
import cn.abc.flink.stat.service.cis.goods.inventory.handler.GoodsInventoryHeaderHandler;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static cn.abc.flink.stat.common.HeaderTableKeyConfig.EXCLUDE_HIDDEN_1;


/**
 * @description:
 * @author: lzq
 * @Date: 2022/8/10 7:42 下午
 */
//2021-12-20 新增异步导出服务
@Component
public class GoodsInventoryAsyncExportService implements BaseAsyncExportInterface {

    @Value("${abc.async.export.page-size}")
    private static final Integer PAGE_SIZE = 50000;

    private static final Integer EXCEL_SHEET_MAX_ROW_NUMBER = 1048576;

    @Autowired
    private GoodsInventoryService service;

    @Autowired
    private DimensionQuery dimensionQuery;

    @Autowired
    private ExecutorService cacheExecutorService;

    @Autowired
    private GoodsInventoryDataHandler dataHandler;

    @Autowired
    ExecuteBillCommonHandler executeBillCommonHandler;

    @Autowired
    private GoodsInventoryHeaderHandler headerHandler;

    @Override
    public String getKey() {
        return "goods-inventory";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        Integer pharmacyNo = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "pharmacyNo"));
        String hisType = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerHisType"));
        StringBuilder fileName = new StringBuilder();
        if (hisType.equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)){
            List<String> keyWordList = (List<String>) MapUtils.isExistsAndReturn(params, "keyWordList");
            if (keyWordList.contains(ExportKeyWordEnum.GOODS_INVENTORY_CLASSIFY.getKey())) {
                fileName.append("进销存分类汇总");
            }else if (keyWordList.contains(ExportKeyWordEnum.GOODS_INVENTORY_GOODS.getKey())){
                fileName.append("进销存药品汇总");
            } else if (keyWordList.contains(ExportKeyWordEnum.GOODS_INVENTORY_RECORD.getKey())) {
                fileName.append("进销存明细");
            }
        }else {
            fileName.append("进销存统计");
        }
        if (pharmacyNo != null) {
            String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
            String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
            Integer pharmacyType = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "pharmacyType"));
            Map<String, Map<Integer, String>> pharmacyNameByNo = dimensionQuery.queryPharmacyNameByNo(chainId, clinicId,
                    pharmacyType);
            fileName.append(pharmacyNameByNo.get(clinicId).get(pharmacyNo));
        }
        fileName.append(beginDate);
        fileName.append("_");
        fileName.append(endDate);
        fileName.append(".xlsx");
        return fileName.toString();
    }


    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String headerClinicId = (String) MapUtils.isExistsAndReturn(params, "headerClinicId");
        Integer nodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        //单调升连锁后，区分是否是真正的单店 1-表示 真正的单店 ，0-表示连锁下的单店
        String viewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        Integer pharmacyType = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "pharmacyType"));
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String fee1 = (String) MapUtils.isExistsAndReturn(params, "fee1");
        String fee2 = (String) MapUtils.isExistsAndReturn(params, "fee2");
        String goodsId = (String) MapUtils.isExistsAndReturn(params, "goodsId");
        String goodsKeyword = (String) MapUtils.isExistsAndReturn(params, "goodsKeyword");
        String employeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        Integer enableCost = MapUtils.isExistsAndReturn(params, "enableCost") != null
                ? ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "enableCost")) : null;
        String supplierId = (String) MapUtils.isExistsAndReturn(params, "supplierId");
        Integer dimension = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "dimension"));
        Integer pharmacyNo = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "pharmacyNo"));
        String headerType = (String) MapUtils.isExistsAndReturn(params, "headerType");
        String hisType = (String) MapUtils.isExistsAndReturn(params, "headerHisType");
        Integer stockHaveChange = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "stockHaveChange"));
        Integer supportedBusiness = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "headerSupportedBusiness"));
        String batchNo = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "batchNo"));
        if (stockHaveChange == null) {
            stockHaveChange = 0;
        }
        List<String> keyWordList = (List<String>) MapUtils.isExistsAndReturn(params, "keyWordList");
        List<String> actions;
        if (MapUtils.isExistsAndReturn(params, "actions") != null
                && !StrUtil.isBlank(String.valueOf(MapUtils.isExistsAndReturn(params, "actions")))) {
            actions = (List<String>) MapUtils.isExistsAndReturn(params, "actions");
        } else {
            actions = null;
        }
        List<String> pharmacyNos = null;
        if (MapUtils.isExistsAndReturn(params, "pharmacyNos") != null) {
            pharmacyNos = (List<String>) MapUtils.isExistsAndReturn(params, "pharmacyNos");
        }
        String baseMedicineType = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "baseMedicineType"));
        List<Long> profitCategoryTypeIds = null;
        if (MapUtils.isExistsAndReturn(params, "profitCategoryTypeIds") != null) {
            profitCategoryTypeIds = (List<Long>) MapUtils.isExistsAndReturn(params, "profitCategoryTypeIds");
        }
        List<Integer> sceneIds = null;
        if (MapUtils.isExistsAndReturn(params, "sceneIds") != null) {
            sceneIds = (List<Integer>) MapUtils.isExistsAndReturn(params, "sceneIds");
        }
        List<Long> batchIds = null;
        if (MapUtils.isExistsAndReturn(params, "batchIds") != null) {
            batchIds = (List<Long>) MapUtils.isExistsAndReturn(params, "batchIds");
        }
        //单店判断
        GoodsInventoryParam param = new GoodsInventoryParam(chainId, clinicId, pharmacyType, beginDate, endDate, fee1,
                fee2, goodsId, goodsKeyword, actions, supplierId, enableCost, dimension, employeeId, nodeType,
                pharmacyNo, headerType, hisType);
        param.setIsExport(true);
        param.setSingleStore(viewMode, String.valueOf(nodeType));
        param.getParams().setHeaderClinicId(headerClinicId);
        param.setKeyWordList(keyWordList);
        param.setStockHaveChange(stockHaveChange);
        param.setSupportedBusiness(supportedBusiness);
        param.setPharmacyNos(pharmacyNos);
        param.setBaseMedicineType(baseMedicineType);
        param.setProfitCategoryTypeIds(profitCategoryTypeIds);
        param.setSceneIds(sceneIds);
        param.getParams().setViewModeInteger(Integer.valueOf(viewMode));
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            param.setClinicId(headerClinicId);
        }
        param.setBatchNo(batchNo);
        param.setBatchIds(batchIds);
        //新增分页查询添加数据
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        List<ExcelUtils.AbcExcelSheet> sheets = export(param);
        ExcelUtils.export(baos, sheets);
        return baos;
    }

    /**
     * @param param param
     * @return List<ExcelUtils.AbcExcelSheet>
     * @throws Exception e
     */
    private List<ExcelUtils.AbcExcelSheet> export(GoodsInventoryParam param) throws Exception {
        param.initBeginDateAndEndDate();
        param.initDs();
        dataHandler.setIsContainsAllotInInside(param, dimensionQuery);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        String classifySheetName= "分类";
        String goodsSheetName = "药品";
        String recordSheetName = "流水";
        if (param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            classifySheetName = "进销存分类汇总";
            goodsSheetName = "进销存药品汇总";
            recordSheetName = "进销存明细";
        }
        //当前页
        int pageIndex1 = 1;
        int pageIndex2 = 1;
        //是否继续
        boolean isDrug = true;
        boolean isDocument = true;
        if (param.getKeyWordList().contains(ExportKeyWordEnum.GOODS_INVENTORY_CLASSIFY.getKey())) {
            V2StatResponse classifyResponse = service.inventoryByClassify(param);
            ExcelUtils.AbcExcelSheet classifySheet = new ExcelUtils.AbcExcelSheet();
            classifySheet.setName(classifySheetName);
            classifySheet.setSheetDefinition(ExcelUtils.exportTableHeader(classifyResponse.getHeader()));
            classifySheet.setData(ExcelUtils.exportDataV2(classifyResponse.getData(), classifyResponse.getHeader()));
            classifySheet.setTableHeaderEmployeeItems(classifyResponse.getHeader());
            sheets.add(classifySheet);
        }
        if (param.getKeyWordList().contains(ExportKeyWordEnum.GOODS_INVENTORY_GOODS.getKey())) {
            String tableKey = HeaderTableKeyConfig.STAT_GOODS_INVENTORY_SUMMARY;
            if (param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                tableKey = HeaderTableKeyConfig.STAT_PHARMACY_GOODS_INVENTORY_SUMMARY;
            }
            List<TableHeaderEmployeeItem> summaryHeaderList = dimensionQuery.getTableHeaderEmployeeItems(
                    param.getParams().getEmployeeId(), tableKey, param.getParams().getViewModeInteger(),
                    param.getParams().getNodeType(), EXCLUDE_HIDDEN_1, Integer.valueOf(param.getHisType()));
            List<List<Object>> drugExportData = new ArrayList<>();
            ExcelUtils.AbcExcelSheet summarySheet = new ExcelUtils.AbcExcelSheet();
            summarySheet.setName(goodsSheetName);
            InventoryGoods inventoryGoods = null;
            while (isDrug) {
                //先查询合计行数据处理表头再查询列表数据
                if (inventoryGoods == null) {
                    CompletableFuture<InventoryGoods> goodsSummary1 = CompletableFuture.supplyAsync(() -> {
                        return dataHandler.goodsSummary(param, executeBillCommonHandler.fetchGoodsList(
                                param.getChainId(), param.getClinicId(), param.getParams().getEmployeeId(),
                                param.getGoodsId(), param.getGoodsKeyword()));
                    }, cacheExecutorService);
                    CompletableFuture.allOf(goodsSummary1).join();
                    inventoryGoods = goodsSummary1.get();
                }
                summaryHeaderList = headerHandler.goodsV1(summaryHeaderList, param, inventoryGoods);
                List<List<Object>> summaryList = service.export(param, pageIndex1++, PAGE_SIZE,
                        "drug", summaryHeaderList);
                if (CollUtil.isEmpty(summaryList)) {
                    List<List<Object>> summary = ExcelUtils.exportDataV2(CollUtil.newArrayList(inventoryGoods), summaryHeaderList);
                    summaryList.addAll(summary);
                    isDrug = false;
                }
                drugExportData.addAll(summaryList);
            }
            summarySheet.setData(drugExportData);
            summarySheet.setSheetDefinition(ExcelUtils.exportTableHeader(summaryHeaderList));
            sheets.add(summarySheet);
        }
        if (param.getKeyWordList().contains(ExportKeyWordEnum.GOODS_INVENTORY_RECORD.getKey())) {
            String headerKey;
            if (!StrUtil.isBlank(param.getHeaderType()) && param.getHeaderType().equals("stock")) {
                headerKey = HeaderTableKeyConfig.STOCK_GOODS_INVENTORY_DETAIL;
            } else if (!StrUtil.isBlank(param.getHeaderType())
                    && param.getHeaderType().equals(CommonConstants.CONTAINS_EYEGLASS_HEADER_TYPE)) {
                headerKey = HeaderTableKeyConfig.STOCK_GOODS_INVENTORY_DETAIL;
            } else if (param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                headerKey = HeaderTableKeyConfig.STAT_PHARMACY_GOODS_INVENTORY_RECORD;
            } else {
                headerKey = HeaderTableKeyConfig.GOODS_INVENTORY_RECORD;
            }
            if(StrUtil.isNotEmpty(param.getBatchNo())){
                List<Long> stockIds = dimensionQuery.queryGoodsStockIdsByBatchNo(param.getChainId(), param.getClinicId(), param.getBatchNo());
                param.setStockIds(stockIds);
            }
            List<TableHeaderEmployeeItem> recordHeaderList = dimensionQuery.getTableHeaderEmployeeItems(
                    param.getParams().getEmployeeId(), headerKey, param.getParams().getViewModeInteger(),
                    param.getParams().getNodeType(), EXCLUDE_HIDDEN_1, Integer.valueOf(param.getHisType()));
            recordHeaderList = headerHandler.recordV1(recordHeaderList, param, null);
            List<List<Object>> recordExportData = new ArrayList<>();
            ExcelUtils.AbcExcelSheet recordSheet = new ExcelUtils.AbcExcelSheet();
            recordSheet.setName(recordSheetName);
            recordSheet.setSheetDefinition(ExcelUtils.exportTableHeader(recordHeaderList));
            while (isDocument) {
                //获取数据
                // 单据维度数据时间范围过大 20*50000=1000000 excel单个sheet最大行数为1048576
                if (pageIndex2 * PAGE_SIZE > EXCEL_SHEET_MAX_ROW_NUMBER) {
                    break;
                }
                List<List<Object>> recordList = service.export(param, pageIndex2++, PAGE_SIZE,
                        "", recordHeaderList);
                if (CollUtil.isEmpty(recordList)) {
                    //进销存单据tab-合计行
                    CompletableFuture<InventoryRecord> recordSummary1 = CompletableFuture.supplyAsync(() -> {
                        return dataHandler.recordSummary(param, executeBillCommonHandler.fetchGoodsList(
                                param.getChainId(), param.getClinicId(), param.getParams().getEmployeeId(),
                                param.getGoodsId(), param.getGoodsKeyword()));
                    }, cacheExecutorService);
                    InventoryRecord inventoryRecord = recordSummary1.get();
                    recordList = ExcelUtils.exportDataV2(CollUtil.newArrayList(inventoryRecord), recordHeaderList);
                    isDocument = false;
                }
                recordExportData.addAll(recordList);
            }
            recordSheet.setData(recordExportData);
            sheets.add(recordSheet);
        }
        return sheets;
    }
}
