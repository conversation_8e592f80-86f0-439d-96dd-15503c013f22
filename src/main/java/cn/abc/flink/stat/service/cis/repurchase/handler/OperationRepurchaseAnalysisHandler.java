package cn.abc.flink.stat.service.cis.repurchase.handler;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.mq.rabbit.send.RabbitMqSendAsyncExport;
import cn.abc.flink.stat.service.cis.management.domain.ManagementParam;
import cn.abc.flink.stat.service.cis.repurchase.domain.RepurchaseAnalysisHeader;
import cn.abc.flink.stat.service.cis.repurchase.domain.RepurchaseAnalysisRsp;
import cn.abc.flink.stat.service.cis.repurchase.domain.RepurchaseDoctor;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-01-24 14:35
 **/
@Component
public class OperationRepurchaseAnalysisHandler {

    @Autowired
    private RabbitMqSendAsyncExport rabbitMqSendAsyncExport;

    private static final int NUMBER = 160;
    /**
     * @Description:
     * @param
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.repurchase.domain.RepurchaseAnalysisHeader>
     * @Author: zs
     * @Date: 2022/8/3 18:42
     */
    public static List<RepurchaseAnalysisHeader> getHeaderList() {
        List<RepurchaseAnalysisHeader> headers = new ArrayList<>();
        headers.add(new RepurchaseAnalysisHeader("医生", "doctorName", "left", NUMBER));
        headers.add(new RepurchaseAnalysisHeader("就诊人数", "medicalCount", "right", NUMBER));
        headers.add(new RepurchaseAnalysisHeader("复诊人数", "repurchaseCount", "right", NUMBER));
        headers.add(new RepurchaseAnalysisHeader("复诊率", "repurchaseRate", "right", NUMBER));

        return headers;
    }

    /**
     * @Description:
     * @param
     * @param response -
     * @param param -
     * @param repurchaseAnalysisRsp -
     * @return
     * @Author: zs
     * @Date: 2022/8/3 18:42
     */
    public void export(HttpServletResponse response, ManagementParam param, RepurchaseAnalysisRsp repurchaseAnalysisRsp) {
        String fileName = "医生复诊分析" + param.getBeginDate() + "_" + param.getEndDate();
        byte[] bytes = fileName.getBytes(StandardCharsets.UTF_8);
        try {
            rabbitMqSendAsyncExport.sendOperatorLogMessages(param.getChainId(), param.getClinicId(), param.getParams().getEmployeeId(), fileName, param.getLoginWay());
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            fileName = new String(bytes, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList();
        ExcelUtils.AbcExcelSheet repurchaseDoctorSheet = new ExcelUtils.AbcExcelSheet();
        List<RepurchaseDoctor> data = repurchaseAnalysisRsp.getData();
        List<RepurchaseAnalysisHeader> header = repurchaseAnalysisRsp.getHeader();
        repurchaseDoctorSheet.setName("主表");
        repurchaseDoctorSheet.setData(makeRepurchaseDoctorBody(data));
        repurchaseDoctorSheet.setSheetDefinition(makeRepurchaseDoctorHead(header));
        sheets.add(repurchaseDoctorSheet);

        try {
            ExcelUtils.export(fileName, response, sheets);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    /**
     * @Description:
     * @param
     * @param data -
     * @return
     * @return java.util.List
     * @Author: zs
     * @Date: 2022/8/3 18:44
     */
    private List makeRepurchaseDoctorBody(List<RepurchaseDoctor> data) {
        List<List<Object>> lines = new ArrayList<>();
        for (RepurchaseDoctor d : data) {
            List<Object> row = new ArrayList<>();
            row.add(d.getDoctorName());
            row.add(d.getMedicalCount());
            row.add(d.getRepurchaseCount());
            row.add(d.getRepurchaseRate());
            lines.add(row);
        }
        return lines;
    }

    /**
     * @Description:
     * @param
     * @param headers -
     * @return
     * @return java.util.List
     * @Author: zs
     * @Date: 2022/8/3 18:44
     */
    private List makeRepurchaseDoctorHead(List<RepurchaseAnalysisHeader> headers) {
        List<List<String>> header = new ArrayList<>();
        for (RepurchaseAnalysisHeader h : headers) {
            header.add(Lists.newArrayList(h.getLabel()));
        }
        return header;
    }


}
