package cn.abc.flink.stat.service.cis.charge.product;

import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.V2Goods;
import cn.abc.flink.stat.dimension.domain.V2GoodsCustomType;
import cn.abc.flink.stat.dimension.domain.V2GoodsProfitCategoryType;
import cn.abc.flink.stat.dimension.domain.V2GoodsSysType;
import cn.abc.flink.stat.dimension.domain.V2GoodsTag;
import cn.abc.flink.stat.es.SuggestService;
import cn.abc.flink.stat.service.cis.achievement.charge.AchievementChargeService;
import cn.abc.flink.stat.service.cis.charge.product.domain.ChargeProductData;
import cn.abc.flink.stat.service.cis.charge.product.domain.ChargeProductEmployee;
import cn.abc.flink.stat.service.cis.charge.product.domain.ChargeProductResult;
import cn.abc.flink.stat.service.cis.charge.product.domain.ChargeProductRpcResp;
import cn.abc.flink.stat.service.cis.charge.product.domain.ChargeProductSelectResult;
import cn.abc.flink.stat.service.cis.charge.product.pojos.OperationProductReqParams;
import cn.abc.flink.stat.service.cis.config.StatConfigService;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.V2GoodsCompose;
import cn.abc.flink.stat.service.cis.handler.GoodsHandler;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/12 5:18 下午
 * @modified ljc
 */
@Service
public class ChargeProductService {
    private static final Logger logger = LoggerFactory.getLogger(ChargeProductService.class);

    @Resource
    ChargeProductAsyncSelector selector;

    @Autowired
    ChargeProductExportHandler handler;

    @Autowired
    AchievementChargeService achievementChargeService;

    @Autowired
    private SuggestService suggestService;

    @Autowired
    private StatConfigService statConfigService;

    @Autowired
    DimensionQuery query;

    @Autowired
    GoodsHandler goodsHandler;

    String env = TableUtils.getCisTable();


    /**
     * 收费项目统计 - rpc
     * @param params -
     * @return rpc 返回内容
     * @throws ExecutionException -
     * @throws InterruptedException -
     */
    public ChargeProductRpcResp getChargeProductListForRpc(OperationProductReqParams params)
            throws ExecutionException, InterruptedException {
        V2StatResponse chargeProductList = getChargeProductList(params, new StatConfigParam());
        ChargeProductRpcResp chargeProductRpcResp = new ChargeProductRpcResp();
        ChargeProductResult result = new ChargeProductResult();
        result.setData(chargeProductList.getData());
        result.setTotal((Double) chargeProductList.getTotal().getData().get(0));
        result.setCount((Double) chargeProductList.getTotal().getData().get(1));
        result.setAmount((Double) chargeProductList.getTotal().getData().get(2));
        chargeProductRpcResp.setContentFromApiResult(result);
        return chargeProductRpcResp;
    }


    /**
     * 收费项目service
     * @param params 参数
     * @param configParam 配置param
     * @return ChargeProductResult
     * @throws ExecutionException exception
     * @throws InterruptedException exception
     */
    public V2StatResponse getChargeProductList(OperationProductReqParams params, StatConfigParam configParam)
            throws ExecutionException, InterruptedException {
        V2StatResponse response = new V2StatResponse();
        StatConfigDto dto = statConfigService.selectConfig(configParam);
        if (dto != null) {
            params.setIsComposeShareEqually(dto.getIsComposeShareEqually());
        }
        String tableKey;
        if (!StrUtil.isBlank(params.getHisType()) && params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
            tableKey = HeaderTableKeyConfig.STAT_PHARMACY_OPERATION_CHARGE_PRODUCT_LIST;
        } else {
            tableKey = HeaderTableKeyConfig.STAT_OPERATION_CHARGE_PRODUCT_LIST;
        }
        if (params.getTagIds() != null && params.getTagIds().size() > 0) {
            String tagIdSql = goodsHandler.getGoodsSqlByTagIds(params.getChainId(), params.getClinicId(), "product_id", params.getTagIds());
            if (tagIdSql == null) {
                return getV2StatResponse(params, response, tableKey);
            }
            params.setTagIdSql(tagIdSql);
        }
        params.initParams(dto, query);
        ChargeProductResult result = new ChargeProductResult();
        params.setFee();
        params.searchProductIds(suggestService);

        // KUDU详细数据
        CompletableFuture<List<ChargeProductData>> dataFuture = CompletableFuture.supplyAsync(() -> {
            return selector.selectChargeProductList(env, params);
        });

        // 修正发药退药
        CompletableFuture<Map<String, ChargeProductData>> dispensingLogFuture = CompletableFuture.supplyAsync(() -> {
            return selector.selectDispensingLog(env, params);
        });
        // 总数
        CompletableFuture<ChargeProductResult> summaryFuture = CompletableFuture.supplyAsync(() -> {
            return selector.selectChargeProductSummary(env, params);
        });
        CompletableFuture<List<ChargeProductData>> composeSubItemsFormTransactionF = CompletableFuture.supplyAsync(() -> {
            if (dto != null && dto.getIsComposeShareEqually() == 0) {
                // 按照套餐整体提成的时候
                return selector.selectComposeSubItemsFormTransaction(params);
            }
            return new ArrayList<>();
        });
        CompletableFuture<List<TableHeaderEmployeeItem>> headerEmployeeItemsFuture = CompletableFuture.supplyAsync(
                () -> {
//                    return query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(),
//                            tableKey,
//                            params.getParams().getViewModeInteger(),
//                            params.getParams().getNodeType(), HeaderTableKeyConfig.EXCLUDE_HIDDEN_1, Integer.parseInt(params.getHisType()));
                    return new ArrayList<>();
                });
//        });
        // 员工维度
        CompletableFuture<Map<String, Employee>> empDimensionFuture =
                CompletableFuture.supplyAsync(() -> selector.selectEmps(params.getChainId()));
        // 二级分类维度
        CompletableFuture<Map<Integer, V2GoodsCustomType>> typeFuture =
                CompletableFuture.supplyAsync(() -> selector.selectProductCustomTypeTextByChainId(params.getChainId()));
        CompletableFuture<Map<String, V2GoodsSysType>> v2GoodsSysTypeF =
                CompletableFuture.supplyAsync(() -> query.selectSysFeeType());

        // 关联goods
        CompletableFuture<List<ChargeProductData>> dataJoinFuture = dataFuture.whenCompleteAsync((list, ac) -> {
            HashSet<String> ids = new HashSet<>();
            Set<String> goodIds = new HashSet<>();
            Set<Long> goodsVersionIds = new HashSet<>();
            Map<String, List<V2GoodsCompose>> v2GoodsCompose = new HashMap<>();
            Map<Long, V2GoodsProfitCategoryType> goodsVersoinCompose = new HashMap<>();
            for (ChargeProductData d : list) {
                ids.add(d.getProductId());
                if (d.getProfitCategoryType() != null) {
                    goodsVersionIds.add(d.getProfitCategoryType());
                }
                if (params.getIsComposeShareEqually() == 0 && d.getFee1().contains("11")) {
                    goodIds.add(d.getProductId());
                }
            }
            // goods维度
            Map<String, V2Goods> goodsMap = query.queryProducts(params.getChainId(), ids);
            Map<String, List<V2GoodsTag>> goodsTagMap = query.queryGoodsTagByGoodsIds(params.getChainId(), params.getClinicId(), ids);
            if (!goodIds.isEmpty()) {
                v2GoodsCompose = query.selectComposeDetail(params.getChainId(), params.getClinicId(), goodIds);
            }
            if (!goodsVersionIds.isEmpty()) {
                goodsVersoinCompose = query.queryGoodsProfitCategoryTypeByChainIdAndId(params.getChainId(), goodsVersionIds);
            }

            for (ChargeProductData d : list) {
                V2Goods goods = goodsMap.get(d.getProductId());
                d.setInfoFromGoods(goods);
                d.setSpecification(v2GoodsCompose, params);
                d.setProfitCategory(goodsVersoinCompose.get(d.getProfitCategoryType()));
                if (params.getIsExcport() == 0) {
                    d.setGoodsTagName(GoodsHandler.splicingGoodsTagNameToList(goodsTagMap.get(d.getProductId())));
                } else {
                    d.setGoodsTagName(GoodsHandler.splicingGoodsTagName(goodsTagMap.get(d.getProductId())));
                }

            }
        });

        CompletableFuture.allOf(dataJoinFuture, summaryFuture, empDimensionFuture, typeFuture, v2GoodsSysTypeF, composeSubItemsFormTransactionF,
                headerEmployeeItemsFuture, dispensingLogFuture).join();

        List<ChargeProductData> chargeProductData = dataJoinFuture.get();
        Map<Integer, V2GoodsCustomType> types2 = typeFuture.get();
        Map<String, ChargeProductData> dispensingLogMap = dispensingLogFuture.get();
        Map<String, V2GoodsSysType> v2GoodsSysTypeMap = v2GoodsSysTypeF.get();
        Map<String, Double> composeSubItemsCostMap = new HashMap<>();
        for (ChargeProductData composeCost : composeSubItemsFormTransactionF.get()) {
            String key = composeCost.getProductId() + "|" + composeCost.getFee1();
            if (composeCost.getFee2() != null) {
                key += "|" + composeCost.getFee2();
            }
            composeSubItemsCostMap.put(key, composeCost.getCost());
        }
        for (ChargeProductData d : chargeProductData) {
            d.setFee(selector, types2, params.getChainId(), v2GoodsSysTypeMap, params.getHisType());
            d.computeGross();
            d.setComposeCost(composeSubItemsCostMap);
            d.setGross((d.getAmount() != null ? d.getAmount() : 0) - (d.getCost() != null ? d.getCost() : 0));
            if (d.getAmount() != null && d.getAmount() != 0) {
                d.setProfit(d.getGross() / d.getAmount());
            }
            d.hoverCode(dispensingLogMap);
            d.pretty(params.getCashierId(), params.getPermission());
        }
        result = summaryFuture.get();
        response.setData(chargeProductData);
        response.setHeader(headerEmployeeItemsFuture.get());
        StatResponseTotal total = new StatResponseTotal((long) result.getTotal());
        List<Object> templateData = new ArrayList<>();
        templateData.add(result.getTotal());
        templateData.add(result.getCount());
        templateData.add(result.getAmount());
        total.setData(templateData);
        total.setTemplate("共 %s 条数据，销售数量 %s，销售金额 %s");
        response.setTotal(total);
        return response;
    }

    private V2StatResponse getV2StatResponse(OperationProductReqParams params, V2StatResponse response, String tableKey) {
        response.setHeader(query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(),
                tableKey,
                params.getParams().getViewModeInteger(),
                params.getParams().getNodeType(), HeaderTableKeyConfig.EXCLUDE_HIDDEN_1, Integer.parseInt(params.getHisType())));
        StatResponseTotal total = new StatResponseTotal();
        List<Object> templateData = new ArrayList<>();
        templateData.add(0);
        templateData.add(0);
        templateData.add(0);
        total.setData(templateData);
        total.setTemplate("共 %s 条数据，销售数量 %s，销售金额 %s");
        response.setTotal(total);
        return response;
    }

    /**
     * 导出收费项目
     * @param response resp
     * @param params 参数
     * @throws IOException exception
     * @throws ExecutionException exception
     * @throws InterruptedException exception
     */
    public void exportChargeProductList(HttpServletResponse response, OperationProductReqParams params)
            throws IOException, ExecutionException, InterruptedException {
        StatConfigParam configParam = new StatConfigParam(params.getChainId(), params.getClinicId(),
                params.getDispensaryType());
        V2StatResponse result = getChargeProductList(params, configParam);
        handler.exportChargeProduct(response, result, params);
    }

    /**
     * 收费项目筛选框service
     * @param chainId 连锁id
     * @param clinicId 门店id
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @return ChargeProductSelectResult
     * @throws ParseException Exception
     * @throws ExecutionException Exception
     * @throws InterruptedException Exception
     */
    public ChargeProductSelectResult getSelectionList(String chainId, String clinicId,
                                                      String beginDate, String endDate
    ) throws ParseException, ExecutionException, InterruptedException {
        CompletableFuture<ChargeProductSelectResult> respFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return selector.selectEmployeeAndFee(chainId, clinicId, beginDate, endDate, true);
            } catch (ExecutionException | InterruptedException e) {
                e.printStackTrace();
            }
            return new ChargeProductSelectResult();
        });

        CompletableFuture<List<ChargeProductEmployee>> cashierFuture =
                CompletableFuture.supplyAsync(() -> {
                    try {
                        return selector.selectChargeProductCashier(env, chainId, clinicId, beginDate, endDate);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    return null;
                });

        CompletableFuture.allOf(respFuture, cashierFuture);
        ChargeProductSelectResult arsp = respFuture.get();
        List<ChargeProductEmployee> cashier = cashierFuture.get();
        arsp.setCashiers(cashier);

        return arsp;
    }
}
