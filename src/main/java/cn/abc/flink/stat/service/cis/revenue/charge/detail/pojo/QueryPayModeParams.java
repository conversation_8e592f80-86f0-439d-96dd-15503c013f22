package cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo;

import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import lombok.EqualsAndHashCode;

import lombok.ToString;

import java.util.List;

/**
 * 支付方式修改params
 */
@EqualsAndHashCode(callSuper = true)
@ToString
public class QueryPayModeParams extends AbcScStatRequestParams {

	/**
	 * chargeSheetIds
	 */
	private List<String> chargeSheetIds;

	/**
	 * transactionIds
	 */
	private List<String> transactionIds;


    public List<String> getChargeSheetIds() {
        return this.chargeSheetIds;
    }

    public List<String> getTransactionIds() {
        return this.transactionIds;
    }


    public void setChargeSheetIds(List<String> chargeSheetIds) {
        this.chargeSheetIds = chargeSheetIds;
    }

    public void setTransactionIds(List<String> transactionIds) {
        this.transactionIds = transactionIds;
    }

}
