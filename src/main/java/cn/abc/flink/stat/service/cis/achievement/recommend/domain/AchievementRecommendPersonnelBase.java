package cn.abc.flink.stat.service.cis.achievement.recommend.domain;




/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2021-11-01 11:29
 **/
public class AchievementRecommendPersonnelBase {
	private String chainId;
	private String clinicId;
	private String clinicName;
	/**
	 * 推荐leve1
	 */
	private String sourceLevelOne;
	/**
	 * 推荐leve2
	 */
	private String sourceLevelTwo;

	/**
	 * 推荐leve3 类型1.employee，2.patient
	 */
	private Integer visitSourceFromType;
	/**
	 * 推荐leve3
	 */
	private String visitSourceFrom;
	/**
	 * 就诊推荐姓名
	 */
	private String recommendName;
	private Byte isCopywriter;
	private Long patientCount;

	/**
	 * 转诊医生id
	 */
	private String referralDoctorId;

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public String getSourceLevelOne() {
        return this.sourceLevelOne;
    }

    public String getSourceLevelTwo() {
        return this.sourceLevelTwo;
    }

    public Integer getVisitSourceFromType() {
        return this.visitSourceFromType;
    }

    public String getVisitSourceFrom() {
        return this.visitSourceFrom;
    }

    public String getRecommendName() {
        return this.recommendName;
    }

    public Byte getIsCopywriter() {
        return this.isCopywriter;
    }

    public Long getPatientCount() {
        return this.patientCount;
    }

    public String getReferralDoctorId() {
        return this.referralDoctorId;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setSourceLevelOne(String sourceLevelOne) {
        this.sourceLevelOne = sourceLevelOne;
    }

    public void setSourceLevelTwo(String sourceLevelTwo) {
        this.sourceLevelTwo = sourceLevelTwo;
    }

    public void setVisitSourceFromType(Integer visitSourceFromType) {
        this.visitSourceFromType = visitSourceFromType;
    }

    public void setVisitSourceFrom(String visitSourceFrom) {
        this.visitSourceFrom = visitSourceFrom;
    }

    public void setRecommendName(String recommendName) {
        this.recommendName = recommendName;
    }

    public void setIsCopywriter(Byte isCopywriter) {
        this.isCopywriter = isCopywriter;
    }

    public void setPatientCount(Long patientCount) {
        this.patientCount = patientCount;
    }

    public void setReferralDoctorId(String referralDoctorId) {
        this.referralDoctorId = referralDoctorId;
    }

}
