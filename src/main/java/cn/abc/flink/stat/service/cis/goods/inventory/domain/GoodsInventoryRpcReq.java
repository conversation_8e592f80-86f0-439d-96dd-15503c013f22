package cn.abc.flink.stat.service.cis.goods.inventory.domain;

import lombok.EqualsAndHashCode;


/**
 * @description: 进销存rpc请求参数实体
 * @author: lzq
 * @Date: 2022/8/10
 */
public class GoodsInventoryRpcReq {

    /**
     * 连锁id
     */
    private String chainId;

    /**
     * 门店id
     */
    private String clinicId;

    /**
     * 盘存日期只有年YYYY 例：2024
     */
    private Integer takeInventoryDate;

    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 结束时间
     */
    private String endDate;



    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public Integer getTakeInventoryDate() {
        return this.takeInventoryDate;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setTakeInventoryDate(Integer takeInventoryDate) {
        this.takeInventoryDate = takeInventoryDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

}
