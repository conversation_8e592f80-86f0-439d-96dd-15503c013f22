package cn.abc.flink.stat.service.cis.commission.domain;

import cn.abc.flink.stat.common.AbcDefaultValueUtils;
import cn.abc.flink.stat.common.ObjectUtils;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.request.params.AbcScStatFilterEmployee;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.service.cis.commission.pojos.CommissionDiff;
import cn.abc.flink.stat.service.cis.commission.pojos.CommissionFlatEmployeeAndItemDto;
import cn.abc.flink.stat.service.cis.commission.pojos.CommissionItemBo;
import cn.abc.flink.stat.service.cis.commission.pojos.DataQuerySQLPojo;
import cn.abc.flink.stat.service.cis.commission.pojos.classify.CommissionClassifySQL;
import cn.abc.flink.stat.service.cis.commission.pojos.classify.CommissionQueryClassification;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.handler.SelectHandler;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
// 导入新的参数类，用于@deprecated注解中的引用

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.commission.domain
 * @Author: zs
 * @CreateTime: 2022-08-17  15:10
 * @Description: 提成报表参数实体类
 * @Version: 1.0
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel("提成param")
public class CommissionParam extends AbcScStatRequestParams {
    private String employeeId;
    @ApiModelProperty(value = "人员信息")
    private String employees;
    private List<AbcScStatFilterEmployee> filterDoctorParams;
    private List<String> employeeList;

    private String commissionId;
    private Integer commissionType;
    private Integer offset;
    private Integer limit;
    private Boolean isAll;
    //非请求参数
    private String employeeIdsSql;
    private String dispensingEmployeeIdsSql;
    private String hisEmployeeIdsSql;
    private String snapEmployeeIdsSql;
    private String snapDispensingEmployeeIdsSql;
    private String snapHisEmployeeIdsSql;
    private String whereSQL;
    private String exceptComposeWhereSQL;
    private String hisWhereSQL;
    private String typeSql;
    private Integer isIncludeCompose;
    private String type;
    private String whereSQL1;
    private String promotionCardSql;
    private String memberSql;
    private String executeSql;

    private Integer prescriptionClassify;
    private Integer acceptClassify;
    private Integer amountClassify;

    private String memberEmployeeIdsSql;
    private String memberWhere;
    private String promotionCardEmployeeIdsSql;
    private String promotionCardWhere;
    private String dispensingWhere;

    //修改方案中的字段
    /**
     * @deprecated 建议使用新的参数类 {@link DiffCommissionUpdateParam}
     */
    @Deprecated
    private String headerEmployeeId;
    @Deprecated
    private V3Commission newCommission;
    @Deprecated
    private V3Commission oldCommission;
    @Deprecated
    private CommissionDiff.CommissionEmployeeDiff diffEmployee;
    @Deprecated
    private CommissionDiff.CommissionItemDiff diffItem;
    @Deprecated
    private V3CommissionOperation operation;
    @Deprecated
    private List<V3CommissionOperationLog> operationLogs;
    @Deprecated
    private List<CommissionItemBo> newList;
    @Deprecated
    private List<CommissionItemBo> oldList;
    @Deprecated
    private List<V3CommissionItemParticular> newModeList;
    @Deprecated
    private List<V3CommissionItemParticular> oldModeList;
    @Deprecated
    private Map<String, V3CommissionOperationLog> oldLogMap;

    /**
     * 欠费收入计提时机 1.欠费时计提 2.还款时计提
     */
    private String statConfigSql;
    /**
     * 套餐是否平摊 0.不平摊 1.平摊
     */
    private Integer isComposeShareEqually;
    /**
     * 住院费用计提时机 1.出院结算时计提 2.医嘱计费时计提
     */
    private Integer hospitalFeeCommissionTiming;

    @ApiModelProperty(value = "商品分类一级")
    private String feeType1;
    private String fee1;

    @ApiModelProperty(value = "商品分类二级")
    private String feeType2;
    private String fee2;

    @ApiModelProperty(value = "销售单号")
    private String sellNo;

    @ApiModelProperty(value = "商品编码")
    private String shortId;

    @ApiModelProperty(value = "商品Id")
    private String goodsId;

    /**
     * 设置欠费收入计提时机
     *
     * @param dto -
     */
    public void setStatConfig(StatConfigDto dto) {
        if (dto == null) {
            return;
        }
        this.isComposeShareEqually = dto.getIsComposeShareEqually();
        this.hospitalFeeCommissionTiming = dto.getHospitalFeeCommissionTiming();
        if (dto.getArrearsCommissionTiming() != 1) {
            this.statConfigSql = "record_source_type != 1";
        }
        if (dto.getArrearsCommissionTiming() != 2) {
            this.statConfigSql = "record_source_type != 2";
        }
    }

    /**
     * 处理销售提成参数
     *
     * @param saleCommissionDtoList -
     * @param sql                   -
     * @param hisSql                -
     */
    public void setSaleParam(List<CommissionFlatEmployeeAndItemDto> saleCommissionDtoList, String sql, String hisSql) {
        Map<Integer, List<CommissionFlatEmployeeAndItemDto>> commissionModeMap = new HashMap<>();
        saleCommissionDtoList.forEach(dto -> commissionModeMap.computeIfAbsent(dto.getCommissionMode(),
                k -> new ArrayList<>()).add(dto));
        TreeSet<Integer> commissionModeSet = new TreeSet<>(commissionModeMap.keySet());
        //0表示不包含套餐
        this.isIncludeCompose = 0;
        for (CommissionFlatEmployeeAndItemDto c : saleCommissionDtoList) {
            if (c != null && c.getType() != null && "11".equals(c.getType().trim())) {
                this.isIncludeCompose = 1;
                break;
            }
        }

        this.whereSQL = sql;
        this.hisWhereSQL = hisSql;
        this.employeeIdsSql = this.dispensaryType == 0 ? setEmployeeIds(saleCommissionDtoList,
                "doctor_id", "seller_id") : setClinicIdAndEmployeeIds(saleCommissionDtoList,
                "doctor_id", "seller_id");
        this.dispensingEmployeeIdsSql = this.dispensaryType == 0 ? setDispensingEmployeeIds(saleCommissionDtoList)
                : setClinicIdAndDispensingEmployeeIds(saleCommissionDtoList);
        this.prescriptionClassify = commissionModeSet.contains(1) ? 1 : 0;
        this.acceptClassify = commissionModeSet.contains(CommonConstants.NUMBER_TWO) ? 1 : 0;
        this.amountClassify = commissionModeSet.last() >= CommonConstants.NUMBER_THREE ? 1 : 0;

        if (CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(this.hisType)) {
            if (this.hospitalFeeCommissionTiming == null || this.hospitalFeeCommissionTiming == 1) {
                this.hisEmployeeIdsSql = this.dispensaryType == 0
                        ? setEmployeeIds(saleCommissionDtoList, "release_created_by", null)
                        : setClinicIdAndEmployeeIds(saleCommissionDtoList, "release_created_by", null);
            } else if (this.hospitalFeeCommissionTiming == 2) {
                this.hisEmployeeIdsSql = this.dispensaryType == 0 ? setEmployeeIds(saleCommissionDtoList,
                        "doctor_id", null) : setClinicIdAndEmployeeIds(saleCommissionDtoList,
                        "doctor_id", null);
            }
        }
    }

    /**
     * 处理销售提成参数  带科室
     *
     * @param saleCommissionDtoList -
     * @param sql                   -
     * @param hisSql                -
     */
    public void setSaleFeeParam(List<CommissionFlatEmployeeAndItemDto> saleCommissionDtoList, String sql, String hisSql) {
        Map<Integer, List<CommissionFlatEmployeeAndItemDto>> commissionModeMap = new HashMap<>();
        saleCommissionDtoList.forEach(dto -> commissionModeMap.computeIfAbsent(dto.getCommissionMode(),
                k -> new ArrayList<>()).add(dto));
        TreeSet<Integer> commissionModeSet = new TreeSet<>(commissionModeMap.keySet());
        //0表示不包含套餐
        this.isIncludeCompose = 0;
        for (CommissionFlatEmployeeAndItemDto c : saleCommissionDtoList) {
            if (c != null && c.getType() != null && "11".equals(c.getType().trim())) {
                this.isIncludeCompose = 1;
                break;
            }
        }

        this.whereSQL = sql;
        this.hisWhereSQL = hisSql;
        this.employeeIdsSql = setDepartmentAndEmployeeIdsSql(saleCommissionDtoList, "department_id",
                "seller_department_id", "doctor_id", "seller_id");
        this.dispensingEmployeeIdsSql = setDispensingDepartmentAndEmployeeIdsSql(saleCommissionDtoList);
        this.prescriptionClassify = commissionModeSet.contains(1) ? 1 : 0;
        this.acceptClassify = commissionModeSet.contains(CommonConstants.NUMBER_TWO) ? 1 : 0;
        this.amountClassify = commissionModeSet.last() >= CommonConstants.NUMBER_THREE ? 1 : 0;

        if (CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(this.hisType)) {
            if (this.hospitalFeeCommissionTiming == null || this.hospitalFeeCommissionTiming == 1) {
                this.hisEmployeeIdsSql = setDepartmentAndEmployeeIdsSql(saleCommissionDtoList,
                        "release_department_id", null, "release_created_by", null);
            } else if (this.hospitalFeeCommissionTiming == 2) {
                this.hisEmployeeIdsSql = this.dispensaryType == 0 ? setEmployeeIds(saleCommissionDtoList,
                        "doctor_id", null) : setClinicIdAndEmployeeIds(saleCommissionDtoList,
                        "doctor_id", null);
            }
        }
    }

    /**
     * @param commissionDtoList -
     * @return -
     */
    private String setDispensingEmployeeIds(List<CommissionFlatEmployeeAndItemDto> commissionDtoList) {
        Set<String> employeeIds = commissionDtoList.stream()
                .map(CommissionFlatEmployeeAndItemDto::getEmployeeId)
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toSet());
        if (!employeeIds.isEmpty()) {
            String ids = "('" + String.join("','", employeeIds) + "')";
            return "(if(transcribe_doctor_id=doctor_id, seller_id in " + ids + ",(doctor_id in " + ids + " or seller_id in " + ids + ")))";
        }
        return null;
    }


    /**
     * @param employeeAndItemDtoList -
     * @return -
     */
    private String setClinicIdAndDispensingEmployeeIds(List<CommissionFlatEmployeeAndItemDto> employeeAndItemDtoList) {
        Map<String, Set<String>> clinicIdEmployeeMap = new HashMap<>();
        employeeAndItemDtoList.stream()
                .filter(dto -> dto.getEmployeeId() != null && !dto.getEmployeeId().trim().isEmpty()
                        && dto.getClinicId() != null && !dto.getClinicId().trim().isEmpty())
                .forEach(dto -> clinicIdEmployeeMap.computeIfAbsent(dto.getClinicId(), k -> new HashSet<>())
                        .add(dto.getEmployeeId()));

        StringBuilder sb = new StringBuilder();
        sb.append("(");
        if (!clinicIdEmployeeMap.isEmpty()) {
            clinicIdEmployeeMap.keySet().forEach(k -> {
                if (sb.length() > 1) {
                    sb.append(" or ");
                }
                String ids = "('" + String.join("','", clinicIdEmployeeMap.get(k)) + "')";
                sb.append("(" + "clinic_id" + " = '").append(k).append("' and (if(transcribe_doctor_id=doctor_id, seller_id in ")
                        .append(ids).append(",(doctor_id in ").append(ids).append(" or seller_id in ").append(ids).append("))))");
            });
        }
        sb.append(")");
        if (sb.length() == CommonConstants.NUMBER_TWO) {
            return null;
        }
        return sb.toString();
    }

    /**
     * @param commissionDtoList -
     * @param employeeId        -
     * @param sellerId          -
     * @return -
     */
    public String setEmployeeIds(List<CommissionFlatEmployeeAndItemDto> commissionDtoList,
                                 String employeeId, String sellerId) {
        Set<String> employeeIds = commissionDtoList.stream()
                .map(CommissionFlatEmployeeAndItemDto::getEmployeeId)
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toSet());
        if (!employeeIds.isEmpty()) {
            String ids = "('" + String.join("','", employeeIds) + "')";
            if (sellerId != null) {
                return "(" + employeeId + " in " + ids + " or " + sellerId + " in " + ids + ")";
            } else {
                return "(" + employeeId + " in " + ids + ")";
            }
        }
        return null;
    }

    /**
     * 带发药科室的
     *
     * @param commissionDtoList -
     * @return -
     */
    private static String setDispensingDepartmentAndEmployeeIdsSql(List<CommissionFlatEmployeeAndItemDto> commissionDtoList) {
        Map<String, Map<String, Set<String>>> map = new HashMap<>();
        commissionDtoList.forEach(x -> {
            if (StringUtils.isBlank(x.getDepartmentId())) {
                x.setDepartmentId(AbcDefaultValueUtils.DEFAULT_ID);
            }
            Map<String, Set<String>> m = map.get(x.getClinicId());
            if (m == null) {
                m = new HashMap<>();
                m.put(x.getDepartmentId(), new HashSet<>());
                map.put(x.getClinicId(), m);
            }
            Set<String> departmentSet = m.computeIfAbsent(x.getDepartmentId(), k -> new HashSet<>());
            if (StringUtils.isNotBlank(x.getEmployeeId())) {
                departmentSet.add(x.getEmployeeId());
            }
        });
        StringBuilder sb = new StringBuilder();
        sb.append("(");
        for (Map.Entry<String, Map<String, Set<String>>> clinicEntry : map.entrySet()) {
            if (sb.length() > 1) {
                sb.append(" or ");
            }
            if (StringUtils.isNotBlank(clinicEntry.getKey())) {
                sb.append(" (clinic_id = '").append(clinicEntry.getKey()).append("'");
            } else {
                sb.append(" (1=1");
            }

            if (clinicEntry.getValue() != null && !clinicEntry.getValue().isEmpty()) {
                sb.append(" and (");
            }
            int clinicStrSize = sb.length();
            for (Map.Entry<String, Set<String>> departmentEntry : clinicEntry.getValue().entrySet()) {
                if (sb.length() > clinicStrSize) {
                    sb.append(" or ");
                }
                sb.append("(");
                if (AbcDefaultValueUtils.DEFAULT_ID.equals(departmentEntry.getKey())) {
                    sb.append("((department_id is null or department_id = '') and (seller_department_id is null or seller_department_id = ''))");
                } else {
                    sb.append("(if(transcribe_doctor_id=doctor_id, seller_department_id = '").append(departmentEntry.getKey())
                            .append("', (department_id = '").append(departmentEntry.getKey()).append("' or seller_department_id = '").append(departmentEntry.getKey()).append("')))");
                }
                if (departmentEntry.getValue() != null && !departmentEntry.getValue().isEmpty()) {
                    String joinStr = String.join("','", departmentEntry.getValue());
                    sb.append(" and (if(transcribe_doctor_id=doctor_id, seller_id in ('").append(joinStr).append("'),(doctor_id in ('")
                            .append(joinStr).append("') or seller_id in ('").append(joinStr).append("'))))");
                }
                sb.append(")");
            }
            sb.append(clinicEntry.getValue() != null && !clinicEntry.getValue().isEmpty() ? "))" : ")");
        }
        sb.append(")");
        return sb.toString();
    }

    /**
     * 带科室的
     *
     * @param commissionDtoList -
     * @return -
     */
    private static String setDepartmentAndEmployeeIdsSql(List<CommissionFlatEmployeeAndItemDto> commissionDtoList,
                                                         String departmentId, String sellerDepartmentId,
                                                         String doctorId, String sellerId) {
        Map<String, Map<String, Set<String>>> map = new HashMap<>();
        commissionDtoList.forEach(x -> {
            if (StringUtils.isBlank(x.getDepartmentId())) {
                x.setDepartmentId(AbcDefaultValueUtils.DEFAULT_ID);
            }
            Map<String, Set<String>> m = map.get(x.getClinicId());
            if (m == null) {
                m = new HashMap<>();
                m.put(x.getDepartmentId(), new HashSet<>());
                map.put(x.getClinicId(), m);
            }
            Set<String> departmentSet = m.get(x.getDepartmentId());
            if (departmentSet == null) {
                departmentSet = new HashSet<>();
                m.put(x.getDepartmentId(), departmentSet);
            }
            if (StringUtils.isNotBlank(x.getEmployeeId())) {
                departmentSet.add(x.getEmployeeId());
            }
        });
        StringBuilder sb = new StringBuilder();
        sb.append("(");
        for (Map.Entry<String, Map<String, Set<String>>> clinicEntry : map.entrySet()) {
            if (sb.length() > 1) {
                sb.append(" or ");
            }
            if (StringUtils.isNotBlank(clinicEntry.getKey())) {
                sb.append(" (clinic_id = '").append(clinicEntry.getKey()).append("'");
            } else {
                sb.append(" (1=1");
            }

            if (clinicEntry.getValue() != null && !clinicEntry.getValue().isEmpty()) {
                sb.append(" and (");
            }
            int clinicStrSize = sb.length();
            for (Map.Entry<String, Set<String>> departmentEntry : clinicEntry.getValue().entrySet()) {
                if (sb.length() > clinicStrSize) {
                    sb.append(" or ");
                }
                sb.append("(");
                if (AbcDefaultValueUtils.DEFAULT_ID.equals(departmentEntry.getKey())) {
                    if (sellerDepartmentId != null) {
                        sb.append("((").append(departmentId).append(" is null or ").append(departmentId).append(" = '') and (")
                                .append(sellerDepartmentId).append(" is null or ").append(sellerDepartmentId).append(" = ''))");
                    } else {
                        sb.append("(").append(departmentId).append(" is null or ").append(departmentId).append(" = '')");
                    }
                } else {
                    if (sellerDepartmentId != null) {
                        sb.append("(").append(departmentId).append(" = '").append(departmentEntry.getKey())
                                .append("' or ").append(sellerDepartmentId).append(" = '").append(departmentEntry.getKey()).append("')");
                    } else {
                        sb.append("(").append(departmentId).append(" = '").append(departmentEntry.getKey()).append("')");
                    }
                }
                if (departmentEntry.getValue() != null && !departmentEntry.getValue().isEmpty()) {
                    String joinStr = String.join("','", departmentEntry.getValue());
                    if (sellerId != null) {
                        sb.append(" and (").append(doctorId).append(" in ('").append(joinStr).append("') or ")
                                .append(sellerId).append(" in ('").append(joinStr).append("'))");
                    } else {
                        sb.append(" and (").append(doctorId).append(" in ('").append(joinStr).append("'))");
                    }
                }
                sb.append(")");
            }
            sb.append(clinicEntry.getValue() != null && !clinicEntry.getValue().isEmpty() ? "))" : ")");
        }
        sb.append(")");
        return sb.toString();
    }

    /**
     * @param employeeAndItemDtoList -
     * @param employeeId             -
     * @param sellerId               -
     * @return -
     */
    public String setClinicIdAndEmployeeIds(List<CommissionFlatEmployeeAndItemDto> employeeAndItemDtoList,
                                            String employeeId, String sellerId) {
        Map<String, Set<String>> clinicIdEmployeeMap = new HashMap<>();
        employeeAndItemDtoList.stream()
                .filter(dto -> dto.getEmployeeId() != null && !dto.getEmployeeId().trim().isEmpty()
                        && dto.getClinicId() != null && !dto.getClinicId().trim().isEmpty())
                .forEach(dto -> clinicIdEmployeeMap.computeIfAbsent(dto.getClinicId(), k -> new HashSet<>())
                        .add(dto.getEmployeeId()));

        StringBuilder sb = new StringBuilder();
        sb.append("(");
        if (!clinicIdEmployeeMap.isEmpty()) {
            clinicIdEmployeeMap.keySet().forEach(k -> {
                if (sb.length() > 1) {
                    sb.append(" or ");
                }
                if (sellerId != null) {
                    sb.append("(" + "clinic_id" + " = '").append(k).append("' and (").append(employeeId).append(" in ('")
                            .append(String.join("','", clinicIdEmployeeMap.get(k))).append("') or ")
                            .append(sellerId).append(" in ('")
                            .append(String.join("','", clinicIdEmployeeMap.get(k))).append("'))");
                } else {
                    sb.append("(" + "clinic_id" + " = '").append(k).append("' and ").append(employeeId).append(" in ('")
                            .append(String.join("','", clinicIdEmployeeMap.get(k))).append("')");
                }
                sb.append(")");
            });
        }
        sb.append(")");
        if (sb.length() == CommonConstants.NUMBER_TWO) {
            return null;
        }
        return sb.toString();
    }

    /**
     * 处理销售提成例外参数
     *
     * @param allExceptGoodsIdMap      -
     * @param commissionClassifySQLMap -
     */
    public void setSaleExceptParam(Map<String, Set<String>> allExceptGoodsIdMap,
                                   Map<CommissionQueryClassification, CommissionClassifySQL> commissionClassifySQLMap,
                                   String type) {
        List<CommissionFlatEmployeeAndItemDto> itemDtos = new ArrayList<>();
        for (CommissionClassifySQL classifySQL : commissionClassifySQLMap.values()) {
            itemDtos.addAll(classifySQL.getCommissionList());
        }
        this.whereSQL = assembleExceptSql(allExceptGoodsIdMap, "product_id", "product_id");
        this.exceptComposeWhereSQL = assembleExceptSql(allExceptGoodsIdMap, "compose_parent_product_id",
                "compose_parent_product_id");
        this.employeeIdsSql = this.dispensaryType == 0 ? setEmployeeIds(itemDtos,
                "doctor_id", "seller_id") : setClinicIdAndEmployeeIds(itemDtos,
                "doctor_id", "seller_id");
        this.dispensingEmployeeIdsSql = this.dispensaryType == 0 ? setDispensingEmployeeIds(itemDtos)
                : setClinicIdAndDispensingEmployeeIds(itemDtos);
        if (CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(this.hisType)) {
            if (this.hospitalFeeCommissionTiming == null || this.hospitalFeeCommissionTiming == 1) {
                this.hisEmployeeIdsSql = this.dispensaryType == 0
                        ? setEmployeeIds(itemDtos, "release_created_by", null)
                        : setClinicIdAndEmployeeIds(itemDtos, "release_created_by", null);
                if ("goods".equals(type)) {
                    this.hisWhereSQL = assembleExceptSql(allExceptGoodsIdMap, "product_id", "advice_goods_id");
                } else if ("fee".equals(type)) {
                    this.hisWhereSQL = assembleExceptSql(allExceptGoodsIdMap, "product_id", "product_id");
                }
            } else if (this.hospitalFeeCommissionTiming == 2) {
                this.hisEmployeeIdsSql = this.dispensaryType == 0 ? setEmployeeIds(itemDtos,
                        "doctor_id", null) : setClinicIdAndEmployeeIds(itemDtos,
                        "doctor_id", null);
                if ("goods".equals(type)) {
                    this.hisWhereSQL = assembleExceptSql(allExceptGoodsIdMap, "goods_id", "advice_goods_id");
                } else if ("fee".equals(type)) {
                    this.hisWhereSQL = assembleExceptSql(allExceptGoodsIdMap, "goods_id", "goods_id");
                }
            }
        }
    }

    /**
     * 处理销售提成例外参数  带科室  只用于医院
     *
     * @param allExceptGoodsIdMap      -
     * @param commissionClassifySQLMap -
     */
    public void setSaleFeeExceptParam(Map<String, Set<String>> allExceptGoodsIdMap,
                                      Map<CommissionQueryClassification, CommissionClassifySQL> commissionClassifySQLMap,
                                      String type) {
        List<CommissionFlatEmployeeAndItemDto> itemDtos = new ArrayList<>();
        for (CommissionClassifySQL classifySQL : commissionClassifySQLMap.values()) {
            itemDtos.addAll(classifySQL.getCommissionList());
        }
        this.whereSQL = assembleExceptSql(allExceptGoodsIdMap, "product_id", "product_id");
        this.employeeIdsSql = setDepartmentAndEmployeeIdsSql(itemDtos, "department_id",
                "seller_department_id", "doctor_id", "seller_id");
        if (this.hospitalFeeCommissionTiming == null || this.hospitalFeeCommissionTiming == 1) {
                this.hisWhereSQL = assembleExceptSql(allExceptGoodsIdMap, "product_id", "product_id");
        } else if (this.hospitalFeeCommissionTiming == 2) {
                this.hisWhereSQL = assembleExceptSql(allExceptGoodsIdMap, "goods_id", "goods_id");
        }
    }

    /**
     * 例外goods拼接sql
     *
     * @param allExceptGoodsIdsMap -
     * @param field1               -
     * @param field2               -
     * @return -
     */
    private String assembleExceptSql(Map<String, Set<String>> allExceptGoodsIdsMap, String field1, String field2) {
        if (allExceptGoodsIdsMap.size() == 0) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        builder.append("(");
        for (Map.Entry<String, Set<String>> entry : allExceptGoodsIdsMap.entrySet()) {
            String key = entry.getKey();
            Set<String> exceptIds = entry.getValue();
            if (builder.length() > 1) {
                builder.append(" or ");
            }
            if ("special".equals(key)) {
                builder.append(" ( " + field1 + " in ('" + String.join("','", exceptIds) + "'))");
            }
            if ("common".equals(key)) {
                builder.append(" ( " + field2 + " in ('" + String.join("','", exceptIds) + "'))");
            }
        }
        builder.append(")");
        return builder.toString();
    }

    public void initData(StatConfigDto dto) {
        setStatConfig(dto);
        initDs();
        if (!StrUtil.isBlank(this.employees)) {
            this.filterDoctorParams = ObjectUtils.jsonStrList2Object(this.employees, AbcScStatFilterEmployee.class);
            this.employeeList = this.filterDoctorParams.stream().map(AbcScStatFilterEmployee::getId).collect(Collectors.toList());
        }
        this.fee1 = SelectHandler.buildChargeFeeClassify1Sql(feeType1);
        this.fee2 = SelectHandler.buildChargeFeeClassify2Sql(feeType2);
    }

    public String getEmployeeId() {
        return this.employeeId;
    }

    public String getEmployees() {
        return this.employees;
    }

    public List<AbcScStatFilterEmployee> getFilterDoctorParams() {
        return this.filterDoctorParams;
    }

    public String getCommissionId() {
        return this.commissionId;
    }

    public Integer getCommissionType() {
        return this.commissionType;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public Integer getLimit() {
        return this.limit;
    }

    public Boolean getIsAll() {
        return this.isAll;
    }

    public String getEmployeeIdsSql() {
        return this.employeeIdsSql;
    }

    public String getDispensingEmployeeIdsSql() {
        return this.dispensingEmployeeIdsSql;
    }

    public String getHisEmployeeIdsSql() {
        return this.hisEmployeeIdsSql;
    }

    public String getSnapEmployeeIdsSql() {
        return this.snapEmployeeIdsSql;
    }

    public String getSnapDispensingEmployeeIdsSql() {
        return this.snapDispensingEmployeeIdsSql;
    }

    public String getSnapHisEmployeeIdsSql() {
        return this.snapHisEmployeeIdsSql;
    }

    public String getWhereSQL() {
        return this.whereSQL;
    }

    public String getExceptComposeWhereSQL() {
        return this.exceptComposeWhereSQL;
    }

    public String getHisWhereSQL() {
        return this.hisWhereSQL;
    }

    public String getTypeSql() {
        return this.typeSql;
    }

    public Integer getIsIncludeCompose() {
        return this.isIncludeCompose;
    }

    public String getType() {
        return this.type;
    }

    public String getWhereSQL1() {
        return this.whereSQL1;
    }

    public String getPromotionCardSql() {
        return this.promotionCardSql;
    }

    public String getMemberSql() {
        return this.memberSql;
    }

    public String getExecuteSql() {
        return this.executeSql;
    }

    public Integer getPrescriptionClassify() {
        return this.prescriptionClassify;
    }

    public Integer getAcceptClassify() {
        return this.acceptClassify;
    }

    public Integer getAmountClassify() {
        return this.amountClassify;
    }

    public String getMemberEmployeeIdsSql() {
        return this.memberEmployeeIdsSql;
    }

    public String getMemberWhere() {
        return this.memberWhere;
    }

    public String getPromotionCardEmployeeIdsSql() {
        return this.promotionCardEmployeeIdsSql;
    }

    public String getPromotionCardWhere() {
        return this.promotionCardWhere;
    }

    public String getDispensingWhere() {
        return this.dispensingWhere;
    }

    public String getHeaderEmployeeId() {
        return this.headerEmployeeId;
    }

    public V3Commission getNewCommission() {
        return this.newCommission;
    }

    public V3Commission getOldCommission() {
        return this.oldCommission;
    }

    public V3CommissionOperation getOperation() {
        return this.operation;
    }

    public List<V3CommissionOperationLog> getOperationLogs() {
        return this.operationLogs;
    }

    public List<CommissionItemBo> getNewList() {
        return this.newList;
    }

    public List<CommissionItemBo> getOldList() {
        return this.oldList;
    }

    public List<V3CommissionItemParticular> getNewModeList() {
        return this.newModeList;
    }

    public List<V3CommissionItemParticular> getOldModeList() {
        return this.oldModeList;
    }

    public Map<String, V3CommissionOperationLog> getOldLogMap() {
        return this.oldLogMap;
    }

    public String getStatConfigSql() {
        return this.statConfigSql;
    }

    public Integer getIsComposeShareEqually() {
        return this.isComposeShareEqually;
    }

    public Integer getHospitalFeeCommissionTiming() {
        return this.hospitalFeeCommissionTiming;
    }


    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public void setEmployees(String employees) {
        this.employees = employees;
    }

    public void setFilterDoctorParams(List<AbcScStatFilterEmployee> filterDoctorParams) {
        this.filterDoctorParams = filterDoctorParams;
    }

    public void setCommissionId(String commissionId) {
        this.commissionId = commissionId;
    }

    public void setCommissionType(Integer commissionType) {
        this.commissionType = commissionType;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setIsAll(Boolean isAll) {
        this.isAll = isAll;
    }

    public void setEmployeeIdsSql(String employeeIdsSql) {
        this.employeeIdsSql = employeeIdsSql;
    }

    public void setDispensingEmployeeIdsSql(String dispensingEmployeeIdsSql) {
        this.dispensingEmployeeIdsSql = dispensingEmployeeIdsSql;
    }

    public void setHisEmployeeIdsSql(String hisEmployeeIdsSql) {
        this.hisEmployeeIdsSql = hisEmployeeIdsSql;
    }

    public void setSnapEmployeeIdsSql(String snapEmployeeIdsSql) {
        this.snapEmployeeIdsSql = snapEmployeeIdsSql;
    }

    public void setSnapDispensingEmployeeIdsSql(String snapDispensingEmployeeIdsSql) {
        this.snapDispensingEmployeeIdsSql = snapDispensingEmployeeIdsSql;
    }

    public void setSnapHisEmployeeIdsSql(String snapHisEmployeeIdsSql) {
        this.snapHisEmployeeIdsSql = snapHisEmployeeIdsSql;
    }

    public void setWhereSQL(String whereSQL) {
        this.whereSQL = whereSQL;
    }

    public void setExceptComposeWhereSQL(String exceptComposeWhereSQL) {
        this.exceptComposeWhereSQL = exceptComposeWhereSQL;
    }

    public void setHisWhereSQL(String hisWhereSQL) {
        this.hisWhereSQL = hisWhereSQL;
    }

    public void setTypeSql(String typeSql) {
        this.typeSql = typeSql;
    }

    public void setIsIncludeCompose(Integer isIncludeCompose) {
        this.isIncludeCompose = isIncludeCompose;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setWhereSQL1(String whereSQL1) {
        this.whereSQL1 = whereSQL1;
    }

    public void setPromotionCardSql(String promotionCardSql) {
        this.promotionCardSql = promotionCardSql;
    }

    public void setMemberSql(String memberSql) {
        this.memberSql = memberSql;
    }

    public void setExecuteSql(String executeSql) {
        this.executeSql = executeSql;
    }

    public void setPrescriptionClassify(Integer prescriptionClassify) {
        this.prescriptionClassify = prescriptionClassify;
    }

    public void setAcceptClassify(Integer acceptClassify) {
        this.acceptClassify = acceptClassify;
    }

    public void setAmountClassify(Integer amountClassify) {
        this.amountClassify = amountClassify;
    }

    public void setMemberEmployeeIdsSql(String memberEmployeeIdsSql) {
        this.memberEmployeeIdsSql = memberEmployeeIdsSql;
    }

    public void setMemberWhere(String memberWhere) {
        this.memberWhere = memberWhere;
    }

    public void setPromotionCardEmployeeIdsSql(String promotionCardEmployeeIdsSql) {
        this.promotionCardEmployeeIdsSql = promotionCardEmployeeIdsSql;
    }

    public void setPromotionCardWhere(String promotionCardWhere) {
        this.promotionCardWhere = promotionCardWhere;
    }

    public void setDispensingWhere(String dispensingWhere) {
        this.dispensingWhere = dispensingWhere;
    }

    public void setHeaderEmployeeId(String headerEmployeeId) {
        this.headerEmployeeId = headerEmployeeId;
    }

    public void setNewCommission(V3Commission newCommission) {
        this.newCommission = newCommission;
    }

    public void setOldCommission(V3Commission oldCommission) {
        this.oldCommission = oldCommission;
    }

    public void setOperation(V3CommissionOperation operation) {
        this.operation = operation;
    }

    public void setOperationLogs(List<V3CommissionOperationLog> operationLogs) {
        this.operationLogs = operationLogs;
    }

    public void setNewList(List<CommissionItemBo> newList) {
        this.newList = newList;
    }

    public void setOldList(List<CommissionItemBo> oldList) {
        this.oldList = oldList;
    }

    public void setNewModeList(List<V3CommissionItemParticular> newModeList) {
        this.newModeList = newModeList;
    }

    public void setOldModeList(List<V3CommissionItemParticular> oldModeList) {
        this.oldModeList = oldModeList;
    }

    public void setOldLogMap(Map<String, V3CommissionOperationLog> oldLogMap) {
        this.oldLogMap = oldLogMap;
    }

    public void setStatConfigSql(String statConfigSql) {
        this.statConfigSql = statConfigSql;
    }

    public void setIsComposeShareEqually(Integer isComposeShareEqually) {
        this.isComposeShareEqually = isComposeShareEqually;
    }

    public void setHospitalFeeCommissionTiming(Integer hospitalFeeCommissionTiming) {
        this.hospitalFeeCommissionTiming = hospitalFeeCommissionTiming;
    }

    public Boolean getAll() {
        return isAll;
    }

    public void setAll(Boolean all) {
        isAll = all;
    }

    public CommissionDiff.CommissionEmployeeDiff getDiffEmployee() {
        return diffEmployee;
    }

    public void setDiffEmployee(CommissionDiff.CommissionEmployeeDiff diffEmployee) {
        this.diffEmployee = diffEmployee;
    }

    public CommissionDiff.CommissionItemDiff getDiffItem() {
        return diffItem;
    }

    public void setDiffItem(CommissionDiff.CommissionItemDiff diffItem) {
        this.diffItem = diffItem;
    }

    public String getFeeType1() {
        return feeType1;
    }

    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public String getFeeType2() {
        return feeType2;
    }

    public void setFeeType2(String feeType2) {
        this.feeType2 = feeType2;
    }

    public String getSellNo() {
        return sellNo;
    }

    public void setSellNo(String sellNo) {
        this.sellNo = sellNo;
    }

    public String getShortId() {
        return shortId;
    }

    public void setShortId(String shortId) {
        this.shortId = shortId;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getFee1() {
        return fee1;
    }

    public void setFee1(String fee1) {
        this.fee1 = fee1;
    }

    public String getFee2() {
        return fee2;
    }

    public void setFee2(String fee2) {
        this.fee2 = fee2;
    }

    public List<String> getEmployeeList() {
        return employeeList;
    }

    public void setEmployeeList(List<String> employeeList) {
        this.employeeList = employeeList;
    }
}
