package cn.abc.flink.stat.service.his.pe.domain;

import io.swagger.annotations.ApiModelProperty;


/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/28 16:50
 */
public class HisPeOrderSheetStatusResp {
    @ApiModelProperty(value = "普通个检-预约人次")
    private Integer generalPersonalExamBusinessCount = 0;

    @ApiModelProperty(value = "普通个检-完检人次")
    private Integer generalPersonalExamCheckedCount = 0;

    @ApiModelProperty(value = "普通个检-开单人次")
    private Integer generalPersonalExamOrderCount = 0;

    @ApiModelProperty(value = "公卫个检-预约人次")
    private Integer phsPersonalExamBusinessCount = 0;

    @ApiModelProperty(value = "公卫个检-完检人次")
    private Integer phsPersonalExamCheckedCount = 0;

    @ApiModelProperty(value = "公卫个检-开单人次")
    private Integer phsPersonalExamOrderCount = 0;

    @ApiModelProperty(value = "普通团检-预约人次")
    private Integer generalTeamExamBusinessCount = 0;

    @ApiModelProperty(value = "普通团检-完检人次")
    private Integer generalTeamExamCheckedCount = 0;

    @ApiModelProperty(value = "普通团检-开单人次")
    private Integer generalTeamExamOrderCount = 0;

    @ApiModelProperty(value = "公卫团检-预约人次")
    private Integer phsTeamExamBusinessCount = 0;

    @ApiModelProperty(value = "公卫团检-完检人次")
    private Integer phsTeamExamCheckedCount = 0;

    @ApiModelProperty(value = "公卫团检-开单人次")
    private Integer phsTeamExamOrderCount = 0;

    public void set(HisPeOrderSheetDAO dao) {
        if (dao.getType() == 0 && dao.getBusinessType() == 0 && dao.getOrderStatus() == 1) {
            this.generalPersonalExamBusinessCount = dao.getCount();
        } else if (dao.getType() == 0 && dao.getBusinessType() == 0 && dao.getOrderStatus() == 2) {
            this.generalPersonalExamCheckedCount = dao.getCount();
        } else if (dao.getType() == 0 && dao.getBusinessType() == 0 && dao.getOrderStatus() == 3) {
            this.generalPersonalExamOrderCount = dao.getCount();
        } else if (dao.getType() == 0 && dao.getBusinessType() == 10 && dao.getOrderStatus() == 1) {
            this.phsPersonalExamBusinessCount = dao.getCount();
        } else if (dao.getType() == 0 && dao.getBusinessType() == 10 && dao.getOrderStatus() == 2) {
            this.phsPersonalExamCheckedCount = dao.getCount();
        } else if (dao.getType() == 0 && dao.getBusinessType() == 10 && dao.getOrderStatus() == 3) {
            this.phsPersonalExamOrderCount = dao.getCount();
        } else if (dao.getType() == 10 && dao.getBusinessType() == 0 && dao.getOrderStatus() == 1) {
            this.generalTeamExamBusinessCount = dao.getCount();
        } else if (dao.getType() == 10 && dao.getBusinessType() == 0 && dao.getOrderStatus() == 2) {
            this.generalTeamExamCheckedCount = dao.getCount();
        } else if (dao.getType() == 10 && dao.getBusinessType() == 0 && dao.getOrderStatus() == 3) {
            this.generalTeamExamOrderCount = dao.getCount();
        } else if (dao.getType() == 10 && dao.getBusinessType() == 10 && dao.getOrderStatus() == 1) {
            this.phsTeamExamBusinessCount = dao.getCount();
        } else if (dao.getType() == 10 && dao.getBusinessType() == 10 && dao.getOrderStatus() == 2) {
            this.phsTeamExamCheckedCount = dao.getCount();
        } else if (dao.getType() == 10 && dao.getBusinessType() == 10 && dao.getOrderStatus() == 3) {
            this.phsTeamExamOrderCount = dao.getCount();
        }
    }

    public Integer getGeneralPersonalExamBusinessCount() {
        return generalPersonalExamBusinessCount;
    }

    public void setGeneralPersonalExamBusinessCount(Integer generalPersonalExamBusinessCount) {
        this.generalPersonalExamBusinessCount = generalPersonalExamBusinessCount;
    }

    public Integer getGeneralPersonalExamCheckedCount() {
        return generalPersonalExamCheckedCount;
    }

    public void setGeneralPersonalExamCheckedCount(Integer generalPersonalExamCheckedCount) {
        this.generalPersonalExamCheckedCount = generalPersonalExamCheckedCount;
    }

    public Integer getGeneralPersonalExamOrderCount() {
        return generalPersonalExamOrderCount;
    }

    public void setGeneralPersonalExamOrderCount(Integer generalPersonalExamOrderCount) {
        this.generalPersonalExamOrderCount = generalPersonalExamOrderCount;
    }

    public Integer getPhsPersonalExamBusinessCount() {
        return phsPersonalExamBusinessCount;
    }

    public void setPhsPersonalExamBusinessCount(Integer phsPersonalExamBusinessCount) {
        this.phsPersonalExamBusinessCount = phsPersonalExamBusinessCount;
    }

    public Integer getPhsPersonalExamCheckedCount() {
        return phsPersonalExamCheckedCount;
    }

    public void setPhsPersonalExamCheckedCount(Integer phsPersonalExamCheckedCount) {
        this.phsPersonalExamCheckedCount = phsPersonalExamCheckedCount;
    }

    public Integer getPhsPersonalExamOrderCount() {
        return phsPersonalExamOrderCount;
    }

    public void setPhsPersonalExamOrderCount(Integer phsPersonalExamOrderCount) {
        this.phsPersonalExamOrderCount = phsPersonalExamOrderCount;
    }

    public Integer getGeneralTeamExamBusinessCount() {
        return generalTeamExamBusinessCount;
    }

    public void setGeneralTeamExamBusinessCount(Integer generalTeamExamBusinessCount) {
        this.generalTeamExamBusinessCount = generalTeamExamBusinessCount;
    }

    public Integer getGeneralTeamExamCheckedCount() {
        return generalTeamExamCheckedCount;
    }

    public void setGeneralTeamExamCheckedCount(Integer generalTeamExamCheckedCount) {
        this.generalTeamExamCheckedCount = generalTeamExamCheckedCount;
    }

    public Integer getGeneralTeamExamOrderCount() {
        return generalTeamExamOrderCount;
    }

    public void setGeneralTeamExamOrderCount(Integer generalTeamExamOrderCount) {
        this.generalTeamExamOrderCount = generalTeamExamOrderCount;
    }

    public Integer getPhsTeamExamBusinessCount() {
        return phsTeamExamBusinessCount;
    }

    public void setPhsTeamExamBusinessCount(Integer phsTeamExamBusinessCount) {
        this.phsTeamExamBusinessCount = phsTeamExamBusinessCount;
    }

    public Integer getPhsTeamExamCheckedCount() {
        return phsTeamExamCheckedCount;
    }

    public void setPhsTeamExamCheckedCount(Integer phsTeamExamCheckedCount) {
        this.phsTeamExamCheckedCount = phsTeamExamCheckedCount;
    }

    public Integer getPhsTeamExamOrderCount() {
        return phsTeamExamOrderCount;
    }

    public void setPhsTeamExamOrderCount(Integer phsTeamExamOrderCount) {
        this.phsTeamExamOrderCount = phsTeamExamOrderCount;
    }
}
