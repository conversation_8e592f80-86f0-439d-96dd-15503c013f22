package cn.abc.flink.stat.service.his.achievement.cost.impl;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.service.his.achievement.cost.IHisRevenuesCostSevice;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostReqParams;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @CreateTime: 2024-08-15  10:40
 * @Description: 医院收费日报导出
 * @Version: 1.0
 */
@Service
public class HisRevenuesCostChargeDailyAsyncExport implements BaseAsyncExportInterface {

    @Resource
    private IHisRevenuesCostSevice hisRevenuesCostSevice;

    @Override
    public String getKey() {
        return "revenue-cost-charge-daily";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String name = (String) MapUtils.isExistsAndReturn(params, "name");
        return name + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        // 参数构造
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String chargeId = (String) MapUtils.isExistsAndReturn(params, "chargeId");
        Object cashierIds = MapUtils.isExistsAndReturn(params, "cashierIds");
        String departmentId = (String) MapUtils.isExistsAndReturn(params, "departmentId");
        List<String> components = (List<String>) MapUtils.isExistsAndReturn(params, "components");
        List<?> businessScopeRaw = (List<?>) MapUtils.isExistsAndReturn(params, "businessScope");
        List<Integer> businessScope = businessScopeRaw == null ? null : businessScopeRaw.stream()
                .map(item -> item instanceof Double ? ((Double) item).intValue() : (Integer) item)
                .collect(Collectors.toList());
        String name = (String) MapUtils.isExistsAndReturn(params, "name");
        Integer type = params.get("type") == null ? null : Double.valueOf((double) params.get("type")).intValue();
        String hisType = (String) MapUtils.isExistsAndReturn(params, "headerHisType");
        Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String chainViewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        String headerClinicId = (String) MapUtils.isExistsAndReturn(params, "headerClinicId");

        HisRevenuesCostReqParams reqParams = new HisRevenuesCostReqParams();
        AbcCisBaseQueryParams abcCisBaseQueryParams = new AbcCisBaseQueryParams();
        abcCisBaseQueryParams.setViewModeInteger(Integer.valueOf(chainViewMode));
        abcCisBaseQueryParams.setNodeType(clinicNodeType);
        reqParams.setParams(abcCisBaseQueryParams);
        reqParams.setChainId(chainId);
        reqParams.setClinicId(clinicId);
        reqParams.setHeaderClinicId(headerClinicId);
        reqParams.setBeginDate(beginDate);
        reqParams.setEndDate(endDate);
        reqParams.setChargeId(chargeId);
        reqParams.setCashierIds(cashierIds == null ? null : cashierIds.toString());
        reqParams.setDepartmentId(departmentId);
        reqParams.setType(type);
        reqParams.setHisType(hisType);
        reqParams.setName(name);
        if (components != null) {
            reqParams.setComponents(components);
        }
        reqParams.setBusinessScope(businessScope);

        reqParams.setSingleStore(chainViewMode, clinicNodeType.toString());
        List<ExcelUtils.AbcExcelSheet> sheets = hisRevenuesCostSevice.exportRevenueCostChargeDaily(reqParams);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtils.exportCustomizeStyle(baos, sheets);
        return baos;
    }

}
