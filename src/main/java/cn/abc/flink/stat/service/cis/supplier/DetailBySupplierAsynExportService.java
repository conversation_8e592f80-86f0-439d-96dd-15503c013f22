package cn.abc.flink.stat.service.cis.supplier;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.SupplierParam;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-03-21 15:07
 **/
@Component
public class DetailBySupplierAsynExportService implements BaseAsyncExportInterface {

    private static final Logger LOGGER = LoggerFactory.getLogger(DetailBySupplierAsynExportService.class);
    @Autowired
    private SupplierService service;

    @Override
    public String getKey() {
        return "supplier-detail";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String fileName = "供应商明细" + beginDate + "_" + endDate + ".xlsx";
        return fileName;
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        LOGGER.info("供应商明细异步导出key：supplier-detail  参数:{}", JSON.toJSONString(params));
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String supplierId = (String) MapUtils.isExistsAndReturn(params, "supplierId");
        String fee1 = (String) MapUtils.isExistsAndReturn(params, "fee1");
        String fee2 = (String) MapUtils.isExistsAndReturn(params, "fee2");
        Integer pharmacyType = ((Double) MapUtils.isExistsAndReturn(params, "pharmacyType")).intValue();
        String employeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String chainViewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        SupplierParam param = new SupplierParam();
        param.initAbcCisBaseQueryParams(chainId, clinicId);
        param.initBeginDateAndEndDate(beginDate, endDate);
        param.getParams().setEmployeeId(employeeId);
        param.setPharmacyType(pharmacyType);
        param.getParams().setEmployeeId(employeeId);
        param.getParams().setNodeType(clinicNodeType);
        param.setSupplierId(supplierId);
        param.setFee1(fee1);
        param.setFee2(fee2);
        param.setFinalFeeType();
        if (chainViewMode != null) {
            param.getParams().setViewMode(chainViewMode);
        }
        param.initDs();
        List<ExcelUtils.AbcExcelSheet> sheets = service.asynExportDetailBySupplier(param);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
