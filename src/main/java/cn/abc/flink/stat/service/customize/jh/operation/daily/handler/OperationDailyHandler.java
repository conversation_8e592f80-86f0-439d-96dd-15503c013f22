package cn.abc.flink.stat.service.customize.jh.operation.daily.handler;

import cn.abc.flink.stat.common.CaseUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.contants.CommonConstants;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/23 09:12
 */
public class OperationDailyHandler {
    /**
     * @param
     * @param chainId   -
     * @param beginDate -
     * @param endDate   -
     * @return
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.lang.Object>>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/23 09:12
     */
    public static Map<String, Map<String, Object>> handle(String chainId, String beginDate, String endDate) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        String begin = beginDate;
        while (TimeUtils.compareDate(begin, endDate)) {
            String dStr = TimeUtils.formatDate(begin);
            Map<String, Object> resp = new HashMap<>();
            resp.put("date", dStr);
            resp.put("chainId", chainId);
            map.put(dStr, resp);
            begin = TimeUtils.dateSubDay(begin, 1);
        }
        return map;
    }

    /**
     * @Description:
     * @param
     * @param list -
     * @param flatMap -
     * @return
     * @Author: zs
     * @Date: 2022/8/23 10:17
     */
    public static void dealNumerical(List<Map<String, Object>> list, Map<String, String> flatMap) {
        list.stream().forEach(map -> {
            map.put("patientCount",
                    Integer.parseInt(map.getOrDefault("visitPatientCount", "0").toString())
                            + Integer.parseInt(map.getOrDefault("revisitPatientCount", "0").toString())
                            + Integer.parseInt(map.getOrDefault("retailPatientCount", "0").toString())
            );
            map.put("prescriptionDropCount", "-");

            Integer visitCount = 0;
            if (map.containsKey("visitPatientCount") && map.get("visitPatientCount") != null) {
                visitCount += Integer.parseInt(map.getOrDefault("visitPatientCount", "0").toString());
            }
            if (map.containsKey("revisitPatientCount") && map.get("revisitPatientCount") != null) {
                visitCount += Integer.parseInt(map.getOrDefault("revisitPatientCount", "0").toString());
            }
            if (visitCount == 0) {
                map.put("visitPatientCountRate", "0.00%");
                map.put("revisitPatientCountRate", "0.00%");
            } else {
                BigDecimal vc = new BigDecimal(visitCount);
                map.put("visitPatientCountRate", new BigDecimal(map.getOrDefault("visitPatientCount", "0")
                        .toString()).multiply(new BigDecimal(CommonConstants.NUMBER_HUNDRED)).divide(vc,
                        CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                map.put("revisitPatientCountRate", new BigDecimal(map.getOrDefault("revisitPatientCount", "0")
                        .toString()).multiply(new BigDecimal(CommonConstants.NUMBER_HUNDRED)).divide(vc,
                        CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
            }
            calcRate(map, "slicesAmount", "totalAmount", "slicesAmountRate");
            calcRate(map, "particlesAmount", "totalAmount", "particlesAmountRate");
            calcRate(map, "seasonsTeaAmount", "totalAmount", "seasonsTeaAmountRate");
            calcRate(map, "gxAmount", "totalAmount", "gxAmountRate");
            calcRate(map, "therapyAmount", "totalAmount", "therapyAmountRate");
            calcRate(map, "retailPatientCount", "patientCount", "retailPatientCountRate");
            calcRate(map, "outpatientVisitCount", "registrationClinicCount", "outpatientVisitCountRate");
            calcRate(map, "outpatientRevisitCount", "registrationClinicCount", "outpatientRecisitCountRate");
            calcRate(map, "outpatientDropCount", "registrationClinicCount", "outpatientDropCountRate");
            calcRate(map, "depVisitCount", "registrationDepInternalDoctorCount", "depVisitCountRate");
            calcRate(map, "depRevisitCount", "registrationDepInternalDoctorCount", "depRecisitCountRate");
            calcRate(map, "depDropCount", "registrationDepInternalDoctorCount", "depDropCountRate");
            calcRate(map, "registrationClinicInternalDoctorCount", "registrationClinicCount", "registrationClinicRate");
            calcRate(map, "registrationDepInternalDoctorCount", "registrationClinicCount", "registrationDepRate");

            flatMap.forEach((key, val) -> {
                if (!map.containsKey(key)) {
                    map.put(key, 0);
                }
            });
        });
    }

    /**
     * @Description:
     * @param
     * @param map -
     * @param divisorFiled -
     * @param dividendFiled -
     * @param targetField -
     * @return
     * @Author: zs
     * @Date: 2022/8/23 10:17
     */
    private static void calcRate(Map<String, Object> map,
                                 String divisorFiled,
                                 String dividendFiled,
                                 String targetField) {
        if (map.containsKey(dividendFiled)) {
            if (map.containsKey(divisorFiled)) {
                BigDecimal totalAmount = CaseUtils.object2BigDecimal(map.get(dividendFiled));
                if (totalAmount != null && !totalAmount.equals(BigDecimal.ZERO)
                        && !totalAmount.equals(BigDecimal.ZERO.setScale(CommonConstants.NUMBER_EIGHT))) {
                    map.put(targetField, CaseUtils.object2BigDecimal(map.get(divisorFiled)).multiply(new BigDecimal(
                            CommonConstants.NUMBER_HUNDRED)).divide(totalAmount, CommonConstants.NUMBER_TWO,
                            BigDecimal.ROUND_HALF_UP).toPlainString() + "%");
                } else {
                    map.put(targetField, "0");
                }
            } else {
                map.put(targetField, "0");
            }

        } else {
            map.put(targetField, "-");
        }
    }
}
