package cn.abc.flink.stat.service.cis.central.examination.vo;

import cn.abc.flink.stat.common.utils.MathUtil;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2Goods;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.service.cis.handler.PatientHandler;


import java.math.BigDecimal;
import java.util.Map;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.central.test.vo
 * @Author: zs
 * @CreateTime: 2024-09-11  14:38
 * @Description: 中心-门店vo
 * @Version: 1.0
 */
public class CentralInspectionExaminationItemVo {
    /**
     * 合作门店
     */
    private String cooperationClinicId;
    private String cooperationClinicName;
    private String clinicId;
    private String clinicName;
    /**
     * sheetId
     */
    private String patientId;
    private String patientName;
    /**
     * 类型
     */
    private String type;
    private Long number;
    /**
     * 项目
     */
    private String goodsId;
    private String goodsName;
    /**
     * 检查/检验单号
     */
    private String no;
    /**
     * 完成时间
     */
    private String testTime;

    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * 服务费
     */
    private BigDecimal serviceCharge;
    /**
     * 毛利
     */
    private BigDecimal profit;
    /**
     * 毛利率
     */
    private String profitRate;

    public void set(Map<String, Organ> organMap, Map<String, V2Patient> v2PatientMap, Map<String, V2Goods> goodsMap) {
        Organ organ = organMap.get(this.cooperationClinicId);
        Organ clinic = organMap.get(this.clinicId);
        V2Patient v2Patient = v2PatientMap.get(this.patientId);
        V2Goods v2Goods = goodsMap.get(this.goodsId);
        if (organ != null) {
            this.cooperationClinicName = organ.getName();
        }
        if (clinic != null) {
            this.clinicName = clinic.getName();
        }
        if (v2Goods != null) {
            this.goodsName = v2Goods.getName();
        }
        if (v2Patient != null) {
           this.patientName = PatientHandler.getPatientInfo(v2Patient.getName(), v2Patient.getSex(), v2Patient.getBirthday(), "", 1);
        }
        if (this.cooperationClinicName == null) {
            this.cooperationClinicName = "-";
        }
        if (this.clinicName == null) {
            this.clinicName = "-";
        }
        if (this.goodsName == null) {
            this.goodsName = "-";
        }
        if (this.patientName == null) {
            this.patientName = "-";
        }
    }

    /**
     * 设置毛利和毛利率
     */
    public void setRate() {
        if (this.cost != null && this.serviceCharge != null) {
            this.profit = this.serviceCharge.subtract(this.cost);
            if (this.serviceCharge.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal decimal = MathUtil.calculateRatio(this.profit, this.serviceCharge, 2, new BigDecimal(100));
                this.profitRate = decimal.toString() + "%";
            }
        }
    }

    public String getCooperationClinicId() {
        return this.cooperationClinicId;
    }

    public String getCooperationClinicName() {
        return this.cooperationClinicName;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public String getType() {
        return this.type;
    }

    public Long getNumber() {
        return this.number;
    }

    public String getGoodsId() {
        return this.goodsId;
    }

    public String getGoodsName() {
        return this.goodsName;
    }

    public String getNo() {
        return this.no;
    }

    public String getTestTime() {
        return this.testTime;
    }

    public BigDecimal getCost() {
        return this.cost;
    }

    public BigDecimal getServiceCharge() {
        return this.serviceCharge;
    }

    public BigDecimal getProfit() {
        return this.profit;
    }

    public String getProfitRate() {
        return this.profitRate;
    }


    public void setCooperationClinicId(String cooperationClinicId) {
        this.cooperationClinicId = cooperationClinicId;
    }

    public void setCooperationClinicName(String cooperationClinicName) {
        this.cooperationClinicName = cooperationClinicName;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setNumber(Long number) {
        this.number = number;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public void setTestTime(String testTime) {
        this.testTime = testTime;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public void setProfit(BigDecimal profit) {
        this.profit = profit;
    }

    public void setProfitRate(String profitRate) {
        this.profitRate = profitRate;
    }

}
