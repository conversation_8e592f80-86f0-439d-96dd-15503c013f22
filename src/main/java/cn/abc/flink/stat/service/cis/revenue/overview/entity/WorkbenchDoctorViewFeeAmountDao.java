package cn.abc.flink.stat.service.cis.revenue.overview.entity;



import java.math.BigDecimal;

public class WorkbenchDoctorViewFeeAmountDao {
    private String employeeId;
    private String feeType1;
    private BigDecimal amount;

    public String getEmployeeId() {
        return this.employeeId;
    }

    public String getFeeType1() {
        return this.feeType1;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }


    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

}
