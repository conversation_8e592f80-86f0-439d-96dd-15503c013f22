package cn.abc.flink.stat.service.his.achievement.cost.impl;

import cn.abc.flink.stat.common.*;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.domain.V2HisChargeDeposit;
import cn.abc.flink.stat.common.domain.V2PatientorderHospitalExtend;
import cn.abc.flink.stat.common.model.PayMode;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.common.response.StatResponse;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.common.utils.MathUtil;
import cn.abc.flink.stat.common.utils.TableHeaderUtils;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnChargeProductMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresChargeProductMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresRevenueChareDetailMapper;
import cn.abc.flink.stat.db.dao.ChargeMapper;
import cn.abc.flink.stat.db.his.hologres.dao.IHoloHisHisRevenuesCostMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.*;
import cn.abc.flink.stat.pojo.EmployeeResp;
import cn.abc.flink.stat.pojo.KeyValuePojo;
import cn.abc.flink.stat.service.cis.config.StatConfigService;
import cn.abc.flink.stat.service.cis.config.pojo.StatComponentConfig;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.config.pojo.StatOrganTableConfig;
import cn.abc.flink.stat.service.cis.handler.ChargeHandler;
import cn.abc.flink.stat.service.cis.handler.GoodsHandler;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.RevenueChargedDailyService;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.*;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.handler.RevenueChargedDailyHandler;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.handler.HeaderHandler;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.handler.RevenueChargeDetailHandler;
import cn.abc.flink.stat.service.cis.selection.DepartmentService;
import cn.abc.flink.stat.service.cis.selection.EmployeeService;
import cn.abc.flink.stat.service.cis.selection.pojo.DepartmentResp;
import cn.abc.flink.stat.service.cis.selection.pojo.WardResp;
import cn.abc.flink.stat.service.his.achievement.charge.domain.HisAchievementChargeReqParams;
import cn.abc.flink.stat.service.his.achievement.charge.handler.HisAchievementChargeHandler;
import cn.abc.flink.stat.service.his.achievement.cost.IHisRevenuesCostSevice;
import cn.abc.flink.stat.service.his.achievement.cost.domain.*;
import cn.abc.flink.stat.service.his.achievement.cost.handler.HisHeaderHandler;
import cn.abc.flink.stat.service.his.achievement.cost.handler.HisRevenueCostChargeDailyMethodInvoke;
import cn.abc.flink.stat.service.his.achievement.cost.handler.HisRevenuesCostHandler;
import cn.abc.flink.stat.service.his.physical.examination.charge.handler.HisPeChargeDataHandler;
import cn.abc.flink.stat.service.his.revenue.product.HisRevenueOutpatientProductService;
import cn.abc.flink.stat.service.his.revenue.product.domain.HisRevenueOutpatientProductReq;
import cn.abc.flink.stat.source.AbcThreadExecutor;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.his.achievement.cost.impl
 * @Author: zs
 * @CreateTime: 2023-12-02  14:40
 * @Description: 住院结算实现类
 * @Version: 1.0
 */
@Service
public class HisRevenuesCostSeviceImpl implements IHisRevenuesCostSevice {
    private static Logger LOGGER = LoggerFactory.getLogger(HisRevenuesCostSeviceImpl.class);

    @Resource
    private IHoloHisHisRevenuesCostMapper mapper;

    @Resource
    private ChargeMapper chargeMapper;

    @Autowired
    private HisRevenuesCostHandler hisRevenuesCostHandler;

    @Autowired
    private DimensionQuery query;

    @Resource
    private AbcThreadExecutor executor;

    @Resource
    private HologresRevenueChareDetailMapper hologresRevenueChareDetailMapper;

    @Resource
    private HisRevenueOutpatientProductService outpatientProductService;

    @Resource
    private ArnChargeProductMapper arnChargeProductMapper;

    @Resource
    private HologresChargeProductMapper hologresChargeProductMapper;

    @Resource
    private StoreUtils storeUtils;

    @Resource
    private StatConfigService statConfigService;

    @Autowired
    private HisPeChargeDataHandler hisPeChargeDataHandler;

    @Autowired
    private RevenueChargedDailyHandler revenueChargedDailyHandler;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private DepartmentService departmentService;

    @Autowired
    private RevenueChargedDailyService revenueChargedDailyService;

    @Autowired
    private GoodsHandler goodsHandler;

    private static final List<Integer> ALL_SCOPES = new ArrayList<>();

    private static final List<Integer> OUTPATIENT_AND_HOPITAL_SCOPES = new ArrayList<>();

    static {
        ALL_SCOPES.add(1);
        ALL_SCOPES.add(2);
        ALL_SCOPES.add(4);

        OUTPATIENT_AND_HOPITAL_SCOPES.add(1);
        OUTPATIENT_AND_HOPITAL_SCOPES.add(2);
    }

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 住院结算统计-费用分类
     * @Author: zs
     * @Date: 2023/12/2 14:49
     */
    @Override
    public V2StatResponse selectFeeType(HisRevenuesCostReqParams params) {

        CompletableFuture<List<HisRevenuesCostFeeTypeRes>> summyF = executor.supplyAsync(() -> {
            return mapper.selectFeeTypeSummy(TableUtils.getHisTable(), params);
        });
        CompletableFuture<List<HisRevenuesCostFeeTypeRes>> feeTypeListF = executor.supplyAsync(() -> {
            return mapper.selectFeeTypes(TableUtils.getHisTable(), params);
        });
        CompletableFuture<List<HisRevenuesCostFeeTypeRes>> verticalSummyF = executor.supplyAsync(() -> {
            return mapper.selectVerticalSummy(TableUtils.getHisTable(), params);
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        CompletableFuture<Map<String, String>> departmentF = executor.supplyAsync(() -> {
            return query.queryDepartmentNameByOrgan(params.getChainId(), params.getClinicId());
        });
        //获取表头
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.REVENUE_COST_SETTLEMENT_FEE_TYPE,
                        params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0);
        CompletableFuture.allOf(summyF, feeTypeListF, verticalSummyF, feeTypeF).join();
        List<HisRevenuesCostFeeTypeRes> summy = null;
        List<HisRevenuesCostFeeTypeRes> feeTypeList = null;
        List<HisRevenuesCostFeeTypeRes> verticalSummy = null;
        Map<Long, V2GoodsFeeType> feeType = null;
        Map<String, String> department = null;
        V2StatResponse response = new V2StatResponse();
        response.setHeader(headerEmployeeItems);
        try {
            summy = summyF.get();
            feeTypeList = feeTypeListF.get();
            verticalSummy = verticalSummyF.get();
            feeType = feeTypeF.get();
            department = departmentF.get();
            if (summy == null || summy.size() == 0) {
                return response;
            }
            hisRevenuesCostHandler.feeTypePolymerization(response, headerEmployeeItems, summy, feeTypeList, verticalSummy, feeType, department);
        } catch (Exception e) {
            LOGGER.error("住院结算统计-费用分类异常：{}", e);
        }
        return response;
    }

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 结算统计-单据
     * @Author: zs
     * @Date: 2023/12/4 09:55
     */
    @Override
    public V2StatResponse selectTransaction(HisRevenuesCostReqParams params) {
        params.parameterProcessing();
        V2StatResponse response = new V2StatResponse();
        //获取表头
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.REVENUE_COST_SETTLEMENT_TRANSACTION,
                        params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0);
        response.setHeader(headerEmployeeItems);
        //查询单据数据
        CompletableFuture<List<HisRevenuesCostTransactionRes>> selectTransactionListF = CompletableFuture.supplyAsync(() -> {
            return mapper.selectTransaction(TableUtils.getHisTable(), params);
        });
        CompletableFuture<HisRevenuesCostTotalRes> totalF = selectTotal(params, 0);
        CompletableFuture.allOf(selectTransactionListF, totalF);
        List<HisRevenuesCostTransactionRes> transactionList = null;
        HisRevenuesCostTotalRes total = null;
        try {
            transactionList = selectTransactionListF.get();
            total = totalF.get();
            if (transactionList == null || transactionList.size() == 0) {
                setCountValue(response, new HisRevenuesCostTotalRes());
                return response;
            }
        } catch (Exception e) {
            LOGGER.error("结算统计-单据List:{}", e);
        }
        //设置纬度信息
        Set<String> patientIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        Set<String> empIds = new HashSet<>();
        Set<String> patientOrderIds = new HashSet<>();
        for (HisRevenuesCostTransactionRes dto : transactionList) {
            if (StringUtils.isNotBlank(dto.getPatientId())) {
                patientIds.add(dto.getPatientId());
            }
            if (StringUtils.isNotBlank(dto.getDepartmentId())) {
                departmentIds.add(dto.getDepartmentId());
            }
            if (StringUtils.isNotBlank(dto.getChargeById())) {
                empIds.add(dto.getChargeById());
            }
            if (StringUtils.isNotBlank(dto.getDoctorId())) {
                empIds.add(dto.getDoctorId());
            }
            if (StringUtils.isNotBlank(dto.getNurseId())) {
                empIds.add(dto.getNurseId());
            }
            if (StringUtils.isNotBlank(dto.getPatientOrderId())) {
                patientOrderIds.add(dto.getPatientOrderId());
            }
        }
        CompletableFuture<Map<String, Department>> departmentF = executor.supplyAsync(() -> {
            return query.queryDepartments(params.getChainId(), departmentIds);
        });
        CompletableFuture<Map<String, V2Patient>> patientF = executor.supplyAsync(() -> {
            return query.queryPatientAndNoCache(params.getChainId(), patientIds);
        });
        CompletableFuture<Map<Integer, String>> payF = executor.supplyAsync(() -> {
            return query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
        });
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() -> {
            return query.queryEmployeeByChainAndIds(params.getChainId(), empIds);
        });
        CompletableFuture<Map<String, String>> wardFuture = executor.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId());
        });
        CompletableFuture<Map<String, V2PatientorderHospitalExtend>> patientorderHospitalExtendsF = executor.supplyAsync(() -> {
            return query.queryPatientOrderHospitalExtendById(params.getChainId(), patientOrderIds);
        });
        CompletableFuture<Map<String, V2PatientSourceType>> patientSourceTypeFuture = executor.supplyAsync(() -> {
            return query.queryPatientSourceType(params.getChainId());
        });
        CompletableFuture<Map<String, V1EmrDiagnosis>> dischargeDiagnosisF = executor.supplyAsync(() -> {
            return query.queryDischargeDiagnosisByChainIdAndPatientOrderIds(params.getChainId(), patientOrderIds);
        });
        CompletableFuture<Map<String, List<String>>> patientTagF = CompletableFuture.supplyAsync(() -> {
            return query.selectPatientTagByIds(params.getChainId(), params.getIsExport() == 1 ? null:patientIds);
        });
        CompletableFuture.allOf(departmentF, patientF, payF, employeeF, wardFuture, patientorderHospitalExtendsF, patientSourceTypeFuture, dischargeDiagnosisF, patientTagF);
        try {
            Map<String, Department> departmentMap = departmentF.get();
            Map<String, V2Patient> patientMap = patientF.get();
            Map<Integer, String> payTypeMap = payF.get();
            Map<String, Employee> employeeMap = employeeF.get();
            Map<String, String> wardMap = wardFuture.get();
            Map<String, V2PatientorderHospitalExtend> patientorderHospitalExtends = patientorderHospitalExtendsF.get();
            Map<String, V2PatientSourceType> sourceTypeMap = patientSourceTypeFuture.get();
            Map<String, V1EmrDiagnosis> dischargeDiagnosisMap = dischargeDiagnosisF.get();
            Map<String, List<String>> patientTagMap = patientTagF.get();
            for (HisRevenuesCostTransactionRes dto : transactionList) {
                dto.set(departmentMap, patientMap, employeeMap, wardMap, patientorderHospitalExtends);
                dto.setPayModeStr(ChargeHandler.handlePayMode(query, dto.getPayMode(), dto.getPaySubMode(), payTypeMap));
                dto.setFirstVisitSource(sourceTypeMap, employeeMap, patientMap);
                dto.setDischargeDiagnosisMessage(dischargeDiagnosisMap);
                dto.setPatientTag(RevenueChargeDetailHandler.getPatientTag(dto.getPatientId(), patientTagMap));
            }
            setCountValue(response, total);
            response.setData(transactionList);
        } catch (Exception e) {
            LOGGER.error("结算统计-单据:{}", e);
        }
        return response;
    }

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 结算统计-项目
     * @Author: zs
     * @Date: 2023/12/5 15:24
     */
    @Override
    public V2StatResponse selectProject(HisRevenuesCostReqParams params) {
        params.parameterProcessing();
        V2StatResponse response = new V2StatResponse();
        //获取表头
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.REVENUE_COST_SETTLEMENT_PROJECT,
                        params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0);
        if (params.getBalanceDimension() == CommonConstants.NUMBER_ONE) {
            headerEmployeeItems = HeaderHandler.getRevenueChargeDetailBatchHeader(headerEmployeeItems, "收费项");
        }
        response.setHeader(headerEmployeeItems);
        if ((params.getTagIds() != null && !params.getTagIds().isEmpty()) || (params.getBaseMedicineType() != null && !params.getBaseMedicineType().isEmpty())) {
            String tagSql = goodsHandler.getGoodsSql(params.getChainId(), params.getClinicId(), "product_id", params.getTagIds(), params.getBaseMedicineType());
            if (tagSql == null) {
                return response;
            }
            params.setTagIdSql(tagSql);
        }
        //查询单据数据
        CompletableFuture<List<HisRevenuesCostProjectRes>> selectProjectListF = executor.supplyAsync(() -> {
            if (params.getBalanceDimension() == CommonConstants.NUMBER_ZERO) {
                return mapper.selectProject(TableUtils.getHisTable(), params);
            } else {
                return mapper.selectBatchProject(TableUtils.getHisTable(), params);
            }
        });
        CompletableFuture<HisRevenuesCostTotalRes> totalF = selectTotal(params, 1);
        CompletableFuture.allOf(selectProjectListF, totalF);
        List<HisRevenuesCostProjectRes> projectList = null;
        HisRevenuesCostTotalRes total = null;
        try {
            projectList = selectProjectListF.get();
            total = totalF.get();
            if (projectList == null || projectList.size() == 0) {
                setCountValue(response, new HisRevenuesCostTotalRes());
                return response;
            }
        } catch (Exception e) {
            LOGGER.error("结算统计-项目List异常：{}", e);
        }
        //设置纬度信息
        Set<String> patientIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        Set<String> empIds = new HashSet<>();
        Set<String> goodsIds = new HashSet<>();
        Set<String> patientOrderIds = new HashSet<>();
        Set<Long> adviceIds = new HashSet<>();
        Set<String> batchIds = new HashSet<>();
        for (HisRevenuesCostProjectRes dto : projectList) {
            if (StringUtils.isNotBlank(dto.getPatientId())) {
                patientIds.add(dto.getPatientId());
            }
            if (dto.getPatientSourceFrom() != null && dto.getPatientSourceFromType() != null) {
                if (dto.getPatientSourceFromType() == 2) {
                    patientIds.add(dto.getPatientSourceFrom());
                } else {
                    empIds.add(dto.getPatientSourceFrom());
                }
            }
            if (StringUtils.isNotBlank(dto.getProductId())) {
                goodsIds.add(dto.getProductId());
            }
            if (StringUtils.isNotBlank(dto.getChargeById())) {
                empIds.add(dto.getChargeById());
            }
            if (StringUtils.isNotBlank(dto.getReleaseCreatedBy())) {
                empIds.add(dto.getReleaseCreatedBy());
            }
            if (StringUtils.isNotBlank(dto.getCheckOperatorId())) {
                empIds.add(dto.getCheckOperatorId());
            }
            if (StringUtils.isNotBlank(dto.getReleaseDepartmentId())) {
                departmentIds.add(dto.getReleaseDepartmentId());
            }
            if (StringUtils.isNotBlank(dto.getCheckDepartmentId())) {
                departmentIds.add(dto.getCheckDepartmentId());
            }
            if (StringUtils.isNotBlank(dto.getPatientOrderId())) {
                patientOrderIds.add(dto.getPatientOrderId());
            }
            if (dto.getAdviceId() != null) {
                adviceIds.add(dto.getAdviceId());
            }
            if (dto.getBatchId() != null) {
                batchIds.add(dto.getBatchId());
            }
        }
        CompletableFuture<Map<String, Department>> departmentF = executor.supplyAsync(() -> {
            return query.queryDepartments(params.getChainId(), departmentIds);
        });
        CompletableFuture<Map<String, V2Patient>> patientF = executor.supplyAsync(() -> {
            return query.queryPatientAndNoCache(params.getChainId(), patientIds);
        });
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() -> {
            return query.queryEmployeeByChainAndIds(params.getChainId(), empIds);
        });
        CompletableFuture<Map<String, String>> wardFuture = executor.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId());
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        //classLevelId
        CompletableFuture<Map<Integer, V2GoodsCustomType>> goodsFeeTypesF = executor.supplyAsync(() -> {
            return query.queryProductCustomTypeTextByChainId(params.getChainId());
        });
        CompletableFuture<Map<String, V2Goods>> goodsF = executor.supplyAsync(() -> {
            return query.queryProducts(params.getChainId(), goodsIds);
        });
        CompletableFuture<Map<Long, String>> adviceF = executor.supplyAsync(() -> {
            return query.queryAdvice(params.getChainId(), params.getClinicId(), adviceIds);
        });
        CompletableFuture<Map<String, V2PatientorderHospitalExtend>> patientorderHospitalExtendsF = executor.supplyAsync(() -> {
            return query.queryPatientOrderHospitalExtendById(params.getChainId(), patientOrderIds);
        });
        CompletableFuture<Map<Long, V2GoodsStock>> goodsStockF = CompletableFuture.supplyAsync(() -> {
            return query.queryGoodsStocks(params.getChainId(), batchIds);
        });
        CompletableFuture<Map<String, List<V2GoodsTag>>> goodsTagF = CompletableFuture.supplyAsync(() -> {
            return query.queryGoodsTagByGoodsIds(params.getChainId(), params.getClinicId(), goodsIds);
        });
        CompletableFuture<Map<String, V2PatientClinic>> patientClinicF = CompletableFuture.supplyAsync(() -> {
            return query.selectPatientClinicByPatientIds(params.getChainId(), params.getClinicId(), new ArrayList<>(patientIds));
        });
        // 查询一二级名字
        CompletableFuture<Map<String, V2PatientSourceType>> patientSourceTypeMapF = CompletableFuture.supplyAsync(() -> {
            return query.queryPatientSourceType(params.getChainId());
        });
        CompletableFuture<Map<String, V1EmrDiagnosis>> dischargeDiagnosisF = executor.supplyAsync(() -> {
            return query.queryDischargeDiagnosisByChainIdAndPatientOrderIds(params.getChainId(), patientOrderIds);
        });
        CompletableFuture<Map<String, List<String>>> patientTagF = CompletableFuture.supplyAsync(() -> {
            return query.selectPatientTagByIds(params.getChainId(), params.getIsExport() == 1 ? null:patientIds);
        });
        CompletableFuture.allOf(departmentF, patientF, employeeF, wardFuture, feeTypeF, goodsFeeTypesF, goodsF, adviceF, patientorderHospitalExtendsF, goodsStockF, goodsTagF, patientSourceTypeMapF, dischargeDiagnosisF, patientTagF);
        try {
            Map<String, Department> departmentMap = departmentF.get();
            Map<String, V2Patient> patientMap = patientF.get();
            Map<String, Employee> employeeMap = employeeF.get();
            Map<String, String> wardMap = wardFuture.get();
            Map<Long, V2GoodsFeeType> feeType = feeTypeF.get();
            Map<String, V2Goods> goods = goodsF.get();
            Map<Integer, V2GoodsCustomType> goodsFeeTypes = goodsFeeTypesF.get();
            Map<Long, String> advice = adviceF.get();
            Map<String, V2PatientorderHospitalExtend> patientorderHospitalExtends = patientorderHospitalExtendsF.get();
            Map<Long, V2GoodsStock> goodsStock = goodsStockF.get();
            Map<String, List<V2GoodsTag>> goodsTag = goodsTagF.get();
            Map<String, V2PatientClinic> patientClinic = patientClinicF.get();
            Map<String, V2PatientSourceType> patientSourceTypeMap = patientSourceTypeMapF.get();
            Map<String, V1EmrDiagnosis> dischargeDiagnosisMap = dischargeDiagnosisF.get();
            Map<String, List<String>> patientTagMap = patientTagF.get();
            for (HisRevenuesCostProjectRes dto : projectList) {
                dto.set(departmentMap, patientMap, employeeMap, wardMap, feeType, goods, advice, patientorderHospitalExtends, patientClinic);
                dto.setGoodsStock(goodsStock);
                dto.setGoodsTag(params, goodsTag);
                dto.setPatientSource(patientSourceTypeMap, employeeMap, patientMap);
                dto.setFee(query, goodsFeeTypes, params.getChainId(), params.getHisType());
                dto.setDischargeDiagnosisMessage(dischargeDiagnosisMap);
                dto.setPatientTag(RevenueChargeDetailHandler.getPatientTag(dto.getPatientId(), patientTagMap));
            }
            setCountValue(response, total);
            response.setData(projectList);
        } catch (Exception e) {
            LOGGER.error("结算统计-项目异常：{}", e);
        }
        return response;
    }

    /**
     * @param
     * @param params -
     * @param type   0单据 1项目
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 结算统计右下角合计
     * @Author: zs
     * @Date: 2023/12/4 17:44
     */
    public CompletableFuture<HisRevenuesCostTotalRes> selectTotal(HisRevenuesCostReqParams params, int type) {
        params.parameterProcessing();
        CompletableFuture<HisRevenuesCostTotalRes> selectTotalF = executor.supplyAsync(() -> {
            return mapper.selectTotal(TableUtils.getHisTable(), params, type);
        });
        return selectTotalF;
    }

    /**
     * @param
     * @param v2StatResponse 返回值
     * @param total          合计行
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 单据返回值处理
     * @Author: zs
     * @Date: 2023/12/5 10:27
     */
    public void setCountValue(V2StatResponse v2StatResponse,
                              HisRevenuesCostTotalRes total) {
        StatResponseTotal statResponseTotal = new StatResponseTotal();
        statResponseTotal.setCount(total.getCount());
        List<Object> totalCount = Arrays.asList(total.getCount(), total.getCharge(), total.getRefund(), total.getTotal());
        statResponseTotal.setTemplate("总条数 %s， 出院结算实收 %s， 撤销结算 %s， 合计 %s");
        statResponseTotal.setData(totalCount);
        v2StatResponse.setTotal(statResponseTotal);
    }

    /**
     * @param
     * @param v2StatResponse 返回值
     * @param total          合计行
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 计费统计-患者/项目右下角合计
     * @Author: zs
     * @Date: 2023/12/5 10:27
     */
    public void setChargeCountValue(V2StatResponse v2StatResponse,
                                    HisRevenuesCostTotalRes total) {
        StatResponseTotal statResponseTotal = new StatResponseTotal();
        statResponseTotal.setCount(total.getCount());
        List<Object> totalCount = Arrays.asList(total.getCount(), total.getTotal());
        statResponseTotal.setTemplate("共 %s 条数据, 计费金额 %s");
        statResponseTotal.setData(totalCount);
        v2StatResponse.setTotal(statResponseTotal);
    }


    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 住院结算统计-计费-费用分类
     * @Author: zs
     * @Date: 2023/12/2 14:49
     */
    @Override
    public V2StatResponse selectChargeFeeType(HisRevenuesCostReqParams params) {

        CompletableFuture<List<HisRevenuesCostFeeTypeRes>> summyF = executor.supplyAsync(() -> {
            return mapper.selectChargeFeeTypeSummy(TableUtils.getHisTable(), params);
        });
        CompletableFuture<List<HisRevenuesCostFeeTypeRes>> feeTypeListF = executor.supplyAsync(() -> {
            return mapper.selectChargeFeeTypes(TableUtils.getHisTable(), params);
        });
        CompletableFuture<List<HisRevenuesCostFeeTypeRes>> verticalSummyF = executor.supplyAsync(() -> {
            return mapper.selectChargeVerticalSummy(TableUtils.getHisTable(), params);
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        CompletableFuture<Map<String, String>> departmentF = executor.supplyAsync(() -> {
            return query.queryDepartmentNameByOrgan(params.getChainId(), params.getClinicId());
        });
        //获取表头
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.REVENUE_COST_SETTLEMENT_FEE_TYPE,
                        params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0);
        CompletableFuture.allOf(summyF, feeTypeListF, verticalSummyF, feeTypeF).join();
        List<HisRevenuesCostFeeTypeRes> summy = null;
        List<HisRevenuesCostFeeTypeRes> feeTypeList = null;
        List<HisRevenuesCostFeeTypeRes> verticalSummy = null;
        Map<Long, V2GoodsFeeType> feeType = null;
        Map<String, String> department = null;
        V2StatResponse response = new V2StatResponse();
        response.setHeader(headerEmployeeItems);
        try {
            summy = summyF.get();
            feeTypeList = feeTypeListF.get();
            verticalSummy = verticalSummyF.get();
            feeType = feeTypeF.get();
            department = departmentF.get();
            if (summy == null || summy.size() == 0) {
                return response;
            }
            hisRevenuesCostHandler.feeTypePolymerization(response, headerEmployeeItems, summy, feeTypeList, verticalSummy, feeType, department);
        } catch (Exception e) {
            LOGGER.error("住院结算统计-计费-费用分类异常：{}", e);
        }
        return response;
    }


    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 计费-患者
     * @Author: zs
     * @Date: 2023/12/4 09:55
     */
    @Override
    public V2StatResponse selectChargPatient(HisRevenuesCostReqParams params) {
        V2StatResponse response = new V2StatResponse();
        //获取表头
        List<TableHeaderEmployeeItem> headerEmployeeItems =
                query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.REVENUE_COST_CHARGE_PATIENT,
                        params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0);
        response.setHeader(headerEmployeeItems);
        //list
        List<HisRevenuesCostChargePatientRes> selectChargPatientList = mapper.selectChargPatientList(TableUtils.getHisTable(), params);
        if (selectChargPatientList == null || selectChargPatientList.size() == 0) {
            setChargeCountValue(response, new HisRevenuesCostTotalRes());
            return response;
        }
        HisRevenuesCostTotalRes total = mapper.selectChargPatientTotal(TableUtils.getHisTable(), params, 0);
        //设置纬度信息
        Set<String> patientIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        Set<String> empIds = new HashSet<>();
        Set<String> patientOrderIds = new HashSet<>();
        Map<String, V2PatientorderHospitalExtend> patientorderHospitalExtendMap = new HashMap<>();
        Map<String, V2HisChargeDeposit> v2HisChargeDepositMap = new HashMap<>();
        for (HisRevenuesCostChargePatientRes dto : selectChargPatientList) {
            if (StringUtils.isNotBlank(dto.getPatientId())) {
                patientIds.add(dto.getPatientId());
            }
            if (StringUtils.isNotBlank(dto.getDepartmentId())) {
                departmentIds.add(dto.getDepartmentId());
            }
            if (StringUtils.isNotBlank(dto.getPatientOrderId())) {
                patientOrderIds.add(dto.getPatientOrderId());
            }
        }
        //查询余额ods
        List<V2PatientorderHospitalExtend> patientorderHospitalExtends =
                chargeMapper.selectPatientorderHospitalExtendById(TableUtils.getCisPatientorderTable(), patientOrderIds, params.getChainId());
        if (patientorderHospitalExtends != null) {
            for (V2PatientorderHospitalExtend dto : patientorderHospitalExtends) {
                if (StringUtils.isNotBlank(dto.getDoctorId())) {
                    empIds.add(dto.getDoctorId());
                }
                if (StringUtils.isNotBlank(dto.getNurseId())) {
                    empIds.add(dto.getNurseId());
                }
                patientorderHospitalExtendMap.put(dto.getId(), dto);
            }
        }
        CompletableFuture<List<V2HisChargeDeposit>> v2HisChargeDepositsF = executor.supplyAsync(() -> {
            return chargeMapper.selectChargeDeposits(TableUtils.getHisChargeTable(), patientOrderIds, params.getChainId());
        });
        //查询主管医生，护士，床号
        CompletableFuture<Map<String, Department>> departmentF = executor.supplyAsync(() -> {
            return query.queryDepartments(params.getChainId(), departmentIds);
        });
        CompletableFuture<Map<String, V2Patient>> patientF = executor.supplyAsync(() -> {
            return query.queryPatientAndNoCache(params.getChainId(), patientIds);
        });
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() -> {
            return query.queryEmployeeByChainAndIds(params.getChainId(), empIds);
        });
        CompletableFuture<Map<String, String>> wardFuture = executor.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId());
        });
        CompletableFuture<Map<String, V2PatientSourceType>> patientSourceTypeFuture = executor.supplyAsync(() -> {
            return query.queryPatientSourceType(params.getChainId());
        });
        CompletableFuture.allOf(departmentF, patientF, employeeF, wardFuture, v2HisChargeDepositsF, patientSourceTypeFuture).join();
        try {
            Map<String, Department> departmentMap = departmentF.get();
            Map<String, V2Patient> patientMap = patientF.get();
            Map<String, Employee> employeeMap = employeeF.get();
            Map<String, String> wardMap = wardFuture.get();
            Map<String, V2PatientSourceType> sourceTypeMap = patientSourceTypeFuture.get();
            List<V2HisChargeDeposit> v2HisChargeDeposits = v2HisChargeDepositsF.get();
            if (v2HisChargeDeposits != null) {
                for (V2HisChargeDeposit dto : v2HisChargeDeposits) {
                    v2HisChargeDepositMap.put(dto.getPatientOrderId(), dto);
                }
            }
            for (HisRevenuesCostChargePatientRes dto : selectChargPatientList) {
                dto.set(departmentMap, patientMap, employeeMap, wardMap, patientorderHospitalExtendMap, v2HisChargeDepositMap);
                dto.setFirstVisitSource(sourceTypeMap, employeeMap, patientMap);
            }
            setChargeCountValue(response, total);
            response.setData(selectChargPatientList);
        } catch (Exception e) {
            LOGGER.error("计费-患者异常：{}", e);
        }
        return response;
    }

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 计费-项目
     * @Author: zs
     * @Date: 2023/12/13 10:15
     */
    @Override
    public V2StatResponse selectChargProject(HisRevenuesCostReqParams params) {
        V2StatResponse response = new V2StatResponse();
        List<HisRevenuesCostChargeProjectRes> selectChargeProjectList;
        HisRevenuesCostTotalRes total;
        params.initDs();
        List<String> list = new ArrayList<>();
        List<TableHeaderEmployeeItem> headerEmployeeItems = query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.REVENUE_COST_CHARGE_PROJECT,
                params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0);
        if (params.getDimension() == 1) {
            selectChargeProjectList = mapper.selectChargeProjectListBatch(TableUtils.getHisTable(), params);
            total = mapper.selectChargPatientBatchTotal(TableUtils.getHisTable(), params);
        } else {
            list.add("生产批号");
            list.add("批次");
            list.add("有效日期");
            selectChargeProjectList = mapper.selectChargProjectList(TableUtils.getHisTable(), params);
            total = mapper.selectChargPatientTotal(TableUtils.getHisTable(), params, 1);
        }
        for (String label : list) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem ->
                    !tableHeaderEmployeeItem.getLabel().equals(label)
            ).collect(Collectors.toList());
        }
        response.setHeader(headerEmployeeItems);
        if (selectChargeProjectList == null || selectChargeProjectList.size() == 0) {
            setChargeCountValue(response, new HisRevenuesCostTotalRes());
            return response;
        }
        //设置纬度信息
        Set<String> patientIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        Set<String> empIds = new HashSet<>();
        Set<String> patientOrderIds = new HashSet<>();
        Set<Long> adviceIds = new HashSet<>();
        Set<String> goodsIds = new HashSet<>();
        HashMap<String, V2PatientorderHospitalExtend> patientorderHospitalExtendMap = new HashMap<>();
        for (HisRevenuesCostChargeProjectRes dto : selectChargeProjectList) {
            if (StringUtils.isNotBlank(dto.getPatientId())) {
                patientIds.add(dto.getPatientId());
            }
            if (StringUtils.isNotBlank(dto.getDepartmentId())) {
                departmentIds.add(dto.getDepartmentId());
            }
            if (StringUtils.isNotBlank(dto.getIssueDepartmentId())) {
                departmentIds.add(dto.getIssueDepartmentId());
            }
            if (StringUtils.isNotBlank(dto.getVerifierDepartmentId())) {
                departmentIds.add(dto.getVerifierDepartmentId());
            }
            if (StringUtils.isNotBlank(dto.getIssueDoctorId())) {
                empIds.add(dto.getIssueDoctorId());
            }
            if (StringUtils.isNotBlank(dto.getVerifierId())) {
                empIds.add(dto.getVerifierId());
            }
            if (StringUtils.isNotBlank(dto.getPatientOrderId())) {
                patientOrderIds.add(dto.getPatientOrderId());
            }
            if (dto.getAdviceId() != null) {
                adviceIds.add(dto.getAdviceId());
            }
            if (StringUtils.isNotBlank(dto.getGoodsId())) {
                goodsIds.add(dto.getGoodsId());
            }
        }
        //查询主管医生，护士，床号
        CompletableFuture<Map<String, Department>> departmentF = executor.supplyAsync(() -> {
            return query.queryDepartments(params.getChainId(), departmentIds);
        });
        CompletableFuture<Map<String, V2Patient>> patientF = executor.supplyAsync(() -> {
            return query.queryPatientAndNoCache(params.getChainId(), patientIds);
        });
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() -> {
            return query.queryEmployeeByChainAndIds(params.getChainId(), empIds);
        });
        CompletableFuture<Map<String, String>> wardFuture = executor.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId());
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        //classLevelId
        CompletableFuture<Map<Integer, V2GoodsCustomType>> goodsFeeTypesF = executor.supplyAsync(() -> {
            return query.queryProductCustomTypeTextByChainId(params.getChainId());
        });
        CompletableFuture<Map<String, V2Goods>> goodsF = executor.supplyAsync(() -> {
            return query.queryProducts(params.getChainId(), goodsIds);
        });
        CompletableFuture<List<V2PatientorderHospitalExtend>> patientorderHospitalExtendsF = executor.supplyAsync(() -> {
            return chargeMapper.selectPatientorderHospitalExtendById(TableUtils.getCisPatientorderTable(), patientOrderIds, params.getChainId());
        });
        CompletableFuture<Map<Long, String>> adviceF = executor.supplyAsync(() -> {
            return query.queryAdvice(params.getChainId(), params.getClinicId(), adviceIds);
        });
        CompletableFuture.allOf(departmentF, patientF, employeeF, wardFuture, patientorderHospitalExtendsF, feeTypeF, goodsFeeTypesF, goodsF, adviceF).join();
        try {
            Map<String, Department> departmentMap = departmentF.get();
            Map<String, V2Patient> patientMap = patientF.get();
            Map<String, Employee> employeeMap = employeeF.get();
            Map<String, String> wardMap = wardFuture.get();
            Map<Integer, V2GoodsCustomType> goodsFeeTypes = goodsFeeTypesF.get();
            Map<Long, V2GoodsFeeType> feeType = feeTypeF.get();
            Map<String, V2Goods> goods = goodsF.get();
            Map<Long, String> advice = adviceF.get();
            List<V2PatientorderHospitalExtend> patientorderHospitalExtends = patientorderHospitalExtendsF.get();
            if (patientorderHospitalExtends != null) {
                for (V2PatientorderHospitalExtend dto : patientorderHospitalExtends) {
                    patientorderHospitalExtendMap.put(dto.getId(), dto);
                }
            }
            for (HisRevenuesCostChargeProjectRes dto : selectChargeProjectList) {
                dto.set(departmentMap, patientMap, employeeMap, wardMap, patientorderHospitalExtendMap, feeType, goods, advice);
                dto.setFee(query, goodsFeeTypes, params.getChainId(), params.getHisType());
                if (dto.getSettleAmount() == null) {
                    dto.setSettleAmount(BigDecimal.ZERO);
                }
            }
            setChargeCountValue(response, total);
            response.setData(selectChargeProjectList);
        } catch (Exception e) {
            LOGGER.error("计费-项目异常：{}", e);
        }
        return response;
    }

    /**
     * @param
     * @param reqParams -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.ExcelUtils.AbcExcelSheet>
     * @Description: 结算统计-异步导出
     * @Author: zs
     * @Date: 2023/12/11 17:43
     */
    @Override
    public List<ExcelUtils.AbcExcelSheet> asyncExport(HisRevenuesCostReqParams reqParams) {
        CompletableFuture<V2StatResponse> transactionF = executor.supplyAsync(() -> {
            return selectTransaction(reqParams);
        });
        CompletableFuture<V2StatResponse> projectF = executor.supplyAsync(() -> {
            return selectProject(reqParams);
        });
        CompletableFuture.allOf(transactionF, projectF).join();
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        try {
            V2StatResponse transaction = transactionF.get();
            V2StatResponse project = projectF.get();

            ExcelUtils.AbcExcelSheet transactionSheet = new ExcelUtils.AbcExcelSheet();
            transactionSheet.setName("按单据");
            transactionSheet.setData(ExcelUtils.exportDataV2(transaction.getData(), transaction.getHeader()));
            transactionSheet.setSheetDefinition(ExcelUtils.exportTableHeader(transaction.getHeader()));
            transactionSheet.setTableHeaderEmployeeItems(transaction.getHeader());
            sheets.add(transactionSheet);

            ExcelUtils.AbcExcelSheet projectSheet = new ExcelUtils.AbcExcelSheet();
            projectSheet.setName("按项目");
            projectSheet.setData(ExcelUtils.exportDataV2(project.getData(), project.getHeader()));
            projectSheet.setSheetDefinition(ExcelUtils.exportTableHeader(project.getHeader()));
            projectSheet.setTableHeaderEmployeeItems(project.getHeader());
            sheets.add(projectSheet);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("结算异步导出异常：{}", e);
        }
        return sheets;
    }

    /**
     * @param
     * @param reqParams -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.ExcelUtils.AbcExcelSheet>
     * @Description: 计费统计-异步导出
     * @Author: zs
     * @Date: 2023/12/11 17:43
     */
    @Override
    public List<ExcelUtils.AbcExcelSheet> chargeAsyncExport(HisRevenuesCostReqParams reqParams) {
        CompletableFuture<V2StatResponse> chargPatientF = executor.supplyAsync(() -> {
            return selectChargPatient(reqParams);
        });
        CompletableFuture<V2StatResponse> chargProjectF = executor.supplyAsync(() -> {
            return selectChargProject(reqParams);
        });
        CompletableFuture.allOf(chargPatientF, chargProjectF).join();
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        try {
            V2StatResponse patient = chargPatientF.get();
            V2StatResponse project = chargProjectF.get();
            ExcelUtils.AbcExcelSheet transactionSheet = new ExcelUtils.AbcExcelSheet();
            transactionSheet.setName("按单据");
            transactionSheet.setData(ExcelUtils.exportDataV2(patient.getData(), patient.getHeader()));
            transactionSheet.setSheetDefinition(ExcelUtils.exportTableHeader(patient.getHeader()));
            transactionSheet.setTableHeaderEmployeeItems(patient.getHeader());
            sheets.add(transactionSheet);

            ExcelUtils.AbcExcelSheet projectSheet = new ExcelUtils.AbcExcelSheet();
            projectSheet.setName("按项目");
            projectSheet.setData(ExcelUtils.exportDataV2(project.getData(), project.getHeader()));
            projectSheet.setSheetDefinition(ExcelUtils.exportTableHeader(project.getHeader()));
            projectSheet.setTableHeaderEmployeeItems(project.getHeader());
            sheets.add(projectSheet);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("计费异步导出异常：{}", e);
        }
        return sheets;
    }

    /**
     * 住院收费日月报
     *
     * @param params -
     * @return -
     */
    @Override
    public HisRevenuesCostChargeReportRes selectChargeReport(HisRevenuesCostReqParams params) {
        //查询汇总数据
        CompletableFuture<HisRevenuesCostChargeReportRes> chargeReportSummaryListF = CompletableFuture.supplyAsync(() -> {
            return mapper.selectChargeReportSummary(TableUtils.getHisTable(), params);
        });
        //查询费用分类
        CompletableFuture<List<HisRevenuesCostFeeTypeRes>> feeTypeListF = CompletableFuture.supplyAsync(() -> {
            return mapper.selectVerticalSummy(TableUtils.getHisTable(), params);
        });
        //查询收费方式
        CompletableFuture<List<HisRevenuesCostPayModeRes>> payModeListF = CompletableFuture.supplyAsync(() -> {
            return mapper.selectAmountByPayMode(TableUtils.getHisTable(), params);
        });
        //查询医保收费方式
        CompletableFuture<HisRevenuesCostSheBaoPayModeRes> sheBaoPayModeListF = CompletableFuture.supplyAsync(() -> {
            return hologresRevenueChareDetailMapper.selectAmountBySheBaoPayMode(TableUtils.getCisTable(), params);
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        CompletableFuture<Map<Integer, String>> payF = executor.supplyAsync(() -> {
            return query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
        });

        CompletableFuture.allOf(chargeReportSummaryListF, feeTypeListF, payModeListF, sheBaoPayModeListF,
                feeTypeF, payF).join();

        HisRevenuesCostChargeReportRes result = new HisRevenuesCostChargeReportRes();
        try {
            List<HisRevenuesCostFeeTypeRes> feeTypeRes = feeTypeListF.get();
            List<HisRevenuesCostPayModeRes> payModeRes = payModeListF.get();
            HisRevenuesCostSheBaoPayModeRes sheBaoPayModeRes = sheBaoPayModeListF.get();
            Map<Long, V2GoodsFeeType> feeTypeMap = feeTypeF.get();
            Map<Integer, String> payTypeMap = payF.get();

            Map<String, BigDecimal> feeTypeResMap = new TreeMap<>();
            for (HisRevenuesCostFeeTypeRes fee : feeTypeRes) {
                String FeeName = "未指定";
                if (feeTypeMap.containsKey(fee.getFeeTypeId())) {
                    FeeName = feeTypeMap.get(fee.getFeeTypeId()).getName();
                }
                feeTypeResMap.put(FeeName, feeTypeResMap.getOrDefault(FeeName, BigDecimal.ZERO).add(fee.getFeeTypeAmount()));
            }

            Map<String, BigDecimal> payModeResMap = new TreeMap<>();
            if (sheBaoPayModeRes != null) {
                sheBaoPayModeRes.pretty(payModeResMap);
            }
            for (HisRevenuesCostPayModeRes pay : payModeRes) {
                String payName = ChargeHandler.handlePayMode(query, pay.getPayMode(), pay.getPaySubMode(), payTypeMap);
                payName = payName == null ? "未指定" : payName;
                payModeResMap.put(payName, payModeResMap.getOrDefault(payName, BigDecimal.ZERO).add(pay.getPrice()));
            }

            result.pretty(chargeReportSummaryListF.get());
            result.assembleFeeAndPayMode(feeTypeResMap, payModeResMap);
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
            LOGGER.error("selectChargeReport exception", e);
        }
        return result;
    }

    @Override
    public List<ExcelUtils.AbcExcelSheet> chargeReportAsyncExport(HisRevenuesCostReqParams params) {
        List<ExcelUtils.AbcExcelSheet> excelSheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet sheet1 = new ExcelUtils.AbcExcelSheet();
        sheet1.setName("住院收费日报");
        HisRevenuesCostChargeReportRes chargeReportRes = selectChargeReport(params);
        List<HisRevenueCostReportExportResult> dataList = new ArrayList<>();
        dataList.addAll(hisRevenuesCostHandler.reportSummaryHandler(chargeReportRes));
        dataList.addAll(hisRevenuesCostHandler.reportKeyValueHandler(chargeReportRes.getFeeTypeList(), "费用分类"));
        dataList.addAll(hisRevenuesCostHandler.reportKeyValueHandler(chargeReportRes.getPayModeList(), "支付方式"));
        sheet1.setHeadStyleClass(HisRevenueCostReportExportResult.class);
        sheet1.setData(dataList);
        excelSheets.add(sheet1);
        return excelSheets;
    }

    @Override
    public V2StatResponse selectChargeDeposit(HisRevenuesCostReqParams params) throws Exception {
        V2StatResponse response = new V2StatResponse();
        params.setPayModes();
        List<HisRevenuesCostChargeDeposit> depositList = mapper.selectChargeDepositList(TableUtils.getHisTable(), params);
        Set<String> patientIdSet = new HashSet<>();
        Set<String> departmentIdSet = new HashSet<>();
        Set<String> employeeIdSet = new HashSet<>();
        Set<String> patientOrderIdSet = new HashSet<>();
        for (HisRevenuesCostChargeDeposit deposit : depositList) {
            if (!StrUtil.isBlank(deposit.getPatientId())) {
                patientIdSet.add(deposit.getPatientId());
            }
            if (!StrUtil.isBlank(deposit.getDepartmentId())) {
                departmentIdSet.add(deposit.getDepartmentId());
            }
            if (!StrUtil.isBlank(deposit.getOperateId())) {
                employeeIdSet.add(deposit.getOperateId());
            }
            if (!StrUtil.isBlank(deposit.getPatientOrderId())) {
                patientOrderIdSet.add(deposit.getPatientOrderId());
            }
        }
        CompletableFuture<Map<String, V2Patient>> patientFuture = executor.supplyAsync(() -> {
            return query.queryPatient(params.getChainId(), patientIdSet, params.getEnablePatientMobile());
        });
        CompletableFuture<Map<String, Department>> departmentFuture = executor.supplyAsync(() -> {
            return query.queryDepartments(params.getChainId(), departmentIdSet);
        });
        CompletableFuture<Map<String, String>> wardFuture = executor.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId());
        });
        CompletableFuture<Map<String, Employee>> employeeFuture = executor.supplyAsync(() -> {
            return query.queryEmployeeByChainAndIds(params.getChainId(), employeeIdSet);
        });
        CompletableFuture<Map<String, V2PatientorderHospitalExtend>> patientOrderExtendFuture = executor.supplyAsync(() -> {
            return query.queryPatientOrderHospitalExtendById(params.getChainId(), patientOrderIdSet);
        });
        CompletableFuture<Map<Integer, String>> payFuture = executor.supplyAsync(() -> {
            return query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
        });
        CompletableFuture<Map<String, V2Patientorder>> patientorderFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryPatientorderByIds(params.getChainId(), patientOrderIdSet);
        });
        CompletableFuture.allOf(patientFuture, departmentFuture, wardFuture, employeeFuture, payFuture);
        Map<String, V2Patient> patientMap = patientFuture.get();
        Map<String, Department> departmentMap = departmentFuture.get();
        Map<String, String> wardMap = wardFuture.get();
        Map<String, Employee> employeeMap = employeeFuture.get();
        Map<String, V2PatientorderHospitalExtend> patientOrderHospitalExtendMap = patientOrderExtendFuture.get();
        Map<String, V2Patientorder> v2PatientorderMap = patientorderFuture.get();
        Map<Integer, String> payTypeMap = payFuture.get();
        for (HisRevenuesCostChargeDeposit deposit : depositList) {
            deposit.setPatientMessage(patientMap);
            deposit.setDepartmentMessage(departmentMap);
            deposit.setWardMessage(wardMap);
            deposit.setEmployeeMessage(employeeMap);
            deposit.setPatientOrderMessage(patientOrderHospitalExtendMap, v2PatientorderMap);
            deposit.setPayModeMessage(query, payTypeMap);
            deposit.pretty();
        }
        List<TableHeaderEmployeeItem> headerEmployeeItemList = query.getTableHeaderEmployeeItems(
                params.getParams().getEmployeeId(), HeaderTableKeyConfig.STAT_HIS_REVENUE_COST_CHARGE_DEPOSIT,
                params.getParams().getViewModeInteger(),
                params.getParams().getNodeType(), HeaderTableKeyConfig.EXCLUDE_HIDDEN_1);
        HisRevenuesCostChargeDepositTotal depositTotal = mapper.selectChargeDepositTotal(TableUtils.getHisTable(), params);
        StatResponseTotal total = hisRevenuesCostHandler.processingDepositTotal(depositTotal);
        response.setData(depositList);
        response.setTotal(total);
        response.setHeader(headerEmployeeItemList);
        return response;
    }

    @Override
    public List<ExcelUtils.AbcExcelSheet> chargeDepositAsyncExport(HisRevenuesCostReqParams reqParams) throws Exception {
        V2StatResponse response = selectChargeDeposit(reqParams);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet feeTypeSheet = new ExcelUtils.AbcExcelSheet();
        feeTypeSheet.setName("住院押金收费");
        feeTypeSheet.setData(ExcelUtils.exportDataV2(response.getData(), response.getHeader()));
        feeTypeSheet.setSheetDefinition(ExcelUtils.exportTableHeader(response.getHeader()));
        feeTypeSheet.setTableHeaderEmployeeItems(response.getHeader());
        sheets.add(feeTypeSheet);
        return sheets;
    }

    @Override
    @Deprecated
    public V2StatResponse selectChargedProduct(HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException {
        StatConfigDto config = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(), params.getDispensaryType()));
        params.initHisSql(config);
        if (params.getTagIds() != null && params.getTagIds().size() > 0) {
            String goodsSqlByTagIds = goodsHandler.getGoodsSqlByTagIds(params.getChainId(), params.getClinicId(), params.getTagIds());
            if (goodsSqlByTagIds != null && !"".equals(goodsSqlByTagIds)) {
                params.setTagIdGoodsIdSql("goods_id" + goodsSqlByTagIds);
                params.setTagIdSql("product_id" + goodsSqlByTagIds);
            }
        }
        CompletableFuture<List<HisChargedProductDAO>> chargeDataF = executor.supplyAsync(() -> {
            return mapper.selectChargedProduct(TableUtils.getHisTable(), params);
        });
        CompletableFuture<HisChargedProductTotalDAO> chargeTotalF = executor.supplyAsync(() -> {
            return mapper.selectChargedProductTotal(TableUtils.getHisTable(), params);
        });
        CompletableFuture<List<HisChargedProductDAO>> settleDataF = executor.supplyAsync(() -> {
            return mapper.selectChargedSettle(TableUtils.getHisTable(), params);
        });
        CompletableFuture<HisChargedProductTotalDAO> settleTotalF = executor.supplyAsync(() -> {
            return mapper.selectChargedSettleTotal(TableUtils.getHisTable(), params);
        });
        List<HisChargedProductDAO> list;
        if (config.getHospitalChargeProductSet() == 1) {
            settleDataF.join();
            list = settleDataF.get();
        } else {
            chargeDataF.join();
            list = chargeDataF.get();
        }
        V2StatResponse response = new V2StatResponse();
        response.setHeader(query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.STAT_HIS_REVENUE_COST_CHARGED_PRODUCT,
                params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0));
        Set<String> ids = list.stream().map(HisChargedProductDAO::getProductId).collect(Collectors.toSet());

        CompletableFuture<Map<String, V2Goods>> goodsF = executor.supplyAsync(() -> {
            return query.queryProducts(params.getChainId(), ids);
        });
        CompletableFuture<Map<String, List<V2GoodsTag>>> goodsTagF = executor.supplyAsync(() -> {
            return query.queryGoodsTagByGoodsIds(params.getChainId(), params.getClinicId(), ids);
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        CompletableFuture.allOf(goodsF, feeTypeF, goodsTagF).join();
        Map<String, V2Goods> goods = goodsF.get();
        Map<Long, V2GoodsFeeType> feeTypeMap = feeTypeF.get();
        Map<String, List<V2GoodsTag>> goodsTagMap = goodsTagF.get();
        for (HisChargedProductDAO dao : list) {
            dao.setGoodsInfo(goods, feeTypeMap);
            if (params.getIsExport() == 1) {
                dao.setGoodsTagName(GoodsHandler.splicingGoodsTagName(goodsTagMap.get(dao.getProductId())));
            } else {
                dao.setGoodsTagName(GoodsHandler.splicingGoodsTagNameToList(goodsTagMap.get(dao.getProductId())));
            }
            dao.pretty();
        }

        response.setData(list);
        HisChargedProductTotalDAO total;
        if (config.getHospitalChargeProductSet() == 1) {
            settleTotalF.join();
            total = settleTotalF.get();
        } else {
            chargeTotalF.join();
            total = chargeTotalF.get();
        }
        total.pretty();
        response.setTotal(new StatResponseTotal("共 %s 条数据，销售数量 %s，销售金额 %s，成本 %s，毛利 %s，毛利率 %s", Arrays.asList(total.getCount(), total.getTotalCount(), total.getAmount(), total.getCost(), total.getProfit(), total.getProfitRate()), total.getCount(), params.getOffset(), params.getLimit()));
        return response;
    }

    /**
     * @param
     * @param params -
     * @return
     * @return cn.abc.flink.stat.common.response.V2StatResponse
     * @Description: 营收费/成本统计-收费项目统计-汇总
     * @Author: zs
     * @Date: 2024/3/21 10:54
     */
    @Override
    public V2StatResponse selectChargedFeeTypes(HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException {
        StatConfigDto config = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(), params.getDispensaryType()));
        params.initHisSql(config);
        params.initCisSql(config);
        params.setIsFeeType(CommonConstants.NUMBER_ONE);
        CompletableFuture<List<HisChargedProductDAO>> hospitalChargeListF = executor.supplyAsync(() -> {
            return mapper.selectChargedProduct(TableUtils.getHisTable(), params);
        });
        CompletableFuture<List<HisChargedProductDAO>> hospitalSettleListF = executor.supplyAsync(() -> {
            return mapper.selectChargedSettle(TableUtils.getHisTable(), params);
        });
        CompletableFuture<List<HisChargedProductDAO>> outpatiengListF = executor.supplyAsync(() -> mapper.selectCisChargeTransactionGroupByFeeTypeId(TableUtils.getCisTable(), params, false));

        outpatiengListF.join();
        List<HisChargedProductDAO> outpatientList = outpatiengListF.get();
        List<HisChargedProductDAO> hospitalList;
        if (config.getHospitalChargeProductSet() == 1) {
            hospitalSettleListF.join();
            hospitalList = hospitalSettleListF.get();
        } else {
            hospitalChargeListF.join();
            hospitalList = hospitalChargeListF.get();
        }
        V2StatResponse response = new V2StatResponse();
        response.setHeader(query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.STAT_HIS_REVENUE_COST_CHARGED_PRODUCT_TOTAL,
                params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0));
        if (hospitalList == null && outpatientList == null) {
            return response;
        }
        Set<Long> feeTypeKeys = new HashSet<>();
        Set<String> productIds = new HashSet<>();
        Map<Long, HisChargedProductDAO> hospitalMap = new HashMap<>();
        Map<Long, HisChargedProductDAO> outpatientMap = new HashMap<>();

        CompletableFuture<Map<String, V2Goods>> goodsF = executor.supplyAsync(() -> {
            return query.queryProducts(params.getChainId(), productIds);
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        CompletableFuture.allOf(goodsF, feeTypeF).join();
        List<Map<String, Object>> data = new ArrayList<>();
        Map<String, V2Goods> goods = goodsF.get();
        Map<Long, V2GoodsFeeType> feeTypeMap = feeTypeF.get();
        Map<String, Object> totalReslutMap = listHandler(hospitalList, outpatientList, feeTypeKeys, productIds, hospitalMap, outpatientMap, goods, feeTypeMap);
        BigDecimal zero = BigDecimal.ZERO;
        for (Long feeType : feeTypeKeys) {
            HisChargedProductDAO total = new HisChargedProductDAO(zero, zero, zero, zero, "0");
            HisChargedProductDAO hospital = hospitalMap.get(feeType);
            HisChargedProductDAO outpatient = outpatientMap.get(feeType);
            if (hospital != null) {
                total.setTotal(hospital);
            }
            if (outpatient != null) {
                total.setTotal(outpatient);
            }
            V2GoodsFeeType v2GoodsFeeType = feeTypeMap.get(feeType);
            if (v2GoodsFeeType != null) {
                total.setFeeTypeName(v2GoodsFeeType.getName());
            }
            if (total.getFeeTypeName() == null || "".equals(total.getFeeTypeName())) {
                total.setFeeTypeName("-");
            }
            total.pretty();
            Map<String, Object> reslut = new HashMap<>();
            assemblyResults(total, "", reslut);
            assemblyResults(hospital, "Hospital", reslut);
            assemblyResults(outpatient, "Outpatient", reslut);
            data.add(reslut);
        }
        response.setSummary(totalReslutMap);
        response.setData(data);
        return response;
    }

    public V2StatResponse selectChargedProducts(HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException {
        StatConfigDto config = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(), params.getDispensaryType()));
        params.initHisSql(config);
        params.initCisSql(config);
        params.setIsFeeType(CommonConstants.NUMBER_ONE);
        CompletableFuture<List<HisAllChargeProductDAO>> future = executor.supplyAsync(() -> {
            return mapper.selectAllChargedProducts(TableUtils.getHisTable(), TableUtils.getCisTable(), params, true);
        });
        CompletableFuture<HisAllChargeProductDAO> totalFuture = executor.supplyAsync(() -> {
            return mapper.selectAllChargedProductsTotal(TableUtils.getHisTable(), TableUtils.getCisTable(), params, true);
        });

        future.join();
        List<HisAllChargeProductDAO> list = future.get();
        V2StatResponse response = new V2StatResponse();
        response.setHeader(query.getTableHeaderEmployeeItems(params.getParams().getEmployeeId(), HeaderTableKeyConfig.STAT_HIS_ALL_REVENUE_PRODUCTS,
                params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0));
        if (list == null && list.isEmpty()) {
            return response;
        }
        Set<String> productIds = new HashSet<>();
        for (HisAllChargeProductDAO dao : list) {
            productIds.add(dao.getProductId());
        }
        CompletableFuture<Map<String, V2Goods>> goodsF = executor.supplyAsync(() -> {
            return query.queryProducts(params.getChainId(), productIds);
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> {
            return query.selectAdviceFeeType(params.getChainId());
        });
        CompletableFuture.allOf(goodsF, feeTypeF).join();
        Map<String, V2Goods> goodsMap = goodsF.get();
        Map<Long, V2GoodsFeeType> feeTypeMap = feeTypeF.get();
        for (HisAllChargeProductDAO dao : list) {
            dao.initData(goodsMap, feeTypeMap);
        }
        HisAllChargeProductDAO total = totalFuture.get();
        response.setTotal(new StatResponseTotal(total.getSummaryCount().longValue()));
        total.setProductName("合计");
        total.calcSummaryValues();
        response.setSummary(total);
        response.setData(list);
        return response;
    }

    public List<ExcelUtils.AbcExcelSheet> exportHisChargeProducts(HisRevenuesCostChargedProductReqParams params) throws ExecutionException, InterruptedException {
        params.initParams();
        V2StatResponse products = selectChargedProducts(params);
        V2StatResponse fees = selectChargedFeeTypes(params);

        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();

        ExcelUtils.AbcExcelSheet feeSheet = new ExcelUtils.AbcExcelSheet();
        feeSheet.setName("按类型");
        feeSheet.setData(ExcelUtils.exportMapData(fees.getData(), fees.getHeader()));
        feeSheet.setSheetDefinition(ExcelUtils.exportTableHeader(fees.getHeader()));
        sheets.add(feeSheet);

        ExcelUtils.AbcExcelSheet productSheet = new ExcelUtils.AbcExcelSheet();
        productSheet.setName("按项目");
        productSheet.setData(ExcelUtils.exportDataV2(products.getData(), products.getHeader()));
        productSheet.setSheetDefinition(ExcelUtils.exportTableHeader(products.getHeader()));
        sheets.add(productSheet);

        return sheets;
    }

    /**
     * @param
     * @param hospitalList   -
     * @param outpatientList -
     * @param feeTypeKeys    -
     * @param productIds     -
     * @param hospitalMap    -
     * @param outpatientMap  -
     * @param goods          -
     * @param feeTypeMap     -
     * @return
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Description: 处理结果集合并返回汇总的合计
     * @Author: zs
     * @Date: 2024/3/21 16:17
     */
    private Map<String, Object> listHandler(List<HisChargedProductDAO> hospitalList,
                                            List<HisChargedProductDAO> outpatientList,
                                            Set<Long> feeTypeKeys, Set<String> productIds,
                                            Map<Long, HisChargedProductDAO> hospitalMap,
                                            Map<Long, HisChargedProductDAO> outpatientMap,
                                            Map<String, V2Goods> goods,
                                            Map<Long, V2GoodsFeeType> feeTypeMap) {
        BigDecimal zero = BigDecimal.ZERO;
        HisChargedProductDAO total = new HisChargedProductDAO(zero, zero, zero, zero, "0");
        HisChargedProductDAO hTotal = new HisChargedProductDAO(zero, zero, zero, zero, "0");
        if (hospitalList != null && hospitalList.size() > 0) {
            hospitalList.forEach(x -> {
                feeTypeKeys.add(x.getFeeTypeId());
                productIds.add(x.getProductId());
                hospitalMap.put(x.getFeeTypeId(), x);
                if (x.getCount() != null) {
                    hTotal.setCount(hTotal.getCount().add(x.getCount()));
                }
                if (x.getAmount() != null) {
                    hTotal.setAmount(hTotal.getAmount().add(x.getAmount()));
                }
                if (x.getCostPrice() != null) {
                    hTotal.setCostPrice(hTotal.getCostPrice().add(x.getCostPrice()));
                }
                x.setGoodsInfo(goods, feeTypeMap);
                x.pretty();
            });
            hTotal.pretty();
        }
        HisChargedProductDAO oTotal = new HisChargedProductDAO(zero, zero, zero, zero, "0");
        if (outpatientList != null && outpatientList.size() > 0) {
            outpatientList.forEach(x -> {
                feeTypeKeys.add(x.getFeeTypeId());
                productIds.add(x.getProductId());
                outpatientMap.put(x.getFeeTypeId(), x);
                if (x.getCount() != null) {
                    oTotal.setCount(oTotal.getCount().add(x.getCount()));
                }
                if (x.getAmount() != null) {
                    oTotal.setAmount(oTotal.getAmount().add(x.getAmount()));
                }
                if (x.getCostPrice() != null) {
                    oTotal.setCostPrice(oTotal.getCostPrice().add(x.getCostPrice()));
                }
                x.setGoodsInfo(goods, feeTypeMap);
                x.pretty();
            });
            oTotal.pretty();
        }
        //汇总的合计
        total.setTotal(oTotal);
        total.setTotal(hTotal);
        total.pretty();
        total.setFeeTypeName("合计");
        Map<String, Object> reslut = new HashMap<>();
        assemblyResults(total, "", reslut);
        assemblyResults(hTotal, "Hospital", reslut);
        assemblyResults(oTotal, "Outpatient", reslut);
        return reslut;
    }

    /**
     * @param
     * @param total  值
     * @param reslut 结果
     * @param suffix 前缀
     * @return
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Description: 将结果值拉平
     * @Author: zs
     * @Date: 2024/3/21 16:07
     */
    private void assemblyResults(HisChargedProductDAO total, String suffix, Map<String, Object> reslut) {
        if (total == null) {
            BigDecimal zero = BigDecimal.ZERO;
            total = new HisChargedProductDAO(zero, zero, zero, zero, "0");
        }
        reslut.put("feeTypeName" + suffix, total.getFeeTypeName());
        reslut.put("count" + suffix, total.getCount());
        reslut.put("amount" + suffix, total.getAmount());
        reslut.put("costPrice" + suffix, total.getCostPrice());
        reslut.put("gross" + suffix, total.getGross());
        reslut.put("profitText" + suffix, total.getProfitText());
    }

    @Override
    @Deprecated
    public List<ExcelUtils.AbcExcelSheet> exportChargedProduct(HisRevenuesCostChargedProductReqParams params, HisRevenueOutpatientProductReq outpatientParams) throws ExecutionException, InterruptedException {
        params.initParams();
        V2StatResponse response = selectChargedProduct(params);
        V2StatResponse responseTotal = selectChargedFeeTypes(params);
        V2StatResponse result = outpatientProductService.getOutpatientProduct(outpatientParams);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        if (responseTotal.getSummary() != null) {
            if (responseTotal.getData() != null) {
                responseTotal.getData().add(responseTotal.getSummary());
            }
        }
        ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();
        sheet.setName("汇总");
        sheet.setData(ExcelUtils.exportMapData(responseTotal.getData(), responseTotal.getHeader()));
        sheet.setSheetDefinition(ExcelUtils.exportTableHeader(responseTotal.getHeader()));
        sheets.add(sheet);

        ExcelUtils.AbcExcelSheet sheet1 = new ExcelUtils.AbcExcelSheet();
        sheet1.setName("门诊");
        sheet1.setData(ExcelUtils.exportDataV2(result.getData(), result.getHeader()));
        sheet1.setSheetDefinition(ExcelUtils.exportTableHeader(result.getHeader()));
        sheets.add(sheet1);

        ExcelUtils.AbcExcelSheet sheet2 = new ExcelUtils.AbcExcelSheet();
        sheet2.setName("住院");
        sheet2.setData(ExcelUtils.exportDataV2(response.getData(), response.getHeader()));
        sheet2.setSheetDefinition(ExcelUtils.exportTableHeader(response.getHeader()));
        sheets.add(sheet2);

        return sheets;
    }

    @Override
    public V2StatResponse selectInomeSummary(HisIncomeSummaryReq params) throws ExecutionException, InterruptedException {
        StatConfigDto statConfig = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(), params.getDispensaryType()));
        params.initParams(statConfig);

        List<HisIncomeFeeTypeDAO> outpatientIncomeList = null;
        List<HisIncomeFeeTypeDAO> hospitalIncomeList = null;
        List<HisIncomeFeeTypeDAO> peChargeIncomeList = null;

        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() -> query.selectAdviceFeeType(params.getChainId()));
        CompletableFuture<Map<String, String>> departmentF = executor.supplyAsync(() -> query.queryBatchDepartmentNamesByOrgan(params.getChainId(), params.getClinicId()));
        if (params.getScope() == 1) {
            CompletableFuture<List<HisIncomeFeeTypeDAO>> outpatientIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormTransactionRecord(TableUtils.getCisTable(), params));
            CompletableFuture.allOf(feeTypeF, departmentF, outpatientIncomeF);
            outpatientIncomeList = outpatientIncomeF.get();
        } else if (params.getScope() == 2) {
            CompletableFuture<List<HisIncomeFeeTypeDAO>> hospitalIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormHisChargeSettle(TableUtils.getHisTable(), params));
            CompletableFuture.allOf(feeTypeF, departmentF, hospitalIncomeF);
            hospitalIncomeList = hospitalIncomeF.get();
        } else if (params.getScope() == 3) {
            CompletableFuture<List<HisIncomeFeeTypeDAO>> outpatientIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormTransactionRecord(TableUtils.getCisTable(), params));
            CompletableFuture<List<HisIncomeFeeTypeDAO>> hospitalIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormHisChargeSettle(TableUtils.getHisTable(), params));
            CompletableFuture<List<HisIncomeFeeTypeDAO>> peChargeIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormPeChargeTransaction(TableUtils.getHisTable(), params));
            CompletableFuture.allOf(feeTypeF, departmentF, outpatientIncomeF, hospitalIncomeF, peChargeIncomeF);
            outpatientIncomeList = outpatientIncomeF.get();
            hospitalIncomeList = hospitalIncomeF.get();
            peChargeIncomeList = peChargeIncomeF.get();
        } else if (params.getScope() == 4) {
            CompletableFuture<List<HisIncomeFeeTypeDAO>> peChargeIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormPeChargeTransaction(TableUtils.getHisTable(), params));
            CompletableFuture.allOf(feeTypeF, departmentF, peChargeIncomeF);
            peChargeIncomeList = peChargeIncomeF.get();
        } else {
            CompletableFuture<List<HisIncomeFeeTypeDAO>> outpatientIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormTransactionRecord(TableUtils.getCisTable(), params));
            CompletableFuture<List<HisIncomeFeeTypeDAO>> hospitalIncomeF = executor.supplyAsync(() -> mapper.selectIncomeGroupByFeeTypeFormHisChargeSettle(TableUtils.getHisTable(), params));
            CompletableFuture.allOf(feeTypeF, departmentF, outpatientIncomeF, hospitalIncomeF);
            outpatientIncomeList = outpatientIncomeF.get();
            hospitalIncomeList = hospitalIncomeF.get();
        }
        Map<String, String> departmentMap = departmentF.get();
        Map<Long, V2GoodsFeeType> feeTypeMap = feeTypeF.get();
        Map<String, Map<String, Object>> departmentSummaryMap = new HashMap<>();
        // 门诊合计
        Map<String, Object> outpatientSummaryMap = new HashMap<>();
        // 住院合计
        Map<String, Object> hospitalSummaryMap = new HashMap<>();
        // 体检合计
        Map<String, Object> peChargeSummaryMap = new HashMap<>();
        // 总合计
        Map<String, Object> summaryMap = new HashMap<>();
        Set<Long> feeTypeIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        Map<String, Map<String, Object>> outpatientInomeMap = new HashMap<>();
        if (outpatientIncomeList != null) {
            outpatientIncomeList.stream().forEach(x -> {
                x.setDepartmentId(x.getDepartmentId() != null ? x.getDepartmentId() : AbcDefaultValueUtils.DEFAULT_ID);
                departmentIds.add(x.getDepartmentId());
                Map<String, Object> m = outpatientInomeMap.get(x.getDepartmentId());
                if (m == null) {
                    m = x.newMap(departmentMap, "门诊");
                    outpatientInomeMap.put(x.getDepartmentId() != null ? x.getDepartmentId() : AbcDefaultValueUtils.DEFAULT_ID, m);
                }
                feeTypeIds.add(x.getId() != null ? x.getId() : 0);
                m.put("name#" + x.getId(), x.getAmount());
                m.put("totalAmount", x.getAmount().add((BigDecimal) m.get("totalAmount")));

                Map<String, Object> dsMap = departmentSummaryMap.get(x.getDepartmentId() == null ? "null" : x.getDepartmentId());
                if (dsMap == null) {
                    dsMap = new HashMap<>();
                    dsMap.put("totalAmount", BigDecimal.ZERO);
                    dsMap.put("scopeName", "合计");
                    dsMap.put("departmentName", departmentMap.getOrDefault(x.getDepartmentId(), "无科室"));
                    dsMap.put("departmentId", x.getDepartmentId());
                    departmentSummaryMap.put(x.getDepartmentId() == null ? "null" : x.getDepartmentId(), dsMap);
                }
                dsMap.put("name#" + x.getId(), dsMap.keySet().contains("name#" + x.getId()) ? x.getAmount().add((BigDecimal) dsMap.get("name#" + x.getId())) : x.getAmount());
                dsMap.put("totalAmount", x.getAmount().add((BigDecimal) dsMap.get("totalAmount")));

                MathUtil.add(outpatientSummaryMap, "name#" + x.getId(), x.getAmount());
                MathUtil.add(outpatientSummaryMap, "totalAmount", x.getAmount());
                if (params.getScope() == 0 || params.getScope() == 3) {
                    MathUtil.add(summaryMap, "name#" + x.getId(), x.getAmount());
                }
            });
        }
        Map<String, Map<String, Object>> hospitalInomeMap = new HashMap<>();
        if (hospitalIncomeList != null) {
            hospitalIncomeList.stream().forEach(x -> {
                x.setDepartmentId(x.getDepartmentId() != null ? x.getDepartmentId() : AbcDefaultValueUtils.DEFAULT_ID);
                departmentIds.add(x.getDepartmentId());
                Map<String, Object> m = hospitalInomeMap.get(x.getDepartmentId());
                if (m == null) {
                    m = x.newMap(departmentMap, "住院");
                    hospitalInomeMap.put(x.getDepartmentId() != null ? x.getDepartmentId() : AbcDefaultValueUtils.DEFAULT_ID, m);
                }
                feeTypeIds.add(x.getId() != null ? x.getId() : 0);
                m.put("name#" + x.getId(), x.getAmount());
                m.put("totalAmount", x.getAmount().add((BigDecimal) m.get("totalAmount")));

                Map<String, Object> dsMap = departmentSummaryMap.get(x.getDepartmentId() == null ? "null" : x.getDepartmentId());
                if (dsMap == null) {
                    dsMap = new HashMap<>();
                    dsMap.put("totalAmount", BigDecimal.ZERO);
                    dsMap.put("scopeName", "合计");
                    dsMap.put("departmentName", departmentMap.getOrDefault(x.getDepartmentId(), "无科室"));
                    dsMap.put("departmentId", x.getDepartmentId());
                    departmentSummaryMap.put(x.getDepartmentId() == null ? "null" : x.getDepartmentId(), dsMap);
                }
                dsMap.put("name#" + x.getId(), dsMap.keySet().contains("name#" + x.getId()) ? x.getAmount().add((BigDecimal) dsMap.get("name#" + x.getId())) : x.getAmount());
                dsMap.put("totalAmount", x.getAmount().add((BigDecimal) dsMap.get("totalAmount")));

                MathUtil.add(hospitalSummaryMap, "name#" + x.getId(), x.getAmount());
                MathUtil.add(hospitalSummaryMap, "totalAmount", x.getAmount());
                if (params.getScope() == 0 || params.getScope() == 3) {
                    MathUtil.add(summaryMap, "name#" + x.getId(), x.getAmount());
                }
            });
        }
        Map<String, Map<String, Object>> peChargeInomeMap = new HashMap<>();
        if (peChargeIncomeList != null) {
            peChargeIncomeList.stream().forEach(x -> {
                x.setDepartmentId(x.getDepartmentId() != null ? x.getDepartmentId() : AbcDefaultValueUtils.DEFAULT_ID);
                departmentIds.add(x.getDepartmentId());
                Map<String, Object> m = peChargeInomeMap.get(x.getDepartmentId());
                if (m == null) {
                    m = x.newMap(departmentMap, "体检");
                    peChargeInomeMap.put(x.getDepartmentId(), m);
                }
                feeTypeIds.add(x.getId() != null ? x.getId() : 0);
                m.put("name#" + x.getId(), x.getAmount());
                m.put("totalAmount", x.getAmount().add((BigDecimal) m.get("totalAmount")));

                Map<String, Object> dsMap = departmentSummaryMap.get(x.getDepartmentId());
                if (dsMap == null) {
                    dsMap = new HashMap<>();
                    dsMap.put("totalAmount", BigDecimal.ZERO);
                    dsMap.put("scopeName", "合计");
                    dsMap.put("departmentName", departmentMap.getOrDefault(x.getDepartmentId(), "无科室"));
                    dsMap.put("departmentId", x.getDepartmentId());
                    departmentSummaryMap.put(x.getDepartmentId(), dsMap);
                }
                dsMap.put("name#" + x.getId(), dsMap.keySet().contains("name#" + x.getId()) ? x.getAmount().add((BigDecimal) dsMap.get("name#" + x.getId())) : x.getAmount());
                dsMap.put("totalAmount", x.getAmount().add((BigDecimal) dsMap.get("totalAmount")));

                MathUtil.add(peChargeSummaryMap, "name#" + x.getId(), x.getAmount());
                MathUtil.add(peChargeSummaryMap, "totalAmount", x.getAmount());
                if (params.getScope() == 3) {
                    MathUtil.add(summaryMap, "name#" + x.getId(), x.getAmount());
                }
            });
        }

        List<Long> sortFeeTypeIds = new ArrayList<>(feeTypeIds);
        Collections.sort(sortFeeTypeIds);
        mergeOhtherValueForIncomeSummary(outpatientInomeMap, outpatientSummaryMap, hospitalInomeMap, hospitalSummaryMap, peChargeInomeMap, peChargeSummaryMap, sortFeeTypeIds);

        List<String> sortDepartmentIds = new ArrayList<>(departmentIds);
        Collections.sort(sortDepartmentIds);
        Collections.reverse(sortDepartmentIds);

        List<Map<String, Object>> data = new ArrayList<>();
        for (String departmentId : sortDepartmentIds) {
            // 门诊
            if (params.getScope() == 0 || params.getScope() == 1 || params.getScope() == 3) {
                Map<String, Object> map = outpatientInomeMap.get(departmentId);
                if (map == null) {
                    map = new HashMap<>();
                    map.put("departmentId", departmentId);
                    map.put("departmentName", departmentMap.getOrDefault(departmentId, "无科室"));
                    map.put("scopeName", "门诊");
                }
                data.add(map);
            }
            // 住院
            if (params.getScope() == 0 || params.getScope() == 2 || params.getScope() == 3) {
                Map<String, Object> map = hospitalInomeMap.get(departmentId);
                if (map == null) {
                    map = new HashMap<>();
                    map.put("departmentId", departmentId);
                    map.put("departmentName", departmentMap.getOrDefault(departmentId, "无科室"));
                    map.put("scopeName", "住院");
                }
                data.add(map);
            }
            // 体检
            if (params.getScope() == 3 || params.getScope() == 4) {
                Map<String, Object> map = peChargeInomeMap.get(departmentId);
                if (map == null) {
                    map = new HashMap<>();
                    map.put("departmentId", departmentId);
                    map.put("departmentName", departmentMap.getOrDefault(departmentId, "无科室"));
                    map.put("scopeName", "体检");
                }
                data.add(map);
            }
            // 合计
            if (params.getScope() == 0 || params.getScope() == 3) {
                Map<String, Object> map = departmentSummaryMap.get(departmentId);
                if (map == null) {
                    map = new HashMap<>();
                    map.put("departmentId", departmentId);
                    map.put("departmentName", departmentMap.getOrDefault(departmentId, "-"));
                    map.put("scopeName", "合计");
                }
                data.add(map);
            }
        }

        if (params.getScope() == 1) {
            outpatientSummaryMap.put("departmentId", "summary");
            outpatientSummaryMap.put("departmentName", "合计");
            outpatientSummaryMap.put("isSummaries", true);
            data.add(outpatientSummaryMap);
        } else if (params.getScope() == 2) {
            hospitalSummaryMap.put("departmentId", "summary");
            hospitalSummaryMap.put("departmentName", "合计");
            hospitalSummaryMap.put("isSummaries", true);
            data.add(hospitalSummaryMap);
        } else if (params.getScope() == 3) {
            outpatientSummaryMap.put("departmentId", "summary");
            outpatientSummaryMap.put("departmentName", "合计");
            outpatientSummaryMap.put("scopeName", "门诊合计");
            outpatientSummaryMap.put("isSummaries", true);
            data.add(outpatientSummaryMap);
            hospitalSummaryMap.put("departmentId", "summary");
            hospitalSummaryMap.put("departmentName", "合计");
            hospitalSummaryMap.put("scopeName", "住院合计");
            hospitalSummaryMap.put("isSummaries", true);
            data.add(hospitalSummaryMap);
            peChargeSummaryMap.put("departmentId", "summary");
            peChargeSummaryMap.put("departmentName", "合计");
            peChargeSummaryMap.put("scopeName", "体检合计");
            peChargeSummaryMap.put("isSummaries", true);
            data.add(peChargeSummaryMap);
            summaryMap.put("departmentId", "summary");
            summaryMap.put("departmentName", "合计");
            summaryMap.put("scopeName", "全院合计");
            summaryMap.put("isSummaries", true);
            BigDecimal outpatientSummaryTotalAmount = outpatientSummaryMap.containsKey("totalAmount") ? (BigDecimal) outpatientSummaryMap.get("totalAmount") : BigDecimal.ZERO;
            BigDecimal hospitalSummaryTotalAmount = hospitalSummaryMap.containsKey("totalAmount") ? (BigDecimal) hospitalSummaryMap.get("totalAmount") : BigDecimal.ZERO;
            BigDecimal peSummaryTotalAmount = peChargeSummaryMap.containsKey("totalAmount") ? (BigDecimal) peChargeSummaryMap.get("totalAmount") : BigDecimal.ZERO;
            summaryMap.put("totalAmount", outpatientSummaryTotalAmount.add(hospitalSummaryTotalAmount).add(peSummaryTotalAmount));
            data.add(summaryMap);
        } else if (params.getScope() == 4) {
            peChargeSummaryMap.put("departmentId", "summary");
            peChargeSummaryMap.put("departmentName", "合计");
            peChargeSummaryMap.put("isSummaries", true);
            data.add(peChargeSummaryMap);
        } else {
            outpatientSummaryMap.put("departmentId", "summary");
            outpatientSummaryMap.put("departmentName", "合计");
            outpatientSummaryMap.put("scopeName", "门诊合计");
            outpatientSummaryMap.put("isSummaries", true);
            data.add(outpatientSummaryMap);
            hospitalSummaryMap.put("departmentId", "summary");
            hospitalSummaryMap.put("departmentName", "合计");
            hospitalSummaryMap.put("scopeName", "住院合计");
            hospitalSummaryMap.put("isSummaries", true);
            data.add(hospitalSummaryMap);
            summaryMap.put("departmentId", "summary");
            summaryMap.put("departmentName", "合计");
            summaryMap.put("scopeName", "全院合计");
            summaryMap.put("isSummaries", true);
            BigDecimal outpatientSummaryTotalAmount = outpatientSummaryMap.containsKey("totalAmount") ? (BigDecimal) outpatientSummaryMap.get("totalAmount") : BigDecimal.ZERO;
            BigDecimal hospitalSummaryTotalAmount = hospitalSummaryMap.containsKey("totalAmount") ? (BigDecimal) hospitalSummaryMap.get("totalAmount") : BigDecimal.ZERO;
            summaryMap.put("totalAmount", outpatientSummaryTotalAmount.add(hospitalSummaryTotalAmount));
            data.add(summaryMap);
        }

        V2StatResponse response = new V2StatResponse();
        response.setHeader(HisHeaderHandler.genIncomeSummaryHeader(params.getScope(), sortFeeTypeIds, feeTypeMap));
        response.setData(data);

        return response;
    }

    /**
     * 判断feeTypeIds是否大于16，如果大于，增加一个其他字段，并且index>15的feeType对应的值增加到这个字段上
     */
    private void mergeOhtherValueForIncomeSummary(Map<String, Map<String, Object>> outpatientIncomeMap, Map<String, Object> outpatientSummaryMap, Map<String, Map<String, Object>> hospitalIncomeMap, Map<String, Object> hospitalSummaryMap, Map<String, Map<String, Object>> peChargeIncomeMap, Map<String, Object> peChargeSummaryMap, List<Long> feeTypeIds) {
        if (feeTypeIds.size() > 16) {
            List<Long> otherFeeTypeIds = new ArrayList();
            for (int i = 15; i < feeTypeIds.size(); i++) {
                otherFeeTypeIds.add(feeTypeIds.get(i));
            }
            if (!otherFeeTypeIds.isEmpty()) {
                outpatientIncomeMap.forEach((key, m) -> {
                    if (m != null) {
                        BigDecimal others = BigDecimal.ZERO;
                        for (Map.Entry<String, Object> entry : m.entrySet()) {
                            if (entry.getKey().contains("name#")) {
                                long type = Long.parseLong(entry.getKey().split("#")[1]);
                                if (otherFeeTypeIds.contains(type)) {
                                    others = others.add((BigDecimal) entry.getValue());
                                }
                            }
                        }
                        m.put("#others", others);
                        outpatientSummaryMap.put("#others", others.add((BigDecimal) outpatientSummaryMap.getOrDefault("#others", BigDecimal.ZERO)));
                    }
                });

                hospitalIncomeMap.forEach((key, m) -> {
                    if (m != null) {
                        BigDecimal others = BigDecimal.ZERO;
                        for (Map.Entry<String, Object> entry : m.entrySet()) {
                            if (entry.getKey().contains("name#")) {
                                long type = Long.parseLong(entry.getKey().split("#")[1]);
                                if (otherFeeTypeIds.contains(type)) {
                                    others = others.add((BigDecimal) entry.getValue());
                                }
                            }
                        }
                        m.put("#others", others);
                        hospitalSummaryMap.put("#others", others.add((BigDecimal) hospitalSummaryMap.getOrDefault("#others", BigDecimal.ZERO)));
                    }
                });

                peChargeIncomeMap.forEach((key, m) -> {
                    if (m != null) {
                        BigDecimal others = BigDecimal.ZERO;
                        for (Map.Entry<String, Object> entry : m.entrySet()) {
                            if (entry.getKey().contains("name#")) {
                                long type = Long.parseLong(entry.getKey().split("#")[1]);
                                if (otherFeeTypeIds.contains(type)) {
                                    others = others.add((BigDecimal) entry.getValue());
                                }
                            }
                        }
                        m.put("#others", others);
                        peChargeSummaryMap.put("#others", others.add((BigDecimal) peChargeSummaryMap.getOrDefault("#others", BigDecimal.ZERO)));
                    }
                });
            }
        }

    }

    @Override
    public V2StatResponse selectReceiptSummary(HisReciptSummaryReq params) throws ExecutionException, InterruptedException {
        params.initParams();
        CompletableFuture<List<HisReciptPayModeDAO>> outpatientF = null;
        CompletableFuture<Map<Integer, String>> payModeF = executor.supplyAsync(() -> query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture<List<HisReciptPayModeDAO>> dischargedF = null;
        CompletableFuture<List<HisReciptPayModeDAO>> depositF = null;
        CompletableFuture<List<HisReciptPayModeDAO>> peChargeF = null;
        if (params.getScope() == 0 || params.getScope() == 1 || params.getScope() == 3) {
            outpatientF = executor.supplyAsync(() -> mapper.selectReciptPayModeFormTranasctionRecord(TableUtils.getCisTable(), params));
        }
        if (params.getScope() == 0 || params.getScope() == 2 || params.getScope() == 3) {
            dischargedF = executor.supplyAsync(() -> mapper.selectReciptPayModeFormHisChargeSettle(TableUtils.getHisTable(), params));
            depositF = executor.supplyAsync(() -> mapper.selectReciptPayModeFormHisDeposit(TableUtils.getHisTable(), params));
        }
        if (params.getScope() == 3 || params.getScope() == 4) {
            peChargeF = executor.supplyAsync(() -> mapper.selectReciptPayModeFormPeChargeTransaction(TableUtils.getHisTable(), params));
        }
        payModeF.join();
        Map<Integer, String> payModeMap = payModeF.get();
        Map<Integer, HisReciptSummaryResp> map = new HashMap<>();
        V2StatResponse response = new V2StatResponse();
        if (outpatientF != null) {
            outpatientF.join();
            BigDecimal repaymentAmount = BigDecimal.ZERO;
            BigDecimal oweAmount = BigDecimal.ZERO;
            for (HisReciptPayModeDAO x : outpatientF.get()) {
                if (x.getPayMode() == PayMode.OWE) {
                    oweAmount = oweAmount.add(x.getAmount());
                } else {
                    HisReciptSummaryResp resp = map.get(x.getPayMode());
                    if (resp == null) {
                        resp = new HisReciptSummaryResp();
                        resp.setPayMode(x.getPayMode());
                        resp.setPayModeName(payModeMap.getOrDefault(x.getPayMode(), "-"));
                        map.put(x.getPayMode(), resp);
                    }
                    resp.setTotalAmount(resp.getTotalAmount().add(x.getAmount()));
                    if ("-2".equals(x.getClassify())) {
                        resp.setMemberAmount(resp.getMemberAmount().add(x.getAmount()));
                    } else if ("18-0".equals(x.getClassify())) {
                        resp.setPromotionCardAmount(resp.getPromotionCardAmount().add(x.getAmount()));
                    } else {
                        resp.setOutpatientAmount(resp.getOutpatientAmount().add(x.getAmount()));
                    }
                    if (x.getRepaymentAmount() != null) {
                        repaymentAmount = repaymentAmount.add(x.getRepaymentAmount());
                    }
                }
            }
            if (oweAmount != null && BigDecimal.ZERO.compareTo(oweAmount) < 0) {
                StatResponseTotal total = new StatResponseTotal();
                total.setTemplate("备注：统计范围内欠费%s元，还款%s元");
                total.setData(Arrays.asList(oweAmount.setScale(2, RoundingMode.HALF_UP), repaymentAmount.setScale(2, RoundingMode.HALF_UP)));
                response.setTotal(total);
            }
        }

        if (dischargedF != null) {
            dischargedF.join();
            dischargedF.get().stream().forEach(x -> {
                HisReciptSummaryResp resp = map.get(x.getPayMode());
                if (resp == null) {
                    resp = new HisReciptSummaryResp();
                    resp.setPayMode(x.getPayMode());
                    resp.setPayModeName(payModeMap.getOrDefault(x.getPayMode(), "-"));
                    map.put(x.getPayMode(), resp);
                }
                resp.setTotalAmount(resp.getTotalAmount().add(x.getAmount()));
                resp.setDischargeAmount(resp.getDischargeAmount().add(x.getAmount()));
            });
        }
        if (depositF != null) {
            depositF.join();
            depositF.get().stream().forEach(x -> {
                HisReciptSummaryResp resp = map.get(x.getPayMode());
                if (resp == null) {
                    resp = new HisReciptSummaryResp();
                    resp.setPayMode(x.getPayMode());
                    resp.setPayModeName(payModeMap.getOrDefault(x.getPayMode(), "-"));
                    map.put(x.getPayMode(), resp);
                }
                resp.setTotalAmount(resp.getTotalAmount().add(x.getAmount()));
                resp.setDepositAmount(resp.getDepositAmount().add(x.getAmount()));
                resp.setDepositSettleRefundAmount(resp.getDepositSettleRefundAmount().add(x.getSettleRefundAmount().negate()));
            });
        }
        if (peChargeF != null) {
            peChargeF.join();
            peChargeF.get().stream().forEach(x -> {
                HisReciptSummaryResp resp = map.get(x.getPayMode());
                if (resp == null) {
                    resp = new HisReciptSummaryResp();
                    resp.setPayMode(x.getPayMode());
                    resp.setPayModeName(payModeMap.getOrDefault(x.getPayMode(), "-"));
                    map.put(x.getPayMode(), resp);
                }
                resp.setTotalAmount(resp.getTotalAmount().add(x.getAmount()));
                resp.setPeChargeAmount(resp.getPeChargeAmount().add(x.getAmount()));
            });
        }

        HisReciptSummaryResp ta = new HisReciptSummaryResp();
        ta.setPayModeName("合计");
        for (Map.Entry<Integer, HisReciptSummaryResp> entry : map.entrySet()) {
            entry.getValue().setTotalAmount(
                    entry.getValue().getOutpatientAmount()
                            .add(entry.getValue().getDischargeAmount())
                            .add(entry.getValue().getDepositAmount())
                            .add(entry.getValue().getDepositSettleRefundAmount())
                            .add(entry.getValue().getMemberAmount())
                            .add(entry.getValue().getPromotionCardAmount())
                            .add(entry.getValue().getPeChargeAmount())
            );
            ta.setOutpatientAmount(ta.getOutpatientAmount().add(entry.getValue().getOutpatientAmount()));
            ta.setDischargeAmount(ta.getDischargeAmount().add(entry.getValue().getDischargeAmount()));
            ta.setDepositAmount(ta.getDepositAmount().add(entry.getValue().getDepositAmount()));
            ta.setDepositSettleRefundAmount(ta.getDepositSettleRefundAmount().add(entry.getValue().getDepositSettleRefundAmount()));
            ta.setPeChargeAmount(ta.getPeChargeAmount().add(entry.getValue().getPeChargeAmount()));
            ta.setMemberAmount(ta.getMemberAmount().add(entry.getValue().getMemberAmount()));
            ta.setPromotionCardAmount(ta.getPromotionCardAmount().add(entry.getValue().getPromotionCardAmount()));
            ta.setTotalAmount(ta.getTotalAmount().add(entry.getValue().getTotalAmount()));
        }
        List<HisReciptSummaryResp> results = new ArrayList<>(map.values());
        response.setHeader(HisHeaderHandler.genReciptSummaryHeader(params.getScope()));
        response.setData(results);
        response.setSummary(ta);
        return response;
    }

    @Override
    public List<ExcelUtils.AbcExcelSheet> exportInomeSummary(HisIncomeSummaryReq params) throws ExecutionException, InterruptedException {
        V2StatResponse response = selectInomeSummary(params);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();
        sheet.setData(ExcelUtils.exportMapData(response.getData(), response.getHeader()));
        sheet.setSheetDefinition(ExcelUtils.exportTableHeader(HisAchievementChargeHandler.handleExportHeader(response.getHeader())));
        if (params.getScope() == 1) {
            sheet.setName("门诊收入汇总表");
        } else if (params.getScope() == 2) {
            sheet.setName("住院收入汇总表");
        } else if (params.getScope() == 4) {
            sheet.setName("体检收入");
        } else {
            sheet.setName("全院收入汇总表");
        }
        sheets.add(sheet);
        return sheets;
    }

    @Override
    public List<ExcelUtils.AbcExcelSheet> exportReceiptSummary(HisReciptSummaryReq params) throws ExecutionException, InterruptedException {
        V2StatResponse response = selectReceiptSummary(params);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();
        sheet.setData(ExcelUtils.exportDataV2(response.getData(), response.getHeader()));
        sheet.setSheetDefinition(ExcelUtils.exportTableHeader(response.getHeader()));
        if (params.getScope() == 1) {
            sheet.setName("门诊收款汇总表");
        } else if (params.getScope() == 2) {
            sheet.setName("住院收款汇总表");
        } else if (params.getScope() == 4) {
            sheet.setName("体检收款汇总表");
        } else {
            sheet.setName("全院收款汇总表");
        }
        sheets.add(sheet);
        return sheets;
    }

    @Override
    public V2StatResponse selectChargeDepositTypeSelection(HisRevenuesCostReqParams params) {
        V2StatResponse response = new V2StatResponse();
        response.setData(HisChargeDepositTypeEnum.getAllDepositType());
        return response;
    }

    @Override
    public V2StatResponse selectChargedSettlePatient(HisRevenuesCostChargedSettlePatientReqParams params) throws ExecutionException, InterruptedException {
        params.initParams();
        List<HisChargedSettlePatientDAO> patientList;
        HisChargedSettlePatientTotalDAO patientTotal;
        if (params.getSignal() != null && params.getSignal() == 0) {
            CompletableFuture<List<HisChargedSettlePatientDAO>> inPatientF = executor.supplyAsync(() ->
                    mapper.selectChargedSettleInPatient(TableUtils.getHisTable(), params));
            CompletableFuture<HisChargedSettlePatientTotalDAO> inPatientTotalF = executor.supplyAsync(() ->
                    mapper.selectChargedSettleInPatientTotal(TableUtils.getHisTable(), params));
            CompletableFuture.allOf(inPatientF, inPatientTotalF).join();
            patientList = inPatientF.get();
            patientTotal = inPatientTotalF.get();
        } else if (params.getSignal() != null && params.getSignal() == 1) {
            CompletableFuture<List<HisChargedSettlePatientDAO>> outPatientF = executor.supplyAsync(() ->
                    mapper.selectChargedSettleOutPatient(TableUtils.getHisTable(), params));
            CompletableFuture<HisChargedSettlePatientTotalDAO> outPatientTotalF = executor.supplyAsync(() ->
                    mapper.selectChargedSettleOutPatientTotal(TableUtils.getHisTable(), params));
            CompletableFuture.allOf(outPatientF, outPatientTotalF).join();
            patientList = outPatientF.get();
            patientTotal = outPatientTotalF.get();
        } else {
            CompletableFuture<List<HisChargedSettlePatientDAO>> chargedF = executor.supplyAsync(() ->
                    mapper.selectChargedSettleCharged(TableUtils.getHisTable(), params));
            CompletableFuture<HisChargedSettlePatientTotalDAO> chargedTotalF = executor.supplyAsync(() ->
                    mapper.selectChargedSettleChargedTotal(TableUtils.getHisTable(), params));
            CompletableFuture.allOf(chargedF, chargedTotalF).join();
            patientList = chargedF.get();
            patientTotal = chargedTotalF.get();
        }

        V2StatResponse response = new V2StatResponse();
        List<TableHeaderEmployeeItem> tableHeaderEmployeeItems = query.getTableHeaderEmployeeItems(params.getParams()
                        .getEmployeeId(), HeaderTableKeyConfig.STAT_HIS_REVENUE_COS_SETTLE_PATIENT,
                params.getParams().getViewModeInteger(), params.getParams().getNodeType(), 0);
        if (patientList == null) {
            response.setHeader(tableHeaderEmployeeItems);
            response.setTotal(new StatResponseTotal("共 %s 条数据，总收入 %s，总在院天数 %s", Arrays.asList(0, 0.00, 0),
                    0L, params.getOffset(), params.getLimit()));
            return response;
        }
        Set<String> patientIds = new HashSet<>();
        Set<String> employeeIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        Set<String> patientOrderIds = new HashSet<>();
        Set<String> feeTypeIds = new HashSet<>();
        for (HisChargedSettlePatientDAO patientDAO : patientList) {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getPatientId())) {
                patientIds.add(patientDAO.getPatientId());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getDoctorId())) {
                employeeIds.add(patientDAO.getDoctorId());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getNurseId())) {
                employeeIds.add(patientDAO.getNurseId());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getCreatedBy())) {
                employeeIds.add(patientDAO.getCreatedBy());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getDepartmentId())) {
                departmentIds.add(patientDAO.getDepartmentId());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getPatientOrderId())) {
                patientOrderIds.add(patientDAO.getPatientOrderId());
            }
            if (StringUtils.isNotEmpty(patientDAO.getFeeTypeInfo())) {
                JSONObject jsonObject = JSON.parseObject(patientDAO.getFeeTypeInfo());
                jsonObject.keySet().stream().filter(org.apache.commons.lang3.StringUtils::isNotEmpty).map(feeTypeIds::add);
            }
        }

        CompletableFuture<Map<String, Department>> departmentF = executor.supplyAsync(() ->
                query.queryDepartments(params.getChainId(), departmentIds));
        CompletableFuture<Map<String, V2Patient>> patientF = executor.supplyAsync(() ->
                query.queryPatientAndNoCache(params.getChainId(), patientIds));
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() ->
                query.queryEmployeeByChainAndIds(params.getChainId(), employeeIds));
        CompletableFuture<Map<String, String>> wardFuture = executor.supplyAsync(() ->
                query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture<Map<String, V2PatientorderHospitalExtend>> patientorderHospitalExtendsFuture =
                executor.supplyAsync(() -> query.queryPatientOrderHospitalExtendById(params.getChainId(), patientOrderIds));
        CompletableFuture<Map<Long, V2GoodsFeeType>> feeTypeF = executor.supplyAsync(() ->
                query.selectAdviceFeeType(params.getChainId()));
        CompletableFuture<Map<Integer, String>> payF = executor.supplyAsync(() ->
                query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture<Map<String, List<String>>> patientTagF = CompletableFuture.supplyAsync(() ->
                query.selectPatientTagByIds(params.getChainId(), params.getIsExport() == 1 ? null:patientIds));
        CompletableFuture<Map<String, V2PatientSourceType>> patientSourceTypeF = executor.supplyAsync(() ->
                query.queryPatientSourceType(params.getChainId()));
        CompletableFuture<Map<String, V2PatientClinic>> patientClinicsF = CompletableFuture.supplyAsync(() ->
                query.queryPatientClinicByPatientIds(params.getChainId(), params.getClinicId(), Lists.newArrayList(patientIds)));
        CompletableFuture<Map<String, V1EmrDiagnosis>> dischargeDiagnosisF = executor.supplyAsync(() ->
                query.queryDischargeDiagnosisByChainIdAndPatientOrderIds(params.getChainId(), patientOrderIds));


        CompletableFuture.allOf(departmentF, patientF, employeeF, wardFuture, feeTypeF).join();
        Map<String, Department> departmentMap = departmentF.get();
        Map<String, V2Patient> patientMap = patientF.get();
        Map<String, Employee> employeeMap = employeeF.get();
        Map<String, String> wardMap = wardFuture.get();
        Map<String, V2PatientorderHospitalExtend> patientorderHospitalExtendMap = patientorderHospitalExtendsFuture.get();
        Map<Long, V2GoodsFeeType> feeTypeMap = feeTypeF.get();
        Map<Integer, String> payTypeMap = payF.get();
        Map<String, List<String>> patientTagMap = patientTagF.get();
        Map<String, V2PatientSourceType> patientSourceTypeMap = patientSourceTypeF.get();
        Map<String, V2PatientClinic> patientClinicMap = patientClinicsF.get();
        Map<String, V1EmrDiagnosis> dischargeDiagnosisMap = dischargeDiagnosisF.get();

        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, KeyValuePojo> feeMap = new HashMap<>();
        Map<String, KeyValuePojo> payMap = new HashMap<>();
        for (HisChargedSettlePatientDAO dao : patientList) {
            dao.setBaseInfo(departmentMap, employeeMap, wardMap, patientorderHospitalExtendMap);
            dao.assemblePatientInfo(patientMap, employeeMap, patientSourceTypeMap, patientTagMap, patientClinicMap);
            dao.assembleDiagnosis(dischargeDiagnosisMap);
            dao.assembleTypeInfo(query, feeTypeMap, payTypeMap, feeMap, payMap, dataList);
        }

        response.setData(dataList);
        response.setHeader(HisHeaderHandler.assembleHisChargeSettlePatientHeader(tableHeaderEmployeeItems, feeMap, payMap, params));
        response.setTotal(new StatResponseTotal("共 %s 条数据，总收入 %s，总在院天数 %s",
                Arrays.asList(patientTotal.getTotalCount(), patientTotal.getTotalAmount(), patientTotal.getTotalDays()),
                Long.parseLong(patientTotal.getTotalCount().toString()), params.getOffset(), params.getLimit()));
        return response;
    }

    @Override
    public HisRevenueCostChargeSettlePatientSelectRsp selectChargedSettlePatientSelect(HisRevenuesCostChargedSettlePatientReqParams params) throws Exception {
        params.initDs("yyyy");
        List<HisChargedSettlePatientDAO> patientSelectList;
        if (params.getSignal() != null && params.getSignal() == 0) {
            patientSelectList = mapper.selectChargedSettleInPatientSelect(TableUtils.getHisTable(), params);
        } else {
            patientSelectList = mapper.selectChargedSettleOutPatientSelect(TableUtils.getHisTable(), params);
        }

        List<KeyValuePojo> payStatus = new ArrayList<>();
        payStatus.add(new KeyValuePojo("住院中", 2));
        payStatus.add(new KeyValuePojo("已收", 1));
        payStatus.add(new KeyValuePojo("待收", 0));
        payStatus.add(new KeyValuePojo("已退", -1));

        if (patientSelectList == null) {
            return new HisRevenueCostChargeSettlePatientSelectRsp(payStatus);
        }

        Set<String> employeeIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        for (HisChargedSettlePatientDAO patientDAO : patientSelectList) {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getDoctorId())) {
                employeeIds.add(patientDAO.getDoctorId());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getNurseId())) {
                employeeIds.add(patientDAO.getNurseId());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getCreatedBy())) {
                employeeIds.add(patientDAO.getCreatedBy());
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(patientDAO.getDepartmentId())) {
                departmentIds.add(patientDAO.getDepartmentId());
            }
        }

        CompletableFuture<Map<String, Department>> departmentF = executor.supplyAsync(() -> {
            return query.queryDepartments(params.getChainId(), departmentIds);
        });
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() -> {
            return query.queryEmployeeByChainAndIds(params.getChainId(), employeeIds);
        });
        CompletableFuture<Map<String, String>> wardFuture = executor.supplyAsync(() -> {
            return query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId());
        });

        CompletableFuture.allOf(departmentF, employeeF, wardFuture).join();
        Map<String, Department> departmentMap = departmentF.get();
        Map<String, Employee> employeeMap = employeeF.get();
        Map<String, String> wardMap = wardFuture.get();

        Map<String, DepartmentResp> departmentsMap = new HashMap<>();
        Map<String, WardResp> wardsMap = new HashMap<>();
        Map<String, EmployeeResp> doctorIdsMap = new HashMap<>();
        Map<String, EmployeeResp> nurseIdsMap = new HashMap<>();
        Map<String, EmployeeResp> chargeIdsMap = new HashMap<>();
        for (HisChargedSettlePatientDAO dao : patientSelectList) {
            String departmentId = dao.getDepartmentId();
            if (departmentId == null || departmentId.isEmpty()) {
                departmentId = "00000000000000000000000000000000";
                departmentsMap.put(departmentId, new DepartmentResp(departmentId, "未指定"));
            } else if (!departmentsMap.containsKey(departmentId)) {
                departmentsMap.put(departmentId, new DepartmentResp(departmentId, departmentMap.get(departmentId) == null
                        ? "未指定" : departmentMap.get(departmentId).getName()));
            }

            String wardId = dao.getWardId();
            if (wardId == null || wardId.isEmpty()) {
                wardId = "00000000000000000000000000000000";
                wardsMap.put(wardId, new WardResp(wardId, "未指定"));
            } else if (!wardsMap.containsKey(wardId)) {
                wardsMap.put(wardId, new WardResp(wardId, wardMap.get(wardId)));
            }

            String chargeBy = dao.getCreatedBy();
            if (chargeBy == null || chargeBy.isEmpty()) {
                chargeBy = "00000000000000000000000000000000";
                chargeIdsMap.put(chargeBy, new EmployeeResp(chargeBy, "未指定"));
            } else if (!chargeIdsMap.containsKey(chargeBy)) {
                chargeIdsMap.put(chargeBy, new EmployeeResp(chargeBy, employeeMap.get(chargeBy) == null
                        ? "未指定" : employeeMap.get(chargeBy).getName()));
            }

            String doctorId = dao.getDoctorId();
            if (doctorId == null || doctorId.isEmpty()) {
                doctorId = "00000000000000000000000000000000";
                doctorIdsMap.put(doctorId, new EmployeeResp(doctorId, "未指定"));
            } else if (!doctorIdsMap.containsKey(doctorId)) {
                doctorIdsMap.put(doctorId, new EmployeeResp(doctorId, employeeMap.get(doctorId) == null
                        ? "未指定" : employeeMap.get(doctorId).getName()));
            }

            String nurseId = dao.getNurseId();
            if (nurseId == null || nurseId.isEmpty()) {
                nurseId = "00000000000000000000000000000000";
                nurseIdsMap.put(nurseId, new EmployeeResp(nurseId, "未指定"));
            } else if (!nurseIdsMap.containsKey(nurseId)) {
                nurseIdsMap.put(nurseId, new EmployeeResp(nurseId, employeeMap.get(nurseId) == null
                        ? "未指定" : employeeMap.get(nurseId).getName()));
            }
        }
        return new HisRevenueCostChargeSettlePatientSelectRsp(new LinkedList<>(departmentsMap.values()),
                new LinkedList<>(wardsMap.values()),
                new LinkedList<>(doctorIdsMap.values()),
                new LinkedList<>(nurseIdsMap.values()),
                new LinkedList<>(chargeIdsMap.values()),
                payStatus);
    }

    @Override
    public List<ExcelUtils.AbcExcelSheet> exportHisRevenueCostChargeSettlePatient(HisRevenuesCostChargedSettlePatientReqParams params) throws ExecutionException, InterruptedException {
        V2StatResponse response = selectChargedSettlePatient(params);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();

        ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();
        sheet.setName("住院收费");
        sheet.setData(ExcelUtils.exportMapData(response.getData(), response.getHeader()));
        sheet.setSheetDefinition(ExcelUtils.exportTableHeader(response.getHeader()));
        sheets.add(sheet);
        return sheets;
    }

    @Override
    public HisRevenueCostPhysicalExaminationDayReportRsp selectPhysicalExaminationChargeDayReport(HisRevenuesCostReqParams params) {
        return hisPeChargeDataHandler.selectPhysicalExaminationChargeDayReport(params);
    }

    @Override
    public List<ExcelUtils.AbcExcelSheet> peChargeDayReportAsyncExport(HisRevenuesCostReqParams params) throws Exception {
        ArrayList<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        HisRevenueCostPhysicalExaminationDayReportRsp rsp = selectPhysicalExaminationChargeDayReport(params);
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        ExcelUtils.AbcExcelSheet details = new ExcelUtils.AbcExcelSheet();
        RevenueExportResult result = new RevenueExportResult();
        result.setFirstLine("现金结算合计");
        result.setSecondLine(rsp.getCashSettleTotalAmount().toPlainString());
        result.setFourthLine("实收合计");
        result.setFifthLine(rsp.getReceivedTotalAmount().toPlainString());
        revenueExportResults.add(result);
        List<RevenueExportResult> revenueVosResults = revenueChargedDailyHandler.revenueVosHandler(rsp.getPayModeList(), "收费方式");
        revenueExportResults.addAll(revenueVosResults);
        details.setName("体检收费报表");
        details.setData(revenueExportResults);
        details.setHeadStyleClass(RevenueExportResult.class);
        sheets.add(details);
        return sheets;
    }


    public HisChargedWardSelectionResp selectChargedSelection(HisChargedSelectionReqParams params) throws ExecutionException, InterruptedException {
        HisChargedWardSelectionResp resp = new HisChargedWardSelectionResp();

        HisAchievementChargeReqParams reqParams = new HisAchievementChargeReqParams();
        reqParams.setBeginDate(params.getBeginDate());
        reqParams.setEndDate(params.getEndDate());
        reqParams.setChainId(params.getChainId());
        reqParams.setClinicId(params.getClinicId());
        reqParams.setParams(params.getParams());
        reqParams.initDs();
        CompletableFuture<List<String>> wardIdsF = executor.supplyAsync(() -> {
            if (params.getTypes().contains(0)) {
                return mapper.selectChargedWardSelection(TableUtils.getHisTable(), params);
            }
            return null;
        });
        CompletableFuture<Map<String, String>> wardMapF = executor.supplyAsync(() -> {
            if (params.getTypes().contains(0)) {
                return query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId());
            }
            return null;
        });
        CompletableFuture<List<DepartmentResp>> departmentF = executor.supplyAsync(() -> {
            if (params.getTypes().contains(1)) {
                return departmentService.selectDepartmentFromChargeSettle(reqParams);
            }
            return null;
        });
        CompletableFuture<List<EmployeeResp>> doctorF = executor.supplyAsync(() -> {
            if (params.getTypes().contains(2)) {
                return employeeService.selectEmployeeFromHisChargeProduct(reqParams);
            }
            return null;
        });
        CompletableFuture<List<EmployeeResp>> nurseF = executor.supplyAsync(() -> {
            if (params.getTypes().contains(3)) {
                return employeeService.selectNurseFromHisCharged(params.getBeginDate(), params.getEndDate(), params.getChainId(), params.getClinicId());
            }
            return null;
        });
        CompletableFuture.allOf(wardIdsF, wardMapF, departmentF, doctorF, nurseF).join();
        Map<String, String> wardMap = wardMapF.get();
        List<String> wardIds = wardIdsF.get();
        if (wardIds != null) {
            resp.setWards(wardIds.stream()
                    .filter(Objects::nonNull)
                    .map(x -> {
                        Map<String, Object> m = new HashMap<>();
                        m.put("id", x);
                        m.put("name", wardMap.getOrDefault(x, "未指定"));
                        return m;
                    }).collect(Collectors.toList()));
        }
        resp.setDoctors(doctorF.get());
        resp.setDepartments(departmentF.get());
        resp.setNurses(nurseF.get());
        return resp;
    }

    public HisChargeSettlementSelecttionResp selectChargedSettlementSelection(AbcScStatRequestParams params) throws ExecutionException, InterruptedException {
        HisChargeSettlementSelecttionResp resp = new HisChargeSettlementSelecttionResp();
        HisAchievementChargeReqParams reqParams = new HisAchievementChargeReqParams();
        reqParams.setBeginDate(params.getBeginDate());
        reqParams.setEndDate(params.getEndDate());
        reqParams.setChainId(params.getChainId());
        reqParams.setClinicId(params.getClinicId());
        reqParams.setParams(params.getParams());
        reqParams.initDs();
        CompletableFuture<List<EmployeeResp>> hisEmployeeIdChargeSettleF =
                executor.supplyAsync(() -> employeeService.selectEmployeeFromChargeSettle(reqParams));
        CompletableFuture<List<DepartmentResp>> hisDepartmentIdChargeSettleF =
                executor.supplyAsync(() -> departmentService.selectDepartmentFromChargeSettle(reqParams));
        CompletableFuture<List<String>> wardIdsF = executor.supplyAsync(() -> mapper.selectChargeSettlementWardIds(TableUtils.getHisTable(), params));
        CompletableFuture<Map<String, String>> wardMapF = executor.supplyAsync(() -> query.queryWardNameByChainIdAndClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture.allOf(hisEmployeeIdChargeSettleF, hisDepartmentIdChargeSettleF, wardIdsF, wardMapF).join();

        resp.setCashiers(hisEmployeeIdChargeSettleF.get());
        resp.setDepartments(hisDepartmentIdChargeSettleF.get());
        Map<String, String> wardMap = wardMapF.join();
        resp.setWards(wardIdsF.get().stream()
                .filter(Objects::nonNull)
                .map(x -> {
                    Map<String, Object> m = new HashMap<>();
                    m.put("id", x);
                    m.put("name", wardMap.getOrDefault(x, "未指定"));
                    return m;
                }).collect(Collectors.toList()));
        return resp;
    }

    public V2StatResponse selectCashierDailyReport(HospCashierDailyReportParams params) throws ExecutionException, InterruptedException {
        CompletableFuture<List<CashierPayDAO>> cisPayF = executor.supplyAsync(() -> mapper.selectCisPayReceivedPriceWithCashierExcludeYiBao(TableUtils.getCisTable(), params));
        CompletableFuture<List<CashierPayDAO>> peChargePayF = executor.supplyAsync(() -> mapper.selectPeChargePayReceivedPriceWithCashierExcludeYiBao(TableUtils.getHisTable(), params));
        CompletableFuture<List<CashierPayDAO>> hisSettlePayF = executor.supplyAsync(() -> mapper.selectHisChargedSettlePayReceivedPriceWithCashierExcludeYiBao(TableUtils.getHisTable(), params));
        CompletableFuture<List<HospCashierDailyReportYiBaoDAO>> yibaoPayF = executor.supplyAsync(() -> mapper.selectYiBaoReceivedPriceWithCashier(TableUtils.getCisTable(), params));
        CompletableFuture<Map<Integer, String>> payConfigF = executor.supplyAsync(() -> query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() -> query.queryEmployeeByChainId(params.getChainId()));

        CompletableFuture.allOf(cisPayF, peChargePayF, hisSettlePayF, yibaoPayF, payConfigF, employeeF);

        Map<Integer, String> payMap = payConfigF.get();
        Map<String, Employee> employeeMap = employeeF.get();
        Set<Integer> payTypeIds = new HashSet<>();
        Map<String, Map<String, Object>> map = new HashMap<>();
        List<CashierPayDAO> cisPayList = cisPayF.get();
        List<CashierPayDAO> peChargePayList = peChargePayF.get();
        List<CashierPayDAO> hisSettlePayList = hisSettlePayF.get();
        List<HospCashierDailyReportYiBaoDAO> yibaoPay = yibaoPayF.get();
        if (cisPayList != null) {
            for (CashierPayDAO dao : cisPayList) {
                dao.addToMap(map, employeeMap);
                payTypeIds.add(dao.getPayType());
            }
        }
        if (peChargePayList != null) {
            for (CashierPayDAO dao : peChargePayList) {
                dao.addToMap(map, employeeMap);
                payTypeIds.add(dao.getPayType());
            }
        }
        if (hisSettlePayList != null) {
            for (CashierPayDAO dao : hisSettlePayList) {
                dao.addToMap(map, employeeMap);
                payTypeIds.add(dao.getPayType());
            }
        }
        if (yibaoPay != null) {
            for (HospCashierDailyReportYiBaoDAO dao : yibaoPay) {
                dao.addToMap(map, employeeMap, params.getProvinceId());
            }
        }

        List<Integer> payIds = new ArrayList<>(payTypeIds);
        Collections.sort(payIds);

        List<Integer> otherFields = HisHeaderHandler.getCashierDailyReportOhterFields(payIds);
        Map<String, Object> totalAmountRow = new HashMap<>();
        for (Map.Entry<String, Map<String, Object>> entry : map.entrySet()) {
            BigDecimal other = BigDecimal.ZERO;
            for (Map.Entry<String, Object> kv : entry.getValue().entrySet()) {
                if (!otherFields.isEmpty() && StringUtils.isNumeric(kv.getKey()) && otherFields.contains(Integer.parseInt(kv.getKey()))) {
                    other = other.add((BigDecimal) kv.getValue());
                }

                if (!"cashierId".equalsIgnoreCase(kv.getKey()) && !"cashierName".equalsIgnoreCase(kv.getKey())) {
                    BigDecimal totalAmount = (BigDecimal) totalAmountRow.getOrDefault(kv.getKey(), BigDecimal.ZERO);
                    totalAmountRow.put(kv.getKey(), totalAmount.add((BigDecimal) kv.getValue()));
                }
            }
            entry.getValue().put("#other", other);
        }

        totalAmountRow.put("cashierName", "合计");
        V2StatResponse response = new V2StatResponse();
        response.setHeader(HisHeaderHandler.genCashierDailyReportHeader(payIds, payMap));
        response.setData(new ArrayList(map.values()));
        response.setSummary(totalAmountRow);
        return response;
    }

    public List<ExcelUtils.AbcExcelSheet> exportCashierDailyReport(HospCashierDailyReportParams params) throws ExecutionException, InterruptedException {
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        V2StatResponse rsp = selectCashierDailyReport(params);
        ExcelUtils.AbcExcelSheet sheet = new ExcelUtils.AbcExcelSheet();

        List data = rsp.getData();
        if (data != null && rsp.getSummary() != null) {
            data.add(rsp.getSummary());
        }

        sheet.setName("sheet1");
        sheet.setData(ExcelUtils.exportMapDataV2(data, rsp.getHeader()));
        sheet.setSheetDefinition(ExcelUtils.exportTableHeader(rsp.getHeader()));
        sheets.add(sheet);
        return sheets;
    }

    @Override
    public List<StatOrganTableConfig> selectChargeDayReportConfig(HisRevenuesCostReqParams params) {
        return statConfigService.selectV2StatOrganTableConfig(params.getClinicId());
    }

    @Override
    public List<HisRevenuesCostChargeReportDetailRes> selectChargeDayReportDetail(HisRevenuesCostReqParams params) throws Exception {
        params.initBeginDateAndEndDate();
        params.initParams();
        List<HisRevenuesCostChargeReportDetailRes> result = new ArrayList<>();
        Map<String, StatComponentConfig> componentConfigMap = statConfigService
                .selectV2StatComponentConfig(params.getParams().getViewModeInteger(), params.getParams().getNodeType(),
                        params.getComponents());

        HisRevenueCostChargeDailyMethodInvoke methodInvoke = new HisRevenueCostChargeDailyMethodInvoke();
        for (String key : params.getComponents()) {
            if (!HisRevenueCostChargeDailyMethodInvoke.getMethodMap().containsKey(key)) {
                continue;
            }
            StatComponentConfig config = componentConfigMap.get(key);
            HisRevenuesCostChargeReportDetailRes res = new HisRevenuesCostChargeReportDetailRes(config, config.getTableKey() == null ? 1 : 2);
            res.setValue(methodInvoke.invokeMethod(key, params, config));
            result.add(res);
        }
        return result;
    }

    /**
     * 发票明细
     *
     * @param params -
     * @param config -
     * @return -
     */
    private V2StatResponse getInvoiceDetail(HisRevenuesCostReqParams params, StatComponentConfig config) {
        V2StatResponse resp = new V2StatResponse();
        resp.setHeader(config.getHeader());
        StatResponse response = revenueChargedDailyService.selectInvoiceDailyDetail(new RevenueParam(params, ""));
        if (response != null) {
            resp.setData(response.getData());
        }
        return resp;
    }

    /**
     * 发票汇总
     *
     * @param params -
     * @return -
     */
    private List<List<KeyValuePojo>> getInvoiceTotal(HisRevenuesCostReqParams params) throws Exception {
        return revenueChargedDailyService.selectInvoiceDailyTotal(new RevenueParam(params, ""));
    }

    /**
     * 青岛医保 and 杭州预算指标
     *
     * @param params -
     * @return -
     */
    private RevenueChargedDailyMedicalInsuranceEntity getSheBaoHangZhou(HisRevenuesCostReqParams params) {
        return revenueChargedDailyService.selectMedicalInsurance(new RevenueParam(params, ""));
    }

    /**
     * 青岛医保 and 杭州预算指标
     *
     * @param params -
     * @return -
     */
    private RevenueChargedDailyBudgetIndexEntity getSheBaoQingDao(HisRevenuesCostReqParams params) {
        return revenueChargedDailyService.selectBudgetIndexList(new RevenueParam(params, ""));
    }

    /**
     * 国标医保
     *
     * @param params -
     * @return -
     */
    private List<RevenueSheBaoVo> getSheBaoGuoBiao(HisRevenuesCostReqParams params) {
        if (params.getBusinessScope() == null || params.getBusinessScope().isEmpty() || params.getBusinessScope().contains(1)) {
            RevenueChargeDailySheBaoResp resp = revenueChargedDailyService.selectNationalStandardMedicalInsurance(new RevenueParam(params, ""));
            if (resp != null && resp.getRevenueSheBaoVoList() != null) {
                return resp.getRevenueSheBaoVoList();
            }
        }
        return null;
    }

    /**
     * 内蒙古医保
     *
     * @param params -
     * @return -
     */
    private List<HisRevenueKeyValuePojo> getSheBaoNeiMengGu(HisRevenuesCostReqParams params) {
        List<HisRevenueKeyValuePojo> list = new ArrayList<>();
        RevenueChargeDailySheBaoResp resp = revenueChargedDailyService
                .selectNationalStandardMedicalInsurance(new RevenueParam(params, "neimenggu"));
        if (resp != null && resp.getRevenueVoList() != null) {
            resp.getRevenueVoList().forEach(d -> {
                if ("个人现金支出".equals(d.getName())) {
                    list.add(new HisRevenueKeyValuePojo(d.getName(), d.getValue(), Boolean.TRUE));
                } else {
                    list.add(new HisRevenueKeyValuePojo(d.getName(), d.getValue()));
                }
            });
        }
        return list;
    }

    /**
     * 按人员类型统计的住院支付方式
     *
     * @param params          -
     * @param componentConfig -
     * @return -
     */
    private HisRevenueCostReportPayModeRes getPayModeByHospital(HisRevenuesCostReqParams params,
                                                                StatComponentConfig componentConfig) throws Exception {
        RevenueParam payModeHospitalParam = new RevenueParam(params, "");
        payModeHospitalParam.initData(null, "created_by");
        payModeHospitalParam.setDataRange(2);
        CompletableFuture<List<HisRevenueCostReportPayModeDao>> payModeHospitalF = executor.supplyAsync(() ->
                mapper.getPayModeByHospital(TableUtils.getHisTable(), payModeHospitalParam));
        CompletableFuture<List<RevenueChargeDailyNengMengGuSheBaoEntity>> sheBaoF = executor.supplyAsync(() ->
                hologresRevenueChareDetailMapper.getSheBaoPayModeByInsutype(TableUtils.getCisTable(), payModeHospitalParam,
                        SqlUtils.buildCashierSqlByList(params.getChargeIdList(), "last_modified_by")));
        CompletableFuture<HisRevenueCostReportPayModeTotalRes> totalF = executor.supplyAsync(() ->
                mapper.getPayModeByHospitalTotal(TableUtils.getHisTable(), payModeHospitalParam));
        CompletableFuture<Map<Integer, String>> payF = executor.supplyAsync(() ->
                query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture.allOf(payModeHospitalF, sheBaoF).join();
        List<HisRevenueCostReportPayModeDao> hospitalData = payModeHospitalF.get();
        List<RevenueChargeDailyNengMengGuSheBaoEntity> sheBaoData = sheBaoF.get();
        HisRevenueCostReportPayModeTotalRes totalRes = totalF.get();
        Map<Integer, String> payTypeMap = payF.get();
        HisRevenueCostReportPayModeRes response = new HisRevenueCostReportPayModeRes();
        response.setHeader(componentConfig.getHeader());
        if (hospitalData == null && sheBaoData == null) {
            return response;
        }
        List<HisRevenueCostReportPayModeDao> resData = new ArrayList<>();
        revenueChargedDailyHandler.handlePayModeSheBaoData(sheBaoData, resData);
        if (hospitalData != null) {
            for (HisRevenueCostReportPayModeDao pay : hospitalData) {
                String payName = ChargeHandler.handlePayModeAssembleWebChat(query, pay.getPayMode(), pay.getPaySubMode(), payTypeMap);
                pay.setPayModeName(payName == null ? "未指定" : payName);
                resData.add(pay);
            }
        }
        response.setData(resData);
        response.setTotal(totalRes == null ? new HisRevenueCostReportPayModeTotalRes(0, 0, BigDecimal.ZERO, BigDecimal.ZERO) : totalRes);
        response.setSummary(revenueChargedDailyHandler.handlePayModeSummary(resData));
        return response;
    }

    /**
     * 按人员类型统计的门诊支付方式
     *
     * @param params          -
     * @param componentConfig -
     * @return -
     */
    private HisRevenueCostReportPayModeRes getPayModeByOutpatient(HisRevenuesCostReqParams params,
                                                                  StatComponentConfig componentConfig) throws Exception {
        RevenueParam payModeOutpatientParam = new RevenueParam(params, "");
        payModeOutpatientParam.setDataRange(1);
        HisRevenueCostReportPayModeRes response = revenueChargedDailyService.getPayModeByRegistration(payModeOutpatientParam);
        response.setHeader(componentConfig.getHeader());
        return response;
    }

    /**
     * 按人员类型统计的挂号支付方式
     *
     * @param params          -
     * @param componentConfig -
     * @return -
     */
    private HisRevenueCostReportPayModeRes getPayModeByRegistration(HisRevenuesCostReqParams params,
                                                                    StatComponentConfig componentConfig) throws Exception {
        RevenueParam payModeRegistrationParam = new RevenueParam(params, "");
        payModeRegistrationParam.setDataRange(0);
        HisRevenueCostReportPayModeRes response = revenueChargedDailyService.getPayModeByRegistration(payModeRegistrationParam);
        response.setHeader(componentConfig.getHeader());
        return response;
    }

    /**
     * 根据业务获取支付模式。
     * 该方法接收一个HisRevenuesCostReqParams参数，用于构建HisReciptSummaryReq请求对象，
     * 初始化请求参数后，调用selectReceiptSummary方法获取V2StatResponse响应。
     *
     * @param params HisRevenuesCostReqParams类型的请求参数
     * @return V2StatResponse类型的支付模式响应
     * @throws Exception 如果过程中发生异常，则抛出
     */
    private V2StatResponse getPayModeByBusiness(HisRevenuesCostReqParams params) throws Exception {
        // 构建请求参数并设置业务范围
        HisReciptSummaryReq reqParam = new HisReciptSummaryReq(params);
        reqParam.initParams();
        V2StatResponse v2StatResponse = selectReceiptSummary(reqParam);
        if (params.getBusinessScope() == null || params.getBusinessScope().isEmpty()) {
            params.setBusinessScope(Arrays.asList(1, 2, 3));
        }
        List<HisReciptSummaryResp> data = v2StatResponse.getData();
        HisReciptSummaryResp summary = (HisReciptSummaryResp) v2StatResponse.getSummary();
        data.forEach(d -> d.setTotalAmount(BigDecimal.ZERO));
        summary.setTotalAmount(BigDecimal.ZERO);
        List<TableHeaderEmployeeItem> newHeader = new ArrayList<>();
        newHeader.add(TableHeaderUtils.genTableHeaderEmployeeItem("payModeName", "收款方式", 120, 0, null, null));
        if (params.getBusinessScope().contains(1)) {
            newHeader.add(TableHeaderUtils.genTableHeaderEmployeeItem("outpatientAmount", "门诊", 90, 1, null, null));
            data.forEach(d -> d.setTotalAmount(d.getOutpatientAmount().add(d.getTotalAmount())));
            summary.setTotalAmount(summary.getOutpatientAmount().add(summary.getTotalAmount()));
        }
        if (params.getBusinessScope().contains(2)) {
            TableHeaderEmployeeItem hospital = TableHeaderUtils.genTableHeaderEmployeeItem("hospital", "住院", 90, 2, null, null);
            List<TableHeaderEmployeeItem> children = new ArrayList<>();
            TableHeaderEmployeeItem hospitalCharged = TableHeaderUtils.genTableHeaderEmployeeItem("dischargeAmount", "住院结算", 90, 2, null, null);
            hospitalCharged.setParentKey("hospital");
            children.add(hospitalCharged);
            TableHeaderEmployeeItem depositRemain = TableHeaderUtils.genTableHeaderEmployeeItem("depositSettleRefundAmount", "结算余退", 90, 2, null, null);
            depositRemain.setParentKey("hospital");
            children.add(depositRemain);
            hospital.setColumnChildren(children);
            newHeader.add(hospital);
            newHeader.add(TableHeaderUtils.genTableHeaderEmployeeItem("depositAmount", "住院押金", 90, 3, null, null));
            data.forEach(d -> d.setTotalAmount(d.getDischargeAmount().add(d.getDepositSettleRefundAmount()).add(d.getDepositAmount()).add(d.getTotalAmount())));
            summary.setTotalAmount(summary.getDischargeAmount().add(summary.getDepositSettleRefundAmount()).add(summary.getDepositAmount()).add(summary.getTotalAmount()));
        }
        if (params.getBusinessScope().contains(3)) {
            newHeader.add(TableHeaderUtils.genTableHeaderEmployeeItem("peChargeAmount", "体检", 90, 4, null, null));
            data.forEach(d -> d.setTotalAmount(d.getPeChargeAmount().add(d.getTotalAmount())));
            summary.setTotalAmount(summary.getPeChargeAmount().add(summary.getTotalAmount()));
        }
        if (params.getBusinessScope().contains(1)) {
            newHeader.add(TableHeaderUtils.genTableHeaderEmployeeItem("memberAmount", "会员充值", 90, 5, null, null));
            newHeader.add(TableHeaderUtils.genTableHeaderEmployeeItem("promotionCardAmount", "卡项充值", 90, 6, null, null));
            data.forEach(d -> d.setTotalAmount(d.getMemberAmount().add(d.getPromotionCardAmount()).add(d.getTotalAmount())));
            summary.setTotalAmount(summary.getMemberAmount().add(summary.getPromotionCardAmount()).add(summary.getTotalAmount()));
        }
        newHeader.add(TableHeaderUtils.genTableHeaderEmployeeItem("totalAmount", "合计", 90, 7, null, null));
        v2StatResponse.setHeader(newHeader);
        return v2StatResponse;
    }

    /**
     * 日报收费方式
     *
     * @param params -
     * @return -
     * @throws Exception -
     */
    private List<HisRevenueKeyValuePojo> getHisPayModeList(HisRevenuesCostReqParams params) throws Exception {
        //门诊
        CompletableFuture<List<RevenueVo>> payModeFuture = executor.supplyAsync(() ->
                revenueChargedDailyService.selectPayModeList(new RevenueParam(params, "")));
        //住院
        CompletableFuture<List<HisRevenueKeyValuePojo>> hisPayModeFuture = executor.supplyAsync(() ->
                selectHisPayModeList(params));
        //体检
        CompletableFuture<HisRevenueCostPhysicalExaminationDayReportRsp> physicalPayModeFuture = executor.supplyAsync(() ->
                hisPeChargeDataHandler.selectPhysicalExaminationChargeDayReport(params));

        if (params.getBusinessScope() == null || params.getBusinessScope().isEmpty()) {
            params.setBusinessScope(Arrays.asList(1,2,3));
        }
        Map<String, HisRevenueKeyValuePojo> mergePayModeMap = new HashMap<>();
        if (params.getBusinessScope().contains(1)) {
            payModeFuture.join();
            List<RevenueVo> payModes = payModeFuture.get();
            if (payModes != null && !payModes.isEmpty()) {
                mergePayModeMap = payModes.stream().collect(Collectors.toMap(RevenueVo::getName, v -> new HisRevenueKeyValuePojo(v.getName(), v.getValue())));
            }
        }
        if (params.getBusinessScope().contains(2)) {
            hisPayModeFuture.join();
            List<HisRevenueKeyValuePojo> hisPayModes = hisPayModeFuture.get();
            if (hisPayModes != null && !hisPayModes.isEmpty()) {
                for (HisRevenueKeyValuePojo hisPayMode : hisPayModes) {
                    hisRevenuesCostHandler.mergeChargeDailyData(mergePayModeMap, hisPayMode);
                }
            }
        }
        if (params.getBusinessScope().contains(3)) {
            physicalPayModeFuture.join();
            HisRevenueCostPhysicalExaminationDayReportRsp rsp = physicalPayModeFuture.get();
            if (rsp != null && rsp.getPayModeList() != null && !rsp.getPayModeList().isEmpty()) {
                for (RevenueVo phyPayMode : rsp.getPayModeList()) {
                    hisRevenuesCostHandler.mergeChargeDailyData(mergePayModeMap, new HisRevenueKeyValuePojo(phyPayMode.getName(), phyPayMode.getValue()));
                }
            }
        }
        return mergePayModeMap.values().stream().sorted(Comparator.comparing(HisRevenueKeyValuePojo::getName)).collect(Collectors.toList());
    }

    /**
     * 获取日报费用分类
     *
     * @param params -
     * @return -
     * @throws Exception -
     */
    private List<HisRevenueKeyValuePojo> getHisFeeTypeList(HisRevenuesCostReqParams params) throws Exception {
        List<HisRevenueKeyValuePojo> resultList = new ArrayList<>();
        // 准备各业务范围的Future
        //门诊
        CompletableFuture<List<RevenueVo>> feeTypeFuture = executor.supplyAsync(() ->
                revenueChargedDailyService.selectFeeTypeList(new RevenueParam(params, "")));
        CompletableFuture<RevenueChargedDailySummaryDao> summaryFuture = executor.supplyAsync(() ->
                revenueChargedDailyService.selectSummary(new RevenueParam(params, "")));
        // 住院
        CompletableFuture<List<HisRevenueKeyValuePojo>> hisFeeTypeFuture = executor.supplyAsync(() ->
                selectHisFeeTypeList(params));
        CompletableFuture<HisRevenuesCostChargeReportRes> hisSummaryFuture = executor.supplyAsync(() ->
                mapper.selectChargeReportSummary(TableUtils.getHisTable(), params));
        //体检
        CompletableFuture<List<HisRevenueKeyValuePojo>> physicalFeeTypeFuture = executor.supplyAsync(() ->
                hisPeChargeDataHandler.selectPhysicalExaminationChargeDayReportFeeTypeId(params));

        // 如果业务范围为空，获取所有数据
        if (params.getBusinessScope() == null || params.getBusinessScope().isEmpty()) {
            params.setBusinessScope(Arrays.asList(1,2,3));
        }
        // 将合并后的数据转换为结果列表
        Map<String, HisRevenueKeyValuePojo> mergedFeeMap = new HashMap<>();

        if (params.getBusinessScope().contains(1)) {
            CompletableFuture.allOf(feeTypeFuture, summaryFuture).join();
            List<RevenueVo> feeTypes = feeTypeFuture.get();
            RevenueChargedDailySummaryDao outpatientSummary = summaryFuture.get();

            // 处理门诊费用类型数据
            if (feeTypes != null && !feeTypes.isEmpty()) {
                mergedFeeMap = feeTypes.stream().collect(Collectors.toMap(RevenueVo::getName, v -> new HisRevenueKeyValuePojo(v.getName(), v.getValue())));
            }
            if (params.getBusinessScope().size() == 3) {
                resultList.add(new HisRevenueKeyValuePojo("门诊结算", outpatientSummary == null ? BigDecimal.ZERO : outpatientSummary.getTotalAmount()));
            } else {
                resultList.add(new HisRevenueKeyValuePojo("门诊结算", outpatientSummary == null ? BigDecimal.ZERO : outpatientSummary.getTotalAmount().subtract(outpatientSummary.getTotalRecharge())));
                resultList.add(new HisRevenueKeyValuePojo("充值合计", outpatientSummary == null ? BigDecimal.ZERO : outpatientSummary.getTotalRecharge()));
            }
            mergedFeeMap.put("结算合计", new HisRevenueKeyValuePojo("结算合计", outpatientSummary == null ? BigDecimal.ZERO : outpatientSummary.getTotalAmount()));
        }

        if (params.getBusinessScope().contains(2)) {
            CompletableFuture.allOf(hisFeeTypeFuture, hisSummaryFuture).join();
            List<HisRevenueKeyValuePojo> hisFeeTypes = hisFeeTypeFuture.get();
            HisRevenuesCostChargeReportRes inpatientSummary = hisSummaryFuture.get();

            // 处理住院费用类型数据
            if (hisFeeTypes != null && !hisFeeTypes.isEmpty()) {
                for (HisRevenueKeyValuePojo item : hisFeeTypes) {
                    hisRevenuesCostHandler.mergeChargeDailyData(mergedFeeMap, item);
                }
            }

            // 添加住院汇总数据
            if (params.getBusinessScope().size() == 3) {
                resultList.add(new HisRevenueKeyValuePojo("住院结算", inpatientSummary == null ? BigDecimal.ZERO : inpatientSummary.getSettlementTotalPrice()));
            } else {
                resultList.add(new HisRevenueKeyValuePojo("住院结算", inpatientSummary == null ? BigDecimal.ZERO : inpatientSummary.getSettlementTotalPrice().subtract(inpatientSummary.getDepositSettlementDeductPrice())));
                resultList.add(new HisRevenueKeyValuePojo("押金结算抵扣", inpatientSummary == null ? BigDecimal.ZERO : inpatientSummary.getDepositSettlementDeductPrice()));
            }
            hisRevenuesCostHandler.mergeChargeDailyData(mergedFeeMap, new HisRevenueKeyValuePojo("结算合计", inpatientSummary == null ? BigDecimal.ZERO : inpatientSummary.getSettlementTotalPrice()));
        }

        if (params.getBusinessScope().contains(3)) {
            physicalFeeTypeFuture.join();
            List<HisRevenueKeyValuePojo> physicalFeeTypes = physicalFeeTypeFuture.get();

            // 处理体检费用类型数据
            if (physicalFeeTypes != null && !physicalFeeTypes.isEmpty()) {
                for (HisRevenueKeyValuePojo item : physicalFeeTypes) {
                    if ("结算合计".equals(item.getName())) {
                        continue;
                    }
                    if ("体检结算".equals(item.getName())) {
                        resultList.add(new HisRevenueKeyValuePojo("体检结算", item.getValue()));
                        hisRevenuesCostHandler.mergeChargeDailyData(mergedFeeMap, new HisRevenueKeyValuePojo("结算合计", item.getValue()));
                        continue;
                    }
                    hisRevenuesCostHandler.mergeChargeDailyData(mergedFeeMap, item);
                }
            }
        }

        if (mergedFeeMap.containsKey("结算合计")) {
            resultList.add(new HisRevenueKeyValuePojo("结算合计", mergedFeeMap.remove("结算合计").getValue(), Boolean.TRUE));
        }

        resultList.addAll(mergedFeeMap.values().stream().sorted(Comparator.comparing(KeyValuePojo::getName)).collect(Collectors.toList()));
        return resultList;
    }

    private List<HisRevenueKeyValuePojo> selectHisFeeTypeList(HisRevenuesCostReqParams params) {
        List<HisRevenuesCostFeeTypeRes> hisFeeTypeList = mapper.selectVerticalSummy(TableUtils.getHisTable(), params);
        Map<Long, V2GoodsFeeType> feeTypeMap = query.selectAdviceFeeType(params.getChainId());
        return hisRevenuesCostHandler.handleHisFeeTypeList(hisFeeTypeList, feeTypeMap);
    }

    private List<HisRevenueKeyValuePojo> selectHisPayModeList(HisRevenuesCostReqParams params) {
        List<HisRevenuesCostPayModeRes> hisPayModeList = mapper.selectAmountByPayMode(TableUtils.getHisTable(), params);
//        HisRevenuesCostSheBaoPayModeRes sheBaoPayModeList = hologresRevenueChareDetailMapper.selectAmountBySheBaoPayMode(TableUtils.getCisTable(), params);
        Map<Integer, String> payTypeMap = query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
        Map<String, BigDecimal> payModeResMap = new TreeMap<>();
//        if (sheBaoPayModeList != null) {
//            sheBaoPayModeList.pretty(payModeResMap);
//        }
        for (HisRevenuesCostPayModeRes pay : hisPayModeList) {
            String payName = ChargeHandler.handlePayModeAssembleWebChat(query, pay.getPayMode(), pay.getPaySubMode(), payTypeMap);
            payName = payName == null ? "未指定" : payName;
            payModeResMap.put(payName, payModeResMap.getOrDefault(payName, BigDecimal.ZERO).add(pay.getPrice()));
        }
        return payModeResMap.entrySet().stream()
                .map(entry -> new HisRevenueKeyValuePojo(entry.getKey(), entry.getValue().setScale(2, RoundingMode.HALF_UP)))
                .collect(Collectors.toList());
    }

    @Override
    public void selectChargeDayReportDetailExport(HttpServletResponse response, HisRevenuesCostReqParams params) throws Exception {
        String fileName = params.getName() + params.getBeginDate() + "_" + params.getEndDate() + ".xlsx";
        List<ExcelUtils.AbcExcelSheet> sheets = exportRevenueCostChargeDaily(params);
        ExcelUtils.export(fileName, response, sheets);
    }

    @Override
    public List<ExcelUtils.AbcExcelSheet> exportRevenueCostChargeDaily(HisRevenuesCostReqParams reqParams) throws Exception {
        List<HisRevenuesCostChargeReportDetailRes> detailResList = selectChargeDayReportDetail(reqParams);
        Map<String, Object> detailResMap = detailResList.stream()
                .collect(Collectors.toMap(HisRevenuesCostChargeReportDetailRes::getKey, HisRevenuesCostChargeReportDetailRes::getValue));
        List<HisRevenueCostReportDailyExportResult> exportResults = new ArrayList<>();
        for (String modeName : reqParams.getComponents()) {
            if ("fee_type".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handleFeeTypeExportData(reqParams, (List<HisRevenueKeyValuePojo>) detailResMap.get(modeName)));
            } else if ("pay_mode".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.reportKeyValueHandlerV2((List<HisRevenueKeyValuePojo>) detailResMap.get(modeName), "收费方式"));
            } else if ("pay_mode_by_business".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handlePayModeByBusinessExportData(reqParams, (V2StatResponse) detailResMap.get(modeName)));
            } else if ("pay_mode_by_registration".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handlePayModeData((HisRevenueCostReportPayModeRes) detailResMap.get(modeName), "registration"));
            } else if ("pay_mode_by_outpatient".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handlePayModeData((HisRevenueCostReportPayModeRes) detailResMap.get(modeName), "outpatient"));
            } else if ("pay_mode_by_hospital".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handlePayModeData((HisRevenueCostReportPayModeRes) detailResMap.get(modeName), "hospital"));
            } else if ("shebao_guobiao".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handleSheBaoGuoBiao((List<RevenueSheBaoVo>) detailResMap.get(modeName)));
            } else if ("shebao_hangzhou".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handleSheBaoHangZhou((RevenueChargedDailyMedicalInsuranceEntity) detailResMap.get(modeName)));
            } else if ("shebao_qingdao".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handleSheBaoQingDao((RevenueChargedDailyBudgetIndexEntity) detailResMap.get(modeName)));
            } else if ("shebao_neimenggu".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.reportKeyValueHandlerV2((List<HisRevenueKeyValuePojo>) detailResMap.get(modeName), "医保"));
            } else if ("budget_index".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handleBudgetIndex((RevenueChargedDailyBudgetIndexEntity) detailResMap.get(modeName)));
            } else if ("invoice_total".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handleInvoiceTotal((List<List<KeyValuePojo>>) detailResMap.get(modeName)));
            } else if ("invoice_detail".equals(modeName)) {
                exportResults.addAll(hisRevenuesCostHandler.handleInvoiceDetail((V2StatResponse) detailResMap.get(modeName)));
            }
        }

        List<ExcelUtils.AbcExcelSheet> excelSheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet details = new ExcelUtils.AbcExcelSheet();
        details.setName(reqParams.getName());
        details.setData(exportResults);
        details.setHeadStyleClass(HisRevenueCostReportDailyExportResult.class);
        excelSheets.add(details);
        return excelSheets;
    }


    @Override
    public HisRevenueCostChargeReportSelectRsp selectChargeDayReportSelect(HisRevenuesCostReqParams params) throws Exception {
        params.initBeginDateAndEndDate();
        HisRevenueCostChargeReportSelectRsp resp = new HisRevenueCostChargeReportSelectRsp();
        CompletableFuture<Map<String, Employee>> employeeF = executor.supplyAsync(() ->
                query.queryEmployeeByChainId(params.getChainId()));
        CompletableFuture<Map<String, String>> departmentF = executor.supplyAsync(() ->
                query.queryDepartmentNameByOrgan(params.getChainId(), params.getClinicId()));
        //门诊
        CompletableFuture<List<String>> chargeTransactionCashierF = executor.supplyAsync(() ->
                revenueChargedDailyService.selectChargeTransactionCashierIds(new RevenueParam(params, "")));
        CompletableFuture<List<String>> chargeTransactionDepartmentF = executor.supplyAsync(() ->
                revenueChargedDailyService.selectChargeTransactionDepartmentIds(new RevenueParam(params, "")));
        CompletableFuture<List<String>> invoiceDailyCashierF = executor.supplyAsync(() ->
                revenueChargedDailyService.selectInvoiceDailyCashierIds(new RevenueParam(params, "")));

        // 住院
        CompletableFuture<List<String>> hisChargeSettleCashierF = executor.supplyAsync(() ->
                mapper.selectHisChargeSettleCashierIds(TableUtils.getHisTable(), params));
        CompletableFuture<List<String>> hisChargeSettleDepartmentF = executor.supplyAsync(() ->
                mapper.selectHisChargeSettleDepartmentIds(TableUtils.getHisTable(), params));

        //体检
        CompletableFuture<List<String>> peCashierF = executor.supplyAsync(() ->
                hisPeChargeDataHandler.selectPhysicalExaminationCashierIds(params));
        CompletableFuture<List<String>> peDepartmentF = executor.supplyAsync(() ->
                hisPeChargeDataHandler.selectPhysicalExaminationDepartmentIds(params));

        // 住院押金
        HisReciptSummaryReq reqParam = new HisReciptSummaryReq(params);
        reqParam.initParams();
        CompletableFuture<List<String>> hisDepositCashierF = executor.supplyAsync(() ->
                mapper.selectHisDepositCashierIds(TableUtils.getHisTable(), reqParam));
        CompletableFuture<List<String>> hisDepositDepartmentF = executor.supplyAsync(() ->
                mapper.selectHisDepositDepartmentIds(TableUtils.getHisTable(), reqParam));
        CompletableFuture<List<String>> peChargeCashierF = executor.supplyAsync(() ->
                mapper.selectPeChargeTransactionCashierIds(TableUtils.getHisTable(), reqParam));

        if (params.getType() == null) {
            CompletableFuture.allOf(employeeF, chargeTransactionCashierF, chargeTransactionDepartmentF, invoiceDailyCashierF,
                    hisChargeSettleCashierF, hisChargeSettleDepartmentF, peCashierF, peDepartmentF, hisDepositCashierF,
                    hisDepositDepartmentF, peChargeCashierF).join();
            resp.setChargeIds(HisRevenuesCostHandler.handleCashierEmployee(employeeF.get(), chargeTransactionCashierF.get(),
                    invoiceDailyCashierF.get(), hisChargeSettleCashierF.get(), peCashierF.get(), hisDepositCashierF.get(),
                    peChargeCashierF.get()));
            resp.setDepartments(HisRevenuesCostHandler.handleDepartment(departmentF.get(), chargeTransactionDepartmentF.get(),
                    hisChargeSettleDepartmentF.get(), peDepartmentF.get(), hisDepositDepartmentF.get()));
        } else if (params.getType() == 1) {
            // 门诊收费员
            CompletableFuture.allOf(employeeF, chargeTransactionCashierF, invoiceDailyCashierF).join();
            resp.setChargeIds(HisRevenuesCostHandler.handleCashierEmployee(employeeF.get(), chargeTransactionCashierF.get(),
                    invoiceDailyCashierF.get()));
        } else if (params.getType() == 2) {
            // 住院收费员
            CompletableFuture.allOf(employeeF, hisChargeSettleCashierF, hisChargeSettleDepartmentF).join();
            resp.setChargeIds(HisRevenuesCostHandler.handleCashierEmployee(employeeF.get(), hisChargeSettleCashierF.get()));
            resp.setDepartments(HisRevenuesCostHandler.handleDepartment(departmentF.get(), hisChargeSettleDepartmentF.get()));
        } else if (params.getType() == 3) {
            // 体检收费员
            CompletableFuture.allOf(employeeF, peCashierF, peDepartmentF).join();
            resp.setChargeIds(HisRevenuesCostHandler.handleCashierEmployee(employeeF.get(), peCashierF.get()));
        } else if (params.getType() == 4) {
            // 收费员日报筛选
            CompletableFuture.allOf(employeeF, chargeTransactionCashierF, hisChargeSettleCashierF, peChargeCashierF).join();
            resp.setChargeIds(HisRevenuesCostHandler.handleCashierEmployee(employeeF.get(), chargeTransactionCashierF.get(),
                    hisChargeSettleCashierF.get(), peChargeCashierF.get()));
        }
        return resp;
    }
}
