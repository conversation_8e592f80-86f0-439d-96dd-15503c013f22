package cn.abc.flink.stat.service.cis.achievement.charge.entity;


import java.math.BigDecimal;

public class AchievementChargeDepartmentFeeAmountEntity {
    private String chainId;
    private String clinicId;
    private String departmentId;
    private Byte isCopywriter;
    private String classifyLevel1;
    private Integer classifyLevel2;
    private BigDecimal originPrice = BigDecimal.ZERO;
    private BigDecimal receivedPrice = BigDecimal.ZERO;
    private BigDecimal costPrice = BigDecimal.ZERO;
    private BigDecimal grossProfit;
    private BigDecimal grossProfitRate;
    private BigDecimal deductPrice = BigDecimal.ZERO;
    private Long feeTypeId;

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getDepartmentId() {
        return this.departmentId;
    }

    public Byte getIsCopywriter() {
        return this.isCopywriter;
    }

    public String getClassifyLevel1() {
        return this.classifyLevel1;
    }

    public Integer getClassifyLevel2() {
        return this.classifyLevel2;
    }

    public BigDecimal getGrossProfit() {
        return this.grossProfit;
    }

    public BigDecimal getGrossProfitRate() {
        return this.grossProfitRate;
    }

    public Long getFeeTypeId() {
        return this.feeTypeId;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public void setIsCopywriter(Byte isCopywriter) {
        this.isCopywriter = isCopywriter;
    }

    public void setClassifyLevel1(String classifyLevel1) {
        this.classifyLevel1 = classifyLevel1;
    }

    public void setClassifyLevel2(Integer classifyLevel2) {
        this.classifyLevel2 = classifyLevel2;
    }

    public void setGrossProfit(BigDecimal grossProfit) {
        this.grossProfit = grossProfit;
    }

    public void setGrossProfitRate(BigDecimal grossProfitRate) {
        this.grossProfitRate = grossProfitRate;
    }

    public void setFeeTypeId(Long feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public BigDecimal getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    public BigDecimal getReceivedPrice() {
        return receivedPrice;
    }

    public void setReceivedPrice(BigDecimal receivedPrice) {
        this.receivedPrice = receivedPrice;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getDeductPrice() {
        return deductPrice;
    }

    public void setDeductPrice(BigDecimal deductPrice) {
        this.deductPrice = deductPrice;
    }
}
