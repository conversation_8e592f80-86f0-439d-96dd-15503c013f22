package cn.abc.flink.stat.service.cis.revenue.charge.daily.handler;

import cn.abc.flink.stat.common.SpecialUtil;
import cn.abc.flink.stat.common.SqlUtils;
import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnMedicareDoctorChargeMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnOperationCashierMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnOperationCashierSelectMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnRevenueChargeDetailMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresMedicareDoctorChargeMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresOperationCashierMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresRevenueChareDetailMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.service.cis.handler.ChargeHandler;
import cn.abc.flink.stat.service.cis.handler.SelectHandler;
import cn.abc.flink.stat.service.cis.invoice.entity.InvoiceDailyEntity;
import cn.abc.flink.stat.service.cis.medicare.doctor.charge.entity.MedicareDoctorChargeBaseDao;
import cn.abc.flink.stat.service.cis.medicare.doctor.charge.entity.MedicareDoctorChargeDose;
import cn.abc.flink.stat.service.cis.operation.domain.CashierFeeClassify;
import cn.abc.flink.stat.service.cis.operation.domain.CashierPayMode;
import cn.abc.flink.stat.service.cis.operation.domain.OperationChargeReqParams;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargeDailyNengMengGuSheBaoEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargeDailySheBaoResp;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyBudgetIndexEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyChineseDoseEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyFeeEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyMedicalInsuranceDao;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyMedicalInsuranceEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyPayTypeEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailySummaryDao;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueExportResult;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueParam;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueSheBaoVo;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueVo;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeDao;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeTotalRes;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisShebaoFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisShebaoStatFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.GetMedTypeReqBody;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.GetMedTypeRspBody;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.ShebaoStatSummaryReportRsp;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/23 16:07
 */
@Component
public class RevenueChargedDailyHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RevenueChargedDailyHandler.class);
    private static final Map<String, String> FEE_MAPPING = new HashMap<>();
    private static final Map<Integer, String> PAY_MAPPING = new HashMap<>();
    private static final ObjectMapper SOBJECTMAPPER = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * @param
     * @param object -
     * @return
     * @return java.lang.String
     * @Description:
     * @Author: zs
     * @Date: 2022/11/24 17:09
     */
    public static String dump(Object object) {
        String json = null;
        if (object == null) {
            return null;
        }
        try {
            json = SOBJECTMAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return json;
    }

    @Autowired
    private DimensionQuery query;

    static {
        FEE_MAPPING.put("1-1", "西药");
        FEE_MAPPING.put("1-3", "中成药");
        FEE_MAPPING.put("1-12", "中药饮片");
        FEE_MAPPING.put("1-13", "中药颗粒");
        FEE_MAPPING.put("4-1", "治疗");
        FEE_MAPPING.put("4-2", "理疗");
        FEE_MAPPING.put("3-2", "检查");
        FEE_MAPPING.put("3-1", "检验");
        FEE_MAPPING.put("5-0", "挂号费");
        FEE_MAPPING.put("5-1", "挂号费");
        FEE_MAPPING.put("2-1", "医疗器械");
        FEE_MAPPING.put("7-0", "商品");
        FEE_MAPPING.put("7-1", "商品");
        FEE_MAPPING.put("7-2", "商品");
        FEE_MAPPING.put("7-3", "商品");
        FEE_MAPPING.put("7-4", "商品");
        FEE_MAPPING.put("14", "加工费");
        FEE_MAPPING.put("13-0", "快递费");
        FEE_MAPPING.put("13-1", "快递费");
        FEE_MAPPING.put("-2", "会员充值");
        FEE_MAPPING.put("18-0", "卡项充值本金");
        FEE_MAPPING.put("24-1", "镜片");
        FEE_MAPPING.put("24-2", "镜架");
        FEE_MAPPING.put("24-3", "角膜塑形镜");
        FEE_MAPPING.put("24-4", "软性亲水镜");
        FEE_MAPPING.put("24-5", "硬性透氧镜");
        FEE_MAPPING.put("24-6", "太阳镜");


        PAY_MAPPING.put(CommonConstants.NUMBER_NINETEEN, "payABC");
        PAY_MAPPING.put(CommonConstants.NUMBER_FIVE, "payMedicalInsurance");
        PAY_MAPPING.put(CommonConstants.NUMBER_TWO, "payWeChat");
        PAY_MAPPING.put(CommonConstants.NUMBER_THREE, "payAlipay");
        PAY_MAPPING.put(CommonConstants.NUMBER_ONE, "payCash");
        PAY_MAPPING.put(CommonConstants.NUMBER_FOUR, "payBankCard");
        PAY_MAPPING.put(CommonConstants.NUMBER_SIX, "payMemberCard");
        PAY_MAPPING.put(CommonConstants.NUMBER_SEVEN, "payCoupon");
    }

    @Resource
    private HologresOperationCashierMapper hologresOperationCashierMapper;

    @Resource
    private ArnOperationCashierSelectMapper arnOperationCashierSelectMapper;

    @Resource
    private ArnOperationCashierMapper arnOperationCashierMapper;

    @Resource
    private HologresMedicareDoctorChargeMapper hologresMedicareDoctorChargeMapper;

    @Resource
    private ArnMedicareDoctorChargeMapper arnMedicareDoctorChargeMapper;

    @Resource
    private ArnRevenueChargeDetailMapper arnRevenueChargeDetailMapper;

    @Resource
    private HologresRevenueChareDetailMapper hologresRevenueChareDetailMapper;

    @Resource
    private StoreUtils storeUtils;

    @Resource
    private AbcCisShebaoFeignClient abcCisShebaoFeignClient;

    @Resource
    private AbcCisShebaoStatFeignClient abcCisShebaoStatFeignClient;

    @Autowired
    private DimensionQuery dimensionQuery;

    /**
     * 日报汇总
     *
     * @param param   -
     * @param endDate 结束日期
     * @return -
     */
    public RevenueChargedDailySummaryDao selectSummary(RevenueParam param, String endDate) {
        return storeUtils.getMapper(param.getBeginDate(), param.getEndDate(),
                arnRevenueChargeDetailMapper, hologresRevenueChareDetailMapper)
                .selectSummary(TableUtils.getCisTable(), param, endDate);
    }

    /**
     * 按费用分类统计
     *
     * @param param -
     * @return -
     */
    public List<RevenueVo> selectFeeTypeList(RevenueParam param) {
        try {
            String feeType1 = SelectHandler.buildChargeFeeClassify1Sql(param.getFeeType1());
            OperationChargeReqParams operaParam = JSON.parseObject(JSON.toJSONString(param), OperationChargeReqParams.class);
            operaParam.setChainId(param.getChainId());
            operaParam.setClinicId(param.getClinicId());
            operaParam.setBeginDate(param.getBeginDate());
            operaParam.setEndDate(param.getEndDate());
            operaParam.setHisType(param.getHisType());
            operaParam.setSellerId(param.getSellerId());
            operaParam.setCashierSql(param.getCashierIdSql());
            operaParam.setDepartmentId(param.getDepartmentId());
            operaParam.setFeeType1(feeType1);
            operaParam.setComposeSql(param.getComposeSql());
            operaParam.initBeginDateAndEndDate();
            operaParam.initDs();
            List<CashierFeeClassify> cashierFeeClassifies;
            Map<Long, V2GoodsFeeType> feeTypeMap = null;
            if (operaParam.getHisType() != null && "100".equals(operaParam.getHisType())) {
                CompletableFuture<List<CashierFeeClassify>> adviceFeeClassifyListFuture =
                        CompletableFuture.supplyAsync(() -> storeUtils.getMapper(operaParam.getBeginDate(),
                                operaParam.getEndDate(), arnOperationCashierMapper, hologresOperationCashierMapper)
                                .fetchAdviceFeeClassify(TableUtils.getBisTable(),
                                        TableUtils.getCisTable(), operaParam, param.getGroup()));
                CompletableFuture<Map<Long, V2GoodsFeeType>> adviceFeeFuture =
                        CompletableFuture.supplyAsync(() -> query.selectAdviceFeeType(operaParam.getChainId()));
                CompletableFuture.allOf(adviceFeeClassifyListFuture, adviceFeeFuture).join();
                cashierFeeClassifies = adviceFeeClassifyListFuture.get();
                feeTypeMap = adviceFeeFuture.get();
            } else {
                CompletableFuture<List<CashierFeeClassify>> cashierFeeClassifyListFuture =
                        CompletableFuture.supplyAsync(() -> storeUtils.getMapper(operaParam.getBeginDate(),
                                operaParam.getEndDate(), arnOperationCashierMapper, hologresOperationCashierMapper)
                                .fetchFeeClassify(TableUtils.getBisTable(),
                                        TableUtils.getCisTable(), operaParam, param.getGroup()));
                cashierFeeClassifyListFuture.join();
                cashierFeeClassifies = cashierFeeClassifyListFuture.get();
            }
            return collectLevelData(cashierFeeClassifies, feeTypeMap, operaParam);
        } catch (Exception e) {
            LOGGER.error("selectFeeTypeList方法异常", e);
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    /**
     * 按收费方式统计
     *
     * @param param -
     * @return -
     */
    public List<RevenueVo> selectPayModeList(RevenueParam param) {
        Map<Integer, String> customCharge
                = query.queryPayTypeTextByChainIdOrClinicId(param.getChainId(), param.getClinicId());
        List<CashierPayMode> cashierPayModeList;
        try {
            OperationChargeReqParams param1 = new OperationChargeReqParams();
            param1.setChainId(param.getChainId());
            param1.setClinicId(param.getClinicId());
            param1.setBeginDate(param.getBeginDate());
            param1.setEndDate(param.getEndDate());
            param1.setSellerId(param.getSellerId());
            param1.setCashierSql(param.getCashierIdSql());
            param1.setHisType(param.getHisType());
            param1.setIsComposeShareEqually(param.getIsComposeShareEqually());
            param1.setArrearsStatTiming(param.getArrearsStatTiming());
            param1.initBeginDateAndEndDate();
            param1.initDs();
            cashierPayModeList = storeUtils.getMapper(param.getBeginDate(),
                    param.getEndDate(), arnOperationCashierMapper, hologresOperationCashierMapper)
                    .fetchChargeDailyPayMode(TableUtils.getBisTable(), TableUtils.getCisTable(), param1);
            return dealPayModeData(cashierPayModeList, customCharge);
        } catch (Exception e) {
            LOGGER.error("selectPayModeList方法异常", e);
            e.printStackTrace();
        }
        return new ArrayList<>();
    }


    /**
     * 预算指标诊所整体指标中药帖数/帖均统计
     *
     * @return 帖数/帖均对象
     */
    public RevenueChargedDailyChineseDoseEntity selectChineseDoseList(RevenueParam reqParams) {
        RevenueChargedDailyChineseDoseEntity chineseDoseEntity = new RevenueChargedDailyChineseDoseEntity();
        String endDate = TimeUtils.appendEnd(reqParams.getEndDate());
        try {
            CompletableFuture<MedicareDoctorChargeBaseDao> chineseBaseF = CompletableFuture.supplyAsync(() ->
                    storeUtils.getMapper(reqParams.getBeginDate(), endDate, arnMedicareDoctorChargeMapper,
                            hologresMedicareDoctorChargeMapper)
                            .selectChineseAmount(TableUtils.getCisTable(), reqParams, endDate));

            CompletableFuture<MedicareDoctorChargeDose> chineseBoseF = CompletableFuture.supplyAsync(() ->
                    storeUtils.getMapper(reqParams.getBeginDate(), endDate, arnMedicareDoctorChargeMapper,
                            hologresMedicareDoctorChargeMapper).selectChineseDoseCount(TableUtils.getCisTable(),
                            reqParams, endDate));

            chineseBaseF.join();
            MedicareDoctorChargeBaseDao baseDao = chineseBaseF.get();
            BigDecimal totalChineseAmount = (baseDao != null) ? baseDao.getChineseAmount()
                    .setScale(CommonConstants.NUMBER_TWO, RoundingMode.HALF_UP) : BigDecimal.ZERO;

            chineseBoseF.join();
            MedicareDoctorChargeDose chargeDose = chineseBoseF.get();
            BigDecimal totalDoseCount = (chargeDose != null)
                    ? BigDecimal.valueOf(chargeDose.getDoseCount()) : BigDecimal.ZERO;

            chineseDoseEntity.setChineseDoseCount(totalChineseAmount);  //中药帖数
            //中药贴均
            chineseDoseEntity.setChineseDosePriceAvg((!totalDoseCount.equals(BigDecimal.ZERO)) ? totalChineseAmount
                    .divide(totalDoseCount, CommonConstants.NUMBER_TWO, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        } catch (Exception e) {
            LOGGER.error("selectBudgetIndexList方法异常", e);
            e.printStackTrace();
        }
        return chineseDoseEntity;
    }

    /**
     * 杭州医保分类统计
     *
     * @param reqParams -
     * @return 医保分类对象
     */
    public RevenueChargedDailyMedicalInsuranceEntity selectMedicalInsurance(RevenueParam reqParams) {
        RevenueChargedDailyMedicalInsuranceEntity medicalInsuranceEntity = new RevenueChargedDailyMedicalInsuranceEntity();
        try {
            CompletableFuture<BigDecimal> totalCashFee = CompletableFuture.supplyAsync(() ->
                    storeUtils.getMapper(reqParams.getBeginDate(), reqParams.getEndDate(),
                            arnRevenueChargeDetailMapper, hologresRevenueChareDetailMapper)
                            .selectTotalCashFee(TableUtils.getCisTable(), reqParams,
                                    TimeUtils.appendEnd(reqParams.getEndDate())));

            CompletableFuture<List<RevenueChargedDailyMedicalInsuranceDao>> medicalInsuranceList
                    = CompletableFuture.supplyAsync(() ->
                    storeUtils.getMapper(reqParams.getBeginDate(), reqParams.getEndDate(),
                            arnRevenueChargeDetailMapper, hologresRevenueChareDetailMapper)
                            .selectMedicalInsurance(TableUtils.getCisTable(), reqParams,
                                    TimeUtils.appendEnd(reqParams.getEndDate())));

            totalCashFee.join();
            //总现金
            BigDecimal totalCash = totalCashFee.get();
            medicalInsuranceList.join();

            List<RevenueChargedDailyMedicalInsuranceDao> medicalInsuranceDaos = medicalInsuranceList.get();
            if (medicalInsuranceDaos == null || medicalInsuranceDaos.size() == 0) {
                medicalInsuranceEntity.setSelfFundPatients(totalCash == null ? BigDecimal.ZERO
                        : totalCash.setScale(2, RoundingMode.HALF_UP));
                return medicalInsuranceEntity;
            }

            Set<String> medTypeSet = medicalInsuranceDaos.stream()
                    .filter(x -> "1".equals(x.getSubMode()))
                    .map(RevenueChargedDailyMedicalInsuranceDao::getMedType).collect(Collectors.toSet());

            GetMedTypeReqBody reqBody = new GetMedTypeReqBody();
            reqBody.setMedTypeCodes(new ArrayList<>(medTypeSet));
            reqBody.setChainId(reqParams.getChainId());
            reqBody.setClinicId(reqParams.getHeaderClinicId());
            AbcServiceResponseBody<GetMedTypeRspBody> responseBody = abcCisShebaoFeignClient.queryMedType(reqBody);
            GetMedTypeRspBody reportRsp = responseBody.getData();
            LOGGER.info("杭州医保返回数据：{}", reportRsp);
            Map<String, String> medTypMap = reportRsp.getMedTypes().stream()
                    .collect(Collectors.toMap(GetMedTypeRspBody.MedType::getMedTypeCode,
                            GetMedTypeRspBody.MedType::getMedTypeName));

            for (RevenueChargedDailyMedicalInsuranceDao m : medicalInsuranceDaos) {
                switch (m.getSubMode()) {
                    //市医保
                    case "1":
                        assembleMunicipalMedicalInsurance(medicalInsuranceEntity, medTypMap, m);
                        break;
                    //省医保
                    case "2":
                        medicalInsuranceEntity.getProvinceMedicalInsuranceClinic()
                                .setMedicalInsurancePay(medicalInsuranceEntity.getProvinceMedicalInsuranceClinic()
                                        .getMedicalInsurancePay().add(m.getYiBao()).setScale(2, RoundingMode.HALF_UP));
                        medicalInsuranceEntity.getProvinceMedicalInsuranceClinic().setCashPay(medicalInsuranceEntity
                                .getProvinceMedicalInsuranceClinic().getCashPay().add(m.getYiBaoCash())
                                .setScale(2, RoundingMode.HALF_UP));
                        break;
                    //省内异地医保
                    case "30":
                        medicalInsuranceEntity.getProvinceInnerMedicalInsuranceClinic()
                                .setMedicalInsurancePay(medicalInsuranceEntity.getProvinceInnerMedicalInsuranceClinic()
                                        .getMedicalInsurancePay().add(m.getYiBao()).setScale(2, RoundingMode.HALF_UP));
                        medicalInsuranceEntity.getProvinceInnerMedicalInsuranceClinic()
                                .setCashPay(medicalInsuranceEntity.getProvinceInnerMedicalInsuranceClinic()
                                        .getCashPay().add(m.getYiBaoCash()).setScale(2, RoundingMode.HALF_UP));
                        break;
                    //省外异地
                    case "40":
                        medicalInsuranceEntity.getProvinceOutMedicalInsuranceClinic()
                                .setMedicalInsurancePay(medicalInsuranceEntity.getProvinceOutMedicalInsuranceClinic()
                                        .getMedicalInsurancePay().add(m.getYiBao()).setScale(2, RoundingMode.HALF_UP));
                        medicalInsuranceEntity.getProvinceOutMedicalInsuranceClinic()
                                .setCashPay(medicalInsuranceEntity.getProvinceOutMedicalInsuranceClinic()
                                        .getCashPay().add(m.getYiBaoCash()).setScale(2, RoundingMode.HALF_UP));
                        break;
                    default:
                }
            }
            //自费金额
            medicalInsuranceEntity.setSelfFundPatients(totalCash == null ? BigDecimal.ZERO : totalCash
                    .subtract(medicalInsuranceEntity.getGeneralClinic().getCashPay())
                    .subtract(medicalInsuranceEntity.getSpecifiedDiseaseClinic().getCashPay())
                    .subtract(medicalInsuranceEntity.getProvinceMedicalInsuranceClinic().getCashPay())
                    .subtract(medicalInsuranceEntity.getProvinceInnerMedicalInsuranceClinic().getCashPay())
                    .subtract(medicalInsuranceEntity.getProvinceOutMedicalInsuranceClinic().getCashPay())
                    .setScale(2, RoundingMode.HALF_UP));
        } catch (Exception e) {
            LOGGER.error("selectMedicalInsurance方法异常", e);
            e.printStackTrace();
        }
        return medicalInsuranceEntity;
    }

    /**
     * 处理市医保数据
     *
     * @param medicalInsuranceEntity -
     * @param medTypMap              -
     * @param m                      -
     */
    private void assembleMunicipalMedicalInsurance(RevenueChargedDailyMedicalInsuranceEntity medicalInsuranceEntity,
                                                   Map<String, String> medTypMap,
                                                   RevenueChargedDailyMedicalInsuranceDao m) {
        if (medTypMap.containsKey(m.getMedType())) {
            switch (medTypMap.get(m.getMedType())) {
                //普通门诊
                case "普通门诊":
                    medicalInsuranceEntity.getGeneralClinic().setMedicalInsurancePay(medicalInsuranceEntity
                            .getGeneralClinic().getMedicalInsurancePay().add(m.getYiBao()));
                    medicalInsuranceEntity.getGeneralClinic().setCashPay(medicalInsuranceEntity
                            .getGeneralClinic().getCashPay().add(m.getYiBaoCash()).setScale(2, RoundingMode.HALF_UP));
                    break;
                //规定病种
                case "规定病种":
                    medicalInsuranceEntity.getSpecifiedDiseaseClinic().setMedicalInsurancePay(medicalInsuranceEntity
                            .getSpecifiedDiseaseClinic().getMedicalInsurancePay().add(m.getYiBao()));
                    medicalInsuranceEntity.getSpecifiedDiseaseClinic().setCashPay(medicalInsuranceEntity
                            .getSpecifiedDiseaseClinic().getCashPay().add(m.getYiBaoCash()).setScale(2, RoundingMode.HALF_UP));
                    break;
                default:
            }
        }
    }

    /**
     * 国标
     *
     * @param reqParams -
     * @return 医保分类对象
     */
    public RevenueChargeDailySheBaoResp selectNationalStandardMedicalInsurance(RevenueParam reqParams) {
        RevenueChargeDailySheBaoResp resp = new RevenueChargeDailySheBaoResp();
        resp.setRegion(reqParams.getRegion());
        if (reqParams.getRegion().equalsIgnoreCase("neimenggu")) {
            resp.setRevenueVoList(selectChiFengMedicalInsurance(reqParams));
        } else {
            resp.setRevenueSheBaoVoList(selectGuoBiaoMedicalInsurance(reqParams));
        }
        return resp;
    }

    /**
     * 内蒙古赤峰医保分类统计
     *
     * @param reqParams -
     * @return 医保分类对象
     */
    public List<RevenueVo> selectChiFengMedicalInsurance(RevenueParam reqParams) {
        List<RevenueVo> revenueVos = new ArrayList<>();
        RevenueChargeDailyNengMengGuSheBaoEntity sheBaoEntity = hologresRevenueChareDetailMapper.selectMedicalInsuranceData(TableUtils.getCisTable(), reqParams, reqParams.getEndDate());
        revenueVos.add(new RevenueVo("医疗费用总额", sheBaoEntity == null || sheBaoEntity.getMedfeeSumamt() == null
                ? BigDecimal.ZERO : sheBaoEntity.getMedfeeSumamt().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("医保支付总额", sheBaoEntity == null || sheBaoEntity.getReceivedFee() == null
                ? BigDecimal.ZERO : sheBaoEntity.getReceivedFee().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("个人现金支出", sheBaoEntity == null || sheBaoEntity.getPsnCashPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getPsnCashPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("统筹基金支付", sheBaoEntity == null || sheBaoEntity.getHifpPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getHifpPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("个人账户支付", sheBaoEntity == null || sheBaoEntity.getAcctPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getAcctPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("共济账户支付", sheBaoEntity == null || sheBaoEntity.getAcctMulaidPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getAcctMulaidPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("公务员补助", sheBaoEntity == null || sheBaoEntity.getCvlservPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getCvlservPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("企业补充医保", sheBaoEntity == null || sheBaoEntity.getHifesPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getHifesPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("居民大病报销", sheBaoEntity == null || sheBaoEntity.getHifmiPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getHifmiPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("职工大额医疗补助", sheBaoEntity == null || sheBaoEntity.getHifobPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getHifobPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("医疗救助", sheBaoEntity == null || sheBaoEntity.getMafPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getMafPay().setScale(2, RoundingMode.HALF_UP)));
        revenueVos.add(new RevenueVo("其他基金", sheBaoEntity == null || sheBaoEntity.getOthPay() == null
                ? BigDecimal.ZERO : sheBaoEntity.getOthPay().setScale(2, RoundingMode.HALF_UP)));
        return revenueVos;
    }


    /**
     * 国标医保分类统计
     *
     * @param reqParams -
     * @return 医保分类对象
     */
    public List<RevenueSheBaoVo> selectGuoBiaoMedicalInsurance(RevenueParam reqParams) {
        List<RevenueSheBaoVo> result = new ArrayList<>();
        try {
            LOGGER.info("日月报国标/rpc/shebao-stat/summary-report-info参数："
                            + "chainId：{}, clinicId：{}, beginDate：{}, endDate：{}", reqParams.getChainId(),
                    reqParams.getHeaderClinicId(), reqParams.getBeginDate(), reqParams.getEndDate());
            AbcServiceResponseBody<ShebaoStatSummaryReportRsp> summaryReportInfo =
                    abcCisShebaoStatFeignClient.getSummaryReportInfo(reqParams.getChainId(),
                            reqParams.getHeaderClinicId(), reqParams.getBeginDate().substring(0, 10),
                            reqParams.getEndDate().substring(0, 10), reqParams.getCashierIdList());
            ShebaoStatSummaryReportRsp data = summaryReportInfo.getData();
            LOGGER.info("日月报国标数据，社保返回值{}", data == null ? "null" : JSON.toJSONString(data));
            if (data != null) {
                JsonNode extraInfo = data.getExtraInfo();
                JsonNode nationalPaymentItemList1 = extraInfo.get("nationalPaymentItemList");
                String dump = dump(nationalPaymentItemList1);
                LOGGER.info("日月报国标数据，社保返回值解析出来的数据{}", dump);
                if (dump != null && !"".equals(dump)) {
                    List<ShebaoStatSummaryReportRsp.NationalPaymentItem> nationalPaymentItemList
                            = JSON.parseArray(dump, ShebaoStatSummaryReportRsp.NationalPaymentItem.class);
                    if (nationalPaymentItemList != null && nationalPaymentItemList.size() > 0) {
                        for (ShebaoStatSummaryReportRsp.NationalPaymentItem x : nationalPaymentItemList) {
                            RevenueSheBaoVo revenueSheBaoVo =
                                    new RevenueSheBaoVo(x.getMedType(), x.getMedPay(), x.getCashPay());
                            result.add(revenueSheBaoVo);
                        }
                    }
                } else {
                    LOGGER.info("收费日月报国标走到默认数据");
                    result.add(new RevenueSheBaoVo("普通门诊", BigDecimal.ZERO, BigDecimal.ZERO));
                    result.add(new RevenueSheBaoVo("大病门诊", BigDecimal.ZERO, BigDecimal.ZERO));
                    result.add(new RevenueSheBaoVo("其他", BigDecimal.ZERO, BigDecimal.ZERO));
                }
            }
        } catch (Exception e) {
            LOGGER.error("selectGuoBiaoMedicalInsurance方法异常", e);
            e.printStackTrace();
        }
        return result;
    }


    /**
     * 对二级分类进行汇总统计
     *
     * @param cashierFeeClassifyList 费用分类列表
     * @param feeTypeMap             -
     * @param param                  -
     * @return 费用分类对象
     */
    private List<RevenueVo> collectLevelData(List<CashierFeeClassify> cashierFeeClassifyList,
                                             Map<Long, V2GoodsFeeType> feeTypeMap,
                                             OperationChargeReqParams param) {
        List<RevenueVo> feeEntities = new ArrayList<>();
        TreeMap<String, BigDecimal> feeTypeRes = new TreeMap<>();
        if (!"100".equals(param.getHisType())) {
            FEE_MAPPING.entrySet().stream()
                    .filter(entry -> "2".equals(param.getHisType()) || !entry.getKey().startsWith("24-"))
                    .forEach(entry -> {
                        if (SpecialUtil.getSpecialRegisterProperties().getChain().contains(param.getChainId()) && SpecialUtil.getSpecialRegisterProperties().getRegisterTargetValue().equals(entry.getValue())) {
                            feeTypeRes.put(SpecialUtil.getSpecialRegisterProperties().getRegisterResultValue(), BigDecimal.ZERO);
                        } else {
                            feeTypeRes.put(entry.getValue(), BigDecimal.ZERO);
                        }
                    });
            feeTypeRes.put("其他费用", BigDecimal.ZERO);
        }
        if (cashierFeeClassifyList.size() != 0) {
            for (CashierFeeClassify fee : cashierFeeClassifyList) {
                String k = fee.getPk();
                if (StringUtils.isEmpty(k)) {
                    continue;
                }
                BigDecimal value = fee.getValue().setScale(CommonConstants.NUMBER_TWO, RoundingMode.HALF_UP);
                String feeName;
                if ("100".equals(param.getHisType())) {
                    feeName = feeTypeMap.getOrDefault(fee.getFeeTypeId(), new V2GoodsFeeType()).getName();
                    if (org.apache.commons.lang3.StringUtils.isBlank(feeName)) {
                        feeName = "其他费用";
                    }
                    feeTypeRes.put(feeName, feeTypeRes.getOrDefault(feeName, BigDecimal.ZERO).add(value));
                } else {
                    feeName = FEE_MAPPING.getOrDefault(fee.getLevel1(), "其他费用");
                    if (SpecialUtil.getSpecialRegisterProperties().getChain().contains(param.getChainId()) && SpecialUtil.getSpecialRegisterProperties().getRegisterTargetValue().equals(feeName)) {
                        String registerResultValue = SpecialUtil.getSpecialRegisterProperties().getRegisterResultValue();
                        feeTypeRes.put(registerResultValue, feeTypeRes.get(registerResultValue).add(value));
                    } else {
                        feeTypeRes.put(feeName, feeTypeRes.get(feeName).add(value));
                    }
                }
            }
        }
        feeTypeRes.forEach((k, v) -> feeEntities.add(new RevenueVo(k, v)));
        return feeEntities;
    }

    /**
     * 按收费方式统计数据返回值处理
     *
     * @param cashierPayModeList 收费方式列表
     * @param customCharge       店铺自定义收费方式
     * @return 收费方式对象
     */
    private List<RevenueVo> dealPayModeData(List<CashierPayMode> cashierPayModeList,
                                            Map<Integer, String> customCharge) {
        List<RevenueVo> result = new ArrayList<>();
        try {
            if (!cashierPayModeList.isEmpty() && !customCharge.isEmpty()) {
                for (CashierPayMode mode : cashierPayModeList) {
                    RevenueVo revenueVo = new RevenueVo();
                    String k = mode.getPk();
                    if (StringUtils.isEmpty(k)) {
                        continue;
                    }
                    String payName = ChargeHandler.handlePayModeAssembleWebChat(query, mode.getPayMode(), mode.getPaySubMode(), customCharge);
                    revenueVo.setName(payName == null ? "未指定" : payName);
                    revenueVo.setValue(mode.getValue().setScale(CommonConstants.NUMBER_TWO, RoundingMode.HALF_UP));
                    result.add(revenueVo);
                }
                
                // 对结果列表按name分组累加并排序
                result = result.stream()
                    .collect(Collectors.groupingBy(
                        RevenueVo::getName,
                        Collectors.reducing(BigDecimal.ZERO, RevenueVo::getValue, BigDecimal::add)
                    ))
                    .entrySet().stream()
                    .map(entry -> {
                        RevenueVo revenueVo = new RevenueVo();
                        revenueVo.setName(entry.getKey());
                        revenueVo.setValue(entry.getValue());
                        return revenueVo;
                    })
                    .sorted(Comparator.comparing(RevenueVo::getName))
                    .collect(Collectors.toList());
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    /**
     * @param
     * @param summaryData 运营报表-汇总行数据
     * @return
     * @return cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueExportResult
     * @Description: 日月报导出汇总行数据处理
     * @Author: zs
     * @Date: 2022/11/24 16:51
     */
    public List<RevenueExportResult> summaryDataHandler(RevenueChargedDailySummaryDao summaryData) {
        ArrayList<RevenueExportResult> revenueExportResults = new ArrayList<>();
        RevenueExportResult classifyResult = new RevenueExportResult("医保支付合计", "0", "现金营收合计", "0", "充值合计", "0");
        RevenueExportResult resultTotal = new RevenueExportResult("实收合计", "0", "", "", "", "");
        if (summaryData != null) {
            classifyResult.setSecondLine(summaryData.getTotalMedicalInsurance().toString());
            //向上取整保留三位小数
            classifyResult.setFourthLine(summaryData.getTotalCashRevenue()
                    .setScale(CommonConstants.NUMBER_THREE, RoundingMode.HALF_UP).toString());
            classifyResult.setSixthLine(summaryData.getTotalRecharge()
                    .setScale(CommonConstants.NUMBER_THREE, RoundingMode.HALF_UP).toString());
            resultTotal.setSecondLine(summaryData.getTotalAmount().toString());
        }
        RevenueExportResult result1 = new RevenueExportResult("", "", "", "", "", "");
        revenueExportResults.add(classifyResult);
        revenueExportResults.add(resultTotal);
        revenueExportResults.add(result1);
        return revenueExportResults;
    }

    /**
     * @param
     * @param invoiceData 发票数据
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueExportResult>
     * @Description: 运营报表-发票数据处理
     * @Author: zs
     * @Date: 2022/11/24 17:14
     */
    public List<RevenueExportResult> invoiceDataHandler(InvoiceDailyEntity invoiceData) {
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        RevenueExportResult result1 = new RevenueExportResult("发票", "", "", "", "", "");
        revenueExportResults.add(result1);
        RevenueExportResult result2 = new RevenueExportResult("挂号发票起号",
                invoiceData.getRegistrationFirstNumber() == null ? "0" : invoiceData.getRegistrationFirstNumber(),
                "挂号发票止号",
                invoiceData.getRegistrationLastNumber() == null ? "0" : invoiceData.getRegistrationLastNumber(),
                "挂号开票总数",
                invoiceData.getRegistrationCount() == null ? "0" : invoiceData.getRegistrationCount().toString());
        revenueExportResults.add(result2);
        RevenueExportResult result3 = new RevenueExportResult("挂号作废号",
                invoiceData.getRegistrationInvalidCount() == null ? "0"
                        : invoiceData.getRegistrationInvalidCount().toString(),
                "挂号作废金额",
                invoiceData.getRegistrationInvalidAmount() == null ? "0"
                        : invoiceData.getRegistrationInvalidAmount().toString(), "", "");
        revenueExportResults.add(result3);
        RevenueExportResult result4 = new RevenueExportResult("挂号作废票号 ",
                invoiceData.getRegistrationInvalidNumber() == null ? "0" : invoiceData.getRegistrationInvalidNumber(),
                "", "", "", "");
        revenueExportResults.add(result4);
        RevenueExportResult result5 = new RevenueExportResult("门诊发票起号",
                invoiceData.getOutpatientFirstNumber() == null ? "0" : invoiceData.getOutpatientFirstNumber(),
                "门诊发票止号",
                invoiceData.getOutpatientLastNumber() == null ? "0" : invoiceData.getOutpatientLastNumber(),
                "门诊开票总数",
                invoiceData.getOutpatientCount() == null ? "0" : invoiceData.getOutpatientCount().toString());
        revenueExportResults.add(result5);
        RevenueExportResult result6 = new RevenueExportResult("门诊作废号",
                invoiceData.getOutpatientInvalidCount() == null ? "0"
                        : invoiceData.getOutpatientInvalidCount().toString(),
                "门诊作废金额",
                invoiceData.getOutpatientInvalidAmount() == null ? "0"
                        : invoiceData.getOutpatientInvalidAmount().toString(), "", "");
        revenueExportResults.add(result6);
        RevenueExportResult result7 = new RevenueExportResult("门诊作废票号 ",
                invoiceData.getOutpatientInvalidNumber() == null ? "0" : invoiceData.getOutpatientInvalidNumber(),
                "", "", "", "");
        revenueExportResults.add(result7);
        RevenueExportResult result8 = new RevenueExportResult("", "", "", "", "", "");
        revenueExportResults.add(result8);
        return revenueExportResults;
    }

    /**
     * @param
     * @param chargedDailyFeeEntity 费用分类数据
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueExportResult>
     * @Description: 费用分类数据处理
     * @Author: zs
     * @Date: 2022/11/24 17:53
     */
    public List<RevenueExportResult> chargedDailyFeeEntityHandler(RevenueChargedDailyFeeEntity chargedDailyFeeEntity) {
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        RevenueExportResult result1 = new RevenueExportResult("费用分类", "", "", "", "", "");
        revenueExportResults.add(result1);
        RevenueExportResult result2 = new RevenueExportResult("西药",
                chargedDailyFeeEntity.getFeeChineseMedicine().toString(),
                "中成药", chargedDailyFeeEntity.getFeeChinesePatentMedicine().toString(),
                "中药饮片", chargedDailyFeeEntity.getFeeChineseMedicinePieces().toString());
        revenueExportResults.add(result2);
        RevenueExportResult result3 = new RevenueExportResult("中药颗粒",
                chargedDailyFeeEntity.getFeeChineseMedicineGranules().toString(),
                "治疗", chargedDailyFeeEntity.getFeeTreatment().toString(),
                "理疗", chargedDailyFeeEntity.getFeePhysiotherapy().toString());
        revenueExportResults.add(result3);
        RevenueExportResult result4 = new RevenueExportResult("检查",
                chargedDailyFeeEntity.getFeeExamination().toString(),
                "检验", chargedDailyFeeEntity.getFeeCheckout().toString(),
                "挂号费", chargedDailyFeeEntity.getFeeRegistration().toString());
        revenueExportResults.add(result4);
        RevenueExportResult result5 = new RevenueExportResult("医疗器械",
                chargedDailyFeeEntity.getFeeMaterial().toString(),
                "商品", chargedDailyFeeEntity.getFeeGoods().toString(),
                "加工费", chargedDailyFeeEntity.getFeeProcess().toString());
        revenueExportResults.add(result5);
        RevenueExportResult result6 = new RevenueExportResult("快递费",
                chargedDailyFeeEntity.getFeeExpress().toString(),
                "会员充值", chargedDailyFeeEntity.getFeeMemberRecharge().toString(),
                "其他费用", chargedDailyFeeEntity.getFeeOther().toString());
        revenueExportResults.add(result6);
        RevenueExportResult result7 = new RevenueExportResult("", "", "", "", "", "");
        revenueExportResults.add(result7);
        return revenueExportResults;
    }

    /**
     * @param
     * @param budgetIndexEntity 预算指标数据
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueExportResult>
     * @Description: 预算指标数据处理
     * @Author: zs
     * @Date: 2022/11/24 18:13
     */
    public List<RevenueExportResult> budgetIndexHandler(RevenueChargedDailyBudgetIndexEntity budgetIndexEntity) {
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        RevenueChargedDailyChineseDoseEntity data =
                budgetIndexEntity.getRevenueChargedDailyChineseDoseEntity();
        //获取对象中的社保数据
        ShebaoStatSummaryReportRsp shebaoStatSummaryReportRsp = budgetIndexEntity.getShebaoStatSummaryReportRsp();
        JsonNode extraInfo = shebaoStatSummaryReportRsp.getExtraInfo();
        JsonNode budgetIndicator = extraInfo.get("budgetIndicator");
        String budgetIndicators = dump(budgetIndicator);
        //社保会返回默认值
        ShebaoStatSummaryReportRsp.HzBudgetIndicator shebaoData =
                JSON.parseObject(budgetIndicators, ShebaoStatSummaryReportRsp.HzBudgetIndicator.class);

        RevenueExportResult result1 = new RevenueExportResult("预算指标", "", "", "", "", "");
        revenueExportResults.add(result1);
        if (shebaoData != null) {
            RevenueExportResult result2 = new RevenueExportResult("市医保预算指标",
                    "就诊人次", shebaoData.getPersonTimes() + "", "就诊人头",
                    shebaoData.getPersonCount() + "", "比值:" + shebaoData.getPersonTimesCountRate().toString());
            revenueExportResults.add(result2);
            RevenueExportResult result3 = new RevenueExportResult("",
                    "次均", shebaoData.getTotalSocialPaymentFeeTimesAvg().toString(),
                    "列支", shebaoData.getSocialPaymentFee().toString(), "");
            revenueExportResults.add(result3);
        } else {
            RevenueExportResult result2 = new RevenueExportResult("市医保预算指标",
                    "就诊人次", 0 + "", "就诊人头",
                    0 + "", "比值:" + 0);
            revenueExportResults.add(result2);
            RevenueExportResult result3 = new RevenueExportResult("",
                    "次均", "0",
                    "列支", "0", "");
            revenueExportResults.add(result3);
        }
        RevenueExportResult result4 = new RevenueExportResult("诊所整体指标",
                "中药贴数", data.getChineseDoseCount() == null ? "0" : data.getChineseDoseCount().toString(),
                "中药贴均", data.getChineseDosePriceAvg() == null ? "0" : data.getChineseDosePriceAvg().toString(), "");
        revenueExportResults.add(result4);
        RevenueExportResult result5 = new RevenueExportResult("", "", "", "", "", "");
        revenueExportResults.add(result5);
        return revenueExportResults;
    }

    public List<RevenueExportResult> handleSheBaoQingDao(RevenueChargedDailyBudgetIndexEntity sheBaoQingDao) {
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        RevenueExportResult result1 = new RevenueExportResult("医保", "", "", "", "", "");
        revenueExportResults.add(result1);
        if (sheBaoQingDao == null || sheBaoQingDao.getShebaoStatSummaryReportRsp() == null || sheBaoQingDao.getShebaoStatSummaryReportRsp().getExtraInfo() == null) {
            return revenueExportResults;
        }
        ShebaoStatSummaryReportRsp.QingDaoExtraInfo extraInfo = JSON.parseObject(dump(sheBaoQingDao
                .getShebaoStatSummaryReportRsp().getExtraInfo()), ShebaoStatSummaryReportRsp.QingDaoExtraInfo.class);
        ShebaoStatSummaryReportRsp.QingDaoShebaoPaymentItem outpatient = extraInfo.getNormalOutpatient();
        ShebaoStatSummaryReportRsp.QingDaoShebaoPaymentItem seriousIllnessOutpatient = extraInfo.getSeriousIllnessOutpatient();
        ShebaoStatSummaryReportRsp.QingDaoShebaoPaymentItem longCare = extraInfo.getLongCare();

        revenueExportResults.add(new RevenueExportResult("普通门诊", "医保支付",
                outpatient == null || outpatient.getReceivedFee() == null ? "0" : outpatient.getReceivedFee().toString(),
                "大病门诊", "医保支付",
                seriousIllnessOutpatient == null || seriousIllnessOutpatient.getReceivedFee() == null ? "0" : seriousIllnessOutpatient.getReceivedFee().toString()));
        revenueExportResults.add(new RevenueExportResult("",
                "个人负担", outpatient == null || outpatient.getPersonalBurden() == null ? "0" : outpatient.getPersonalBurden().toString(), "", "个人负担",
                seriousIllnessOutpatient == null || seriousIllnessOutpatient.getPersonalBurden() == null ? "0" : seriousIllnessOutpatient.getPersonalBurden().toString()));
        revenueExportResults.add(new RevenueExportResult("长期护理", "医保支付",
                longCare == null || longCare.getReceivedFee() == null ? "0" : longCare.getReceivedFee().toString(), "", "", ""));
        revenueExportResults.add(new RevenueExportResult("", "个人负担",
                longCare == null || longCare.getPersonalBurden() == null ? "0" : longCare.getPersonalBurden().toString(), "", "", ""));
        revenueExportResults.add(new RevenueExportResult("", "", "", "", "", ""));
        return revenueExportResults;
    }

    /**
     * @param
     * @param hangZhouSheBao 杭州医保数据/就诊类型数据
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueExportResult>
     * @Description: 就诊类型数据处理
     * @Author: zs
     * @Date: 2022/11/25 09:51
     */
    public List<RevenueExportResult> hangZhouSheBaoHandler(RevenueChargedDailyMedicalInsuranceEntity hangZhouSheBao) {
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        //市医保普通门诊
        RevenueChargedDailyPayTypeEntity generalClinic = hangZhouSheBao.getGeneralClinic();
        //市医保规定病种门诊
        RevenueChargedDailyPayTypeEntity specifiedDiseaseClinic = hangZhouSheBao.getSpecifiedDiseaseClinic();
        //省医保门诊
        RevenueChargedDailyPayTypeEntity provinceMedicalInsuranceClinic = hangZhouSheBao.getProvinceMedicalInsuranceClinic();
        //省内异地
        RevenueChargedDailyPayTypeEntity provinceInnerMedicalInsuranceClinic = hangZhouSheBao.getProvinceInnerMedicalInsuranceClinic();
        //省外异地
        RevenueChargedDailyPayTypeEntity provinceOutMedicalInsuranceClinic = hangZhouSheBao.getProvinceOutMedicalInsuranceClinic();
        RevenueExportResult result1 = new RevenueExportResult("就诊类型", "", "", "", "", "");
        revenueExportResults.add(result1);
        RevenueExportResult result2 = new RevenueExportResult("市医保普通门诊",
                "现金支付", generalClinic.getCashPay() == null ? "0" : generalClinic.getCashPay().toString(),
                "市医保规定病种门诊",
                "现金支付",
                specifiedDiseaseClinic.getCashPay() == null ? "0" : specifiedDiseaseClinic.getCashPay().toString());
        revenueExportResults.add(result2);
        RevenueExportResult result3 = new RevenueExportResult("",
                "医保支付",
                generalClinic.getMedicalInsurancePay() == null ? "0"
                        : generalClinic.getMedicalInsurancePay().toString(),
                "",
                "医保支付",
                specifiedDiseaseClinic.getMedicalInsurancePay() == null ? "0"
                        : specifiedDiseaseClinic.getMedicalInsurancePay().toString());
        revenueExportResults.add(result3);
        RevenueExportResult result4 = new RevenueExportResult("省医保普通门诊",
                "现金支付", provinceMedicalInsuranceClinic.getCashPay() == null ? "0"
                : provinceMedicalInsuranceClinic.getCashPay().toString(),
                "省内异地",
                "现金",
                provinceInnerMedicalInsuranceClinic.getCashPay() == null ? "0"
                        : provinceInnerMedicalInsuranceClinic.getCashPay().toString());
        revenueExportResults.add(result4);
        RevenueExportResult result5 = new RevenueExportResult("",
                "医保支付",
                provinceMedicalInsuranceClinic.getMedicalInsurancePay() == null ? "0"
                        : provinceMedicalInsuranceClinic.getMedicalInsurancePay().toString(),
                "", "医保",
                provinceInnerMedicalInsuranceClinic.getMedicalInsurancePay() == null ? "0"
                        : provinceInnerMedicalInsuranceClinic.getMedicalInsurancePay().toString());
        revenueExportResults.add(result5);
        RevenueExportResult result6 = new RevenueExportResult("省外异地",
                "现金", provinceOutMedicalInsuranceClinic.getCashPay() == null ? "0"
                : provinceOutMedicalInsuranceClinic.getCashPay().toString(),
                "自费患者",
                "现金支付",
                hangZhouSheBao.getSelfFundPatients() == null ? "0" : hangZhouSheBao.getSelfFundPatients().toString());
        revenueExportResults.add(result6);
        RevenueExportResult result7 = new RevenueExportResult("",
                "医保",
                provinceOutMedicalInsuranceClinic.getMedicalInsurancePay() == null ? "0"
                        : provinceOutMedicalInsuranceClinic.getMedicalInsurancePay().toString(),
                "", "", "");
        revenueExportResults.add(result7);
        RevenueExportResult result8 = new RevenueExportResult("", "", "", "", "", "");
        revenueExportResults.add(result8);
        return revenueExportResults;
    }

    /**
     * @param
     * @param revenueSheBaoVos 赤峰医保/国标数据
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueExportResult>
     * @Description: 赤峰医保/国标医保数据处理
     * @Author: zs
     * @Date: 2022/11/25 10:37
     */
    public List<RevenueExportResult> revenueSheBaoVosHandler(List<RevenueSheBaoVo> revenueSheBaoVos) {
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        RevenueExportResult result1 = new RevenueExportResult("医保", "", "", "", "", "");
        revenueExportResults.add(result1);
        //循环处理数据
        if (revenueSheBaoVos != null && revenueSheBaoVos.size() >= CommonConstants.NUMBER_THREE) {
            RevenueSheBaoVo sheBaoVo = revenueSheBaoVos.get(0) == null
                    ? new RevenueSheBaoVo() : revenueSheBaoVos.get(0);
            RevenueSheBaoVo sheBaoVo1 = revenueSheBaoVos.get(1) == null
                    ? new RevenueSheBaoVo() : revenueSheBaoVos.get(1);
            RevenueSheBaoVo sheBaoVo2 = revenueSheBaoVos.get(CommonConstants.NUMBER_TWO) == null
                    ? new RevenueSheBaoVo() : revenueSheBaoVos.get(CommonConstants.NUMBER_TWO);
            RevenueExportResult result2 = new RevenueExportResult(sheBaoVo.getName(),
                    "医保支付", sheBaoVo.getMedicalInsurancePay() == null ? "0"
                    : sheBaoVo.getMedicalInsurancePay().toString(),
                    sheBaoVo1.getName(),
                    "医保支付",
                    sheBaoVo1.getMedicalInsurancePay() == null ? "0"
                            : sheBaoVo1.getMedicalInsurancePay().toString());
            revenueExportResults.add(result2);
            RevenueExportResult result3 = new RevenueExportResult("",
                    "现金支付", sheBaoVo.getCashPay() == null ? "0"
                    : sheBaoVo.getCashPay().toString(), "", "现金支付",
                    sheBaoVo1.getCashPay() == null ? "0" : sheBaoVo1.getCashPay().toString());
            revenueExportResults.add(result3);
            RevenueExportResult result4 = new RevenueExportResult(sheBaoVo2.getName(),
                    "医保支付", sheBaoVo2.getMedicalInsurancePay() == null ? "0"
                    : sheBaoVo2.getMedicalInsurancePay().toString(), "", "", "");
            revenueExportResults.add(result4);
            RevenueExportResult result5 = new RevenueExportResult("",
                    "现金支付", sheBaoVo2.getCashPay() == null ? "0"
                    : sheBaoVo.getCashPay().toString(), "", "", "");
            revenueExportResults.add(result5);
            RevenueExportResult result6 = new RevenueExportResult("", "", "", "", "", "");
            revenueExportResults.add(result6);
        }
        return revenueExportResults;
    }

    /**
     * 收费方式数据
     *
     * @param revenueVos -
     * @param name       -
     * @return -
     */
    public List<RevenueExportResult> revenueVosHandler(List<RevenueVo> revenueVos, String name) {
        List<RevenueExportResult> revenueExportResults = new ArrayList<>();
        RevenueExportResult result = new RevenueExportResult(name, "", "", "", "", "");
        revenueExportResults.add(result);
        int size = revenueVos.size();
        if (revenueVos != null && size > 0) {
            Boolean isLoop = true;
            Boolean isFirst = true;
            int x = 0;
            int y = 1;
            int z = CommonConstants.NUMBER_TWO;
            while (isLoop) {
                //定义三个基本容器
                RevenueVo xRevenueVo = new RevenueVo();
                RevenueVo yRevenueVo = new RevenueVo();
                RevenueVo zRevenueVo = new RevenueVo();
                //不是第一次就将角标往后移动
                if (!isFirst) {
                    //后移角标
                    x = x + CommonConstants.NUMBER_THREE;
                    y = y + CommonConstants.NUMBER_THREE;
                    z = z + CommonConstants.NUMBER_THREE;
                }
                isFirst = false;
                if (!(x > size - 1)) {
                    xRevenueVo = revenueVos.get(x);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                if (!(y > size - 1)) {
                    yRevenueVo = revenueVos.get(y);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                if (!(z > size - 1)) {
                    zRevenueVo = revenueVos.get(z);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                //最后一个角标等于size就说明这是最后一次循环
                if (z == size - 1) {
                    isLoop = false;
                }
                RevenueExportResult result1 = new RevenueExportResult(
                        xRevenueVo.getName() == null ? "" : xRevenueVo.getName(),
                        xRevenueVo.getValue() == null ? "" : xRevenueVo.getValue().toString(),

                        yRevenueVo.getName() == null ? "" : yRevenueVo.getName(),
                        yRevenueVo.getValue() == null ? "" : yRevenueVo.getValue().toString(),

                        zRevenueVo.getName() == null ? "" : zRevenueVo.getName(),
                        zRevenueVo.getValue() == null ? "" : zRevenueVo.getValue().toString());
                revenueExportResults.add(result1);
            }
        }
        RevenueExportResult result1 = new RevenueExportResult("", "", "", "", "", "");
        revenueExportResults.add(result1);
        return revenueExportResults;
    }

    /**
     * @param params -
     */
    public HisRevenueCostReportPayModeRes getPayModeByRegistration(RevenueParam params) throws Exception {
        HisRevenueCostReportPayModeRes response = new HisRevenueCostReportPayModeRes();
        CompletableFuture<List<HisRevenueCostReportPayModeDao>> businessDataF = CompletableFuture.supplyAsync(() ->
                storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), arnRevenueChargeDetailMapper,
                        hologresRevenueChareDetailMapper).selectPayModeByInsutype(TableUtils.getCisTable(), params));
        CompletableFuture<List<RevenueChargeDailyNengMengGuSheBaoEntity>> sheBaoDataF = CompletableFuture.supplyAsync(() ->
                hologresRevenueChareDetailMapper.getSheBaoPayModeByInsutype(TableUtils.getCisTable(), params,
                        SqlUtils.buildCashierSqlByList(params.getCashierIdList(),"last_modified_by")));
        CompletableFuture<HisRevenueCostReportPayModeTotalRes> totalF = CompletableFuture.supplyAsync(() ->
                storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), arnRevenueChargeDetailMapper,
                        hologresRevenueChareDetailMapper).getPayModeTotal(TableUtils.getCisTable(), params));
        CompletableFuture<Map<Integer, String>> customChargeF = CompletableFuture.supplyAsync(() ->
                query.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId()));
        CompletableFuture.allOf(businessDataF, sheBaoDataF, totalF, customChargeF).join();
        List<HisRevenueCostReportPayModeDao> businessData = businessDataF.get();
        List<RevenueChargeDailyNengMengGuSheBaoEntity> sheBaoEntity = sheBaoDataF.get();
        HisRevenueCostReportPayModeTotalRes totalRes = totalF.get();
        Map<Integer, String> customChargeMap = customChargeF.get();
        if (businessData == null && sheBaoEntity == null) {
            response.init();
            return response;
        }
        List<HisRevenueCostReportPayModeDao> result = new ArrayList<>();
        handlePayModeSheBaoData(sheBaoEntity, result);
        handlePayModeBusindessData(businessData, customChargeMap, result);
        response.setData(result);
        response.setTotal(totalRes == null ? new HisRevenueCostReportPayModeTotalRes(0, 0) : totalRes);
        response.setSummary(handlePayModeSummary(result));
        return response;
    }

    /**
     * 支付方式合计行
     *
     * @param result -
     * @return -
     */
    public HisRevenueCostReportPayModeDao handlePayModeSummary(List<HisRevenueCostReportPayModeDao> result) {
        HisRevenueCostReportPayModeDao summary = new HisRevenueCostReportPayModeDao("合计");
        for (HisRevenueCostReportPayModeDao payModeDao : result) {
            summary.setCityEmployeePrice(summary.getCityEmployeePrice().add(payModeDao.getCityEmployeePrice()));
            summary.setCityResidentPrice(summary.getCityResidentPrice().add(payModeDao.getCityResidentPrice()));
            summary.setSelfPayAndOtherPrice(summary.getSelfPayAndOtherPrice().add(payModeDao.getSelfPayAndOtherPrice()));
            summary.setCityEmployeeRefundPrice(summary.getCityEmployeeRefundPrice().add(payModeDao.getCityEmployeeRefundPrice()));
            summary.setCityResidentRefundPrice(summary.getCityResidentRefundPrice().add(payModeDao.getCityResidentRefundPrice()));
            summary.setSelfPayAndOtherRefundPrice(summary.getSelfPayAndOtherRefundPrice().add(payModeDao.getSelfPayAndOtherRefundPrice()));
        }
        return summary;
    }

    /**
     * 处理支付方式挂号社保数据
     *
     * @param sheBaoEntity -
     * @param result       -
     */
    public void handlePayModeSheBaoData(List<RevenueChargeDailyNengMengGuSheBaoEntity> sheBaoEntity,
                                        List<HisRevenueCostReportPayModeDao> result) {
        if (sheBaoEntity == null || sheBaoEntity.size() == 0) {
            HisRevenueCostReportPayModeDao sheBao = new HisRevenueCostReportPayModeDao();
            result.addAll(sheBao.initData());
            return;
        }
        Map<String, HisRevenueCostReportPayModeDao> map = new LinkedHashMap<>();
        for (RevenueChargeDailyNengMengGuSheBaoEntity entity : sheBaoEntity) {
            HisRevenueCostReportPayModeDao hifpPay = map.getOrDefault("统筹基金支付", new HisRevenueCostReportPayModeDao("统筹基金支付"));
            HisRevenueCostReportPayModeDao accPay = map.getOrDefault("个人帐户支付", new HisRevenueCostReportPayModeDao("个人帐户支付"));
            HisRevenueCostReportPayModeDao acctMulaidPay = map.getOrDefault("共济账户支付", new HisRevenueCostReportPayModeDao("共济账户支付"));
            HisRevenueCostReportPayModeDao cvlservPay = map.getOrDefault("公务员补助", new HisRevenueCostReportPayModeDao("公务员补助"));
            HisRevenueCostReportPayModeDao hifesPay = map.getOrDefault("企业补充医保", new HisRevenueCostReportPayModeDao("企业补充医保"));
            HisRevenueCostReportPayModeDao hifmiPay = map.getOrDefault("居民大病报销", new HisRevenueCostReportPayModeDao("居民大病报销"));
            HisRevenueCostReportPayModeDao hifobPay = map.getOrDefault("职工大额补助", new HisRevenueCostReportPayModeDao("职工大额补助"));
            HisRevenueCostReportPayModeDao mafPay = map.getOrDefault("医疗救助", new HisRevenueCostReportPayModeDao("医疗救助"));
            HisRevenueCostReportPayModeDao othPay = map.getOrDefault("其他基金", new HisRevenueCostReportPayModeDao("其他基金"));
            if (entity.getType() == 0) {
                if ("职工".equals(entity.getPersonType())) {
                    hifpPay.setCityEmployeePrice(entity.getHifpPay());
                    accPay.setCityEmployeePrice(entity.getAcctPay());
                    acctMulaidPay.setCityEmployeePrice(entity.getAcctMulaidPay());
                    cvlservPay.setCityEmployeePrice(entity.getCvlservPay());
                    hifesPay.setCityEmployeePrice(entity.getHifesPay());
                    hifmiPay.setCityEmployeePrice(entity.getHifmiPay());
                    hifobPay.setCityEmployeePrice(entity.getHifobPay());
                    mafPay.setCityEmployeePrice(entity.getMafPay());
                    othPay.setCityEmployeePrice(entity.getOthPay());
                } else if ("居民".equals(entity.getPersonType())) {
                    hifpPay.setCityResidentPrice(entity.getHifpPay());
                    accPay.setCityResidentPrice(entity.getAcctPay());
                    acctMulaidPay.setCityResidentPrice(entity.getAcctMulaidPay());
                    cvlservPay.setCityResidentPrice(entity.getCvlservPay());
                    hifesPay.setCityResidentPrice(entity.getHifesPay());
                    hifmiPay.setCityResidentPrice(entity.getHifmiPay());
                    hifobPay.setCityResidentPrice(entity.getHifobPay());
                    mafPay.setCityResidentPrice(entity.getMafPay());
                    othPay.setCityResidentPrice(entity.getOthPay());
                } else {
                    hifpPay.setSelfPayAndOtherPrice(entity.getHifpPay());
                    accPay.setSelfPayAndOtherPrice(entity.getAcctPay());
                    acctMulaidPay.setSelfPayAndOtherPrice(entity.getAcctMulaidPay());
                    cvlservPay.setSelfPayAndOtherPrice(entity.getCvlservPay());
                    hifesPay.setSelfPayAndOtherPrice(entity.getHifesPay());
                    hifmiPay.setSelfPayAndOtherPrice(entity.getHifmiPay());
                    hifobPay.setSelfPayAndOtherPrice(entity.getHifobPay());
                    mafPay.setSelfPayAndOtherPrice(entity.getMafPay());
                    othPay.setSelfPayAndOtherPrice(entity.getOthPay());
                }
            } else {
                if ("职工".equals(entity.getPersonType())) {
                    hifpPay.setCityResidentRefundPrice(entity.getHifpPay());
                    accPay.setCityResidentRefundPrice(entity.getAcctPay());
                    acctMulaidPay.setCityResidentRefundPrice(entity.getAcctMulaidPay());
                    cvlservPay.setCityResidentRefundPrice(entity.getCvlservPay());
                    hifesPay.setCityResidentRefundPrice(entity.getHifesPay());
                    hifmiPay.setCityResidentRefundPrice(entity.getHifmiPay());
                    hifobPay.setCityResidentRefundPrice(entity.getHifobPay());
                    mafPay.setCityResidentRefundPrice(entity.getMafPay());
                    othPay.setCityResidentRefundPrice(entity.getOthPay());
                } else if ("居民".equals(entity.getPersonType())) {
                    hifpPay.setCityResidentPrice(entity.getHifpPay());
                    accPay.setCityResidentPrice(entity.getAcctPay());
                    acctMulaidPay.setCityResidentPrice(entity.getAcctMulaidPay());
                    cvlservPay.setCityResidentPrice(entity.getCvlservPay());
                    hifesPay.setCityResidentPrice(entity.getHifesPay());
                    hifmiPay.setCityResidentPrice(entity.getHifmiPay());
                    hifobPay.setCityResidentPrice(entity.getHifobPay());
                    mafPay.setCityResidentPrice(entity.getMafPay());
                    othPay.setCityResidentPrice(entity.getOthPay());
                } else {
                    hifpPay.setSelfPayAndOtherPrice(entity.getHifpPay());
                    accPay.setSelfPayAndOtherPrice(entity.getAcctPay());
                    acctMulaidPay.setSelfPayAndOtherPrice(entity.getAcctMulaidPay());
                    cvlservPay.setSelfPayAndOtherPrice(entity.getCvlservPay());
                    hifesPay.setSelfPayAndOtherPrice(entity.getHifesPay());
                    hifmiPay.setSelfPayAndOtherPrice(entity.getHifmiPay());
                    hifobPay.setSelfPayAndOtherPrice(entity.getHifobPay());
                    mafPay.setSelfPayAndOtherPrice(entity.getMafPay());
                    othPay.setSelfPayAndOtherPrice(entity.getOthPay());
                }
            }
            map.put("统筹基金支付", hifpPay);
            map.put("个人帐户支付", accPay);
            map.put("共济账户支付", acctMulaidPay);
            map.put("公务员补助", cvlservPay);
            map.put("企业补充医保", hifesPay);
            map.put("居民大病报销", hifmiPay);
            map.put("职工大额补助", hifobPay);
            map.put("医疗救助", mafPay);
            map.put("其他基金", othPay);
        }
        result.addAll(map.values());
    }

    /**
     * 处理支付方式挂号业务数据
     *
     * @param businessData    -
     * @param customChargeMap -
     * @param result          -
     */
    private void handlePayModeBusindessData(List<HisRevenueCostReportPayModeDao> businessData,
                                            Map<Integer, String> customChargeMap,
                                            List<HisRevenueCostReportPayModeDao> result) {
        if (businessData == null) {
            return;
        }
        for (HisRevenueCostReportPayModeDao pay : businessData) {
            String payName = ChargeHandler.handlePayModeAssembleWebChat(query, pay.getPayMode(), pay.getPaySubMode(), customChargeMap);
            pay.setPayModeName(payName == null ? "未指定" : payName);
            pay.pretty();
            result.add(pay);
        }
    }

    public List<String> selectChargeTransactionCashierIds(RevenueParam param) {
        boolean useKudu = storeUtils.isUseKudu(param.getBeginDate(), param.getEndDate());
        List<String> cashierIds;
        if (useKudu) {
            cashierIds = arnOperationCashierSelectMapper.selectChargeTransactionCashierIds(TableUtils.getCisTable(), param);
            try {
                if (TimeUtils.isGreaterThanToday(param.getEndDate())) {
                    String beginDate = TimeUtils.getNow().substring(0, cn.abc.flink.stat.common.constants.CommonConstants.NUMBER_TEN);
                    List<String> cashierIds2 = arnOperationCashierMapper.selectChargeTransactionCashierIds(TableUtils.getCisTable(), beginDate, param);
                    if (cashierIds2 != null) {
                        cashierIds.removeAll(cashierIds2);
                        cashierIds.addAll(cashierIds2);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            cashierIds = arnOperationCashierMapper.selectChargeTransactionCashierIds(TableUtils.getCisTable(), param.getBeginDate(), param);
        }
        return cashierIds;
    }

    public List<String> selectChargeTransactionDepartmentIds(RevenueParam param) {
        boolean useKudu = storeUtils.isUseKudu(param.getBeginDate(), param.getEndDate());
        List<String> departmentIds;
        if (useKudu) {
            departmentIds = arnOperationCashierSelectMapper.selectChargeTransactionDepartmentIds(TableUtils.getCisTable(), param);
            try {
                if (TimeUtils.isToday(param.getEndDate())) {
                    String beginDate = param.getEndDate().substring(0, cn.abc.flink.stat.common.constants.CommonConstants.NUMBER_TEN);
                    List<String> departmentIds2 = arnOperationCashierMapper.selectChargeTransactionDepartmentIds(TableUtils.getCisTable(), beginDate, param);
                    if (departmentIds2 != null) {
                        departmentIds.removeAll(departmentIds2);
                        departmentIds.addAll(departmentIds2);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            departmentIds = arnOperationCashierMapper.selectChargeTransactionDepartmentIds(TableUtils.getCisTable(), param.getBeginDate(), param);
        }
        return departmentIds;
    }
}
