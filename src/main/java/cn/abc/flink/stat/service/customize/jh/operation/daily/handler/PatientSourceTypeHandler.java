package cn.abc.flink.stat.service.customize.jh.operation.daily.handler;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/23 10:21
 */
public class PatientSourceTypeHandler {

    /**
     * @param
     * @param sourceTypeMap -
     * @param sourceType    -
     * @return
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/23 10:21
     */
    public static Map<String, String> filterHandle(Map<String, String> sourceTypeMap, String sourceType) {
        Map<String, String> map = new HashMap<>();
        if (sourceType == null || sourceType.trim().equals("")) {
            return map;
        }
        for (String st : sourceType.split(",")) {
            if (sourceTypeMap.containsKey(st)) {
                map.put(st, sourceTypeMap.get(st));
            }
        }
        return map;
    }

    /**
     * @param
     * @param map -
     * @return
     * @return java.lang.String
     * @Description:
     * @Author: zs
     * @Date: 2022/8/23 10:21
     */
    public static String map2String(Map<String, String> map) {
        if (map.isEmpty()) {
            return "'0'";
        }
        StringBuilder sb = new StringBuilder();
        map.values().stream().forEach(val -> {
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append("'" + val + "'");
        });
        return sb.toString();
    }

    /**
     * @Description:
     * @param
     * @param sourceTypeMap -
     * @param sourceType -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/23 10:21
     */
    public static String handle(Map<String, String> sourceTypeMap, String sourceType) {
        if (sourceType == null || sourceType.trim().equals("")) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (String st : sourceType.split(",")) {
            if (sourceTypeMap.containsKey(st)) {
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append(sourceTypeMap.get(st));
            }
        }
        return sb.toString();
    }
}
