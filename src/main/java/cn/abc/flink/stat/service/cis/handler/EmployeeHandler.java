package cn.abc.flink.stat.service.cis.handler;

import cn.abc.flink.stat.common.AbcDefaultValueUtils;
import cn.abc.flink.stat.common.request.params.AbcScStatFilterEmployee;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.SnapEmployee;
import cn.abc.flink.stat.dimension.domain.V2ClinicChainEmployeeSnap;
import cn.abc.flink.stat.pojo.EmployeeResp;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class EmployeeHandler {

    public static String handleEmployeeNameById(Map<String, Employee> employeeMap, String id) {
        if (id == null || id.trim().equals("")) {
            return AbcDefaultValueUtils.DEFAULT_TEXT;
        }
        Employee e = employeeMap.getOrDefault(id, null);
        if (e != null) {
            return e.getName();
        }
        return AbcDefaultValueUtils.DEFAULT_TEXT;
    }

    public static String handleEmployeeName(Map<String, Employee> employeeMap, Map<Long, V2ClinicChainEmployeeSnap> snapMap, String id, Long snapId) {
        return handleEmployeeName(employeeMap, snapMap, id, snapId, AbcDefaultValueUtils.DEFAULT_TEXT);
    }

    public static String handleEmployeeSnapSql(String doctorFieldName, String snapFieldName, SnapEmployee employee) {
        if (doctorFieldName == null || snapFieldName == null || employee == null) {
            return null;
        }
        StringBuffer intermediateValue = new StringBuffer();
        String snapIds = "";
        if (employee.getSnapIds() != null) {
            // 转换为字符串
            snapIds = "(" + employee.getSnapIds().stream()
                    .map(String::valueOf) // 将每个元素转为字符串
                    .collect(Collectors.joining(", ")) + ")";
        }
        intermediateValue.append(" and ").append(doctorFieldName).append(" = ").append("'").append(employee.getEmployeeId()).append("'");
        intermediateValue.append(" and (").append(snapFieldName).append(" in ").append(snapIds);

        if (employee.getIsIncludeTheLatest() == 1) {
            intermediateValue.append(" or ").append(snapFieldName).append(" is null");
        }
        intermediateValue.append(")");
        return intermediateValue.toString();
    }

    public static String handleEmployeeSnapSql(String doctorFieldName, String snapFieldName, List<SnapEmployee> employees) {
        if (doctorFieldName == null || snapFieldName == null || employees == null || employees.size() == 0) {
            return null;
        }
        StringBuffer intermediateValue = new StringBuffer();
        intermediateValue.append(" and (");
        int i = 0;
        for (SnapEmployee employee : employees) {
            if (i != 0) {
                intermediateValue.append(" or ");
            }
            String snapIds = "";
            if (employee.getSnapIds() != null && employee.getSnapIds().size() > 0) {
                // 转换为字符串
                snapIds = "(" + employee.getSnapIds().stream()
                        .map(String::valueOf) // 将每个元素转为字符串
                        .collect(Collectors.joining(", ")) + ")";
            }
            intermediateValue.append(" (").append(doctorFieldName).append(" = ").append("'").append(employee.getEmployeeId()).append("'");
            if (snapIds != null && !snapIds.trim().equals("")) {
                intermediateValue.append(" and (").append(snapFieldName).append(" in ").append(snapIds);
            }

            if (employee.getIsIncludeTheLatest() == 1) {
                intermediateValue.append(" or ").append(snapFieldName).append(" is null");
            }
            if (snapIds != null && !snapIds.trim().equals("")) {
                intermediateValue.append(")");
            }
            intermediateValue.append(")");
            i++;
        }
        intermediateValue.append(" )");
        return intermediateValue.toString();
    }

    /**
     * @param
     * @param doctorFieldName       医生字段名称
     * @param snapFieldName         快照字段明细
     * @param sellerFieldName       销售人字段名称
     * @param employees             -
     * @param isContainNotSpecified 是否存在未指定选项
     * @return
     * @return java.lang.String
     * @Description: 将医生筛选拼接成sql，给开单人筛选使用
     * @Author: zs
     * @Date: 2024/10/23 15:29
     */
    public static String handleEmployeeSnapSql(String doctorFieldName, String snapFieldName, String sellerFieldName, boolean isContainNotSpecified, List<SnapEmployee> employees) {
        if (doctorFieldName == null || snapFieldName == null || employees == null || sellerFieldName == null || employees.size() == 0) {
            return null;
        }
        StringBuffer intermediateValue = new StringBuffer();
        intermediateValue.append(" and (");
        int i = 0;
        for (SnapEmployee employee : employees) {
            if (i != 0) {
                intermediateValue.append(" or ");
            }
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getEmployeeId())) {
                //走未指定逻辑
                intermediateValue.append(" ((" + doctorFieldName + " is null or " + doctorFieldName + " = '') and (" + sellerFieldName + " is null or " + sellerFieldName + " = ''))");
            } else {
                String snapIds = "";
                if (employee.getSnapIds() != null) {
                    // 转换为字符串
                    snapIds = "(" + employee.getSnapIds().stream()
                            .map(String::valueOf) // 将每个元素转为字符串
                            .collect(Collectors.joining(", ")) + ")";
                }
                intermediateValue.append("(").append(doctorFieldName).append(" = ").append("'").append(employee.getEmployeeId()).append("'");
                intermediateValue.append(" and (").append(snapFieldName).append(" in ").append(snapIds);

                if (employee.getIsIncludeTheLatest() == 1) {
                    intermediateValue.append(" or ").append(snapFieldName).append(" is null");
                }
                intermediateValue.append(") ");
                intermediateValue.append(" or ").append(sellerFieldName).append(" = ").append("'").append(employee.getEmployeeId()).append("'");
                intermediateValue.append(")");
            }
            i++;
        }
        intermediateValue.append(" )");
        return intermediateValue.toString();
    }

    public static String handleEmployeeSnapSql(String fieldName, boolean isContainNotSpecified, List<SnapEmployee> employees) {
        if (fieldName == null || employees.size() == 0) {
            return null;
        }
        StringBuffer intermediateValue = new StringBuffer();
        intermediateValue.append(" and (");
        int i = 0;
        for (SnapEmployee employee : employees) {
            if (i != 0) {
                intermediateValue.append(" or ");
            }
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getEmployeeId())) {
                //走未指定逻辑
                intermediateValue.append("(" + fieldName + " is null or " + fieldName + " = '') ");
            } else {
                intermediateValue.append("(").append(fieldName).append(" = ").append("'").append(employee.getEmployeeId()).append("')");
            }
            i++;
        }
        intermediateValue.append(" )");
        return intermediateValue.toString();
    }

    /**
     * 通过人员快照和人员表获取人员名字，优先从快照获取
     *
     * @param employeeMap 人员map
     * @param snapMap     人员快照map
     * @param id          人员id
     * @param snapId      人员快照id
     * @return 姓名
     */
    public static String handleEmployeeName(Map<String, Employee> employeeMap, Map<Long, V2ClinicChainEmployeeSnap> snapMap, String id, Long snapId, String defaultValue) {
        if (snapId == null && (id == null || id.trim().equals(""))) {
            return defaultValue;
        }
        if (snapId != null) {
            V2ClinicChainEmployeeSnap snap = snapMap.get(snapId);
            if (snap != null) {
                return snap.getName();
            }
        }
        Employee e = employeeMap.get(id);
        if (e != null) {
            return e.getName();
        }
        return defaultValue;
    }

    /**
     * 把id的list转换为人员的list
     *
     * @param employeeMap
     * @param ids
     * @return
     */
    public static List<EmployeeResp> getEmployeeListFromIds(Map<String, Employee> employeeMap, Collection<String> ids) {
        List<EmployeeResp> list = new ArrayList<>();
        for (String id : ids) {
            if (id != null && !"".equals(id.trim())) {
                Employee e = employeeMap.get(id);
                if (e != null) {
                    EmployeeResp er = new EmployeeResp();
                    er.setId(id);
                    er.setName(e.getName());
                    er.setNamePyFirst(e.getNamePyFirst());
                    er.setNamePy(e.getNamePy());
                    list.add(er);
                }
            }
        }
        return list;
    }

    /**
     * 开单业绩人员sql 特殊处理
     *
     * @param isContainNotSpecified -
     * @param employees             -
     * @return -
     */
    public static String handleChargeEmployeeSnapSql(boolean isContainNotSpecified,
                                                     List<SnapEmployee> employees) {
        if (employees.size() == 0) {
            return null;
        }
        StringBuilder intermediateValue = new StringBuilder();
        StringBuilder itemDoctorValue = new StringBuilder();
        String notSpecified = "";
        String notSpecifiedItemDoctor = "";
        int size = 1;
        intermediateValue.append("(");
        itemDoctorValue.append("(");
        for (SnapEmployee employee : employees) {
            if (size != 1) {
                intermediateValue.append(" or ");
                itemDoctorValue.append(" or ");
            } else {
                intermediateValue.append("(");
                itemDoctorValue.append("(");
            }
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getEmployeeId())) {
                //走未指定逻辑
                notSpecified = "((doctor_id is null or doctor_id = '') and (seller_id is null or seller_id = '') and retail_type !=2 and charge_sheet_type not in (3,6,8,12,16,18) and product_type not in (2,3,4,11,19))";
                notSpecifiedItemDoctor = "((item_doctor_id is null or item_doctor_id = '') and (retail_type =2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19))";
            } else {
                String snapIds = "";
                if (employee.getSnapIds() != null) {
                    // 转换为字符串
                    snapIds = "(" + employee.getSnapIds().stream().map(String::valueOf) // 将每个元素转为字符串
                            .collect(Collectors.joining(", ")) + ")";
                }
                intermediateValue.append("((doctor_id = '").append(employee.getEmployeeId()).append("'")
                        .append(" and doctor_snap_id in ").append(snapIds);

                if (employee.getIsIncludeTheLatest() == 1) {
                    intermediateValue.append(" or doctor_snap_id is null");
                }
                intermediateValue.append(") ");
                intermediateValue.append(" or seller_id = '").append(employee.getEmployeeId()).append("')");
                itemDoctorValue.append("(item_doctor_id = '").append(employee.getEmployeeId()).append("')");
                if (size == employees.size() || size == employees.size() - 1) {
                    intermediateValue.append(") and retail_type !=2 and charge_sheet_type not in (3,6,8,12,16,18) and product_type not in (2,3,4,11,19))");
                    itemDoctorValue.append(") and (retail_type =2 or charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19)))");
                }
                size++;
            }
        }
        if (intermediateValue.length() == 0) {
            return "and (" + notSpecified + " or " + notSpecifiedItemDoctor + ")";
        }
        if ("".equals(notSpecified)) {
            return "and (" + intermediateValue + " or " + itemDoctorValue + ")";
        }
        return "and (" + intermediateValue + " or " + itemDoctorValue + " or " + notSpecified + " or " + notSpecifiedItemDoctor + ")";
    }

    /**
     * @param isContainNotSpecified -
     * @param employees             -
     * @param isToday               -
     * @return -
     */
    public static String handleChargeEmployeeDoctorSql(boolean isContainNotSpecified,
                                                       List<AbcScStatFilterEmployee> employees,
                                                       boolean isToday,
                                                       String type) {
        StringBuilder intermediateValue = new StringBuilder();
        StringBuilder itemDoctorValue = new StringBuilder();
        Set<String> itemDoctorIds = new HashSet<>();
        boolean hasNotSpecified = false;

        // 处理doctor_id部分
        intermediateValue.append("(");
        for (AbcScStatFilterEmployee employee : employees) {
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getId())) {
                if (intermediateValue.length() > 1) {
                    intermediateValue.append(" or ");
                }
                intermediateValue.append("(doctor_id is null or doctor_id = '')");
                hasNotSpecified = true;
            } else {
                if (intermediateValue.length() > 1) {
                    intermediateValue.append(" or ");
                }
                intermediateValue.append("(doctor_id = '").append(employee.getId()).append("' and (employee_snaps ->> ");
                if (isToday) {
                    intermediateValue.append("'$.doctorName' = '").append(employee.getName());
                } else {
                    intermediateValue.append("'doctorName' = '").append(employee.getName());
                }
                intermediateValue.append("' or employee_snaps is null))");
                itemDoctorIds.add(employee.getId());
            }
        }
        if ("charge".equals(type)) {
            intermediateValue.append(") and retail_type != 2 and charge_sheet_type not in (3,6,8,12,16,18) and product_type not in (2,3,4,11,19)");
        } else {
            intermediateValue.append(") and charge_sheet_type not in (3,6,8,12,16,18) ");
        }
        // 处理item_doctor_id部分
        itemDoctorValue.append("(");
        if (hasNotSpecified) {
            itemDoctorValue.append("(item_doctor_id is null or item_doctor_id = '')");
        }
        if (!itemDoctorIds.isEmpty()) {
            if (hasNotSpecified) {
                itemDoctorValue.append(" or ");
            }
            itemDoctorValue.append("item_doctor_id in ('").append(String.join("','", itemDoctorIds)).append("')");
        }
        if ("charge".equals(type)) {
            itemDoctorValue.append(") and (retail_type = 2 and charge_sheet_type in (3,6,8,12,16,18) or product_type in (2,3,4,11,19))");
        } else {
            itemDoctorValue.append(") and charge_sheet_type in (3,6,8,12,16,18) ");
        }
        return "and (" + intermediateValue + " or " + itemDoctorValue + ")";
    }

    /**
     * 挂号姓名快照搜索
     *
     * @param doctorId              -
     * @param doctorName            -
     * @param isContainNotSpecified -
     * @param employees             -
     * @return -
     */
    public static String handleRegistrationEmployeeSnapSql(String doctorId, String doctorName,
                                                           boolean isContainNotSpecified,
                                                           List<AbcScStatFilterEmployee> employees) {
        StringBuffer intermediateValue = new StringBuffer();
        intermediateValue.append(" and (");
        int i = 0;
        for (AbcScStatFilterEmployee employee : employees) {
            if (i != 0) {
                intermediateValue.append(" or ");
            }
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getId())) {
                //走未指定逻辑
                intermediateValue.append("(" + doctorId + " is null or " + doctorId + " = '') ");
            } else {
                intermediateValue.append("(").append(doctorId).append(" = '").append(employee.getId()).append("' and ")
                        .append(doctorName).append(" = '").append(employee.getName()).append("')");
            }
            i++;
        }
        intermediateValue.append(" )");
        return intermediateValue.toString();
    }

    /**
     * 获取快照id
     *
     * @param employeeInfos -
     */
    @SafeVarargs
    public static Set<Long> getEmployeeSnapIds(List<EmployeeResp>... employeeInfos) {
        Set<Long> snapIds = new HashSet<>();
        for (List<EmployeeResp> employeeInfo : employeeInfos) {
            for (EmployeeResp employeeResp : employeeInfo) {
                if (employeeResp.getSnapId() != null) {
                    snapIds.add(employeeResp.getSnapId());
                }
            }
        }
        return snapIds;
    }


    public static String handleEmployeeSnapNameSql(String snapFieldName,
                                                   String fieldIdName,
                                                   String fieldName,
                                                   boolean isContainNotSpecified,
                                                   List<AbcScStatFilterEmployee> employees,
                                                   boolean isToday) {
        if (fieldName == null || snapFieldName == null || employees.isEmpty()) {
            return null;
        }
        StringBuffer intermediateValue = new StringBuffer();
        intermediateValue.append(" and (");
        int i = 0;
        for (AbcScStatFilterEmployee employee : employees) {
            if (i != 0) {
                intermediateValue.append(" or ");
            }
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getId())) {
                //走未指定逻辑
                intermediateValue.append("(" + fieldIdName + " is null or " + fieldIdName + " = '') ");
            } else {
                intermediateValue.append("(").append(fieldIdName).append(" = ").append("'").append(employee.getId()).append("')");
                intermediateValue.append(" and (").append(snapFieldName).append(" ->> '");
                if (isToday) {
                    intermediateValue.append("$.").append(fieldName).append("' = '").append(employee.getName()).append("'");
                } else {
                    intermediateValue.append(fieldName).append("' = '").append(employee.getName()).append("'");
                }
                intermediateValue.append(" or ").append(snapFieldName).append(" is null");
                intermediateValue.append(") ");
            }
            i++;
        }
        intermediateValue.append(" )");
        return intermediateValue.toString();
    }

    public static String handleEmployeeSnapNameSql(String doctorIdFieldName,
                                                   String sellerIdFieldName,
                                                   String snapFieldName,
                                                   String doctorFieldName,
                                                   String sellerFieldName,
                                                   boolean isContainNotSpecified,
                                                   List<AbcScStatFilterEmployee> employees,
                                                   boolean isToday) {
        if (doctorIdFieldName == null || sellerIdFieldName == null || employees == null || doctorFieldName == null
                || sellerFieldName == null || employees.isEmpty()) {
            return null;
        }
        StringBuffer intermediateValue = new StringBuffer();
        intermediateValue.append(" and (");
        int i = 0;
        for (AbcScStatFilterEmployee employee : employees) {
            if (i != 0) {
                intermediateValue.append(" or ");
            }
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getId())) {
                //走未指定逻辑
                intermediateValue.append(" ((" + doctorIdFieldName + " is null or " + doctorIdFieldName + " = '') and (" + sellerIdFieldName + " is null or " + sellerIdFieldName + " = ''))");
            } else {
                intermediateValue.append("(").append(doctorIdFieldName).append(" = ").append("'").append(employee.getId()).append("'");
                intermediateValue.append(" and (").append(snapFieldName).append(" ->> '");
                if (isToday) {
                    intermediateValue.append("$.").append(doctorFieldName).append("' = '").append(employee.getName()).append("'");
                } else {
                    intermediateValue.append(doctorFieldName).append("' = '").append(employee.getName()).append("'");
                }
                intermediateValue.append(" or ").append(snapFieldName).append(" is null))");
                intermediateValue.append(" or (").append(sellerIdFieldName).append(" = ").append("'").append(employee.getId()).append("'");
                intermediateValue.append(" and (").append(snapFieldName).append(" ->> '");
                if (isToday) {
                    intermediateValue.append("$.").append(sellerFieldName).append("' = '").append(employee.getName()).append("'");
                } else {
                    intermediateValue.append(sellerFieldName).append("' = '").append(employee.getName()).append("'");
                }
                intermediateValue.append(" or ").append(snapFieldName).append(" is null))");
            }
            i++;
        }
        intermediateValue.append(")");
        return intermediateValue.toString();
    }

    /**
     * @param fieldName             -
     * @param isContainNotSpecified -
     * @param employees             -
     * @return -
     */
    public static String handleChargeAchievementEmployeeSql(String fieldName, boolean isContainNotSpecified,
                                                            List<AbcScStatFilterEmployee> employees) {

        if (fieldName == null || employees.isEmpty()) {
            return null;
        }
        StringBuffer intermediateValue = new StringBuffer();
        intermediateValue.append(" and (");
        Set<String> ids = new HashSet<>();
        boolean isNotSpecified = false;
        for (AbcScStatFilterEmployee employee : employees) {
            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getId())) {
                //走未指定逻辑
                intermediateValue.append("(" + fieldName + " is null or " + fieldName + " = '') ");
                isNotSpecified = true;
            } else {
                ids.add(employee.getId());
            }
        }
        if (!ids.isEmpty()) {
            if (isNotSpecified) {
                intermediateValue.append(" or ");
            }
            intermediateValue.append(fieldName).append(" in ('").append(String.join("','", ids)).append("')");
        }
        intermediateValue.append(" )");
        return intermediateValue.toString();
    }

    public static String handleEmployeeNameSql(String fieldIdName, String fieldName,
                                               boolean isContainNotSpecified,
                                               List<AbcScStatFilterEmployee> filterDoctorParams) {
        if (fieldIdName == null || fieldName.isEmpty()) {
            return null;
        }
        StringBuilder sql = new StringBuilder(" and (");
        int i = 0;
        for (AbcScStatFilterEmployee employee : filterDoctorParams) {
            if (i != 0) {
                sql.append(" or ");
            }

            if (isContainNotSpecified && "00000000000000000000000000000000".equals(employee.getId())) {
                // 未指定逻辑
                sql.append("(").append(fieldIdName).append(" is null or ").append(fieldIdName).append(" = '') ");
            } else {
                sql.append("(").append(fieldIdName).append(" = '").append(employee.getId()).append("' AND ");
                sql.append(fieldName).append(" = '").append(employee.getName()).append("')");
            }
            i++;
        }
        sql.append(")");
        return sql.toString();
    }
}
