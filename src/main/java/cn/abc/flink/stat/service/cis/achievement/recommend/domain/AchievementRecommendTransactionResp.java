package cn.abc.flink.stat.service.cis.achievement.recommend.domain;



import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AchievementRecommendTransactionResp {
    private List<Map<String, Object>> data;
    private List<AchievementRecommendHeader> header;
    List<AchievementRecommendFeeClassify> feeList;
    private Map<String, Long> total;
    private BigDecimal totalSum;

    public AchievementRecommendTransactionResp() {
    }

    public AchievementRecommendTransactionResp(List<Map<String, Object>> data,
                                               List<AchievementRecommendHeader> header,
                                               Map<String, Long> total) {
        this.data = data;
        this.header = header;
        this.total = total;
    }

    public List<AchievementRecommendHeader> getHeader() {
        return this.header;
    }

    public Map<String, Long> getTotal() {
        return this.total;
    }

    public BigDecimal getTotalSum() {
        return this.totalSum;
    }


    public void setHeader(List<AchievementRecommendHeader> header) {
        this.header = header;
    }

    public void setTotal(Map<String, Long> total) {
        this.total = total;
    }

    public void setTotalSum(BigDecimal totalSum) {
        this.totalSum = totalSum;
    }

}
