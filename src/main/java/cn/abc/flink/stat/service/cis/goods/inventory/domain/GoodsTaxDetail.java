package cn.abc.flink.stat.service.cis.goods.inventory.domain;

import cn.abc.flink.stat.common.ABCNumberUtils;


import java.math.BigDecimal;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-06-27 18:59
 **/
public class GoodsTaxDetail {

    private String goodsId;

    private String taxName;

    private String beginDate;

    private String endDate;

    private BigDecimal tax;

    private String taxText;

    private String actionName;

    private BigDecimal excludeTax;

    private String dateRange;

    public void apply(){
        this.taxText = ABCNumberUtils.round2TextPretty1(tax)+"%";
    }


    public String getGoodsId() {
        return this.goodsId;
    }

    public String getTaxName() {
        return this.taxName;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public BigDecimal getTax() {
        return this.tax;
    }

    public String getTaxText() {
        return this.taxText;
    }

    public String getActionName() {
        return this.actionName;
    }

    public BigDecimal getExcludeTax() {
        return this.excludeTax;
    }

    public String getDateRange() {
        return this.dateRange;
    }


    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public void setTaxText(String taxText) {
        this.taxText = taxText;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public void setExcludeTax(BigDecimal excludeTax) {
        this.excludeTax = excludeTax;
    }

    public void setDateRange(String dateRange) {
        this.dateRange = dateRange;
    }

}
