package cn.abc.flink.stat.service.cis.goods.inventory.handler;

import cn.abc.flink.stat.common.*;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.model.TempTable;
import cn.abc.flink.stat.common.request.params.GoodsInfoParam;
import cn.abc.flink.stat.common.response.StatResponseKeyDataItem;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresGoodsInventoryMapper;
import cn.abc.flink.stat.db.dao.OdsChargeMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.*;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.*;
import cn.abc.flink.stat.service.cis.handler.GoodsHandler;
import cn.abc.flink.stat.service.cis.handler.SelectHandler;
import cn.abc.flink.stat.source.AbcThreadExecutor;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/8/10 4:37 下午
 */
@Component
public class GoodsInventoryDataHandler {
    private static final Logger logger = LoggerFactory.getLogger(GoodsInventoryDataHandler.class);

    @Resource
    private HologresGoodsInventoryMapper hologresGoodsInventoryMapper;

    @Autowired
    private DimensionQuery dimensionQuery;

    @Resource
    private AbcThreadExecutor abcyunExecutorPool;

    @Autowired
    private OdsChargeMapper odsChargeMapper;

    @Value("${stat.inventory-begin-end-date}")
    private String inventoryBeginEndConfine;

    @Value("${abc.goods-inventory.purchaseActionClinic}")
    private String goodsInventoryPurchaseActionClinic;


    static final int TWO = 2;
    static final int SEVEN = 7;


    /**
     * 第一步：应该根据chainId 、一级分类（可选）、二级分类（可选）、goodsId(可选) ,查询v2_goods表，再分页
     * 第二步：将第一步分页出来的多个goods_id 加其它条件再去查询库存相关数据
     * 第三步：合并前两个结果集，如果某个goods_id没有库存数据，则将库存相关所有字段置为0.00
     * <p>
     * 一级分类逻辑
     * 1-2  :  对应sql条件 type = 1 and sub_type = 2 and c_m_spec!='中药饮片'  and  c_m_spec!='中药颗粒'
     * 1-12 :   对应sql条件 type = 1 and  sub_type = 2  and c_m_spec= '中药饮片'
     * 1-13 :   对应sql条件 type = 1 and sub_type = 2 and c_m_spec= '中药颗粒'
     * x-y其它： 对应sql条件 type = x and  sub_type = y
     * 14 :    对应sql条件 type = 14
     * - :     对应sql条件 type is null or sub_type is null 两个至少有一个为空
     * 二级分类逻辑
     * -101 :   对应sql条件 type = 14 and （sub_type is  null or sub_type =1）
     * -1 * sub_type - 100  && 不是-101 :  对应sql条件 type = 14 and  sub_type  is not null
     * z 其它: 对应sql条件 custom_type_id =z (未指定对应二级分类id为0:custom_type_id=0)
     *
     * @param param    param
     * @param goodsIds goodsIds
     * @return List<InventoryGoods>
     */
    public List<InventoryGoods> goods(GoodsInventoryParam param, List<String> goodsIds) {
        long startTime = System.currentTimeMillis();
        List<InventoryGoods> result = new ArrayList<>();
        List<String> goodsList = new ArrayList<>();
        Set<Integer> classifyLevel2Set = new HashSet<>();
        Set<Long> goodsSpuSpecIdsSet = new HashSet<>();
        Set<Long> profitCategoryTypeIdSet = new HashSet<>();
        //查询满足条件的商品记录，并分页
        ArrayList<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        // 如果用户勾选库存有变动 先查询发生过进销存的药品
        if (param.getStockHaveChange() == 1) {
            List<InventoryGoods> inventoryGoodsList = hologresGoodsInventoryMapper
                    .selectGoods(TableUtils.getCisTable(),
                            new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                    param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                                    param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                                    param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                                    param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                                    Integer.valueOf(param.getHisType())), goodsIds, feeTypeIdsList);
            if (!CollUtil.isEmpty(inventoryGoodsList)) {
                goodsList.addAll(inventoryGoodsList.stream().map(InventoryGoods::getGoodsId).collect(Collectors.toSet()));
            } else {
                return result;
            }
            logger.error("进销存统计-汇总查询进销存数据耗时：{}毫秒", System.currentTimeMillis() - startTime);
            List<InventoryGoods> inventoryBeginGoodsList = new ArrayList<>();
            List<InventoryGoods> inventoryEndGoodsList = new ArrayList<>();
            List<InventoryGoods> taxRatModifyList = new ArrayList<>();
            try {
                if (!CollUtil.isEmpty(goodsList) && goodsList.size() > CommonConstants.NUMBER_FIVE_THOUSAND) {
                    int toIndex = CommonConstants.NUMBER_FIVE_THOUSAND;
                    for (int i = 0; i < goodsList.size(); i += CommonConstants.NUMBER_FIVE_THOUSAND) {
                        if (i + CommonConstants.NUMBER_FIVE_THOUSAND > goodsList.size()) {
                            toIndex = goodsList.size() - i;
                        }
                        List<String> newGoodsList = goodsList.subList(i, toIndex + i);
                        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
                        CompletableFuture<List<InventoryGoods>> goodsBeginListFuture = getInventoryBeginData(param, feeTypeIdsList, newGoodsList, tempTable);
                        CompletableFuture<List<InventoryGoods>> goodsEndListFuture = getInventoryEndData(param, feeTypeIdsList, newGoodsList, tempTable);
                        CompletableFuture<List<InventoryGoods>> taxRatModifyListFuture = getInventoryTaxRatData(param, newGoodsList);
                        CompletableFuture.allOf(goodsBeginListFuture, goodsEndListFuture, taxRatModifyListFuture).join();
                        inventoryBeginGoodsList.addAll(goodsBeginListFuture.get());
                        inventoryEndGoodsList.addAll(goodsEndListFuture.get());
                        taxRatModifyList.addAll(taxRatModifyListFuture.get());
                    }
                } else {
                    TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
                    CompletableFuture<List<InventoryGoods>> goodsBeginListFuture = getInventoryBeginData(param, feeTypeIdsList, goodsList, tempTable);
                    CompletableFuture<List<InventoryGoods>> goodsEndListFuture = getInventoryEndData(param, feeTypeIdsList, goodsList, tempTable);
                    CompletableFuture<List<InventoryGoods>> taxRatModifyListFuture = getInventoryTaxRatData(param, goodsList);
                    CompletableFuture.allOf(goodsBeginListFuture, goodsEndListFuture, taxRatModifyListFuture).join();
                    inventoryBeginGoodsList.addAll(goodsBeginListFuture.get());
                    inventoryEndGoodsList.addAll(goodsEndListFuture.get());
                    taxRatModifyList.addAll(taxRatModifyListFuture.get());
                }
                // 组装期初期末数据
                inventoryGoodsList = processingBeginAndEndData(inventoryGoodsList, inventoryBeginGoodsList, inventoryEndGoodsList, taxRatModifyList, goodsList);
                logger.error("进销存统计-汇总查询进销存期初期末数据耗时：{}毫秒", System.currentTimeMillis() - startTime);
                classifyLevel2Set.addAll(inventoryGoodsList.stream().map(InventoryGoods::getFeeType2).collect(Collectors.toSet()));
                profitCategoryTypeIdSet.addAll(inventoryGoodsList.stream().map(InventoryGoods::getProfitCategoryTypeId).collect(Collectors.toSet()));
                CompletableFuture<Map<String, V2GoodsClassify>> goodsPriceAndSupplierFuture = getGoodsPriceAndSupplier(param, goodsList);
                CompletableFuture<Map<String, V2GoodsClassify>> goodsInfoFuture
                        = abcyunExecutorPool.supplyAsync(() -> {
                    return dimensionQuery.queryGoodsInfo(TableUtils.getCisGoodsTable(),
                            new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                    param.getFeeType1(), param.getFeeType2(), null, null,
                                    param.getParams().getHeaderClinicId(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                                    param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), goodsList, feeTypeIdsList);
                }).exceptionally(e -> {
                    throw new CompletionException(e);
                });
                CompletableFuture<Map<Integer, V2GoodsCustomType>> classifyLevel2MapFuture = getV2GoodsCustomTypeData(classifyLevel2Set);
                CompletableFuture<Map<Long, V2GoodsProfitCategoryType>> goodsProfitCategoryTypeMapFuture = abcyunExecutorPool.supplyAsync(() -> {
                    return dimensionQuery.queryGoodsProfitCategoryTypeByChainIdAndId(param.getChainId(), profitCategoryTypeIdSet);
                }).exceptionally(e -> {
                    throw new CompletionException(e);
                });
                Map<String, InventoryGoods> inventoryGoodsMap = new HashMap<>();
                if (inventoryGoodsList.size() > 0) {
                    for (InventoryGoods inventoryGoods : inventoryGoodsList) {
                        inventoryGoodsMap.put(inventoryGoods.getGoodsId(), inventoryGoods);
                    }
                }
                Map<String, V2GoodsClassify> goodsInfo = goodsInfoFuture.get();
                selectGoodsSetGoodsSpuSpec(param, goodsSpuSpecIdsSet, goodsInfo);
                goodsInfoPriceProcesing(goodsInfo, goodsPriceAndSupplierFuture.get());
                Map<Integer, V2GoodsCustomType> customTypeMap = classifyLevel2MapFuture.get();
                Map<Long, V2GoodsProfitCategoryType> goodsProfitCategoryTypeMap = goodsProfitCategoryTypeMapFuture.get();
                logger.error("进销存统计-汇总查询进销存维度数据数据耗时：{}毫秒", System.currentTimeMillis() - startTime);
                goodsInfoDataProcessing(goodsInfo, customTypeMap, inventoryGoodsMap, result, param, goodsProfitCategoryTypeMap);
            } catch (Exception e) {
                logger.error("进销存药品数据查询组装错误{}", e.getMessage());
            }
        } else {
            Map<String, V2GoodsClassify> goodsInfo = dimensionQuery.queryGoodsInfo(TableUtils.getCisGoodsTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                            param.getParams().getHeaderClinicId(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                            param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), goodsIds, feeTypeIdsList);
            logger.error("进销存统计-汇总查询进销存goods数据数据耗时：{}毫秒", System.currentTimeMillis() - startTime);
            if (goodsInfo.size() > 0) {
                for (Map.Entry<String, V2GoodsClassify> entry : goodsInfo.entrySet()) {
                    goodsList.add(entry.getKey());
                    classifyLevel2Set.add(entry.getValue().getClassifyLevel2());
                    goodsSpuSpecIdsSet.add(entry.getValue().getExtendSpecId());
                    profitCategoryTypeIdSet.add(entry.getValue().getProfitCategoryType());
                }
            } else {
                return result;
            }
            List<InventoryGoods> inventoryGoodsList = new ArrayList<>();
            List<InventoryGoods> inventoryBeginGoodsList = new ArrayList<>();
            List<InventoryGoods> inventoryEndGoodsList = new ArrayList<>();
            List<InventoryGoods> taxRatModifyList = new ArrayList<>();
            try {
                if (!CollUtil.isEmpty(goodsList) && goodsList.size() > CommonConstants.NUMBER_FIVE_THOUSAND) {
                    int toIndex = CommonConstants.NUMBER_FIVE_THOUSAND;
                    for (int i = 0; i < goodsList.size(); i += CommonConstants.NUMBER_FIVE_THOUSAND) {
                        if (i + CommonConstants.NUMBER_FIVE_THOUSAND > goodsList.size()) {
                            toIndex = goodsList.size() - i;
                        }
                        List<String> newGoodsList = goodsList.subList(i, toIndex + i);
                        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
                        CompletableFuture<List<InventoryGoods>> goodsActionListFuture
                                = abcyunExecutorPool.supplyAsync(() -> {
                            return hologresGoodsInventoryMapper
                                    .selectGoods(TableUtils.getCisTable(),
                                            new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                                    param.getFeeType1(), param.getFeeType2(), null, null,
                                                    param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                                                    param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                                                    param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                                                    Integer.valueOf(param.getHisType())), newGoodsList, feeTypeIdsList);
                        }).exceptionally(e -> {
                            throw new CompletionException(e);
                        });
                        CompletableFuture<List<InventoryGoods>> goodsBeginListFuture = getInventoryBeginData(param, feeTypeIdsList, newGoodsList, tempTable);
                        CompletableFuture<List<InventoryGoods>> goodsEndListFuture = getInventoryEndData(param, feeTypeIdsList, newGoodsList, tempTable);
                        CompletableFuture<List<InventoryGoods>> taxRatModifyListFuture = getInventoryTaxRatData(param, newGoodsList);
                        CompletableFuture.allOf(goodsActionListFuture, goodsBeginListFuture, goodsEndListFuture, taxRatModifyListFuture).join();
                        inventoryBeginGoodsList.addAll(goodsBeginListFuture.get());
                        inventoryEndGoodsList.addAll(goodsEndListFuture.get());
                        inventoryGoodsList.addAll(goodsActionListFuture.get());
                        taxRatModifyList.addAll(taxRatModifyListFuture.get());
                    }
                } else {
                    TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
                    CompletableFuture<List<InventoryGoods>> goodsActionListFuture
                            = abcyunExecutorPool.supplyAsync(() -> {
                        return hologresGoodsInventoryMapper
                                .selectGoods(TableUtils.getCisTable(),
                                        new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                                param.getFeeType1(), param.getFeeType2(), null, null,
                                                param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                                                param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                                                param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                                                Integer.valueOf(param.getHisType())), goodsList, feeTypeIdsList);
                    }).exceptionally(e -> {
                        throw new CompletionException(e);
                    });
                    CompletableFuture<List<InventoryGoods>> goodsBeginListFuture = getInventoryBeginData(param, feeTypeIdsList, goodsList, tempTable);
                    CompletableFuture<List<InventoryGoods>> goodsEndListFuture = getInventoryEndData(param, feeTypeIdsList, goodsList, tempTable);
                    CompletableFuture<List<InventoryGoods>> taxRatModifyListFuture = getInventoryTaxRatData(param, goodsList);
                    CompletableFuture.allOf(goodsActionListFuture, goodsBeginListFuture, goodsEndListFuture, taxRatModifyListFuture).join();
                    inventoryBeginGoodsList.addAll(goodsBeginListFuture.get());
                    inventoryEndGoodsList.addAll(goodsEndListFuture.get());
                    inventoryGoodsList.addAll(goodsActionListFuture.get());
                    taxRatModifyList.addAll(taxRatModifyListFuture.get());
                }
                // 组装期初期末数据
                inventoryGoodsList = processingBeginAndEndData(inventoryGoodsList, inventoryBeginGoodsList, inventoryEndGoodsList, taxRatModifyList, goodsList);
                logger.error("进销存统计-汇总查询进销存期初期末action数据数据耗时：{}毫秒", System.currentTimeMillis() - startTime);
                CompletableFuture<Map<String, V2GoodsClassify>> goodsPriceAndSupplierFuture = getGoodsPriceAndSupplier(param, goodsList);
                CompletableFuture<Map<Integer, V2GoodsCustomType>> classifyLevel2MapFuture = getV2GoodsCustomTypeData(classifyLevel2Set);
                CompletableFuture<Map<Long, V2GoodsClassify>> goodsSpuSpecFuture = getGoodsSpuSpec(goodsSpuSpecIdsSet, param.getChainId());
                CompletableFuture<Map<Long, V2GoodsProfitCategoryType>> goodsProfitCategoryTypeMapFuture = abcyunExecutorPool.supplyAsync(() -> {
                    return dimensionQuery.queryGoodsProfitCategoryTypeByChainIdAndId(param.getChainId(), profitCategoryTypeIdSet);
                }).exceptionally(e -> {
                    throw new CompletionException(e);
                });
                Map<String, InventoryGoods> inventoryGoodsMap = new HashMap<>();
                if (inventoryGoodsList.size() > 0) {
                    for (InventoryGoods inventoryGoods : inventoryGoodsList) {
                        inventoryGoodsMap.put(inventoryGoods.getGoodsId(), inventoryGoods);
                    }
                }
                if (CisJWTUtils.isSupportBusiness(param.getSupportedBusiness(), CisJWTUtils.CisBusiness.CIS_BUSINESS_EYE)) {
                    Map<Long, V2GoodsClassify> v2GoodsCpuSpecMap = goodsSpuSpecFuture.get();
                    for (Map.Entry<String, V2GoodsClassify> entry : goodsInfo.entrySet()) {
                        if (!BeanUtil.isEmpty(v2GoodsCpuSpecMap.get(entry.getValue().getExtendSpecId()))) {
                            entry.getValue().setSpuSpecMessage(v2GoodsCpuSpecMap.get(entry.getValue().getExtendSpecId()));
                        }
                    }
                }
                goodsInfoPriceProcesing(goodsInfo, goodsPriceAndSupplierFuture.get());
                Map<Integer, V2GoodsCustomType> customTypeMap = classifyLevel2MapFuture.get();
                Map<Long, V2GoodsProfitCategoryType> goodsProfitCategoryTypeMap = goodsProfitCategoryTypeMapFuture.get();
                goodsInfoDataProcessing(goodsInfo, customTypeMap, inventoryGoodsMap, result, param, goodsProfitCategoryTypeMap);
                logger.error("进销存统计-汇总查询进销存维度数据查询组装数据耗时：{}毫秒", System.currentTimeMillis() - startTime);
            } catch (Exception e) {
                logger.error("进销存药品数据查询组装错误{}", e.getMessage());
            }
        }
        logger.error("进销存统计-汇总查询进销存汇总数据耗时：{}毫秒", System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 查询药品最近进价以及最近供应商
     *
     * @param param     进销存param
     * @param goodsList 药品ids
     * @return 药品最近进价以及最近供应商Map
     */
    private CompletableFuture<Map<String, V2GoodsClassify>> getGoodsPriceAndSupplier(GoodsInventoryParam param, List<String> goodsList) {
        return abcyunExecutorPool.supplyAsync(() -> {
            return dimensionQuery.queryGoodsInfoPrice(TableUtils.getCisGoodsTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                            param.getSelectGoodsClinicId(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                            param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), goodsList);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
    }

    /**
     * 查询goods扩展字段
     *
     * @param goodsSpuSpecIdSet 药品扩展信息ids
     * @param chainId           连锁id
     * @return goods扩展字段Map
     */
    private CompletableFuture<Map<Long, V2GoodsClassify>> getGoodsSpuSpec(Set<Long> goodsSpuSpecIdSet,
                                                                          String chainId) {
        return abcyunExecutorPool.supplyAsync(() -> {
            return dimensionQuery.selectV2GoodsSpuSpec(TableUtils.getCisGoodsTable(), chainId, goodsSpuSpecIdSet);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
    }

    /**
     * 查询药品自定义类型
     *
     * @param classifyLevel2Set 自定义类型idSet
     * @return 药品自定义类型
     */
    private CompletableFuture<Map<Integer, V2GoodsCustomType>> getV2GoodsCustomTypeData(Set<Integer> classifyLevel2Set) {
        return abcyunExecutorPool.supplyAsync(() -> {
            return dimensionQuery.queryCustomTypeByIds(classifyLevel2Set);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
    }

    /**
     * 查询进销存税率数据
     *
     * @param param        进销存param
     * @param newGoodsList 药品ids
     * @return 进销存税率数据
     */
    private CompletableFuture<List<InventoryGoods>> getInventoryTaxRatData(GoodsInventoryParam param, List<String> newGoodsList) {
        return abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper
                    .selectTaxRatModify(TableUtils.getCisTable(),
                            new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                    param.getFeeType1(), param.getFeeType2(), null, null,
                                    param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                                    param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), newGoodsList);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
    }

    /**
     * 查询进销存期末数据
     *
     * @param param          进销存param
     * @param feeTypeIdsList 费用类型ids
     * @param newGoodsList   药品ids
     * @param tempTable      期初期末表信息
     * @return 期末数据
     */
    private CompletableFuture<List<InventoryGoods>> getInventoryEndData(GoodsInventoryParam param,
                                                                        ArrayList<Long> feeTypeIdsList,
                                                                        List<String> newGoodsList,
                                                                        TempTable tempTable) {
        return abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper
                    .selectEndGoods(TableUtils.getCisTable(),
                            new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                    param.getFeeType1(), param.getFeeType2(), null, null,
                                    param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                                    param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), newGoodsList, feeTypeIdsList, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
    }

    /**
     * 查询进销存期初数据
     *
     * @param param          进销存param
     * @param feeTypeIdsList 费用类型ids
     * @param newGoodsList   药品ids
     * @param tempTable      期初期末表信息
     * @return 期初数据
     */
    private CompletableFuture<List<InventoryGoods>> getInventoryBeginData(GoodsInventoryParam param,
                                                                          ArrayList<Long> feeTypeIdsList,
                                                                          List<String> newGoodsList,
                                                                          TempTable tempTable) {
        return abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper
                    .selectBeginGoods(TableUtils.getCisTable(),
                            new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                    param.getFeeType1(), param.getFeeType2(), null, null,
                                    param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                                    param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), newGoodsList, feeTypeIdsList, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
    }

    /**
     * 处理期初期末数据
     *
     * @param inventoryGoodsList      进销存药品action数据
     * @param inventoryBeginGoodsList 进销存药品begin数据
     * @param inventoryEndGoodsList   进销存药品end数据
     * @param taxRatModifyList        进销存税率修正数据
     * @param goodsIds                药品ids
     */
    private List<InventoryGoods> processingBeginAndEndData(List<InventoryGoods> inventoryGoodsList,
                                                           List<InventoryGoods> inventoryBeginGoodsList,
                                                           List<InventoryGoods> inventoryEndGoodsList,
                                                           List<InventoryGoods> taxRatModifyList,
                                                           List<String> goodsIds) {
        Map<String, List<InventoryGoods>> beginMap =
                inventoryBeginGoodsList.stream().collect(Collectors.groupingBy(InventoryGoods::getGoodsId));
        Map<String, List<InventoryGoods>> endMap =
                inventoryEndGoodsList.stream().collect(Collectors.groupingBy(InventoryGoods::getGoodsId));
        Map<String, List<InventoryGoods>> actionMap =
                inventoryGoodsList.stream().collect(Collectors.groupingBy(InventoryGoods::getGoodsId));
        Map<String, List<InventoryGoods>> taxRatModifyMap =
                taxRatModifyList.stream().collect(Collectors.groupingBy(InventoryGoods::getGoodsId));
        List<InventoryGoods> goodsList = new ArrayList<>();
        for (String goodsId : goodsIds) {
            InventoryGoods goods = null;
            if (!CollUtil.isEmpty(actionMap.get(goodsId))) {
                goods = actionMap.get(goodsId).get(0);
            } else if (!CollUtil.isEmpty(beginMap.get(goodsId))) {
                goods = beginMap.get(goodsId).get(0);
            } else if (!CollUtil.isEmpty(endMap.get(goodsId))) {
                goods = endMap.get(goodsId).get(0);
            }
            if (goods != null) {
                goods.setBeginAndEndData(beginMap.get(goodsId), endMap.get(goodsId));
                goodsList.add(goods);
            }
            if (!CollUtil.isEmpty(taxRatModifyMap.get(goodsId))) {
                InventoryGoods taxGoods = taxRatModifyMap.get(goodsId).get(0);
                if (taxGoods != null && goods != null) {
                    goods.setTaxModify(taxRatModifyMap.get(goodsId));
                }
            }
        }
        return goodsList;
    }

    /**
     * 处理价格以及供应商等维度数据
     *
     * @param goodsInfo             goodsInfo
     * @param goodsPriceAndSupplier goodsPriceAndSupplier
     */
    private void goodsInfoPriceProcesing(Map<String, V2GoodsClassify> goodsInfo,
                                         Map<String, V2GoodsClassify> goodsPriceAndSupplier) {

        for (Map.Entry<String, V2GoodsClassify> entry : goodsInfo.entrySet()) {
            V2GoodsClassify v2GoodsClassify = goodsPriceAndSupplier.get(entry.getKey());
            if (v2GoodsClassify != null) {
                entry.getValue().setPackagePrice(v2GoodsClassify.getPackagePrice());
                entry.getValue().setPiecePrice(v2GoodsClassify.getPiecePrice());
                entry.getValue().setLastPackageCostPrice(v2GoodsClassify.getLastPackageCostPrice());
                entry.getValue().setLastStockInOrderSupplier(v2GoodsClassify.getLastStockInOrderSupplier());
            }
        }
    }

    /**
     * @param param    param
     * @param goodsIds goodsIds
     * @return InventoryGoods
     */
    public InventoryGoods goodsSummary(GoodsInventoryParam param, List<String> goodsIds) {
        long startTime = System.currentTimeMillis();
        ArrayList<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        goodsIds = stockHaveChangeGoodsIds(param, goodsIds, feeTypeIdsList, true);
        ArrayList<InventoryGoods> taxModifyList = new ArrayList<>();
        String feeType1 = null;
        if (param.getFee1() != null && param.getFee1().length() > 0) {
            feeType1 = SelectHandler.buildChargeFeeClassify1Sql(param.getFee1())
                    .replaceAll("classify_level_1_id", "classify_level1");
        }
        String feeType2 = null;
        if (param.getFee2() != null && param.getFee2().length() > 0) {
            feeType2 = SelectHandler.buildChargeFeeClassify2Sql(param.getFee2())
                    .replaceAll("classify_level_1_id", "classify_level1")
                    .replaceAll("classify_level_2_id", "classify_level2");
        }
        param.setFeeType1(feeType1);
        param.setFeeType2(feeType2);
        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
        param.setSubBeginDate(param.getBeginDate().substring(0, CommonConstants.DATE_LENGTH));
        param.setSubEndDate(param.getEndDate().substring(0, CommonConstants.DATE_LENGTH));
        InventoryGoods ig = null;
        if (!CollUtil.isEmpty(goodsIds) && goodsIds.size() > CommonConstants.NUMBER_FIVE_THOUSAND) {
            int toIndex = CommonConstants.NUMBER_FIVE_THOUSAND;
            List<InventoryGoods> inventoryGoodsList = new ArrayList<>();
            for (int i = 0; i < goodsIds.size(); i += CommonConstants.NUMBER_FIVE_THOUSAND) {
                if (i + CommonConstants.NUMBER_FIVE_THOUSAND > goodsIds.size()) {
                    toIndex = goodsIds.size() - i;
                }
                List<String> newGoodsList = goodsIds.subList(i, toIndex + i);
                // 期初action期末拆分
                CompletableFuture<InventoryGoods> goodsBeginSummaryFuture = abcyunExecutorPool.supplyAsync(() -> {
                    return hologresGoodsInventoryMapper
                            .selectGoodsSummaryBegin(TableUtils.getCisTable(), param, newGoodsList, feeTypeIdsList, tempTable);
                }).exceptionally(e -> {
                    throw new CompletionException(e);
                });
                CompletableFuture<InventoryGoods> goodsEndSummaryFuture = abcyunExecutorPool.supplyAsync(() -> {
                    return hologresGoodsInventoryMapper
                            .selectGoodsSummaryEnd(TableUtils.getCisTable(), param, newGoodsList, feeTypeIdsList, tempTable);
                }).exceptionally(e -> {
                    throw new CompletionException(e);
                });
                CompletableFuture<InventoryGoods> goodsActionSummaryFuture = abcyunExecutorPool.supplyAsync(() -> {
                    return hologresGoodsInventoryMapper
                            .selectGoodsSummary(TableUtils.getCisTable(), param, newGoodsList, feeTypeIdsList);
                }).exceptionally(e -> {
                    throw new CompletionException(e);
                });
                CompletableFuture<InventoryGoods> taxRatModifyListFuture
                        = abcyunExecutorPool.supplyAsync(() -> {
                    return hologresGoodsInventoryMapper
                            .selectSummaryTaxRatModify(TableUtils.getCisTable(), param, newGoodsList);
                }).exceptionally(e -> {
                    throw new CompletionException(e);
                });
                CompletableFuture.allOf(goodsBeginSummaryFuture, goodsActionSummaryFuture,
                        goodsEndSummaryFuture, taxRatModifyListFuture).join();
                InventoryGoods inventoryGoods = null;
                try {
                    InventoryGoods beginIg = goodsBeginSummaryFuture.get();
                    InventoryGoods endIg = goodsEndSummaryFuture.get();
                    InventoryGoods actionIg = goodsActionSummaryFuture.get();
                    InventoryGoods taxModify = taxRatModifyListFuture.get();
                    taxModifyList.add(taxModify);
                    if (!BeanUtil.isEmpty(actionIg)) {
                        inventoryGoods = actionIg;
                    } else if (!BeanUtil.isEmpty(beginIg)) {
                        inventoryGoods = beginIg;
                    } else if (!BeanUtil.isEmpty(endIg)) {
                        inventoryGoods = endIg;
                    }
                    if (!BeanUtil.isEmpty(inventoryGoods)) {
                        // 期初期末数据组装
                        inventoryGoods.setBeginAndEndDataSummary(beginIg, endIg);
                        inventoryGoods.pretty(param.getIsExport());
                        inventoryGoods.setTaxModify(taxModifyList);
                        inventoryGoods.pretty(param.getIsExport());
                        if (!param.getPermission().isEnableCost()) {
                            inventoryGoods.setCostNull();
                        }
                        inventoryGoods.setInTaxRatText("-");
                        inventoryGoods.setOutTaxRatText("-");
                        inventoryGoodsList.add(inventoryGoods);
                    }
                } catch (Exception e) {
                    logger.info("查询进销存合计行数据组装异常:{}", e.getMessage());
                }
            }
            for (int l = 0; l < inventoryGoodsList.size(); l++) {
                if (l == 0) {
                    ig = inventoryGoodsList.get(l);
                } else {
                    ig.setSummaryData(inventoryGoodsList.get(l));
                }
            }
        } else {
            // 期初action期末拆分
            List<String> finalGoodsIds = goodsIds;
            CompletableFuture<InventoryGoods> goodsBeginSummaryFuture = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectGoodsSummaryBegin(TableUtils.getCisTable(), param, finalGoodsIds, feeTypeIdsList, tempTable);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture<InventoryGoods> goodsEndSummaryFuture = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectGoodsSummaryEnd(TableUtils.getCisTable(), param, finalGoodsIds, feeTypeIdsList, tempTable);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture<InventoryGoods> goodsActionSummaryFuture = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectGoodsSummary(TableUtils.getCisTable(), param, finalGoodsIds, feeTypeIdsList);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture<InventoryGoods> taxRatModifyListFuture
                    = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectSummaryTaxRatModify(TableUtils.getCisTable(), param, finalGoodsIds);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture.allOf(goodsBeginSummaryFuture, goodsActionSummaryFuture,
                    goodsEndSummaryFuture, taxRatModifyListFuture).join();
            try {
                InventoryGoods beginIg = goodsBeginSummaryFuture.get();
                InventoryGoods endIg = goodsEndSummaryFuture.get();
                InventoryGoods actionIg = goodsActionSummaryFuture.get();
                InventoryGoods taxModify = taxRatModifyListFuture.get();
                taxModifyList.add(taxModify);
                if (!BeanUtil.isEmpty(actionIg)) {
                    ig = actionIg;
                } else if (!BeanUtil.isEmpty(beginIg)) {
                    ig = beginIg;
                } else if (!BeanUtil.isEmpty(endIg)) {
                    ig = endIg;
                }
                if (!BeanUtil.isEmpty(ig)) {
                    // 期初期末数据组装
                    ig.setBeginAndEndDataSummary(beginIg, endIg);
                    ig.pretty(param.getIsExport());
                    ig.setTaxModify(taxModifyList);
                    ig.pretty(param.getIsExport());
                    if (!param.getPermission().isEnableCost()) {
                        ig.setCostNull();
                    }
                    ig.setInTaxRatText("-");
                    ig.setOutTaxRatText("-");
                }
            } catch (Exception e) {
                logger.info("查询进销存合计行数据组装异常:{}", e.getMessage());
            }
        }
        logger.error("进销存统计-汇总查询合计行数据耗时:{}毫秒", System.currentTimeMillis() - startTime);
        return ig;
    }

    /**
     * @param goodsInfo                  goodsInfo
     * @param classifyLevel2Map          classifyLevel2Map
     * @param inventoryGoodsMap          inventoryGoodsMap
     * @param result                     result
     * @param param                      param
     * @param goodsProfitCategoryTypeMap 利润分类map
     */
    private void goodsInfoDataProcessing(Map<String, V2GoodsClassify> goodsInfo,
                                         Map<Integer, V2GoodsCustomType> classifyLevel2Map,
                                         Map<String, InventoryGoods> inventoryGoodsMap,
                                         List<InventoryGoods> result, GoodsInventoryParam param,
                                         Map<Long, V2GoodsProfitCategoryType> goodsProfitCategoryTypeMap) {
        Map<Long, V2GoodsFeeType> v2GoodsFeeTypeMap = dimensionQuery
                .queryGoodsFeeTypeInfo(TableUtils.getCisGoodsTable(), param.getChainId());
        for (Map.Entry<String, V2GoodsClassify> entry : goodsInfo.entrySet()) {
            InventoryGoods ig = new InventoryGoods();
            ig.setGoodsId(entry.getKey());
            V2GoodsClassify goods = entry.getValue();
            //相当于goodsInfo左关联inventoryGoodsList
            if (inventoryGoodsMap.containsKey(entry.getKey())) {
                InventoryGoods inventoryGoods = inventoryGoodsMap.get(entry.getKey());
                BeanUtil.copyProperties(inventoryGoods, ig);
            } else {
                mapNotDataInventoryGoodsSetDefaultValue(ig, param);
            }
            goodsInfoGoodsIsNotNullDataProcessing(goods, ig, classifyLevel2Map, param.getHeaderType(), v2GoodsFeeTypeMap, goodsProfitCategoryTypeMap, param.getHisType());
            ig.pretty(param.getIsExport());
            if (!param.getPermission().isEnableCost()) {
                ig.setCostNull();
            }
            result.add(ig);

        }
    }

    /**
     * @param ig    ig
     * @param param param
     */
    private void mapNotDataInventoryGoodsSetDefaultValue(InventoryGoods ig, GoodsInventoryParam param) {
        if (ig.getFeeType1Text() == null) {
            ig.setFeeType1Text("-");
        }
        if (ig.getFeeType2Text() == null) {
            ig.setFeeType2Text("-");
        }
        if (ig.getManufacturer() == null || "".equals(ig.getManufacturer())) {
            ig.setManufacturer("-");
        }
        if (ig.getSpecification() == null || "".equals(ig.getSpecification())) {
            ig.setSpecification("-");
        }
        ig.setEndCountText("0.00");
        ig.setEndCostText("0.00");
        ig.setEndCostExcludeTaxText("0.00");
        mapNotDataInventoryGoodsSetCheckDefaultValue(ig);
        //发药
        ig.setDispenseCountText("0.00");
        ig.setDispenseCostText("0.00");
        ig.setDispenseCostExcludeTaxText("0.00");
        ig.setDispensePriceText("0.00");
        ig.setDispensePriceExcludeTaxText("0.00");
//        //退药
//        ig.setReturnCountText("0.00");
//        ig.setReturnCostText("0.00");
//        ig.setReturnCostExcludeTaxText("0.00");
//        ig.setReturnPriceText("0.00");
//        ig.setReturnPriceExcludeTaxText("0.00");
//        //入库
//        ig.setInCountText("0.00");
//        ig.setInCostText("0.00");
//        ig.setInCostExcludeTaxText("0.00");
//        //调拨
//        ig.setAllotCountText("0.00");
//        ig.setAllotCostText("0.00");
//        ig.setAllotCostExcludeTaxText("0.00");
//        //调拨
//        ig.setAllotInCountText("0.00");
//        ig.setAllotInCostText("0.00");
//        ig.setAllotInCostExcludeTaxText("0.00");
//        //调拨
//        ig.setAllotOutCountText("0.00");
//        ig.setAllotOutCostText("0.00");
//        ig.setAllotOutCostExcludeTaxText("0.00");
//        //出库
//        ig.setOutCountText("0.00");
//        ig.setOutCostText("0.00");
//        ig.setOutCostExcludeTaxText("0.00");
        // 领料出库
        ig.setCollectOutCountText("0.00");
        ig.setCollectOutCostText("0.00");
        ig.setCollectOutCostExcludeTaxText("0.00");
        // 破损出库
        ig.setDamagedOutCountText("0.00");
        ig.setDamagedOutCostText("0.00");
        ig.setDamagedOutCostExcludeTaxText("0.00");

        ig.setBeginCountText("0.00");
        ig.setBeginCostText("0.00");
        ig.setBeginCostExcludeTaxText("0.00");
    }

    /**
     * @param ig ig
     */
    private void mapNotDataInventoryGoodsSetCheckDefaultValue(InventoryGoods ig) {
        // 盘盈入库
        ig.setCheckInCountText("0.00");
        ig.setCheckInCostText("0.00");
        ig.setCheckInCostExcludeTaxText("0.00");
        ig.setCheckInPriceText("0.00");
        ig.setCheckInPriceExcludeTaxText("0.00");
        // 盘亏出库
        ig.setCheckOutCountText("0.00");
        ig.setCheckOutCostText("0.00");
        ig.setCheckOutCostExcludeTaxText("0.00");
        ig.setCheckOutPriceText("0.00");
        ig.setCheckOutPriceExcludeTaxText("0.00");
    }

    /**
     * @param goods                      goods
     * @param ig                         ig
     * @param classifyLevel2Map          classifyLevel2Map
     * @param headerType                 表头类型 stockContainsEyeglass
     * @param goodsProfitCategoryTypeMap 利润分类map
     * @param hisType
     */
    private void goodsInfoGoodsIsNotNullDataProcessing(V2GoodsClassify goods, InventoryGoods ig,
                                                       Map<Integer, V2GoodsCustomType> classifyLevel2Map,
                                                       String headerType, Map<Long, V2GoodsFeeType> v2GoodsFeeTypeMap,
                                                       Map<Long, V2GoodsProfitCategoryType> goodsProfitCategoryTypeMap, String hisType) {
        if (goods != null) {
            //商品编码 为空或者为1则显示-
            ig.setGoodsShortId(!notNull(goods.getShortId()) || goods.getShortId().equals("1") ? "-"
                    : goods.getShortId());
            //药品物资 规格逻辑
            if (!StrUtil.isBlank(headerType)
                    && headerType.equals(CommonConstants.CONTAINS_EYEGLASS_HEADER_TYPE)) {
                ig.setSpecification(V2GoodsClassify.setSpecificationBySpecColum(goods));
            } else {
                if (goods.getType() != null && goods.getSubType() != null && (goods.getType() == 1
                        || goods.getType() == TWO || goods.getType() == SEVEN)) {
                    String spe = GoodsHandler.handleGoodsSpec(goods.getType(), goods.getSubType(),
                            goods.getPieceNum(), goods.getPieceUnit(), goods.getPackageUnit(),
                            goods.getMedicineDosageNum(), goods.getMedicineDosageUnit(),
                            goods.getMaterialSpec(), goods.getExtendSpec());
                    ig.setSpecification(spe);
                } else {
                    ig.setSpecification(V2GoodsClassify.setSpecificationBySpecColum(goods));
                }
            }
            //药品名称
            if (notNull(goods.getName()) && notNull(goods.getMedicineCadn())) {
                ig.setGoodsText(goods.getMedicineCadn() + "(" + goods.getName() + ")");
            } else if (notNull(goods.getMedicineCadn())) {
                ig.setGoodsText(goods.getMedicineCadn());
            } else {
                ig.setGoodsText(notNull(goods.getName()) ? goods.getName() : "-");
            }
            //厂家
            ig.setManufacturer(goods.getManufacturer());
            ig.setNmpn(goods.getMedicineNmpn());
            //根据一级分类和二级分类id获取其名称 ,需要在V2Goods对象中增加
            ig.setFeeType1Text(dimensionQuery.queryProductClassifyLevel1(goods.getOrganId(), goods.getClassifyLevel1(), hisType));
            V2GoodsCustomType t = classifyLevel2Map.get(goods.getClassifyLevel2());
            if (t != null) {
                ig.setFeeType2Text(t.getName());
            }
            ig.setInTaxRat(goods.getInTaxRat());
            ig.setOutTaxRat(goods.getOutTaxRat());
            ig.setSheBaoCodeNationalCode(goods.getShebaoCodeNationalCode());
            ig.setLastPackageCostPrice(goods.getLastPackageCostPrice());
            ig.setLastStockInOrderSupplier(goods.getLastStockInOrderSupplier());
            if (goods.getType() != null && goods.getSubType() != null && goods.getType() == CommonConstants.NUMBER_ONE
                    && goods.getSubType() == CommonConstants.NUMBER_TWO) {
                ig.setPieceUnit(goods.getPieceUnit());
                if (goods.getPiecePrice() == null) {
                    ig.setPiecePrice(goods.getPackagePrice());
                } else {
                    ig.setPiecePrice(goods.getPiecePrice());
                }
            } else {
                ig.setPieceUnit("-");
                ig.setPiecePrice(goods.getPackagePrice());
            }
            if (goods.getType() != null && goods.getSubType() != null) {
                ig.setMinPieceUnit(goods.getPieceUnit());
            } else {
                ig.setMinPieceUnit("-");
            }
            //设置费用分类
            if (goods.getFeeTypeId() != null && v2GoodsFeeTypeMap.containsKey(goods.getFeeTypeId())) {
                ig.setFeeTypeName(v2GoodsFeeTypeMap.get(goods.getFeeTypeId()).getName());
            }
            ig.setPackageUnit(goods.getPackageUnit());
            ig.setProfitCategoryTypeId(goods.getProfitCategoryType());
            if (!BeanUtil.isEmpty(goodsProfitCategoryTypeMap.get(goods.getProfitCategoryType()))) {
                ig.setProfitCategoryTypeName(goodsProfitCategoryTypeMap.get(goods.getProfitCategoryType()).getName());
            }
            ig.setDosageFormTypeName(GoodsConst.dosageFormType(goods.getDosageFormType()));
            ig.setNationalStandardImplementationName(goods.getNationalStandardImplementationName());
            ig.setMedicineNmpn(goods.getMedicineNmpn());
            ig.setRemark(goods.getRemark());
            ig.apply();
        }
    }

    /**
     * @param chainId chainId
     * @return Map<String, List < V2GoodsSupplier>>
     */
    public Map<String, List<V2GoodsSupplier>> getSupplierList(String chainId) {
        List<V2GoodsSupplier> suppliers = dimensionQuery.querySupplierByChainId(chainId,
                CommonConstants.PROMOTION_TYPE);
        Map res = new HashMap();
        res.put("rows", suppliers);
        return res;
    }


    /**
     * @param param    param
     * @param goodsIds goodsIds
     * @return List<InventoryGoods>
     */
    public List<InventoryGoods> goodsV1(GoodsInventoryParam param, List<String> goodsIds) {
        Set<Long> goodsSpuSpecIdsSet = new HashSet<>();
        ArrayList<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        // 如果用户勾选库存有变动 先查询发生过进销存的药品
        goodsIds = stockHaveChangeGoodsIds(param, goodsIds, feeTypeIdsList, false);
        Set<Integer> classifyLevel2Set = new HashSet();
        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
        List<String> finalGoodsIds = goodsIds;
        CompletableFuture<List<InventoryGoods>> goodsListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectGoods(TableUtils.getCisTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                            param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                            param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                            param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                            Integer.valueOf(param.getHisType())), finalGoodsIds, feeTypeIdsList);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryGoods>> goodsBeginListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectBeginGoods(TableUtils.getCisTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                            param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                            param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), finalGoodsIds, feeTypeIdsList, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryGoods>> goodsEndListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectEndGoods(TableUtils.getCisTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                            param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                            param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), finalGoodsIds, feeTypeIdsList, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryGoods>> taxRatModifyListFuture = getInventoryTaxRatData(param, finalGoodsIds);
        CompletableFuture.allOf(goodsListFuture, goodsBeginListFuture, goodsEndListFuture, taxRatModifyListFuture).join();
        List<InventoryGoods> inventoryGoods = null;
        try {
            inventoryGoods = goodsListFuture.get();
            List<InventoryGoods> inventoryBeginGoods = goodsBeginListFuture.get();
            List<InventoryGoods> inventoryEndGoods = goodsEndListFuture.get();
            List<InventoryGoods> taxRatModifyList = new ArrayList<>();
            List<String> goodsIds1 = new ArrayList<>();
            for (InventoryGoods g : inventoryEndGoods) {
                goodsIds1.add(g.getGoodsId());
                classifyLevel2Set.add(g.getFeeType2());
            }
            inventoryGoods = processingBeginAndEndData(inventoryGoods, inventoryBeginGoods, inventoryEndGoods, taxRatModifyList, goodsIds1);
            Map<Integer, V2GoodsCustomType> classifyLevel2Map = dimensionQuery.queryCustomTypeByIds(classifyLevel2Set);
            Map<String, V2GoodsClassify> goodsInfo = dimensionQuery.queryGoodsInfo(TableUtils.getCisGoodsTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), null, null,
                            param.getParams().getHeaderClinicId()), goodsIds1, feeTypeIdsList);
            selectGoodsSetGoodsSpuSpec(param, goodsSpuSpecIdsSet, goodsInfo);
            Map<String, V2GoodsClassify> goodsPriceAndSupplier = dimensionQuery.queryGoodsInfoPrice(
                    TableUtils.getCisGoodsTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                            param.getHeaderClinicId()), goodsIds1);
            goodsInfoPriceProcesing(goodsInfo, goodsPriceAndSupplier);
            inventoryGoodsProcessing(inventoryGoods, goodsInfo, classifyLevel2Map, param);
        } catch (Exception e) {
            logger.error("进销存goods汇总数据查询组装异常:{}", e.getMessage());
            return null;
        }
        return inventoryGoods;
    }

    public List<String> stockHaveChangeGoodsIds(GoodsInventoryParam
                                                        param, List<String> goodsIds, ArrayList<Long> feeTypeIdsList, boolean isSummary) {
        if (param.getStockHaveChange() == 1) {
            if (isSummary) {
                goodsIds = hologresGoodsInventoryMapper.selectStockHaveChangeGoods(TableUtils.getCisTable(), new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                        param.getFeeType1(), param.getFeeType2(), null, null,
                        param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                        param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                        param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                        Integer.valueOf(param.getHisType())), goodsIds, feeTypeIdsList);
            } else {
                goodsIds = hologresGoodsInventoryMapper.selectStockHaveChangeGoods(TableUtils.getCisTable(), new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                        param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                        param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                        param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                        param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                        Integer.valueOf(param.getHisType())), goodsIds, feeTypeIdsList);
            }

        }
        return goodsIds;
    }

    /**
     * @param inventoryGoods    inventoryGoods
     * @param mpGoods           mpGoods
     * @param classifyLevel2Map classifyLevel2Map
     * @param param             param
     */
    private void inventoryGoodsProcessing
    (List<InventoryGoods> inventoryGoods, Map<String, V2GoodsClassify> mpGoods,
     Map<Integer, V2GoodsCustomType> classifyLevel2Map,
     GoodsInventoryParam param) {
        for (InventoryGoods ig : inventoryGoods) {
            V2GoodsClassify goods = mpGoods.get(ig.getGoodsId());
            if (goods != null) {
                ig.setGoodsShortId(!notNull(goods.getShortId()) || goods.getShortId().equals("1") ? "-"
                        : goods.getShortId());
                if (!StrUtil.isBlank(goods.getSpecification())) {
                    ig.setSpecification(goods.getSpecification());
                } else {
                    if (goods.getType() != null && goods.getSubType() != null && (goods.getType() == 1
                            || goods.getType() == TWO || goods.getType() == SEVEN)) {
                        String spe = GoodsHandler.handleGoodsSpec(goods.getType(), goods.getSubType(),
                                goods.getPieceNum(),
                                goods.getPieceUnit(), goods.getPackageUnit(), goods.getMedicineDosageNum(),
                                goods.getMedicineDosageUnit(),
                                goods.getMaterialSpec(), goods.getExtendSpec());
                        ig.setSpecification(spe);
                    }
                }
                if (notNull(goods.getName()) && notNull(goods.getMedicineCadn())) {
                    ig.setGoodsText(goods.getMedicineCadn() + "(" + goods.getName() + ")");
                } else if (notNull(goods.getMedicineCadn())) {
                    ig.setGoodsText(goods.getMedicineCadn());
                } else {
                    ig.setGoodsText(notNull(goods.getName()) ? goods.getName() : "-");
                }
                ig.setInTaxRat(goods.getInTaxRat());
                ig.setOutTaxRat(goods.getOutTaxRat());
                ig.setManufacturer(goods.getManufacturer());
                ig.setNmpn(goods.getMedicineNmpn());
                ig.setSheBaoCodeNationalCode(goods.getShebaoCodeNationalCode());
                ig.setLastPackageCostPrice(goods.getLastPackageCostPrice());
                ig.setLastStockInOrderSupplier(goods.getLastStockInOrderSupplier());
                if (goods.getType() != null && goods.getSubType() != null
                        && goods.getType() == CommonConstants.NUMBER_ONE
                        && goods.getSubType() == CommonConstants.NUMBER_TWO) {
                    ig.setPieceUnit(goods.getPieceUnit());
                    if (goods.getPiecePrice() == null) {
                        ig.setPiecePrice(goods.getPackagePrice());
                    } else {
                        ig.setPiecePrice(goods.getPiecePrice());
                    }
                } else {
                    ig.setPieceUnit("-");
                    ig.setPiecePrice(goods.getPackagePrice());
                }
                ig.setPackageUnit(goods.getPackageUnit());
                ig.setDosageFormTypeName(GoodsConst.dosageFormType(goods.getDosageFormType()));
                ig.setMedicineNmpn(goods.getMedicineNmpn());
                ig.setRemark(goods.getRemark());
            }

            ig.setFeeType1Text(dimensionQuery.queryProductClassifyLevel1(param.getChainId(), ig.getFeeType1(), param.getHisType()));
            V2GoodsCustomType t = classifyLevel2Map.get(ig.getFeeType2());
            if (t != null) {
                ig.setFeeType2Text(t.getName());
            }

            ig.setBeginCount(ig.getBeginCount());
            ig.setBeginCost(ig.getBeginCost());
            ig.setBeginCostExcludeTax(ig.getBeginCostExcludeTax());

            ig.pretty(param.getIsExport());
            if (!param.getPermission().isEnableCost()) {
                ig.setCostNull();
            }
        }
    }

    /**
     * @param param    param
     * @param goodsIds goodsIds
     * @return List<InventoryRecord>
     * @throws ExecutionException   ExecutionException
     * @throws InterruptedException InterruptedException
     */
    public List<InventoryRecord> record(GoodsInventoryParam param, List<String> goodsIds)
            throws ExecutionException, InterruptedException {
        long stratTime = System.currentTimeMillis();
        ArrayList<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        Set<Long> goodsSpuSpecIdsSet = new HashSet<>();
        List<InventoryRecord> records;
        if (param.getDimension() == 0) {
            //按批次维度
            records = hologresGoodsInventoryMapper
                    .selectRecord(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList,
                            GoodsInventoryActionEnum.toActions(param.getActions(), param.getHisType()));
        } else {
            //按药品维度
            records = hologresGoodsInventoryMapper
                    .selectRecordByGoods(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList,
                            GoodsInventoryActionEnum.toActions(param.getActions(), param.getHisType()));
        }
        logger.error("进销存明细查询明细信息耗时:{}毫秒", System.currentTimeMillis() - stratTime);
        Set<String> supplierIds = new HashSet<>();
        Set<Integer> classifyLevel2Set = new HashSet<>();
        Set<String> orderSet = new HashSet<>();
        Set<Long> profitCategoryTypeIdSet = new HashSet<>();
        Set<Long> stockIdSet = new HashSet<>();
        Set<Long> idSet = new HashSet<>();
        Set<String> organIdSet = new HashSet<>();
        Set<String> patientOrderIdSet = new HashSet<>();

        if (goodsIds == null) {
            goodsIds = new ArrayList<>();
        }
        Set<String> patientIds = new HashSet<>();
        for (InventoryRecord r : records) {
            goodsIds.add(r.getGoodsId());
            supplierIds.add(r.getSupplierId());
            classifyLevel2Set.add(r.getFeeType2());
            patientIds.add(r.getPatientId());
            if (r.getScene() != null && (Objects.equals(r.getScene(), GoodsInventorySceneTypeEnum.EC.getTypeId())
                    || Objects.equals(r.getScene(), GoodsInventorySceneTypeEnum.MT.getTypeId()))) {
                orderSet.add(r.getOrderId());
            }
            profitCategoryTypeIdSet.add(r.getProfitCategoryTypeId());
            stockIdSet.add(r.getStockId());
            if (!StrUtil.isBlank(r.getIds())) {
                String[] idSplit = r.getIds().split(",");
                for (String id : idSplit) {
                    idSet.add(Long.valueOf(id));
                }
            }
            organIdSet.add(r.getOrganId());
            organIdSet.add(r.getCooperationClinicId());
            patientOrderIdSet.add(r.getPatientOrderId());
        }
        List<String> finalGoodsIds = goodsIds;
        CompletableFuture<Map<String, V2GoodsClassify>> goodsInfoFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryGoodsInfo(TableUtils.getCisGoodsTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), null, null,
                            param.getParams().getHeaderClinicId(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                            param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()),
                    finalGoodsIds, feeTypeIdsList);
        });
        CompletableFuture<Map<String, Organ>> organMapFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryOrgansByIds(organIdSet);
        });
        CompletableFuture<Map<String, Employee>> employeeFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryEmployeeByChainId(param.getChainId());
        });
        CompletableFuture<Map<String, V2GoodsSupplier>> supplyFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.querySupplierByIds(supplierIds);
        });
        CompletableFuture<Map<Integer, V2GoodsCustomType>> customTypeFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryCustomTypeByIds(classifyLevel2Set);
        });
        CompletableFuture<Map<String, V2Patient>> patientFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryPatient(param.getChainId(), patientIds, param.getEnablePatientMobile());
        });
        CompletableFuture<Map<String, V1EcOrder>> ecOrderFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryEcOrderByIds(param.getChainId(), orderSet);
        });
        CompletableFuture<Map<String, Map<Integer, String>>> pharmacyFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryPharmacyNameByNo(param.getChainId(), param.getClinicId(), param.getPharmacyType());
        });
        CompletableFuture<Map<Long, V2GoodsFeeType>> v2GoodsFeeTypeMapFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryGoodsFeeTypeInfo(TableUtils.getCisGoodsTable(), param.getChainId());
        });
        CompletableFuture<Map<Long, V2GoodsProfitCategoryType>> goodsProfitCategoryTypeMapFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryGoodsProfitCategoryTypeByChainIdAndId(param.getChainId(), profitCategoryTypeIdSet);
        });
        CompletableFuture<Map<Long, V2GoodsStock>> goodsStockMapFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryGoodsStocksByIds(stockIdSet);
        });
        CompletableFuture<Map<String, List<V2GoodsHistoryVersion>>> goodsHistoryVersionFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryGoodsHisToryVersionByChainIdAndGoodsIds(param.getChainId(), finalGoodsIds);
        });
        CompletableFuture<Map<String, V2Patientorder>> patientOrderFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryPatientorderByIds(param.getChainId(), patientOrderIdSet);
        });
        CompletableFuture.allOf(goodsInfoFuture, organMapFuture, employeeFuture, supplyFuture, customTypeFuture,
                patientFuture, pharmacyFuture, ecOrderFuture, goodsProfitCategoryTypeMapFuture, v2GoodsFeeTypeMapFuture,
                goodsStockMapFuture, goodsStockMapFuture, goodsHistoryVersionFuture, patientOrderFuture).join();
        logger.error("进销存明细查询维度信息耗时:{}毫秒", System.currentTimeMillis() - stratTime);
        Map<String, V2GoodsClassify> goodsInfo = goodsInfoFuture.get();
        Map<String, List<V2GoodsHistoryVersion>> goodsVersionMap = goodsHistoryVersionFuture.get();
        Map<String, V2Patientorder> patientOrderMap = patientOrderFuture.get();
        selectGoodsSetGoodsSpuSpec(param, goodsSpuSpecIdsSet, goodsInfo);
        List<InventoryRecord> traceableCodeList = new ArrayList<>();
        if (!CollUtil.isEmpty(idSet) && idSet.size() > CommonConstants.NUMBER_TWO_THOUSAND) {
            int toIndex = CommonConstants.NUMBER_TWO_THOUSAND;
            for (int i = 0; i < idSet.size(); i += CommonConstants.NUMBER_TWO_THOUSAND) {
                if (i + CommonConstants.NUMBER_TWO_THOUSAND > idSet.size()) {
                    toIndex = idSet.size() - i;
                }
                List<Long> newIdList = CollUtil.sub(idSet, i, toIndex + i);
                traceableCodeList.addAll(hologresGoodsInventoryMapper.selectRecordTraceableCode(TableUtils.getCisTable(), param, newIdList));
            }
        } else {
            traceableCodeList = hologresGoodsInventoryMapper.selectRecordTraceableCode(TableUtils.getCisTable(), param, CollUtil.newArrayList(idSet));
        }
        Map<Long, List<InventoryRecord>> traceableCodeMap = traceableCodeList.stream().collect(Collectors.groupingBy(InventoryRecord::getId));
        for (InventoryRecord r : records) {
            r.changeNumberByPieceNumber();
            r.setFeeType1Text(dimensionQuery.queryProductClassifyLevel1(param.getChainId(), r.getFeeType1(), param.getHisType()));
            r.setSupplierAndOrganAndEmployee(organMapFuture.get(), employeeFuture.get(),
                    supplyFuture.get(), customTypeFuture.get(), patientFuture.get(), param,
                    ecOrderFuture.get());
            r.setActionAndCostAndPrice(param);
            r.setGoodsMessage(goodsInfo.get(r.getGoodsId()), param.getHeaderType(), v2GoodsFeeTypeMapFuture.get(), goodsProfitCategoryTypeMapFuture.get());
            r.setPharmacyNoInfo(pharmacyFuture.get());
            r.setShowAccuracy(param.getIsExport());
            r.setTraceableCode(traceableCodeMap, goodsVersionMap);
            r.setPatientOrderMessage(patientOrderMap);
            r.setActionByHisType(param.getHisType(), param.getChainId(), goodsInventoryPurchaseActionClinic);
            r.setGoodsStockMessage(goodsStockMapFuture.get());
        }
        return records;
    }

    /**
     * 查询goods扩展信息
     *
     * @param param              进销存param
     * @param goodsSpuSpecIdsSet goods扩展信息ids
     * @param goodsInfo          goods信息
     */
    private void selectGoodsSetGoodsSpuSpec(GoodsInventoryParam param, Set<Long> goodsSpuSpecIdsSet, Map<String, V2GoodsClassify> goodsInfo) {
        for (Map.Entry<String, V2GoodsClassify> entry : goodsInfo.entrySet()) {
            goodsSpuSpecIdsSet.add(entry.getValue().getExtendSpecId());
        }
        Map<Long, V2GoodsClassify> v2GoodsSpuSpecMap = dimensionQuery.selectV2GoodsSpuSpec(TableUtils.getCisGoodsTable(),
                param.getChainId(), goodsSpuSpecIdsSet);
        if (CisJWTUtils.isSupportBusiness(param.getSupportedBusiness(), CisJWTUtils.CisBusiness.CIS_BUSINESS_EYE)) {
            for (Map.Entry<String, V2GoodsClassify> entry : goodsInfo.entrySet()) {
                if (!BeanUtil.isEmpty(v2GoodsSpuSpecMap.get(entry.getValue().getExtendSpecId()))) {
                    entry.getValue().setSpuSpecMessage(v2GoodsSpuSpecMap.get(entry.getValue().getExtendSpecId()));
                }
            }
        }
    }

    /**
     * feeTypeIf转换
     *
     * @param param -
     * @return List
     */
    private ArrayList<Long> getFeeTypeIdsList(GoodsInventoryParam param) {

        ArrayList<Long> feeTypeIdsList = new ArrayList<>();
        if (param.getFeeTypeIds() != null && param.getFeeTypeIds().length() > 0) {
            String[] split = param.getFeeTypeIds().split(",");
            for (String s : split) {
                feeTypeIdsList.add(Long.parseLong(s));
            }
        }
        return feeTypeIdsList;
    }

    /**
     * @param param    param
     * @param goodsIds goodsIds
     * @return InventoryRecord
     */
    public InventoryRecord recordSummary(GoodsInventoryParam param, List<String> goodsIds) {
        long statTime = System.currentTimeMillis();
        List<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        InventoryRecord r = hologresGoodsInventoryMapper
                .selectRecordSummary(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList,
                        GoodsInventoryActionEnum.toActions(param.getActions(), param.getHisType()));
        //如果只查询单个药则 汇总变更数量显示大单位，
        //2022/05/05 改为当选择单个药品展示单位
        if (goodsIds != null && goodsIds.size() == 1) {
            V2Goods v2Goods = dimensionQuery.queryProduct(String.join(",", goodsIds));
            //中药没有大单位，只有小单位
            r.pretty(v2Goods != null ? v2Goods.getPackage_unit() : null, v2Goods != null
                    ? v2Goods.getPiece_unit() : null);
            //当选择一个药品，只汇总库存变更
            r.setBeforeCountText("-");
            r.setBeforeCountDecimalText("-");
            //单个药品的进销存统计需要总量数据，前端从ActionCountText字段获取数据，该数据需保留(如果值为0则只展示-)
            r.setAfterCountText("-");
            r.setAfterCountDecimalText("-");
            r.setGoodsUnitBefore("-");
            r.setGoodsUnitAfter("-");
            if (!StrUtil.isBlank(r.getActionCountText()) && r.getActionCountText().equals("0")) {
                r.setActionCountDecimalText("-");
                r.setActionCountText("-");
            }

        } else {
            r.pretty(null, null);
            //汇总行 界面上原始库存、库存变更、结余库存 都显示-
            r.setBeforeCountText("-");
            r.setBeforeCountDecimalText("-");
            r.setActionCountText("-");
            r.setActionCountDecimalText("-");
            r.setAfterCountText("-");
            r.setAfterCountDecimalText("-");
            r.setGoodsUnitBefore("-");
            r.setGoodsUnitAction("-");
            r.setGoodsUnitAfter("-");
        }

        if (!param.getPermission().isEnableCost()) {
            r.setInPriceText("");
            r.setInPrice(null);
            r.setActionCost(null);
            r.setActionCostText("");
            r.setActionCostDecimalText("");
        }
        r.setShowAccuracy(param.getIsExport());
        r.setPatientName("-");
        logger.error("进销存统计-明细查询合计行耗时:{}毫秒", System.currentTimeMillis() - statTime);
        return r;
    }

    /**
     * @param s s
     * @return boolean
     */
    private boolean notNull(String s) {
        return s != null && !s.equals("");
    }

    /**
     * @param list list
     */
    public void handleDate(List<GoodsTaxDetail> list) {

        for (GoodsTaxDetail item : list) {
            item.setDateRange(item.getBeginDate().replaceAll("-", "/") + "-"
                    + item.getEndDate().replaceAll("-", "/"));

            item.apply();
        }
    }

    /**
     * @param param param
     * @return Map<Integer, String>
     */
    public Map<String, Map<Integer, String>> getPharmacy(GoodsInventoryParam param) {
        return dimensionQuery.queryPharmacyNameByNo(param.getChainId(), param.getClinicId(), param.getPharmacyType());
    }

    /**
     * @param param       请求param
     * @param number      规格修改导致库存变化数量
     * @param goodsIdList 药品idList
     * @return -
     */
    public List<InventoryGoods> stockSummaryByGoods(GoodsInventoryParam param, Integer number,
                                                    List<String> goodsIdList, ArrayList<Long> feeTypeIdsList) {
        // 查询药品信息 获取大小单位
        Map<String, V2GoodsClassify> goodsInfo = dimensionQuery.queryGoodsInfo(TableUtils.getCisGoodsTable(),
                new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                        param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                        param.getParams().getHeaderClinicId(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                        param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()),
                CollUtil.newArrayList(param.getGoodsId()), feeTypeIdsList);
        // 查询数据
        List<InventoryGoods> inventoryGoodsList = null;
        if (number > 0) {
            TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
            // 查询小单位转换成大单位的数据

            CompletableFuture<List<InventoryGoods>> goodsListFuture
                    = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectGoods(TableUtils.getCisTable(),
                                new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                        param.getFeeType1(), param.getFeeType2(), null, null,
                                        param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                                        param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                                        param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                                        Integer.valueOf(param.getHisType())), goodsIdList, feeTypeIdsList);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture<List<InventoryGoods>> goodsBeginListFuture
                    = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectBeginGoods(TableUtils.getCisTable(),
                                new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                        param.getFeeType1(), param.getFeeType2(), null, null,
                                        param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                                        param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), goodsIdList, feeTypeIdsList, tempTable);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture<List<InventoryGoods>> goodsEndListFuture
                    = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectEndGoods(TableUtils.getCisTable(),
                                new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                                        param.getFeeType1(), param.getFeeType2(), null, null,
                                        param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                                        param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), goodsIdList, feeTypeIdsList, tempTable);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture<List<InventoryGoods>> taxRatModifyListFuture = getInventoryTaxRatData(param, goodsIdList);
            CompletableFuture.allOf(goodsListFuture, goodsBeginListFuture, goodsEndListFuture, taxRatModifyListFuture).join();
            try {
                inventoryGoodsList = goodsListFuture.get();
                List<InventoryGoods> inventoryBeginGoodsList = goodsBeginListFuture.get();
                List<InventoryGoods> inventoryEndGoodsList = goodsEndListFuture.get();
                List<InventoryGoods> taxRatModifyList = taxRatModifyListFuture.get();
                inventoryGoodsList = processingBeginAndEndData(inventoryGoodsList, inventoryBeginGoodsList,
                        inventoryEndGoodsList, taxRatModifyList, goodsIdList);
                for (InventoryGoods inventoryGoods : inventoryGoodsList) {
                    inventoryGoods.pretty(false);
                }
            } catch (Exception e) {
                logger.error("库存-进销存统计查询组装数据异常：{}", e.getMessage());
            }

        } else {
            TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
            // 查询大单位加小单位的数据
            CompletableFuture<List<InventoryGoods>> goodsListFuture
                    = abcyunExecutorPool.supplyAsync(() -> {
                return hologresGoodsInventoryMapper
                        .selectPackageAndPieceStockSummary(TableUtils.getCisTable(), param, goodsIdList, feeTypeIdsList,
                                tempTable);
            }).exceptionally(e -> {
                throw new CompletionException(e);
            });
            CompletableFuture<List<InventoryGoods>> taxRatModifyListFuture = getInventoryTaxRatData(param, goodsIdList);
            CompletableFuture.allOf(goodsListFuture, taxRatModifyListFuture).join();
            try {
                inventoryGoodsList = goodsListFuture.get();
                List<InventoryGoods> taxRatModifyList = taxRatModifyListFuture.get();
                Map<String, List<InventoryGoods>> taxRatModifyMap =
                        taxRatModifyList.stream().collect(Collectors.groupingBy(InventoryGoods::getGoodsId));
                // 大小单位数据组装成合计数据
                for (InventoryGoods inventoryGoods : inventoryGoodsList) {
                    V2GoodsClassify goodsClassify = goodsInfo.get(param.getGoodsId());
                    if (!CollUtil.isEmpty(taxRatModifyMap.get(param.getGoodsId()))) {
                        InventoryGoods taxGoods = taxRatModifyMap.get(param.getGoodsId()).get(0);
                        if (taxGoods != null) {
                            inventoryGoods.setTaxModify(taxRatModifyMap.get(param.getGoodsId()));
                        }
                    }
                    inventoryGoods.setUnit(goodsClassify.getPackageUnit(), goodsClassify.getPieceUnit());
                }
            } catch (Exception e) {
                logger.error("库存-进销存统计无规格修改查询组装数据异常：{}", e.getMessage());
            }

        }
        if (CollUtil.isEmpty(inventoryGoodsList)) {
            InventoryGoods inventoryGoods = new InventoryGoods();
            V2GoodsClassify goodsClassify = goodsInfo.get(param.getGoodsId());
            if (!BeanUtil.isEmpty(goodsClassify)) {
                inventoryGoods.setUnit(goodsClassify.getPackageUnit(), goodsClassify.getPieceUnit());
            } else {
                inventoryGoods.setUnit(null, null);
            }
            inventoryGoodsList.add(inventoryGoods);
        }
        for (InventoryGoods inventoryGoods : inventoryGoodsList) {
            inventoryGoods.pretty(false);
            V2GoodsClassify goodsClassify = goodsInfo.get(param.getGoodsId());
            if (!BeanUtil.isEmpty(goodsClassify)) {
                if (goodsClassify.getType() != null && goodsClassify.getSubType() != null
                        && !(goodsClassify.getType() == CommonConstants.NUMBER_ONE
                        && goodsClassify.getSubType() == CommonConstants.NUMBER_TWO)) {
                    inventoryGoods.changeKeyDataByPieceNumber(goodsClassify.getPieceNum());
                }
                inventoryGoods.setUnit(goodsClassify.getPackageUnit(), goodsClassify.getPieceUnit());
            } else {
                inventoryGoods.setUnit(null, null);
            }
        }
        return inventoryGoodsList;
    }

    /**
     * 查询是否包含药品规格修改导致库存发生变化
     *
     * @param param       -
     * @param goodsIdList -
     * @return -
     */
    public Integer selectSpecificationModificationCount(GoodsInventoryParam param, List<String> goodsIdList) {
        return hologresGoodsInventoryMapper
                .selectSpecificationModificationCount(TableUtils.getCisTable(), param, goodsIdList);
    }

    /**
     * 查询修改税率导致的不含税成本变化
     *
     * @param param       -
     * @param goodsIdList -
     * @return -
     */
    public Integer queryTaxModifyCount(GoodsInventoryParam param, List<String> goodsIdList) {
        return hologresGoodsInventoryMapper
                .selectTaxModifyCount(TableUtils.getCisTable(), param, goodsIdList);
    }

    /**
     * @param inventoryGoodsList -
     * @param number             number
     * @return -
     */
    public List<StatResponseKeyDataItem> getStockSummaryKeyData(
            List<InventoryGoods> inventoryGoodsList,
            Integer number) {
        DecimalFormat format = new DecimalFormat("0.00");
        List<StatResponseKeyDataItem> keyDataList = new ArrayList<>();
        InventoryGoods inventoryGoods = inventoryGoodsList.get(0);
        keyDataList.add(new StatResponseKeyDataItem("期初库存",
                number == 0
                        ? inventoryGoods.getBeginCountText()
                        : format.format(inventoryGoods.getBeginCount())));
        keyDataList.add(new StatResponseKeyDataItem("入库数量",
                number == 0
                        ? inventoryGoods.getInTotalCountText()
                        : format.format(inventoryGoods.getInTotalCount())));
        keyDataList.add(new StatResponseKeyDataItem("出库数量",
                number == 0
                        ? inventoryGoods.getOutTotalCountText()
                        : format.format(inventoryGoods.getOutTotalCount())));
        if (!new DecimalFormat("0.00").format(inventoryGoods.getInSpecificationModificationCount())
                .equals("0.00")) {
            keyDataList.add(new StatResponseKeyDataItem("修正数量",
                    format.format(inventoryGoods.getInSpecificationModificationCount())));
        }
        keyDataList.add(new StatResponseKeyDataItem("期末数量",
                number == 0
                        ? inventoryGoods.getEndCountText()
                        : format.format(inventoryGoods.getEndCount())));
        return keyDataList;
    }

    /**
     * 查询进销存药品规格修改记录
     *
     * @param param       -
     * @param goodsIdList -
     * @return -
     */
    public List<StockSummaryStockChange> getSpecificationModification(
            GoodsInventoryParam param,
            List<String> goodsIdList
    ) {
        List<StockSummaryStockChange> list = new ArrayList<>();
        logger.info("clinicId: {}", param.getClinicId());
        // 查询药品规格修改进销存记录
        List<InventoryRecord> recordList = hologresGoodsInventoryMapper
                .selectSpecificationModification(TableUtils.getCisTable(),
                        param, goodsIdList);
        Set<String> employeeSet = recordList.stream().map(InventoryRecord::getCreatedUserId)
                .collect(Collectors.toSet());
        Set<String> organSet = recordList.stream().map(InventoryRecord::getOrganId)
                .collect(Collectors.toSet());
        Map<String, Employee> employeeMap = dimensionQuery.queryEmployeeByChainAndIds(param.getChainId(), employeeSet);
        Map<String, String> organMap = dimensionQuery.queryOrganByIds(organSet);
        for (InventoryRecord inventoryRecord : recordList) {
            // 数据组装
            StockSummaryStockChange change = new StockSummaryStockChange();
            change.setChangeDate(inventoryRecord.getCreateDate());
            inventoryRecord.pretty(inventoryRecord.getBeforePackageUnit(), inventoryRecord.getBeforePieceUnit());
            change.setChangeBeforeStock(inventoryRecord.getBeforeCountText());
            inventoryRecord.pretty(inventoryRecord.getPackageUnit(), inventoryRecord.getPieceUnit());
            change.setChangeAfterStock(inventoryRecord.getAfterCountText());
            change.setChangeBeforeSpec(inventoryRecord.getBeforeDispSpec());
            change.setChangeAfterSpec(inventoryRecord.getDispSpec());
            change.setOperator(employeeMap.get(inventoryRecord.getCreatedUserId()).getName());
            change.setClinicName(organMap.get(inventoryRecord.getOrganId()));
            list.add(change);
        }
        list = list.stream().sorted(Comparator.comparing(StockSummaryStockChange::getChangeDate).reversed())
                .collect(Collectors.toList());
        return list;
    }

    /**
     * @param inventoryGoodsList 进销存记录list
     * @param number             药品规格修改记录数
     * @return 返回list
     */
    public List<StockGoodsInventorySummary> getStockSummaryData(List<InventoryGoods> inventoryGoodsList,
                                                                Integer number, Integer taxModifyNumber, GoodsInventoryParam param) {
        boolean isOpenPharmacyFlagNumber;
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            isOpenPharmacyFlagNumber = dimensionQuery.queryOpenPharmacyFlagNumberByOrgan(TableUtils.getCisGoodsTable(), param.getChainId(), param.getClinicId()) == 20;
        } else {
            isOpenPharmacyFlagNumber = dimensionQuery.queryIsOpenPharmacyByOrgan(TableUtils.getCisGoodsTable(), param.getChainId());
        }
        DecimalFormat format = new DecimalFormat("0.00");
        DecimalFormat fourFormat = new DecimalFormat("0.0000");
        DecimalFormat format1 = new DecimalFormat("0.00");
        List<StockGoodsInventorySummary> list = new ArrayList<>();
        InventoryGoods inventoryGoods = inventoryGoodsList.get(0);
        if (number > 0 || taxModifyNumber > 0) {
            list.add(new StockGoodsInventorySummary("期初", "",
                    format.format(inventoryGoods.getBeginCount()),
                    format.format(inventoryGoods.getBeginConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getBeginCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getBeginCost(), format, fourFormat),
                    "-",
                    "-",
                    !param.getPermission().isEnableCost()
            ));
            inContainsSpecUpdate(list, inventoryGoods, format, fourFormat, param, isOpenPharmacyFlagNumber);
            outContainsSpecUpdate(list, inventoryGoods, format, fourFormat, param, isOpenPharmacyFlagNumber);
            if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                if (!format1.format(inventoryGoods.getInSpecificationModificationCount()).equals("0.00")) {
                    list.add(new StockGoodsInventorySummary("修正", "规格修改",
                            format.format(inventoryGoods.getInSpecificationModificationCount()),
                            format.format(inventoryGoods.getInSpecificationModificationConvertPieceCount()),
                            getTwoOrFourDecimalStr(inventoryGoods.getInSpecificationModificationCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getInSpecificationModificationCost(), format, fourFormat),
                            format.format(inventoryGoods.getInSpecificationModificationPriceExcludeTax()),
                            format.format(inventoryGoods.getInSpecificationModificationPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                }
                if (taxModifyNumber > 0) {
                    list.add(new StockGoodsInventorySummary("修正", "税率修改",
                            "-",
                            "-",
                            getTwoOrFourDecimalStr(inventoryGoods.getAfterTotalCostModifyExcludeTax(), format, fourFormat),
                            "-",
                            "-",
                            "-",
                            !param.getPermission().isEnableCost()
                    ));
                }
            }
            list.add(new StockGoodsInventorySummary("期末", "",
                    format.format(inventoryGoods.getEndCount()),
                    format.format(inventoryGoods.getEndConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getEndCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getEndCost(), format, fourFormat),
                    "-",
                    "-",
                    !param.getPermission().isEnableCost()
            ));
        } else {
            list.add(new StockGoodsInventorySummary("期初", "",
                    !StrUtil.isBlank(inventoryGoods.getBeginCountText())
                            ? inventoryGoods.getBeginCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getBeginConvertPieceCountText())
                            ? inventoryGoods.getBeginConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getBeginCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getBeginCost(), format, fourFormat),
                    "-",
                    "-",
                    !param.getPermission().isEnableCost()
            ));
            inNotContainsSpecUpdate(list, inventoryGoods, format, fourFormat, param, isOpenPharmacyFlagNumber);
            outNotContainsSpecUpdate(list, inventoryGoods, format, fourFormat, param, isOpenPharmacyFlagNumber);


            list.add(new StockGoodsInventorySummary("期末", "",
                    !StrUtil.isBlank(inventoryGoods.getEndCountText())
                            ? inventoryGoods.getEndCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getEndConvertPieceCountText())
                            ? inventoryGoods.getEndConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getEndCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getEndCost(), format, fourFormat),
                    "-",
                    "-",
                    !param.getPermission().isEnableCost()
            ));
        }
        return list;
    }

    /**
     * @param list                     返回实体list
     * @param inventoryGoods           进销存记录实体
     * @param format                   bigDecimal格式化
     * @param isOpenPharmacyFlagNumber 是否开启多库房
     */
    private void outNotContainsSpecUpdate(List<StockGoodsInventorySummary> list, InventoryGoods inventoryGoods,
                                          DecimalFormat format, DecimalFormat fourFormat, GoodsInventoryParam param,
                                          boolean isOpenPharmacyFlagNumber) {
        if (param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY) && param.getPharmacyType() == 0
                && !format.format(inventoryGoods.getInInitReturnCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            list.add(new StockGoodsInventorySummary("出库", "初始化退货",
                    !StrUtil.isBlank(inventoryGoods.getInInitReturnCountText())
                            ? inventoryGoods.getInInitReturnCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getInInitReturnConvertPieceCountText())
                            ? inventoryGoods.getInInitReturnConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitReturnCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitReturnCost(), format, fourFormat),
                    format.format(inventoryGoods.getInInitReturnPriceExcludeTax()),
                    format.format(inventoryGoods.getInInitReturnPrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "采购退货",
                    !StrUtil.isBlank(inventoryGoods.getReturnGoodsOutCountText())
                            ? inventoryGoods.getReturnGoodsOutCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getReturnGoodsOutConvertPieceCountText())
                            ? inventoryGoods.getReturnGoodsOutConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getReturnGoodsOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getReturnGoodsOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getReturnGoodsOutPriceExcludeTax()),
                    format.format(inventoryGoods.getReturnGoodsOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
        }
        if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            list.add(new StockGoodsInventorySummary("出库", "发药出库",
                    !StrUtil.isBlank(inventoryGoods.getDispenseCountText())
                            ? inventoryGoods.getDispenseCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getDispenseConvertPieceCountText())
                            ? inventoryGoods.getDispenseConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));
        } else {
            list.add(new StockGoodsInventorySummary("出库", "门诊发药",
                    !StrUtil.isBlank(inventoryGoods.getOutPatientDispenseCountText())
                            ? inventoryGoods.getOutPatientDispenseCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getOutPatientDispenseConvertPieceCountText())
                            ? inventoryGoods.getOutPatientDispenseConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getOutPatientDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getOutPatientDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getOutPatientDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getOutPatientDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "住院药房发药(已结算)",
                    !StrUtil.isBlank(inventoryGoods.getHospitalPharmacyDispenseCountText())
                            ? inventoryGoods.getHospitalPharmacyDispenseCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getHospitalPharmacyDispenseConvertPieceCountText())
                            ? inventoryGoods.getHospitalPharmacyDispenseConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalPharmacyDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalPharmacyDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getHospitalPharmacyDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getHospitalPharmacyDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "住院自动发药(已结算)",
                    !StrUtil.isBlank(inventoryGoods.getHospitalAutomaticDispenseCountText())
                            ? inventoryGoods.getHospitalAutomaticDispenseCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getHospitalAutomaticDispenseConvertPieceCountText())
                            ? inventoryGoods.getHospitalAutomaticDispenseConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalAutomaticDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalAutomaticDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getHospitalAutomaticDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getHospitalAutomaticDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "住院发药(未结算)",
                    !StrUtil.isBlank(inventoryGoods.getHospitalNoSettleDispenseCountText())
                            ? inventoryGoods.getHospitalNoSettleDispenseCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getHospitalNoSettleDispenseConvertPieceCountText())
                            ? inventoryGoods.getHospitalNoSettleDispenseConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalNoSettleDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalNoSettleDispenseCost(), format, fourFormat),
                    "-",
                    "-",
                    !param.getPermission().isEnableCost()
            ));
        }
        if (param.getPharmacyType() == 0) {
            if (param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                list.add(new StockGoodsInventorySummary("出库", "科室消耗",
                        !StrUtil.isBlank(inventoryGoods.getOutDepartmentConsumptionCountText())
                                ? inventoryGoods.getOutDepartmentConsumptionCountText() : "0.00",
                        !StrUtil.isBlank(inventoryGoods.getOutDepartmentConsumptionConvertPieceCountText())
                                ? inventoryGoods.getOutDepartmentConsumptionConvertPieceCountText() : "0.00",
                        getTwoOrFourDecimalStr(inventoryGoods.getOutDepartmentConsumptionCostAmountExcludingTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getOutDepartmentConsumptionCostAmount(), format, fourFormat),
                        format.format(inventoryGoods.getOutDepartmentConsumptionAmountExcludingTax()),
                        format.format(inventoryGoods.getOutDepartmentConsumptionAmount()),
                        !param.getPermission().isEnableCost()
                ));
            }
            list.add(new StockGoodsInventorySummary("出库", "领用出库",
                    !StrUtil.isBlank(inventoryGoods.getCollectOutCountText())
                            ? inventoryGoods.getCollectOutCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getCollectOutConvertPieceCountText())
                            ? inventoryGoods.getCollectOutConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getCollectOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getCollectOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getCollectOutPriceExcludeTax()),
                    format.format(inventoryGoods.getCollectOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
            if (isOpenPharmacyFlagNumber || !param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                    list.add(new StockGoodsInventorySummary("出库", "调拨出库",
                            !StrUtil.isBlank(inventoryGoods.getAllotOutCountText())
                                    ? inventoryGoods.getAllotOutCountText() : "0.00",
                            !StrUtil.isBlank(inventoryGoods.getAllotOutConvertPieceCountText())
                                    ? inventoryGoods.getAllotOutConvertPieceCountText() : "0.00",
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCost(), format, fourFormat),
                            format.format(inventoryGoods.getAllotOutPriceExcludeTax()),
                            format.format(inventoryGoods.getAllotOutPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                } else {
                    list.add(new StockGoodsInventorySummary("出库", "调剂出库",
                            !StrUtil.isBlank(inventoryGoods.getAllotOutCountText())
                                    ? inventoryGoods.getAllotOutCountText() : "0.00",
                            !StrUtil.isBlank(inventoryGoods.getAllotOutConvertPieceCountText())
                                    ? inventoryGoods.getAllotOutConvertPieceCountText() : "0.00",
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCost(), format, fourFormat),
                            format.format(inventoryGoods.getAllotOutPriceExcludeTax()),
                            format.format(inventoryGoods.getAllotOutPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                }
            }
            list.add(new StockGoodsInventorySummary("出库", "报损出库",
                    !StrUtil.isBlank(inventoryGoods.getDamagedOutCountText())
                            ? inventoryGoods.getDamagedOutCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getDamagedOutConvertPieceCountText())
                            ? inventoryGoods.getDamagedOutConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getDamagedOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getDamagedOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getDamagedOutPriceExcludeTax()),
                    format.format(inventoryGoods.getDamagedOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
            if (param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                list.add(new StockGoodsInventorySummary("出库", "其他出库",
                        !StrUtil.isBlank(inventoryGoods.getOutOtherCountText())
                                ? inventoryGoods.getOutOtherCountText() : "0.00",
                        !StrUtil.isBlank(inventoryGoods.getOutOtherConvertPieceCountText())
                                ? inventoryGoods.getOutOtherConvertPieceCountText() : "0.00",
                        getTwoOrFourDecimalStr(inventoryGoods.getOutOtherCostAmountExcludingTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getOutOtherCostAmount(), format, fourFormat),
                        format.format(inventoryGoods.getOutOtherAmountExcludingTax()),
                        format.format(inventoryGoods.getOutOtherAmount()),
                        !param.getPermission().isEnableCost()
                ));
            }
            list.add(new StockGoodsInventorySummary("出库", "盘亏出库",
                    !StrUtil.isBlank(inventoryGoods.getCheckOutCountText())
                            ? inventoryGoods.getCheckOutCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getCheckOutConvertPieceCountText())
                            ? inventoryGoods.getCheckOutConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getCheckOutPriceExcludeTax()),
                    format.format(inventoryGoods.getCheckOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
            if (param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_NORMAL)) {
                list.add(new StockGoodsInventorySummary("出库", "生产出库",
                        !StrUtil.isBlank(inventoryGoods.getProductionOutCountText())
                                ? inventoryGoods.getProductionOutCountText() : "0.00",
                        !StrUtil.isBlank(inventoryGoods.getProductionOutConvertPieceCountText())
                                ? inventoryGoods.getProductionOutConvertPieceCountText() : "0.00",
                        getTwoOrFourDecimalStr(inventoryGoods.getProductionOutCostExcludeTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getProductionOutCost(), format, fourFormat),
                        format.format(inventoryGoods.getProductionOutPriceExcludeTax()),
                        format.format(inventoryGoods.getProductionOutPrice()),
                        !param.getPermission().isEnableCost()
                ));
            }
            if (param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber()) && param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                list.add(new StockGoodsInventorySummary("出库", "配货出库",
                        !StrUtil.isBlank(inventoryGoods.getDeliveryOutCountText())
                                ? inventoryGoods.getDeliveryOutCountText() : "0.00",
                        !StrUtil.isBlank(inventoryGoods.getDeliveryOutConvertPieceCountText())
                                ? inventoryGoods.getDeliveryOutConvertPieceCountText() : "0.00",
                        getTwoOrFourDecimalStr(inventoryGoods.getDeliveryOutCostExcludeTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getDeliveryOutCost(), format, fourFormat),
                        format.format(inventoryGoods.getDeliveryOutPriceExcludeTax()),
                        format.format(inventoryGoods.getDeliveryOutPrice()),
                        !param.getPermission().isEnableCost()
                ));
            }
        }
    }

    /**
     * @param list                     返回实体list
     * @param inventoryGoods           进销存记录实体
     * @param format                   bigDecimal格式化
     * @param isOpenPharmacyFlagNumber 是否开启多库房
     */
    private void inNotContainsSpecUpdate(List<StockGoodsInventorySummary> list, InventoryGoods inventoryGoods,
                                         DecimalFormat format, DecimalFormat fourFormat, GoodsInventoryParam param,
                                         boolean isOpenPharmacyFlagNumber) {
        if (param.getPharmacyType() == 0
                && !format.format(inventoryGoods.getInInitCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            list.add(new StockGoodsInventorySummary("入库", "初始化入库",
                    !StrUtil.isBlank(inventoryGoods.getInInitCountText())
                            ? inventoryGoods.getInInitCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getInInitConvertPieceCountText())
                            ? inventoryGoods.getInInitConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitCostExcludeTax(), format, format),
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitCost(), format, format),
                    format.format(inventoryGoods.getInInitPriceExcludeTax()),
                    format.format(inventoryGoods.getInInitPrice()),
                    !param.getPermission().isEnableCost()
            ));
        }
        list.add(new StockGoodsInventorySummary("入库", "采购入库",
                !StrUtil.isBlank(inventoryGoods.getPurchaseInCountText())
                        ? inventoryGoods.getPurchaseInCountText() : "0.00",
                !StrUtil.isBlank(inventoryGoods.getPurchaseInConvertPieceCountText())
                        ? inventoryGoods.getPurchaseInConvertPieceCountText() : "0.00",
                getTwoOrFourDecimalStr(inventoryGoods.getPurchaseInCostExcludeTax(), format, format),
                getTwoOrFourDecimalStr(inventoryGoods.getPurchaseInCost(), format, format),
                format.format(inventoryGoods.getPurchaseInPriceExcludeTax()),
                format.format(inventoryGoods.getPurchaseInPrice()),
                !param.getPermission().isEnableCost()
        ));
        if (param.getPharmacyType() == 0) {
            if (isOpenPharmacyFlagNumber) {
                list.add(new StockGoodsInventorySummary("入库", "领用入库",
                        !StrUtil.isBlank(inventoryGoods.getInReceiveCountText())
                                ? inventoryGoods.getInReceiveCountText() : "0.00",
                        !StrUtil.isBlank(inventoryGoods.getInReceiveConvertPieceCountText())
                                ? inventoryGoods.getInReceiveConvertPieceCountText() : "0.00",
                        getTwoOrFourDecimalStr(inventoryGoods.getInReceiveCostAmountExcludingTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getInReceiveCostAmount(), format, fourFormat),
                        format.format(inventoryGoods.getInReceiveAmountExcludingTax()),
                        format.format(inventoryGoods.getInReceiveAmount()),
                        !param.getPermission().isEnableCost()
                ));
            }
            if (isOpenPharmacyFlagNumber || !param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                    list.add(new StockGoodsInventorySummary("入库", "调拨入库",
                            !StrUtil.isBlank(inventoryGoods.getAllotInCountText())
                                    ? inventoryGoods.getAllotInCountText() : "0.00",
                            !StrUtil.isBlank(inventoryGoods.getAllotInConvertPieceCountText())
                                    ? inventoryGoods.getAllotInConvertPieceCountText() : "0.00",
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotInCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotInCost(), format, fourFormat),
                            format.format(inventoryGoods.getAllotInPriceExcludeTax()),
                            format.format(inventoryGoods.getAllotInPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                } else {
                    list.add(new StockGoodsInventorySummary("入库", "调剂入库",
                            !StrUtil.isBlank(inventoryGoods.getAllotInCountText())
                                    ? inventoryGoods.getAllotInCountText() : "0.00",
                            !StrUtil.isBlank(inventoryGoods.getAllotInConvertPieceCountText())
                                    ? inventoryGoods.getAllotInConvertPieceCountText() : "0.00",
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotInCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotInCost(), format, fourFormat),
                            format.format(inventoryGoods.getAllotInPriceExcludeTax()),
                            format.format(inventoryGoods.getAllotInPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                }
            }
            list.add(new StockGoodsInventorySummary("入库", "盘盈入库",
                    !StrUtil.isBlank(inventoryGoods.getCheckInCountText())
                            ? inventoryGoods.getCheckInCountText() : "0.00",
                    !StrUtil.isBlank(inventoryGoods.getCheckInConvertPieceCountText())
                            ? inventoryGoods.getCheckInConvertPieceCountText() : "0.00",
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckInCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckInCost(), format, fourFormat),
                    format.format(inventoryGoods.getCheckInPriceExcludeTax()),
                    format.format(inventoryGoods.getCheckInPrice()),
                    !param.getPermission().isEnableCost()
            ));
        }
    }

    /**
     * @param list                     返回实体list
     * @param inventoryGoods           进销存记录实体
     * @param format                   bigDecimal格式化2位小数
     * @param fourFormat               bigDecimal格式化4位小数
     * @param isOpenPharmacyFlagNumber 是否开启多库房
     */
    private void outContainsSpecUpdate(List<StockGoodsInventorySummary> list, InventoryGoods inventoryGoods,
                                       DecimalFormat format, DecimalFormat fourFormat, GoodsInventoryParam param,
                                       boolean isOpenPharmacyFlagNumber) {
        if (param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY) && param.getPharmacyType() == 0
                && !format.format(inventoryGoods.getInInitReturnCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            list.add(new StockGoodsInventorySummary("出库", "初始化退货",
                    format.format(inventoryGoods.getInInitReturnCount()),
                    format.format(inventoryGoods.getInInitReturnConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitReturnCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitReturnCost(), format, fourFormat),
                    format.format(inventoryGoods.getInInitReturnPriceExcludeTax()),
                    format.format(inventoryGoods.getInInitReturnPrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "采购退货",
                    format.format(inventoryGoods.getReturnGoodsOutCount()),
                    format.format(inventoryGoods.getReturnGoodsOutConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getReturnGoodsOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getReturnGoodsOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getReturnGoodsOutPriceExcludeTax()),
                    format.format(inventoryGoods.getReturnGoodsOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
        }
        if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
            list.add(new StockGoodsInventorySummary("出库", "发药出库",
                    format.format(inventoryGoods.getDispenseCount()),
                    format.format(inventoryGoods.getDispenseConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));

        } else {
            list.add(new StockGoodsInventorySummary("出库", "门诊发药",
                    format.format(inventoryGoods.getOutPatientDispenseCount()),
                    format.format(inventoryGoods.getOutPatientDispenseConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getOutPatientDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getOutPatientDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getOutPatientDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getOutPatientDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "住院药房发药(已结算)",
                    format.format(inventoryGoods.getHospitalPharmacyDispenseCount()),
                    format.format(inventoryGoods.getHospitalPharmacyDispenseConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalPharmacyDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalPharmacyDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getHospitalPharmacyDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getHospitalPharmacyDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "住院自动发药(已结算)",
                    format.format(inventoryGoods.getHospitalAutomaticDispenseCount()),
                    format.format(inventoryGoods.getHospitalAutomaticDispenseConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalAutomaticDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalAutomaticDispenseCost(), format, fourFormat),
                    format.format(inventoryGoods.getHospitalAutomaticDispensePriceExcludeTax()),
                    format.format(inventoryGoods.getHospitalAutomaticDispensePrice()),
                    !param.getPermission().isEnableCost()
            ));
            list.add(new StockGoodsInventorySummary("出库", "住院发药(未结算)",
                    format.format(inventoryGoods.getHospitalNoSettleDispenseCount()),
                    format.format(inventoryGoods.getHospitalNoSettleDispenseConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalNoSettleDispenseCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getHospitalNoSettleDispenseCost(), format, fourFormat),
                    "-",
                    "-",
                    !param.getPermission().isEnableCost()
            ));
        }
        if (param.getPharmacyType() == 0) {
            if (param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                list.add(new StockGoodsInventorySummary("出库", "科室消耗",
                        format.format(inventoryGoods.getOutDepartmentConsumptionCount()),
                        format.format(inventoryGoods.getOutDepartmentConsumptionConvertPieceCount()),
                        getTwoOrFourDecimalStr(inventoryGoods.getOutDepartmentConsumptionCostAmountExcludingTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getOutDepartmentConsumptionCostAmount(), format, fourFormat),
                        format.format(inventoryGoods.getOutDepartmentConsumptionAmountExcludingTax()),
                        format.format(inventoryGoods.getOutDepartmentConsumptionAmount()),
                        !param.getPermission().isEnableCost()
                ));
            }
            list.add(new StockGoodsInventorySummary("出库", "领用出库",
                    format.format(inventoryGoods.getCollectOutCount()),
                    format.format(inventoryGoods.getCollectOutConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getCollectOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getCollectOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getCollectOutPriceExcludeTax()),
                    format.format(inventoryGoods.getCollectOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
            if (isOpenPharmacyFlagNumber || !param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                    list.add(new StockGoodsInventorySummary("出库", "调拨出库",
                            format.format(inventoryGoods.getAllotOutCount()),
                            format.format(inventoryGoods.getAllotOutConvertPieceCount()),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCost(), format, fourFormat),
                            format.format(inventoryGoods.getAllotOutPriceExcludeTax()),
                            format.format(inventoryGoods.getAllotOutPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                } else {
                    list.add(new StockGoodsInventorySummary("出库", "调剂出库",
                            format.format(inventoryGoods.getAllotOutCount()),
                            format.format(inventoryGoods.getAllotOutConvertPieceCount()),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotOutCost(), format, fourFormat),
                            format.format(inventoryGoods.getAllotOutPriceExcludeTax()),
                            format.format(inventoryGoods.getAllotOutPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                }
            }
            list.add(new StockGoodsInventorySummary("出库", "报损出库",
                    format.format(inventoryGoods.getDamagedOutCount()),
                    format.format(inventoryGoods.getDamagedOutConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getDamagedOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getDamagedOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getDamagedOutPriceExcludeTax()),
                    format.format(inventoryGoods.getDamagedOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
            if (param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_HOSPITAL)) {
                list.add(new StockGoodsInventorySummary("出库", "其他出库",
                        format.format(inventoryGoods.getOutOtherCount()),
                        format.format(inventoryGoods.getOutOtherConvertPieceCount()),
                        getTwoOrFourDecimalStr(inventoryGoods.getOutOtherCostAmountExcludingTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getOutOtherCostAmount(), format, fourFormat),
                        format.format(inventoryGoods.getOutOtherAmountExcludingTax()),
                        format.format(inventoryGoods.getOutOtherAmount()),
                        !param.getPermission().isEnableCost()
                ));
            }
            list.add(new StockGoodsInventorySummary("出库", "盘亏出库",
                    format.format(inventoryGoods.getCheckOutCount()),
                    format.format(inventoryGoods.getCheckOutConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckOutCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckOutCost(), format, fourFormat),
                    format.format(inventoryGoods.getCheckOutPriceExcludeTax()),
                    format.format(inventoryGoods.getCheckOutPrice()),
                    !param.getPermission().isEnableCost()
            ));
            if (param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_NORMAL)) {
                list.add(new StockGoodsInventorySummary("出库", "生产出库",
                        format.format(inventoryGoods.getProductionOutCount()),
                        format.format(inventoryGoods.getProductionOutConvertPieceCount()),
                        getTwoOrFourDecimalStr(inventoryGoods.getProductionOutCostExcludeTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getProductionOutCost(), format, fourFormat),
                        format.format(inventoryGoods.getProductionOutPriceExcludeTax()),
                        format.format(inventoryGoods.getProductionOutPrice()),
                        !param.getPermission().isEnableCost()
                ));
            }
            if (param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber()) && param.getHisType().equalsIgnoreCase(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                list.add(new StockGoodsInventorySummary("出库", "配货出库",
                        format.format(inventoryGoods.getDeliveryOutCount()),
                        format.format(inventoryGoods.getDeliveryOutConvertPieceCount()),
                        getTwoOrFourDecimalStr(inventoryGoods.getDeliveryOutCostExcludeTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getDeliveryOutCost(), format, fourFormat),
                        format.format(inventoryGoods.getDeliveryOutPriceExcludeTax()),
                        format.format(inventoryGoods.getDeliveryOutPrice()),
                        !param.getPermission().isEnableCost()
                ));
            }
        }
    }

    /**
     * @param list                     返回实体list
     * @param inventoryGoods           进销存记录实体
     * @param format                   bigDecimal格式化2位小数
     * @param fourFormat               bigDecimal格式化4位小数
     * @param isOpenPharmacyFlagNumber 是否开启多库房
     */
    private void inContainsSpecUpdate(List<StockGoodsInventorySummary> list, InventoryGoods inventoryGoods,
                                      DecimalFormat format, DecimalFormat fourFormat, GoodsInventoryParam param,
                                      boolean isOpenPharmacyFlagNumber) {
        if (param.getPharmacyType() == 0
                && !format.format(inventoryGoods.getInInitCount()).equals(CommonConstants.DECIMAL_ZERO_RESERVE_TWO_DIGITS_STR)) {
            list.add(new StockGoodsInventorySummary("入库", "初始化入库",
                    format.format(inventoryGoods.getInInitCount()),
                    format.format(inventoryGoods.getInInitConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitCostExcludeTax(), format, format),
                    getTwoOrFourDecimalStr(inventoryGoods.getInInitCost(), format, format),
                    format.format(inventoryGoods.getInInitPriceExcludeTax()),
                    format.format(inventoryGoods.getInInitPrice()),
                    !param.getPermission().isEnableCost()
            ));
        }
        list.add(new StockGoodsInventorySummary("入库", "采购入库",
                format.format(inventoryGoods.getPurchaseInCount()),
                format.format(inventoryGoods.getPurchaseInConvertPieceCount()),
                getTwoOrFourDecimalStr(inventoryGoods.getPurchaseInCostExcludeTax(), format, format),
                getTwoOrFourDecimalStr(inventoryGoods.getPurchaseInCost(), format, format),
                format.format(inventoryGoods.getPurchaseInPriceExcludeTax()),
                format.format(inventoryGoods.getPurchaseInPrice()),
                !param.getPermission().isEnableCost()
        ));
        if (param.getPharmacyType() == 0) {
            if (isOpenPharmacyFlagNumber) {
                list.add(new StockGoodsInventorySummary("入库", "领用入库",
                        format.format(inventoryGoods.getInReceiveCount()),
                        format.format(inventoryGoods.getInReceiveConvertPieceCount()),
                        getTwoOrFourDecimalStr(inventoryGoods.getInReceiveCostAmountExcludingTax(), format, fourFormat),
                        getTwoOrFourDecimalStr(inventoryGoods.getInReceiveCostAmount(), format, fourFormat),
                        format.format(inventoryGoods.getInReceiveAmountExcludingTax()),
                        format.format(inventoryGoods.getInReceiveAmount()),
                        !param.getPermission().isEnableCost()
                ));
            }
        }
        if (param.getPharmacyType() == 0) {
            if (isOpenPharmacyFlagNumber || !param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                if (!param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
                    if (!StrUtil.isBlank(param.getPharmacyNosSql())) {
                        list.add(new StockGoodsInventorySummary("入库", "调拨入库(店间)",
                                format.format(inventoryGoods.getAllotInCount()),
                                format.format(inventoryGoods.getAllotInConvertPieceCount()),
                                getTwoOrFourDecimalStr(inventoryGoods.getAllotInCostExcludeTax(), format, fourFormat),
                                getTwoOrFourDecimalStr(inventoryGoods.getAllotInCost(), format, fourFormat),
                                format.format(inventoryGoods.getAllotInPriceExcludeTax()),
                                format.format(inventoryGoods.getAllotInPrice()),
                                !param.getPermission().isEnableCost()
                        ));
                    } else {
                        list.add(new StockGoodsInventorySummary("入库", "调拨入库(店内)",
                                format.format(inventoryGoods.getAllotInInsideCount()),
                                format.format(inventoryGoods.getAllotInInsideConvertPieceCount()),
                                getTwoOrFourDecimalStr(inventoryGoods.getAllotInInsideCostExcludeTax(), format, fourFormat),
                                getTwoOrFourDecimalStr(inventoryGoods.getAllotInInsideCost(), format, fourFormat),
                                format.format(inventoryGoods.getAllotInInsidePriceExcludeTax()),
                                format.format(inventoryGoods.getAllotInInsidePrice()),
                                !param.getPermission().isEnableCost()
                        ));
                    }
                } else {
                    list.add(new StockGoodsInventorySummary("入库", "调剂入库",
                            format.format(inventoryGoods.getAllotInCount()),
                            format.format(inventoryGoods.getAllotInConvertPieceCount()),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotInCostExcludeTax(), format, fourFormat),
                            getTwoOrFourDecimalStr(inventoryGoods.getAllotInCost(), format, fourFormat),
                            format.format(inventoryGoods.getAllotInPriceExcludeTax()),
                            format.format(inventoryGoods.getAllotInPrice()),
                            !param.getPermission().isEnableCost()
                    ));
                }
            }
            list.add(new StockGoodsInventorySummary("入库", "盘盈入库",
                    format.format(inventoryGoods.getCheckInCount()),
                    format.format(inventoryGoods.getCheckInConvertPieceCount()),
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckInCostExcludeTax(), format, fourFormat),
                    getTwoOrFourDecimalStr(inventoryGoods.getCheckInCost(), format, fourFormat),
                    format.format(inventoryGoods.getCheckInPriceExcludeTax()),
                    format.format(inventoryGoods.getCheckInPrice()),
                    !param.getPermission().isEnableCost()
            ));
        }
    }

    /**
     * 查询goodsIdList
     *
     * @param param    进销存统计param
     * @param goodsIds 查询的药品idList
     * @return goodsIdList
     */
    public List<String> stockSummarySelectGoodsIdList(GoodsInventoryParam param, List<String> goodsIds) {
        return dimensionQuery.queryGoodsIdList(TableUtils.getCisGoodsTable(),
                new GoodsInfoParam(param.getChainId(), param.getSpuGoodsId(), param.getSpherical(),
                        param.getLenticular(), param.getMaterial(), param.getSpec(), param.getColor(),
                        param.getWearCycle()), goodsIds);
    }

    /**
     * @param param -
     * @return -
     */
    public List<StockSummaryTaxRateChange> stockSummaryTaxRateModifyInfo(GoodsInventoryParam param) {
        List<StockSummaryTaxRateChange> stockSummaryTaxRateChanges =
                hologresGoodsInventoryMapper.selectStockSummaryTaxRateModifyInfo(TableUtils.getCisTable(), param);
        if (stockSummaryTaxRateChanges.size() == 0) {
            return new ArrayList<>();
        }
        HashSet<String> operatorIds = new HashSet<>();
        for (StockSummaryTaxRateChange taxRateChange : stockSummaryTaxRateChanges) {
            if (taxRateChange.getOperator() != null) {
                operatorIds.add(taxRateChange.getOperator());
            }
        }

        Map<String, String> employeeMap = dimensionQuery.queryEmployeeNameByChainAndIds(param.getChainId(), operatorIds);
        stockSummaryTaxRateChanges.forEach(data -> {
            data.setOperatorName(employeeMap.getOrDefault(data.getOperator(), "-"));
            data.pretty();
        });
        return stockSummaryTaxRateChanges;
    }


    /**
     * 判断是否大于两位小数
     *
     * @param number 数值
     * @return 是否大于两位小数
     */
    public static String getTwoOrFourDecimalStr(BigDecimal number, DecimalFormat format, DecimalFormat fourFormat) {
        if (Math.max(0, number.stripTrailingZeros().scale()) > 2) {
            return fourFormat.format(number);
        } else {
            return format.format(number);
        }
    }

    /**
     * 查询盘存信息
     *
     * @param req req
     * @return 盘存信息
     */
    public List<ShebaoStockNewInfo> selectSheBaoTakeInventoryData(GoodsInventoryRpcReq req) {
        return hologresGoodsInventoryMapper.selectSheBaoTakeInventoryData(TableUtils.getCisTable(), req);
    }

    public List<InventoryReport> report(GoodsInventoryParam param, List<String> feeType1IdList) throws Exception {
        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
        CompletableFuture<List<InventoryReport>> beginReportDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectReportBeginData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryReport>> actionReportDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectReportActionData(TableUtils.getCisTable(), param);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryReport>> endReportDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectReportEndData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture.allOf(beginReportDataListFuture, actionReportDataListFuture, endReportDataListFuture).join();
        List<InventoryReport> beginReportDataList = beginReportDataListFuture.get();
        List<InventoryReport> actionIRList = actionReportDataListFuture.get();
        List<InventoryReport> endReportDataList = endReportDataListFuture.get();
        Set<Integer> feeType2Set = endReportDataList.stream().map(InventoryReport::getFeeType2).filter(Objects::nonNull).collect(Collectors.toSet());
        List<InventoryReport> reportList = new ArrayList<>();
        if (!CollUtil.isEmpty(feeType2Set)) {
            for (String feeType1Id : feeType1IdList) {
                List<InventoryReport> endFilterList
                        = endReportDataList.stream().filter(endIr -> endIr.getFeeType1().equals(feeType1Id)
                                && endIr.getFeeType2() != null)
                        .collect(Collectors.toList());
                Map<Integer, V2GoodsCustomType> goodsCustomTypeMap = dimensionQuery.queryCustomTypeByIds(feeType2Set);
                for (InventoryReport endReport : endFilterList) {
                    List<InventoryReport> endFilterDataList
                            = endFilterList.stream().filter(endIr -> endIr.getFeeType1().equals(feeType1Id)
                                    && endIr.getFeeType2() != null && endIr.getFeeType2().equals(endReport.getFeeType2()))
                            .collect(Collectors.toList());
                    List<InventoryReport> beginFilterDataList
                            = beginReportDataList.stream().filter(beginIr -> beginIr.getFeeType1().equals(feeType1Id)
                                    && beginIr.getFeeType2() != null && beginIr.getFeeType2().equals(endReport.getFeeType2()))
                            .collect(Collectors.toList());
                    List<InventoryReport> actionFilterDataList
                            = actionIRList.stream().filter(actionIr -> actionIr.getFeeType1().equals(feeType1Id)
                                    && actionIr.getFeeType2() != null && actionIr.getFeeType2().equals(endReport.getFeeType2()))
                            .collect(Collectors.toList());
                    new InventoryReport();
                    InventoryReport report;
                    if (!CollUtil.isEmpty(actionFilterDataList)) {
                        report = actionFilterDataList.get(0);
                    } else {
                        report = new InventoryReport();
                    }
                    report.setFeeType1Name(dimensionQuery.queryProductClassifyLevel1(param.getChainId(), feeType1Id, param.getHisType()));
                    if (goodsCustomTypeMap.get(endReport.getFeeType2()) != null) {
                        report.setFeeType2Name(goodsCustomTypeMap.get(endReport.getFeeType2()).getName());
                    } else {
                        report.setFeeType2Name("未指定");
                    }
                    report.setBeginAndEndData(beginFilterDataList, endFilterDataList);
                    report.pretty(param);
                    reportList.add(report);
                }
            }
        } else {
            for (String feeType1Id : feeType1IdList) {
                InventoryReport report = new InventoryReport();
                if (!CollUtil.isEmpty(actionIRList)) {
                    List<InventoryReport> actionFilterList
                            = actionIRList.stream().filter(beginIr -> beginIr.getFeeType1().equals(feeType1Id))
                            .collect(Collectors.toList());
                    if (!CollUtil.isEmpty(actionFilterList)) {
                        report = actionFilterList.get(0);
                    }
                }
                report.setFeeType1(feeType1Id);
                report.setFeeType1Name(dimensionQuery.queryProductClassifyLevel1(param.getChainId(), feeType1Id, param.getHisType()));
                report.setBeginAndEndData(beginReportDataList, endReportDataList);
                report.pretty(param);
                reportList.add(report);
            }
        }
        return reportList;
    }

    public InventoryReport reportSummary(GoodsInventoryParam param) throws Exception {
        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
        CompletableFuture<InventoryReport> beginReportDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectReportSummaryBeginData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<InventoryReport> actionReportDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectReportSummaryActionData(TableUtils.getCisTable(), param);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<InventoryReport> endReportDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectReportSummaryEndData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture.allOf(beginReportDataListFuture, actionReportDataListFuture, endReportDataListFuture).join();
        InventoryReport beginReportSummaryData = beginReportDataListFuture.get();
        InventoryReport actionSummaryIR = actionReportDataListFuture.get();
        InventoryReport endReportSummaryData = endReportDataListFuture.get();
        InventoryReport reportSummary = new InventoryReport();
        if (!BeanUtil.isEmpty(actionSummaryIR)) {
            reportSummary = actionSummaryIR;
        }
        reportSummary.setFeeType1Name("合计");
        reportSummary.setBeginAndEndSummaryData(beginReportSummaryData, endReportSummaryData);
        reportSummary.pretty(param);
        return reportSummary;
    }

    /**
     * 进销存基药报表数据组装
     *
     * @param param          -
     * @param feeType1IdList -
     * @return List<InventoryEssentialMedicineReport>
     */
    public List<InventoryEssentialMedicineReport> getEssentialMedicineReport(GoodsInventoryParam
                                                                                     param, List<String> feeType1IdList) {
        List<InventoryEssentialMedicineReport> actionReportList = hologresGoodsInventoryMapper.selectEssentialMedicineReport(TableUtils.getCisTable(), param);
        List<InventoryEssentialMedicineReport> reportList = new ArrayList<>();
        for (String feeType1Id : feeType1IdList) {
            InventoryEssentialMedicineReport report = new InventoryEssentialMedicineReport();
            if (!CollUtil.isEmpty(actionReportList)) {
                List<InventoryEssentialMedicineReport> actionFilterList
                        = actionReportList.stream().filter(beginIr -> beginIr.getFeeType1().equals(feeType1Id))
                        .collect(Collectors.toList());
                if (!CollUtil.isEmpty(actionFilterList)) {
                    report = actionFilterList.get(0);
                }
            }
            report.setFeeType1(feeType1Id);
            report.setFeeType1Name(dimensionQuery.queryProductClassifyLevel1(param.getChainId(), feeType1Id, param.getHisType()));
            report.pretty(param);
            reportList.add(report);
        }
        return reportList;
    }

    /**
     * 进销存基药合计报表数据组装
     *
     * @param dataList -
     * @return -
     */
    public InventoryEssentialMedicineReport getEssentialMedicineReportSummary(GoodsInventoryParam
                                                                                      param, List<InventoryEssentialMedicineReport> dataList) {
        InventoryEssentialMedicineReport reportSummary = new InventoryEssentialMedicineReport();
        if (!param.getPermission().isEnableCost()) {
            return reportSummary;
        }
        for (InventoryEssentialMedicineReport medicineReport : dataList) {
            reportSummary.setStockInEssentialMedicineCost(reportSummary.getStockInEssentialMedicineCost().add(medicineReport.getStockInEssentialMedicineCost()));
            reportSummary.setStockInNoEssentialMedicineCost(reportSummary.getStockInNoEssentialMedicineCost().add(medicineReport.getStockInNoEssentialMedicineCost()));
            reportSummary.setStockInCost(reportSummary.getStockInCost().add(medicineReport.getStockInCost()));
            reportSummary.setOutpatientDispenseEssentialMedicineCost(reportSummary.getOutpatientDispenseEssentialMedicineCost().add(medicineReport.getOutpatientDispenseEssentialMedicineCost()));
            reportSummary.setOutpatientDispenseNoEssentialMedicineCost(reportSummary.getOutpatientDispenseNoEssentialMedicineCost().add(medicineReport.getOutpatientDispenseNoEssentialMedicineCost()));
            reportSummary.setOutpatientDispenseCost(reportSummary.getOutpatientDispenseCost().add(medicineReport.getOutpatientDispenseCost()));
            reportSummary.setHospitalPharmacyDispenseEssentialMedicineCost(reportSummary.getHospitalPharmacyDispenseEssentialMedicineCost().add(medicineReport.getHospitalPharmacyDispenseEssentialMedicineCost()));
            reportSummary.setHospitalPharmacyDispenseNoEssentialMedicineCost(reportSummary.getHospitalPharmacyDispenseNoEssentialMedicineCost().add(medicineReport.getHospitalPharmacyDispenseNoEssentialMedicineCost()));
            reportSummary.setHospitalPharmacyDispenseCost(reportSummary.getHospitalPharmacyDispenseCost().add(medicineReport.getHospitalPharmacyDispenseCost()));

            reportSummary.setHospitalAutomaticDispenseEssentialMedicineCost(reportSummary.getHospitalAutomaticDispenseEssentialMedicineCost().add(medicineReport.getHospitalAutomaticDispenseEssentialMedicineCost()));
            reportSummary.setHospitalAutomaticDispenseNoEssentialMedicineCost(reportSummary.getHospitalAutomaticDispenseNoEssentialMedicineCost().add(medicineReport.getHospitalAutomaticDispenseNoEssentialMedicineCost()));
            reportSummary.setHospitalAutomaticDispenseCost(reportSummary.getHospitalAutomaticDispenseCost().add(medicineReport.getHospitalAutomaticDispenseCost()));
            reportSummary.setDispenseEssentialMedicineCost(reportSummary.getDispenseEssentialMedicineCost().add(medicineReport.getDispenseEssentialMedicineCost()));
            reportSummary.setDispenseNoEssentialMedicineCost(reportSummary.getDispenseNoEssentialMedicineCost().add(medicineReport.getDispenseNoEssentialMedicineCost()));
            reportSummary.setDispenseCost(reportSummary.getDispenseCost().add(medicineReport.getDispenseCost()));
            reportSummary.setStockOutEssentialMedicineCost(reportSummary.getStockOutEssentialMedicineCost().add(medicineReport.getStockOutEssentialMedicineCost()));
            reportSummary.setStockOutNoEssentialMedicineCost(reportSummary.getStockOutNoEssentialMedicineCost().add(medicineReport.getStockOutNoEssentialMedicineCost()));
            reportSummary.setStockOutCost(reportSummary.getStockOutCost().add(medicineReport.getStockOutCost()));
            reportSummary.setTotalEssentialMedicineCost(reportSummary.getTotalEssentialMedicineCost().add(medicineReport.getTotalEssentialMedicineCost()));
            reportSummary.setTotalNoEssentialMedicineCost(reportSummary.getTotalNoEssentialMedicineCost().add(medicineReport.getTotalNoEssentialMedicineCost()));
            reportSummary.setTotalCost(reportSummary.getTotalCost().add(medicineReport.getTotalCost()));
            reportSummary.setEssentialMedicineModifyCost(reportSummary.getEssentialMedicineModifyCost().add(medicineReport.getEssentialMedicineModifyCost()));
            reportSummary.setNoEssentialMedicineModifyCost(reportSummary.getNoEssentialMedicineModifyCost().add(medicineReport.getNoEssentialMedicineModifyCost()));
            reportSummary.setModifyCost(reportSummary.getModifyCost().add(medicineReport.getModifyCost()));
        }
        reportSummary.setFeeType1Name("合计");
        reportSummary.pretty(param);
        return reportSummary;
    }

    /**
     * 查询报表住院未结算数据
     *
     * @param param 进销存报表param
     * @return 住院未结算数据
     */
    public InventoryReport selectReportNoSettleData(GoodsInventoryParam param) {
        return hologresGoodsInventoryMapper.selectReportNoSettleData(TableUtils.getCisTable(), param);
    }

    public List<InventoryClassify> classify(GoodsInventoryParam param, List<String> feeType1IdList) throws Exception {
        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
        CompletableFuture<List<InventoryClassify>> beginClassifyDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectClassifyBeginData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryClassify>> actionClassifyDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectClassifyActionData(TableUtils.getCisTable(), param);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryClassify>> endClassifyDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectClassifyEndData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<List<InventoryClassify>> taxRatClassifyDataListFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper
                    .selectClassifyTaxRatModify(TableUtils.getCisTable(), param);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture.allOf(beginClassifyDataListFuture, actionClassifyDataListFuture, endClassifyDataListFuture, taxRatClassifyDataListFuture).join();
        List<InventoryClassify> beginClassifyDataList = beginClassifyDataListFuture.get();
        List<InventoryClassify> actionClassifyList = actionClassifyDataListFuture.get();
        List<InventoryClassify> endClassifyDataList = endClassifyDataListFuture.get();
        List<InventoryClassify> taxRatClassifyDataList = taxRatClassifyDataListFuture.get();
        Map<String, List<InventoryClassify>> taxRatClassifyMap;
        if (CollUtil.isNotEmpty(taxRatClassifyDataList)) {
            taxRatClassifyMap = taxRatClassifyDataList.stream().collect(Collectors.groupingBy(InventoryClassify::getFeeType1));
        } else {
            taxRatClassifyMap = new HashMap<>();
        }
        List<InventoryClassify> reportList = new ArrayList<>();
        for (String feeType1Id : feeType1IdList) {
            InventoryClassify classify = new InventoryClassify();
            if (!CollUtil.isEmpty(actionClassifyList)) {
                List<InventoryClassify> actionFilterList
                        = actionClassifyList.stream().filter(beginIr -> beginIr.getFeeType1().equals(feeType1Id))
                        .collect(Collectors.toList());
                if (!CollUtil.isEmpty(actionFilterList)) {
                    classify = actionFilterList.get(0);
                }
            }
            classify.setFeeType1(feeType1Id);
            classify.setFeeType1Name(dimensionQuery.queryProductClassifyLevel1(param.getChainId(), feeType1Id, param.getHisType()));
            classify.setBeginAndEndData(beginClassifyDataList, endClassifyDataList);
            classify.setTaxModify(taxRatClassifyMap.get(feeType1Id));
            classify.pretty(param);
            reportList.add(classify);
        }
        return reportList;
    }

    public InventoryClassify classifySummary(GoodsInventoryParam param) throws Exception {
        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
        CompletableFuture<InventoryClassify> beginClassifySummaryDataFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectClassifySummaryBeginData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<InventoryClassify> actionClassifySummaryDataFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectClassifySummaryActionData(TableUtils.getCisTable(), param);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<InventoryClassify> endClassifySummaryDataFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper.selectClassifySummaryEndData(TableUtils.getCisTable(), param, tempTable);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture<InventoryClassify> taxRatClassifySummaryDataFuture
                = abcyunExecutorPool.supplyAsync(() -> {
            return hologresGoodsInventoryMapper
                    .selectClassifySummaryTaxRatModify(TableUtils.getCisTable(), param);
        }).exceptionally(e -> {
            throw new CompletionException(e);
        });
        CompletableFuture.allOf(beginClassifySummaryDataFuture, actionClassifySummaryDataFuture, endClassifySummaryDataFuture, taxRatClassifySummaryDataFuture).join();
        InventoryClassify beginClassifySummaryData = beginClassifySummaryDataFuture.get();
        InventoryClassify actionClassifySummaryData = actionClassifySummaryDataFuture.get();
        InventoryClassify endClassifySummaryData = endClassifySummaryDataFuture.get();
        InventoryClassify taxRatClassifySummaryData = taxRatClassifySummaryDataFuture.get();
        InventoryClassify classifySummary = new InventoryClassify();
        if (!BeanUtil.isEmpty(actionClassifySummaryData)) {
            classifySummary = actionClassifySummaryData;
        }
        classifySummary.setFeeType1Name("合计");
        classifySummary.setBeginAndEndSummaryData(beginClassifySummaryData, endClassifySummaryData);
        classifySummary.setTaxModify(CollUtil.toList(taxRatClassifySummaryData));
        classifySummary.pretty(param);
        return classifySummary;
    }

    public List<InventoryProfitCategoryType> availableProfitCategoryType(GoodsInventoryParam param) {
        List<InventoryProfitCategoryType> list = new ArrayList<>();
        Set<Long> profitCategoryTypeIds = hologresGoodsInventoryMapper.selectProfitCategoryTypeId(TableUtils.getCisTable(), param);
        Map<Long, V2GoodsProfitCategoryType> goodsProfitCategoryTypeMap = dimensionQuery.queryGoodsProfitCategoryTypeByChainIdAndId(param.getChainId(), profitCategoryTypeIds);
        for (Long profitCategoryTypeId : profitCategoryTypeIds) {
            if (!BeanUtil.isEmpty(goodsProfitCategoryTypeMap.get(profitCategoryTypeId))) {
                InventoryProfitCategoryType inventoryProfitCategoryType = new InventoryProfitCategoryType(String.valueOf(profitCategoryTypeId), goodsProfitCategoryTypeMap.get(profitCategoryTypeId).getName());
                list.add(inventoryProfitCategoryType);
            }
        }
        return list;
    }

    /**
     * 设置是否包含店内调拨： 1:包含 0:不包含
     * 连锁总部子店以及单店都可以统计店间调拨，店内调拨是否统计根据以下逻辑判断
     * 1.筛选了多库房的必然包含店内调拨
     * 2.没筛选多库房的,单库房开通了多库房才会统计店内调拨，子店以及总部只展示店间调拨
     *
     * @param param          进销存统计param
     * @param dimensionQuery 公共查询
     */
    public void setIsContainsAllotInInside(GoodsInventoryParam param, DimensionQuery dimensionQuery) {
        int isContainsAllotInInside = 0;
        boolean isOpenPharmacyFlagNumber;
        if (!param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            isOpenPharmacyFlagNumber = dimensionQuery.queryOpenPharmacyFlagNumberByOrgan(TableUtils.getCisGoodsTable(), param.getChainId(), param.getClinicId()) == 20;
        } else {
            isOpenPharmacyFlagNumber = dimensionQuery.queryIsOpenPharmacyByOrgan(TableUtils.getCisGoodsTable(), param.getChainId());
        }
        if (!CollUtil.isEmpty(param.getPharmacyNos())) {
            isContainsAllotInInside = 1;
        } else {
            if (isOpenPharmacyFlagNumber && param.getDispensaryType().equals(HisTypeEnum.SINGLE.getTypeNumber())) {
                isContainsAllotInInside = 1;
            }
        }
        param.setIsContainsAllotInInside(isContainsAllotInInside);
    }

    /**
     * 查询进销存来源,有序展示 合作诊所常态展示,电商外卖目前各自都只有一种
     *
     * @param param 进销存统计param
     * @return 来源列表
     */
    public List<InventorySource> availableScene(GoodsInventoryParam param) {
        List<InventorySource> list = new ArrayList<>();
        List<InventorySource> inventorySourceList = hologresGoodsInventoryMapper.selectInventorySource(TableUtils.getCisTable(), param);
        Map<Integer, List<InventorySource>> sourceMap = inventorySourceList.stream().collect(Collectors.groupingBy(InventorySource::getId));
        if (CollUtil.isNotEmpty(sourceMap.get(GoodsInventorySceneTypeEnum.RETAIL.getTypeId()))) {
            list.add(new InventorySource(GoodsInventorySceneTypeEnum.RETAIL.getTypeId(), GoodsInventorySceneTypeEnum.RETAIL.getTypeName()));
        }
        if (CollUtil.isNotEmpty(sourceMap.get(GoodsInventorySceneTypeEnum.EC.getTypeId()))) {
            list.add(new InventorySource(GoodsInventorySceneTypeEnum.EC.getTypeId(), GoodsInventorySceneTypeEnum.EC.getTypeName() + "-" + B2cEcTypeEnum.PDD.getTypeName()));
        }
        if (CollUtil.isNotEmpty(sourceMap.get(GoodsInventorySceneTypeEnum.MT.getTypeId()))) {
            list.add(new InventorySource(GoodsInventorySceneTypeEnum.MT.getTypeId(), "外卖-" + GoodsInventorySceneTypeEnum.MT.getTypeName()));
        }
        if (CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(param.getHisType())) {
            //查询合作诊所数据
            List<ChargeSourceType> cooperationClinics = odsChargeMapper.selectCooperativeClinic(TableUtils.getCisBasicTable(), param.getChainId(), param.getClinicId());
            if (CollUtil.isNotEmpty(cooperationClinics)) {
                //合作诊所 目前根据场景值设定的来源 业务都是增加5或10 暂定99为合作诊所
                InventorySource cooperationClinicSource = new InventorySource(99, "合作诊所");
                List<InventorySource> cooperationClinicChildren = new ArrayList<>();
                cooperationClinics.forEach(cooperationClinic -> {
                    InventorySource inventorySource = new InventorySource(cooperationClinic.getStrId(), cooperationClinic.getName());
                    cooperationClinicChildren.add(inventorySource);
                });
                cooperationClinicSource.setChildren(cooperationClinicChildren);
                list.add(cooperationClinicSource);
            }
        }
        return list;
    }
}
