package cn.abc.flink.stat.service.cis.commission.pojos;


import lombok.experimental.Accessors;
/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/22 16:02
 */
@Accessors(chain = true)
public class V3CommissionOperationLogBase {
    protected String id;
    protected String chainId;
    protected String clinicId;
    protected String commissionId;
    protected String commissionItemId;
    protected String operationId;
    protected Integer operationType;
    protected String productId;
    protected String type;
    protected String subType;
    protected Integer customType;
    protected String name;
    protected Integer mode;
    protected String displaySpec;

    protected Integer isDeleted;
    protected String created;
    protected String createdBy;

    protected String particulars;

    public String getId() {
        return this.id;
    }

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getCommissionId() {
        return this.commissionId;
    }

    public String getCommissionItemId() {
        return this.commissionItemId;
    }

    public String getOperationId() {
        return this.operationId;
    }

    public Integer getOperationType() {
        return this.operationType;
    }

    public String getProductId() {
        return this.productId;
    }

    public String getType() {
        return this.type;
    }

    public String getSubType() {
        return this.subType;
    }

    public Integer getCustomType() {
        return this.customType;
    }

    public String getName() {
        return this.name;
    }

    public Integer getMode() {
        return this.mode;
    }

    public String getDisplaySpec() {
        return this.displaySpec;
    }

    public Integer getIsDeleted() {
        return this.isDeleted;
    }

    public String getCreated() {
        return this.created;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public String getParticulars() {
        return this.particulars;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setCommissionId(String commissionId) {
        this.commissionId = commissionId;
    }

    public void setCommissionItemId(String commissionItemId) {
        this.commissionItemId = commissionItemId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public void setCustomType(Integer customType) {
        this.customType = customType;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public void setDisplaySpec(String displaySpec) {
        this.displaySpec = displaySpec;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setParticulars(String particulars) {
        this.particulars = particulars;
    }

}
