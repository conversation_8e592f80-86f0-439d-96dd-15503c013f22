package cn.abc.flink.stat.service.cis.allocation;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.request.params.GoodsInfoParam;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresAllocationMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.pojo.allocation.AllocationParam;
import cn.abc.flink.stat.pojo.allocation.InventoryAllocationDetail;
import cn.abc.flink.stat.pojo.allocation.InventoryAllocationSummary;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.V2GoodsClassify;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 调拨统计
 */
@Service
public class AllocationService {

    private static final Logger logger = LoggerFactory.getLogger(AllocationService.class);
    @Resource
    private HologresAllocationMapper hologresAllocationMapper;

    @Autowired
    DimensionQuery dimensionQuery;

    /**
     * 查询调拨统计汇总
     *
     * @param param 调拨统计param
     * @return 调拨统计汇总data
     * @throws Exception e
     */
    public V2StatResponse allocationSummary(AllocationParam param) throws Exception {
        V2StatResponse response = new V2StatResponse();
        List<InventoryAllocationSummary> list = hologresAllocationMapper.countAllocationInfo(TableUtils.getCisTable(), param);
        Set<String> organSet = new HashSet<>();
        for (InventoryAllocationSummary countGoodStockTrans : list) {
            organSet.add(countGoodStockTrans.getFromOrganId());
            organSet.add(countGoodStockTrans.getToOrganId());
        }
        CompletableFuture<List<TableHeaderEmployeeItem>> headerFeature = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.getTableHeaderEmployeeItems(
                    param.getParams().getEmployeeId(),
                    HeaderTableKeyConfig.STAT_ALLOCATION_SUMMARY,
                    param.getParams().getViewModeInteger(),
                    param.getParams().getNodeType(),
                    HeaderTableKeyConfig.EXCLUDE_HIDDEN_1);
        });
        CompletableFuture<Map<String, Map<Integer, String>>> pharmacyNoMapFeature = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryPharmacyNameByNo(param.getChainId(), param.getClinicId(), 0);
        });
        CompletableFuture<Map<String, String>> organMapFeature = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryOrganByIds(organSet);
        });
        CompletableFuture.allOf(headerFeature, pharmacyNoMapFeature, organMapFeature).join();
        List<TableHeaderEmployeeItem> headerEmployeeItems = headerFeature.get();
        Map<String, Map<Integer, String>> pharmacyNoMap = pharmacyNoMapFeature.get();
        Map<String, String> organMap = organMapFeature.get();
        for (InventoryAllocationSummary allocationSummary : list) {
            allocationSummary.setFromOrganName(organMap.get(allocationSummary.getFromOrganId()));
            allocationSummary.setToOrganName(organMap.get(allocationSummary.getToOrganId()));
            if (!StrUtil.isBlank(param.getClinicId())) {
                allocationSummary.setPharmacyNoName(pharmacyNoMap.get(param.getClinicId()));
            }
            allocationSummary.pretty();
        }
        if (!CollUtil.isEmpty(list)) {
            InventoryAllocationSummary summary = new InventoryAllocationSummary();
            summary.setFromOrganName("合计");
            summary.setToOrganName("-");
            summary.setCountAndAmount(list);
            summary.pretty();
            response.setSummary(summary);
        }
        headerHandler(headerEmployeeItems, param.getAllocationType());
        response.setHeader(headerEmployeeItems);
        response.setData(list);
        response.setTotal(new StatResponseTotal((long) list.size()));
        return response;
    }

    /**
     * 查询调拨统计明细
     *
     * @param param 调拨统计param
     * @return 调拨统计明细
     * @throws Exception e
     */
    public V2StatResponse allocationDetail(AllocationParam param) throws Exception {
        V2StatResponse response = new V2StatResponse();
        List<InventoryAllocationDetail> list = hologresAllocationMapper.selectAllocationDetailsList(TableUtils.getCisTable(), param);
        Set<String> organSet = new HashSet<>();
        Set<String> userSet = new HashSet<>();
        List<String> goodsIds = new ArrayList<>();
        for (InventoryAllocationDetail goodStockTrans : list) {
            goodsIds.add(goodStockTrans.getGoodsId());
            organSet.add(goodStockTrans.getFromOrganId());
            organSet.add(goodStockTrans.getToOrganId());
            userSet.add(goodStockTrans.getOperatorId());
        }
        CompletableFuture<List<TableHeaderEmployeeItem>> headerFeature = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.getTableHeaderEmployeeItems(
                    param.getParams().getEmployeeId(),
                    HeaderTableKeyConfig.STAT_ALLOCATION_DETAIL,
                    param.getParams().getViewModeInteger(),
                    param.getParams().getNodeType(),
                    HeaderTableKeyConfig.EXCLUDE_HIDDEN_1);
        });
        CompletableFuture<Map<String, Map<Integer, String>>> pharmacyNoMapFeature = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryPharmacyNameByNo(param.getChainId(), param.getClinicId(), 0);
        });
        CompletableFuture<Map<String, String>> organMapFeature = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryOrganByIds(organSet);
        });
        CompletableFuture<Map<String, String>> userMapFeature = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryEmployeeNameByChainAndIds(param.getChainId(), userSet);
        });
        CompletableFuture<Map<String, V2GoodsClassify>> goodsInfoFuture = CompletableFuture.supplyAsync(() -> {
            return dimensionQuery.queryGoodsInfo(TableUtils.getCisGoodsTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), 0,
                            null, null),
                    goodsIds, null);
        });
        CompletableFuture<Long> totalF = CompletableFuture.supplyAsync(() -> {
            return hologresAllocationMapper.selectAllocationDetailTotal(TableUtils.getCisTable(), param);
        });
        CompletableFuture.allOf(headerFeature, pharmacyNoMapFeature, organMapFeature, userMapFeature,
                goodsInfoFuture, totalF).join();
        List<TableHeaderEmployeeItem> headerEmployeeItems = headerFeature.get();
        Map<String, Map<Integer, String>> pharmacyNoMap = pharmacyNoMapFeature.get();
        Map<String, String> organMap = organMapFeature.get();
        Map<String, String> userMap = userMapFeature.get();
        Map<String, V2GoodsClassify> goodsMap = goodsInfoFuture.get();
        for (InventoryAllocationDetail allocationDetail : list) {
            allocationDetail.setFeeType1Text(dimensionQuery.queryProductClassifyLevel1(param.getChainId(), allocationDetail.getFeeType1(), param.getHisType()));
            allocationDetail.setFromOrganName(organMap.get(allocationDetail.getFromOrganId()));
            allocationDetail.setToOrganName(organMap.get(allocationDetail.getToOrganId()));
            allocationDetail.setOperatorName(userMap.get(allocationDetail.getOperatorId()));
            allocationDetail.setGoodsMessage(goodsMap);
            if (!StrUtil.isBlank(param.getClinicId())) {
                allocationDetail.setPharmacyNoName(pharmacyNoMap.get(param.getClinicId()));
            }
        }

        response.setData(list);
        response.setTotal(new StatResponseTotal(totalF.get()));
        headerHandler(headerEmployeeItems, param.getAllocationType());
        response.setHeader(headerEmployeeItems);
        return response;
    }

    /**
     * @param headerEmployeeItems 表头
     * @param allocationType      调拨类型 1店间调拨 2店内调拨
     */
    private void headerHandler(List<TableHeaderEmployeeItem> headerEmployeeItems, Integer allocationType) {
        if (allocationType != null && allocationType == CommonConstants.NUMBER_TWO) {
            for (TableHeaderEmployeeItem headerEmployeeItem : headerEmployeeItems) {
                if (headerEmployeeItem.getProp().equals("fromOrganName")) {
                    headerEmployeeItem.setProp("fromPharmacyNoName");
                    headerEmployeeItem.setLabel("调出药房");
                }
                if (headerEmployeeItem.getProp().equals("toOrganName")) {
                    headerEmployeeItem.setProp("toPharmacyNoName");
                    headerEmployeeItem.setLabel("调入药房");
                }
            }
        }
    }

    /**
     * @param param param
     * @return List<ExcelUtils.AbcExcelSheet>
     * @throws Exception -
     */
    public ExcelUtils.AbcExcelSheet asyncAllocationSummaryExport(AllocationParam param)
            throws Exception {
        V2StatResponse summary = allocationSummary(param);
        ExcelUtils.AbcExcelSheet summarySheet = new ExcelUtils.AbcExcelSheet();
        summarySheet.setName("汇总");
        summarySheet.setSheetDefinition(ExcelUtils.exportTableHeader(summary.getHeader()));
        summarySheet.setData(ExcelUtils.exportDataV2(summary.getData(), summary.getHeader()));
        return summarySheet;
    }

    /**
     * @param param param
     * @return List<ExcelUtils.AbcExcelSheet>
     * @throws Exception -
     */
    public ExcelUtils.AbcExcelSheet asyncAllocationDetailExport(AllocationParam param)
            throws Exception {
        V2StatResponse detail = allocationDetail(param);
        ExcelUtils.AbcExcelSheet detailSheet = new ExcelUtils.AbcExcelSheet();
        detailSheet.setName("明细");
        detailSheet.setSheetDefinition(ExcelUtils.exportTableHeader(detail.getHeader()));
        detailSheet.setData(ExcelUtils.exportDataV2(detail.getData(), detail.getHeader()));
        return detailSheet;
    }
}
