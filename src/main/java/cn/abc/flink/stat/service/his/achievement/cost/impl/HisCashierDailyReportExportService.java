package cn.abc.flink.stat.service.his.achievement.cost.impl;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.service.his.achievement.cost.IHisRevenuesCostSevice;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargedSettlePatientReqParams;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HospCashierDailyReportParams;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/04/16 15:53
 */
@Component
public class HisCashierDailyReportExportService implements BaseAsyncExportInterface {

    @Resource
    private IHisRevenuesCostSevice service;

    @Override
    public String getKey() {
        return "hosp-cashier-daily-report";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        return "收费员日报" + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String headerEmployeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String chainViewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");

        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String cashierId = (String) MapUtils.isExistsAndReturn(params, "cashierId");
        List<String> cashierIds = (List<String>) MapUtils.isExistsAndReturn(params, "cashierIds");
        String provinceId = (String) MapUtils.isExistsAndReturn(params, "provinceId");

        HospCashierDailyReportParams reqParams = new HospCashierDailyReportParams();
        reqParams.initAbcCisBaseQueryParams(chainId, clinicId, headerEmployeeId, chainViewMode, clinicNodeType);
        reqParams.getParams().setEmployeeId(headerEmployeeId);
        reqParams.setSingleStore(chainViewMode, clinicNodeType.toString());
        reqParams.setBeginDate(TimeUtils.appendBegin(beginDate));
        reqParams.setEndDate(TimeUtils.appendEnd(endDate));
        reqParams.setCashierId(cashierId);
        reqParams.setCashierIds(cashierIds);
        reqParams.setProvinceId(provinceId);
        reqParams.initDs();

        List<ExcelUtils.AbcExcelSheet> sheets = service.exportCashierDailyReport(reqParams);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
