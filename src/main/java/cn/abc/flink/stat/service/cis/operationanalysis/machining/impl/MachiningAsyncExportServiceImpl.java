package cn.abc.flink.stat.service.cis.operationanalysis.machining.impl;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.db.dao.MachiningMapper;
import cn.abc.flink.stat.service.cis.operationanalysis.machining.IMachiningService;
import cn.abc.flink.stat.service.cis.operationanalysis.machining.domain.MachiningParam;
import cn.abc.flink.stat.service.cis.operationanalysis.machining.domain.MachiningResult;
import cn.abc.flink.stat.service.cis.operationanalysis.machining.domain.MachiningVo;
import cn.abc.flink.stat.service.cis.operationanalysis.machining.handler.MachiningHandler;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.operationanalysis.machining.impl
 * @Author: zs
 * @CreateTime: 2022-08-31  10:36
 * @Description: 运营分析-加工结算异步导出实现类
 * @Version: 1.0
 */
@Service
public class MachiningAsyncExportServiceImpl implements BaseAsyncExportInterface {
    private static final Logger LOGGER = LoggerFactory.getLogger(MachiningAsyncExportServiceImpl.class);

    @Autowired
    private IMachiningService machiningService;

    @Override
    public String getKey() {
        return "machining-project";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String name = "加工结算统计.xlsx";
        if(beginDate != null && endDate != null){
            name = "加工结算统计" + beginDate + "_" + endDate + ".xlsx";
        }
        return name;
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws ParseException, ExecutionException, InterruptedException {
        MachiningParam param = new MachiningParam();
        param.setClinicNodeType(Double.valueOf((double) params.get("headerClinicType")).intValue());
        param.setChainViewMode(Integer.parseInt((String) MapUtils.isExistsAndReturn(params, "headerViewMode")));
        param.setChainId((String) MapUtils.isExistsAndReturn(params, "chainId"));
        param.setClinicId((String) MapUtils.isExistsAndReturn(params, "clinicId"));
        param.setBeginDate((String) MapUtils.isExistsAndReturn(params, "beginDate"));
        param.setEndDate(TimeUtils.appendEnd((String) MapUtils.isExistsAndReturn(params, "endDate")));
        param.setCreatedBeginDate((String) MapUtils.isExistsAndReturn(params, "createdBeginDate"));
        param.setCreatedEndDate(TimeUtils.appendEnd((String) MapUtils.isExistsAndReturn(params, "createdEndDate")));
        param.setCurrentEmployeeId((String) MapUtils.isExistsAndReturn(params, "headerEmployeeId"));
//        Integer pageIndex = (Integer) MapUtils.isExistsAndReturn(params, "pageIndex");
//        Integer pageSize = (Integer) MapUtils.isExistsAndReturn(params, "pageSize");
//        param.setPageIndex(pageIndex * pageSize);
//        param.setPageIndex(pageSize);
        //加工项目名
        param.setProcessingItemName((String) MapUtils.isExistsAndReturn(params, "processingItemName"));
        //加工厂家
        param.setProcessingFactoryId((String) MapUtils.isExistsAndReturn(params, "processingFactoryId"));
        Object processingType1 = MapUtils.isExistsAndReturn(params, "processingType");
        if (processingType1 != null && !"".equals(processingType1)) {
            Integer processingType = new Integer((String) MapUtils.isExistsAndReturn(params, "processingType"));
            param.setProcessingType(processingType);
        }

        //患者
        param.setPatientId((String) MapUtils.isExistsAndReturn(params, "patientId"));
        //导出
        List<ExcelUtils.AbcExcelSheet> sheets = machiningService.asyncExport(param);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtils.exportCustomizeStyle(baos, sheets);
        return baos;
    }
}
