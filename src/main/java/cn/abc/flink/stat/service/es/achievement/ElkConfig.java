package cn.abc.flink.stat.service.es.achievement;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "elkconfig")
@Configuration
public class ElkConfig {

    private String clustername;
    private String index;


    public String getClustername() {
        return this.clustername;
    }

    public String getIndex() {
        return this.index;
    }


    public void setClustername(String clustername) {
        this.clustername = clustername;
    }

    public void setIndex(String index) {
        this.index = index;
    }

}
