package cn.abc.flink.stat.service.cis.outpatient.handler;

import cn.abc.flink.stat.common.AbcDefaultValueUtils;
import cn.abc.flink.stat.common.OutpatientChargeTypeEnum;
import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Department;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2OutpatientPrescriptionForm;
import cn.abc.flink.stat.dimension.domain.V2OutpatientPrescriptionFormItem;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.domain.V2PatientExtend;
import cn.abc.flink.stat.dimension.domain.V2PatientSourceType;
import cn.abc.flink.stat.service.cis.handler.PatientHandler;
import cn.abc.flink.stat.service.cis.outpatient.domain.EyeExaminationItem;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientOralExamination;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientProductFormItem;
import cn.abc.flink.stat.service.cis.outpatient.domain.PatientListInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.RevisitParams;
import cn.abc.flink.stat.service.cis.outpatient.domain.V2OutpatientPrescriptionFormItemDto;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.entity.RevenueChargeDetailTransactionDao;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.parquet.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/29 9:24 上午
 * @modified ljc
 */
@Component
public class OutpatientHandler {
    private static Logger logger = LoggerFactory.getLogger(OutpatientHandler.class);
    public static final List<Integer> TOP_LEFT = Arrays.asList(11, 12, 13, 14, 15, 16, 17, 18, 51, 52, 53, 54, 55);
    public static final List<Integer> TOP_RIGHT = Arrays.asList(21, 22, 23, 24, 25, 26, 27, 28, 61, 62, 63, 64, 65);
    public static final List<Integer> BOTTOM_LEF = Arrays.asList(41, 42, 43, 44, 45, 46, 47, 48, 81, 82, 83, 84, 85);
    public static final List<Integer> BOTTOM_RIGHT = Arrays.asList(31, 32, 33, 34, 35, 36, 37, 38, 71, 72, 73, 74, 75);
    public static final HashMap<String, String> UNIT = new HashMap<>();



    @Resource
    DimensionQuery query;
    static {
        UNIT.put("frameSpherical", "D");
        UNIT.put("frameLenticular", "D");
        UNIT.put("frameAxial", "°");
        UNIT.put("frameAdd", "D");
        UNIT.put("framePupilDistance", "mm");
        UNIT.put("framePupilHeight", "mm");
        UNIT.put("contactFocalLength", "D");
        UNIT.put("contactBozr", "D");
        UNIT.put("contactDiameter", "mm");
        UNIT.put("contactLenticular", "D");
        UNIT.put("contactAxial", "°");
    }

    /**
     * @Description:
     * @param
     * @param l1 -
     * @param l2 -
     * @return
     * @return java.lang.Double
     * @Author: zs
     * @Date: 2022/8/11 20:03
     */
    public Double getRatio(Long l1, Long l2) {
        BigDecimal b1 = BigDecimal.valueOf(l1);
        BigDecimal b2 = BigDecimal.valueOf(l2);
        return b1.divide(b2, CommonConstants.NUMBER_FOUR, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    /**
     * 根据传入维度，获取初复诊字段
     *
     * @param dimension -
     * @return string
     */
    public String getRevisitCol(String dimension) {
        String result = "is_doctor_revisit";
        switch (dimension) {
            case RevisitParams.CHAINSTR:
            case RevisitParams.CHAINNUM:
                result =  "is_chain_revisit";
                break;
            case RevisitParams.CLINICSTR:
            case RevisitParams.CLINICNUM:
                result =  "is_clinic_revisit";
                break;
            case RevisitParams.DEPTSTR:
            case RevisitParams.DEPTNUM:
                result =  "is_depart_revisit";
                break;
            case RevisitParams.DOCSTR:
            case RevisitParams.DOCNUM:
                result =  "is_doctor_revisit";
                break;
            default:
        }
        return result;
    }

    /**
     * @Description:
     * @param
     * @param dimension -
     * @return
     * @return boolean
     * @Author: zs
     * @Date: 2022/8/11 20:04
     */
    public boolean isDeptDim(String dimension) {
        switch (dimension) {
            case RevisitParams.DEPTSTR:
            case RevisitParams.DEPTNUM:
                return true;
            default:
                return false;
        }
    }

    /**
     * 是否为初复诊过滤sql
     *
     * @param col -
     * @param isRevisit -
     * @return String
     */
    public String getVisitFilterSql(String col, Integer isRevisit) {
        if (isRevisit == null) {
            return null;
        }
        return " " + col + "=" + isRevisit + " ";
    }

    /**
     * @Description:
     * @param
     * @param s -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/22 22:51
     */
    public String getDateSql(String s, String mapperName) {
        switch (s) {
            case "month":
                if ("holo".equals(mapperName)) {
                    return "substr(outpatient_sheet_diagnosed_date::varchar,1,7)";
                }
                return "substr(outpatient_sheet_diagnosed_date,1,7)";
            default:
                if ("holo".equals(mapperName)) {
                    return "substr(outpatient_sheet_diagnosed_date::varchar,1,10)";
                }
                return "substr(outpatient_sheet_diagnosed_date,1,10)";
        }
    }

    /**
     * 转换为Map
     *
     * @param patients -
     * @param chainId -
     * @return List
     */
    public List<Map<String, Object>> genPatientDataMap(List<PatientListInfo> patients, String chainId) {
        List<Map<String, Object>> list = new ArrayList<>();
        HashSet<String> patientIds = new HashSet<>();
        for (PatientListInfo p : patients) {
            patientIds.add(p.getId());
        }
        HashMap<String, V2Patient> v2PatientMap = new HashMap<>();
        //获取患者纬度信息
        if (patientIds != null && patientIds.size() > 0) {
            List<V2Patient> v2Patients = query.queryPatient(chainId, patientIds);
            if (v2Patients != null && v2Patients.size() > 0) {
                for (V2Patient v : v2Patients) {
                    v2PatientMap.put(v.getId(), v);
                }
            }
        }

        for (PatientListInfo p : patients) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", p.getId());
            map.put("name", p.getName());
            map.put("sex", p.getSex());
            map.put("mobile", p.getMobile());
            map.put("lastCreated", p.getLastCreated());
            map.put("count", p.getCount());
            map.put("fee", p.getFee());
            map.put("chain_id", p.getChainId());
            map.put("isMember", p.getIsMember());
            Map<String, Object> age = new HashMap<>();
            //获取患者纬度信息
            V2Patient v2Patient = v2PatientMap.get(p.getId());
            //纬度信息中有就用这个没有就用老的
            if (v2Patient != null) {
                map.put("name", v2Patient.getName());
                map.put("sex", v2Patient.getSex());
                map.put("mobile", v2Patient.getMobile());
            }
            JSONObject json = null;
            try {
                json = JSONObject.parseObject(p.getAge());
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("门诊日志json解析异常genPatientDataMap方法age字段");
            }

            if (json != null) {
                age.put("day", json.get("day"));
                age.put("month", json.get("month"));
                age.put("year", json.get("year"));
            }
            if (v2Patient != null && v2Patient.getBirthday() != null) {
                //通过患者生日和最近就诊时间计算患者年龄
                Map<String, Integer> stringIntegerMap = dayComparePrecise(v2Patient.getBirthday(), p.getLastCreated());
                age.put("day", stringIntegerMap.get("day"));
                age.put("month", stringIntegerMap.get("month"));
                age.put("year", stringIntegerMap.get("year"));
            }
            map.put("age", age);
            list.add(map);
        }
        return list;
    }

    /**
     * @param
     * @param employeeId     -
     * @param tableKey       -
     * @param chainViewMode  -
     * @param excludeHidden  -
     * @param clinicNodeType -
     * @return
     * @return java.util.List<cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/10 19:21
     */
    public List<TableHeaderEmployeeItem> getPatientHeader(String employeeId,
                                                          String tableKey,
                                                          Integer chainViewMode,
                                                          Integer excludeHidden,
                                                          Integer clinicNodeType) {
        return query.getTableHeaderEmployeeItems(employeeId, tableKey, chainViewMode, clinicNodeType, excludeHidden);
    }

    /**
     * @Description: 计算两个时间相差的年月日
     * @param
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @return
     * @return java.util.Map<java.lang.String,java.lang.Integer>
     * @Author: zs
     * @Date: 2022/8/17 11:23
     */
    public static Map<String, Integer> dayComparePrecise(String beginDate, String endDate) {
        HashMap<String, Integer> resultMap = new HashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (beginDate == null || endDate == null) {
            return null;
        }
        try {
            Date begin = dateFormat.parse(beginDate);
            Date end = dateFormat.parse(endDate);
            Calendar from = Calendar.getInstance();
            from.setTime(begin);
            Calendar to = Calendar.getInstance();
            to.setTime(end);
            int year = to.get(Calendar.YEAR) - from.get(Calendar.YEAR);
            int month = to.get(Calendar.MONTH) - from.get(Calendar.MONTH);
            int day = to.get(Calendar.DAY_OF_MONTH) - from.get(Calendar.DAY_OF_MONTH);
            if (month < 0) {
                month = CommonConstants.NUMBER_TWELVE - from.get(Calendar.MONTH) + to.get(Calendar.MONTH);
                year -= 1;
            }
            if (month == 0 && day < 0) {
                month = CommonConstants.NUMBER_TWELVE - from.get(Calendar.MONTH) + to.get(Calendar.MONTH);
                year -= 1;
            }
            if (day < 0) {
                day = from.getMaximum(Calendar.DAY_OF_MONTH) - from.get(Calendar.DAY_OF_MONTH) + to.get(Calendar.DAY_OF_MONTH);
                month -= 1;
            }
            resultMap.put("month", month);
            resultMap.put("year", year);
            resultMap.put("day", day);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return resultMap;
    }
    /**
     * 门诊清单数据组装
     *
     * @param infos                事实数据
     * @param organs               门店维度
     * @param emps                 emplpyee维度
     * @param patients             患者维度
     * @param sources              患者来源维度
     * @param pfMap                处方
     * @param pfiMap               处方item
     * @param outpatientProductMap -
     * @param hisType              0:普通诊所 1：牙科诊所 2：眼科诊所
     * @return List
     * @throws IllegalAccessException    -
     * @throws NoSuchMethodException     -
     * @throws InvocationTargetException -
     */
    public List<Map<String, Object>> genOutpatientDataMap(
            List<OutpatientListInfo> infos,
            Map<String, Organ> organs,
            Map<String, Employee> emps,
            Map<String, V2Patient> patients,
            Map<String, V2PatientSourceType> sources,
            Map<String, List<OutpatientProductFormItem>> outpatientProductMap,
            String hisType,
            Map<String, V2PatientExtend> v2PatientExtendMap, Map<String, Department> department)
            throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        List<Map<String, Object>> result = new ArrayList<>();
        if (infos != null) {
            for (OutpatientListInfo info : infos) {
                info.setFee(info.getFee() == null ? null : info.getFee().
                        setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
                info.setShebaoChargeType(OutpatientChargeTypeEnum.getTypeNameByName(info.getShebaoChargeTypeNumber()));
                Map<String, Object> one = BeanUtils.describe(info);
                //收费金额处理
                chargeField(one);
                //诊号 辅助检查 门店相关字段
                auxiliaryExaminationStoreFiled(organs, info, one);
                //口腔相关
                oralCavityFild(outpatientProductMap, hisType, info, one);
                // 牙科诊断
                JSONArray dentistryDiagnosisArray = null;
                try {
                    dentistryDiagnosisArray = JSONArray.parseArray(info.getExtendDiagnosisInfos());
                } catch (JSONException e) {
                    e.printStackTrace();
                    logger.error("门诊日志json解析失败genOutpatientDataMap方法extendDiagnosisInfos字段，报错信息{}",
                            e.getLocalizedMessage());
                }
                if (dentistryDiagnosisArray != null) {
                    String dentistryDiagnosis = handleJsonArr(dentistryDiagnosisArray, true);
                    one.put("diagnosis", dentistryDiagnosis);
                }
                one.put("diagnosisTime", info.getCreated());
                //患者 员工 就诊推荐等字段
                patientField(emps, patients, sources, info, one, v2PatientExtendMap, department);
                // 新增挂号收费员、门诊收费员、随访人员
                registeredOutpatientFollowUpMan(emps, info, one);
                //处方单相关字段
                prescriptionField(info, one, emps);
                //眼科相关字段
                eyeField(info, one);
                //体格检查：将体温，血氧，血压等字段显示出来
                physicalExaminationField(info, one);
                //<br>字符处理
                one.forEach((key, v) -> {
                    if (v != null && ("String").equals(v.getClass().getSimpleName())) {
                        v = ((String) v).replaceAll("<br>|<br/>|<br />|<br {2}/>", "");
                        if ("".equals(v)) {
                            v = "-";
                        }
                        one.put(key, v);
                    }
                });
                result.add(one);
            }
        }
        return result;
    }

    /**
     * @Description:
     * @param
     * @param info 门诊数据
     * @param result 结果
     * @return
     * @Author: zs
     * @Date: 2023/5/15 16:57
     */
    private void physicalExaminationField(OutpatientListInfo info, Map<String, Object> result) {
        //获取体格字段
        String physicalExamination = info.getPhysicalExamination();
        if (physicalExamination != null && !"".equals(physicalExamination)) {
            String[] split = physicalExamination.split("，");
            for (int i = 0; i < split.length; i++) {
                String name = split[i];
                if (name.contains("体温")) {
                    if (!result.containsKey("temperature")) {
                        result.put("temperature", name); //体温
                    }
                }
                if (name.contains("血压")) {
                    if (!result.containsKey("bloodPressure")) {
                        result.put("bloodPressure", name); //血压
                    }
                }
                if (name.contains("血糖")) {
                    if (!result.containsKey("bloodSugar")) {
                        result.put("bloodSugar", name); //血糖
                    }
                }
            }
        } else {
            //血糖，血压，体温字段置空
            result.put("temperature", "-"); //体温
            result.put("bloodPressure", "-"); //血压
            result.put("bloodSugar", "-"); //血糖
        }
    }

    /**
     * @Description:
     * @param
     * @param one -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 20:05
     */
    private void chargeField(Map<String, Object> one) {
        String personalHistory = (String) one.get("personalHistory");
        if (personalHistory == null || "".equals(personalHistory)) {
            one.put("personalHistory", "-");
        }
        String visitSourceRemark = (String) one.get("visitSourceRemark");
        if (visitSourceRemark == null) {
            one.put("visitSourceRemark", "-");
        }
        //删除类转换为Map时生成的class字段
        one.remove("class");
        one.remove("outpatient_charge_by");
        one.remove("registration_charge_by");
        one.remove("revisit_id");
    }

    /**
     * @Description: 诊号 辅助检查 门店相关字段
     * @param
     * @param organs -
     * @param info -
     * @param one -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 15:03
     */
    private void auxiliaryExaminationStoreFiled(Map<String, Organ> organs,
                                                OutpatientListInfo info, Map<String, Object> one) {
        one.put("patientAge", getJsonMap(info.getPatientAge()));
        // 添加诊号 by：libl
        one.put("orderNo", PatientHandler.handlePatientorderNo(info.getOrderNo().toString()));
        //json串解析
        if (info.getEpidemiologicalHistory() != null && !info.getEpidemiologicalHistory().isEmpty()) {
            one.put("epidemiologicalHistory", getEpidemiologicalHistoryStr(info.getEpidemiologicalHistory()));
        } else {
            one.put("epidemiologicalHistory", "-");
        }

        //auxiliaryExamination，从ExtendData Json中获取
        // 辅助检查
        String auxiliaryExaminations = info.getAuxiliaryExaminations();
        try {
            String extendData = info.getExtendData();
            Map<String, Object> extendMap = null;
            if (!Strings.isNullOrEmpty(extendData)) {
                extendMap = getJsonMap(extendData);
            }
            if (auxiliaryExaminations != null && !"[]".equals(auxiliaryExaminations)) {
                JSONArray auxExamination = JSONArray.parseArray(auxiliaryExaminations);
                String auxiliaryExamination = handleJsonArr(auxExamination, false);
                one.put("auxiliaryExaminations", auxiliaryExamination);
            } else {
                // 兼容之前的辅助检查。
                if (extendMap != null) {
                    one.put("auxiliaryExaminations", extendMap.get("auxiliaryExamination"));
                }
            }
            //方药
            one.put("chinesePrescription", extendMap.get("chinesePrescription"));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("门诊日志辅助检查字段解析失败auxiliaryExaminationStoreFiled方法auxiliaryExamination字段");
        }


        // 门店
        Organ organ = organs.get(info.getClinicId());
        if (organ != null) {
            one.put("clinicName", organ.getName());
            one.put("shortName", organ.getShortName());
        }
    }

    /**
     * @Description: 患者 员工 就诊推荐等字段
     * @param
     * @param emps -
     * @param patients -
     * @param sources -
     * @param info -
     * @param one -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 14:58
     */
    private void patientField(Map<String, Employee> emps,
                              Map<String, V2Patient> patients,
                              Map<String, V2PatientSourceType> sources, OutpatientListInfo info,
                              Map<String, Object> one, Map<String, V2PatientExtend> v2PatientExtendMap,
                              Map<String, Department> department) {
        // 患者
        V2Patient patient = patients.get(info.getPatientId());
        if (patient != null) {
            String idCard = patient.getIdCard();
            if (idCard != null && !"".equals(idCard)) {
                one.put("idNumber", idCard);
            } else {
                one.put("idNumber", "-");
            }
            String addr = patient.getAddressProvinceName() + patient.getAddressCityName()
                    + patient.getAddressDistrictName() + patient.getAddressDetail();
            addr = addr.replace("null", "");
            one.put("address", Strings.isNullOrEmpty(addr) ? "-" : addr);
            String profession = patient.getProfession();
            String company = patient.getCompany();
            // Modified by: libl 添加职业
            one.put("profession", Strings.isNullOrEmpty(profession) ? "-" : profession);
            one.put("company", Strings.isNullOrEmpty(company) ? "-" : company); //Modified by: libl 添加单位
            //将患者基本信息变成从patient表获取
            one.put("patientName", patient.getName() == null ? "-" : patient.getName());
            one.put("patientSn", patient.getSn() == null ? "-" : patient.getSn());
            one.put("patientSex", patient.getSex() == null ? "-" : patient.getSex());
            one.put("patientRemark", patient.getRemark() == null ? "-" : patient.getRemark());
            one.put("patientMobile", patient.getMobile() == null ? "-" : patient.getMobile());
            one.put("patientWeight", patient.getWeight() == null ? "-" : patient.getWeight());
            one.put("ethnicity", patient.getEthnicity() == null ? "-" : patient.getEthnicity());
            //年龄计算
            String lockTime = info.getAgeLockTime() == null ? info.getCreated():info.getAgeLockTime();
            if (patient.getBirthday() != null && !"".equals(patient.getBirthday())) {
                //计算生日
                Map<String, Integer> age = OutpatientHandler.dayComparePrecise(patient.getBirthday(), lockTime);
                one.put("patientAge", age);
            }
            one.put("height", patient.getHeight() == null ? "-" : patient.getHeight().setScale(2, RoundingMode.HALF_UP));
            one.put("email", patient.getEmail() == null ? "-" : patient.getEmail());
            one.put("nationality", patient.getNationality() == null ? "-" : patient.getNationality());
            one.put("crowdCategory", patient.getCrowdCategory() == null ? "-" : patient.getCrowdCategory());
        }
        V2PatientExtend v2PatientExtend = v2PatientExtendMap.get(info.getPatientId());
        if (v2PatientExtend != null) {
            one.put("parentName", v2PatientExtend.getParentName() == null ? "-" : v2PatientExtend.getParentName());
        }
        // by：libl 解决移除医生后没有医生的信息
        if (info != null && info.getOutpatientSheetCopywriterId() != null) {
            Employee employee = emps.get(info.getOutpatientSheetCopywriterId());
            if (employee != null) {
                String medicalAssistance = employee.getName();
                one.put("medicalAssistance", Strings.isNullOrEmpty(medicalAssistance) ? "-" : medicalAssistance);
            } else {
                one.put("medicalAssistance", "-");
            }
        }
        if (info.getDoctorName() == null || "".equals(info.getDoctorName())) {
            Employee employee = emps.get(info.getDoctorId());
            if (employee != null) {
                one.put("doctorName", employee.getName());
            } else {
                one.put("doctorName", "-");
            }
        }
        Department departmentDto = department.getOrDefault(info.getDepartmentId(), new Department());
        //科室
        one.put("departmentName", Strings.isNullOrEmpty(departmentDto.getName()) ? "-" : departmentDto.getName());
        //来源
        String patientSource = handlePatientSource(info.getSourceId(), info.getReferrerId(), sources, emps, patients);
        one.put("patientSource", patientSource);
        // 就诊推荐
        String visitSourceName = handleVisitSourceNameById(info, sources, emps, patients);
        one.put("visitSourceName", visitSourceName);
    }

    public String getMedicalAssistance(String empId, Set<String> ids, Map<String, Employee> emps) {
        StringBuffer result = new StringBuffer();
        if (empId != null && ids != null && ids.size() > 0) {
            int i = 0;
            for (String id:ids) {
                ++i;
                Employee employee = emps.get(id);
                if (employee != null) {
                    result.append(employee.getName());
                    if (i < ids.size() - 1) {
                        result.append(",");
                    }
                }
            }
            return result.toString();
        } else {
            return "-";
        }
    }
    /**
     * @Description: 口腔相关字段处理
     * @param
     * @param outpatientProductMap -
     * @param hisType -
     * @param info -
     * @param one -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 14:51
     */
    private void oralCavityFild(Map<String, List<OutpatientProductFormItem>> outpatientProductMap, String hisType,
                                OutpatientListInfo info, Map<String, Object> one) {
        // 口腔相关
        //删除口腔病历扩展
        one.remove("dentistry_extend");

        String de = info.getDentistryExtend();
        try {
            if (de != null) {
                JSONObject jo = JSON.parseObject(de);
                if ("1".equals(hisType) || "2".equals(hisType)) {
                    //治疗计划
                    JSONArray treatmentPlanArray = jo.getJSONArray("treatmentPlans");
                    String treatmentPlans = handleJsonArr(treatmentPlanArray, false);
                    one.put("treatmentPlans", treatmentPlans);
                }
                //处置 诊所和医院新增处置
                JSONArray disposalArray = jo.getJSONArray("disposals");
                String disposals = handleJsonArr(disposalArray, false);
                one.put("disposals", disposals);
                //口腔检查
                JSONArray examinationArray = jo.getJSONArray("dentistryExaminations");
                String oralExamination = handleJsonArr(examinationArray, false);
                if (info.getOralExamination() != null && !"".equals(info.getOralExamination())) {
                    one.put("oralExaminations", info.getOralExamination());
                } else {
                    if (oralExamination != null && !"".equals(oralExamination)) {
                        one.put("oralExaminations", oralExamination);
                    } else {
                        one.put("oralExaminations", "-");
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("门诊日志json解析失败报错,方法为oralCavityFild字段为dentistryExtend");
        }
        List<Map<String, Object>> outpatientProductItems = handleProductItem(outpatientProductMap.get(info.getOutpatientSheetId()));
        if (!outpatientProductItems.isEmpty()) {
            one.put("outpatientProductItem", outpatientProductItems);
        } else {
            one.put("outpatientProductItem", "-");
        }
    }

    /**
     * @Description: 新增挂号收费员、门诊收费员、随访人员
     * @param
     * @param emps -
     * @param info -
     * @param one -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 14:46
     */
    private void registeredOutpatientFollowUpMan(Map<String, Employee> emps,
                                                 OutpatientListInfo info, Map<String, Object> one) {
        // 新增挂号收费员、门诊收费员、随访人员
        String revisitId = info.getRevisitId();
        String registrationChargeBy = info.getRegistrationChargeBy();
        String outpatientChargeBy = info.getOutpatientChargeBy();
        List<String> revisitIds = new ArrayList<>();
        List<String> registrationChargeBys = new ArrayList<>();
        List<String> outpatientChargeBys = new ArrayList<>();
        if (revisitId != null) {
            revisitIds = Arrays.asList(revisitId.split(","));
        }
        if (registrationChargeBy != null) {
            registrationChargeBys = Arrays.asList(registrationChargeBy.split(","));
        }
        if (outpatientChargeBy != null) {
            outpatientChargeBys = Arrays.asList(outpatientChargeBy.split(","));
        }
        String revisitExecutor = "";
        String registrationChargeBy1 = "";
        String outpatientChargeBy1 = "";
        revisitExecutor = getString(emps, revisitIds, revisitExecutor);
        registrationChargeBy1 = getString(emps, registrationChargeBys, registrationChargeBy1);
        outpatientChargeBy1 = getString(emps, outpatientChargeBys, outpatientChargeBy1);
        dataProcessing(one, revisitExecutor, registrationChargeBy1, outpatientChargeBy1);
    }

    /**
     * @Description:
     * @param
     * @param emps -
     * @param revisitIds -
     * @param revisitExecutor -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/15 11:26
     */
    private String getString(Map<String, Employee> emps, List<String> ids, String resultStr) {
        if (ids == null || ids.isEmpty()) {
            return "";
        }
        Set<String> duplicate = new HashSet<>(ids);
        Iterator<String> iterator = duplicate.iterator();
        int i = 0;
        while (iterator.hasNext()) {
            i++;
            Employee revisit = emps.get(iterator.next());
            if (revisit != null) {
                if (duplicate.size() == i) {
                    resultStr += revisit.getName();
                } else {
                    resultStr += revisit.getName() + ",";
                }

            }
        }
        return resultStr;
    }

    /**
     * @Description:
     * @param
     * @param one -
     * @param revisitExecutor -
     * @param registrationChargeBy -
     * @param outpatientChargeBy -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 14:47
     */
    private void dataProcessing(Map<String, Object> one, String revisitExecutor,
                           String registrationChargeBy, String outpatientChargeBy) {
        if ("".equals(revisitExecutor)) {
            revisitExecutor = "-";
        }

        if ("".equals(registrationChargeBy)) {
            registrationChargeBy = "-";
        }

        if ("".equals(outpatientChargeBy)) {
            outpatientChargeBy = "-";
        }

        one.put("revisitExecutor", revisitExecutor);
        one.put("registrationChargeBy", registrationChargeBy);
        one.put("outpatientChargeBy", outpatientChargeBy);
    }

    /**
     * @Description: 处方单处理
     * @param
     * @param pfMap -
     * @param pfiMap -
     * @param info -
     * @param one -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 14:38
     */
    private void prescriptionField(OutpatientListInfo info, Map<String, Object> one, Map<String, Employee> emps) {
        Map<String, V2OutpatientPrescriptionForm> pfMap = new HashMap<>();
        List<V2OutpatientPrescriptionForm> glassesForms = new ArrayList<>();
        if (info != null && info.getPrescriptionFormInfos() != null && !info.getPrescriptionFormInfos().isEmpty()) {
            String prescriptionFormInfos = info.getPrescriptionFormInfos();
            List<V2OutpatientPrescriptionForm> prescriptionForms = JSON.parseArray(prescriptionFormInfos, V2OutpatientPrescriptionForm.class);
            for (V2OutpatientPrescriptionForm form:prescriptionForms) {
                pfMap.put(form.getId(), form);
                if (form.getType() != null && form.getType() == CommonConstants.NUMBER_FIVE) {
                    glassesForms.add(form);
                }
            }
        }
        //处方单
        if (info != null && info.getPrescriptionFormItemInfos() != null && !info.getPrescriptionFormItemInfos().isEmpty()) {
            List<V2OutpatientPrescriptionFormItem> pfis = JSON.parseArray(info.getPrescriptionFormItemInfos(), V2OutpatientPrescriptionFormItem.class);
            if (pfis != null && !pfis.isEmpty()) {
                //中药处方Map,处方单id为key
                Map<String, Map<String, Object>> cMap = new HashMap<>();
                //输液处方
                Map<String, Map<String, Object>> iMap = new HashMap<>();
                //外用处方
                Map<String, Map<String, Object>> eMap = new HashMap<>();
                //西药处方
                Map<String, Map<String, Object>> wMap = new HashMap<>();
                for (V2OutpatientPrescriptionFormItem pfi : pfis) {
                    V2OutpatientPrescriptionForm pf = pfMap.get(pfi.getPrescription_form_id());
                    if (pf == null) {
                        continue;
                    }
                    Byte type = pf.getType();
                    if (type != null) {
                        switch (type) {
                            case 1:
                                genPreForm(wMap, pfi, pf);
                                break;
                            case CommonConstants.NUMBER_TWO:
                                genPreForm(iMap, pfi, pf);
                                break;
                            case CommonConstants.NUMBER_THREE:
                                genPreForm(cMap, pfi, pf);
                                break;
                            case CommonConstants.NUMBER_FOUR:
                                genPreForm(eMap, pfi, pf);
                                break;
                            default:
                        }
                    }
                }
                one.put("prescriptionChineseForms", new ArrayList<>(cMap.values()));
                one.put("prescriptionExternalTreatForms", new ArrayList<>(eMap.values()));
                one.put("prescriptionInfusionForms", new ArrayList<>(iMap.values()));
                one.put("prescriptionWesternForms", new ArrayList<>(wMap.values()));
            }
            if (glassesForms.size() > 0) {
                //眼镜处方
                Map<String, Object> gMap = new HashMap<>();
                for (V2OutpatientPrescriptionForm glassesForm:glassesForms) {
                    // 眼镜处方的item为null
                    if (glassesForm != null) {
                        if (glassesForm.getType() == CommonConstants.NUMBER_FIVE) {
                            genGlassesForm(gMap, glassesForm, emps);
                        }
                    }
                }
                one.put("prescriptionGlassesForms", gMap);
            }
        }
        one.put("extendData", "");
        one.put("prescriptionFormItemInfos", "");
        one.put("prescriptionFormInfos", "");
    }

    /**
     * @Description: 眼科相关字段处理
     * @param
     * @param info -
     * @param one -
     * @return
     * @Author: zs
     * @Date: 2022/8/11 14:34
     */
    private void eyeField(OutpatientListInfo info, Map<String, Object> one) {
        //眼科检查相关字段
        one.put("wearGlassesHistory", info.getWearGlassesHistory() == null ? "-" : info.getWearGlassesHistory());
        //眼睛查体
        one.put("eyeExamination", info.getEyeExamination() == null ? "-" : getEyeInspection(info.getEyeExamination()));
        //月经史
        one.put("obstetricalHistory", info.getObstetricalHistory());
        //家族史
        one.put("familyHistory", info.getFamilyHistory() == null ? "-" : info.getFamilyHistory());
    }

    /**
     * @Description: json解析眼科检查
     * @param
     * @param eyeExamination -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/11 14:06
     */
    private String getEyeInspection(String eyeExamination) {
        StringBuffer result = new StringBuffer();
        if (eyeExamination == null) {
            return result.append("-").toString();
        }
        JSONObject jsonObject = JSON.parseObject(eyeExamination);
        if (jsonObject == null || jsonObject.get("items") == null) {
            return result.append("-").toString();
        }
        List<EyeExaminationItem> eyeExaminationItems = JSON.
                parseArray(JSON.toJSONString(jsonObject.get("items")), EyeExaminationItem.class);
        //循环拼接数据
        for (EyeExaminationItem x : eyeExaminationItems) {
            String name = x.getName();
            String rightEyeValue = x.getRightEyeValue();
            String leftEyeValue = x.getLeftEyeValue();
            if ((rightEyeValue == null || "".equals(rightEyeValue))
                    && (leftEyeValue == null || "".equals(leftEyeValue)) || name == null) {
                continue;
            }
            result.append(name).append(":").append(rightEyeValue == null || "".equals(rightEyeValue) ? "" : "右眼"
                    + rightEyeValue).append(rightEyeValue == null || "".equals(rightEyeValue) || leftEyeValue == null
                    || "".equals(leftEyeValue) ? "" : ";").append(leftEyeValue == null || "".equals(leftEyeValue)
                    ? "" : "左眼" + leftEyeValue).append("\n");
        }
        return result.toString();
    }


    /**
     * 处方单item拼接
     *
     * @param map Map结构，Map<String, Map<String, Object>,一级key，form_id，二级Map为此form_id的具体数据（频率，用法，处方item等）
     * @param pfi -
     * @param pf -
     */
    public void genPreForm(Map<String, Map<String, Object>> map, V2OutpatientPrescriptionFormItem pfi,
                           V2OutpatientPrescriptionForm pf) {
        Map<String, Object> one = map.get(pfi.getPrescription_form_id());
        if (one == null) {
            one = new HashMap<>();
            one.put("freq", pf.getFreq());
//            one.put("id", pf.getId());
            one.put("dailyDosage", pf.getDaily_dosage());
            one.put("doseCount", pf.getDose_count());
            one.put("usageLevel", pf.getUsage_level());
            if (pf.getType() == CommonConstants.NUMBER_THREE) {
                one.put("requirement", pf.getUsage());
            } else {
                one.put("requirement", pf.getRequirement());
            }
            // 此form下的formItem
            one.put("prescriptionFormItems", new ArrayList<>());
        }
        if (pf.getType() == CommonConstants.NUMBER_FOUR) {
            String extend_data = pfi.getExtend_data();
            if (extend_data != null && extend_data.contains("acupoints")) {
                JSONObject jsonObject = JSON.parseObject(extend_data);
                JSONArray acupoints = jsonObject.getJSONArray("acupoints");
                pfi.setAcupointsList(acupoints);
            }
            if (extend_data != null && extend_data.contains("externalGoodsItems")) {
                JSONObject jsonObject = JSON.parseObject(extend_data);
                JSONArray externalGoodsItems = jsonObject.getJSONArray("externalGoodsItems");
                pfi.setExternalGoodsItemsList(externalGoodsItems);
            }
        }
        pfi.setExtend_data(null);
        V2OutpatientPrescriptionFormItemDto itemDto = new V2OutpatientPrescriptionFormItemDto(pfi);
        ((List) one.get("prescriptionFormItems")).add(itemDto);
        map.put(pfi.getPrescription_form_id(), one);
    }


    /**
     * 眼镜处方单item拼接
     *
     * @param map Map结构，Map<String, Object>
     * @param pf 处方数据
     * @param emps -
     */
    public void genGlassesForm(Map<String, Object> map, V2OutpatientPrescriptionForm pf, Map<String, Employee> emps) {
        // 眼镜处方详情
        if (pf.getGlassesParams() != null) {
            List<Map> glassesParams= JSON.parseArray(pf.getGlassesParams(), Map.class);
            map.put("glassesParams", glassesParams);
            map.put("glassesType", pf.getGlassesType());
            if (pf.getGlassesType() != null && pf.getGlassesType() == 0) {
                //隐形眼镜不显示usage
                map.put("usage", pf.getUsage());
            } else {
                map.put("usage", "");
            }
        }
        // 配镜人
        if (pf.getOptometristId() != null) {
            Employee employee = emps.get(pf.getOptometristId());
            if (employee != null) {
                map.put("optometristName", employee.getName());
            } else {
                map.put("optometristName", "-");
            }
        } else {
            map.put("optometristName", "-");
        }
        // 备注
        if (pf.getRequirement() != null) {
            map.put("requirement", pf.getRequirement());
        } else {
            map.put("requirement", "-");
        }
    }

    /**
     * @Description:
     * @param
     * @param itemsList -
     * @return
     * @return java.util.Map<java.lang.String,java.util.List<cn.abc.flink.stat.
            dimension.domain.V2OutpatientPrescriptionFormItem>>
     * @Author: zs
     * @Date: 2022/8/11 20:06
     */
    public Map<String, List<V2OutpatientPrescriptionFormItem>> genFormitemMapByOutpatientSheetId(
            List<V2OutpatientPrescriptionFormItem> itemsList) {
        Map<String, List<V2OutpatientPrescriptionFormItem>> pfiMap = new HashMap<>();
        itemsList.forEach(x -> {
            List<V2OutpatientPrescriptionFormItem> list = pfiMap.
                    getOrDefault(x.getOutpatient_sheet_id(), new ArrayList<>());
            if (list.size() == 0) {
                pfiMap.put(x.getOutpatient_sheet_id(), list);
            }
            list.add(x);
        });
        return pfiMap;
    }

    /**
     * @Description:
     * @param
     * @param formList -
     * @param gMap 眼镜处方结果值
     * @return
     * @return java.util.Map<java.lang.String,cn.abc.flink.stat.dimension.domain.V2OutpatientPrescriptionForm>
     * @Author: zs
     * @Date: 2022/8/11 20:06
     */
    public Map<String, V2OutpatientPrescriptionForm> genFormMapByIds(List<V2OutpatientPrescriptionForm> formList,
                                                                     Map<String, List<V2OutpatientPrescriptionForm>> gMap) {
        Map<String, V2OutpatientPrescriptionForm> pfMap = new HashMap<>();
        for (V2OutpatientPrescriptionForm form:formList) {
            pfMap.put(form.getId(), form);
            if (form.getType() != null && form.getType() == CommonConstants.NUMBER_FIVE) {
                List<V2OutpatientPrescriptionForm> list = new ArrayList<>();
                if (gMap.containsKey(form.getOutpatient_sheet_id())) {
                    list = gMap.get(form.getOutpatient_sheet_id());
                }
                list.add(form);
                gMap.put(form.getOutpatient_sheet_id(), list);
            }
        }
        return pfMap;
    }

    /**
     * @param
     * @param jsonString -
     * @return
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 13:56
     */
    public Map<String, Object> getJsonMap(String jsonString) {
        if (!Strings.isNullOrEmpty(jsonString)) {
            Map map = null;
            try {
                map = JSON.parseObject(jsonString, Map.class);
            } catch (JSONException e) {
                logger.info(jsonString);
                try {
                    jsonString = jsonString.replace("[\"", "[\\\"").replace("\"]", "\\\"]");
                    jsonString = jsonString.replace("\"m\"", "\\\"m\\\"");
                    map = JSON.parseObject(jsonString, Map.class);
                } catch (JSONException b) {
                    logger.info(b.getMessage());
                }
            }
            return map;
        }
        return null;
    }

    /**
     * @param
     * @param type -
     * @param list -
     * @return
     * @return java.util.List<java.lang.String>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 09:53
     */
    public List<String> getIds(String type, List<OutpatientListInfo> list) {
        HashSet<String> ids = new HashSet<>();
        switch (type) {
            case "patient":
                list.forEach(x -> {
                    ids.add(x.getPatientId());
                    if (x.getReferrerId() != null) {
                        ids.add(x.getReferrerId());
                    }
                });
                break;
            case "outpatientSheet":
                list.forEach(x -> ids.add(x.getOutpatientSheetId()));
                break;
            case "department":
                list.forEach(x -> ids.add(x.getDepartmentId()));
                break;
            case "doctor":
                list.forEach(x -> ids.add(x.getDoctorId()));
                break;
            case "patientOrderId":
                list.forEach(x -> ids.add(x.getPatientOrderId()));
                break;
            default:
        }
        return new ArrayList<>(ids);
    }

    public List<Long> getLongIds(String type, List<OutpatientListInfo> list) {
        HashSet<Long> ids = new HashSet<>();
        switch (type) {
            case "snap":
                list.forEach(x -> {
                    ids.add(x.getOutpatientSheetDoctorSnapId());
                });
                break;
            default:
        }
        return new ArrayList<>(ids);
    }

    /**
     * @Description:
     * @param
     * @return
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
     * @Author: zs
     * @Date: 2022/8/11 20:07
     */
    public List<Map<String, String>> getPatientListHeader() {
        List<Map<String, String>> list = new ArrayList<>();
        list.add(genHeaderMap("患者姓名", "name"));
        list.add(genHeaderMap("性别", "sex"));
        list.add(genHeaderMap("年龄", "age"));
        list.add(genHeaderMap("手机号", "mobile"));
        list.add(genHeaderMap("就诊次数合计", "count"));
        list.add(genHeaderMap("就诊费用合计", "fee"));
        list.add(genHeaderMap("最近一次就诊", "lastCreated"));
        return list;
    }

    /**
     * @Description:
     * @param
     * @param employeeId -
     * @param tableKey -
     * @param chainViewMode -
     * @param excludeHidden -
     * @param clinicNodeType -
     * @return
     * @return java.util.List<cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem>
     * @Author: zs
     * @Date: 2022/8/11 20:07
     */
    public List<TableHeaderEmployeeItem> getOutPatientHeader(String employeeId,
                                                             String tableKey,
                                                             Integer chainViewMode,
                                                             Integer excludeHidden,
                                                             Integer clinicNodeType) {
        List<TableHeaderEmployeeItem> list = query.
                getTableHeaderEmployeeItems(employeeId, tableKey, chainViewMode, clinicNodeType, excludeHidden);
        return list;
    }


    /**
     * @Description:
     * @param
     * @param clinicId -
     * @param hisType -
     * @return
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
     * @Author: zs
     * @Date: 2022/8/11 20:07
     */
    public List<Map<String, String>> getOutpatientListHeader(String clinicId, String hisType) {
        boolean isChain = Strings.isNullOrEmpty(clinicId);
        List<Map<String, String>> list = new ArrayList<>();
        list.add(genHeaderMap("门诊日期", "created"));
        //添加诊号 by：libl
        list.add(genHeaderMap("诊号", "orderNo"));
        list.add(genHeaderMap("患者姓名", "patientName"));
        list.add(genHeaderMap("性别", "patientSex"));
        list.add(genHeaderMap("年龄", "patientAge"));
        list.add(genHeaderMap("身份证号", "idNumber"));
        list.add(genHeaderMap("挂号收费员", "registrationChargeBy"));
        list.add(genHeaderMap("门诊收费员", "outpatientChargeBy"));
        list.add(genHeaderMap("随访人", "revisitExecutor"));
        list.add(genHeaderMap("联系电话", "patientMobile"));
        list.add(genHeaderMap("住址", "address"));
        list.add(genHeaderMap("职业", "profession"));
        list.add(genHeaderMap("工作单位", "company"));
        if (!isChain) {
            list.add(genHeaderMap("类型", "type"));
        }
        list.add(genHeaderMap("初诊/复诊", "diagnoseStatus"));
        /*list.add(genHeaderMap("来源", "sourceFrom"));
        list.add(genHeaderMap("推荐人", "referrer"));*/
        list.add(genHeaderMap("患者来源", "patientSource"));
        if (isChain) {
            list.add(genHeaderMap("就诊门店", "clinicName"));
        }
        list.add(genHeaderMap("医生", "doctorName"));
        list.add(genHeaderMap("主诉", "chiefComplaint"));
        list.add(genHeaderMap("个人史", "personalHistory"));
        list.add(genHeaderMap("现病史", "presentHistory"));
        list.add(genHeaderMap("既往史", "pastHistory"));
        list.add(genHeaderMap("流行病史", "epidemiologicalHistory"));
        list.add(genHeaderMap("体格检查", "physicalExamination"));
        if ("0".equals(hisType)) {
            list.add(genHeaderMap("望闻切诊", "chineseExamination"));
        }
        list.add(genHeaderMap("口腔检查", "oralExaminations"));
        list.add(genHeaderMap("辅助检查", "auxiliaryExaminations"));
        list.add(genHeaderMap("诊断", "diagnosis"));
        if ("0".equals(hisType)) {
            list.add(genHeaderMap("辩证", "syndrome"));
            list.add(genHeaderMap("治法", "therapy"));
        }
        if ("1".equals(hisType)) {
            list.add(genHeaderMap("治疗计划", "treatmentPlans"));
            list.add(genHeaderMap("处置", "disposals"));
        }
        list.add(genHeaderMap("诊疗项目", "outpatientProductItem"));
        list.add(genHeaderMap("处方", "prescription"));
        list.add(genHeaderMap("医嘱", "doctorAdvice"));
        list.add(genHeaderMap("费用", "fee"));
        return list;
    }


    /**
     * @Description:
     * @param
     * @param key -
     * @param value -
     * @return
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @Author: zs
     * @Date: 2022/8/11 20:07
     */
    public Map<String, String> genHeaderMap(String key, String value) {
        Map<String, String> map = new HashMap<>();
        map.put("label", key);
        map.put("prop", value);
        return map;
    }

    /**
     * 导出时替换年龄名称字段数据
     *
     * @param map         -
     * @param ageKey      -
     * @param nameKey     -
     * @param isMemberKey -
     */
    public void replaceAgeAndName(Map<String, Object> map, String ageKey, String nameKey, String isMemberKey) {
        Object o = map.get(isMemberKey);
        Integer isM = 0;
        if (o != null) {
            isM = Integer.parseInt(o.toString());
        }
        //如果是会员，在名字后拼接 （会员）
        if (isM == 1) {
            map.put(nameKey, map.getOrDefault(nameKey, "-") + "(会员)");
        }
        Map age = (Map) map.get(ageKey);
        Integer year = null;
        Integer month = null;
        Integer day = null;
        if (age != null) {
            year = (Integer) age.get("year");
            month = (Integer) age.get("month");
            day = (Integer) age.get("day");
        }
        String rs = "";
        if (year != null && year != 0) {
            rs = rs + year + "岁";
        }
        if (month != null && month != 0) {
            rs = rs + month + "月";
        }
        //没有年月（0岁），才拼接天
        if ((month == null || month == 0) && (year == null || year == 0)) {
            rs = rs + day + "天";
        }
        map.put(ageKey, rs);
    }

    /**
     * 门诊清单导出 处方、口腔替换
     *
     * @param map -
     */
    public void replaceOutpatientListOther(Map<String, Object> map) {
        // 替换处方
        String prescription = "";
        //中药数据list
        List cList = (List) map.get("prescriptionChineseForms");
        //注射数据list
        List iList = (List) map.get("prescriptionInfusionForms");
        //西药数据list
        List wList = (List) map.get("prescriptionWesternForms");
        //外治处方list
        List eList = (List) map.get("prescriptionExternalTreatForms");
        //眼镜数据list
        Map<String, Object> gList = (Map<String, Object>) map.get("prescriptionGlassesForms");
        if ((cList == null || cList.size() == 0)
                && (iList == null || iList.size() == 0)
                && (wList == null || wList.size() == 0) && (gList == null || gList.size() == 0) && (eList == null || eList.size() == 0)) {
            prescription = "-";
        } else {
            //将list转换为string
            prescription = prescription + genPrescriptionString(wList, "prescriptionWesternForms");
            prescription = prescription + genPrescriptionString(iList, "prescriptionInfusionForms");
            prescription = prescription + genPrescriptionString(cList, "prescriptionChineseForms");
            prescription = prescription + genGlassesPrescriptionString(gList, "prescriptionGlassesForms");
            prescription = prescription + genPrescriptionExternalString(eList);
        }
        map.put("prescription", prescription);

        //替换口腔检查，将json字段拼接为字符串
        String oralExamination = (String) map.get("oralExaminations");
        try {
            String oral = oralJsonParseString(oralExamination);
            map.put("oralExaminations", oral);
        } catch (Exception e) {
        }
    }

    /**
     * 门诊清单导出 月经史逻辑替换替换
     *
     * @param map -
     */
    public void replaceObstetricalHistory(Map<String, Object> map) {
        String obstetricalHistory = (String) map.getOrDefault("obstetricalHistory", "");
        String join = "-";
        if (obstetricalHistory != null && !"".equals(obstetricalHistory)) {
            List<String> strings = null;
            List<String> result = new ArrayList<>();
            try {
                strings = JSON.parseArray(obstetricalHistory, String.class);
                for (String info:strings) {
                    String dto = "";
                    if (info.contains("type")) {
                        Map<String, Object> parse = JSON.parseObject(info, Map.class);
                        if ("pregnant".equals(parse.getOrDefault("type", ""))) {
                            //怀孕史
                            Object pregnantCount = parse.getOrDefault("pregnantCount", "");
                            Object birthCount = parse.getOrDefault("birthCount", "");
                            dto = "孕 " + pregnantCount + " 产 " + birthCount;
                        } else if ("menstruation".equals(parse.getOrDefault("type", ""))){
                            StringBuffer menstruation = new StringBuffer();
                            //月经史
                            Object menophaniaAge = parse.getOrDefault("menophaniaAge", "");
                            if (menophaniaAge != null && !"".equals(menophaniaAge)) {
                                menstruation.append("初潮年龄 " + menophaniaAge).append(";");
                            }
                            List<Integer> menstruationDays = (List<Integer>) parse.getOrDefault("menstruationDays", new ArrayList<Integer>());
                            if (menstruationDays != null && menstruationDays.size() > 0) {
                                menstruation.append(" 行径天数 ").append(menstruationDays.get(0));
                                if (menstruationDays.size() > 1) {
                                    menstruation.append("~").append(menstruationDays.get(1));
                                }
                                menstruation.append(";");
                            }
                            List<Integer> menstrualCycle = (List<Integer>) parse.getOrDefault("menstrualCycle", new ArrayList<Integer>());
                            if (menstrualCycle != null && menstrualCycle.size() > 0) {
                                menstruation.append(" 月经周期 ").append(menstrualCycle.get(0));
                                if (menstrualCycle.size() > 1) {
                                    menstruation.append("~").append(menstrualCycle.get(1));
                                }
                                menstruation.append(";");
                            }
                            Object menopauseDate = parse.getOrDefault("menopauseDate", "");
                            if (menopauseDate != null && !"".equals(menopauseDate)) {
                                menstruation.append(" 末次月经日期 " + menopauseDate);
                            }
                            Object menopauseAge = parse.getOrDefault("menopauseAge", "");
                            if (menopauseDate != null && !"".equals(menopauseAge)) {
                                menstruation.append(" 绝经年龄  " + menopauseAge);
                            }
                            dto = menstruation.toString();
                        }
                    } else {
                        dto = info;
                    }
                    if (!"".equals(dto)) {
                        result.add(dto);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (result != null && result.size() > 0) {
                join = String.join(",", result);
            }
            map.put("obstetricalHistory", join);
        }
    }

    /**
     * @Description: 眼镜处方配置
     * @param
     * @param gList -
     * @param prescriptionGlassesForms -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2023/9/13 11:17
     */
    private String genGlassesPrescriptionString(Map<String, Object> gList, String prescriptionGlassesForms) {
        if (gList == null) {
            return "";
        }
        StringBuilder rs = new StringBuilder();
        //控制显示那些字段
        Integer glassesType = (Integer)gList.get("glassesType");
        String optometristName = (String)gList.get("optometristName");
        String requirement = (String)gList.get("requirement");
        String usage = (String)gList.get("usage");
        List glassesParams = (List)gList.get("glassesParams");
        //定义一个隐形的key集合
        String stealth = "contactFocalLength,contactBozr,contactDiameter,contactLenticular,contactAxial";
        StringBuilder left = new StringBuilder().append("左眼：");
        StringBuilder right = new StringBuilder().append("右眼：");
        //循环眼镜表格数据
        for (Object o : glassesParams) {
            Map m = (Map) o;
            String name = (String)m.get("name");
            String leftEyeValue = (String)m.get("leftEyeValue");
            String rightEyeValue = (String)m.get("rightEyeValue");
            String key = (String)m.get("key");
            //眼镜配镜处方类型 0框架 1隐形
            if (glassesType != null && glassesType == 1) {
                if (!stealth.contains(key)) {
                    continue;
                }
            } else {
                if (stealth.contains(key)) {
                    continue;
                }
            }
            String s1 = UNIT.getOrDefault(key, "");
            if (leftEyeValue != null && !"".equals(leftEyeValue)) {
                left.append(name).append(leftEyeValue).append(s1).append("，");
            }
            if (rightEyeValue != null && !"".equals(rightEyeValue)) {
                right.append(name).append(rightEyeValue).append(s1).append("，");
            }
        }
        if (right.length() > 0) {
            right.deleteCharAt(right.length() - 1);
        }
        if (left.length() > 0) {
            left.deleteCharAt(left.length() - 1);
        }
        rs.append(right).append(";").append(left);
        if (usage != null) {
            rs.append(" 用法：").append(usage);
        } else {
            rs.append(" 用法：-");
        }
        if (requirement != null) {
            rs.append(" 备注：").append(requirement);
        } else {
            rs.append(" 备注：-");
        }
        if (optometristName != null) {
            rs.append(" 验光师：").append(optometristName);
        } else {
            rs.append(" 验光师：-");
        }
        return rs.toString();
    }

    /**
     * 口腔检查，json转换为string
     *
     * @param oralExamination -
     * @return -
     */
    private String oralJsonParseString(String oralExamination) {
        StringBuilder rs = new StringBuilder();
        if (oralExamination != null) {
            JSONArray orals = JSON.parseArray(oralExamination);
            if (orals != null) {
                for (int i = 0; i < orals.size(); i++) {
                    String oneString = orals.getString(i);
                    OutpatientOralExamination one = JSONObject.parseObject(oneString, OutpatientOralExamination.class);
                    if (one.getPositions() != null) {
                        one.getPositions().forEach(pos -> pos.getDataNo().forEach(no -> {
                            rs.append(" ").append("#").append(getPositionNum(pos.getPosition().get(0))).append(no);
                        }));
                    }
                    if (one.getDescribes() != null) {
                        rs.append(" ").append(Strings.join(one.getDescribes(), " "));
                    }
                }
                rs.append(",");
            }
        }
        //除去最后一个逗号
        return rs.length() > 0 ? rs.substring(0, rs.length() - 1) : rs.toString();
    }

    /**
     * @Description:
     * @param
     * @param s -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/11 20:08
     */
    private String getPositionNum(String s) {
        String result = "";
        if (s != null) {
            switch (s) {
                case "top-left":
                    result = "1";
                    break;
                case "top-right":
                    result = "2";
                    break;
                case "bottom-right":
                    result = "3";
                    break;
                case "bottom-left":
                    result = "4";
                    break;
                default:
            }
        }
        return result;
    }

    /**
     * 将list数据转换为string
     *
     * @param list  处方list
     * @param pType 处方item类型，prescriptionChineseForms，prescriptionWesternForms，prescriptionInfusionForms
     * @return -
     */
    private String genPrescriptionString(List list, String pType) {
        if (list == null) {
            return "";
        }
        StringBuilder rs = new StringBuilder();
        for (Object o : list) {
            Map m = (Map) o;
            //获取该prescriptionForm下的prescriptionFormItems
            List items = (List) m.get("prescriptionFormItems");
            String dailyDosage = (String) m.get("dailyDosage"); // by:libl 解决导出没有用法用量
            String freq = (String) m.get("freq"); // by:libl 解决导出没有用法用量
            Integer doseCount = (Integer) m.get("doseCount"); // by:libl 解决导出没有用法用量
            String requirement = (String) m.get("requirement"); // 解决导出没有服法
            String usageLevel = (String) m.get("usageLevel");
            if (usageLevel == null) {
                usageLevel = "";
            }
            //如果是中药处方
            if ("prescriptionChineseForms".equals(pType)) {
                for (Object x : items) {
                    V2OutpatientPrescriptionFormItemDto i = (V2OutpatientPrescriptionFormItemDto) x;
                    String sb = i.getName() + " " + i.getUnit_count().stripTrailingZeros().toPlainString()
                            + (i.getUnit() == null ? "g" : i.getUnit())
                            + (i.getSpecial_requirement() == null ? "" : i.getSpecial_requirement())
                            + "   ";
                    rs.append(sb);
                }
                rs.append("【用法用量】共" + doseCount + "剂，" + requirement  + freq + dailyDosage + " " + usageLevel); // by:libl 解决导出没有用法用量
                rs.append("\r\n");
            } else {
                //如果是西药或者注射处方
                for (Object x : items) {
                    V2OutpatientPrescriptionFormItemDto i = (V2OutpatientPrescriptionFormItemDto) x;
                    String sb = i.getName() + " " + i.getUsage()
                            + (i.getAst() != null && i.getAst() == 1 ? "(皮试)" : "") + ","
                            + (query.queryPrescriptionFormFreq(i.getFreq()) == null ? ""
                            : query.queryPrescriptionFormFreq(i.getFreq()) + ",")
                            + "每次"
                            + i.getDosage()
                            + i.getDosage_unit()
                            + " "
                            + i.getUnit_count().stripTrailingZeros().toPlainString()
                            + i.getUnit()
                            + "\r\n";
                    rs.append(sb);
                }
            }
        }
        return rs.toString();
    }

    /**
     * 外治处方转string
     * 导出显示 针灸 1次；服帖 1伏；【右】中府；【左】天灵盖
     * 项目名 数量单位 在一起 穴位在一起
     */
    private String genPrescriptionExternalString(List list) {
        if (list == null) {
            return "";
        }
        StringBuilder project = new StringBuilder();
        StringBuilder drugs = new StringBuilder();
        StringBuilder acupoint = new StringBuilder();
        StringBuilder reslut = new StringBuilder();
        //
        for (Object o : list) {
            Map m = (Map) o;
            //获取该prescriptionForm下的prescriptionFormItems
            List items = (List) m.get("prescriptionFormItems");
            for (Object x : items) {
                V2OutpatientPrescriptionFormItemDto i = (V2OutpatientPrescriptionFormItemDto) x;
                String sb = i.getName() + " " + i.getUnit_count().stripTrailingZeros().toPlainString()
                        + (i.getUnit() == null ? "" : i.getUnit())
                        + "; ";
                project.append(sb);
                List<Object> acupointsList = i.getAcupointsList();
                if (acupointsList != null && acupointsList.size() > 0) {
                    for (Object acupoints:acupointsList) {
                        JSONObject jsonAcupoints = (JSONObject) acupoints;
                        String name = jsonAcupoints.getString("name");
                        String position = jsonAcupoints.getString("position");
                        acupoint.append("[").append(position).append("]").append(name).append(";");
                    }
                }
                List<Object> externalGoodsItemsList = i.getExternalGoodsItemsList();
                if (externalGoodsItemsList != null && externalGoodsItemsList.size() > 0) {
                    for (Object externalGoodsItems:externalGoodsItemsList) {
                        JSONObject jsonAcupoints = (JSONObject) externalGoodsItems;
                        String name = jsonAcupoints.getString("name");
                        Integer unitCount = jsonAcupoints.getInteger("unitCount");
                        String unit = jsonAcupoints.getString("unit");
                        drugs.append(name).append(" ").append(unitCount).append(unit).append(";");
                    }
                }
                reslut.append(project);
                if (drugs.length() > 0) {
                    reslut.append(drugs);
                    drugs = new StringBuilder();
                }
                reslut.append(acupoint);
                project = new StringBuilder();
                acupoint = new StringBuilder();
            }
        }
        return reslut.toString();
    }

    /**
     * 导出时替换BigDecimal为Double（保证导出时表格格式为数字，而非字符）
     *
     * @param map    -
     * @param feeKey -
     */
    public void replaceFee(Map<String, Object> map, String feeKey) {
        if (map != null && feeKey != null) {
            String b = map.get(feeKey).toString();
            if (b != null) {
                Double d = Double.valueOf(b.equals("0.00") ? "0" : b);
                map.put(feeKey, d);
            }
        }
    }

    /**
     * @param
     * @param epidemiologicalHistory -
     * @return
     * @return java.lang.String
     * @Description: 流行病史json串拼接
     * @Author: zs
     * @Date: 2022/8/11 13:48
     */
    public String getEpidemiologicalHistoryStr(String epidemiologicalHistory) {
        JSONObject jsonObject = null;
        JSONArray symptomList = null;
        JSONArray suspiciousList = null;
        try {
            jsonObject = JSON.parseObject(epidemiologicalHistory);
            symptomList = jsonObject.getJSONArray("symptomList");
            suspiciousList = jsonObject.getJSONArray("suspiciousList");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("门诊日志json解析失败getEpidemiologicalHistoryStr方法jsonObject解析epidemiologicalHistory字段");
            try {
                symptomList = JSON.parseArray(epidemiologicalHistory);
            } catch (Exception f) {
                f.printStackTrace();
                logger.info("门诊日志json解析失败getEpidemiologicalHistoryStr方法JSONArray解析epidemiologicalHistory字段");
                symptomList = new JSONArray();
                f.printStackTrace();
            }

        }
        String str = "";
        Boolean hasActive = false;
        for (int i = 0; i < symptomList.size(); i++) {
            JSONObject item = symptomList.getJSONObject(i);
            boolean isSuspicious;
            try {
                //没有isSuspicious 说明没有suspiciousList数据
                isSuspicious = item.getBoolean("isSuspicious");

            } catch (NullPointerException e) {
                isSuspicious = false;
            }
            if (!isSuspicious) {
                str += item.getString("label");
            }
            if (isSuspicious) {
                if (suspiciousList != null && suspiciousList.size() != 0) {
                    str += item.getString("label");
                    for (int j = 0; j < suspiciousList.size(); j++) {
                        JSONObject jo1 = suspiciousList.getJSONObject(j);
                        if ("有".equals(jo1.getString("value"))) {
                            hasActive = true;
                        }

                        str = getString(suspiciousList, str, j, jo1);
                    }
                    if (hasActive) {
                        str += "，需密切关注和引导新冠肺炎排查";
                    }
                    str += "。";
                } else {
                    str = getStr(str, item);
                }
            } else {
                str = getString(str, item);
            }
        }
        return str;
    }

    /**
     * @param
     * @param suspiciousList -
     * @param str            -
     * @param j              -
     * @param jo1            -
     * @return
     * @return java.lang.String
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 13:53
     */
    private String getString(JSONArray suspiciousList, String str, int j, JSONObject jo1) {
        if (j == suspiciousList.size() - 1) {
            str += jo1.getString("label") + "（" + jo1.getString("value") + "）";
        } else {
            str += jo1.getString("label") + "（" + jo1.getString("value") + "）,";
        }
        return str;
    }

    /**
     * @param
     * @param str  -
     * @param item -
     * @return
     * @return java.lang.String
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 13:51
     */
    private String getStr(String str, JSONObject item) {
        String label = item.getString("label");
        if (label != null && !"".equals(label)) {
            str += label + "：";
        } else {
            str += "";
        }
        return str;
    }

    /**
     * @param
     * @param str  -
     * @param item -
     * @return
     * @return java.lang.String
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 13:49
     */
    private String getString(String str, JSONObject item) {
        // 兼容老数据
        String value = item.getString("value");
        boolean isNumber = false;
        if (value != null) {
            isNumber = Pattern.matches("[0-9]", value);
        }
        if (isNumber && Integer.parseInt(value) == 1) {
            str += "（是）；";
        } else if (isNumber && Integer.parseInt(value) == 0) {
            str += "（否）；";
        } else if (value != null && !"".equals(value)) {
            str += "（" + item.getString("value") + "）";
        }

        if (!"".equals(str)) {
            str += Pattern.matches(".*[;；]$", str) ? "" : "；";
        }
        return str;
    }

    /**
     * @param
     * @param jsonArray   -
     * @param isDiagnosis -
     * @return
     * @return java.lang.String
     * @Description: 口腔
     * @Author: zs
     * @Date: 2022/8/11 13:56
     */
    public String handleJsonArr(JSONArray jsonArray, boolean isDiagnosis) {
        StringBuffer sb = new StringBuffer();

        if (isDiagnosis) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jo = (JSONObject) jsonArray.get(i);
                JSONArray valueArrays = jo.getJSONArray("value");
                List<Integer> toothNos = JSONObject.parseArray(jo.getString("toothNos"), Integer.class);
                String strTooth = handleToothNo(toothNos);
                StringBuffer sbName = new StringBuffer();
                if (valueArrays != null) {
                    for (int j = 0; j < valueArrays.size(); j++) {
                        JSONObject jv = (JSONObject) valueArrays.get(j);
                        if (valueArrays.size() - 1 == j) {
                            sbName.append(jv.getString("name"));
                        } else {
                            sbName.append(jv.getString("name") + "，");
                        }
                    }
                }
                String strName = sbName.toString();
                if (jsonArray.size() - 1 == i) {
                    sb.append(strTooth + " " + strName);
                } else {
                    sb.append(strTooth + " " + strName + "，");
                }
            }
        } else {
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jo = (JSONObject) jsonArray.get(i);
                    String value = jo.getString("value");
                    List<Integer> toothNos = JSONObject.parseArray(jo.getString("toothNos"), Integer.class);
                    String strTooth = handleToothNo(toothNos);
                    if (jsonArray.size() - 1 == i) {
                        sb.append(strTooth + " " + value);
                    } else {
                        sb.append(strTooth + " " + value + "，");
                    }
                }
            }
        }
        return sb.toString();
    }
    // 对toothNos处理，若牙位是包含上半口、下半口、全口则返回'上半口'、'下半口'，'全口' 否则以 、分隔返回牙位
    /**
     * @Description:
     * @param
     * @param toothList -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/11 20:12
     */

    public static String handleToothNo(List<Integer> toothList) {
        if (toothList == null || toothList.isEmpty()) {
            return "";
        }

        // 按象限分组
        List<Integer> topRight = new ArrayList<>();    // 1X
        List<Integer> topLeft = new ArrayList<>();     // 2X
        List<Integer> bottomLeft = new ArrayList<>();  // 3X
        List<Integer> bottomRight = new ArrayList<>(); // 4X

        for (Integer tooth : toothList) {
            if (tooth == null || tooth < 11 || tooth > 48) {
                continue; // 跳过非法编号
            }

            int quadrant = tooth / 10; // 十位数表示象限
            int toothNum = tooth % 10; // 个位数表示牙位

            switch (quadrant) {
                case 1:
                    topRight.add(toothNum);
                    break;
                case 2:
                    topLeft.add(toothNum);
                    break;
                case 3:
                    bottomLeft.add(toothNum);
                    break;
                case 4:
                    bottomRight.add(toothNum);
                    break;
                default:
                    // 非法象限，跳过
                    break;
            }
        }

        // 检查是否为全口/半口
        boolean isTopAll = isQuadrantFull(topRight) && isQuadrantFull(topLeft);
        boolean isBottomAll = isQuadrantFull(bottomLeft) && isQuadrantFull(bottomRight);
        boolean isAll = isTopAll && isBottomAll;

        if (isAll) {
            return "全口";
        } else if (isTopAll) {
            return "上半口";
        } else if (isBottomAll) {
            return "下半口";
        } else {
            // 按象限分组排序后拼接
            return formatToothString(topRight, topLeft, bottomLeft, bottomRight);
        }
    }

    // 检查一个象限是否完整（恒牙：1-8，乳牙：1-5）
    private static boolean isQuadrantFull(List<Integer> teeth) {
        if (teeth.size() != 8) { // 恒牙象限应含8颗牙（含智齿）
            return false;
        }
        Collections.sort(teeth);
        return teeth.equals(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8)); // 检查是否覆盖1-8
    }

    // 格式化输出非全口/半口的情况
    private static String formatToothString(List<Integer>... quadrants) {
        StringBuilder sb = new StringBuilder();
        String[] prefixes = {"1", "2", "3", "4"};

        for (int i =  0; i < quadrants.length; i++) {
            if (!quadrants[i].isEmpty()) {
                Collections.sort(quadrants[i]);
                for (int num : quadrants[i]) {
                    sb.append(prefixes[i]).append(num).append("、");
                }
            }
        }

        return sb.length() > 0 ? sb.substring(0, sb.length() - 1) : "";
    }

    //list 按照指定分隔符拼接
    /**
     * @Description:
     * @para -
     * @param collection -
     * @param separator -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/11 20:13
     */
    public String join(Collection collection, String separator) {
        if (collection == null) {
            return null;
        } else if (collection.size() == 0) {
            return "";
        } else {
            StringBuilder sb = new StringBuilder(collection.size() * CommonConstants.NUMBER_SIXTEEN);
            Iterator it = collection.iterator();

            for (int i = 0; i < collection.size(); ++i) {
                if (i > 0) {
                    sb.append(separator);
                }
                sb.append(it.next());
            }
            return sb.toString();
        }
    }

    /**
     * @param
     * @param list -
     * @return
     * @return java.lang.String
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 13:58
     */
    public List<Map<String, Object>> handleProductItem(List<OutpatientProductFormItem> list) {
        List<Map<String, Object>> itemList = new ArrayList<>();
        if (list != null) {
            for (OutpatientProductFormItem item : list) {
                Map<String, Object> itemMap = new HashMap<>();
                List<Integer> toothList = null;
                String remark = null;
                try {
                    toothList = JSONObject.parseArray(item.getTooth_nos(), Integer.class);
                    JSONObject extendDataObj = JSONObject.parseObject(item.getExtend_data());
                    remark = extendDataObj.getString("remark");
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("门诊日志json解析失败字段为handleProductItem方法tooth_nos or extend_data字段");
                }
                String toothStr = "";
                if (toothList != null && !toothList.isEmpty()) {
                    toothStr = handleToothNo(toothList);
                }
                itemMap.put("tooth", toothStr);
                itemMap.put("name", item.getName());
                itemMap.put("unitCount", (int) Double.parseDouble(item.getUnit_count()));
                itemMap.put("unit", item.getUnit());
                itemMap.put("remark", remark);
                itemList.add(itemMap);
            }
        }
        return itemList;
    }

    /**
     * @Description:
     * @param
     * @param level1 -
     * @param level2 -
     * @param sources -
     * @param emps -
     * @param patients -
     * @return
     * @return java.lang.String
     * @Author: zs
     * @Date: 2022/8/11 20:14
     */
    public String handlePatientSource(String level1, String level2, Map<String, V2PatientSourceType> sources,
                                      Map<String, Employee> emps, Map<String, V2Patient> patients) {
        String str = "";
        //来源
        V2PatientSourceType source = sources.get(level1);
        if (source == null) {
            str = "未指定";
        } else {
            if (source.getName().equals("顾客推荐")) {
                V2Patient p = patients.get(level2);
                if (p != null) {
                    str = source.getName() + "-" + p.getName();
                } else {
                    str = source.getName();
                }
            } else if ("医生推荐".equals(source.getName()) || "导医推荐".equals(source.getName())) {
                // 推荐ren
                Employee employee = emps.get(level2);
                if (employee != null) {
                    str = source.getName() + "-" + employee.getName();
                } else {
                    str = source.getName();
                }
            } else {
                if (level2 != null && !"".equals(level2)) {
                    V2PatientSourceType source2 = sources.get(level2);
                    if (source2 != null) {
                        str = source.getName() + "-" + source2.getName();
                    } else {
                        str = source.getName();
                    }
                } else {
                    str = source.getName();
                }
            }
        }
        return str;
    }

    /**
     * 获取就诊来源
     *
     * @param patientSourceTypeMap 就诊来源map
     * @param employeeMap          人员map
     * @param info                  -
     * @param patientMap           患者map 就诊来源1级id,就诊来源2级id
     */
    public String handleVisitSourceNameById(OutpatientListInfo info,
                                          Map<String, V2PatientSourceType> patientSourceTypeMap,
                                          Map<String, Employee> employeeMap,
                                          Map<String, V2Patient> patientMap) {
        String visitSourceType1 = AbcDefaultValueUtils.DEFAULT_TEXT;
        String visitSourceType2 = AbcDefaultValueUtils.DEFAULT_TEXT;
        String visitSourceFrom = AbcDefaultValueUtils.DEFAULT_TEXT;
        String visitSourceType = AbcDefaultValueUtils.DEFAULT_TEXT;
        if (info.getVisitSourceOne() == null || "".equals(info.getVisitSourceTwo())) {
            return visitSourceType;
        }

        V2PatientSourceType st = patientSourceTypeMap.get(info.getVisitSourceOne());
        if (st == null) {
            visitSourceType1 = AbcDefaultValueUtils.DEFAULT_TEXT;
        } else {
            visitSourceType1 = st.getName();
        }
        V2PatientSourceType sourceType2 = patientSourceTypeMap.get(info.getVisitSourceTwo());
        if (sourceType2 != null) {
            visitSourceType2 = sourceType2.getName();
        } else {
            visitSourceType2 = AbcDefaultValueUtils.DEFAULT_TEXT;
        }
        if (info.getVisitSourceFromType() != null) {
            if (info.getVisitSourceFromType() == 1) {
                if (employeeMap.get(info.getVisitSourceFrom()) == null) {
                    visitSourceFrom = AbcDefaultValueUtils.DEFAULT_TEXT;
                } else {
                    visitSourceFrom = employeeMap.get(info.getVisitSourceFrom()).getName();
                }
            } else {
                if (patientMap.get(info.getVisitSourceFrom()) == null) {
                    visitSourceFrom = AbcDefaultValueUtils.DEFAULT_TEXT;
                } else {
                    visitSourceFrom = patientMap.get(info.getVisitSourceFrom()).getName();
                }
            }
        } else {
            if ("医生推荐".equals(visitSourceType1) || "导医推荐".equals(visitSourceType1) || "转诊医生".equals(visitSourceType1)) {
                Employee e = employeeMap.get(info.getVisitSourceFrom());
                if (e == null) {
                    visitSourceFrom = AbcDefaultValueUtils.DEFAULT_TEXT;
                } else {
                    visitSourceFrom = e.getName() == null
                            ? AbcDefaultValueUtils.DEFAULT_TEXT : e.getName();
                }
            } else if ("顾客推荐".equals(visitSourceType1)) {
                V2Patient p = patientMap.get(info.getVisitSourceTwo());
                if (p == null) {
                    visitSourceFrom = AbcDefaultValueUtils.DEFAULT_TEXT;
                } else {
                    visitSourceFrom = p.getName() == null
                            ? AbcDefaultValueUtils.DEFAULT_TEXT : p.getName();
                }
            } else {
                visitSourceFrom = AbcDefaultValueUtils.DEFAULT_TEXT;
            }
        }
        if (info.getVisitSourceOne() == null) {
            visitSourceType = "-";
        } else {
            visitSourceType = visitSourceType1 + (visitSourceType2 == null
                    | "-".equals(visitSourceType2) ? "" : ("-" + visitSourceType2))
                    + (visitSourceFrom == null
                    | "-".equals(visitSourceFrom) ? "" : ("-" + visitSourceFrom));
            if (visitSourceType == null) {
                visitSourceType = "-";
            }
        }
        return visitSourceType;
    }

    /**
     * @Description:
     * @param
     * @param list 目标集合
     * @param listSize 指定分割集合大小
     * @return
     * @return java.util.List<java.util.List<java.lang.String>>
     * @Author: zs
     * @Date: 2023/6/7 10:30
     */
    public List<List<String>> splitList(List<String> list, int listSize) {
        if (null == list || 0 == listSize) {
            return new ArrayList();
        }
        int length = list.size();
        int num = (length + listSize - 1) / listSize;
        List<List<String>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            int fromIndex = i * listSize;
            int toIndex = (i + 1) * listSize < length ? (i + 1) * listSize : length;
            newList.add(list.subList(fromIndex, toIndex));
        }
        return newList;
    }

    /**
     * 门诊日志导出处理诊疗项目字段
     * @param map -
     */
    public void replaceOutpatientProductItem(Map<String, Object> map) {
        Object productItemObj = map.get("outpatientProductItem");
        if (productItemObj == null || "-".equals(productItemObj.toString())) {
            return;
        }
        List<Map<String, Object>> productItemList =  (List<Map<String, Object>>) productItemObj;
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < productItemList.size(); i++) {
            Map<String, Object> itemMap = productItemList.get(i);
            sb.append(itemMap.get("tooth")).append(" ")
                    .append(itemMap.get("name")).append(" ")
                    .append(itemMap.get("unitCount"))
                    .append("*")
                    .append(itemMap.get("unit"));
            if (itemMap.get("remark") != null && !itemMap.get("remark").toString().isEmpty()) {
                sb.append("[").append(itemMap.get("remark")).append("]");
            }
            if (i != productItemList.size() - 1) {
                sb.append("，");
            }
        }
        if (!sb.toString().isEmpty()) {
            map.put("outpatientProductItem", sb.toString());
        }
    }
}

