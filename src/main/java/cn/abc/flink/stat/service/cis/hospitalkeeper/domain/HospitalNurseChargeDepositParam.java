package cn.abc.flink.stat.service.cis.hospitalkeeper.domain;

import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;


/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.hospitalkeeper.domain
 * @Author: zs
 * @CreateTime: 2023-02-03  09:43
 * @Description: 押金管理参数类
 * @Version: 1.0
 */
public class HospitalNurseChargeDepositParam extends AbcScStatRequestParams {

    /**
     * 病区
     */
    private String wardId;

    //概况参数
    /**
     * 费别
     */
    private String feeType;
    /**
     * 筛选输入框：患者/床位/住院号
     */
    private String keyword;
    private String patientId;

    //明细的参数
    /**
     * 类型
     */
    private Integer type;
    /**
     * 收据
     */
    private String receipt;
    /**
     * 条数
     */
    private Integer limit;
    /**
     * 偏移量
     */
    private Integer offset;

    public String getWardId() {
        return this.wardId;
    }

    public String getFeeType() {
        return this.feeType;
    }

    public String getKeyword() {
        return this.keyword;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public Integer getType() {
        return this.type;
    }

    public String getReceipt() {
        return this.receipt;
    }

    public Integer getLimit() {
        return this.limit;
    }

    public Integer getOffset() {
        return this.offset;
    }


    public void setWardId(String wardId) {
        this.wardId = wardId;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

}
