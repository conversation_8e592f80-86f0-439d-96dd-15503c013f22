package cn.abc.flink.stat.service.cis.registration.domain;



import java.math.BigDecimal;

/**
 * RegistrationDetailTotal
 */
public class RegistrationDetailTotal {

    private Long count;

    private BigDecimal received;
    private String receivedText;

    /**
     *
     * @return -
     */
    public RegistrationDetailTotal pretty() {
        return this;
    }

    public Long getCount() {
        return this.count;
    }

    public BigDecimal getReceived() {
        return this.received;
    }

    public String getReceivedText() {
        return this.receivedText;
    }


    public void setCount(Long count) {
        this.count = count;
    }

    public void setReceived(BigDecimal received) {
        this.received = received;
    }

    public void setReceivedText(String receivedText) {
        this.receivedText = receivedText;
    }

}
