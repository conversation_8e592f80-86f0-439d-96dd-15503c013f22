package cn.abc.flink.stat.service.cis.revenue.charge.detail.handler;

import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.service.cis.revenue.overview.pojo.RevenueOperationSelectResp;

import java.math.BigDecimal;

/**
 * @description:
 * @author: dy
 * @create: 2021-09-18 11:01
 */
public class CalculateHandler {

    public static final int ARREARS = 20;
    public static final int REFUND_TYPE = -1;

    protected CalculateHandler() {
    }

    /**
     * @param organalDiscountPrice -
     * @param adjustmentPrice      -
     * @param deductPrice          -
     * @return -
     */
    public static BigDecimal calcDiscountPrice(BigDecimal organalDiscountPrice, BigDecimal adjustmentPrice,
                                               BigDecimal deductPrice) {
        BigDecimal discountPrice = calcDiscountPrice(organalDiscountPrice, deductPrice);
        if (adjustmentPrice != null && adjustmentPrice.compareTo(BigDecimal.ZERO) < 0) {
            discountPrice = discountPrice.subtract(adjustmentPrice);
        }
        return discountPrice;
    }

    /**
     * @param organalDiscountPrice -
     * @param deductPrice          -
     * @return -
     */
    public static BigDecimal calcDiscountPrice(BigDecimal organalDiscountPrice, BigDecimal deductPrice) {
        BigDecimal discountPrice = organalDiscountPrice;
        if (discountPrice == null) {
            discountPrice = BigDecimal.ZERO;
        }
        if (deductPrice != null) {
            discountPrice = discountPrice.subtract(deductPrice);
        }
        return discountPrice;
    }

    /**
     * @param type        收费类型 -1：退费 不等于-1：收费
     * @param recordSourceType   场景类型：0：普通收退费，1：欠，2：还
     * @return 费用类型
     */
    public static String calcAction(Integer type, Integer recordSourceType) {
        String result = "-";
        if (recordSourceType != null) {
            if (recordSourceType == 0 && (type == 0 || type == 1)) {
                result = "收费";
            } else if (recordSourceType == 0 && type == -1) {
                result = "退费";
            } else if (recordSourceType == 1 && (type == 0 || type == 1)) {
                result = "欠费";
            } else if (recordSourceType == 1 && type == -1) {
                result = "退欠费";
            } else if (recordSourceType == CommonConstants.NUMBER_TWO) {
                result = "还款";
            }
        }
        return result;
    }
}
