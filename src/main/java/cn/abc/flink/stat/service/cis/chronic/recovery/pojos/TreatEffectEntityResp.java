package cn.abc.flink.stat.service.cis.chronic.recovery.pojos;

import cn.abc.flink.stat.service.cis.chronic.recovery.entity.TreatEffectEntity;

import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
public class TreatEffectEntityResp extends TreatEffectEntity {
    private Collection<IndictorKeyEarliestLastest> Keys;

    public Collection<IndictorKeyEarliestLastest> getKeys() {
        return this.Keys;
    }


    public void setKeys(Collection<IndictorKeyEarliestLastest> Keys) {
        this.Keys = Keys;
    }

}
