package cn.abc.flink.stat.service.cis.goods.inventory.domain;

import cn.abc.flink.stat.common.*;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.dimension.domain.*;
import cn.abc.flink.stat.service.cis.handler.GoodsHandler;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@ApiModel("进销存明细实体")
@Component
public class InventoryRecord {

    @ApiModelProperty("创建时间")
    private String createDate;

    @ApiModelProperty("门店id")
    private String organId;

    @ApiModelProperty("门店名称")
    private String organText;

    @ApiModelProperty("动作")
    private String action;

    @ApiModelProperty("药品id")
    private String goodsId;

    @ApiModelProperty("药品名称")
    private String goodsText;

    @ApiModelProperty("药品编码id")
    private String goodsShortId;

    @ApiModelProperty("药品一级分类id")
    private String feeType1;

    @ApiModelProperty("药品一级分类名称")
    private String feeType1Text;

    @ApiModelProperty("药品二级分类id")
    private Integer feeType2;

    @ApiModelProperty("药品二级分类名称")
    private String feeType2Text;

    @ApiModelProperty("库房id")
    private Integer pharmacyNo;

    @ApiModelProperty("药品规格")
    private String specification;

    @ApiModelProperty("药品小单位")
    private String pieceUnit;

    @ApiModelProperty("药品大单位")
    private String packageUnit;

    @ApiModelProperty("药品小单位价格")
    private BigDecimal piecePrice;

    @ApiModelProperty("药品大单位价格")
    private BigDecimal packagePrice;

    @ApiModelProperty("供应商id")
    private String supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierText;

    @ApiModelProperty("生产批号")
    private String batchNo;

    @ApiModelProperty("效期")
    private String expiryDate;

    @ApiModelProperty("厂家")
    private String manufacturer;

    @ApiModelProperty("国药准字")
    private String nmpn;

    @ApiModelProperty("生产日期")
    private String productionDate;

    @ApiModelProperty("药品进价")
    private BigDecimal inPrice;

    @ApiModelProperty("药品进价字符串")
    private String inPriceText;

    @ApiModelProperty("药品售价")
    private BigDecimal outPrice;

    @ApiModelProperty("药品售价字符串")
    private String outPriceText;

    @ApiModelProperty("药品最近进价")
    private BigDecimal lastPackageCostPrice;

    @ApiModelProperty("药品最近供应商")
    private String lastStockInOrderSupplier;

    @ApiModelProperty("药品社保码")
    private String sheBaoCodeNationalCode;

    @ApiModelProperty("进项税")
    private BigDecimal inTaxRat;

    @ApiModelProperty("销项税")
    private BigDecimal outTaxRat;

    @ApiModelProperty("进销税字符串")
    private String inTaxRatText;

    @ApiModelProperty("销项税字符串")
    private String outTaxRatText;


    @ApiModelProperty("药品变更前原始数量(小单位转大单位)")
    private BigDecimal beforeCount;

    @ApiModelProperty("变更前库存 大单位+小单位")
    private String beforeCountText;

    @ApiModelProperty("变更前库存 大单位+小单位数量str")
    private String beforeCountDecimalText;

    @ApiModelProperty("变更前库存 大单位+小单位数量Decimal")
    private BigDecimal beforeCountDecimal;

    @ApiModelProperty("变更前库存 大单位数量Decimal")
    private BigDecimal beforePackageCount;

    @ApiModelProperty("变更前库存 小单位数量Decimal")
    private BigDecimal beforePieceCount;

    @ApiModelProperty("变更总进价")
    private BigDecimal actionCost;

    @ApiModelProperty("变更总进价str")
    private String actionCostText;

    @ApiModelProperty("变更总进价Decimal")
    private BigDecimal actionCostDecimal;

    @ApiModelProperty("变更总进价DecimalStr")
    private String actionCostDecimalText;

    @ApiModelProperty("变更数量")
    private BigDecimal actionCount;

    @ApiModelProperty("变更数量转换小单位数量")
    private BigDecimal actionConvertPieceCount;

    @ApiModelProperty("变更数量Str")
    private String actionCountText;

    @ApiModelProperty("变更数量DecimalStr")
    private String actionCountDecimalText;

    @ApiModelProperty("变更数量Decimal")
    private BigDecimal actionCountDecimal;

    @ApiModelProperty("变更数量-大单位数量")
    private BigDecimal actionPackageCount;

    @ApiModelProperty("变更数量-小单位数量")
    private BigDecimal actionPieceCount;

    @ApiModelProperty("变更总售价")
    private BigDecimal actionPrice;

    @ApiModelProperty("变更总售价Str")
    private String actionPriceText;

    @ApiModelProperty("变更总售价Decimal")
    private BigDecimal actionPriceDecimal;

    @ApiModelProperty("变更后数量")
    private BigDecimal afterCount;

    @ApiModelProperty("变更后数量Str")
    private String afterCountText;

    @ApiModelProperty("变更后数量DecimalStr")
    private String afterCountDecimalText;

    @ApiModelProperty("变更后数量Decimal")
    private BigDecimal afterCountDecimal;

    @ApiModelProperty("变更后数量-大单位数量")
    private BigDecimal afterPackageCount;

    @ApiModelProperty("变更后数量-小单位数量")
    private BigDecimal afterPieceCount;

    @ApiModelProperty("操作人id")
    private String operatorId;

    @ApiModelProperty("操作人姓名")
    private String operatorText;

    @ApiModelProperty("患者id")
    private String patientId;

    @ApiModelProperty("患者姓名")
    private String patientName;

    //采购入库或者盘点入库是否修改过入库单，仅为库存-药品资料-进销存清单增加
    private boolean inOrderModified;

    //大单位
    String goodsUnitBefore;
    String goodsUnitAction;
    String goodsUnitAfter;

    @ApiModelProperty("操作人id")
    private String createdUserId;

    @ApiModelProperty("变更前小单位数量")
    private Integer beforePieceNumber;

    @ApiModelProperty("变更后小单位数量")
    private Integer afterPieceNumber;

    @ApiModelProperty("本次规格修改前药品小单位")
    private String beforePieceUnit;

    @ApiModelProperty("本次规格修改前药品大单位")
    private String beforePackageUnit;

    @ApiModelProperty("本次规格修改前药品完整规格")
    private String beforeDispSpec;

    @ApiModelProperty("本次规格修改后药品完整规格")
    private String dispSpec;

    @ApiModelProperty("库房名字")
    private String pharmacyName;

    @ApiModelProperty("费用类型id")
    private Long feeTypeId;

    @ApiModelProperty("费用类型名称")
    private String feeTypeName;

    @ApiModelProperty("入库单备注")
    private String comment;

    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty("订单id")
    private String orderId;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("发药类型 0门诊发药 10住院发药")
    private Integer dispenseType;

    @ApiModelProperty("发药场景 进销存发药场景 null 老数据,(0~100发药 0 门诊发药 ,10医院发药, 20 OTO发药,30微商城发药）")
    private Integer scene;

    @ApiModelProperty("发药方式 0手动 10自动")
    private Integer dispensingMethod;

    @ApiModelProperty("进销存log的商品详情字符串")
    private String goodsStr;

    @ApiModelProperty("追溯码listStr")
    private String traceableCodeListStr;

    @ApiModelProperty("追溯码list")
    private List<String> traceableCodeList;

    @ApiModelProperty("追溯码采集数量")
    private String traceableCodeCountStr;

    @ApiModelProperty("批次id")
    private String batId;

    @ApiModelProperty("最小包装单位")
    private String minPieceUnit;

    @ApiModelProperty("药品的剂型类型名称")
    private String dosageFormTypeName;

    @ApiModelProperty("利润分类")
    private Long profitCategoryTypeId;

    @ApiModelProperty("利润分类名称")
    private String profitCategoryTypeName;

    @ApiModelProperty("药品备注")
    private String remark;

    @ApiModelProperty("库存id")
    private Long stockId;

    @ApiModelProperty("进销存log ids")
    private String ids;

    @ApiModelProperty("进销存log id")
    private Long id;

    @ApiModelProperty("合作诊所id")
    private String cooperationClinicId;

    @ApiModelProperty("批次id")
    private String batchId;

    @ApiModelProperty("就诊单id")
    private String patientOrderId;

    @ApiModelProperty("诊号")
    private String patientOrderNo;

    @Value("${abc.goods-inventory.purchaseActionClinic}")
    private String goodsInventoryPurchaseActionClinic;

    /**
     *
     */
    public void apply() {
        if (inTaxRat == null) {
            inTaxRatText = 0 + "%";
        } else {
            inTaxRatText = inTaxRat.intValue() + "%";
        }
        if (outTaxRat == null) {
            outTaxRatText = 0 + "%";
        } else {
            outTaxRatText = outTaxRat.intValue() + "%";
        }
    }

    /**
     * @param packageUnit packageUnit
     * @param pieceUnit   pieceUnit
     * @return InventoryRecord
     */
    public InventoryRecord pretty(String packageUnit, String pieceUnit) {
        stringDetailValue();
        if (!this.feeType1Text.equals("中药饮片") && !this.feeType1Text.equals("中药颗粒")) {
            if (this.beforePackageCount != null && this.beforePieceCount != null) {
                if (this.beforePieceNumber != null) {
                    int beforeConvertPieceNumber = this.beforePackageCount.intValue() != 0 ? this.beforePackageCount.intValue() * this.beforePieceNumber + this.beforePieceCount.intValue() : this.beforePieceCount.intValue();
                    this.beforePackageCount = new BigDecimal(beforeConvertPieceNumber / this.beforePieceNumber);
                    this.beforePieceCount = new BigDecimal(beforeConvertPieceNumber - this.beforePackageCount.intValue() * this.beforePieceNumber);
                } else if (this.afterPieceNumber != null) {
                    int beforeConvertPieceNumber = this.beforePackageCount.intValue() != 0 ? this.beforePackageCount.intValue() * this.afterPieceNumber + this.beforePieceCount.intValue() : this.beforePieceCount.intValue();
                    this.beforePackageCount = new BigDecimal(beforeConvertPieceNumber / this.afterPieceNumber);
                    this.beforePieceCount = new BigDecimal(beforeConvertPieceNumber - this.beforePackageCount.intValue() * this.afterPieceNumber);
                }
            }
            if (this.afterPieceNumber != null && this.afterPackageCount != null && this.afterPieceCount != null) {
                int afterConvertPieceNumber = this.afterPackageCount.intValue() != 0 ? this.afterPackageCount.intValue() * this.afterPieceNumber + this.afterPieceCount.intValue() : this.afterPieceCount.intValue();
                this.afterPackageCount = new BigDecimal(afterConvertPieceNumber / this.afterPieceNumber);
                this.afterPieceCount = new BigDecimal(afterConvertPieceNumber - this.afterPackageCount.intValue() * this.afterPieceNumber);
            }
        }
        //2022-01-04 进价保留5位小数 或者两位小数
        inPriceText = ABCNumberUtils.roundTextPrettyNew(inPrice);
        outPriceText = ABCNumberUtils.positiveRound2Text(outPrice);
        beforeCountDecimal = ABCNumberUtils.round5Pretty(beforeCount);
        beforeCountDecimalText = ABCNumberUtils.round5TextPretty(beforeCount);
        beforeCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "-"
                : buildCountText(beforePackageCount, packageUnit, beforePieceCount, pieceUnit);
        if (actionCost != null) {
            actionCostText = ABCNumberUtils.round2TextPretty1(actionCost);
            actionCostDecimal = ABCNumberUtils.round5Pretty(actionCost);
            actionCostDecimalText = ABCNumberUtils.round5TextPretty(actionCost);
        }
        if (actionPrice != null) {
            actionPriceText = ABCNumberUtils.round2TextPretty1(actionPrice);
            actionPriceDecimal = ABCNumberUtils.round5Pretty(actionPrice);
        }
        actionCountDecimal = ABCNumberUtils.round5Pretty(actionCount);
        actionCountDecimalText = ABCNumberUtils.round5TextPretty(actionCount);
        if (action.equals("规格修改") && actionConvertPieceCount != null) {
            actionCountText = actionConvertPieceCount.stripTrailingZeros().toPlainString() + pieceUnit;
        } else {
            actionCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "-"
                    : buildCountText(actionPackageCount, packageUnit, actionPieceCount, pieceUnit);
        }
        afterCountDecimal = ABCNumberUtils.round5Pretty(afterCount);
        afterCountDecimalText = ABCNumberUtils.round5TextPretty(afterCount);
        afterCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "-"
                : buildCountText(afterPackageCount, packageUnit, afterPieceCount, pieceUnit);

        goodsUnitBefore = StringUtils.isNotEmpty(packageUnit) ? packageUnit
                : (StringUtils.isNotEmpty(pieceUnit) ? pieceUnit : "-");
        goodsUnitAction = StringUtils.isNotEmpty(packageUnit) ? packageUnit
                : (StringUtils.isNotEmpty(pieceUnit) ? pieceUnit : "-");
        goodsUnitAfter = StringUtils.isNotEmpty(packageUnit) ? packageUnit
                : (StringUtils.isNotEmpty(pieceUnit) ? pieceUnit : "-");
        return this;
    }

    /**
     * 属性字符串默认值
     */
    private void stringDetailValue() {
        if (equalNull(organText)) {
            organText = "-";
        }
        if (equalNull(goodsText)) {
            goodsText = "-";
        }
        if (equalNull(goodsShortId)) {
            goodsShortId = "-";
        }
        if (equalNull(feeType1Text)) {
            feeType1Text = "-";
        }
        if (equalNull(feeType2Text)) {
            feeType2Text = "-";
        }
        if (equalNull(supplierText)) {
            supplierText = "-";
        }
        if (equalNull(operatorText)) {
            operatorText = "-";
        }
        if (equalNull(expiryDate)) {
            expiryDate = "-";
        }
        if (equalNull(batchNo)) {
            batchNo = "-";
        }
        //规格 、厂家 为空处理
        if (equalNull(manufacturer)) {
            manufacturer = "-";
        }
        if (equalNull(specification)) {
            specification = "-";
        }
        //除了发药和退药都是-
        if (equalNull(patientName)) {
            patientName = "-";
        }
        if (equalNull(nmpn)) {
            nmpn = "-";
        }
        if (equalNull(action)) {
            action = "-";
        }
        if (equalNull(sheBaoCodeNationalCode)) {
            sheBaoCodeNationalCode = "-";
        }
        if (StrUtil.isBlank(inTaxRatText)) {
            inTaxRatText = "-";
        }
        if (StrUtil.isBlank(outTaxRatText)) {
            outTaxRatText = "-";
        }
        if (StrUtil.isBlank(pieceUnit)) {
            pieceUnit = "-";
        }
        if (StrUtil.isBlank(productionDate)) {
            productionDate = "-";
        }
        if (StrUtil.isBlank(comment)) {
            comment = "-";
        }
        if (equalNull(pharmacyName)) {
            pharmacyName = "-";
        }
        if (StrUtil.isBlank(this.source)) {
            this.source = "-";
        }
        if (StrUtil.isBlank(this.dosageFormTypeName)) {
            this.dosageFormTypeName = CommonConstants.WHIPPTREE;
        }
        if (StrUtil.isBlank(remark)) {
            this.remark = CommonConstants.WHIPPTREE;
        }
    }

    private String buildCountText(BigDecimal packageCount, String packageUnit, BigDecimal pieceCount, String pieceUnit) {
        StringBuilder sb = new StringBuilder();
        // 大单位和小单位都没有值时显示为0
        if (isBothCountsZero(packageCount, pieceCount)) {
            return "0";
        }
        // 处理大单位
        if (packageCount != null && packageCount.compareTo(BigDecimal.ZERO) != 0) {
            sb.append(ABCNumberUtils.round2TextPretty(packageCount));
            if (!isTraditionalChineseMedicine()) {
                sb.append(packageUnit);
            }
        }
        // 处理小单位
        appendPieceCount(sb, pieceCount, pieceUnit, packageCount);

        return sb.toString();
    }

    private boolean isBothCountsZero(BigDecimal packageCount, BigDecimal pieceCount) {
        return (packageCount == null || packageCount.compareTo(BigDecimal.ZERO) == 0) &&
                (pieceCount == null || pieceCount.compareTo(BigDecimal.ZERO) == 0);
    }

    private boolean isTraditionalChineseMedicine() {
        return this.feeType1Text.equals("中药饮片") || this.feeType1Text.equals("中药颗粒");
    }

    private void appendPieceCount(StringBuilder sb, BigDecimal pieceCount, String pieceUnit, BigDecimal packageCount) {
        if (pieceCount == null || pieceCount.compareTo(BigDecimal.ZERO) == 0) {
            return; // 如果 pieceCount 为 null 或 0，直接返回
        }

        String pieceCountStr = ABCNumberUtils.round2TextPretty(pieceCount);
        if (packageCount != null && packageCount.compareTo(BigDecimal.ZERO) == -1) {
            sb.append(pieceCountStr.replace("-", "")); // 大单位为0时添加负号
        } else {
            sb.append(pieceCountStr); // 否则正常添加
        }
        sb.append(pieceUnit);
    }


    /**
     * @param s s
     * @return boolean
     */
    private boolean equalNull(String s) {
        return s == null || "".equals(s);
    }

    /**
     * 设置药品相关信息
     *
     * @param goods                      药品信息
     * @param headerType                 表头类型
     * @param v2GoodsFeeTypeMap          费用类型map
     * @param goodsProfitCategoryTypeMap 利润分类map
     */
    public void setGoodsMessage(V2GoodsClassify goods,
                                String headerType,
                                Map<Long, V2GoodsFeeType> v2GoodsFeeTypeMap,
                                Map<Long, V2GoodsProfitCategoryType> goodsProfitCategoryTypeMap) {
        if (!BeanUtil.isEmpty(goods)) {
            this.goodsShortId = StrUtil.isBlank(goods.getShortId()) || goods.getShortId().equals("1") ? "-"
                    : goods.getShortId();
            //药品物资 规格逻辑
            if (!StrUtil.isBlank(headerType)
                    && headerType.equals(CommonConstants.CONTAINS_EYEGLASS_HEADER_TYPE)) {
                this.specification = V2GoodsClassify.setSpecificationBySpecColum(goods);
            } else {
                if (goods.getType() != null && goods.getSubType() != null && (goods.getType()
                        == GoodsFirstClassifyEnum.DRUGS.getTypeId().byteValue()
                        || goods.getType() == GoodsFirstClassifyEnum.MATERIAL.getTypeId().byteValue()
                        || goods.getType() == GoodsFirstClassifyEnum.COMMODITY.getTypeId().byteValue())) {
                    this.specification = GoodsHandler.handleGoodsSpec(goods.getType(), goods.getSubType(),
                            goods.getPieceNum(), goods.getPieceUnit(), goods.getPackageUnit(),
                            goods.getMedicineDosageNum(), goods.getMedicineDosageUnit(),
                            goods.getMaterialSpec(), goods.getExtendSpec());
                } else {
                    this.specification = V2GoodsClassify.setSpecificationBySpecColum(goods);
                }
            }

            if (!StrUtil.isBlank(goods.getName()) && !StrUtil.isBlank(goods.getMedicineCadn())) {
                this.goodsText = goods.getMedicineCadn() + "(" + goods.getName() + ")";
            } else if (!StrUtil.isBlank(goods.getMedicineCadn())) {
                this.goodsText = goods.getMedicineCadn();
            } else {
                this.goodsText = !StrUtil.isBlank(goods.getName()) ? goods.getName() : "-";
            }
            this.manufacturer = goods.getManufacturer();
            this.nmpn = goods.getMedicineNmpn();
            //获取商品最新的大单位和小单位
            this.sheBaoCodeNationalCode = goods.getShebaoCodeNationalCode();
            this.lastPackageCostPrice = goods.getLastPackageCostPrice();
            this.lastStockInOrderSupplier = goods.getLastStockInOrderSupplier();
            this.inTaxRat = goods.getInTaxRat();
            this.outTaxRat = goods.getOutTaxRat();
            this.packageUnit = goods.getPackageUnit();
            this.minPieceUnit = goods.getPieceUnit();
            this.pieceUnit = goods.getPieceUnit();
            //设置费用分类
            if (goods.getFeeTypeId() != null && v2GoodsFeeTypeMap.containsKey(goods.getFeeTypeId())) {
                this.feeTypeName = v2GoodsFeeTypeMap.get(goods.getFeeTypeId()).getName();
            }
            this.dosageFormTypeName = GoodsConst.dosageFormType(goods.getDosageFormType());
            this.profitCategoryTypeId = goods.getProfitCategoryType();
            if (!BeanUtil.isEmpty(goodsProfitCategoryTypeMap.get(this.profitCategoryTypeId))) {
                this.profitCategoryTypeName = goodsProfitCategoryTypeMap.get(this.profitCategoryTypeId).getName();
            }
            this.remark = goods.getRemark();
        }
        apply();
        if (goods != null && !BeanUtil.isEmpty(goods)) {
            this.pretty(goods.getPackageUnit(), goods.getPieceUnit());
        }
    }


    /**
     * @param organMap      门店信息map
     * @param employeeMap   人员信息map
     * @param supplierMap   供应商信息map
     * @param customTypeMap 自定义类型map
     * @param patientMap    患者信息map
     * @param param         进销存param
     * @param ecOrderMap    电商订单map
     */
    public void setSupplierAndOrganAndEmployee(Map<String, Organ> organMap,
                                               Map<String, Employee> employeeMap,
                                               Map<String, V2GoodsSupplier> supplierMap,
                                               Map<Integer, V2GoodsCustomType> customTypeMap,
                                               Map<String, V2Patient> patientMap,
                                               GoodsInventoryParam param,
                                               Map<String, V1EcOrder> ecOrderMap) {
        V2GoodsCustomType t = customTypeMap.get(this.getFeeType2());
        if (t != null) {
            this.feeType2Text = t.getName();
        }
        Organ organ = organMap.get(this.getOrganId());
        if (!BeanUtil.isEmpty(organ)) {
            if (param.getChainId().equals(organ.getId())) {
                this.organText = "总部";
            } else {
                this.organText = !StrUtil.isBlank(organ.getShortName()) ? organ.getShortName() : organ.getName();
            }
        }
        Employee e = employeeMap.get(this.getOperatorId());
        if (e != null) {
            this.operatorText = e.getName();
        }
        //供应商- 入库单维度的，每个入库单可以有单独的供应商，可修改
        V2GoodsSupplier supplier = supplierMap.get(this.getSupplierId());
        if (supplier != null) {
            this.supplierText = supplier.getName();
        }
        V2Patient v2Patient = patientMap.get(this.getPatientId());
        if (v2Patient != null) {
            this.patientName = v2Patient.getName();

        }
        if (param.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
            if (this.scene != null && (this.scene.equals(GoodsInventorySceneTypeEnum.EC.getTypeId())
                    || this.scene.equals(GoodsInventorySceneTypeEnum.MT.getTypeId()))) {
                V1EcOrder ecOrder = ecOrderMap.get(this.orderId);
                if (!BeanUtil.isEmpty(ecOrder)) {
                    if (this.scene.equals(GoodsInventorySceneTypeEnum.EC.getTypeId())) {
                        this.source = "电商-" + B2cEcTypeEnum.getTypeNameByTypeId(ecOrder.getEcType());
                    } else if (this.scene.equals(GoodsInventorySceneTypeEnum.MT.getTypeId())) {
                        this.source = "外卖-" + B2cEcTypeEnum.getTypeNameByTypeId(ecOrder.getEcType());
                    } else {
                        this.source = CommonConstants.WHIPPTREE;
                    }
                }
            } else if (this.scene != null && this.scene.equals(GoodsInventorySceneTypeEnum.RETAIL.getTypeId())) {
                this.source = GoodsInventorySceneTypeEnum.RETAIL.getTypeName();
            } else if (StrUtil.isNotEmpty(this.cooperationClinicId)) {
                this.source = "合作诊所-" + organMap.get(this.cooperationClinicId).getName();
            }
        }
    }

    /**
     * 设置匿名患者和价格
     *
     * @param param param
     */
    public void setActionAndCostAndPrice(GoodsInventoryParam param) {
        //  当动作为发药和退药时并且patient_id为空则展示匿名患者,其它动作显示-
        if ("发药".equals(this.action) || "退药".equals(this.action)) {
            if ("00000000000000000000000000000000".equals(this.patientId)
                    || this.patientId == null) {
                this.patientName = "匿名患者";
            }
            //空中药房 ，当动作为发药和退药时，生产批号、进价、变更总进价 ，显示为-
            if (param.getPharmacyType() != null
                    && param.getPharmacyType() == CommonConstants.OPHTHALMOLOGY_CLINIC_TYPE) {
                this.batchNo = "-";
                this.inPriceText = "";
                this.actionCostText = "";
                this.actionCostDecimalText = "";
            }
        }
        //2021-11-11 中药就需要小单位
        if (!param.getPermission().isEnableCost()) {
            this.inPrice = null;
            this.actionCost = null;
            this.inPriceText = "";
            this.actionCostText = "";
            this.actionCostDecimalText = "";
        }
    }

    /**
     * 根据pieceNumber重置变更前数量以及变更后数量
     * 如果小单位数量大于pieceNumber则重新计算数量
     */
    public void changeNumberByPieceNumber() {
        if (action.equals("规格修改")) {
            if (beforePieceNumber != null && beforePieceCount != null
                    && beforePieceCount.intValue() >= beforePieceNumber) {
                int beforeAddNumber = beforePieceCount.intValue() / beforePieceNumber;
                beforePackageCount = beforePackageCount.add(new BigDecimal(beforeAddNumber));
                beforePieceCount = beforePieceCount.subtract(new BigDecimal(beforeAddNumber * beforePieceNumber));
            }
        } else {
            if (afterPieceNumber != null && beforePieceCount != null
                    && beforePieceCount.intValue() >= afterPieceNumber) {
                if (!Objects.equals(this.feeType1, "1-12") && !Objects.equals(this.feeType1, "1-13")) {
                    int beforeAddNumber = beforePieceCount.intValue() / afterPieceNumber;
                    beforePackageCount = beforePackageCount.add(new BigDecimal(beforeAddNumber));
                    beforePieceCount = beforePieceCount.subtract(new BigDecimal(beforeAddNumber * afterPieceNumber));
                }
            }
        }

        if (afterPieceNumber != null && afterPieceCount != null
                && afterPieceCount.intValue() >= afterPieceNumber) {
            if (!Objects.equals(this.feeType1, "1-12") && !Objects.equals(this.feeType1, "1-13")) {
                int afterAddNumber = afterPieceCount.intValue() / afterPieceNumber;
                afterPackageCount = afterPackageCount.add(new BigDecimal(afterAddNumber));
                afterPieceCount = afterPieceCount.subtract(new BigDecimal(afterAddNumber * afterPieceNumber));
            }
        }
        if (afterPieceNumber != null && actionPieceCount != null
                && actionPieceCount.intValue() >= afterPieceNumber) {
            if (!Objects.equals(this.feeType1, "1-12") && !Objects.equals(this.feeType1, "1-13")) {
                int actionAddNumber = actionPieceCount.intValue() / afterPieceNumber;
                actionPackageCount = actionPackageCount.add(new BigDecimal(actionAddNumber));
                actionPieceCount = actionPieceCount.subtract(new BigDecimal(actionAddNumber * afterPieceNumber));
            }
        }


    }

    /**
     * @param isExport 是否是导出 true:是 false：否
     */
    public void setShowAccuracy(Boolean isExport) {
        if (isExport) {
            this.actionCount = ABCNumberUtils.round4Pretty(this.actionCount);
            this.actionConvertPieceCount = ABCNumberUtils.round4Pretty(this.actionConvertPieceCount);
            if (actionCost != null) {
                actionCost = ABCNumberUtils.round4Pretty(actionCost);
                actionCostText = ABCNumberUtils.round4TextPretty(actionCost);
                actionCostDecimal = ABCNumberUtils.round4Pretty(actionCost);
                actionCostDecimalText = ABCNumberUtils.round4TextPretty(actionCost);
            }
            if (actionPrice != null) {
                actionPrice = ABCNumberUtils.round4Pretty(actionPrice);
                actionPriceText = ABCNumberUtils.round4TextPretty(actionPrice);
                actionPriceDecimal = ABCNumberUtils.round4Pretty(actionPrice);
            }
            if (inPrice != null) {
                inPrice = ABCNumberUtils.round5DownPretty(inPrice);
            }
            if (outPrice != null) {
                outPrice = ABCNumberUtils.round4Pretty(outPrice);
            }
        } else {
            this.actionCount = ABCNumberUtils.round2Pretty(this.actionCount);
            this.actionConvertPieceCount = ABCNumberUtils.round2Pretty(this.actionConvertPieceCount);
            if (actionCost != null) {
                actionCost = ABCNumberUtils.round4Pretty(actionCost);
                actionCostText = ABCNumberUtils.round4TextPretty(actionCost);
                actionCostDecimal = ABCNumberUtils.round4Pretty(actionCost);
                actionCostDecimalText = ABCNumberUtils.round4TextPretty(actionCost);
            }
            if (actionPrice != null) {
                actionPrice = ABCNumberUtils.round2Pretty(actionPrice);
                actionPriceText = ABCNumberUtils.round2TextPretty1(actionPrice);
                actionPriceDecimal = ABCNumberUtils.round2Pretty(actionPrice);
            }
            if (inPrice != null) {
                inPrice = ABCNumberUtils.round5DownPretty(inPrice);
            }
            if (outPrice != null) {
                outPrice = ABCNumberUtils.round4Pretty(outPrice);
            }
        }

    }

    /**
     * 设置药房名
     *
     * @param pharmacy -
     */
    public void setPharmacyNoInfo(Map<String, Map<Integer, String>> pharmacy) {
        if (!CollUtil.isEmpty(pharmacy.get(this.organId))) {
            if (this.pharmacyNo != null) {
                this.pharmacyName = pharmacy.get(this.organId).get(this.pharmacyNo);
            }
        } else {
            this.pharmacyName = "-";
        }
    }

    /**
     * 设置追溯码
     * 批次维度直接拿批次上的追溯码 药品维度需要把批次的追溯码合并
     *
     * @param traceableCodeMap 追溯码map
     * @param goodsVersionMap  药品版本map
     */
    public void setTraceableCode(Map<Long, List<InventoryRecord>> traceableCodeMap,
                                 Map<String, List<V2GoodsHistoryVersion>> goodsVersionMap) {
        List<String> noList = new ArrayList<>();
        Map<String, BigDecimal> noMap = new HashMap<>();
        Map<String, BigDecimal> noPackageMap = new HashMap<>();
        Map<String, BigDecimal> noPieceMap = new HashMap<>();
        BigDecimal noNumber = BigDecimal.ZERO;
        BigDecimal noPackageNumber = BigDecimal.ZERO;
        BigDecimal noPieceNumber = BigDecimal.ZERO;
        String concatPackageUnit = this.packageUnit;
        String concatPieceUnit = this.pieceUnit;
        if (!StrUtil.isBlank(this.ids)) {
            for (String logId : ids.split(",")) {
                List<InventoryRecord> traceableCodeRecords = traceableCodeMap.get(Long.valueOf(logId));
                if (!CollUtil.isEmpty(traceableCodeRecords)) {
                    for (InventoryRecord inventoryRecord : traceableCodeRecords) {
                        if (!StrUtil.isBlank(inventoryRecord.getGoodsStr())) {
                            JSONObject goodsObject = JSONObject.parseObject(inventoryRecord.getGoodsStr());
                            // 如果药品版本号不等于0 需要根据版本号取大小单位
                            if (goodsObject.containsKey("goodsVersion") && goodsObject.getInteger("goodsVersion") != null && goodsObject.getInteger("goodsVersion") != 0) {
                                if (!CollUtil.isEmpty(goodsVersionMap.get(this.goodsId))) {
                                    List<V2GoodsHistoryVersion> versionFilterList = goodsVersionMap.get(this.goodsId).stream().filter(version -> version.getGoodsVersion().equals(goodsObject.getInteger("goodsVersion"))).collect(Collectors.toList());
                                    if (!CollUtil.isEmpty(versionFilterList)) {
                                        V2GoodsHistoryVersion version = versionFilterList.get(0);
                                        JSONObject goodsMessageObject = JSONObject.parseObject(version.getGoods());
                                        if (goodsMessageObject.containsKey("packageUnit")) {
                                            concatPackageUnit = goodsMessageObject.getString("packageUnit");
                                        }
                                        if (goodsMessageObject.containsKey("pieceUnit")) {
                                            concatPieceUnit = goodsMessageObject.getString("pieceUnit");
                                        }
                                    }
                                }
                            }
                            if (goodsObject.containsKey("traceableCodeList")) {
                                List<JSONObject> jsonObjects = JSONObject.parseArray(goodsObject.getString("traceableCodeList"), JSONObject.class);
                                setTraceableCodeNoList(noMap, noPackageMap, noPieceMap, jsonObjects);
                            }
                        }
                    }
                }
            }
            for (Map.Entry<String, BigDecimal> entry : noMap.entrySet()) {
                noList.add(entry.getKey() + " ×" + entry.getValue());
                noNumber = noNumber.add(entry.getValue());
            }
            for (Map.Entry<String, BigDecimal> entry : noPieceMap.entrySet()) {
                if (this.afterPieceNumber != null && entry.getValue().compareTo(BigDecimal.valueOf(this.afterPieceNumber)) > 0) {
                    int afterAddNumber = entry.getValue().divide(BigDecimal.valueOf(this.afterPieceNumber), RoundingMode.HALF_DOWN).intValue();
                    noPieceNumber = noPieceNumber.add(entry.getValue().subtract(BigDecimal.valueOf((long) afterAddNumber * this.afterPieceNumber)));
                    if (afterAddNumber != 0) {
                        noPackageMap.merge(entry.getKey(), BigDecimal.valueOf(afterAddNumber), BigDecimal::add);
                        entry.setValue(entry.getValue().subtract(BigDecimal.valueOf((long) afterAddNumber * this.afterPieceNumber)));
                    }
                    if (entry.getValue().compareTo(BigDecimal.ZERO) > 0) {
                        noList.add(entry.getKey() + " ×" + entry.getValue() + concatPieceUnit);
                    }
                } else {
                    noList.add(entry.getKey() + " ×" + entry.getValue() + concatPieceUnit);
                    noPieceNumber = noPieceNumber.add(entry.getValue());
                }
            }
            for (Map.Entry<String, BigDecimal> entry : noPackageMap.entrySet()) {
                noList.add(entry.getKey() + " ×" + entry.getValue() + concatPackageUnit);
                noPackageNumber = noPackageNumber.add(entry.getValue());
            }
            if (noNumber.compareTo(BigDecimal.ZERO) > 0) {
                this.traceableCodeCountStr = "已采集：" + noNumber.stripTrailingZeros().toPlainString();
            } else if (noPackageNumber.compareTo(BigDecimal.ZERO) > 0 && noPieceNumber.compareTo(BigDecimal.ZERO) > 0) {
                this.traceableCodeCountStr = "已采集：" + noPackageNumber.stripTrailingZeros().toPlainString() + concatPackageUnit + " " + noPieceNumber.stripTrailingZeros().toPlainString() + concatPieceUnit;
            } else if (noPackageNumber.compareTo(BigDecimal.ZERO) > 0) {
                this.traceableCodeCountStr = "已采集：" + noPackageNumber.stripTrailingZeros().toPlainString() + concatPackageUnit;
            } else if (noPieceNumber.compareTo(BigDecimal.ZERO) > 0) {
                this.traceableCodeCountStr = "已采集：" + noPieceNumber.stripTrailingZeros().toPlainString() + concatPieceUnit;
            } else {
                this.traceableCodeCountStr = "";
            }
            this.traceableCodeList = noList;
        } else {
            this.traceableCodeCountStr = "";
            this.traceableCodeList = new ArrayList<>();
        }
    }

    /**
     * 设置追溯码list
     * 追溯码在hisPackageCount和hisPieceCount中是有单位的 需特殊处理
     * count是历史数据有的不为1 需特殊处理
     * pieceCount以及其他都为1
     *
     * @param noMap       追溯码map
     * @param jsonObjects 追溯码json集合
     */
    private void setTraceableCodeNoList(Map<String, BigDecimal> noMap,
                                        Map<String, BigDecimal> noPackageMap,
                                        Map<String, BigDecimal> noPieceMap,
                                        List<JSONObject> jsonObjects) {
        for (JSONObject jsonObject : jsonObjects) {
            String no = jsonObject.getString("no");
            // 若编号为空，直接跳过后续处理
            if (StrUtil.isBlank(no)) {
                continue;
            }
            handleField(jsonObject, "hisPackageCount", no, noPackageMap);
            handleField(jsonObject, "hisPieceCount", no, noPieceMap);
            handleCountField(jsonObject, no, noMap);
            // 若三个字段都不包含，则noMap默认+1
            if (!jsonObject.containsKey("hisPackageCount")
                    && !jsonObject.containsKey("hisPieceCount")
                    && !jsonObject.containsKey("count")) {
                addToMap(noMap, no, BigDecimal.ONE);
            }
        }
    }

    /**
     * 通用字段处理方法
     *
     * @param jsonObject JSON对象
     * @param fieldName  字段名
     * @param no         编号
     * @param map        要更新的Map
     */
    private void handleField(JSONObject jsonObject, String fieldName, String no,
                             Map<String, BigDecimal> map) {
        if (jsonObject.containsKey(fieldName)) {
            BigDecimal count = jsonObject.getBigDecimal(fieldName);
            // 若值为null，或需要检查0且值为0，则不处理
            if (count == null || (count.compareTo(BigDecimal.ZERO) == 0)) {
                return;
            }
            addToMap(map, no, count);
        }
    }

    /**
     * 处理count字段（特殊逻辑：null时按1处理）
     */
    private void handleCountField(JSONObject jsonObject, String no, Map<String, BigDecimal> noMap) {
        if (jsonObject.containsKey("count")) {
            BigDecimal count = jsonObject.getBigDecimal("count");
            // count为null时按1处理
            BigDecimal actualCount = (count != null) ? count : BigDecimal.ONE;
            addToMap(noMap, no, actualCount);
        }
    }

    /**
     * 向Map中添加计数（不存在则初始化为0后累加）
     */
    private void addToMap(Map<String, BigDecimal> map, String key, BigDecimal value) {
        map.put(key, map.getOrDefault(key, BigDecimal.ZERO).add(value));
    }


    /**
     * 根据门店类型设置动作类型
     *
     * @param hisType                            门店类型
     * @param chainId                            连锁id
     * @param goodsInventoryPurchaseActionClinic 进销存购进入库动作门店
     */
    public void setActionByHisType(String hisType, String chainId, String goodsInventoryPurchaseActionClinic) {
        this.action = GoodsInventoryActionEnum.getInventoryListShowAction(hisType, this.dispenseType, this.scene, this.dispensingMethod, this.action, chainId, goodsInventoryPurchaseActionClinic);
        // 如果是药店 动作调拨为调剂
        if (hisType.equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
            if (action.equals("调拨入库")) {
                this.action = "调剂入库";
            } else if (action.equals("调拨出库")) {
                this.action = "调剂出库";
            }
        }
    }

    /**
     * 设置goodsStock相关信息
     *
     * @param v2GoodsStockMap goodsStockMap
     */
    public void setGoodsStockMessage(Map<Long, V2GoodsStock> v2GoodsStockMap) {
        if (!BeanUtil.isEmpty(v2GoodsStockMap.get(this.stockId))) {
            this.productionDate = v2GoodsStockMap.get(this.stockId).getProductionDate();
            this.expiryDate = v2GoodsStockMap.get(this.stockId).getExpiryDate();
            this.batchNo = v2GoodsStockMap.get(this.stockId).getBatchNo();
        }
    }


    /**
     * 设置就诊信息
     *
     * @param patientOrderMap 就诊信息map
     */
    public void setPatientOrderMessage(Map<String, V2Patientorder> patientOrderMap) {
        if (GoodsInventoryActionEnum.DISPENSE_MEDICINE_ACTION_SET.contains(action)) {
            if (BeanUtil.isNotEmpty(patientOrderMap.get(this.patientOrderId))) {
                this.patientOrderNo = cn.abc.flink.stat.common.StringUtils.formatNumberWithLeadingZeros(patientOrderMap.get(this.patientOrderId).getNo().intValue(), CommonConstants.NUMBER_EIGHT);
            } else {
                this.patientOrderNo = CommonConstants.WHIPPTREE;
            }
        } else {
            this.patientOrderId = CommonConstants.WHIPPTREE;
            this.patientOrderNo = CommonConstants.WHIPPTREE;
        }
    }

    public String getCreateDate() {
        return this.createDate;
    }

    public String getOrganId() {
        return this.organId;
    }

    public String getOrganText() {
        return this.organText;
    }

    public String getAction() {
        return this.action;
    }

    public String getGoodsId() {
        return this.goodsId;
    }

    public String getGoodsText() {
        return this.goodsText;
    }

    public String getGoodsShortId() {
        return this.goodsShortId;
    }

    public String getFeeType1() {
        return this.feeType1;
    }

    public String getFeeType1Text() {
        return this.feeType1Text;
    }

    public Integer getFeeType2() {
        return this.feeType2;
    }

    public String getFeeType2Text() {
        return this.feeType2Text;
    }

    public Integer getPharmacyNo() {
        return this.pharmacyNo;
    }

    public String getSpecification() {
        return this.specification;
    }

    public String getPieceUnit() {
        return this.pieceUnit;
    }

    public String getPackageUnit() {
        return this.packageUnit;
    }

    public BigDecimal getPiecePrice() {
        return this.piecePrice;
    }

    public BigDecimal getPackagePrice() {
        return this.packagePrice;
    }

    public String getSupplierId() {
        return this.supplierId;
    }

    public String getSupplierText() {
        return this.supplierText;
    }

    public String getBatchNo() {
        return this.batchNo;
    }

    public String getExpiryDate() {
        return this.expiryDate;
    }

    public String getManufacturer() {
        return this.manufacturer;
    }

    public String getNmpn() {
        return this.nmpn;
    }

    public String getProductionDate() {
        return this.productionDate;
    }

    public BigDecimal getInPrice() {
        return this.inPrice;
    }

    public String getInPriceText() {
        return this.inPriceText;
    }

    public BigDecimal getOutPrice() {
        return this.outPrice;
    }

    public String getOutPriceText() {
        return this.outPriceText;
    }

    public BigDecimal getLastPackageCostPrice() {
        return this.lastPackageCostPrice;
    }

    public String getLastStockInOrderSupplier() {
        return this.lastStockInOrderSupplier;
    }

    public String getSheBaoCodeNationalCode() {
        return this.sheBaoCodeNationalCode;
    }

    public BigDecimal getInTaxRat() {
        return this.inTaxRat;
    }

    public BigDecimal getOutTaxRat() {
        return this.outTaxRat;
    }

    public String getInTaxRatText() {
        return this.inTaxRatText;
    }

    public String getOutTaxRatText() {
        return this.outTaxRatText;
    }

    public BigDecimal getBeforeCount() {
        return this.beforeCount;
    }

    public String getBeforeCountText() {
        return this.beforeCountText;
    }

    public String getBeforeCountDecimalText() {
        return this.beforeCountDecimalText;
    }

    public BigDecimal getBeforeCountDecimal() {
        return this.beforeCountDecimal;
    }

    public BigDecimal getBeforePackageCount() {
        return this.beforePackageCount;
    }

    public BigDecimal getBeforePieceCount() {
        return this.beforePieceCount;
    }

    public BigDecimal getActionCost() {
        return this.actionCost;
    }

    public String getActionCostText() {
        return this.actionCostText;
    }

    public BigDecimal getActionCostDecimal() {
        return this.actionCostDecimal;
    }

    public String getActionCostDecimalText() {
        return this.actionCostDecimalText;
    }

    public BigDecimal getActionCount() {
        return this.actionCount;
    }

    public BigDecimal getActionConvertPieceCount() {
        return this.actionConvertPieceCount;
    }

    public String getActionCountText() {
        return this.actionCountText;
    }

    public String getActionCountDecimalText() {
        return this.actionCountDecimalText;
    }

    public BigDecimal getActionCountDecimal() {
        return this.actionCountDecimal;
    }

    public BigDecimal getActionPackageCount() {
        return this.actionPackageCount;
    }

    public BigDecimal getActionPieceCount() {
        return this.actionPieceCount;
    }

    public BigDecimal getActionPrice() {
        return this.actionPrice;
    }

    public String getActionPriceText() {
        return this.actionPriceText;
    }

    public BigDecimal getActionPriceDecimal() {
        return this.actionPriceDecimal;
    }

    public BigDecimal getAfterCount() {
        return this.afterCount;
    }

    public String getAfterCountText() {
        return this.afterCountText;
    }

    public String getAfterCountDecimalText() {
        return this.afterCountDecimalText;
    }

    public BigDecimal getAfterCountDecimal() {
        return this.afterCountDecimal;
    }

    public BigDecimal getAfterPackageCount() {
        return this.afterPackageCount;
    }

    public BigDecimal getAfterPieceCount() {
        return this.afterPieceCount;
    }

    public String getOperatorId() {
        return this.operatorId;
    }

    public String getOperatorText() {
        return this.operatorText;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public boolean getInOrderModified() {
        return this.inOrderModified;
    }

    public String getCreatedUserId() {
        return this.createdUserId;
    }

    public Integer getBeforePieceNumber() {
        return this.beforePieceNumber;
    }

    public Integer getAfterPieceNumber() {
        return this.afterPieceNumber;
    }

    public String getBeforePieceUnit() {
        return this.beforePieceUnit;
    }

    public String getBeforePackageUnit() {
        return this.beforePackageUnit;
    }

    public String getBeforeDispSpec() {
        return this.beforeDispSpec;
    }

    public String getDispSpec() {
        return this.dispSpec;
    }

    public String getPharmacyName() {
        return this.pharmacyName;
    }

    public Long getFeeTypeId() {
        return this.feeTypeId;
    }

    public String getFeeTypeName() {
        return this.feeTypeName;
    }

    public String getComment() {
        return this.comment;
    }

    public String getSource() {
        return this.source;
    }

    public String getOrderId() {
        return this.orderId;
    }

    public Integer getDispenseType() {
        return this.dispenseType;
    }

    public Integer getScene() {
        return this.scene;
    }

    public Integer getDispensingMethod() {
        return this.dispensingMethod;
    }

    public String getGoodsStr() {
        return this.goodsStr;
    }

    public String getTraceableCodeListStr() {
        return this.traceableCodeListStr;
    }

    public List<String> getTraceableCodeList() {
        return this.traceableCodeList;
    }

    public String getTraceableCodeCountStr() {
        return this.traceableCodeCountStr;
    }

    public String getBatId() {
        return this.batId;
    }

    public String getMinPieceUnit() {
        return this.minPieceUnit;
    }

    public String getDosageFormTypeName() {
        return this.dosageFormTypeName;
    }

    public String getGoodsInventoryPurchaseActionClinic() {
        return this.goodsInventoryPurchaseActionClinic;
    }

    public Long getProfitCategoryTypeId() {
        return this.profitCategoryTypeId;
    }

    public String getProfitCategoryTypeName() {
        return this.profitCategoryTypeName;
    }


    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public void setOrganId(String organId) {
        this.organId = organId;
    }

    public void setOrganText(String organText) {
        this.organText = organText;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsText(String goodsText) {
        this.goodsText = goodsText;
    }

    public void setGoodsShortId(String goodsShortId) {
        this.goodsShortId = goodsShortId;
    }

    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public void setFeeType1Text(String feeType1Text) {
        this.feeType1Text = feeType1Text;
    }

    public void setFeeType2(Integer feeType2) {
        this.feeType2 = feeType2;
    }

    public void setFeeType2Text(String feeType2Text) {
        this.feeType2Text = feeType2Text;
    }

    public void setPharmacyNo(Integer pharmacyNo) {
        this.pharmacyNo = pharmacyNo;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public void setPieceUnit(String pieceUnit) {
        this.pieceUnit = pieceUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public void setPiecePrice(BigDecimal piecePrice) {
        this.piecePrice = piecePrice;
    }

    public void setPackagePrice(BigDecimal packagePrice) {
        this.packagePrice = packagePrice;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public void setSupplierText(String supplierText) {
        this.supplierText = supplierText;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public void setNmpn(String nmpn) {
        this.nmpn = nmpn;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public void setInPrice(BigDecimal inPrice) {
        this.inPrice = inPrice;
    }

    public void setInPriceText(String inPriceText) {
        this.inPriceText = inPriceText;
    }

    public void setOutPrice(BigDecimal outPrice) {
        this.outPrice = outPrice;
    }

    public void setOutPriceText(String outPriceText) {
        this.outPriceText = outPriceText;
    }

    public void setLastPackageCostPrice(BigDecimal lastPackageCostPrice) {
        this.lastPackageCostPrice = lastPackageCostPrice;
    }

    public void setLastStockInOrderSupplier(String lastStockInOrderSupplier) {
        this.lastStockInOrderSupplier = lastStockInOrderSupplier;
    }

    public void setSheBaoCodeNationalCode(String sheBaoCodeNationalCode) {
        this.sheBaoCodeNationalCode = sheBaoCodeNationalCode;
    }

    public void setInTaxRat(BigDecimal inTaxRat) {
        this.inTaxRat = inTaxRat;
    }

    public void setOutTaxRat(BigDecimal outTaxRat) {
        this.outTaxRat = outTaxRat;
    }

    public void setInTaxRatText(String inTaxRatText) {
        this.inTaxRatText = inTaxRatText;
    }

    public void setOutTaxRatText(String outTaxRatText) {
        this.outTaxRatText = outTaxRatText;
    }

    public void setBeforeCount(BigDecimal beforeCount) {
        this.beforeCount = beforeCount;
    }

    public void setBeforeCountText(String beforeCountText) {
        this.beforeCountText = beforeCountText;
    }

    public void setBeforeCountDecimalText(String beforeCountDecimalText) {
        this.beforeCountDecimalText = beforeCountDecimalText;
    }

    public void setBeforeCountDecimal(BigDecimal beforeCountDecimal) {
        this.beforeCountDecimal = beforeCountDecimal;
    }

    public void setBeforePackageCount(BigDecimal beforePackageCount) {
        this.beforePackageCount = beforePackageCount;
    }

    public void setBeforePieceCount(BigDecimal beforePieceCount) {
        this.beforePieceCount = beforePieceCount;
    }

    public void setActionCost(BigDecimal actionCost) {
        this.actionCost = actionCost;
    }

    public void setActionCostText(String actionCostText) {
        this.actionCostText = actionCostText;
    }

    public void setActionCostDecimal(BigDecimal actionCostDecimal) {
        this.actionCostDecimal = actionCostDecimal;
    }

    public void setActionCostDecimalText(String actionCostDecimalText) {
        this.actionCostDecimalText = actionCostDecimalText;
    }

    public void setActionCount(BigDecimal actionCount) {
        this.actionCount = actionCount;
    }

    public void setActionConvertPieceCount(BigDecimal actionConvertPieceCount) {
        this.actionConvertPieceCount = actionConvertPieceCount;
    }

    public void setActionCountText(String actionCountText) {
        this.actionCountText = actionCountText;
    }

    public void setActionCountDecimalText(String actionCountDecimalText) {
        this.actionCountDecimalText = actionCountDecimalText;
    }

    public void setActionCountDecimal(BigDecimal actionCountDecimal) {
        this.actionCountDecimal = actionCountDecimal;
    }

    public void setActionPackageCount(BigDecimal actionPackageCount) {
        this.actionPackageCount = actionPackageCount;
    }

    public void setActionPieceCount(BigDecimal actionPieceCount) {
        this.actionPieceCount = actionPieceCount;
    }

    public void setActionPrice(BigDecimal actionPrice) {
        this.actionPrice = actionPrice;
    }

    public void setActionPriceText(String actionPriceText) {
        this.actionPriceText = actionPriceText;
    }

    public void setActionPriceDecimal(BigDecimal actionPriceDecimal) {
        this.actionPriceDecimal = actionPriceDecimal;
    }

    public void setAfterCount(BigDecimal afterCount) {
        this.afterCount = afterCount;
    }

    public void setAfterCountText(String afterCountText) {
        this.afterCountText = afterCountText;
    }

    public void setAfterCountDecimalText(String afterCountDecimalText) {
        this.afterCountDecimalText = afterCountDecimalText;
    }

    public void setAfterCountDecimal(BigDecimal afterCountDecimal) {
        this.afterCountDecimal = afterCountDecimal;
    }

    public void setAfterPackageCount(BigDecimal afterPackageCount) {
        this.afterPackageCount = afterPackageCount;
    }

    public void setAfterPieceCount(BigDecimal afterPieceCount) {
        this.afterPieceCount = afterPieceCount;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public void setOperatorText(String operatorText) {
        this.operatorText = operatorText;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setInOrderModified(boolean inOrderModified) {
        this.inOrderModified = inOrderModified;
    }

    public void setCreatedUserId(String createdUserId) {
        this.createdUserId = createdUserId;
    }

    public void setBeforePieceNumber(Integer beforePieceNumber) {
        this.beforePieceNumber = beforePieceNumber;
    }

    public void setAfterPieceNumber(Integer afterPieceNumber) {
        this.afterPieceNumber = afterPieceNumber;
    }

    public void setBeforePieceUnit(String beforePieceUnit) {
        this.beforePieceUnit = beforePieceUnit;
    }

    public void setBeforePackageUnit(String beforePackageUnit) {
        this.beforePackageUnit = beforePackageUnit;
    }

    public void setBeforeDispSpec(String beforeDispSpec) {
        this.beforeDispSpec = beforeDispSpec;
    }

    public void setDispSpec(String dispSpec) {
        this.dispSpec = dispSpec;
    }

    public void setPharmacyName(String pharmacyName) {
        this.pharmacyName = pharmacyName;
    }

    public void setFeeTypeId(Long feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setDispenseType(Integer dispenseType) {
        this.dispenseType = dispenseType;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public void setDispensingMethod(Integer dispensingMethod) {
        this.dispensingMethod = dispensingMethod;
    }

    public void setGoodsStr(String goodsStr) {
        this.goodsStr = goodsStr;
    }

    public void setTraceableCodeListStr(String traceableCodeListStr) {
        this.traceableCodeListStr = traceableCodeListStr;
    }

    public void setTraceableCodeList(List<String> traceableCodeList) {
        this.traceableCodeList = traceableCodeList;
    }

    public void setTraceableCodeCountStr(String traceableCodeCountStr) {
        this.traceableCodeCountStr = traceableCodeCountStr;
    }

    public void setBatId(String batId) {
        this.batId = batId;
    }

    public void setMinPieceUnit(String minPieceUnit) {
        this.minPieceUnit = minPieceUnit;
    }

    public void setDosageFormTypeName(String dosageFormTypeName) {
        this.dosageFormTypeName = dosageFormTypeName;
    }

    public void setGoodsInventoryPurchaseActionClinic(String goodsInventoryPurchaseActionClinic) {
        this.goodsInventoryPurchaseActionClinic = goodsInventoryPurchaseActionClinic;
    }

    public void setProfitCategoryTypeId(Long profitCategoryTypeId) {
        this.profitCategoryTypeId = profitCategoryTypeId;
    }

    public void setProfitCategoryTypeName(String profitCategoryTypeName) {
        this.profitCategoryTypeName = profitCategoryTypeName;
    }

    public boolean isInOrderModified() {
        return inOrderModified;
    }

    public String getGoodsUnitBefore() {
        return goodsUnitBefore;
    }

    public void setGoodsUnitBefore(String goodsUnitBefore) {
        this.goodsUnitBefore = goodsUnitBefore;
    }

    public String getGoodsUnitAction() {
        return goodsUnitAction;
    }

    public void setGoodsUnitAction(String goodsUnitAction) {
        this.goodsUnitAction = goodsUnitAction;
    }

    public String getGoodsUnitAfter() {
        return goodsUnitAfter;
    }

    public void setGoodsUnitAfter(String goodsUnitAfter) {
        this.goodsUnitAfter = goodsUnitAfter;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getStockId() {
        return stockId;
    }

    public void setStockId(Long stockId) {
        this.stockId = stockId;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCooperationClinicId() {
        return cooperationClinicId;
    }

    public void setCooperationClinicId(String cooperationClinicId) {
        this.cooperationClinicId = cooperationClinicId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getPatientOrderId() {
        return patientOrderId;
    }

    public void setPatientOrderId(String patientOrderId) {
        this.patientOrderId = patientOrderId;
    }

    public String getPatientOrderNo() {
        return patientOrderNo;
    }

    public void setPatientOrderNo(String patientOrderNo) {
        this.patientOrderNo = patientOrderNo;
    }
}
