package cn.abc.flink.stat.service.cis.achievement.recommend.domain;

import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.request.AbcPermission;
import cn.abc.flink.stat.service.cis.achievement.charge.handler.AchievementChargeCalaHandler;
import cn.abc.flink.stat.service.cis.achievement.commission.pojo.AchievementCommissionConfigResp;
import cn.abc.flink.stat.service.cis.config.handler.StatConfigHandler;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;


import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * goods
 */
public class AchievementRecommendGoodsEntity {

    private static final Integer PRECISION_DEFAULT_VALUE = 2;

    private static final String PERCENT = "%";

    @ExcelIgnore
    protected String chainId;
    @ExcelIgnore
    protected String clinicId;
    @ExcelProperty(index = 0, value = "门店")
    protected String clinicName;
    @ExcelIgnore
    protected String sourceLevelOne;
    protected String sourceLevelTwo;

    /**
     * 推荐leve3 类型1.employee，2.patient
     */
    private Integer visitSourceFromType;
    /**
     * 推荐leve3
     */
    private String visitSourceFrom;

    protected String recommendName;
    @ExcelIgnore
    private Byte isWriter;
    @ExcelIgnore
    private String goodsId;
    @ExcelProperty(index = 5, value = "项目编码")
    private String shortId;
    @ExcelProperty(index = 4, value = "项目")
    private String name;
    @ExcelProperty(index = 6, value = "厂家")
    private String manufacturer;
    @ExcelProperty(index = 7, value = "规格")
    private String spec;
    @ExcelIgnore
    private String classifyLevel1;
    @ExcelIgnore
    private Integer classifyLevel2;
    @ExcelProperty(index = 2, value = "一级分类")
    private String feeType1;
    @ExcelProperty(index = 3, value = "二级分类")
    private String feeType2;
    @ExcelProperty(index = 9, value = "单位")
    private String unit;
    @ExcelProperty(index = 8, value = "数量")
    private BigDecimal count;
    @ExcelProperty(index = 12, value = "成本")
    private BigDecimal cost;
    @ExcelProperty(index = 10, value = "实收金额")
    private BigDecimal amount;
    @ExcelProperty(index = 13, value = "毛利")
    private BigDecimal gross;
    @ExcelProperty(index = 14, value = "毛利率")
    private String profit;
    @ExcelIgnore
    private Integer airCount;
    @ExcelIgnore
    private BigDecimal originPrice;
    @ExcelIgnore
    private BigDecimal deductPrice;
    @ExcelProperty(index = 11, value = "计提金额")
    private BigDecimal commissionAmount;
    @ExcelIgnore
    private String copyWriter;
    @ExcelIgnore
    private String grade;

    /**
     * 转诊医生id
     */
    private String referralDoctorId;

    /**
     * 转诊医生姓名
     */
    private String referralDoctorName;
    private Integer hoverCode;

    /**
     * 计算价格、成本、毛利等
     * @param payModeSql 用户的支付方式筛选条件，如果用户筛选支付方式，则没有成本和毛利
     * @param dto -
     */
    public void handlePrice(String payModeSql, StatConfigDto dto) {
        if (payModeSql != null && !"".equals(payModeSql.trim())) {
            this.cost = null;
            this.gross = null;
            this.profit = "-";
        }

        this.originPrice = this.originPrice == null ? BigDecimal.ZERO
                : this.originPrice.setScale(PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP);
        this.cost = this.cost == null ? BigDecimal.ZERO
                : this.cost.setScale(PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP);
        this.amount = this.amount == null ? BigDecimal.ZERO
                : this.amount.setScale(PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP);
        this.count = this.count == null ? BigDecimal.ZERO
                : this.count.setScale(PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP);
        this.commissionAmount = StatConfigHandler.getCommissionAmount(dto, this.originPrice, this.amount,
                null, this.deductPrice);
        this.gross = AchievementChargeCalaHandler.calaGross(this.getCommissionAmount(), this.cost);
        if (this.commissionAmount != null && this.commissionAmount.compareTo(BigDecimal.ZERO) > 0
                && this.gross != null && this.gross.compareTo(BigDecimal.ZERO) > 0) {
            this.profit = AchievementChargeCalaHandler.calaProfit(this.commissionAmount, this.gross) + PERCENT;
        } else {
            this.profit = "-";
        }
    }

    /**
     * 权限设置
     * @param permission 权限配置
     */
    public void permissionSetting(AbcPermission permission) {
        if (!permission.isEnableCost()) {
            this.cost = null;
        }
        if (!permission.isEnableGross()) {
            this.gross = null;
            this.profit = null;
        }
    }

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public String getSourceLevelOne() {
        return this.sourceLevelOne;
    }

    public String getSourceLevelTwo() {
        return this.sourceLevelTwo;
    }

    public Integer getVisitSourceFromType() {
        return this.visitSourceFromType;
    }

    public String getVisitSourceFrom() {
        return this.visitSourceFrom;
    }

    public String getRecommendName() {
        return this.recommendName;
    }

    public Byte getIsWriter() {
        return this.isWriter;
    }

    public String getGoodsId() {
        return this.goodsId;
    }

    public String getShortId() {
        return this.shortId;
    }

    public String getName() {
        return this.name;
    }

    public String getManufacturer() {
        return this.manufacturer;
    }

    public String getSpec() {
        return this.spec;
    }

    public String getClassifyLevel1() {
        return this.classifyLevel1;
    }

    public Integer getClassifyLevel2() {
        return this.classifyLevel2;
    }

    public String getFeeType1() {
        return this.feeType1;
    }

    public String getFeeType2() {
        return this.feeType2;
    }

    public String getUnit() {
        return this.unit;
    }

    public BigDecimal getCount() {
        return this.count;
    }

    public BigDecimal getCost() {
        return this.cost;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public BigDecimal getGross() {
        return this.gross;
    }

    public String getProfit() {
        return this.profit;
    }

    public Integer getAirCount() {
        return this.airCount;
    }

    public BigDecimal getOriginPrice() {
        return this.originPrice;
    }

    public BigDecimal getDeductPrice() {
        return this.deductPrice;
    }

    public BigDecimal getCommissionAmount() {
        return this.commissionAmount;
    }

    public String getCopyWriter() {
        return this.copyWriter;
    }

    public String getGrade() {
        return this.grade;
    }

    public String getReferralDoctorId() {
        return this.referralDoctorId;
    }

    public String getReferralDoctorName() {
        return this.referralDoctorName;
    }

    public Integer getHoverCode() {
        return this.hoverCode;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setSourceLevelOne(String sourceLevelOne) {
        this.sourceLevelOne = sourceLevelOne;
    }

    public void setSourceLevelTwo(String sourceLevelTwo) {
        this.sourceLevelTwo = sourceLevelTwo;
    }

    public void setVisitSourceFromType(Integer visitSourceFromType) {
        this.visitSourceFromType = visitSourceFromType;
    }

    public void setVisitSourceFrom(String visitSourceFrom) {
        this.visitSourceFrom = visitSourceFrom;
    }

    public void setRecommendName(String recommendName) {
        this.recommendName = recommendName;
    }

    public void setIsWriter(Byte isWriter) {
        this.isWriter = isWriter;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setShortId(String shortId) {
        this.shortId = shortId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public void setClassifyLevel1(String classifyLevel1) {
        this.classifyLevel1 = classifyLevel1;
    }

    public void setClassifyLevel2(Integer classifyLevel2) {
        this.classifyLevel2 = classifyLevel2;
    }

    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public void setFeeType2(String feeType2) {
        this.feeType2 = feeType2;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setGross(BigDecimal gross) {
        this.gross = gross;
    }

    public void setProfit(String profit) {
        this.profit = profit;
    }

    public void setAirCount(Integer airCount) {
        this.airCount = airCount;
    }

    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    public void setDeductPrice(BigDecimal deductPrice) {
        this.deductPrice = deductPrice;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public void setCopyWriter(String copyWriter) {
        this.copyWriter = copyWriter;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public void setReferralDoctorId(String referralDoctorId) {
        this.referralDoctorId = referralDoctorId;
    }

    public void setReferralDoctorName(String referralDoctorName) {
        this.referralDoctorName = referralDoctorName;
    }

    public void setHoverCode(Integer hoverCode) {
        this.hoverCode = hoverCode;
    }

}
