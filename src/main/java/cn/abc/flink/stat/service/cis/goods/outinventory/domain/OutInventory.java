package cn.abc.flink.stat.service.cis.goods.outinventory.domain;

import cn.abc.flink.stat.common.ABCNumberUtils;


import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/23 上午10:49
 */

public class OutInventory {

    private String clinicId;
    private String clinicName;

    private Long goodsKind;

    private BigDecimal goodsCount;
    private String goodsCountText;

    private BigDecimal goodsAmount;
    private String goodsAmountText;


    private BigDecimal goodsAmountExcludedTax;
    private String goodsAmountExcludedTaxText;
    //多药房的药房编号
    private Integer pharmacyNo;
    //多药房的药房名称
    private String pharmacyName;

    /**
     * @return -
     */
    public OutInventory pretty() {

        goodsCountText = ABCNumberUtils.round2TextPretty1(goodsCount);
        goodsAmountText = ABCNumberUtils.round4TextPretty(goodsAmount);
        goodsAmountExcludedTaxText = ABCNumberUtils.round4TextPretty(goodsAmountExcludedTax);
        pharmacyName = "-";
        return this;

    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public Long getGoodsKind() {
        return this.goodsKind;
    }

    public BigDecimal getGoodsCount() {
        return this.goodsCount;
    }

    public String getGoodsCountText() {
        return this.goodsCountText;
    }

    public BigDecimal getGoodsAmount() {
        return this.goodsAmount;
    }

    public String getGoodsAmountText() {
        return this.goodsAmountText;
    }

    public BigDecimal getGoodsAmountExcludedTax() {
        return this.goodsAmountExcludedTax;
    }

    public String getGoodsAmountExcludedTaxText() {
        return this.goodsAmountExcludedTaxText;
    }

    public Integer getPharmacyNo() {
        return this.pharmacyNo;
    }

    public String getPharmacyName() {
        return this.pharmacyName;
    }


    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setGoodsKind(Long goodsKind) {
        this.goodsKind = goodsKind;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public void setGoodsCountText(String goodsCountText) {
        this.goodsCountText = goodsCountText;
    }

    public void setGoodsAmount(BigDecimal goodsAmount) {
        this.goodsAmount = goodsAmount;
    }

    public void setGoodsAmountText(String goodsAmountText) {
        this.goodsAmountText = goodsAmountText;
    }

    public void setGoodsAmountExcludedTax(BigDecimal goodsAmountExcludedTax) {
        this.goodsAmountExcludedTax = goodsAmountExcludedTax;
    }

    public void setGoodsAmountExcludedTaxText(String goodsAmountExcludedTaxText) {
        this.goodsAmountExcludedTaxText = goodsAmountExcludedTaxText;
    }

    public void setPharmacyNo(Integer pharmacyNo) {
        this.pharmacyNo = pharmacyNo;
    }

    public void setPharmacyName(String pharmacyName) {
        this.pharmacyName = pharmacyName;
    }

}
