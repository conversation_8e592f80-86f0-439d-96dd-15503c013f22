package cn.abc.flink.stat.service.cis.hospital.domain;



import java.math.BigDecimal;


/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-03-02 18:18
 **/
public class MedicalInsuranceHospitalPatient {
    /**
     * 住院单Id v2_patientorder_hospital表ID
     */
    private String id;  
    /**
     * 患者Id
     */
    private String patientId;  
    /**
     * 身份证号码
     */
    private String idCardNo;
    /**
     * 患者姓名
     */
    private String name; 
    /**
     * 人员类别
     */
    private String userCategory; 
    /**
     * 个人编号
     */
    private String personalNo; 
    /**
     * 结算类型
     */
    private String chargeType;  
    /**
     * 责任医生id
     */
    private String directDoctorId; 
    /**
     * 登记医生id
     */
    private String registerDoctorId;  
    /**
     * 登记护士id
     */
    private String registerNurseId;  
    /**
     * 责任医生
     */
    private String directDoctorName; 
    /**
     * 登记医生
     */
    private String registerDoctorName;  
    /**
     * 登记护士
     */
    private String registerNurseName;  
    /**
     * 状态
     */
    private String hospitalStatus; 
    /**
     * 入院时间
     */
    private String registerTime;  
    /**
     * 出院时间
     */
    private String dischargeTime;  
    /**
     * 期间在院天数
     */
    private Integer duraHospitalDays; 
    /**
     * 期间医疗服务费
     */
    private Double duraMedicalServiceFee;  
    /**
     * 总在院天数
     */
    private Integer allHospitalDays;  
    /**
     * 总医疗服务费
     */
    private Double allMedicalServiceFee;  
    /**
     * 成本
     */
    private Double costPrice;  
    /**
     * 期间成本
     */
    private Double partCostPrice;
    /**
     * 期间总费用
     */
    private Double partFee;

    /**
     * 期间院内总费用
     */
    private Double partInHospitalFee;

    /**
     * 评估等级
     */
    private String assessmentLevel;

    /**
     * hospital_sheet_id v2_charge_hospital_sheet表id
     */
    private BigDecimal hospitalSheetId;

     //结算信息
    //v2
    /**
     * 费用总额
     */
    private Double totalAmount;
    /**
     * 全额统筹
     */
    private Double fullFundPay;
    /**
     * 部分统筹
     */
    private Double partFundPay;
    /**
     * 自费总额
     */
    private Double totalSelfPay;
    /**
     * 乙类自理
     */
    private Double secondSelfPay;

    /**
     * 期间外诊费用
     */
    private Double totalPeriodExpenses;

    /**
     * 外诊总费用
     */
    private Double totalExpenses;


    /**
     * 累计费用
     */
    private Double cumulativeFee;
    /**
     * 累计费用
     */
    private Double inHospitalFee;

    /**
     * 医疗费统筹
     */
    private Double medicaOverallFee; 
    /**
     * 照护费
     */
    private Double nurseFee;  
    /**
     * 救助金
     */
    private Double reliefFund;  
    /**
     * 个人支付
     */
    private Double personalPayFee; 
    /**
     * 个人现金
     */
    private Double personalMoney;

    /**
     * @Description: 设置默认值
     * @param
     * @return
     * @Author: zs
     * @Date: 2023/4/26 15:19
     */
    public void setDefault() {
        this.idCardNo = isNull(this.idCardNo) ? "-" : this.idCardNo;
        this.name = isNull(this.name) ? "-" : this.name;
        this.userCategory = isNull(this.userCategory) ? "-" : this.userCategory;
        this.personalNo = isNull(this.personalNo) ? "-" : this.personalNo;
        this.chargeType = isNull(this.chargeType) ? "-" : this.chargeType;
        this.directDoctorName = isNull(this.directDoctorName) ? "-" : this.directDoctorName;
        this.registerDoctorName = isNull(this.registerDoctorName) ? "-" : this.registerDoctorName;
        this.registerNurseName = isNull(this.registerNurseName) ? "-" : this.registerNurseName;
        this.hospitalStatus = isNull(this.hospitalStatus) ? "-" : this.hospitalStatus;
        this.registerTime = isNull(this.registerTime) ? "-" : this.registerTime;
        this.dischargeTime = isNull(this.dischargeTime) ? "-" : this.dischargeTime;
        this.assessmentLevel = isNull(this.assessmentLevel) ? "-" : this.assessmentLevel;
    }
    /**
     * @Description: 设置默认值
     * @param
     * @return
     * @Author: zs
     * @Date: 2023/4/26 15:19
     */
    public boolean isNull(String value) {
        boolean result = false;
        if (value == null || value.trim().length() == 0) {
            result = true;
        }
        return result;
    }


    public String getId() {
        return this.id;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getIdCardNo() {
        return this.idCardNo;
    }

    public String getName() {
        return this.name;
    }

    public String getUserCategory() {
        return this.userCategory;
    }

    public String getPersonalNo() {
        return this.personalNo;
    }

    public String getChargeType() {
        return this.chargeType;
    }

    public String getDirectDoctorId() {
        return this.directDoctorId;
    }

    public String getRegisterDoctorId() {
        return this.registerDoctorId;
    }

    public String getRegisterNurseId() {
        return this.registerNurseId;
    }

    public String getDirectDoctorName() {
        return this.directDoctorName;
    }

    public String getRegisterDoctorName() {
        return this.registerDoctorName;
    }

    public String getRegisterNurseName() {
        return this.registerNurseName;
    }

    public String getHospitalStatus() {
        return this.hospitalStatus;
    }

    public String getRegisterTime() {
        return this.registerTime;
    }

    public String getDischargeTime() {
        return this.dischargeTime;
    }

    public Integer getDuraHospitalDays() {
        return this.duraHospitalDays;
    }

    public Double getDuraMedicalServiceFee() {
        return this.duraMedicalServiceFee;
    }

    public Integer getAllHospitalDays() {
        return this.allHospitalDays;
    }

    public Double getAllMedicalServiceFee() {
        return this.allMedicalServiceFee;
    }

    public Double getCostPrice() {
        return this.costPrice;
    }

    public Double getPartCostPrice() {
        return this.partCostPrice;
    }

    public Double getPartFee() {
        return this.partFee;
    }

    public Double getPartInHospitalFee() {
        return this.partInHospitalFee;
    }

    public String getAssessmentLevel() {
        return this.assessmentLevel;
    }

    public BigDecimal getHospitalSheetId() {
        return this.hospitalSheetId;
    }

    public Double getTotalAmount() {
        return this.totalAmount;
    }

    public Double getFullFundPay() {
        return this.fullFundPay;
    }

    public Double getPartFundPay() {
        return this.partFundPay;
    }

    public Double getTotalSelfPay() {
        return this.totalSelfPay;
    }

    public Double getSecondSelfPay() {
        return this.secondSelfPay;
    }

    public Double getTotalPeriodExpenses() {
        return this.totalPeriodExpenses;
    }

    public Double getTotalExpenses() {
        return this.totalExpenses;
    }

    public Double getCumulativeFee() {
        return this.cumulativeFee;
    }

    public Double getInHospitalFee() {
        return this.inHospitalFee;
    }

    public Double getMedicaOverallFee() {
        return this.medicaOverallFee;
    }

    public Double getNurseFee() {
        return this.nurseFee;
    }

    public Double getReliefFund() {
        return this.reliefFund;
    }

    public Double getPersonalPayFee() {
        return this.personalPayFee;
    }

    public Double getPersonalMoney() {
        return this.personalMoney;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setUserCategory(String userCategory) {
        this.userCategory = userCategory;
    }

    public void setPersonalNo(String personalNo) {
        this.personalNo = personalNo;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public void setDirectDoctorId(String directDoctorId) {
        this.directDoctorId = directDoctorId;
    }

    public void setRegisterDoctorId(String registerDoctorId) {
        this.registerDoctorId = registerDoctorId;
    }

    public void setRegisterNurseId(String registerNurseId) {
        this.registerNurseId = registerNurseId;
    }

    public void setDirectDoctorName(String directDoctorName) {
        this.directDoctorName = directDoctorName;
    }

    public void setRegisterDoctorName(String registerDoctorName) {
        this.registerDoctorName = registerDoctorName;
    }

    public void setRegisterNurseName(String registerNurseName) {
        this.registerNurseName = registerNurseName;
    }

    public void setHospitalStatus(String hospitalStatus) {
        this.hospitalStatus = hospitalStatus;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public void setDischargeTime(String dischargeTime) {
        this.dischargeTime = dischargeTime;
    }

    public void setDuraHospitalDays(Integer duraHospitalDays) {
        this.duraHospitalDays = duraHospitalDays;
    }

    public void setDuraMedicalServiceFee(Double duraMedicalServiceFee) {
        this.duraMedicalServiceFee = duraMedicalServiceFee;
    }

    public void setAllHospitalDays(Integer allHospitalDays) {
        this.allHospitalDays = allHospitalDays;
    }

    public void setAllMedicalServiceFee(Double allMedicalServiceFee) {
        this.allMedicalServiceFee = allMedicalServiceFee;
    }

    public void setCostPrice(Double costPrice) {
        this.costPrice = costPrice;
    }

    public void setPartCostPrice(Double partCostPrice) {
        this.partCostPrice = partCostPrice;
    }

    public void setPartFee(Double partFee) {
        this.partFee = partFee;
    }

    public void setPartInHospitalFee(Double partInHospitalFee) {
        this.partInHospitalFee = partInHospitalFee;
    }

    public void setAssessmentLevel(String assessmentLevel) {
        this.assessmentLevel = assessmentLevel;
    }

    public void setHospitalSheetId(BigDecimal hospitalSheetId) {
        this.hospitalSheetId = hospitalSheetId;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public void setFullFundPay(Double fullFundPay) {
        this.fullFundPay = fullFundPay;
    }

    public void setPartFundPay(Double partFundPay) {
        this.partFundPay = partFundPay;
    }

    public void setTotalSelfPay(Double totalSelfPay) {
        this.totalSelfPay = totalSelfPay;
    }

    public void setSecondSelfPay(Double secondSelfPay) {
        this.secondSelfPay = secondSelfPay;
    }

    public void setTotalPeriodExpenses(Double totalPeriodExpenses) {
        this.totalPeriodExpenses = totalPeriodExpenses;
    }

    public void setTotalExpenses(Double totalExpenses) {
        this.totalExpenses = totalExpenses;
    }

    public void setCumulativeFee(Double cumulativeFee) {
        this.cumulativeFee = cumulativeFee;
    }

    public void setInHospitalFee(Double inHospitalFee) {
        this.inHospitalFee = inHospitalFee;
    }

    public void setMedicaOverallFee(Double medicaOverallFee) {
        this.medicaOverallFee = medicaOverallFee;
    }

    public void setNurseFee(Double nurseFee) {
        this.nurseFee = nurseFee;
    }

    public void setReliefFund(Double reliefFund) {
        this.reliefFund = reliefFund;
    }

    public void setPersonalPayFee(Double personalPayFee) {
        this.personalPayFee = personalPayFee;
    }

    public void setPersonalMoney(Double personalMoney) {
        this.personalMoney = personalMoney;
    }

}
