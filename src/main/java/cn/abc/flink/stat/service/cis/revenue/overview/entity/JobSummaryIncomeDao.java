package cn.abc.flink.stat.service.cis.revenue.overview.entity;

import cn.abc.flink.stat.dimension.domain.V2Patient;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;


import java.math.BigDecimal;
import java.util.Map;

public class JobSummaryIncomeDao {
    private String patientId;
    private String patientName;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal totalAmount;

    /**
     * 共用字段
     */
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal slicesAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal particlesAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal airPharmacyAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal examinationAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal consultAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal materialsAmount;

    /**
     * 普通诊所的特殊字段
     */
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal registrationAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal westAndChinMedicineAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal executeAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal otherChargeAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal processAndDeliveryAmount;
    /**
     * 护理费，医院管家才有
     */
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal nurseAmount;

    /**
     * 口腔管家的特殊字段
     */
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal treatAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal inspectAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal commodityAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal westMedicineAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal chinMedicineAmount;
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal deliveryAmount;

    /**
     *
     */
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private Integer count;

    /**
     * 套餐金额
     */
    @JsonSerialize(nullsUsing = CustomerBigDecimalSerialize.class)
    private BigDecimal composeAmount;

    public void setPatientNameFromMap(Map<String, V2Patient> patientMap) {
        if (this.patientId != null) {
            V2Patient p = patientMap.get(this.patientId);
            if ( p != null) {
                this.patientName = p.getName();
            } else {
                this.patientName = "匿名患者";
            }
        }
    }


    public void setPatientNameWhenTotalData () {
        this.patientName = "共"+(this.count != null ? this.count : 0)+"条";
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public BigDecimal getTotalAmount() {
        return this.totalAmount;
    }

    public BigDecimal getSlicesAmount() {
        return this.slicesAmount;
    }

    public BigDecimal getParticlesAmount() {
        return this.particlesAmount;
    }

    public BigDecimal getAirPharmacyAmount() {
        return this.airPharmacyAmount;
    }

    public BigDecimal getExaminationAmount() {
        return this.examinationAmount;
    }

    public BigDecimal getConsultAmount() {
        return this.consultAmount;
    }

    public BigDecimal getMaterialsAmount() {
        return this.materialsAmount;
    }

    public BigDecimal getRegistrationAmount() {
        return this.registrationAmount;
    }

    public BigDecimal getWestAndChinMedicineAmount() {
        return this.westAndChinMedicineAmount;
    }

    public BigDecimal getExecuteAmount() {
        return this.executeAmount;
    }

    public BigDecimal getOtherChargeAmount() {
        return this.otherChargeAmount;
    }

    public BigDecimal getProcessAndDeliveryAmount() {
        return this.processAndDeliveryAmount;
    }

    public BigDecimal getNurseAmount() {
        return this.nurseAmount;
    }

    public BigDecimal getTreatAmount() {
        return this.treatAmount;
    }

    public BigDecimal getInspectAmount() {
        return this.inspectAmount;
    }

    public BigDecimal getCommodityAmount() {
        return this.commodityAmount;
    }

    public BigDecimal getWestMedicineAmount() {
        return this.westMedicineAmount;
    }

    public BigDecimal getChinMedicineAmount() {
        return this.chinMedicineAmount;
    }

    public BigDecimal getDeliveryAmount() {
        return this.deliveryAmount;
    }

    public Integer getCount() {
        return this.count;
    }

    public BigDecimal getComposeAmount() {
        return this.composeAmount;
    }


    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public void setSlicesAmount(BigDecimal slicesAmount) {
        this.slicesAmount = slicesAmount;
    }

    public void setParticlesAmount(BigDecimal particlesAmount) {
        this.particlesAmount = particlesAmount;
    }

    public void setAirPharmacyAmount(BigDecimal airPharmacyAmount) {
        this.airPharmacyAmount = airPharmacyAmount;
    }

    public void setExaminationAmount(BigDecimal examinationAmount) {
        this.examinationAmount = examinationAmount;
    }

    public void setConsultAmount(BigDecimal consultAmount) {
        this.consultAmount = consultAmount;
    }

    public void setMaterialsAmount(BigDecimal materialsAmount) {
        this.materialsAmount = materialsAmount;
    }

    public void setRegistrationAmount(BigDecimal registrationAmount) {
        this.registrationAmount = registrationAmount;
    }

    public void setWestAndChinMedicineAmount(BigDecimal westAndChinMedicineAmount) {
        this.westAndChinMedicineAmount = westAndChinMedicineAmount;
    }

    public void setExecuteAmount(BigDecimal executeAmount) {
        this.executeAmount = executeAmount;
    }

    public void setOtherChargeAmount(BigDecimal otherChargeAmount) {
        this.otherChargeAmount = otherChargeAmount;
    }

    public void setProcessAndDeliveryAmount(BigDecimal processAndDeliveryAmount) {
        this.processAndDeliveryAmount = processAndDeliveryAmount;
    }

    public void setNurseAmount(BigDecimal nurseAmount) {
        this.nurseAmount = nurseAmount;
    }

    public void setTreatAmount(BigDecimal treatAmount) {
        this.treatAmount = treatAmount;
    }

    public void setInspectAmount(BigDecimal inspectAmount) {
        this.inspectAmount = inspectAmount;
    }

    public void setCommodityAmount(BigDecimal commodityAmount) {
        this.commodityAmount = commodityAmount;
    }

    public void setWestMedicineAmount(BigDecimal westMedicineAmount) {
        this.westMedicineAmount = westMedicineAmount;
    }

    public void setChinMedicineAmount(BigDecimal chinMedicineAmount) {
        this.chinMedicineAmount = chinMedicineAmount;
    }

    public void setDeliveryAmount(BigDecimal deliveryAmount) {
        this.deliveryAmount = deliveryAmount;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public void setComposeAmount(BigDecimal composeAmount) {
        this.composeAmount = composeAmount;
    }

}
