package cn.abc.flink.stat.service.his.achievement.cost.domain;



import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/2 3:27 下午
 */
public class HisRevenuesCostSheBaoPayModeRes {
    /**
     * '医保统筹'
     */
    private BigDecimal fundPaySumAmt;
    /**
     * '医保个账'
     */
    private BigDecimal acctPay;
    /**
     * '医保其他基金'
     */
    private BigDecimal othPay;

    public void pretty(Map<String, BigDecimal> payModeResMap) {
        if (this.fundPaySumAmt != null) {
            payModeResMap.put("医保统筹", this.fundPaySumAmt);
        }
        if (this.acctPay != null) {
            payModeResMap.put("医保个账", this.acctPay);
        }
        if (this.othPay != null) {
            payModeResMap.put("医保其他基金", this.othPay);
        }
    }

    public BigDecimal getFundPaySumAmt() {
        return this.fundPaySumAmt;
    }

    public BigDecimal getAcctPay() {
        return this.acctPay;
    }

    public BigDecimal getOthPay() {
        return this.othPay;
    }


    public void setFundPaySumAmt(BigDecimal fundPaySumAmt) {
        this.fundPaySumAmt = fundPaySumAmt;
    }

    public void setAcctPay(BigDecimal acctPay) {
        this.acctPay = acctPay;
    }

    public void setOthPay(BigDecimal othPay) {
        this.othPay = othPay;
    }

}
