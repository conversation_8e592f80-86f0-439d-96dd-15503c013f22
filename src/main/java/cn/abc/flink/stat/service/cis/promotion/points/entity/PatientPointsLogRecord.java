package cn.abc.flink.stat.service.cis.promotion.points.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.math.BigDecimal;

@ApiModel("积分流水积分构成记录实体")
public class PatientPointsLogRecord {

    @ApiModelProperty("项目id")
    private String goodsId;

    @ApiModelProperty("项目名称")
    private String goodsName;

    @ApiModelProperty("消费金额")
    private BigDecimal receivedFee;

    @ApiModelProperty("积分数")
    private BigDecimal point;

    @ApiModelProperty("活动id")
    private Long promotionId;

    @ApiModelProperty("活动名称")
    private String promotionName;


    public String getGoodsId() {
        return this.goodsId;
    }

    public String getGoodsName() {
        return this.goodsName;
    }

    public BigDecimal getReceivedFee() {
        return this.receivedFee;
    }

    public BigDecimal getPoint() {
        return this.point;
    }

    public Long getPromotionId() {
        return this.promotionId;
    }

    public String getPromotionName() {
        return this.promotionName;
    }


    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public void setReceivedFee(BigDecimal receivedFee) {
        this.receivedFee = receivedFee;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public void setPromotionId(Long promotionId) {
        this.promotionId = promotionId;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

}
