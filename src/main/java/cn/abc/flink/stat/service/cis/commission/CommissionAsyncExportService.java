package cn.abc.flink.stat.service.cis.commission;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.service.cis.commission.domain.CommissionParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2022/3/1 5:59 下午
 */
@Component
public class CommissionAsyncExportService implements BaseAsyncExportInterface {

    @Autowired
    private CommissionService commissionService;

    @Override
    public String getKey() {
        return "commission";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        return "提成报表" + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params)
            throws ParseException, ExecutionException, InterruptedException {
        CommissionParam param = new CommissionParam();
        String headerClinicId = (String) MapUtils.isExistsAndReturn(params, "headerClinicId");
        Integer clinicType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String viewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        String hisType = (String) MapUtils.isExistsAndReturn(params, "headerHisType");
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        String employeeId = (String) MapUtils.isExistsAndReturn(params, "employeeId");
        String headerEmployeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        Boolean isAll = (Boolean) MapUtils.isExistsAndReturn(params, "isAll");
        Integer supportedBusiness = Double.valueOf((double) params.get("headerSupportedBusiness")).intValue();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        param.setBeginDate(beginDate);
        param.setEndDate(endDate);
        param.setEmployeeId(employeeId);
        param.setIsAll(isAll);
        AbcCisBaseQueryParams abcParams = new AbcCisBaseQueryParams(chainId, headerClinicId, headerEmployeeId, viewMode, clinicType);
        abcParams.setClinicType(clinicType.toString());
        param.setSingleStore(viewMode, clinicType.toString());
        param.setChainId(abcParams.getChainId());
        param.setClinicId(clinicId);
        param.setHisType(hisType);
        param.setSupportedBusiness(supportedBusiness);
        param.initBeginDateAndEndDate();
        param.setParams(abcParams);
        List<ExcelUtils.AbcExcelSheet> sheets = commissionService.export(param);
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
