package cn.abc.flink.stat.service.cis.execute.bill.domain;

import cn.abc.flink.stat.common.ABCNumberUtils;


import java.math.BigDecimal;
/**
 * @Description:
 * @return 
 * @Author: zs
 * @Date: 2022/8/7 14:58
 */
public class ExecuteBillPersonTotal {
    private Integer personCount;
    private String personCountText;
    private BigDecimal totalCount;
    private String totalCountText;
    private BigDecimal receivedPrice;
    private String receivedPriceText;
    private BigDecimal executedCount;
    private String executedCountText;
    private BigDecimal executeRatio;
    private String executeRatioText;

    /**
     * @Description: 
     * @param 
     * @return 
     * @return cn.abc.flink.stat.service.cis.execute.bill.domain.ExecuteBillPersonTotal
     * @Author: zs
     * @Date: 2022/8/7 14:58
     */
    public ExecuteBillPersonTotal pretty() {
        if (personCount != null) {
            personCountText = personCount.toString();
        } else {
            personCountText = "0";
        }
        totalCountText = ABCNumberUtils.positiveRound0Text(totalCount);
        receivedPriceText = ABCNumberUtils.positiveRound2Text(receivedPrice);
        executedCountText = ABCNumberUtils.positiveRound0Text(executedCount);
        executeRatioText = ABCNumberUtils.positiveRound2RatioText(executeRatio);
        return this;
    }

    public Integer getPersonCount() {
        return this.personCount;
    }

    public String getPersonCountText() {
        return this.personCountText;
    }

    public BigDecimal getTotalCount() {
        return this.totalCount;
    }

    public String getTotalCountText() {
        return this.totalCountText;
    }

    public BigDecimal getReceivedPrice() {
        return this.receivedPrice;
    }

    public String getReceivedPriceText() {
        return this.receivedPriceText;
    }

    public BigDecimal getExecutedCount() {
        return this.executedCount;
    }

    public String getExecutedCountText() {
        return this.executedCountText;
    }

    public BigDecimal getExecuteRatio() {
        return this.executeRatio;
    }

    public String getExecuteRatioText() {
        return this.executeRatioText;
    }


    public void setPersonCount(Integer personCount) {
        this.personCount = personCount;
    }

    public void setPersonCountText(String personCountText) {
        this.personCountText = personCountText;
    }

    public void setTotalCount(BigDecimal totalCount) {
        this.totalCount = totalCount;
    }

    public void setTotalCountText(String totalCountText) {
        this.totalCountText = totalCountText;
    }

    public void setReceivedPrice(BigDecimal receivedPrice) {
        this.receivedPrice = receivedPrice;
    }

    public void setReceivedPriceText(String receivedPriceText) {
        this.receivedPriceText = receivedPriceText;
    }

    public void setExecutedCount(BigDecimal executedCount) {
        this.executedCount = executedCount;
    }

    public void setExecutedCountText(String executedCountText) {
        this.executedCountText = executedCountText;
    }

    public void setExecuteRatio(BigDecimal executeRatio) {
        this.executeRatio = executeRatio;
    }

    public void setExecuteRatioText(String executeRatioText) {
        this.executeRatioText = executeRatioText;
    }

}
