package cn.abc.flink.stat.service.cis.membercharge;


import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.request.params.MemberChargeParam;
import cn.abc.flink.stat.service.cis.membercharge.domain.MemberChargeDetailInfo;
import cn.abc.flink.stat.service.cis.membercharge.domain.MemberChargeSummaryRsp;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/8/23 1:43 下午
 */
@Component
public class MemberChargeExportHandler {

    /**
     *
     * @param response response
     * @param flag flag
     * @param param param
     * @param summary summary
     * @param detail detail
     */
    public void export(HttpServletResponse response, boolean flag, MemberChargeParam param,
                       List<MemberChargeSummaryRsp> summary, List<MemberChargeDetailInfo> detail) {
//        beginDate = beginDate.substring(0, 10);
//        endDate = endDate.substring(0, 10);
        String fileName = "会员充值业绩统计" + param.getBeginDate() + "_" + param.getEndDate();
        byte[] bytes = fileName.getBytes(StandardCharsets.UTF_8);
        try {
            fileName = new String(bytes, "ISO8859-1");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList();

        ExcelUtils.AbcExcelSheet summarySheet = new ExcelUtils.AbcExcelSheet();
        summarySheet.setName("汇总");
        summarySheet.setData(makeSummaryExcelBody(summary, flag));
        summarySheet.setSheetDefinition(makeSummaryExcelHead(flag));
        sheets.add(summarySheet);


        ExcelUtils.AbcExcelSheet detailSheet = new ExcelUtils.AbcExcelSheet();
        detailSheet.setName("明细");
        detailSheet.setData(makeDetailExcelBody(detail, flag));
        detailSheet.setSheetDefinition(makeDetailExcelHead(flag));
        sheets.add(detailSheet);

        try {
            ExcelUtils.export(fileName, response, sheets);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     *
     * @param flag flag
     * @return List
     */
    public List makeSummaryExcelHead(boolean flag) {
        List<List<String>> header = new ArrayList<>();
        for (String label : flag ? MemberChargeHeaderHandler.getSummaryHeaderLabel()
                : MemberChargeHeaderHandler.getSummaryHeaderLabelV1()) {
            header.add(Lists.newArrayList(label));
        }
        return header;
    }

    /**
     *
     * @param summary summary
     * @param flag flag
     * @return List
     */
    public List makeSummaryExcelBody(List<MemberChargeSummaryRsp> summary, boolean flag) {
        List<List<Object>> lines = new ArrayList<>();
        if (summary == null) {
            return lines;
        }
        for (MemberChargeSummaryRsp d : summary) {
            List<Object> row = new ArrayList<>();
            if (flag) {
                row.add(d.getClinicName());
            }
            row.add(d.getSellerName());
            row.add(d.getChargeCount());
            row.add(d.getChargePrincipalText());
            row.add(d.getChargePresentText());
            lines.add(row);
        }
        return lines;
    }

    /**
     *
     * @param flag flag
     * @return List
     */
    public List makeDetailExcelHead(boolean flag) {
        List<List<String>> header = new ArrayList<>();
        for (String label : flag ? MemberChargeHeaderHandler.getDetailHeaderLabel()
                : MemberChargeHeaderHandler.getDetailHeaderLabelV1()) {
            header.add(Lists.newArrayList(label));
        }
        return header;
    }

    /**
     *
     * @param detail detail
     * @param flag flag
     * @return List
     */
    public List makeDetailExcelBody(List<MemberChargeDetailInfo> detail, boolean flag) {
        List<List<Object>> lines = new ArrayList<>();
        if (detail == null) {
            return lines;
        }
        for (MemberChargeDetailInfo d : detail) {
            List<Object> row = new ArrayList<>();
            if (flag) {
                row.add(d.getClinicName());
            }
            row.add(d.getSellerName());
            row.add(d.getChargeTime());
            row.add(d.getChargePrincipalText());
            row.add(d.getChargePresentText());
            row.add(d.getMemberName());
            row.add(d.getMemberMobile());
            row.add(d.getTollCollectorName());
            row.add(d.getRemark());
            lines.add(row);
        }
        return lines;
    }


}
