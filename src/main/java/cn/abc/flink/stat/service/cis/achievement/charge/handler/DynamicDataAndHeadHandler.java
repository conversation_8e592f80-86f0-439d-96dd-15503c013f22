package cn.abc.flink.stat.service.cis.achievement.charge.handler;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeFeeClassify;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 动态导出
 *
 * <AUTHOR>
 */
public final class DynamicDataAndHeadHandler {
    private static final Logger logger = LoggerFactory.getLogger(DynamicDataAndHeadHandler.class);
    private DynamicDataAndHeadHandler() {
    }

    private static final Integer PRECISION_DEFAULT_VALUE = 2;

    private static final BigDecimal PERCENT_VALUE = BigDecimal.valueOf(100);

    /**
     * 动态导出人员数据
     *
     * @param goodsRsp goodsRsp
     * @return 导出数据
     */
    public static List<List<Object>> dynamicPersonnelList(V2StatResponse goodsRsp) {
        List lists = goodsRsp.getData();
        //人员导出新增合计
        Object summary = goodsRsp.getSummary();
        if (summary != null) {
            lists.add(summary);
        }
        List<TableHeaderEmployeeItem> header = goodsRsp.getHeader();

        return ExcelUtils.exportMapData(lists, header);
    }

    /**
     * 获取费用分类的title
     *
     * @param pojoList 费用分类
     * @return 分类title
     */
    public static List<String> getFeeTypeTitleList(List<AchievementChargeFeeClassify> pojoList) {
        List<String> list = new ArrayList<>();
        for (AchievementChargeFeeClassify pojo : pojoList) {

            if (pojo.getChildren().size() > 0) {
                for (Map<String, String> entity : pojo.getChildren()) {
                    list.add(entity.get("value"));
                }
            } else {
                list.add(pojo.getValue());
            }
        }
        return list;
    }

    /**
     * 单据的导出数据处理
     *
     * @param goodsRsp    单据数据和表头
     * @return 导出数据
     */
    public static List<List<Object>> dynamicList(V2StatResponse goodsRsp) {
        List lists = goodsRsp.getData();
        List<TableHeaderEmployeeItem> finalHeaderItems = new ArrayList<>();
        if (goodsRsp.getHeader() != null) {
            for (TableHeaderEmployeeItem tableHeaderItem : goodsRsp.getHeader()) {
                // 处理就诊推荐和首诊来源
                patientSourceHeadSplit(tableHeaderItem, finalHeaderItems);
            }
        }
        List<List<Object>> data = ExcelUtils.exportMapData(lists, finalHeaderItems);
        return data;
    }

    /**
     * 获取人员head
     *
     * @param tableHeaderItems 自定义表头数据
     * @return header
     */
    public static List<List<String>> dynamicPersonnelHead(List<TableHeaderEmployeeItem> tableHeaderItems) {
        List<List<String>> headList = new ArrayList<>();
        headList = ExcelUtils.exportTableHeader(tableHeaderItems);
        return headList;
    }

    /**
     * 获取项目header
     *
     * @param tableHeaderItems 表头
     * @return 项目 header
     */
    public static List<List<String>> getProductHead(List<TableHeaderEmployeeItem> tableHeaderItems) {
        List<List<String>> headList;
        headList = ExcelUtils.exportTableHeader(tableHeaderItems);
        return headList;
    }

    /**
     * 获取导出项目数据
     *
     * @param goodsRsp    项目数据
     * @return 导出数据
     */
    public static List<List<Object>> getProductData(V2StatResponse goodsRsp) {
        List lists = goodsRsp.getData();
        List<TableHeaderEmployeeItem> header = goodsRsp.getHeader();
        List<List<Object>> data = ExcelUtils.exportDataV2(lists, header);
        logger.info("项目数据data=" + data);
        return data;
    }

    /**
     * 获取明细header
     *
     * @param tableHeaderItems 表头
     * @return 项目 header
     */
    public static List<List<String>> getDetailHead(List<TableHeaderEmployeeItem> tableHeaderItems) {
        List<List<String>> headList;
        List<TableHeaderEmployeeItem> finalHeaderItems = new ArrayList<>();
        for (TableHeaderEmployeeItem tableHeaderItem : tableHeaderItems) {
            // 处理就诊推荐和首诊来源
            patientSourceHeadSplit(tableHeaderItem, finalHeaderItems);
        }
        headList = ExcelUtils.exportTableHeader(finalHeaderItems);
        return headList;
    }

    /**
     * 处理就诊推荐
     * @param tableHeaderItems
     * @return
     */
    public static List<TableHeaderEmployeeItem> getDetailHeadExport(List<TableHeaderEmployeeItem> tableHeaderItems) {
        List<TableHeaderEmployeeItem> finalHeaderItems = new ArrayList<>();
        for (TableHeaderEmployeeItem tableHeaderItem : tableHeaderItems) {
            // 处理就诊推荐和首诊来源
            patientSourceHeadSplit(tableHeaderItem, finalHeaderItems);
        }
        return finalHeaderItems;
    }

    /**
     * 获取导出明细数据
     *
     * @param detailRsp    项目数据
     * @return 导出数据
     */
    public static List<List<Object>> getDetailData(V2StatResponse detailRsp) {
        List lists = detailRsp.getData();
        List<TableHeaderEmployeeItem> finalHeaderItems = new ArrayList<>();
        if (detailRsp.getHeader() != null) {
            for (TableHeaderEmployeeItem tableHeaderItem : detailRsp.getHeader()) {
                // 处理就诊推荐和首诊来源
                patientSourceHeadSplit(tableHeaderItem, finalHeaderItems);
            }
        }
        List<List<Object>> data = ExcelUtils.exportDataV2(lists, finalHeaderItems);
        return data;
    }


    /**
     * 获取单据header
     *
     * @param tableHeaderItems 表头
     * @return 项目 header
     */
    public static List<List<String>> dynamicHead(List<TableHeaderEmployeeItem> tableHeaderItems) {
        List<List<String>> headList = new ArrayList<>();
        List<TableHeaderEmployeeItem> finalHeaderItems = new ArrayList<>();
        for (TableHeaderEmployeeItem tableHeaderItem : tableHeaderItems) {
            // 处理就诊推荐和首诊来源
            patientSourceHeadSplit(tableHeaderItem, finalHeaderItems);
        }
        headList = ExcelUtils.exportTableHeader(finalHeaderItems);
        return headList;
    }

    /**
     * 本次推荐，首诊来源header处理
     * @param tableHeaderItem 处理前的headerItems
     * @param finalHeaderItems 处理后的headerItems
     */
    private static void patientSourceHeadSplit(TableHeaderEmployeeItem tableHeaderItem,
                                               List<TableHeaderEmployeeItem> finalHeaderItems) {
            // 处理就诊推荐和首诊来源
            if ("visitSourceType".equals(tableHeaderItem.getProp())) {
                TableHeaderEmployeeItem visitSourceType1 = new TableHeaderEmployeeItem();
                BeanUtil.copyProperties(tableHeaderItem, visitSourceType1);
                visitSourceType1.setPosition(tableHeaderItem.getPosition());
                visitSourceType1.setProp("visitSourceType1");
                visitSourceType1.setLabel("本次推荐(一级)");
                TableHeaderEmployeeItem visitSourceType2 = new TableHeaderEmployeeItem();
                BeanUtil.copyProperties(tableHeaderItem, visitSourceType2);
                visitSourceType2.setPosition(tableHeaderItem.getPosition() + 1);
                visitSourceType2.setProp("visitSourceType2");
                visitSourceType2.setLabel("本次推荐(二级)");
                TableHeaderEmployeeItem visitSourceType3 = new TableHeaderEmployeeItem();
                BeanUtil.copyProperties(tableHeaderItem, visitSourceType3);
                visitSourceType3.setPosition(tableHeaderItem.getPosition() + 1);
                visitSourceType3.setProp("visitSourceFrom");
                visitSourceType3.setLabel("本次推荐(三级)");
                finalHeaderItems.add(visitSourceType1);
                finalHeaderItems.add(visitSourceType2);
                finalHeaderItems.add(visitSourceType3);
            } else if ("patientSourceType".equals(tableHeaderItem.getProp())) {
                TableHeaderEmployeeItem patientSourceType1 = new TableHeaderEmployeeItem();
                BeanUtil.copyProperties(tableHeaderItem, patientSourceType1);
                patientSourceType1.setPosition(tableHeaderItem.getPosition());
                patientSourceType1.setProp("patientSourceType1");
                patientSourceType1.setLabel("首诊来源(一级)");
                TableHeaderEmployeeItem patientSourceType2 = new TableHeaderEmployeeItem();
                BeanUtil.copyProperties(tableHeaderItem, patientSourceType2);
                patientSourceType2.setPosition(tableHeaderItem.getPosition() + 1);
                patientSourceType2.setProp("patientSourceType2");
                patientSourceType2.setLabel("首诊来源(二级)");
                TableHeaderEmployeeItem patientSourceFrom = new TableHeaderEmployeeItem();
                BeanUtil.copyProperties(tableHeaderItem, patientSourceFrom);
                patientSourceFrom.setPosition(tableHeaderItem.getPosition() + 1);
                patientSourceFrom.setProp("patientSourceFrom");
                patientSourceFrom.setLabel("首诊来源(三级)");
                finalHeaderItems.add(patientSourceType1);
                finalHeaderItems.add(patientSourceType2);
                finalHeaderItems.add(patientSourceFrom);
            } else {
                finalHeaderItems.add(tableHeaderItem);
            }

    }

    /**
     * 科室header
     *
     * @param headers 科室header
     * @return 科室header
     */
    public static List<List<String>> departmentHeader(List<TableHeaderEmployeeItem> headers) {
        List<List<String>> headList = new ArrayList<>();
        headList = ExcelUtils.exportTableHeader(headers);
        return headList;
    }

    /**
     * 科室导出数据
     *
     * @param goodsRsp 科室header和data
     * @return 科室导出数据
     */
    public static List<List<Object>> getDepartmentData(V2StatResponse goodsRsp) {
        List list = goodsRsp.getData();
        Object summary = goodsRsp.getSummary();
        if (summary != null) {
            list.add(summary);
        }
        List<TableHeaderEmployeeItem> headers = goodsRsp.getHeader();
        List<List<Object>> data = ExcelUtils.exportMapData(list, headers);
        return data;
    }
}
