package cn.abc.flink.stat.service.his.physical.examination.charge;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.service.his.physical.examination.charge.domain.HisPeChargeParam;
import io.swagger.annotations.ApiModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Component
@ApiModel("营收统计-收费明细-团体普通统计异步导出")
public class HisPeChargeGroupAsyncExportService implements BaseAsyncExportInterface {
    private final Logger logger = LoggerFactory.getLogger(HisPeChargeGroupAsyncExportService.class);

    @Autowired
    private HisPeChargeService hisPeChargeService;

    @Override
    public String getKey() {
        return "his-pe-charge-group";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        return "团体普通体检" + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params)
            throws ParseException, ExecutionException, InterruptedException {
        // 参数构造
        String chainId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "chainId"));
        String clinicId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "clinicId"));
        Integer clinicNodeType = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "headerClinicType"));
        String viewMode = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerViewMode"));
        String headerEmployeeId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerEmployeeId"));
        String beginDate = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "beginDate"));
        String endDate = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "endDate"));
        String cashierId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "cashierId"));
        String sellerId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "sellerId"));
        String payModes = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "payModes"));
        String patientId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "patientId"));
        Integer chargeType = ConvertUtils.getAsInteger(MapUtils.isExistsAndReturn(params, "chargeType"));
        Long peOrganId = ConvertUtils.getAsLong(MapUtils.isExistsAndReturn(params, "peOrganId"));
        HisPeChargeParam reqParams = new HisPeChargeParam();
        reqParams.initAbcCisBaseQueryParams(chainId, clinicId, headerEmployeeId, viewMode, clinicNodeType);
        reqParams.initBeginDateAndEndDate(beginDate, endDate);
        reqParams.setSingleStore(viewMode, String.valueOf(clinicNodeType));
        if (!reqParams.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            reqParams.setClinicId(reqParams.getHeaderClinicId());
        }
        reqParams.setCashierId(cashierId);
        reqParams.setSellerId(sellerId);
        reqParams.setPatientId(patientId);
        reqParams.setPeOrganId(peOrganId);
        reqParams.setPayModes(payModes);
        reqParams.setChargeType(chargeType);
        List<ExcelUtils.AbcExcelSheet> sheets = hisPeChargeService.groupChargeAsyncExport(reqParams);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ExcelUtils.export(byteArrayOutputStream, sheets);
        return byteArrayOutputStream;
    }
}
