package cn.abc.flink.stat.service.cis.commission.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/27 下午8:09
 */
@ApiModel(value = "CommissionTotal", description = "提成汇总")
public class CommissionTotal {

    @ApiModelProperty(value = "总数量")
    private Long totalCount = 0L;

    @ApiModelProperty(value = "提成金额")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "人数")
    private Integer totalEmployeeCount = 0;

    @ApiModelProperty(value = "人员列表")
    private Set<String> employeeSet = new HashSet<>();

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalEmployeeCount() {
        return totalEmployeeCount;
    }

    public void setTotalEmployeeCount(Integer totalEmployeeCount) {
        this.totalEmployeeCount = totalEmployeeCount;
    }

    public Set<String> getEmployeeSet() {
        return employeeSet;
    }

    public void setEmployeeSet(Set<String> employeeSet) {
        this.employeeSet = employeeSet;
    }
}
