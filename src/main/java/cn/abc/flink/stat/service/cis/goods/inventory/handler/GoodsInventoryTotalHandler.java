package cn.abc.flink.stat.service.cis.goods.inventory.handler;

import cn.abc.flink.stat.common.GoodsInventoryActionEnum;
import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.model.TempTable;
import cn.abc.flink.stat.common.request.params.GoodsInfoParam;
import cn.abc.flink.stat.common.response.StatResponseKeyDataItem;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresGoodsInventoryMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.service.cis.goods.inventory.domain.GoodsInventoryParam;
import com.beust.jcommander.internal.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/8/10 8:10 下午
 */
@Component
public class GoodsInventoryTotalHandler {
    private static final Logger logger = LoggerFactory.getLogger(GoodsInventoryTotalHandler.class);

    @Resource
    private HologresGoodsInventoryMapper hologresGoodsInventoryMapper;

    @Autowired
    private DimensionQuery dimensionQuery;

    @Autowired
    private StoreUtils storeUtils;

    @Value("${stat.inventory-begin-end-date}")
    private String inventoryBeginEndConfine;

    /**
     * @param param    param
     * @param goodsIds goodsIds
     * @return StatResponseTotal
     */
    public StatResponseTotal goods(GoodsInventoryParam param, List<String> goodsIds) {
        long startTime = System.currentTimeMillis();
        ArrayList<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        StatResponseTotal total = new StatResponseTotal();
        total.setTemplate("共%s条数据");
        if (param.getStockHaveChange() == 1) {
            goodsIds = hologresGoodsInventoryMapper.selectStockHaveChangeGoods(TableUtils.getCisTable(),
                    new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                            param.getFeeType1(), param.getFeeType2(), null, null,
                            param.getBeginDate(), param.getEndDate(), param.getPharmacyNo(), param.getHint(),
                            param.getBeginDateDs(), param.getEndDateDs(), param.getPharmacyNosSql(),
                            param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds(), param.getIsContainsAllotInInside(),
                            Integer.valueOf(param.getHisType())), goodsIds, feeTypeIdsList);
            total.setData(Collections.singletonList(goodsIds.size()));
            total.setCount((long) goodsIds.size());
            return total;
        }
        TempTable tempTable = TempTable.getTempTable(param.getBeginDate(), param.getEndDate(), inventoryBeginEndConfine);
        Long count = param.getPharmacyType() == 0 ? dimensionQuery.queryGoodsInfoCount(TableUtils.getCisGoodsTable(),
                new GoodsInfoParam(param.getChainId(), param.getClinicId(), param.getPharmacyType(),
                        param.getFeeType1(), param.getFeeType2(), param.getOffset(), param.getLimit(),
                        param.getParams().getHeaderClinicId(), param.getPharmacyNo(), param.getPharmacyNosSql(),
                        param.getBaseMedicineTypeSql(), param.getProfitCategoryTypeIds()), goodsIds, feeTypeIdsList)
                : hologresGoodsInventoryMapper.selectGoodsTotal(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList, tempTable);
        if (count == null) {
            total.setData(Lists.newArrayList("-"));
        } else {
            total.setData(Lists.newArrayList(count));
            total.setCount(count);
        }
        logger.error("进销存统计-汇总查询total耗时:{}毫秒", System.currentTimeMillis() - startTime);
        return total;
    }

    /**
     * @param param    param
     * @param goodsIds goodsIds
     * @return StatResponseTotal
     */
    public StatResponseTotal record(GoodsInventoryParam param, List<String> goodsIds) {
        long startTime = System.currentTimeMillis();
        if (param.getActions() != null && param.getActions().size() == 0) {
            param.setActions(null);
        }
        ArrayList<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        Long count = 0L;
        if (param.getDimension() == 0) {
            count = hologresGoodsInventoryMapper.selectRecordTotal(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList,
                    GoodsInventoryActionEnum.toActions(param.getActions(), param.getHisType()));
        } else {
            count = hologresGoodsInventoryMapper.selectRecordByGoodsTotal(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList,
                    GoodsInventoryActionEnum.toActions(param.getActions(), param.getHisType()));
        }
        StatResponseTotal total = new StatResponseTotal();
        total.setTemplate("共%s条数据");
        if (count == null) {
            total.setData(Lists.newArrayList("-"));
        } else {
            total.setData(Lists.newArrayList(count));
            total.setCount(count);
        }
        logger.error("进销存统计-明细查询total耗时:{}毫秒", System.currentTimeMillis() - startTime);
        return total;
    }

    /**
     * feeTypeIf转换
     *
     * @param param -
     * @return List
     */
    private ArrayList<Long> getFeeTypeIdsList(GoodsInventoryParam param) {

        ArrayList<Long> feeTypeIdsList = new ArrayList<>();
        if (param.getFeeTypeIds() != null && param.getFeeTypeIds().length() > 0) {
            String[] split = param.getFeeTypeIds().split(",");
            for (String s : split) {
                feeTypeIdsList.add(Long.parseLong(s));
            }
        }
        return feeTypeIdsList;
    }

    /**
     * @param param      -
     * @param reportName -
     * @return -
     */
    public List<StatResponseKeyDataItem> reportKeyDate(GoodsInventoryParam param, String reportName) {
        List<StatResponseKeyDataItem> keyDataItems = new ArrayList<>();
        if (param.getDispensaryType().equals(HisTypeEnum.CAHIN.getTypeNumber())) {
            Organ organ = dimensionQuery.queryOrgan(param.getChainId());
            StatResponseKeyDataItem organReportNameItem = new StatResponseKeyDataItem();
            String organReportName = organ.getShortName() == null || organ.getShortName().trim().equals("")
                    ? organ.getName() : organ.getShortName();
            organReportNameItem.setText(organReportName + reportName);
            keyDataItems.add(organReportNameItem);
        } else {
            Organ organ = dimensionQuery.queryOrgan(param.getClinicId());
            StatResponseKeyDataItem organReportNameItem = new StatResponseKeyDataItem();
            String organReportName = organ.getShortName() == null || organ.getShortName().trim().equals("")
                    ? organ.getName() : organ.getShortName();
            organReportNameItem.setText(organReportName + reportName);
            keyDataItems.add(organReportNameItem);
            if (param.getPharmacyNo() != null) {
                Map<String, Map<Integer, String>> pharmacyNoNameMap = dimensionQuery.queryPharmacyNameByChainIdAndClinicId(param.getChainId(), param.getClinicId(), param.getPharmacyType());
                StatResponseKeyDataItem pharmacyNoNameItem = new StatResponseKeyDataItem();
                String pharmacyNoName = "库房：" + pharmacyNoNameMap.get(param.getClinicId()).get(param.getPharmacyNo());
                pharmacyNoNameItem.setText(pharmacyNoName);
                keyDataItems.add(pharmacyNoNameItem);
            }
            if (param.getPharmacyNos() != null) {
                Map<String, Map<Integer, String>> pharmacyNoNameMap = dimensionQuery.queryPharmacyNameByChainIdAndClinicId(param.getChainId(), param.getClinicId(), param.getPharmacyType());
                StringBuilder sb = new StringBuilder();
                sb.append("库房：");
                for (int i = 0; i < param.getPharmacyNos().size(); i++) {
                    if (i == param.getPharmacyNos().size() - 1) {
                        sb.append(pharmacyNoNameMap.get(param.getClinicId()).get(Integer.valueOf(param.getPharmacyNos().get(i))));
                    } else {
                        sb.append(pharmacyNoNameMap.get(param.getClinicId()).get(Integer.valueOf(param.getPharmacyNos().get(i))));
                        sb.append(",");
                    }
                }
                StatResponseKeyDataItem pharmacyNoNameItem = new StatResponseKeyDataItem();
                pharmacyNoNameItem.setText(sb.toString());
                keyDataItems.add(pharmacyNoNameItem);
            }
        }
        return keyDataItems;
    }

    /**
     * 查询进销存明细未结算数量
     *
     * @param param    进销存param
     * @param goodsIds 药品id
     * @return 未结算数量
     */
    public Long noSettleRecord(GoodsInventoryParam param, List<String> goodsIds) {
        if (param.getActions() != null && param.getActions().size() == 0) {
            param.setActions(null);
        }
        ArrayList<Long> feeTypeIdsList = getFeeTypeIdsList(param);
        Long count;
        if (param.getDimension() == 0) {
            count = hologresGoodsInventoryMapper.selectNoSettleRecordTotal(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList,
                    GoodsInventoryActionEnum.toActions(param.getActions(), param.getHisType()));
        } else {
            count = hologresGoodsInventoryMapper.selectNoSettleRecordByGoodsTotal(TableUtils.getCisTable(), param, goodsIds, feeTypeIdsList,
                    GoodsInventoryActionEnum.toActions(param.getActions(), param.getHisType()));
        }
        return count;
    }
}
