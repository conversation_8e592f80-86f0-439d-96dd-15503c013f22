package cn.abc.flink.stat.service.his.achievement.cost.handler;

import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.pojo.EmployeeResp;
import cn.abc.flink.stat.pojo.KeyValuePojo;
import cn.abc.flink.stat.service.cis.invoice.entity.InvoiceDetailEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyBudgetIndexEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyChineseDoseEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyMedicalInsuranceEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailyPayTypeEntity;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueChargedDailySummaryDao;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueSheBaoVo;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueVo;
import cn.abc.flink.stat.service.cis.selection.pojo.DepartmentResp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisReciptSummaryResp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostPhysicalExaminationDayReportRsp;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportDailyExportResult;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportExportResult;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeDao;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueCostReportPayModeTotalRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenueKeyValuePojo;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargeDepositTotal;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostChargeReportRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostFeeTypeRes;
import cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostReqParams;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.ShebaoStatSummaryReportRsp;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.his.achievement.cost.handler
 * @Author: zs
 * @CreateTime: 2023-12-02  16:36
 * @Description: 结算统计handler
 * @Version: 1.0
 */
@Component
public class HisRevenuesCostHandler {
    private static Logger LOGGER = LoggerFactory.getLogger(HisRevenuesCostHandler.class);
    private static final ObjectMapper SOBJECTMAPPER = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * @param
     * @param summy         科室纬度数据
     * @param feeTypeList   费用类型
     * @param verticalSummy 横向合计
     * @param feeType       费用类型
     * @return
     * @Description: 结算统计-费用类型统计
     * @Author: zs
     * @Date: 2023/12/2 16:39
     */
    public void feeTypePolymerization(V2StatResponse response,
                                      List<TableHeaderEmployeeItem> headerEmployeeItems,
                                      List<HisRevenuesCostFeeTypeRes> summy,
                                      List<HisRevenuesCostFeeTypeRes> feeTypeList,
                                      List<HisRevenuesCostFeeTypeRes> verticalSummy,
                                      Map<Long, V2GoodsFeeType> feeType,
                                      Map<String, String> department) {
        List<Map<Object, Object>> result = new ArrayList<>();
        Set<Long> feeTypeIds = new HashSet<>();
        Map<String, Map<String, BigDecimal>> departmentMap = feeTypeGroupByDepartment(feeTypeList, feeTypeIds);
        for (HisRevenuesCostFeeTypeRes dto : summy) {
            if (departmentMap.containsKey(dto.getDepartmentId())) {
                String departmentName = department.getOrDefault(dto.getDepartmentId(), "-");
                dto.setDepartmentName(departmentName);
                Map<String, BigDecimal> hisRevenuesCostFeeTypeRes = departmentMap.get(dto.getDepartmentId());
                try {
                    Map<Object, Object> describe = BeanUtils.describe(dto);
                    describe.putAll(hisRevenuesCostFeeTypeRes);
                    result.add(describe);
                } catch (Exception e) {
                    LOGGER.error("{}", e);
                }
            }
        }
        if (result.size() > 0) {
            Map<Object, Object> vertical = new HashMap<>();
            vertical.put("departmentName", "合计");
            BigDecimal total = BigDecimal.ZERO;
            for (HisRevenuesCostFeeTypeRes dto : verticalSummy) {
                if (dto.getFeeTypeId() == null) {
                    dto.setFeeTypeId(99999999l);
                }
                vertical.put(dto.getFeeTypeId().toString(), dto.getFeeTypeAmount()
                        .setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
                total = total.add(dto.getFeeTypeAmount());
            }
            vertical.put("summy", total.setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
            response.setSummary(vertical);
//            result.add(vertical);
        }
        if (result.size() > 0) {
            for (Map<Object, Object> dto : result) {
                for (Long feeTypeId : feeTypeIds) {
                    if (!dto.containsKey(feeTypeId.toString())) {
                        dto.put(feeTypeId.toString(), BigDecimal.ZERO.setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
                    }
                }
            }
        }
        response.setData(result);
        StatResponseTotal statResponseTotal = new StatResponseTotal();
        statResponseTotal.setCount((long) result.size());
        response.setTotal(statResponseTotal);
        revenuesFeeTypeTable(headerEmployeeItems, feeTypeIds, feeType);
        response.setHeader(headerEmployeeItems);
    }

    /**
     * @param
     * @param feeTypeList -
     * @return
     * @return java.util.Map<java.lang.String, java.util.List < cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostFeeTypeRes>>
     * @Description: 将费用类型集合按照科室聚合
     * @Author: zs
     * @Date: 2023/12/2 16:52
     */
    public Map<String, Map<String, BigDecimal>> feeTypeGroupByDepartment(List<HisRevenuesCostFeeTypeRes> feeTypeList, Set<Long> feeTypeIds) {
        Map<String, Map<String, BigDecimal>> result = new HashMap<>();
        if (feeTypeList != null && feeTypeList.size() > 0) {
            for (HisRevenuesCostFeeTypeRes res : feeTypeList) {
                if (res.getFeeTypeId() == null) {
                    res.setFeeTypeId(99999999l);
                }
                if (res.getDepartmentId() != null) {
                    if (result.containsKey(res.getDepartmentId())) {
                        result.get(res.getDepartmentId()).put(res.getFeeTypeId().toString(), res.getFeeTypeAmount()
                                .setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
                    } else {
                        Map<String, BigDecimal> map = new HashMap<>();
                        map.put(res.getFeeTypeId().toString(), res.getFeeTypeAmount()
                                .setScale(CommonConstants.NUMBER_TWO, BigDecimal.ROUND_HALF_UP));
                        result.put(res.getDepartmentId(), map);
                    }
                    feeTypeIds.add(res.getFeeTypeId());
                }
            }
        }
        return result;
    }

    /**
     * @param
     * @param feeTypeIds -
     * @return
     * @return java.util.Map<java.lang.String, java.util.List < cn.abc.flink.stat.service.his.achievement.cost.domain.HisRevenuesCostFeeTypeRes>>
     * @Description: 将费用类型放到表头中
     * @Author: zs
     * @Date: 2023/12/2 16:52
     */
    public void revenuesFeeTypeTable(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                     Set<Long> feeTypeIds,
                                     Map<Long, V2GoodsFeeType> feeType) {
        if (feeTypeIds != null && feeTypeIds.size() > 0) {
            for (Long id : feeTypeIds) {
                if (id == null) {
                    continue;
                }
                TableHeaderEmployeeItem item = new TableHeaderEmployeeItem();
                item.setKey(id.toString());
                item.setProp(id.toString());
                item.setType("money");
                item.setAlign("right");
                String name = "未分类";
                if (feeType.containsKey(id)) {
                    name = feeType.get(id).getName();
                }
                item.setLabel(name);
                headerEmployeeItems.add(item);
            }
        }
    }

    /**
     * 处理押金统计合计
     *
     * @param depositTotal 押金统计total
     * @return 押金统计合计
     */
    public StatResponseTotal processingDepositTotal(HisRevenuesCostChargeDepositTotal depositTotal) {
        StatResponseTotal statResponseTotal = new StatResponseTotal();
        depositTotal.pretty();
        statResponseTotal.setCount(depositTotal.getCount());
        List<Object> totalCount = Arrays.asList(
                depositTotal.getCount(),
                depositTotal.getPayDepositAmount(),
                depositTotal.getRefundOfDepositAmount(),
                depositTotal.getDepositSettlementAmount(),
                depositTotal.getRefundOfDepositSettlementAmount(),
                depositTotal.getSettlementSurplusRefundAmount());
        statResponseTotal.setTemplate("数据总数 %s，缴押金 %s，退押金 %s，押金结算 %s，押金退结算 %s，结算余退 %s");
        statResponseTotal.setData(totalCount);
        return statResponseTotal;
    }

    public List<HisRevenueCostReportExportResult> reportSummaryHandler(HisRevenuesCostChargeReportRes chargeReportRes) {
        ArrayList<HisRevenueCostReportExportResult> revenueExportResults = new ArrayList<>();
        HisRevenueCostReportExportResult classifyResult = new HisRevenueCostReportExportResult("现金结算实收", "0", "医保结算实收", "0", "押金结算抵扣", "0");
        HisRevenueCostReportExportResult resultTotal = new HisRevenueCostReportExportResult("结算合计", "0", "", "", "", "");
        if (chargeReportRes != null) {
            classifyResult.setSecondLine(chargeReportRes.getCashSettlementReceivedPrice().toString());
            classifyResult.setFourthLine(chargeReportRes.getMedicalInsuranceSettlementReceivedPrice()
                    .setScale(CommonConstants.NUMBER_THREE, RoundingMode.HALF_UP).toString());
            classifyResult.setSixthLine(chargeReportRes.getDepositSettlementDeductPrice()
                    .setScale(CommonConstants.NUMBER_THREE, RoundingMode.HALF_UP).toString());
            resultTotal.setSecondLine(chargeReportRes.getSettlementTotalPrice().toString());
        }
        HisRevenueCostReportExportResult result1 = new HisRevenueCostReportExportResult("", "", "", "", "", "");
        revenueExportResults.add(classifyResult);
        revenueExportResults.add(resultTotal);
        revenueExportResults.add(result1);
        return revenueExportResults;
    }

    public List<HisRevenueCostReportExportResult> reportKeyValueHandler(List<KeyValuePojo> keyValuePojos, String name) {
        List<HisRevenueCostReportExportResult> revenueExportResults = new ArrayList<>();
        HisRevenueCostReportExportResult result = new HisRevenueCostReportExportResult(name, "", "", "", "", "");
        revenueExportResults.add(result);
        int size = keyValuePojos.size();
        if (keyValuePojos.size() > 0) {
            boolean isLoop = true;
            boolean isFirst = true;
            int x = 0;
            int y = 1;
            int z = CommonConstants.NUMBER_TWO;
            while (isLoop) {
                //定义三个基本容器
                KeyValuePojo xKeyValue = new KeyValuePojo();
                KeyValuePojo yKeyValue = new KeyValuePojo();
                KeyValuePojo zKeyValue = new KeyValuePojo();
                //不是第一次就将角标往后移动
                if (!isFirst) {
                    //后移角标
                    x = x + CommonConstants.NUMBER_THREE;
                    y = y + CommonConstants.NUMBER_THREE;
                    z = z + CommonConstants.NUMBER_THREE;
                }
                isFirst = false;
                if (!(x > size - 1)) {
                    xKeyValue = keyValuePojos.get(x);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                if (!(y > size - 1)) {
                    yKeyValue = keyValuePojos.get(y);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                if (!(z > size - 1)) {
                    zKeyValue = keyValuePojos.get(z);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                //最后一个角标等于size就说明这是最后一次循环
                if (z == size - 1) {
                    isLoop = false;
                }
                HisRevenueCostReportExportResult result1 = new HisRevenueCostReportExportResult(
                        xKeyValue.getName() == null ? "" : xKeyValue.getName(),
                        xKeyValue.getValue() == null ? "" : xKeyValue.getValue().toString(),

                        yKeyValue.getName() == null ? "" : yKeyValue.getName(),
                        yKeyValue.getValue() == null ? "" : yKeyValue.getValue().toString(),

                        zKeyValue.getName() == null ? "" : zKeyValue.getName(),
                        zKeyValue.getValue() == null ? "" : zKeyValue.getValue().toString());
                revenueExportResults.add(result1);
            }
        }
        HisRevenueCostReportExportResult result1 = new HisRevenueCostReportExportResult("", "", "", "", "", "");
        revenueExportResults.add(result1);
        return revenueExportResults;
    }

    public List<HisRevenueCostReportDailyExportResult> reportKeyValueHandlerV2(List<HisRevenueKeyValuePojo> keyValuePojos, String name) {
        List<HisRevenueCostReportDailyExportResult> revenueExportResults = new ArrayList<>();
        HisRevenueCostReportDailyExportResult result = new HisRevenueCostReportDailyExportResult(name, "", "", "", "", "");
        revenueExportResults.add(result);
        int size = keyValuePojos.size();
        if (keyValuePojos.size() > 0) {
            boolean isLoop = true;
            boolean isFirst = true;
            int x = 0;
            int y = 1;
            int z = CommonConstants.NUMBER_TWO;
            while (isLoop) {
                //定义三个基本容器
                KeyValuePojo xKeyValue = new KeyValuePojo();
                KeyValuePojo yKeyValue = new KeyValuePojo();
                KeyValuePojo zKeyValue = new KeyValuePojo();
                //不是第一次就将角标往后移动
                if (!isFirst) {
                    //后移角标
                    x = x + CommonConstants.NUMBER_THREE;
                    y = y + CommonConstants.NUMBER_THREE;
                    z = z + CommonConstants.NUMBER_THREE;
                }
                isFirst = false;
                if (!(x > size - 1)) {
                    xKeyValue = keyValuePojos.get(x);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                if (!(y > size - 1)) {
                    yKeyValue = keyValuePojos.get(y);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                if (!(z > size - 1)) {
                    zKeyValue = keyValuePojos.get(z);
                } else {
                    //只要有一个角标越界就终止循环
                    isLoop = false;
                }
                //最后一个角标等于size就说明这是最后一次循环
                if (z == size - 1) {
                    isLoop = false;
                }
                HisRevenueCostReportDailyExportResult result1 = new HisRevenueCostReportDailyExportResult(
                        xKeyValue.getName() == null ? "" : xKeyValue.getName(),
                        xKeyValue.getValue() == null ? "" : xKeyValue.getValue().toString(),

                        yKeyValue.getName() == null ? "" : yKeyValue.getName(),
                        yKeyValue.getValue() == null ? "" : yKeyValue.getValue().toString(),

                        zKeyValue.getName() == null ? "" : zKeyValue.getName(),
                        zKeyValue.getValue() == null ? "" : zKeyValue.getValue().toString());
                revenueExportResults.add(result1);
            }
        }
        HisRevenueCostReportDailyExportResult result1 = new HisRevenueCostReportDailyExportResult("", "", "", "", "", "");
        revenueExportResults.add(result1);
        return revenueExportResults;
    }

    public List<HisRevenueKeyValuePojo> handleHisFeeTypeList(List<HisRevenuesCostFeeTypeRes> feeTypeRes,
                                                             Map<Long, V2GoodsFeeType> feeTypeMap) {
        Map<String, BigDecimal> feeTypeResMap = new HashMap<>();
        for (HisRevenuesCostFeeTypeRes fee : feeTypeRes) {
            String FeeName = "未指定";
            if (feeTypeMap.containsKey(fee.getFeeTypeId())) {
                FeeName = feeTypeMap.get(fee.getFeeTypeId()).getName();
            }
            feeTypeResMap.put(FeeName, feeTypeResMap.getOrDefault(FeeName, BigDecimal.ZERO).add(fee.getFeeTypeAmount()));
        }
        return feeTypeResMap.entrySet().stream()
                .map(entry -> new HisRevenueKeyValuePojo(entry.getKey(), entry.getValue().setScale(2, RoundingMode.HALF_UP)))
                .collect(Collectors.toList());
    }

    public List<HisRevenueKeyValuePojo> transFeeTypeData(List<RevenueVo> feeTypes) {
        List<HisRevenueKeyValuePojo> list = new ArrayList<>();
        feeTypes.forEach(data -> list.add(new HisRevenueKeyValuePojo(data.getName(), data.getValue())));
        return list;
    }

    public List<HisRevenueKeyValuePojo> mergeFeeData(List<RevenueVo> feeTypes,
                                                     List<HisRevenueKeyValuePojo> hisFeeTypes,
                                                     List<HisRevenueKeyValuePojo> physicalFeeTypes,
                                                     RevenueChargedDailySummaryDao summary,
                                                     HisRevenuesCostChargeReportRes hisSummary) {
        Map<String, HisRevenueKeyValuePojo> feeTypeMap = new HashMap<>();
        Map<String, HisRevenueKeyValuePojo> hisFeeTypeMap = new HashMap<>();
        Map<String, HisRevenueKeyValuePojo> physicalFeeTypeMap = new HashMap<>();
        if (feeTypes != null && !feeTypes.isEmpty()) {
            feeTypeMap = feeTypes.stream().collect(Collectors.toMap(RevenueVo::getName, v -> new HisRevenueKeyValuePojo(v.getName(), v.getValue())));
        }
        if (hisFeeTypes != null && !hisFeeTypes.isEmpty()) {
            hisFeeTypeMap = hisFeeTypes.stream().collect(Collectors.toMap(HisRevenueKeyValuePojo::getName, v -> v));
        }
        if (physicalFeeTypes != null && !physicalFeeTypes.isEmpty()) {
            physicalFeeTypeMap = physicalFeeTypes.stream().filter(t -> !"结算合计".equals(t.getName()))
                    .collect(Collectors.toMap(HisRevenueKeyValuePojo::getName, v -> v));
        }
        Map<String, HisRevenueKeyValuePojo> mergedMap = new HashMap<>();
        mergeMaps(mergedMap, feeTypeMap);
        mergeMaps(mergedMap, hisFeeTypeMap);
        mergeMaps(mergedMap, physicalFeeTypeMap);
        BigDecimal outpatientSum = summary == null ? BigDecimal.ZERO : summary.getTotalAmount();
        BigDecimal hospitalSum = hisSummary == null ? BigDecimal.ZERO : hisSummary.getSettlementTotalPrice();
        BigDecimal physicalFeeSum = new BigDecimal(mergedMap.remove("体检结算").getValue().toString());
        BigDecimal totalSum = outpatientSum.add(hospitalSum).add(physicalFeeSum);
        List<HisRevenueKeyValuePojo> res = mergedMap.values().stream().sorted(Comparator.comparing(KeyValuePojo::getName))
                .collect(Collectors.toList());
        res.add(0, new HisRevenueKeyValuePojo("结算合计", totalSum.toString(), Boolean.TRUE));
        res.add(0, new HisRevenueKeyValuePojo("体检结算", physicalFeeSum));
        res.add(0, new HisRevenueKeyValuePojo("住院结算", hospitalSum));
        res.add(0, new HisRevenueKeyValuePojo("门诊结算", outpatientSum));
        return res;
    }

    public void mergeChargeDailyData(Map<String, HisRevenueKeyValuePojo> mergedMap, HisRevenueKeyValuePojo pojo) {
        if (pojo == null) {
            return;
        }
        HisRevenueKeyValuePojo existingValue = mergedMap.get(pojo.getName());
        if (existingValue != null) {
            existingValue.setValue(new BigDecimal(pojo.getValue().toString()).add(new BigDecimal(existingValue.getValue().toString())));
        } else {
            mergedMap.put(pojo.getName(), pojo);
        }
    }

    public List<HisRevenueKeyValuePojo> mergePayModeData(List<RevenueVo> payModes,
                                                         List<HisRevenueKeyValuePojo> hisPayModes,
                                                         HisRevenueCostPhysicalExaminationDayReportRsp physicalRsp) {
        Map<String, HisRevenueKeyValuePojo> payModeMap = new HashMap<>();
        Map<String, HisRevenueKeyValuePojo> hisPayModeMap = new HashMap<>();
        Map<String, HisRevenueKeyValuePojo> physicalPayModeMap = new HashMap<>();
        if (payModes != null && !payModes.isEmpty()) {
            payModeMap = payModes.stream().collect(Collectors.toMap(RevenueVo::getName, v -> new HisRevenueKeyValuePojo(v.getName(), v.getValue())));
        }
        if (hisPayModes != null && !hisPayModes.isEmpty()) {
            hisPayModeMap = hisPayModes.stream().collect(Collectors.toMap(HisRevenueKeyValuePojo::getName, v -> v));
        }
        if (physicalRsp != null && physicalRsp.getPayModeList() != null && !physicalRsp.getPayModeList().isEmpty()) {
            physicalPayModeMap = physicalRsp.getPayModeList().stream().collect(Collectors.toMap(RevenueVo::getName, v -> new HisRevenueKeyValuePojo(v.getName(), v.getValue())));
        }
        Map<String, HisRevenueKeyValuePojo> mergedMap = new HashMap<>();
        mergeMaps(mergedMap, payModeMap);
        mergeMaps(mergedMap, hisPayModeMap);
        mergeMaps(mergedMap, physicalPayModeMap);
        return mergedMap.values().stream().sorted(Comparator.comparing(HisRevenueKeyValuePojo::getName)).collect(Collectors.toList());
    }

    private void mergeMaps(Map<String, HisRevenueKeyValuePojo> mergedMap, Map<String, HisRevenueKeyValuePojo> mapToMerge) {
        mapToMerge.forEach((key, value) -> mergeChargeDailyData(mergedMap, value));
    }


    public List<HisRevenueCostReportDailyExportResult> handleFeeTypeExportData(HisRevenuesCostReqParams param,
                                                                               List<HisRevenueKeyValuePojo> feeType) {
        Map<String, Object> feeMap = feeType.stream().collect(Collectors.toMap(HisRevenueKeyValuePojo::getName, HisRevenueKeyValuePojo::getValue));
        List<HisRevenueCostReportDailyExportResult> results = new ArrayList<>();
        results.add(new HisRevenueCostReportDailyExportResult("费用分类", "", "", "", "", ""));
        if (param.getBusinessScope() == null || param.getBusinessScope().size() == 3) {
            results.add(new HisRevenueCostReportDailyExportResult("门诊结算", feeMap.get("门诊结算").toString(), "住院结算", feeMap.get("住院结算").toString(), "体检结算", feeMap.get("体检结算").toString()));
            feeMap.remove("门诊结算");
            feeMap.remove("住院结算");
            feeMap.remove("体检结算");
        } else {
            if (param.getBusinessScope().contains(1)) {
                results.add(new HisRevenueCostReportDailyExportResult("门诊结算", feeMap.get("门诊结算").toString(), "充值合计", feeMap.get("充值合计").toString(), "", "'"));
                feeMap.remove("门诊结算");
                feeMap.remove("充值合计");
            }
            if (param.getBusinessScope().contains(2)) {
                results.add(new HisRevenueCostReportDailyExportResult("住院结算", feeMap.get("住院结算").toString(), "押金结算抵扣", feeMap.get("押金结算抵扣").toString(), "", "'"));
                feeMap.remove("住院结算");
                feeMap.remove("押金结算抵扣");
            }
            if (param.getBusinessScope().contains(3)) {
                results.add(new HisRevenueCostReportDailyExportResult("体检结算", feeMap.get("体检结算").toString(), "", "", "", ""));
                feeMap.remove("体检结算");
            }
        }
        results.add(new HisRevenueCostReportDailyExportResult("结算合计", feeMap.get("结算合计").toString(), "", "", "", ""));
        feeMap.remove("结算合计");
        List<HisRevenueKeyValuePojo> keyValuePojos = new ArrayList<>();
        feeMap.forEach((k, v) -> keyValuePojos.add(new HisRevenueKeyValuePojo(k, new BigDecimal(v.toString()))));
        results.addAll(reportKeyValueHandlerV2(keyValuePojos, ""));
        return results;
    }

    public List<HisRevenueCostReportDailyExportResult> handlePayModeData(HisRevenueCostReportPayModeRes payModeData, String type) {
        List<HisRevenueCostReportDailyExportResult> list = new ArrayList<>();
        if ("registration".equals(type)) {
            list.add(new HisRevenueCostReportDailyExportResult("收费方式", "市职工挂号", "市居民挂号", "自费及其他挂号", "市职工退号", "市居民退号", "自费及其他退号", "", "", ""));
        } else {
            list.add(new HisRevenueCostReportDailyExportResult("收费方式", "市职工收费", "市居民收费", "自费及其他收费", "市职工退费", "市居民退费", "自费及其他退费", "", "", ""));
        }
        if (payModeData.getData() == null) {
            return list;
        }
        List<HisRevenueCostReportPayModeDao> data = (List<HisRevenueCostReportPayModeDao>) payModeData.getData();
        for (HisRevenueCostReportPayModeDao datum : data) {
            list.add(new HisRevenueCostReportDailyExportResult(datum.getPayModeName(), datum.getCityEmployeePrice().toString(), datum.getCityResidentPrice().toString(),
                    datum.getSelfPayAndOtherPrice().toString(), datum.getCityEmployeeRefundPrice().toString(), datum.getCityResidentRefundPrice().toString(),
                    datum.getSelfPayAndOtherRefundPrice().toString(), "", "", ""));
        }
        HisRevenueCostReportPayModeDao summary = (HisRevenueCostReportPayModeDao) payModeData.getSummary();
        if (summary != null) {
            list.add(new HisRevenueCostReportDailyExportResult("合计", summary.getCityEmployeePrice().toString(), summary.getCityResidentPrice().toString(),
                    summary.getSelfPayAndOtherPrice().toString(), summary.getCityEmployeeRefundPrice().toString(), summary.getCityResidentRefundPrice().toString(),
                    summary.getSelfPayAndOtherRefundPrice().toString(), "", "", ""));
        }
        HisRevenueCostReportPayModeTotalRes total = (HisRevenueCostReportPayModeTotalRes) payModeData.getTotal();
        if ("registration".equals(type)) {
            list.add(new HisRevenueCostReportDailyExportResult("挂号数量:", total.getCount().toString(), "", "", "退号数量:", total.getRefundCount().toString(), "", "", "", ""));
        } else {
            list.add(new HisRevenueCostReportDailyExportResult("收费数量:", total.getCount().toString(), "退费数量", total.getRefundCount().toString(), "实收金额:", total.getReceivedPrice().toString(), "退费金额", total.getRefundPrice().toString(), "", ""));
        }
        list.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", "", "", "", "", ""));
        return list;
    }

    /**
     * 根据业务范围处理支付方式导出数据
     * @param reqParams 请求参数
     * @param payModeByBusiness 支付方式数据
     * @return 导出结果列表
     */
    public List<HisRevenueCostReportDailyExportResult> handlePayModeByBusinessExportData(HisRevenuesCostReqParams reqParams, V2StatResponse payModeByBusiness) {
        List<HisRevenueCostReportDailyExportResult> list = new ArrayList<>();
        if (reqParams.getBusinessScope() == null || reqParams.getBusinessScope().isEmpty()) {
            reqParams.setBusinessScope(Arrays.asList(1, 2, 3));
        }
        
        // 生成表头行
        List<String> headers = getPayModeByBusinessHeaders(reqParams.getBusinessScope());
        list.add(new HisRevenueCostReportDailyExportResult(
                headers.get(0), headers.size() > 1 ? headers.get(1) : "", 
                headers.size() > 2 ? headers.get(2) : "", headers.size() > 3 ? headers.get(3) : "", 
                headers.size() > 4 ? headers.get(4) : "", headers.size() > 5 ? headers.get(5) : "",
                headers.size() > 6 ? headers.get(6) : "", headers.size() > 7 ? headers.get(7) : "", 
                headers.size() > 8 ? headers.get(8) : "", headers.size() > 9 ? headers.get(9) : ""));
        
        // 如果没有数据，直接返回只有表头的列表
        if (payModeByBusiness == null || payModeByBusiness.getData() == null) {
            return list;
        }
        
        // 处理数据行
        List<HisReciptSummaryResp> dataList = (List<HisReciptSummaryResp>) payModeByBusiness.getData();
        for (HisReciptSummaryResp data : dataList) {
            list.add(processPayModeData(reqParams.getBusinessScope(),  data, data.getPayModeName() != null ? data.getPayModeName() : ""));
        }
        
        // 添加合计行（如果有summary）
        if (payModeByBusiness.getSummary() != null) {
            HisReciptSummaryResp summary = (HisReciptSummaryResp) payModeByBusiness.getSummary();
            list.add(processPayModeData(reqParams.getBusinessScope(), summary, "合计"));
        }
        
        // 添加空行
        list.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", "", "", "", "", ""));
        return list;
    }
    
    /**
     * 处理支付方式数据，格式化金额为两位小数
     * @param businessScope 业务范围
     * @param data 数据对象
     * @param firstColumnValue 第一列的值
     * @return 格式化后的导出结果
     */
    private HisRevenueCostReportDailyExportResult processPayModeData(List<Integer> businessScope, HisReciptSummaryResp data, String firstColumnValue) {
        String[] values = new String[10];
        values[0] = firstColumnValue;
        
        int index = 1;
        // 根据业务范围添加相应数据
        if (businessScope.contains(1)) {
            values[index++] = formatAmount(data.getOutpatientAmount());
        }
        
        if (businessScope.contains(2)) {
            values[index++] = formatAmount(data.getDischargeAmount());
            values[index++] = formatAmount(data.getDepositSettleRefundAmount());
            values[index++] = formatAmount(data.getDepositAmount());
        }
        
        if (businessScope.contains(3)) {
            values[index++] = formatAmount(data.getPeChargeAmount());
        }
        
        if (businessScope.contains(1)) {
            values[index++] = formatAmount(data.getMemberAmount());
            values[index++] = formatAmount(data.getPromotionCardAmount());
        }
        
        // 合计列
        values[index++] = formatAmount(data.getTotalAmount());
        
        // 确保数组填满10个元素
        for (int i = 0; i < values.length; i++) {
            if (values[i] == null) {
                values[i] = "";
            }
        }
        
        return new HisRevenueCostReportDailyExportResult(
                values[0], values[1], values[2], values[3], values[4], 
                values[5], values[6], values[7], values[8], values[9]);
    }
    
    /**
     * 格式化金额为两位小数，四舍五入
     * @param amount 金额
     * @return 格式化后的金额字符串
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        return amount.setScale(2, RoundingMode.HALF_UP).toString();
    }
    
    /**
     * 根据业务范围生成表头
     * @param businessScope 业务范围列表
     * @return 表头列表
     */
    private List<String> getPayModeByBusinessHeaders(List<Integer> businessScope) {
        List<String> headers = new ArrayList<>();
        
        // 第一列始终是收费方式
        headers.add("收费方式");
        
        // 根据业务范围添加相应列
        if (businessScope.contains(1)) {
            headers.add("门诊");
        }
        
        if (businessScope.contains(2)) {
            headers.add("住院/住院结算");
            headers.add("住院/结算余退");
            headers.add("住院押金");
        }
        
        if (businessScope.contains(3)) {
            headers.add("体检");
        }
        
        if (businessScope.contains(1)) {
            headers.add("会员充值");
            headers.add("卡项充值");
        }
        
        // 最后两列始终是合计和空列
        headers.add("合计");
        headers.add("");
        
        // 确保至少有 10 个元素，不足的用空字符串补齐
        while (headers.size() < 10) {
            headers.add("");
        }
        return headers;
    }


    public List<HisRevenueCostReportDailyExportResult> handleSheBaoGuoBiao(List<RevenueSheBaoVo> revenueSheBaoVos) {
        List<HisRevenueCostReportDailyExportResult> revenueExportResults = new ArrayList<>();
        HisRevenueCostReportDailyExportResult result1 = new HisRevenueCostReportDailyExportResult("医保", "", "", "", "", "");
        revenueExportResults.add(result1);
        //循环处理数据
        if (revenueSheBaoVos != null && revenueSheBaoVos.size() >= CommonConstants.NUMBER_THREE) {
            RevenueSheBaoVo sheBaoVo = revenueSheBaoVos.get(0) == null ? new RevenueSheBaoVo() : revenueSheBaoVos.get(0);
            RevenueSheBaoVo sheBaoVo1 = revenueSheBaoVos.get(1) == null ? new RevenueSheBaoVo() : revenueSheBaoVos.get(1);
            RevenueSheBaoVo sheBaoVo2 = revenueSheBaoVos.get(CommonConstants.NUMBER_TWO) == null ? new RevenueSheBaoVo() : revenueSheBaoVos.get(CommonConstants.NUMBER_TWO);
            revenueExportResults.add(new HisRevenueCostReportDailyExportResult(sheBaoVo.getName(),
                    "医保支付", sheBaoVo.getMedicalInsurancePay() == null ? "0"
                    : sheBaoVo.getMedicalInsurancePay().toString(),
                    sheBaoVo1.getName(),
                    "医保支付",
                    sheBaoVo1.getMedicalInsurancePay() == null ? "0"
                            : sheBaoVo1.getMedicalInsurancePay().toString()));
            revenueExportResults.add(new HisRevenueCostReportDailyExportResult("",
                    "现金支付", sheBaoVo.getCashPay() == null ? "0"
                    : sheBaoVo.getCashPay().toString(), "", "现金支付",
                    sheBaoVo1.getCashPay() == null ? "0" : sheBaoVo1.getCashPay().toString()));
            revenueExportResults.add(new HisRevenueCostReportDailyExportResult(sheBaoVo2.getName(),
                    "医保支付", sheBaoVo2.getMedicalInsurancePay() == null ? "0"
                    : sheBaoVo2.getMedicalInsurancePay().toString(), "", "", ""));
            revenueExportResults.add(new HisRevenueCostReportDailyExportResult("",
                    "现金支付", sheBaoVo2.getCashPay() == null ? "0"
                    : sheBaoVo.getCashPay().toString(), "", "", ""));
        }
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", ""));
        return revenueExportResults;
    }

    public List<HisRevenueCostReportDailyExportResult> handleSheBaoHangZhou(RevenueChargedDailyMedicalInsuranceEntity sheBaoHangZhou) {
        List<HisRevenueCostReportDailyExportResult> revenueExportResults = new ArrayList<>();
        //市医保普通门诊
        RevenueChargedDailyPayTypeEntity generalClinic = sheBaoHangZhou.getGeneralClinic();
        //市医保规定病种门诊
        RevenueChargedDailyPayTypeEntity specifiedDiseaseClinic = sheBaoHangZhou.getSpecifiedDiseaseClinic();
        //省医保门诊
        RevenueChargedDailyPayTypeEntity provinceMedicalInsuranceClinic = sheBaoHangZhou.getProvinceMedicalInsuranceClinic();
        //省内异地
        RevenueChargedDailyPayTypeEntity provinceInnerMedicalInsuranceClinic = sheBaoHangZhou.getProvinceInnerMedicalInsuranceClinic();
        //省外异地
        RevenueChargedDailyPayTypeEntity provinceOutMedicalInsuranceClinic = sheBaoHangZhou.getProvinceOutMedicalInsuranceClinic();
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("就诊类型", "", "", "", "", ""));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("市医保普通门诊",
                "现金支付", generalClinic.getCashPay() == null ? "0" : generalClinic.getCashPay().toString(),
                "市医保规定病种门诊",
                "现金支付",
                specifiedDiseaseClinic.getCashPay() == null ? "0" : specifiedDiseaseClinic.getCashPay().toString()));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("",
                "医保支付",
                generalClinic.getMedicalInsurancePay() == null ? "0"
                        : generalClinic.getMedicalInsurancePay().toString(),
                "",
                "医保支付",
                specifiedDiseaseClinic.getMedicalInsurancePay() == null ? "0"
                        : specifiedDiseaseClinic.getMedicalInsurancePay().toString()));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("省医保普通门诊",
                "现金支付", provinceMedicalInsuranceClinic.getCashPay() == null ? "0"
                : provinceMedicalInsuranceClinic.getCashPay().toString(),
                "省内异地",
                "现金",
                provinceInnerMedicalInsuranceClinic.getCashPay() == null ? "0"
                        : provinceInnerMedicalInsuranceClinic.getCashPay().toString()));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("",
                "医保支付",
                provinceMedicalInsuranceClinic.getMedicalInsurancePay() == null ? "0"
                        : provinceMedicalInsuranceClinic.getMedicalInsurancePay().toString(),
                "", "医保",
                provinceInnerMedicalInsuranceClinic.getMedicalInsurancePay() == null ? "0"
                        : provinceInnerMedicalInsuranceClinic.getMedicalInsurancePay().toString()));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("省外异地",
                "现金", provinceOutMedicalInsuranceClinic.getCashPay() == null ? "0"
                : provinceOutMedicalInsuranceClinic.getCashPay().toString(),
                "自费患者",
                "现金支付",
                sheBaoHangZhou.getSelfFundPatients() == null ? "0" : sheBaoHangZhou.getSelfFundPatients().toString()));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "医保",
                provinceOutMedicalInsuranceClinic.getMedicalInsurancePay() == null ? "0"
                        : provinceOutMedicalInsuranceClinic.getMedicalInsurancePay().toString(),
                "", "", ""));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", ""));
        return revenueExportResults;
    }

    public List<HisRevenueCostReportDailyExportResult> handleSheBaoQingDao(RevenueChargedDailyBudgetIndexEntity sheBaoQingDao) {
        List<HisRevenueCostReportDailyExportResult> revenueExportResults = new ArrayList<>();
        HisRevenueCostReportDailyExportResult result1 = new HisRevenueCostReportDailyExportResult("医保", "", "", "", "", "");
        revenueExportResults.add(result1);
        if (sheBaoQingDao == null || sheBaoQingDao.getShebaoStatSummaryReportRsp() == null || sheBaoQingDao.getShebaoStatSummaryReportRsp().getExtraInfo() == null) {
            return revenueExportResults;
        }
        ShebaoStatSummaryReportRsp.QingDaoExtraInfo extraInfo = JSON.parseObject(dump(sheBaoQingDao
                .getShebaoStatSummaryReportRsp().getExtraInfo()), ShebaoStatSummaryReportRsp.QingDaoExtraInfo.class);
        ShebaoStatSummaryReportRsp.QingDaoShebaoPaymentItem outpatient = extraInfo.getNormalOutpatient();
        ShebaoStatSummaryReportRsp.QingDaoShebaoPaymentItem seriousIllnessOutpatient = extraInfo.getSeriousIllnessOutpatient();
        ShebaoStatSummaryReportRsp.QingDaoShebaoPaymentItem longCare = extraInfo.getLongCare();

        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("普通门诊", "医保支付",
                outpatient == null || outpatient.getReceivedFee() == null ? "0" : outpatient.getReceivedFee().toString(),
                "大病门诊", "医保支付",
                seriousIllnessOutpatient == null || seriousIllnessOutpatient.getReceivedFee() == null ? "0" : seriousIllnessOutpatient.getReceivedFee().toString()));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("",
                "个人负担", outpatient == null || outpatient.getPersonalBurden() == null ? "0" : outpatient.getPersonalBurden().toString(), "", "个人负担",
                seriousIllnessOutpatient == null || seriousIllnessOutpatient.getPersonalBurden() == null ? "0" : seriousIllnessOutpatient.getPersonalBurden().toString()));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("长期护理", "医保支付",
                longCare == null || longCare.getReceivedFee() == null ? "0" : longCare.getReceivedFee().toString(), "", "", ""));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "个人负担",
                longCare == null || longCare.getPersonalBurden() == null ? "0" : longCare.getPersonalBurden().toString(), "", "", ""));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", ""));
        return revenueExportResults;
    }

    public List<HisRevenueCostReportDailyExportResult> handleBudgetIndex(RevenueChargedDailyBudgetIndexEntity budgetIndexEntity) {
        List<HisRevenueCostReportDailyExportResult> revenueExportResults = new ArrayList<>();
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("预算指标", "", "", "", "", ""));
        RevenueChargedDailyChineseDoseEntity data = budgetIndexEntity.getRevenueChargedDailyChineseDoseEntity();
        //获取对象中的社保数据
        ShebaoStatSummaryReportRsp shebaoStatSummaryReportRsp = budgetIndexEntity.getShebaoStatSummaryReportRsp();
        if (shebaoStatSummaryReportRsp != null) {
            JsonNode extraInfo = shebaoStatSummaryReportRsp.getExtraInfo();
            String budgetIndicators = dump(extraInfo.get("budgetIndicator"));
            //社保会返回默认值
            ShebaoStatSummaryReportRsp.HzBudgetIndicator shebaoData =
                    JSON.parseObject(budgetIndicators, ShebaoStatSummaryReportRsp.HzBudgetIndicator.class);
            if (shebaoData != null) {
                revenueExportResults.add(new HisRevenueCostReportDailyExportResult("市医保预算指标",
                        "就诊人次", shebaoData.getPersonTimes() + "", "就诊人头",
                        shebaoData.getPersonCount() + "", "比值:" + shebaoData.getPersonTimesCountRate().toString()));
                revenueExportResults.add(new HisRevenueCostReportDailyExportResult("",
                        "次均", shebaoData.getTotalSocialPaymentFeeTimesAvg().toString(),
                        "列支", shebaoData.getSocialPaymentFee().toString(), ""));
            } else {
                revenueExportResults.add(new HisRevenueCostReportDailyExportResult("市医保预算指标",
                        "就诊人次", 0 + "", "就诊人头",
                        0 + "", "比值:" + 0));
                revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "次均", "0",
                        "列支", "0", ""));
            }
        } else {
            revenueExportResults.add(new HisRevenueCostReportDailyExportResult("市医保预算指标",
                    "就诊人次", 0 + "", "就诊人头",
                    0 + "", "比值:" + 0));
            revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "次均", "0",
                    "列支", "0", ""));
        }
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("诊所整体指标",
                "中药贴数", data == null || data.getChineseDoseCount() == null ? "0" : data.getChineseDoseCount().toString(),
                "中药贴均", data == null || data.getChineseDosePriceAvg() == null ? "0" : data.getChineseDosePriceAvg().toString(), ""));
        revenueExportResults.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", ""));
        return revenueExportResults;
    }

    public static String dump(Object object) {
        String json = null;
        if (object == null) {
            return null;
        }
        try {
            json = SOBJECTMAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return json;
    }

    public List<HisRevenueCostReportDailyExportResult> handleInvoiceTotal(List<List<KeyValuePojo>> invoiceTotal) {
        List<HisRevenueCostReportDailyExportResult> list = new ArrayList<>();
        Map<String, Object> invoiceTotalMap = invoiceTotal.stream()
                .flatMap(List::stream)
                .collect(Collectors.toMap(
                        KeyValuePojo::getName,
                        KeyValuePojo::getValue
                ));
        list.add(new HisRevenueCostReportDailyExportResult("发票", "", "", "", "", "", "", "", "", ""));
        list.add(new HisRevenueCostReportDailyExportResult("开票票数", invoiceTotalMap.get("开票票数").toString(),
                "开票金额", invoiceTotalMap.get("开票金额").toString(), "冲红票数", invoiceTotalMap.get("冲红票数").toString(),
                "冲红金额", invoiceTotalMap.get("冲红金额").toString(), "", ""));
        list.add(new HisRevenueCostReportDailyExportResult("作废票数", invoiceTotalMap.get("作废票数").toString(), "作废金额", invoiceTotalMap.get("作废金额").toString(),
                "用票总数", invoiceTotalMap.get("用票总数").toString(), "", "", "", ""));
        list.add(new HisRevenueCostReportDailyExportResult("作废票号", invoiceTotalMap.get("作废票号").toString(),
                "", "", "", "", "", "", "", ""));
        
        // 处理发票号段，避免超过Excel单元格32,767字符限制
        String invoiceNumberRanges = invoiceTotalMap.get("发票号段") != null ? invoiceTotalMap.get("发票号段").toString() : "";
        
        // 单元格字符限制
        final int cellLimit = 30000; // 设置稍小于32767的阈值，留出安全余量
        
        if (invoiceNumberRanges.length() <= cellLimit) {
            // 如果不超过限制，正常添加
            list.add(new HisRevenueCostReportDailyExportResult("发票号段", invoiceNumberRanges, "", "", "", "", "", "", "", ""));
        } else {
            // 如果超过限制，按顿号分割，确保每个号段区间的完整性
            String[] invoiceRanges = invoiceNumberRanges.split("、"); // 使用顿号分割
            
            // 将号段区间分组，确保每个单元格内容不超过限制
            List<String> cellContents = new ArrayList<>();
            StringBuilder currentCell = new StringBuilder();
            int currentLength = 0;
            
            for (String range : invoiceRanges) {
                // 检查当前区间是否可以添加到当前单元格
                if (currentLength + range.length() + (currentLength > 0 ? 1 : 0) <= cellLimit) {
                    // 可以添加到当前单元格
                    if (currentLength > 0) {
                        currentCell.append("、"); // 添加顿号分隔符
                        currentLength++;
                    }
                    currentCell.append(range);
                    currentLength += range.length();
                } else {
                    // 当前单元格已满，保存并创建新的单元格
                    cellContents.add(currentCell.toString());
                    currentCell = new StringBuilder(range);
                    currentLength = range.length();
                }
            }
            
            // 添加最后一个单元格
            if (currentLength > 0) {
                cellContents.add(currentCell.toString());
            }
            
            // 将分组后的内容填充到行中，每行最多7列
            boolean isFirstRow = true;
            int cellIndex = 0;
            
            while (cellIndex < cellContents.size()) {
                HisRevenueCostReportDailyExportResult row = new HisRevenueCostReportDailyExportResult();
                
                // 设置第一列标题
                if (isFirstRow) {
                    row.setFirstLine("发票号段");
                    isFirstRow = false;
                }
                
                // 填充当前行的剩余6列
                for (int col = 2; col <= 7 && cellIndex < cellContents.size(); col++, cellIndex++) {
                    String content = cellContents.get(cellIndex);
                    
                    // 根据当前列位置设置值
                    switch (col) {
                        case 2:
                            row.setSecondLine(content);
                            break;
                        case 3:
                            row.setThirdLine(content);
                            break;
                        case 4:
                            row.setFourthLine(content);
                            break;
                        case 5:
                            row.setFifthLine(content);
                            break;
                        case 6:
                            row.setSixthLine(content);
                            break;
                        case 7:
                            row.setSeventhLine(content);
                            break;
                    }
                }
                // 添加当前行到结果列表
                list.add(row);
            }
        }
        
        list.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", "", "", "", "", ""));
        return list;
    }

    public List<HisRevenueCostReportDailyExportResult> handleInvoiceDetail(V2StatResponse invoiceDetail) {
        List<HisRevenueCostReportDailyExportResult> list = new ArrayList<>();
        list.add(new HisRevenueCostReportDailyExportResult("发票票号", "患者", "金额", "日期", "", ""));
        if (invoiceDetail.getData() == null) {
            return list;
        }
        List<InvoiceDetailEntity> invoiceDetailEntityList = (List<InvoiceDetailEntity>) invoiceDetail.getData();
        for (InvoiceDetailEntity entity : invoiceDetailEntityList) {
            list.add(new HisRevenueCostReportDailyExportResult(entity.getInvoiceNumber(), entity.getPatientName(), entity.getInvoiceFee().toString(),
                    entity.getOperationTime(), "", ""));
        }
        list.add(new HisRevenueCostReportDailyExportResult("", "", "", "", "", ""));
        return list;
    }

    @SafeVarargs
    public static List<EmployeeResp> handleCashierEmployee(Map<String, Employee> employeeMap,
                                                    List<String>... employeeList) {
        Map<String, EmployeeResp> employeeResult = new HashMap<>();
        for (List<String> employeeIds : employeeList) {
            if (employeeIds == null || employeeIds.size() == 0) {
                continue;
            }
            for (String employeeId : employeeIds) {
                if ("00000000000000000000000000000000".equals(employeeId)) {
                    employeeResult.put("00000000000000000000000000000000", new EmployeeResp(employeeId, "自助支付"));
                } else {
                    Employee employee = employeeMap.get(employeeId);
                    if (BeanUtil.isEmpty(employee)) {
                        continue;
                    }
                    employeeResult.put(employeeId, new EmployeeResp(employeeId, employee.getName() == null || employee.getName().isEmpty()
                            ? "-" : employee.getName()));
                }
            }
        }
        return new ArrayList<>(employeeResult.values());
    }

    @SafeVarargs
    public static List<DepartmentResp> handleDepartment(Map<String, String> departmentMap,
                                                        List<String>... departmentList) {
        Map<String, DepartmentResp> departmentResult = new HashMap<>();
        for (List<String> departments : departmentList) {
            if (departments == null || departments.size() == 0) {
                continue;
            }
            for (String id : departments) {
                departmentResult.put(id, new DepartmentResp(id, (departmentMap.get(id) == null || departmentMap.get(id).isEmpty())
                        ? "-" : departmentMap.get(id)));
            }
        }
        return new ArrayList<>(departmentResult.values());
    }
}
