package cn.abc.flink.stat.service.cis.chronic.recovery.pojos;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;


@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(17)
public class CreateRecordPatientClinicSheet {
    @ExcelProperty(value = "患者姓名", index = 0)
    private String patientName;

    @ExcelProperty(value = "性别", index = 1)
    private String sex;

    @ExcelProperty(value = "年龄", index = 2)
    private String age;

    @ExcelProperty(value = "手机号", index = 3)
    private String mobile;

    @ExcelProperty(value = "慢病康复项目", index = 4)
    private String name;

    @ExcelProperty(value = "建档日期", index = 5)
    private String created;

    @ExcelProperty(value = "最近疗效评估", index = 6)
    private String lastEvaluation;

    @ExcelProperty(value = "疗效评估日期", index = 7)
    private String lastEvaluationCreated;

    @ExcelProperty(value = "最近就诊日期", index = 8)
    private String lastDiagCreated;

    @ExcelProperty(value = "最近就诊医生", index = 9)
    private String lastDiagCreatedByName;

    @ExcelIgnore
    private String clinicName;

    @ExcelProperty(value = "建档人", index = 10)
    private String createdByName;

    public String getPatientName() {
        return this.patientName;
    }

    public String getSex() {
        return this.sex;
    }

    public String getAge() {
        return this.age;
    }

    public String getMobile() {
        return this.mobile;
    }

    public String getName() {
        return this.name;
    }

    public String getCreated() {
        return this.created;
    }

    public String getLastEvaluation() {
        return this.lastEvaluation;
    }

    public String getLastEvaluationCreated() {
        return this.lastEvaluationCreated;
    }

    public String getLastDiagCreated() {
        return this.lastDiagCreated;
    }

    public String getLastDiagCreatedByName() {
        return this.lastDiagCreatedByName;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public String getCreatedByName() {
        return this.createdByName;
    }


    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public void setLastEvaluation(String lastEvaluation) {
        this.lastEvaluation = lastEvaluation;
    }

    public void setLastEvaluationCreated(String lastEvaluationCreated) {
        this.lastEvaluationCreated = lastEvaluationCreated;
    }

    public void setLastDiagCreated(String lastDiagCreated) {
        this.lastDiagCreated = lastDiagCreated;
    }

    public void setLastDiagCreatedByName(String lastDiagCreatedByName) {
        this.lastDiagCreatedByName = lastDiagCreatedByName;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

}
