package cn.abc.flink.stat.service.cis.goods.inventory.domain;

import cn.abc.flink.stat.common.ABCNumberUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("进销存统计药品实体")
public class InventoryGoods {

    @ApiModelProperty("药品编码")
    private String goodsShortId;

    @ApiModelProperty("药品id")
    private String goodsId;

    @ApiModelProperty("药品名称")
    private String goodsText;

    @ApiModelProperty("一级分类id")
    private String feeType1;

    @ApiModelProperty("一级分类Str")
    private String feeType1Text;

    @ApiModelProperty("二级分类id")
    private Integer feeType2;

    @ApiModelProperty("二级分类Str")
    private String feeType2Text;

    @ApiModelProperty("药品的规格")
    private String specification;

    @ApiModelProperty("厂家")
    private String manufacturer;

    @ApiModelProperty("小单位")
    private String pieceUnit;

    @ApiModelProperty("最小包装单位")
    private String minPieceUnit;

    @ApiModelProperty("大单位")
    private String packageUnit;

    @ApiModelProperty("小单位售价")
    private BigDecimal piecePrice;

    @ApiModelProperty("小单位售价Str")
    private String piecePriceText;

    @ApiModelProperty("大单位售价")
    private BigDecimal packagePrice;

    @ApiModelProperty("国药准字")
    private String nmpn;

    @ApiModelProperty("最近进价")
    private BigDecimal lastPackageCostPrice;

    @ApiModelProperty("最近进价Str")
    private String lastPackageCostPriceText;

    @ApiModelProperty("最近供应商")
    private String lastStockInOrderSupplier;

    @ApiModelProperty("医保码")
    private String sheBaoCodeNationalCode;

    @ApiModelProperty("费用类型id")
    private Long feeTypeId;

    @ApiModelProperty("费用类型名称")
    private String feeTypeName;

    @ApiModelProperty("利润分类")
    private Long profitCategoryTypeId;

    @ApiModelProperty("利润分类名称")
    private String profitCategoryTypeName;

    @ApiModelProperty("药品批准文号")
    private String medicineNmpn;

    @ApiModelProperty("药品备注")
    private String remark;

    @ApiModelProperty("期初大单位总数量")
    private BigDecimal beginCount = BigDecimal.ZERO;
    @ApiModelProperty("期初小单位总数量")
    private BigDecimal beginConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("期初大单位数量")
    private BigDecimal beginPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("期初小单位数量")
    private BigDecimal beginPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("期初成本不含税")
    private BigDecimal beginCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("期初成本含税")
    private BigDecimal beginCost = BigDecimal.ZERO;
    @ApiModelProperty("期初售价不含税")
    private BigDecimal beginPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("期初售价含税")
    private BigDecimal beginPrice = BigDecimal.ZERO;
    @ApiModelProperty("期初大单位总数量字符串")
    private String beginCountText;
    @ApiModelProperty("期初小单位总数量字符串")
    private String beginConvertPieceCountText;
    @ApiModelProperty("期初大单位数量字符串")
    private String beginPackageCountText;
    @ApiModelProperty("期初小单位数量字符串")
    private String beginPieceCountText;
    @ApiModelProperty("期初成本不含税字符串")
    private String beginCostExcludeTaxText;
    @ApiModelProperty("期初成本含税字符串")
    private String beginCostText;
    @ApiModelProperty("期初售价不含税字符串")
    private String beginPriceExcludeTaxText;
    @ApiModelProperty("期初售价含税字符串")
    private String beginPriceText;

    @ApiModelProperty("期末大单位总数量")
    private BigDecimal endCount = BigDecimal.ZERO;
    @ApiModelProperty("期末小单位总数量")
    private BigDecimal endConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("期末大单位数量")
    private BigDecimal endPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("期末小单位数量")
    private BigDecimal endPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("期末成本不含税")
    private BigDecimal endCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("期末成本含税")
    private BigDecimal endCost = BigDecimal.ZERO;
    @ApiModelProperty("期末售价不含税")
    private BigDecimal endPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("期末售价含税")
    private BigDecimal endPrice = BigDecimal.ZERO;
    @ApiModelProperty("期末大单位总数量字符串")
    private String endCountText;
    @ApiModelProperty("期末小单位总数量字符串")
    private String endConvertPieceCountText;
    @ApiModelProperty("期末大单位数量字符串")
    private String endPackageCountText;
    @ApiModelProperty("期末小单位数量字符串")
    private String endPieceCountText;
    @ApiModelProperty("期末成本不含税字符串")
    private String endCostExcludeTaxText;
    @ApiModelProperty("期末成本含税字符串")
    private String endCostText;
    @ApiModelProperty("期末售价不含税字符串")
    private String endPriceExcludeTaxText;
    @ApiModelProperty("期末售价含税字符串")
    private String endPriceText;

    @ApiModelProperty("初始化入库大单位总数量")
    private BigDecimal inInitCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库小单位总数量")
    private BigDecimal inInitConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库大单位数量")
    private BigDecimal inInitPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库小单位数量")
    private BigDecimal inInitPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库成本不含税")
    private BigDecimal inInitCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库成本含税")
    private BigDecimal inInitCost = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库售价不含税")
    private BigDecimal inInitPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库售价含税")
    private BigDecimal inInitPrice = BigDecimal.ZERO;
    @ApiModelProperty("初始化入库大单位总数量字符串")
    private String inInitCountText;
    @ApiModelProperty("初始化入库小单位总数量字符串")
    private String inInitConvertPieceCountText;
    @ApiModelProperty("初始化入库大单位数量字符串")
    private String inInitPackageCountText;
    @ApiModelProperty("初始化入库小单位数量字符串")
    private String inInitPieceCountText;
    @ApiModelProperty("初始化入库成本不含税字符串")
    private String inInitCostExcludeTaxText;
    @ApiModelProperty("初始化入库成本含税字符串")
    private String inInitCostText;
    @ApiModelProperty("初始化入库售价不含税字符串")
    private String inInitPriceExcludeTaxText;
    @ApiModelProperty("初始化入库售价含税字符串")
    private String inInitPriceText;

    @ApiModelProperty("采购入库大单位总数量")
    private BigDecimal purchaseInCount = BigDecimal.ZERO;
    @ApiModelProperty("采购入库小单位总数量")
    private BigDecimal purchaseInConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("采购入库大单位数量")
    private BigDecimal purchaseInPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("采购入库小单位数量")
    private BigDecimal purchaseInPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("采购入库成本不含税")
    private BigDecimal purchaseInCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("采购入库成本含税")
    private BigDecimal purchaseInCost = BigDecimal.ZERO;
    @ApiModelProperty("采购入库售价不含税")
    private BigDecimal purchaseInPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("采购入库售价含税")
    private BigDecimal purchaseInPrice = BigDecimal.ZERO;
    @ApiModelProperty("采购入库大单位总数量字符串")
    private String purchaseInCountText;
    @ApiModelProperty("采购入库小单位总数量字符串")
    private String purchaseInConvertPieceCountText;
    @ApiModelProperty("采购入库大单位数量字符串")
    private String purchaseInPackageCountText;
    @ApiModelProperty("采购入库小单位数量字符串")
    private String purchaseInPieceCountText;
    @ApiModelProperty("采购入库成本不含税字符串")
    private String purchaseInCostExcludeTaxText;
    @ApiModelProperty("采购入库成本含税字符串")
    private String purchaseInCostText;
    @ApiModelProperty("采购入库售价不含税字符串")
    private String purchaseInPriceExcludeTaxText;
    @ApiModelProperty("采购入库售价含税字符串")
    private String purchaseInPriceText;

    @ApiModelProperty("调拨入库(店间调拨)大单位总数量")
    private BigDecimal allotInCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)小单位总数量")
    private BigDecimal allotInConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)大单位数量")
    private BigDecimal allotInPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)小单位数量")
    private BigDecimal allotInPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)成本不含税")
    private BigDecimal allotInCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)成本含税")
    private BigDecimal allotInCost = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)售价不含税")
    private BigDecimal allotInPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)售价含税")
    private BigDecimal allotInPrice = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店间调拨)大单位总数量字符串")
    private String allotInCountText;
    @ApiModelProperty("调拨入库(店间调拨)小单位总数量字符串")
    private String allotInConvertPieceCountText;
    @ApiModelProperty("调拨入库(店间调拨)大单位数量字符串")
    private String allotInPackageCountText;
    @ApiModelProperty("调拨入库(店间调拨)小单位数量字符串")
    private String allotInPieceCountText;
    @ApiModelProperty("调拨入库(店间调拨)成本不含税字符串")
    private String allotInCostExcludeTaxText;
    @ApiModelProperty("调拨入库(店间调拨)成本含税字符串")
    private String allotInCostText;
    @ApiModelProperty("调拨入库(店间调拨)售价不含税字符串")
    private String allotInPriceExcludeTaxText;
    @ApiModelProperty("调拨入库(店间调拨)售价含税字符串")
    private String allotInPriceText;

    @ApiModelProperty("调拨入库(店内调拨)大单位总数量")
    private BigDecimal allotInInsideCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)小单位总数量")
    private BigDecimal allotInInsideConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)大单位数量")
    private BigDecimal allotInInsidePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)小单位数量")
    private BigDecimal allotInInsidePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)成本不含税")
    private BigDecimal allotInInsideCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)成本含税")
    private BigDecimal allotInInsideCost = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)售价不含税")
    private BigDecimal allotInInsidePriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)售价含税")
    private BigDecimal allotInInsidePrice = BigDecimal.ZERO;
    @ApiModelProperty("调拨入库(店内调拨)大单位总数量字符串")
    private String allotInInsideCountText;
    @ApiModelProperty("调拨入库(店内调拨)小单位总数量字符串")
    private String allotInInsideConvertPieceCountText;
    @ApiModelProperty("调拨入库(店内调拨)大单位数量字符串")
    private String allotInInsidePackageCountText;
    @ApiModelProperty("调拨入库(店内调拨)小单位数量字符串")
    private String allotInInsidePieceCountText;
    @ApiModelProperty("调拨入库(店内调拨)成本不含税字符串")
    private String allotInInsideCostExcludeTaxText;
    @ApiModelProperty("调拨入库(店内调拨)成本含税字符串")
    private String allotInInsideCostText;
    @ApiModelProperty("调拨入库(店内调拨)售价不含税字符串")
    private String allotInInsidePriceExcludeTaxText;
    @ApiModelProperty("调拨入库(店内调拨)售价含税字符串")
    private String allotInInsidePriceText;

    @ApiModelProperty("盘点入库大单位总数量")
    private BigDecimal checkInCount = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库小单位总数量")
    private BigDecimal checkInConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库大单位数量")
    private BigDecimal checkInPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库小单位数量")
    private BigDecimal checkInPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库成本不含税")
    private BigDecimal checkInPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库成本含税")
    private BigDecimal checkInPrice = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库售价不含税")
    private BigDecimal checkInCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库售价含税")
    private BigDecimal checkInCost = BigDecimal.ZERO;
    @ApiModelProperty("盘点入库大单位总数量字符串")
    private String checkInCountText;
    @ApiModelProperty("盘点入库小单位总数量字符串")
    private String checkInConvertPieceCountText;
    @ApiModelProperty("盘点入库大单位数量字符串")
    private String checkInPackageCountText;
    @ApiModelProperty("盘点入库小单位数量字符串")
    private String checkInPieceCountText;
    @ApiModelProperty("盘点入库成本不含税字符串")
    private String checkInPriceExcludeTaxText;
    @ApiModelProperty("盘点入库成本含税字符串")
    private String checkInPriceText;
    @ApiModelProperty("盘点入库售价不含税字符串")
    private String checkInCostExcludeTaxText;
    @ApiModelProperty("盘点入库售价含税字符串")
    private String checkInCostText;

    @ApiModelProperty("领用入库大单位总数量")
    private BigDecimal inReceiveCount = BigDecimal.ZERO;
    @ApiModelProperty("领用入库小单位总数量")
    private BigDecimal inReceiveConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("领用入库大单位数量")
    private BigDecimal inReceivePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("领用入库小单位数量")
    private BigDecimal inReceivePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("领用入库成本不含税")
    private BigDecimal inReceiveCostAmount = BigDecimal.ZERO;
    @ApiModelProperty("领用入库成本含税")
    private BigDecimal inReceiveCostAmountExcludingTax = BigDecimal.ZERO;
    @ApiModelProperty("领用入库售价不含税")
    private BigDecimal inReceiveAmount = BigDecimal.ZERO;
    @ApiModelProperty("领用入库售价含税")
    private BigDecimal inReceiveAmountExcludingTax = BigDecimal.ZERO;
    @ApiModelProperty("领用入库大单位总数量字符串")
    private String inReceiveCountText;
    @ApiModelProperty("领用入库小单位总数量字符串")
    private String inReceiveConvertPieceCountText;
    @ApiModelProperty("领用入库大单位数量字符串")
    private String inReceivePackageCountText;
    @ApiModelProperty("领用入库小单位数量字符串")
    private String inReceivePieceCountText;
    @ApiModelProperty("领用入库成本不含税字符串")
    private String inReceiveCostAmountText;
    @ApiModelProperty("领用入库成本含税字符串")
    private String inReceiveCostAmountExcludingTaxText;
    @ApiModelProperty("领用入库售价不含税字符串")
    private String inReceiveAmountText;
    @ApiModelProperty("领用入库售价含税字符串")
    private String inReceiveAmountExcludingTaxText;

    @ApiModelProperty("规格修改大单位总数量")
    private BigDecimal inSpecificationModificationCount = BigDecimal.ZERO;
    @ApiModelProperty("规格修改小单位总数量")
    private BigDecimal inSpecificationModificationConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("规格修改大单位数量")
    private BigDecimal inSpecificationModificationCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("规格修改小单位数量")
    private BigDecimal inSpecificationModificationCost = BigDecimal.ZERO;
    @ApiModelProperty("规格修改成本不含税")
    private BigDecimal inSpecificationModificationPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("规格修改成本含税")
    private BigDecimal inSpecificationModificationPrice = BigDecimal.ZERO;
    @ApiModelProperty("规格修改售价不含税")
    private String inSpecificationModificationCountText;
    @ApiModelProperty("规格修改售价含税")
    private String inSpecificationModificationConvertPieceCountText;
    @ApiModelProperty("规格修改大单位总数量字符串")
    private String inSpecificationModificationCostExcludeTaxText;
    @ApiModelProperty("规格修改小单位总数量字符串")
    private String inSpecificationModificationCostText;
    @ApiModelProperty("规格修改大单位数量字符串")
    private String inSpecificationModificationPriceExcludeTaxText;
    @ApiModelProperty("规格修改小单位数量字符串")
    private String inSpecificationModificationPriceText;

    @ApiModelProperty("入库合计大单位总数量")
    private BigDecimal inTotalCount = BigDecimal.ZERO;
    @ApiModelProperty("入库合计小单位总数量")
    private BigDecimal inTotalConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("入库合计大单位数量")
    private BigDecimal inTotalPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("入库合计小单位数量")
    private BigDecimal inTotalPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("入库合计成本不含税")
    private BigDecimal inTotalPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("入库合计成本含税")
    private BigDecimal inTotalPrice = BigDecimal.ZERO;
    @ApiModelProperty("入库合计售价不含税")
    private BigDecimal inTotalCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("入库合计售价含税")
    private BigDecimal inTotalCost = BigDecimal.ZERO;
    @ApiModelProperty("入库合计大单位总数量字符串")
    private String inTotalCountText;
    @ApiModelProperty("入库合计小单位总数量字符串")
    private String inTotalConvertPieceCountText;
    @ApiModelProperty("入库合计成本不含税字符串")
    private String inTotalCostExcludeTaxText;
    @ApiModelProperty("入库合计成本含税字符串")
    private String inTotalCostText;

    @ApiModelProperty("初始化退货大单位总数量")
    private BigDecimal inInitReturnCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货小单位总数量")
    private BigDecimal inInitReturnConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货大单位数量")
    private BigDecimal inInitReturnPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货小单位数量")
    private BigDecimal inInitReturnPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货成本不含税")
    private BigDecimal inInitReturnCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货成本含税")
    private BigDecimal inInitReturnCost = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货售价不含税")
    private BigDecimal inInitReturnPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货售价含税")
    private BigDecimal inInitReturnPrice = BigDecimal.ZERO;
    @ApiModelProperty("初始化退货大单位总数量字符串")
    private String inInitReturnCountText;
    @ApiModelProperty("初始化退货小单位总数量字符串")
    private String inInitReturnConvertPieceCountText;
    @ApiModelProperty("初始化退货大单位数量字符串")
    private String inInitReturnPackageCountText;
    @ApiModelProperty("初始化退货小单位数量字符串")
    private String inInitReturnPieceCountText;
    @ApiModelProperty("初始化退货成本不含税字符串")
    private String inInitReturnCostExcludeTaxText;
    @ApiModelProperty("初始化退货成本含税字符串")
    private String inInitReturnCostText;
    @ApiModelProperty("初始化退货售价不含税字符串")
    private String inInitReturnPriceExcludeTaxText;
    @ApiModelProperty("初始化退货售价含税字符串")
    private String inInitReturnPriceText;

    @ApiModelProperty("退货出库大单位总数量")
    private BigDecimal returnGoodsOutCount = BigDecimal.ZERO;
    @ApiModelProperty("退货出库小单位总数量")
    private BigDecimal returnGoodsOutConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("退货出库大单位数量")
    private BigDecimal returnGoodsOutPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("退货出库小单位数量")
    private BigDecimal returnGoodsOutPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("退货出库成本不含税")
    private BigDecimal returnGoodsOutCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("退货出库成本含税")
    private BigDecimal returnGoodsOutCost = BigDecimal.ZERO;
    @ApiModelProperty("退货出库售价不含税")
    private BigDecimal returnGoodsOutPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("退货出库售价含税")
    private BigDecimal returnGoodsOutPrice = BigDecimal.ZERO;
    @ApiModelProperty("退货出库大单位总数量字符串")
    private String returnGoodsOutCountText;
    @ApiModelProperty("退货出库小单位总数量字符串")
    private String returnGoodsOutConvertPieceCountText;
    @ApiModelProperty("退货出库大单位数量字符串")
    private String returnGoodsOutPackageCountText;
    @ApiModelProperty("退货出库小单位数量字符串")
    private String returnGoodsOutPieceCountText;
    @ApiModelProperty("退货出库成本不含税字符串")
    private String returnGoodsOutCostExcludeTaxText;
    @ApiModelProperty("退货出库成本含税字符串")
    private String returnGoodsOutCostText;
    @ApiModelProperty("退货出库售价不含税字符串")
    private String returnGoodsOutPriceExcludeTaxText;
    @ApiModelProperty("退货出库售价含税字符串")
    private String returnGoodsOutPriceText;

    @ApiModelProperty("发药大单位总数量")
    private BigDecimal dispenseCount = BigDecimal.ZERO;
    @ApiModelProperty("发药小单位总数量")
    private BigDecimal dispenseConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("发药大单位数量")
    private BigDecimal dispensePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("发药小单位数量")
    private BigDecimal dispensePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("发药成本不含税")
    private BigDecimal dispensePriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("发药成本含税")
    private BigDecimal dispensePrice = BigDecimal.ZERO;
    @ApiModelProperty("发药售价不含税")
    private BigDecimal dispenseCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("发药售价含税")
    private BigDecimal dispenseCost = BigDecimal.ZERO;
    @ApiModelProperty("发药大单位总数量字符串")
    private String dispenseCountText;
    @ApiModelProperty("发药小单位总数量字符串")
    private String dispenseConvertPieceCountText;
    @ApiModelProperty("发药大单位数量字符串")
    private String dispensePackageCountText;
    @ApiModelProperty("发药小单位数量字符串")
    private String dispensePieceCountText;
    @ApiModelProperty("发药成本不含税字符串")
    private String dispensePriceExcludeTaxText;
    @ApiModelProperty("发药成本含税字符串")
    private String dispensePriceText;
    @ApiModelProperty("发药售价不含税字符串")
    private String dispenseCostExcludeTaxText;
    @ApiModelProperty("发药售价含税字符串")
    private String dispenseCostText;

    @ApiModelProperty("门诊发药大单位总数量")
    private BigDecimal outPatientDispenseCount = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药小单位总数量")
    private BigDecimal outPatientDispenseConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药大单位数量")
    private BigDecimal outPatientDispensePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药小单位数量")
    private BigDecimal outPatientDispensePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药成本不含税")
    private BigDecimal outPatientDispensePriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药成本含税")
    private BigDecimal outPatientDispensePrice = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药售价不含税")
    private BigDecimal outPatientDispenseCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药售价含税")
    private BigDecimal outPatientDispenseCost = BigDecimal.ZERO;
    @ApiModelProperty("门诊发药大单位总数量字符串")
    private String outPatientDispenseCountText;
    @ApiModelProperty("门诊发药小单位总数量字符串")
    private String outPatientDispenseConvertPieceCountText;
    @ApiModelProperty("门诊发药大单位数量字符串")
    private String outPatientDispensePackageCountText;
    @ApiModelProperty("门诊发药小单位数量字符串")
    private String outPatientDispensePieceCountText;
    @ApiModelProperty("门诊发药成本不含税字符串")
    private String outPatientDispensePriceExcludeTaxText;
    @ApiModelProperty("门诊发药成本含税字符串")
    private String outPatientDispensePriceText;
    @ApiModelProperty("门诊发药售价不含税字符串")
    private String outPatientDispenseCostExcludeTaxText;
    @ApiModelProperty("门诊发药售价含税字符串")
    private String outPatientDispenseCostText;

    @ApiModelProperty("住院药房发药(已结算)-数量")
    private BigDecimal hospitalPharmacyDispenseCount = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-转换后小单位数量")
    private BigDecimal hospitalPharmacyDispenseConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-大单位数量")
    private BigDecimal hospitalPharmacyDispensePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-小单位数量")
    private BigDecimal hospitalPharmacyDispensePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-售价不含税")
    private BigDecimal hospitalPharmacyDispensePriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-售价含税")
    private BigDecimal hospitalPharmacyDispensePrice = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-成本不含税")
    private BigDecimal hospitalPharmacyDispenseCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-成本含税")
    private BigDecimal hospitalPharmacyDispenseCost = BigDecimal.ZERO;
    @ApiModelProperty("住院药房发药(已结算)-数量str")
    private String hospitalPharmacyDispenseCountText;
    @ApiModelProperty("住院药房发药(已结算)-转换后小单位数量str")
    private String hospitalPharmacyDispenseConvertPieceCountText;
    @ApiModelProperty("住院药房发药(已结算)-大单位数量str")
    private String hospitalPharmacyDispensePackageCountText;
    @ApiModelProperty("住院药房发药(已结算)-小单位数量str")
    private String hospitalPharmacyDispensePieceCountText;
    @ApiModelProperty("住院药房发药(已结算)-售价不含税str")
    private String hospitalPharmacyDispensePriceExcludeTaxText;
    @ApiModelProperty("住院药房发药(已结算)-售价含税str")
    private String hospitalPharmacyDispensePriceText;
    @ApiModelProperty("住院药房发药(已结算)-成本不含税str")
    private String hospitalPharmacyDispenseCostExcludeTaxText;
    @ApiModelProperty("住院药房发药(已结算)-成本含税str")
    private String hospitalPharmacyDispenseCostText;

    @ApiModelProperty("住院记账发药(已结算)-数量")
    private BigDecimal hospitalAutomaticDispenseCount = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-转换后小单位数量")
    private BigDecimal hospitalAutomaticDispenseConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-大单位数量")
    private BigDecimal hospitalAutomaticDispensePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-小单位数量")
    private BigDecimal hospitalAutomaticDispensePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-售价不含税")
    private BigDecimal hospitalAutomaticDispensePriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-售价含税")
    private BigDecimal hospitalAutomaticDispensePrice = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-成本不含税")
    private BigDecimal hospitalAutomaticDispenseCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-成本含税")
    private BigDecimal hospitalAutomaticDispenseCost = BigDecimal.ZERO;
    @ApiModelProperty("住院记账发药(已结算)-数量str")
    private String hospitalAutomaticDispenseCountText;
    @ApiModelProperty("住院记账发药(已结算)-转换后小单位数量str")
    private String hospitalAutomaticDispenseConvertPieceCountText;
    @ApiModelProperty("住院记账发药(已结算)-大单位数量str")
    private String hospitalAutomaticDispensePackageCountText;
    @ApiModelProperty("住院记账发药(已结算)-小单位数量str")
    private String hospitalAutomaticDispensePieceCountText;
    @ApiModelProperty("住院记账发药(已结算)-售价不含税str")
    private String hospitalAutomaticDispensePriceExcludeTaxText;
    @ApiModelProperty("住院记账发药(已结算)-售价含税str")
    private String hospitalAutomaticDispensePriceText;
    @ApiModelProperty("住院记账发药(已结算)-成本不含税str")
    private String hospitalAutomaticDispenseCostExcludeTaxText;
    @ApiModelProperty("住院记账发药(已结算)-成本含税str")
    private String hospitalAutomaticDispenseCostText;

    @ApiModelProperty("住院发药(未结算)-数量")
    private BigDecimal hospitalNoSettleDispenseCount = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-转换后小单位数量")
    private BigDecimal hospitalNoSettleDispenseConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-大单位数量")
    private BigDecimal hospitalNoSettleDispensePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-小单位数量")
    private BigDecimal hospitalNoSettleDispensePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-售价不含税")
    private BigDecimal hospitalNoSettleDispensePriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-售价含税")
    private BigDecimal hospitalNoSettleDispensePrice = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-成本不含税")
    private BigDecimal hospitalNoSettleDispenseCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-成本含税")
    private BigDecimal hospitalNoSettleDispenseCost = BigDecimal.ZERO;
    @ApiModelProperty("住院发药(未结算)-数量str")
    private String hospitalNoSettleDispenseCountText;
    @ApiModelProperty("住院发药(未结算)-转换后小单位数量str")
    private String hospitalNoSettleDispenseConvertPieceCountText;
    @ApiModelProperty("住院发药(未结算)-大单位数量str")
    private String hospitalNoSettleDispensePackageCountText;
    @ApiModelProperty("住院发药(未结算)-小单位数量str")
    private String hospitalNoSettleDispensePieceCountText;
    @ApiModelProperty("住院发药(未结算)-售价不含税str")
    private String hospitalNoSettleDispensePriceExcludeTaxText;
    @ApiModelProperty("住院发药(未结算)-售价含税str")
    private String hospitalNoSettleDispensePriceText;
    @ApiModelProperty("住院发药(未结算)-成本不含税str")
    private String hospitalNoSettleDispenseCostExcludeTaxText;
    @ApiModelProperty("住院发药(未结算)-成本含税str")
    private String hospitalNoSettleDispenseCostText;

    /**
     * 领料出库指标
     */
    @ApiModelProperty("领用出库大单位总数量")
    private BigDecimal collectOutCount = BigDecimal.ZERO;
    @ApiModelProperty("领用出库小单位总数量")
    private BigDecimal collectOutConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("领用出库大单位数量")
    private BigDecimal collectOutPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("领用出库小单位数量")
    private BigDecimal collectOutPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("领用出库成本不含税")
    private BigDecimal collectOutCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("领用出库成本含税")
    private BigDecimal collectOutCost = BigDecimal.ZERO;
    @ApiModelProperty("领用出库售价不含税")
    private BigDecimal collectOutPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("领用出库售价含税")
    private BigDecimal collectOutPrice = BigDecimal.ZERO;
    @ApiModelProperty("领用出库大单位总数量字符串")
    private String collectOutCountText;
    @ApiModelProperty("领用出库小单位总数量字符串")
    private String collectOutConvertPieceCountText;
    @ApiModelProperty("领用出库大单位数量字符串")
    private String collectOutPackageCountText;
    @ApiModelProperty("领用出库小单位数量字符串")
    private String collectOutPieceCountText;
    @ApiModelProperty("领用出库成本不含税字符串")
    private String collectOutCostExcludeTaxText;
    @ApiModelProperty("领用出库成本含税字符串")
    private String collectOutCostText;
    @ApiModelProperty("领用出库售价不含税字符串")
    private String collectOutPriceExcludeTaxText;
    @ApiModelProperty("领用出库售价含税字符串")
    private String collectOutPriceText;

    @ApiModelProperty("调拨出库(店间调拨)大单位总数量")
    private BigDecimal allotOutCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)小单位总数量")
    private BigDecimal allotOutConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)大单位数量")
    private BigDecimal allotOutPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)小单位数量")
    private BigDecimal allotOutPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)成本不含税")
    private BigDecimal allotOutCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)成本含税")
    private BigDecimal allotOutCost = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)售价不含税")
    private BigDecimal allotOutPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)售价含税")
    private BigDecimal allotOutPrice = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店间调拨)大单位总数量字符串")
    private String allotOutCountText;
    @ApiModelProperty("调拨出库(店间调拨)小单位总数量字符串")
    private String allotOutConvertPieceCountText;
    @ApiModelProperty("调拨出库(店间调拨)大单位数量字符串")
    private String allotOutPackageCountText;
    @ApiModelProperty("调拨出库(店间调拨)小单位数量字符串")
    private String allotOutPieceCountText;
    @ApiModelProperty("调拨出库(店间调拨)成本不含税字符串")
    private String allotOutCostExcludeTaxText;
    @ApiModelProperty("调拨出库(店间调拨)成本含税字符串")
    private String allotOutCostText;
    @ApiModelProperty("调拨出库(店间调拨)售价不含税字符串")
    private String allotOutPriceExcludeTaxText;
    @ApiModelProperty("调拨出库(店间调拨)售价含税字符串")
    private String allotOutPriceText;

    @ApiModelProperty("调拨出库(店内调拨)大单位总数量")
    private BigDecimal allotOutInsideCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)小单位总数量")
    private BigDecimal allotOutInsideConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)大单位数量")
    private BigDecimal allotOutInsidePackageCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)小单位数量")
    private BigDecimal allotOutInsidePieceCount = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)成本不含税")
    private BigDecimal allotOutInsideCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)成本含税")
    private BigDecimal allotOutInsideCost = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)售价不含税")
    private BigDecimal allotOutInsidePriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)售价含税")
    private BigDecimal allotOutInsidePrice = BigDecimal.ZERO;
    @ApiModelProperty("调拨出库(店内调拨)大单位总数量字符串")
    private String allotOutInsideCountText;
    @ApiModelProperty("调拨出库(店内调拨)小单位总数量字符串")
    private String allotOutInsideConvertPieceCountText;
    @ApiModelProperty("调拨出库(店内调拨)大单位数量字符串")
    private String allotOutInsidePackageCountText;
    @ApiModelProperty("调拨出库(店内调拨)小单位数量字符串")
    private String allotOutInsidePieceCountText;
    @ApiModelProperty("调拨出库(店内调拨)成本不含税字符串")
    private String allotOutInsideCostExcludeTaxText;
    @ApiModelProperty("调拨出库(店内调拨)成本含税字符串")
    private String allotOutInsideCostText;
    @ApiModelProperty("调拨出库(店内调拨)售价不含税字符串")
    private String allotOutInsidePriceExcludeTaxText;
    @ApiModelProperty("调拨出库(店内调拨)售价含税字符串")
    private String allotOutInsidePriceText;

    @ApiModelProperty("报损出库大单位总数量")
    private BigDecimal damagedOutCount = BigDecimal.ZERO;
    @ApiModelProperty("报损出库小单位总数量")
    private BigDecimal damagedOutConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("报损出库大单位数量")
    private BigDecimal damagedOutPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("报损出库小单位数量")
    private BigDecimal damagedOutPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("报损出库成本不含税")
    private BigDecimal damagedOutCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("报损出库成本含税")
    private BigDecimal damagedOutCost = BigDecimal.ZERO;
    @ApiModelProperty("报损出库售价不含税")
    private BigDecimal damagedOutPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("报损出库售价含税")
    private BigDecimal damagedOutPrice = BigDecimal.ZERO;
    @ApiModelProperty("报损出库大单位总数量字符串")
    private String damagedOutCountText;
    @ApiModelProperty("报损出库小单位总数量字符串")
    private String damagedOutConvertPieceCountText;
    @ApiModelProperty("报损出库大单位数量字符串")
    private String damagedOutPackageCountText;
    @ApiModelProperty("报损出库小单位数量字符串")
    private String damagedOutPieceCountText;
    @ApiModelProperty("报损出库成本不含税字符串")
    private String damagedOutCostExcludeTaxText;
    @ApiModelProperty("报损出库成本含税字符串")
    private String damagedOutCostText;
    @ApiModelProperty("报损出库售价不含税字符串")
    private String damagedOutPriceExcludeTaxText;
    @ApiModelProperty("报损出库售价含税字符串")
    private String damagedOutPriceText;

    @ApiModelProperty("科室消耗大单位总数量")
    private BigDecimal outDepartmentConsumptionCount = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗小单位总数量")
    private BigDecimal outDepartmentConsumptionConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗大单位数量")
    private BigDecimal outDepartmentConsumptionPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗小单位数量")
    private BigDecimal outDepartmentConsumptionPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗成本不含税")
    private BigDecimal outDepartmentConsumptionCostAmount = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗成本含税")
    private BigDecimal outDepartmentConsumptionCostAmountExcludingTax = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗售价不含税")
    private BigDecimal outDepartmentConsumptionAmount = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗售价含税")
    private BigDecimal outDepartmentConsumptionAmountExcludingTax = BigDecimal.ZERO;
    @ApiModelProperty("科室消耗大单位总数量字符串")
    private String outDepartmentConsumptionCountText;
    @ApiModelProperty("科室消耗小单位总数量字符串")
    private String outDepartmentConsumptionConvertPieceCountText;
    @ApiModelProperty("科室消耗大单位数量字符串")
    private String outDepartmentConsumptionPackageCountText;
    @ApiModelProperty("科室消耗小单位数量字符串")
    private String outDepartmentConsumptionPieceCountText;
    @ApiModelProperty("科室消耗成本不含税字符串")
    private String outDepartmentConsumptionCostAmountText;
    @ApiModelProperty("科室消耗成本含税字符串")
    private String outDepartmentConsumptionCostAmountExcludingTaxText;
    @ApiModelProperty("科室消耗售价不含税字符串")
    private String outDepartmentConsumptionAmountText;
    @ApiModelProperty("科室消耗售价含税字符串")
    private String outDepartmentConsumptionAmountExcludingTaxText;

    @ApiModelProperty("其他出库大单位总数量")
    private BigDecimal outOtherCount = BigDecimal.ZERO;
    @ApiModelProperty("其他出库小单位总数量")
    private BigDecimal outOtherConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("其他出库大单位数量")
    private BigDecimal outOtherPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("其他出库小单位数量")
    private BigDecimal outOtherPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("其他出库成本不含税")
    private BigDecimal outOtherCostAmount = BigDecimal.ZERO;
    @ApiModelProperty("其他出库成本含税")
    private BigDecimal outOtherCostAmountExcludingTax = BigDecimal.ZERO;
    @ApiModelProperty("其他出库售价不含税")
    private BigDecimal outOtherAmount = BigDecimal.ZERO;
    @ApiModelProperty("其他出库售价含税")
    private BigDecimal outOtherAmountExcludingTax = BigDecimal.ZERO;
    @ApiModelProperty("其他出库大单位总数量字符串")
    private String outOtherCountText;
    @ApiModelProperty("其他出库小单位总数量字符串")
    private String outOtherConvertPieceCountText;
    @ApiModelProperty("其他出库大单位数量字符串")
    private String outOtherPackageCountText;
    @ApiModelProperty("其他出库小单位数量字符串")
    private String outOtherPieceCountText;
    @ApiModelProperty("其他出库成本不含税字符串")
    private String outOtherCostAmountText;
    @ApiModelProperty("其他出库成本含税字符串")
    private String outOtherCostAmountExcludingTaxText;
    @ApiModelProperty("其他出库售价不含税字符串")
    private String outOtherAmountText;
    @ApiModelProperty("其他出库售价含税字符串")
    private String outOtherAmountExcludingTaxText;

    @ApiModelProperty("盘亏出库大单位总数量")
    private BigDecimal checkOutCount = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库小单位总数量")
    private BigDecimal checkOutConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库大单位数量")
    private BigDecimal checkOutPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库小单位数量")
    private BigDecimal checkOutPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库成本不含税")
    private BigDecimal checkOutPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库成本含税")
    private BigDecimal checkOutPrice = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库售价不含税")
    private BigDecimal checkOutCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库售价含税")
    private BigDecimal checkOutCost = BigDecimal.ZERO;
    @ApiModelProperty("盘亏出库大单位总数量字符串")
    private String checkOutCountText;
    @ApiModelProperty("盘亏出库小单位总数量字符串")
    private String checkOutConvertPieceCountText;
    @ApiModelProperty("盘亏出库大单位数量字符串")
    private String checkOutPackageCountText;
    @ApiModelProperty("盘亏出库小单位数量字符串")
    private String checkOutPieceCountText;
    @ApiModelProperty("盘亏出库成本不含税字符串")
    private String checkOutPriceExcludeTaxText;
    @ApiModelProperty("盘亏出库成本含税字符串")
    private String checkOutPriceText;
    @ApiModelProperty("盘亏出库售价不含税字符串")
    private String checkOutCostExcludeTaxText;
    @ApiModelProperty("盘亏出库售价含税字符串")
    private String checkOutCostText;

    @ApiModelProperty("生产出库-数量")
    private BigDecimal productionOutCount = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-转换后小单位数量")
    private BigDecimal productionOutConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-大单位数量")
    private BigDecimal productionOutPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-小单位数量")
    private BigDecimal productionOutPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-售价不含税")
    private BigDecimal productionOutPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-售价含税")
    private BigDecimal productionOutPrice = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-成本不含税")
    private BigDecimal productionOutCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-成本含税")
    private BigDecimal productionOutCost = BigDecimal.ZERO;
    @ApiModelProperty("生产出库-数量str")
    private String productionOutCountText;
    @ApiModelProperty("生产出库-转换后小单位数量str")
    private String productionOutConvertPieceCountText;
    @ApiModelProperty("生产出库-大单位数量str")
    private String productionOutPackageCountText;
    @ApiModelProperty("生产出库-小单位数量str")
    private String productionOutPieceCountText;
    @ApiModelProperty("生产出库-售价不含税str")
    private String productionOutPriceExcludeTaxText;
    @ApiModelProperty("生产出库-售价含税str")
    private String productionOutPriceText;
    @ApiModelProperty("生产出库-成本不含税str")
    private String productionOutCostExcludeTaxText;
    @ApiModelProperty("生产出库-成本含税str")
    private String productionOutCostText;

    @ApiModelProperty("配货出库-数量")
    private BigDecimal deliveryOutCount = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-转换后小单位数量")
    private BigDecimal deliveryOutConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-大单位数量")
    private BigDecimal deliveryOutPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-小单位数量")
    private BigDecimal deliveryOutPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-售价不含税")
    private BigDecimal deliveryOutPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-售价含税")
    private BigDecimal deliveryOutPrice = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-成本不含税")
    private BigDecimal deliveryOutCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-成本含税")
    private BigDecimal deliveryOutCost = BigDecimal.ZERO;
    @ApiModelProperty("配货出库-数量str")
    private String deliveryOutCountText;
    @ApiModelProperty("配货出库-转换后小单位数量str")
    private String deliveryOutConvertPieceCountText;
    @ApiModelProperty("配货出库-大单位数量str")
    private String deliveryOutPackageCountText;
    @ApiModelProperty("配货出库-小单位数量str")
    private String deliveryOutPieceCountText;
    @ApiModelProperty("配货出库-售价不含税str")
    private String deliveryOutPriceExcludeTaxText;
    @ApiModelProperty("配货出库-售价含税str")
    private String deliveryOutPriceText;
    @ApiModelProperty("配货出库-成本不含税str")
    private String deliveryOutCostExcludeTaxText;
    @ApiModelProperty("配货出库-成本含税str")
    private String deliveryOutCostText;

    /**
     * 出库合计指标
     */
    @ApiModelProperty("出库合计大单位总数量")
    private BigDecimal outTotalCount = BigDecimal.ZERO;
    @ApiModelProperty("出库合计小单位总数量")
    private BigDecimal outTotalConvertPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("出库合计大单位数量")
    private BigDecimal outTotalPackageCount = BigDecimal.ZERO;
    @ApiModelProperty("出库合计小单位数量")
    private BigDecimal outTotalPieceCount = BigDecimal.ZERO;
    @ApiModelProperty("出库合计成本不含税")
    private BigDecimal outTotalPriceExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("出库合计成本含税")
    private BigDecimal outTotalPrice = BigDecimal.ZERO;
    @ApiModelProperty("出库合计售价不含税")
    private BigDecimal outTotalCostExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("出库合计售价含税")
    private BigDecimal outTotalCost = BigDecimal.ZERO;
    @ApiModelProperty("出库合计大单位总数量字符串")
    private String outTotalCountText;
    @ApiModelProperty("出库合计小单位总数量字符串")
    private String outTotalConvertPieceCountText;
    @ApiModelProperty("出库合计成本不含税字符串")
    private String outTotalCostExcludeTaxText;
    @ApiModelProperty("出库合计成本含税字符串")
    private String outTotalCostText;

    @ApiModelProperty("进项税")
    private BigDecimal inTaxRat;
    @ApiModelProperty("销项税")
    private BigDecimal outTaxRat;
    @ApiModelProperty("进项税Str")
    private String inTaxRatText;
    @ApiModelProperty("销项税Str")
    private String outTaxRatText;

    /**
     * 税率修正
     */
    private BigDecimal beforeInTaxRat = BigDecimal.ZERO;
    private BigDecimal beforeOutTaxRat = BigDecimal.ZERO;
    private String beforeInTaxRatText;
    private String beforeOutTaxRatText;
    private BigDecimal afterTotalCostModifyExcludeTax = BigDecimal.ZERO;
    private BigDecimal goodsAfterTotalCostModifyExcludeTax = BigDecimal.ZERO;
    @ApiModelProperty("税率修正不含税成本")
    private BigDecimal goodsPharmacyAfterTotalCostModifyExcludeTax = BigDecimal.ZERO;
    private String afterTotalCostModifyExcludeTaxText;
    private String goodsAfterTotalCostModifyExcludeTaxText;
    private String goodsPharmacyAfterTotalCostModifyExcludeTaxText;
    private String effectedTime;
    private String lastModifiedBy;
    private String lastModified;

    @ApiModelProperty("药品的剂型类型名称")
    private String dosageFormTypeName;

    @ApiModelProperty("国家贯标名称")
    private String nationalStandardImplementationName;

    @ApiModelProperty("剂量")
    private String dosage;

    @ApiModelProperty("注册证号")
    private String certificateNo;

    @ApiModelProperty("注册证名称")
    private String certificateName;

    @ApiModelProperty("国产/进口")
    private String origin;

    @ApiModelProperty("最小计价单位")
    private String minimumPricingUnit;

    /**
     * 药品信息字段设置值
     */
    public void apply() {
        if (inTaxRat == null) {
            inTaxRatText = 0 + "%";
        } else {
            inTaxRatText = inTaxRat.intValue() + "%";
        }
        if (outTaxRat == null) {
            outTaxRatText = 0 + "%";
        } else {
            outTaxRatText = outTaxRat.intValue() + "%";
        }
        if (beforeInTaxRat == null) {
            beforeInTaxRatText = 0 + "%";
        } else {
            beforeInTaxRatText = beforeInTaxRat.intValue() + "%";
        }
        if (beforeOutTaxRat == null) {
            beforeOutTaxRatText = 0 + "%";
        } else {
            beforeOutTaxRatText = beforeOutTaxRat.intValue() + "%";
        }
        if (lastPackageCostPrice != null) {
            lastPackageCostPriceText = ABCNumberUtils.roundTextPrettyNew(lastPackageCostPrice);
        }
        if (piecePrice != null) {
            piecePriceText = ABCNumberUtils.roundTextPrettyNew(piecePrice);
        }
        if (packagePrice != null) {
            piecePriceText = ABCNumberUtils.roundTextPrettyNew(packagePrice);
        }
        if (feeType1 != null) {
            piecePriceText = ABCNumberUtils.roundTextPrettyNew(packagePrice);
        }
        if (StrUtil.isBlank(sheBaoCodeNationalCode)) {
            sheBaoCodeNationalCode = "-";
        }
        if (StrUtil.isBlank(feeTypeName)) {
            feeTypeName = "-";
        }
        if (StrUtil.isBlank(manufacturer)) {
            manufacturer = "-";
        }
        if (StrUtil.isBlank(specification)) {
            specification = "-";
        }
        if (StrUtil.isBlank(lastStockInOrderSupplier)) {
            lastStockInOrderSupplier = "-";
        }
        if (StrUtil.isBlank(this.medicineNmpn)) {
            this.medicineNmpn = CommonConstants.WHIPPTREE;
        }
        if (StrUtil.isBlank(this.remark)) {
            this.remark = CommonConstants.WHIPPTREE;
        }
    }

    /**
     * @param isExport 是否是导出
     * @return InventoryGoods
     */
    public InventoryGoods pretty(Boolean isExport) {
        if (goodsShortId == null) {
            goodsShortId = "-";
        }
        if (goodsText == null) {
            goodsText = "-";
        }
        if (StrUtil.isBlank(feeType1Text)) {
            feeType1Text = "-";
        }
        if (StrUtil.isBlank(feeType2Text)) {
            feeType2Text = "-";
        }
        if (manufacturer == null || "".equals(manufacturer)) {
            manufacturer = "-";
        }

        if (specification == null || "".equals(specification)) {
            specification = "-";
        }
        if (StrUtil.isBlank(nmpn)) {
            nmpn = "-";
        }
        if (StrUtil.isBlank(pieceUnit)) {
            pieceUnit = "-";
        }
        if (StrUtil.isBlank(sheBaoCodeNationalCode)) {
            sheBaoCodeNationalCode = "-";
        }
        if (StrUtil.isBlank(lastStockInOrderSupplier)) {
            lastStockInOrderSupplier = "-";
        }
        if (lastPackageCostPrice != null) {
            lastPackageCostPriceText = ABCNumberUtils.roundTextPrettyNew(lastPackageCostPrice);
        }
        if (piecePrice != null) {
            piecePriceText = ABCNumberUtils.roundTextPrettyNew(piecePrice);
        }
        if (packagePrice != null) {
            piecePriceText = ABCNumberUtils.roundTextPrettyNew(packagePrice);
        }
        if (inTaxRat == null) {
            inTaxRatText = 0 + "%";
        } else {
            inTaxRatText = inTaxRat.intValue() + "%";
        }
        if (outTaxRat == null) {
            outTaxRatText = 0 + "%";
        } else {
            outTaxRatText = outTaxRat.intValue() + "%";
        }
        if (StrUtil.isBlank(this.dosageFormTypeName)) {
            this.dosageFormTypeName = CommonConstants.WHIPPTREE;
        }

        // 期初以及期末数据初始化
        beginAndEndDataInitialize(isExport);

        // 入库数据初始化
        inDataInitialize(isExport);

        // 出库数据初始化
        outDataInitialize(isExport);

        return this;
    }

    /**
     * 期初以及期末数据初始化
     *
     * @param isExport 是否是导出
     */
    private void beginAndEndDataInitialize(Boolean isExport) {
        if (isExport) {
            // 期初库存
            beginCount = ABCNumberUtils.round4Pretty(beginCount);
            beginConvertPieceCount = ABCNumberUtils.round4Pretty(beginConvertPieceCount);

            // 期末库存
            endCount = ABCNumberUtils.round4Pretty(endCount);
            endConvertPieceCount = ABCNumberUtils.round4Pretty(endConvertPieceCount);
        } else {
            // 期初库存
            beginCount = ABCNumberUtils.round2Pretty(beginCount);
            beginConvertPieceCount = ABCNumberUtils.round2Pretty(beginConvertPieceCount);
            // 期末库存
            endCount = ABCNumberUtils.round2Pretty(endCount);
            endConvertPieceCount = ABCNumberUtils.round2Pretty(endConvertPieceCount);
        }
        beginCostExcludeTax = ABCNumberUtils.round4Pretty(beginCostExcludeTax);
        beginCost = ABCNumberUtils.round4Pretty(beginCost);
        endCostExcludeTax = ABCNumberUtils.round4Pretty(endCostExcludeTax);
        endCost = ABCNumberUtils.round4Pretty(endCost);
    }

    /**
     * 入库数据初始化
     *
     * @param isExport 是否是导出
     */
    private void inDataInitialize(Boolean isExport) {
        if (isExport) {
            isExportInDataInitialize();
        } else {
            // 初始化入库
            inInitCount = ABCNumberUtils.round2Pretty(inInitCount);
            inInitConvertPieceCount = ABCNumberUtils.round2Pretty(inInitConvertPieceCount);
            // 采购入库
            purchaseInCount = ABCNumberUtils.round2Pretty(purchaseInCount);
            purchaseInConvertPieceCount = ABCNumberUtils.round2Pretty(purchaseInConvertPieceCount);
            // 领用入库
            inReceiveCount = ABCNumberUtils.round2Pretty(inReceiveCount);
            inReceiveConvertPieceCount = ABCNumberUtils.round2Pretty(inReceiveConvertPieceCount);
            // 调拨入库
            allotInCount = ABCNumberUtils.round2Pretty(allotInCount);
            allotInConvertPieceCount = ABCNumberUtils.round2Pretty(allotInConvertPieceCount);
            // 盘盈入库
            checkInCount = ABCNumberUtils.round2Pretty(checkInCount);
            checkInConvertPieceCount = ABCNumberUtils.round2Pretty(checkInConvertPieceCount);
            checkInPriceExcludeTax = ABCNumberUtils.round2Pretty(checkInPriceExcludeTax);
            checkInPrice = ABCNumberUtils.round2Pretty(checkInPrice);
            // 规格修改
            inSpecificationModificationCount = ABCNumberUtils.round2Pretty(inSpecificationModificationCount);
            inSpecificationModificationConvertPieceCount = ABCNumberUtils.round2Pretty(inSpecificationModificationConvertPieceCount);
            // 入库合计
            inTotalCount = ABCNumberUtils.round2Pretty(inTotalCount);
            inTotalConvertPieceCount = ABCNumberUtils.round2Pretty(inTotalConvertPieceCount);
            //税率修正
            afterTotalCostModifyExcludeTax = ABCNumberUtils.round2Pretty(afterTotalCostModifyExcludeTax);
        }
        inInitCostExcludeTax = ABCNumberUtils.round2Pretty(inInitCostExcludeTax);
        inInitCost = ABCNumberUtils.round2Pretty(inInitCost);
        purchaseInCostExcludeTax = ABCNumberUtils.round2Pretty(purchaseInCostExcludeTax);
        purchaseInCost = ABCNumberUtils.round2Pretty(purchaseInCost);
        inReceiveCostAmountExcludingTax = ABCNumberUtils.round4Pretty(inReceiveCostAmountExcludingTax);
        inReceiveCostAmount = ABCNumberUtils.round4Pretty(inReceiveCostAmount);
        allotInCostExcludeTax = ABCNumberUtils.round4Pretty(allotInCostExcludeTax);
        allotInCost = ABCNumberUtils.round4Pretty(allotInCost);
        checkInCostExcludeTax = ABCNumberUtils.round4Pretty(checkInCostExcludeTax);
        checkInCost = ABCNumberUtils.round4Pretty(checkInCost);
        inSpecificationModificationCostExcludeTax
                = ABCNumberUtils.round4Pretty(inSpecificationModificationCostExcludeTax);
        inSpecificationModificationCost = ABCNumberUtils.round4Pretty(inSpecificationModificationCost);
        inTotalCostExcludeTax = ABCNumberUtils.round4Pretty(inTotalCostExcludeTax);
        inTotalCost = ABCNumberUtils.round4Pretty(inTotalCost);
        goodsAfterTotalCostModifyExcludeTax = ABCNumberUtils.round4Pretty(goodsAfterTotalCostModifyExcludeTax);
        goodsPharmacyAfterTotalCostModifyExcludeTax = ABCNumberUtils.round4Pretty(goodsPharmacyAfterTotalCostModifyExcludeTax);
    }

    /**
     * 导出4位
     */
    private void isExportInDataInitialize() {
        // 初始化入库
        inInitCount = ABCNumberUtils.round4Pretty(inInitCount);
        inInitConvertPieceCount = ABCNumberUtils.round4Pretty(inInitConvertPieceCount);
        // 采购入库
        purchaseInCount = ABCNumberUtils.round4Pretty(purchaseInCount);
        purchaseInConvertPieceCount = ABCNumberUtils.round4Pretty(purchaseInConvertPieceCount);
        // 领用入库
        inReceiveCount = ABCNumberUtils.round2Pretty(inReceiveCount);
        inReceiveConvertPieceCount = ABCNumberUtils.round2Pretty(inReceiveConvertPieceCount);
        // 调拨入库
        allotInCount = ABCNumberUtils.round4Pretty(allotInCount);
        allotInConvertPieceCount = ABCNumberUtils.round4Pretty(allotInConvertPieceCount);
        // 盘盈入库
        checkInCount = ABCNumberUtils.round4Pretty(checkInCount);
        checkInConvertPieceCount = ABCNumberUtils.round4Pretty(checkInConvertPieceCount);
        checkInPriceExcludeTax = ABCNumberUtils.round4Pretty(checkInPriceExcludeTax);
        checkInPrice = ABCNumberUtils.round4Pretty(checkInPrice);
        // 规格修改
        inSpecificationModificationCount = ABCNumberUtils.round4Pretty(inSpecificationModificationCount);
        inSpecificationModificationConvertPieceCount = ABCNumberUtils.round4Pretty(inSpecificationModificationConvertPieceCount);
        // 入库合计
        inTotalCount = ABCNumberUtils.round4Pretty(inTotalCount);
        inTotalConvertPieceCount = ABCNumberUtils.round4Pretty(inTotalConvertPieceCount);
        //税率修正
        afterTotalCostModifyExcludeTax = ABCNumberUtils.round2Pretty(afterTotalCostModifyExcludeTax);
    }

    /**
     * 出库数据初始化
     *
     * @param isExport 是否是导出
     */
    private void outDataInitialize(Boolean isExport) {
        if (isExport) {
            isOutExport();
        } else {
            // 初始化出库
            inInitReturnCount = ABCNumberUtils.round2Pretty(inInitReturnCount);
            inInitReturnConvertPieceCount = ABCNumberUtils.round2Pretty(inInitReturnConvertPieceCount);
            inInitReturnPriceExcludeTax = ABCNumberUtils.round2Pretty(inInitReturnPriceExcludeTax);
            inInitReturnPrice = ABCNumberUtils.round2Pretty(inInitReturnPrice);
            // 退货出库
            returnGoodsOutCount = ABCNumberUtils.round2Pretty(returnGoodsOutCount);
            returnGoodsOutConvertPieceCount = ABCNumberUtils.round2Pretty(returnGoodsOutConvertPieceCount);
            returnGoodsOutPriceExcludeTax = ABCNumberUtils.round2Pretty(returnGoodsOutPriceExcludeTax);
            returnGoodsOutPrice = ABCNumberUtils.round2Pretty(returnGoodsOutPrice);
            // 发药出库
            dispenseCount = ABCNumberUtils.round2Pretty(dispenseCount);
            dispenseConvertPieceCount = ABCNumberUtils.round2Pretty(dispenseConvertPieceCount);
            dispensePriceExcludeTax = ABCNumberUtils.round2Pretty(dispensePriceExcludeTax);
            dispensePrice = ABCNumberUtils.round2Pretty(dispensePrice);
            // 门诊发药出库
            outPatientDispenseCount = ABCNumberUtils.round2Pretty(outPatientDispenseCount);
            outPatientDispenseConvertPieceCount = ABCNumberUtils.round2Pretty(outPatientDispenseConvertPieceCount);
            outPatientDispensePriceExcludeTax = ABCNumberUtils.round2Pretty(outPatientDispensePriceExcludeTax);
            outPatientDispensePrice = ABCNumberUtils.round2Pretty(outPatientDispensePrice);
            // 住院药房发药(已结算)出库
            hospitalPharmacyDispenseCount = ABCNumberUtils.round2Pretty(hospitalPharmacyDispenseCount);
            hospitalPharmacyDispenseConvertPieceCount = ABCNumberUtils.round2Pretty(hospitalPharmacyDispenseConvertPieceCount);
            hospitalPharmacyDispensePriceExcludeTax = ABCNumberUtils.round2Pretty(hospitalPharmacyDispensePriceExcludeTax);
            hospitalPharmacyDispensePrice = ABCNumberUtils.round2Pretty(hospitalPharmacyDispensePrice);
            // 住院记账发药(已结算)出库
            hospitalAutomaticDispenseCount = ABCNumberUtils.round2Pretty(hospitalAutomaticDispenseCount);
            hospitalAutomaticDispenseConvertPieceCount = ABCNumberUtils.round2Pretty(hospitalAutomaticDispenseConvertPieceCount);
            hospitalAutomaticDispensePriceExcludeTax = ABCNumberUtils.round2Pretty(hospitalAutomaticDispensePriceExcludeTax);
            hospitalAutomaticDispensePrice = ABCNumberUtils.round2Pretty(hospitalAutomaticDispensePrice);
            // 住院发药(未结算)出库
            hospitalNoSettleDispenseCount = ABCNumberUtils.round2Pretty(hospitalNoSettleDispenseCount);
            hospitalNoSettleDispenseConvertPieceCount = ABCNumberUtils.round2Pretty(hospitalNoSettleDispenseConvertPieceCount);
            hospitalNoSettleDispensePriceExcludeTax = ABCNumberUtils.round2Pretty(hospitalNoSettleDispensePriceExcludeTax);
            hospitalNoSettleDispensePrice = ABCNumberUtils.round2Pretty(hospitalNoSettleDispensePrice);
            // 领料出库
            collectOutCount = ABCNumberUtils.round2Pretty(collectOutCount);
            collectOutConvertPieceCount = ABCNumberUtils.round2Pretty(collectOutConvertPieceCount);
            // 调拨出库
            allotOutCount = ABCNumberUtils.round2Pretty(allotOutCount);
            allotOutConvertPieceCount = ABCNumberUtils.round2Pretty(allotOutConvertPieceCount);
            // 破损出库
            damagedOutCount = ABCNumberUtils.round2Pretty(damagedOutCount);
            damagedOutConvertPieceCount = ABCNumberUtils.round2Pretty(damagedOutConvertPieceCount);
            // 科室消耗
            outDepartmentConsumptionCount = ABCNumberUtils.round4Pretty(outDepartmentConsumptionCount);
            outDepartmentConsumptionConvertPieceCount = ABCNumberUtils.round4Pretty(outDepartmentConsumptionConvertPieceCount);
            // 领用退出
            outOtherCount = ABCNumberUtils.round4Pretty(outOtherCount);
            outOtherConvertPieceCount = ABCNumberUtils.round4Pretty(outOtherConvertPieceCount);
            // 盘亏出库
            checkOutCount = ABCNumberUtils.round2Pretty(checkOutCount);
            checkOutConvertPieceCount = ABCNumberUtils.round2Pretty(checkOutConvertPieceCount);
            checkOutPriceExcludeTax = ABCNumberUtils.round2Pretty(checkOutPriceExcludeTax);
            checkOutPrice = ABCNumberUtils.round2Pretty(checkOutPrice);
            // 生产出库
            productionOutCount = ABCNumberUtils.round2Pretty(productionOutCount);
            productionOutConvertPieceCount = ABCNumberUtils.round2Pretty(productionOutConvertPieceCount);
            // 配货出库
            deliveryOutCount = ABCNumberUtils.round2Pretty(deliveryOutCount);
            deliveryOutConvertPieceCount = ABCNumberUtils.round2Pretty(deliveryOutConvertPieceCount);
            // 出库合计
            outTotalCount = ABCNumberUtils.round2Pretty(outTotalCount);
            outTotalConvertPieceCount = ABCNumberUtils.round2Pretty(outTotalConvertPieceCount);
        }
        inInitReturnCostExcludeTax = ABCNumberUtils.round4Pretty(inInitReturnCostExcludeTax);
        inInitReturnCost = ABCNumberUtils.round4Pretty(inInitReturnCost);
        returnGoodsOutCostExcludeTax = ABCNumberUtils.round4Pretty(returnGoodsOutCostExcludeTax);
        returnGoodsOutCost = ABCNumberUtils.round4Pretty(returnGoodsOutCost);
        dispenseCostExcludeTax = ABCNumberUtils.round4Pretty(dispenseCostExcludeTax);
        dispenseCost = ABCNumberUtils.round4Pretty(dispenseCost);
        outPatientDispenseCostExcludeTax = ABCNumberUtils.round4Pretty(outPatientDispenseCostExcludeTax);
        outPatientDispenseCost = ABCNumberUtils.round4Pretty(outPatientDispenseCost);
        hospitalPharmacyDispenseCostExcludeTax = ABCNumberUtils.round4Pretty(hospitalPharmacyDispenseCostExcludeTax);
        hospitalPharmacyDispenseCost = ABCNumberUtils.round4Pretty(hospitalPharmacyDispenseCost);
        hospitalAutomaticDispenseCostExcludeTax = ABCNumberUtils.round4Pretty(hospitalAutomaticDispenseCostExcludeTax);
        hospitalAutomaticDispenseCost = ABCNumberUtils.round4Pretty(hospitalAutomaticDispenseCost);
        hospitalNoSettleDispenseCostExcludeTax = ABCNumberUtils.round4Pretty(hospitalNoSettleDispenseCostExcludeTax);
        hospitalNoSettleDispenseCost = ABCNumberUtils.round4Pretty(hospitalNoSettleDispenseCost);
        collectOutCostExcludeTax = ABCNumberUtils.round4Pretty(collectOutCostExcludeTax);
        collectOutCost = ABCNumberUtils.round4Pretty(collectOutCost);
        allotOutCostExcludeTax = ABCNumberUtils.round4Pretty(allotOutCostExcludeTax);
        allotOutCost = ABCNumberUtils.round4Pretty(allotOutCost);
        damagedOutCostExcludeTax = ABCNumberUtils.round4Pretty(damagedOutCostExcludeTax);
        damagedOutCost = ABCNumberUtils.round4Pretty(damagedOutCost);
        outDepartmentConsumptionCostAmount = ABCNumberUtils.round4Pretty(outDepartmentConsumptionCostAmount);
        outDepartmentConsumptionCostAmountExcludingTax = ABCNumberUtils
                .round4Pretty(outDepartmentConsumptionCostAmountExcludingTax);
        outOtherCostAmount = ABCNumberUtils.round4Pretty(outOtherCostAmount);
        outOtherCostAmountExcludingTax = ABCNumberUtils.round4Pretty(outOtherCostAmountExcludingTax);
        checkOutCostExcludeTax = ABCNumberUtils.round4Pretty(checkOutCostExcludeTax);
        checkOutCost = ABCNumberUtils.round4Pretty(checkOutCost);
        productionOutCostExcludeTax = ABCNumberUtils.round4Pretty(productionOutCostExcludeTax);
        productionOutCost = ABCNumberUtils.round4Pretty(productionOutCost);
        deliveryOutCostExcludeTax = ABCNumberUtils.round4Pretty(deliveryOutCostExcludeTax);
        deliveryOutCost = ABCNumberUtils.round4Pretty(deliveryOutCost);
        outTotalCostExcludeTax = ABCNumberUtils.round4Pretty(outTotalCostExcludeTax);
        outTotalCost = ABCNumberUtils.round4Pretty(outTotalCost);
    }

    /**
     * isOutExport
     */
    private void isOutExport() {
        // 初始化退货
        inInitReturnCount = ABCNumberUtils.round4Pretty(inInitReturnCount);
        inInitReturnConvertPieceCount = ABCNumberUtils.round4Pretty(inInitReturnConvertPieceCount);
        inInitReturnPriceExcludeTax = ABCNumberUtils.round4Pretty(inInitReturnPriceExcludeTax);
        inInitReturnPrice = ABCNumberUtils.round4Pretty(inInitReturnPrice);
        // 退货出库
        returnGoodsOutCount = ABCNumberUtils.round4Pretty(returnGoodsOutCount);
        returnGoodsOutConvertPieceCount = ABCNumberUtils.round4Pretty(returnGoodsOutConvertPieceCount);
        returnGoodsOutPriceExcludeTax = ABCNumberUtils.round4Pretty(returnGoodsOutPriceExcludeTax);
        returnGoodsOutPrice = ABCNumberUtils.round4Pretty(returnGoodsOutPrice);
        // 发药出库
        dispenseCount = ABCNumberUtils.round4Pretty(dispenseCount);
        dispenseConvertPieceCount = ABCNumberUtils.round4Pretty(dispenseConvertPieceCount);
        dispensePriceExcludeTax = ABCNumberUtils.round4Pretty(dispensePriceExcludeTax);
        dispensePrice = ABCNumberUtils.round4Pretty(dispensePrice);
        // 门诊发药出库
        outPatientDispenseCount = ABCNumberUtils.round4Pretty(outPatientDispenseCount);
        outPatientDispenseConvertPieceCount = ABCNumberUtils.round4Pretty(outPatientDispenseConvertPieceCount);
        outPatientDispensePriceExcludeTax = ABCNumberUtils.round4Pretty(outPatientDispensePriceExcludeTax);
        outPatientDispensePrice = ABCNumberUtils.round4Pretty(outPatientDispensePrice);
        // 住院药房发药(已结算)出库
        hospitalPharmacyDispenseCount = ABCNumberUtils.round4Pretty(hospitalPharmacyDispenseCount);
        hospitalPharmacyDispenseConvertPieceCount = ABCNumberUtils.round4Pretty(hospitalPharmacyDispenseConvertPieceCount);
        hospitalPharmacyDispensePriceExcludeTax = ABCNumberUtils.round4Pretty(hospitalPharmacyDispensePriceExcludeTax);
        hospitalPharmacyDispensePrice = ABCNumberUtils.round4Pretty(hospitalPharmacyDispensePrice);
        // 住院记账发药(已结算)出库
        hospitalAutomaticDispenseCount = ABCNumberUtils.round4Pretty(hospitalAutomaticDispenseCount);
        hospitalAutomaticDispenseConvertPieceCount = ABCNumberUtils.round4Pretty(hospitalAutomaticDispenseConvertPieceCount);
        hospitalAutomaticDispensePriceExcludeTax = ABCNumberUtils.round4Pretty(hospitalAutomaticDispensePriceExcludeTax);
        hospitalAutomaticDispensePrice = ABCNumberUtils.round4Pretty(hospitalAutomaticDispensePrice);
        // 住院发药(未结算)出库
        hospitalNoSettleDispenseCount = ABCNumberUtils.round4Pretty(hospitalNoSettleDispenseCount);
        hospitalNoSettleDispenseConvertPieceCount = ABCNumberUtils.round4Pretty(hospitalNoSettleDispenseConvertPieceCount);
        hospitalNoSettleDispensePriceExcludeTax = ABCNumberUtils.round4Pretty(hospitalNoSettleDispensePriceExcludeTax);
        hospitalNoSettleDispensePrice = ABCNumberUtils.round4Pretty(hospitalNoSettleDispensePrice);
        // 领料出库
        collectOutCount = ABCNumberUtils.round4Pretty(collectOutCount);
        collectOutConvertPieceCount = ABCNumberUtils.round4Pretty(collectOutConvertPieceCount);
        // 调拨出库
        allotOutCount = ABCNumberUtils.round4Pretty(allotOutCount);
        allotOutConvertPieceCount = ABCNumberUtils.round4Pretty(allotOutConvertPieceCount);
        // 破损出库
        damagedOutCount = ABCNumberUtils.round4Pretty(damagedOutCount);
        damagedOutConvertPieceCount = ABCNumberUtils.round4Pretty(damagedOutConvertPieceCount);
        // 科室消耗
        outDepartmentConsumptionCount = ABCNumberUtils.round4Pretty(outDepartmentConsumptionCount);
        outDepartmentConsumptionConvertPieceCount = ABCNumberUtils.round4Pretty(outDepartmentConsumptionConvertPieceCount);
        // 其他出库
        outOtherCount = ABCNumberUtils.round4Pretty(outOtherCount);
        outOtherConvertPieceCount = ABCNumberUtils.round4Pretty(outOtherConvertPieceCount);
        // 盘亏出库
        checkOutCount = ABCNumberUtils.round4Pretty(checkOutCount);
        checkOutConvertPieceCount = ABCNumberUtils.round4Pretty(checkOutConvertPieceCount);
        checkOutPriceExcludeTax = ABCNumberUtils.round4Pretty(checkOutPriceExcludeTax);
        checkOutPrice = ABCNumberUtils.round4Pretty(checkOutPrice);
        // 生产出库
        productionOutCount = ABCNumberUtils.round4Pretty(productionOutCount);
        productionOutConvertPieceCount = ABCNumberUtils.round4Pretty(productionOutConvertPieceCount);
        productionOutPriceExcludeTax = ABCNumberUtils.round4Pretty(productionOutPriceExcludeTax);
        productionOutPrice = ABCNumberUtils.round4Pretty(productionOutPrice);
        // 配货出库
        deliveryOutCount = ABCNumberUtils.round4Pretty(deliveryOutCount);
        deliveryOutConvertPieceCount = ABCNumberUtils.round4Pretty(deliveryOutConvertPieceCount);
        deliveryOutPriceExcludeTax = ABCNumberUtils.round4Pretty(deliveryOutPriceExcludeTax);
        deliveryOutPrice = ABCNumberUtils.round4Pretty(deliveryOutPrice);
        // 出库合计
        outTotalCount = ABCNumberUtils.round4Pretty(outTotalCount);
        outTotalConvertPieceCount = ABCNumberUtils.round4Pretty(outTotalConvertPieceCount);
    }

    /**
     * 设置大小单位
     *
     * @param packageUnit 药品大单位
     * @param pieceUnit   药品小单位
     */
    public void setUnit(String packageUnit, String pieceUnit) {
        // 期初库存
        beginCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(beginPackageCount, packageUnit, beginPieceCount, pieceUnit);
        beginConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, beginConvertPieceCount, pieceUnit);
        beginPackageCountText = buildPackageCountText(beginPackageCount, packageUnit);
        beginPieceCountText = buildPackageCountText(beginPieceCount, pieceUnit);

        // 入库
        setUnitIn(packageUnit, pieceUnit);

        // 出库
        inInitReturnCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(inInitReturnPackageCount, packageUnit, inInitReturnPieceCount, pieceUnit);
        inInitReturnConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, inInitReturnConvertPieceCount, pieceUnit);
        inInitReturnPackageCountText = buildPackageCountText(inInitReturnPackageCount, packageUnit);
        inInitReturnPieceCountText = buildPackageCountText(inInitReturnPieceCount, pieceUnit);

        returnGoodsOutCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(returnGoodsOutPackageCount, packageUnit, returnGoodsOutPieceCount, pieceUnit);
        returnGoodsOutConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, returnGoodsOutConvertPieceCount, pieceUnit);
        returnGoodsOutPackageCountText = buildPackageCountText(returnGoodsOutPackageCount, packageUnit);
        returnGoodsOutPieceCountText = buildPackageCountText(returnGoodsOutPieceCount, pieceUnit);

        dispenseCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(dispensePackageCount, packageUnit, dispensePieceCount, pieceUnit);
        dispenseConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, dispenseConvertPieceCount, pieceUnit);
        dispensePackageCountText = buildPackageCountText(dispensePackageCount, packageUnit);
        dispensePieceCountText = buildPackageCountText(dispensePieceCount, pieceUnit);

        outPatientDispenseCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(outPatientDispensePackageCount, packageUnit, outPatientDispensePieceCount, pieceUnit);
        outPatientDispenseConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, outPatientDispenseConvertPieceCount, pieceUnit);
        outPatientDispensePackageCountText = buildPackageCountText(outPatientDispensePackageCount, packageUnit);
        outPatientDispensePieceCountText = buildPackageCountText(outPatientDispensePieceCount, pieceUnit);

        hospitalPharmacyDispenseCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(hospitalPharmacyDispensePackageCount, packageUnit, hospitalPharmacyDispensePieceCount, pieceUnit);
        hospitalPharmacyDispenseConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, hospitalPharmacyDispenseConvertPieceCount, pieceUnit);
        hospitalPharmacyDispensePackageCountText = buildPackageCountText(hospitalPharmacyDispensePackageCount, packageUnit);
        hospitalPharmacyDispensePieceCountText = buildPackageCountText(hospitalPharmacyDispensePieceCount, pieceUnit);

        hospitalAutomaticDispenseCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(hospitalAutomaticDispensePackageCount, packageUnit, hospitalAutomaticDispensePieceCount, pieceUnit);
        hospitalAutomaticDispenseConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, hospitalAutomaticDispenseConvertPieceCount, pieceUnit);
        hospitalAutomaticDispensePackageCountText = buildPackageCountText(hospitalAutomaticDispensePackageCount, packageUnit);
        hospitalAutomaticDispensePieceCountText = buildPackageCountText(hospitalAutomaticDispensePieceCount, pieceUnit);

        hospitalNoSettleDispenseCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(hospitalNoSettleDispensePackageCount, packageUnit, hospitalNoSettleDispensePieceCount, pieceUnit);
        hospitalNoSettleDispenseConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, hospitalNoSettleDispenseConvertPieceCount, pieceUnit);
        hospitalNoSettleDispensePackageCountText = buildPackageCountText(hospitalNoSettleDispensePackageCount, packageUnit);
        hospitalNoSettleDispensePieceCountText = buildPackageCountText(hospitalNoSettleDispensePieceCount, pieceUnit);

        collectOutCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(collectOutPackageCount, packageUnit, collectOutPieceCount, pieceUnit);
        collectOutConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, collectOutConvertPieceCount, pieceUnit);
        collectOutPackageCountText = buildPackageCountText(collectOutPackageCount, packageUnit);
        collectOutPieceCountText = buildPackageCountText(collectOutPieceCount, pieceUnit);

        allotOutCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(allotOutPackageCount, packageUnit, allotOutPieceCount, pieceUnit);
        allotOutConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, allotOutConvertPieceCount, pieceUnit);
        allotOutPackageCountText = buildPackageCountText(allotOutPackageCount, packageUnit);
        allotOutPieceCountText = buildPackageCountText(allotOutPieceCount, pieceUnit);

        damagedOutCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(damagedOutPackageCount, packageUnit, damagedOutPieceCount, pieceUnit);
        damagedOutConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, damagedOutConvertPieceCount, pieceUnit);
        damagedOutPackageCountText = buildPackageCountText(damagedOutPackageCount, packageUnit);
        damagedOutPieceCountText = buildPackageCountText(damagedOutPieceCount, pieceUnit);

        outDepartmentConsumptionCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(outDepartmentConsumptionPackageCount, packageUnit,
                outDepartmentConsumptionPieceCount, pieceUnit);
        outDepartmentConsumptionConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit,
                outDepartmentConsumptionConvertPieceCount, pieceUnit);
        outDepartmentConsumptionPackageCountText = buildPackageCountText(outDepartmentConsumptionPackageCount,
                packageUnit);
        outDepartmentConsumptionPieceCountText = buildPackageCountText(outDepartmentConsumptionPieceCount, pieceUnit);

        outOtherCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(outOtherPackageCount, packageUnit, outOtherPieceCount, pieceUnit);
        outOtherConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, outOtherConvertPieceCount, pieceUnit);
        outOtherPackageCountText = buildPackageCountText(outOtherPackageCount, packageUnit);
        outOtherPieceCountText = buildPackageCountText(outOtherPieceCount, pieceUnit);

        checkOutCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(checkOutPackageCount, packageUnit, checkOutPieceCount, pieceUnit);
        checkOutConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, checkOutConvertPieceCount, pieceUnit);
        checkOutPackageCountText = buildPackageCountText(checkOutPackageCount, packageUnit);
        checkOutPieceCountText = buildPackageCountText(checkOutPieceCount, pieceUnit);

        productionOutCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(productionOutPackageCount, packageUnit, productionOutPieceCount, pieceUnit);
        productionOutConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, productionOutConvertPieceCount, pieceUnit);
        productionOutPackageCountText = buildPackageCountText(productionOutPackageCount, packageUnit);
        productionOutPieceCountText = buildPackageCountText(productionOutPieceCount, pieceUnit);

        deliveryOutCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(deliveryOutPackageCount, packageUnit, deliveryOutPieceCount, pieceUnit);
        deliveryOutConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, deliveryOutConvertPieceCount, pieceUnit);
        deliveryOutPackageCountText = buildPackageCountText(deliveryOutPackageCount, packageUnit);
        deliveryOutPieceCountText = buildPackageCountText(deliveryOutPieceCount, pieceUnit);

        outTotalCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(outTotalPackageCount, packageUnit, outTotalPieceCount, pieceUnit);
        outTotalConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, outTotalConvertPieceCount, pieceUnit);

        // 期末库存
        endCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(endPackageCount, packageUnit, endPieceCount, pieceUnit);
        endConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, endConvertPieceCount, pieceUnit);
        endPackageCountText = buildPackageCountText(endPackageCount, packageUnit);
        endPieceCountText = buildPackageCountText(endPieceCount, pieceUnit);
    }

    /**
     * @param packageUnit -
     * @param pieceUnit   -
     */
    private void setUnitIn(String packageUnit, String pieceUnit) {
        inInitCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(inInitPackageCount, packageUnit, inInitPieceCount, pieceUnit);
        inInitConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, inInitConvertPieceCount, pieceUnit);
        inInitPackageCountText = buildPackageCountText(inInitPackageCount, packageUnit);
        inInitPieceCountText = buildPackageCountText(inInitPieceCount, pieceUnit);

        purchaseInCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(purchaseInPackageCount, packageUnit, purchaseInPieceCount, pieceUnit);
        purchaseInConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, purchaseInConvertPieceCount, pieceUnit);
        purchaseInPackageCountText = buildPackageCountText(purchaseInPackageCount, packageUnit);
        purchaseInPieceCountText = buildPackageCountText(purchaseInPieceCount, pieceUnit);

        inReceiveCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(inReceivePackageCount, packageUnit, inReceivePieceCount, pieceUnit);
        inReceiveConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, inReceiveConvertPieceCount, pieceUnit);
        inReceivePackageCountText = buildPackageCountText(inReceivePackageCount, packageUnit);
        inReceivePieceCountText = buildPackageCountText(inReceivePieceCount, pieceUnit);

        allotInCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(allotInPackageCount, packageUnit, allotInPieceCount, pieceUnit);
        allotInConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, allotInConvertPieceCount, pieceUnit);
        allotInPackageCountText = buildPackageCountText(allotInPackageCount, packageUnit);
        allotInPieceCountText = buildPackageCountText(allotInPieceCount, pieceUnit);

        checkInCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(checkInPackageCount, packageUnit, checkInPieceCount, pieceUnit);
        checkInConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, checkInConvertPieceCount, pieceUnit);
        checkInPackageCountText = buildPackageCountText(checkInPackageCount, packageUnit);
        checkInPieceCountText = buildPackageCountText(checkInPieceCount, pieceUnit);

        inTotalCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(inTotalPackageCount, packageUnit, inTotalPieceCount, pieceUnit);
        inTotalConvertPieceCountText = StringUtils.isEmpty(packageUnit) && StringUtils.isEmpty(pieceUnit) ? "0.00"
                : buildCountText(null, packageUnit, inTotalConvertPieceCount, pieceUnit);
    }


    /**
     * @param packageCount packageCount
     * @param packageUnit  packageUnit
     * @param pieceCount   pieceCount
     * @param pieceUnit    pieceUnit
     * @return String
     */
    private String buildCountText(BigDecimal packageCount, String packageUnit, BigDecimal pieceCount,
                                  String pieceUnit) {
        StringBuilder sb = new StringBuilder();

        //大单位小单位都没有值显示为0
        if ((packageCount == null || packageCount.compareTo(BigDecimal.ZERO) == 0) && (pieceCount == null
                || pieceCount.compareTo(BigDecimal.ZERO) == 0)) {
            sb.append("0");
            return sb.toString();
        }
        if (packageCount != null && packageCount.compareTo(BigDecimal.ZERO) != 0) {
            sb.append(ABCNumberUtils.round2TextPretty(packageCount));
            sb.append(packageUnit);
        }

        // 不为空并且计量单位不为-的为中药需要小单位
        if (!StrUtil.isBlank(pieceUnit) && !pieceUnit.equals("-")) {
            if (pieceCount != null && pieceCount.compareTo(BigDecimal.ZERO) != 0) {
                String pieceCountStr = ABCNumberUtils.round2TextPretty(pieceCount);
                if (Math.abs(pieceCount.doubleValue()) >= 1) {
                    sb.append(pieceCountStr);
                } else {
                    if (sb.length() > 0) {
                        String[] split = pieceCountStr.split("\\.");
                        if (split.length > 1) {
                            sb.append(".").append(split[1]);
                        }
                    } else {
                        sb.append(pieceCountStr);
                    }
                }
                sb.append(pieceUnit);
            }
        } else {
            if (pieceCount != null && pieceCount.compareTo(BigDecimal.ZERO) != 0) {
                String pieceCountStr = ABCNumberUtils.round2TextPretty(pieceCount);
                if (Math.abs(pieceCount.doubleValue()) >= 1) {
                    sb.append(pieceCountStr);
                } else {
                    if (sb.length() > 0) {
                        String[] split = pieceCountStr.split("\\.");
                        if (split.length > 1) {
                            sb.append(".").append(split[1]);
                        }
                    } else {
                        sb.append(pieceCountStr);
                    }
                }
                sb.append(pieceUnit);
            }
        }


        return sb.toString();
    }

    /**
     * 拼接大单位数量以及大单位
     *
     * @param packageCount 大单位数量
     * @param packageUnit  大单位
     * @return String
     */
    private String buildPackageCountText(BigDecimal packageCount, String packageUnit) {
        StringBuilder sb = new StringBuilder();
        //大单位小单位都没有值显示为0
        if (packageCount == null || packageCount.compareTo(BigDecimal.ZERO) == 0
                || packageUnit == null || packageUnit.length() <= 0) {
            return sb.toString();
        }
        sb.append(ABCNumberUtils.round2TextPretty(packageCount));
        sb.append(packageUnit);
        return sb.toString();
    }

    /**
     * 拼接小单位数量以及小单位
     *
     * @param pieceCount 大单位数量
     * @param pieceUnit  大单位
     * @return String
     */
    private String buildPieceCountText(BigDecimal pieceCount, String pieceUnit) {
        StringBuilder sb = new StringBuilder();
        if (pieceCount != null && pieceCount.compareTo(BigDecimal.ZERO) != 0) {
            sb.append(ABCNumberUtils.round2TextPretty(pieceCount));
            sb.append(pieceUnit);
        }

        return sb.toString();
    }

    /**
     * 设置值为null
     */
    public void setCostNull() {
        // 期初以及期末数据成本为null
        beginAndEndDataCostSetNull();

        // 入库数据成本为null
        inDataCostSetNull();

        // 出库数据成本为null
        outDataCostSetNull();
    }

    /**
     * 期初以及期末数据成本为null
     */
    private void beginAndEndDataCostSetNull() {
        beginCostExcludeTax = null;
        beginCost = null;

        endCostExcludeTax = null;
        endCost = null;
    }

    /**
     * 入库数据成本为null
     */
    private void inDataCostSetNull() {
        // 初始化入库
        inInitCostExcludeTax = null;
        inInitCost = null;
        // 采购入库
        purchaseInCostExcludeTax = null;
        purchaseInCost = null;
        // 领用入库
        inReceiveCostAmount = null;
        inReceiveCostAmountExcludingTax = null;
        inReceiveAmount = null;
        inReceiveAmountExcludingTax = null;
        // 调拨入库
        allotInCostExcludeTax = null;
        allotInCost = null;
        // 盘盈入库
        checkInPriceExcludeTax = null;
        checkInPrice = null;
        checkInCostExcludeTax = null;
        checkInCost = null;
        // 规格修改
        inSpecificationModificationCostExcludeTax = null;
        inSpecificationModificationCost = null;
        // 入库合计
        inTotalCostExcludeTax = null;
        inTotalCost = null;
        //税率修正
        afterTotalCostModifyExcludeTax = null;
        goodsAfterTotalCostModifyExcludeTax = null;
        goodsPharmacyAfterTotalCostModifyExcludeTax = null;
    }

    /**
     * 出库数据成本为null
     */
    private void outDataCostSetNull() {
        // 初始化退货
        inInitReturnPriceExcludeTax = null;
        inInitReturnPrice = null;
        inInitReturnCostExcludeTax = null;
        inInitReturnCost = null;
        // 退货出库
        returnGoodsOutPriceExcludeTax = null;
        returnGoodsOutPrice = null;
        returnGoodsOutCostExcludeTax = null;
        returnGoodsOutCost = null;
        // 发药出库
        dispensePriceExcludeTax = null;
        dispensePrice = null;
        dispenseCostExcludeTax = null;
        dispenseCost = null;
        // 门诊发药出库
        outPatientDispensePriceExcludeTax = null;
        outPatientDispensePrice = null;
        outPatientDispenseCostExcludeTax = null;
        outPatientDispenseCost = null;
        // 住院药房发药(已结算)出库
        hospitalPharmacyDispensePriceExcludeTax = null;
        hospitalPharmacyDispensePrice = null;
        hospitalPharmacyDispenseCostExcludeTax = null;
        hospitalPharmacyDispenseCost = null;
        // 住院记账发药(已结算)出库
        hospitalAutomaticDispensePriceExcludeTax = null;
        hospitalAutomaticDispensePrice = null;
        hospitalAutomaticDispenseCostExcludeTax = null;
        hospitalAutomaticDispenseCost = null;
        // 住院发药(未结算)出库
        hospitalNoSettleDispensePriceExcludeTax = null;
        hospitalNoSettleDispensePrice = null;
        hospitalNoSettleDispenseCostExcludeTax = null;
        hospitalNoSettleDispenseCost = null;
        // 领料出库
        collectOutCostExcludeTax = null;
        collectOutCost = null;
        // 调拨出库
        allotOutCostExcludeTax = null;
        allotOutCost = null;
        // 破损出库
        damagedOutCostExcludeTax = null;
        damagedOutCost = null;
        // 科室消耗
        outDepartmentConsumptionCostAmount = null;
        outDepartmentConsumptionCostAmountExcludingTax = null;
        // 其他出库
        outOtherCostAmount = null;
        outOtherCostAmountExcludingTax = null;
        // 盘亏出库
        checkOutPriceExcludeTax = null;
        checkOutPrice = null;
        checkOutCostExcludeTax = null;
        checkOutCost = null;
        // 生产出库
        productionOutCostExcludeTax = null;
        productionOutCost = null;
        // 出库合计
        outTotalCostExcludeTax = null;
        outTotalCost = null;
    }

    /**
     * 根据pieceNumber 调整keyData展示数据
     *
     * @param pieceNumber 小单位数量
     */
    public void changeKeyDataByPieceNumber(Integer pieceNumber) {
        changePieceNumberIn(pieceNumber);
        if (pieceNumber != null) {
            int inInitReturnConvertPieceNumber = inInitReturnPackageCount.intValue() != 0 ? inInitReturnPackageCount.intValue() * pieceNumber + inInitReturnPieceCount.intValue() : inInitReturnPieceCount.intValue();
            inInitReturnPackageCount = new BigDecimal(inInitReturnConvertPieceNumber / pieceNumber);
            inInitReturnPieceCount = new BigDecimal(inInitReturnConvertPieceNumber - inInitReturnPackageCount.intValue() * pieceNumber);

            int returnGoodsOutConvertPieceNumber = returnGoodsOutPackageCount.intValue() != 0 ? returnGoodsOutPackageCount.intValue() * pieceNumber + returnGoodsOutPieceCount.intValue() : returnGoodsOutPieceCount.intValue();
            returnGoodsOutPackageCount = new BigDecimal(returnGoodsOutConvertPieceNumber / pieceNumber);
            returnGoodsOutPieceCount = new BigDecimal(returnGoodsOutConvertPieceNumber - returnGoodsOutPackageCount.intValue() * pieceNumber);

            int dispenseConvertPieceNumber = dispensePackageCount.intValue() != 0 ? dispensePackageCount.intValue() * pieceNumber + dispensePieceCount.intValue() : dispensePieceCount.intValue();
            dispensePackageCount = new BigDecimal(dispenseConvertPieceNumber / pieceNumber);
            dispensePieceCount = new BigDecimal(dispenseConvertPieceNumber - dispensePackageCount.intValue() * pieceNumber);

            int outPatientDispenseConvertPieceNumber = outPatientDispensePackageCount.intValue() != 0 ? outPatientDispensePackageCount.intValue() * pieceNumber + outPatientDispensePieceCount.intValue() : outPatientDispensePieceCount.intValue();
            outPatientDispensePackageCount = new BigDecimal(outPatientDispenseConvertPieceNumber / pieceNumber);
            outPatientDispensePieceCount = new BigDecimal(outPatientDispenseConvertPieceNumber - outPatientDispensePackageCount.intValue() * pieceNumber);

            int hospitalPharmacyDispenseConvertPieceNumber = hospitalPharmacyDispensePackageCount.intValue() != 0 ? hospitalPharmacyDispensePackageCount.intValue() * pieceNumber + hospitalPharmacyDispensePieceCount.intValue() : hospitalPharmacyDispensePieceCount.intValue();
            hospitalPharmacyDispensePackageCount = new BigDecimal(hospitalPharmacyDispenseConvertPieceNumber / pieceNumber);
            hospitalPharmacyDispensePieceCount = new BigDecimal(hospitalPharmacyDispenseConvertPieceNumber - hospitalPharmacyDispensePackageCount.intValue() * pieceNumber);

            int hospitalAutomaticDispenseConvertPieceNumber = hospitalAutomaticDispensePackageCount.intValue() != 0 ? hospitalAutomaticDispensePackageCount.intValue() * pieceNumber + hospitalAutomaticDispensePieceCount.intValue() : hospitalAutomaticDispensePieceCount.intValue();
            hospitalAutomaticDispensePackageCount = new BigDecimal(hospitalAutomaticDispenseConvertPieceNumber / pieceNumber);
            hospitalAutomaticDispensePieceCount = new BigDecimal(hospitalAutomaticDispenseConvertPieceNumber - hospitalAutomaticDispensePackageCount.intValue() * pieceNumber);

            int hospitalNoSettleDispenseConvertPieceNumber = hospitalNoSettleDispensePackageCount.intValue() != 0 ? hospitalNoSettleDispensePackageCount.intValue() * pieceNumber + hospitalNoSettleDispensePieceCount.intValue() : hospitalNoSettleDispensePieceCount.intValue();
            hospitalNoSettleDispensePackageCount = new BigDecimal(hospitalNoSettleDispenseConvertPieceNumber / pieceNumber);
            hospitalNoSettleDispensePieceCount = new BigDecimal(hospitalNoSettleDispenseConvertPieceNumber - hospitalNoSettleDispensePackageCount.intValue() * pieceNumber);

            int collectOutConvertPieceNumber = collectOutPackageCount.intValue() != 0 ? collectOutPackageCount.intValue() * pieceNumber + collectOutPieceCount.intValue() : collectOutPieceCount.intValue();
            collectOutPackageCount = new BigDecimal(collectOutConvertPieceNumber / pieceNumber);
            collectOutPieceCount = new BigDecimal(collectOutConvertPieceNumber - collectOutPackageCount.intValue() * pieceNumber);

            int outTotalConvertPieceNumber = outTotalPackageCount.intValue() != 0 ? outTotalPackageCount.intValue() * pieceNumber + outTotalPieceCount.intValue() : outTotalPieceCount.intValue();
            outTotalPackageCount = new BigDecimal(outTotalConvertPieceNumber / pieceNumber);
            outTotalPieceCount = new BigDecimal(outTotalConvertPieceNumber - outTotalPackageCount.intValue() * pieceNumber);
        }
        if (pieceNumber != null && allotOutPieceCount.abs().intValue() > pieceNumber) {
            int addNumber = allotOutPieceCount.abs().intValue() / pieceNumber;
            allotOutPackageCount = allotOutPackageCount.subtract(new BigDecimal(addNumber));
            allotOutPieceCount = allotOutPieceCount.add(new BigDecimal(addNumber * pieceNumber));
        }
        if (pieceNumber != null && damagedOutPieceCount.abs().intValue() > pieceNumber) {
            int addNumber = damagedOutPieceCount.abs().intValue() / pieceNumber;
            damagedOutPackageCount = damagedOutPackageCount.subtract(new BigDecimal(addNumber));
            damagedOutPieceCount = damagedOutPieceCount.add(new BigDecimal(addNumber * pieceNumber));
        }
        if (pieceNumber != null && outDepartmentConsumptionPieceCount.abs().intValue() > pieceNumber) {
            int addNumber = outDepartmentConsumptionPieceCount.abs().intValue() / pieceNumber;
            outDepartmentConsumptionPackageCount = outDepartmentConsumptionPackageCount
                    .subtract(new BigDecimal(addNumber));
            outDepartmentConsumptionPieceCount = outDepartmentConsumptionPieceCount
                    .add(new BigDecimal(addNumber * pieceNumber));
        }
        if (pieceNumber != null && outOtherPieceCount.abs().intValue() > pieceNumber) {
            int addNumber = outOtherPieceCount.abs().intValue() / pieceNumber;
            outOtherPackageCount = outOtherPackageCount.subtract(new BigDecimal(addNumber));
            outOtherPieceCount = outOtherPieceCount.add(new BigDecimal(addNumber * pieceNumber));
        }
        if (pieceNumber != null && checkOutPieceCount.abs().intValue() > pieceNumber) {
            int addNumber = checkOutPieceCount.abs().intValue() / pieceNumber;
            checkOutPackageCount = checkOutPackageCount.subtract(new BigDecimal(addNumber));
            checkOutPieceCount = checkOutPieceCount.add(new BigDecimal(addNumber * pieceNumber));
        }
        if (pieceNumber != null && outTotalPieceCount.abs().intValue() > pieceNumber) {
            int outTotalAddNumber = outTotalPieceCount.abs().intValue() / pieceNumber;
            outTotalPackageCount = outTotalPackageCount.subtract(new BigDecimal(outTotalAddNumber));
            outTotalPieceCount = outTotalPieceCount.add(new BigDecimal(outTotalAddNumber * pieceNumber));
        }
    }

    /**
     * @param pieceNumber -
     */
    private void changePieceNumberIn(Integer pieceNumber) {
        if (pieceNumber != null && inInitPieceCount.intValue() > pieceNumber) {
            int addNumber = inInitPieceCount.intValue() / pieceNumber;
            inInitPackageCount = inInitPackageCount.add(new BigDecimal(addNumber));
            inInitPieceCount = inInitPieceCount.subtract(new BigDecimal(addNumber * pieceNumber));
        }
        if (pieceNumber != null) {
            int purchaseInConvertPieceNumber = purchaseInPackageCount.intValue() != 0 ? purchaseInPackageCount.intValue() * pieceNumber + purchaseInPieceCount.intValue() : purchaseInPieceCount.intValue();
            purchaseInPackageCount = new BigDecimal(purchaseInConvertPieceNumber / pieceNumber);
            purchaseInPieceCount = new BigDecimal(purchaseInConvertPieceNumber - purchaseInPackageCount.intValue() * pieceNumber);
            int inReceiveConvertPieceNumber = inReceivePackageCount.intValue() != 0 ? inReceivePackageCount.intValue() * pieceNumber + inReceivePieceCount.intValue() : inReceivePieceCount.intValue();
            inReceivePackageCount = new BigDecimal(inReceiveConvertPieceNumber / pieceNumber);
            inReceivePieceCount = new BigDecimal(inReceiveConvertPieceNumber - inReceivePackageCount.intValue() * pieceNumber);
            int inTotalConvertPieceNumber = inTotalPackageCount.intValue() != 0 ? inTotalPackageCount.intValue() * pieceNumber + inTotalPieceCount.intValue() : inTotalPieceCount.intValue();
            inTotalPackageCount = new BigDecimal(inTotalConvertPieceNumber / pieceNumber);
            inTotalPieceCount = new BigDecimal(inTotalConvertPieceNumber - inTotalPackageCount.intValue() * pieceNumber);
        }
        if (pieceNumber != null && allotInPieceCount.intValue() > pieceNumber) {
            int addNumber = allotInPieceCount.intValue() / pieceNumber;
            allotInPackageCount = allotInPackageCount.add(new BigDecimal(addNumber));
            allotInPieceCount = allotInPieceCount.subtract(new BigDecimal(addNumber * pieceNumber));
        }
        if (pieceNumber != null && checkInPieceCount.intValue() > pieceNumber) {
            int addNumber = checkInPieceCount.intValue() / pieceNumber;
            checkInPackageCount = checkInPackageCount.add(new BigDecimal(addNumber));
            checkInPieceCount = checkInPieceCount.subtract(new BigDecimal(addNumber * pieceNumber));
        }
    }

    /**
     * 设置期初期末数据
     *
     * @param beginIgList 期初数据
     * @param endIgList   期末数据
     */
    public void setBeginAndEndData(List<InventoryGoods> beginIgList, List<InventoryGoods> endIgList) {
        if (!CollUtil.isEmpty(beginIgList)) {
            InventoryGoods beginIg = beginIgList.get(0);
            this.beginCount = beginIg.getBeginCount();
            this.beginConvertPieceCount = beginIg.getBeginConvertPieceCount();
            this.beginCost = beginIg.getBeginCost();
            this.beginCostExcludeTax = beginIg.getBeginCostExcludeTax();
        }
        if (!CollUtil.isEmpty(endIgList)) {
            InventoryGoods endIg = endIgList.get(0);
            this.endCount = endIg.getEndCount();
            this.endConvertPieceCount = endIg.getEndConvertPieceCount();
            this.endCost = endIg.getEndCost();
            this.endCostExcludeTax = endIg.getEndCostExcludeTax();
            this.feeType1 = endIg.getFeeType1();
            this.feeType2 = endIg.getFeeType2();
            this.profitCategoryTypeId = endIg.getProfitCategoryTypeId();
        }
    }

    /**
     * 设置期初期末合计行数据
     *
     * @param beginIg 期初数据
     * @param endIg   期末数据
     */
    public void setBeginAndEndDataSummary(InventoryGoods beginIg, InventoryGoods endIg) {
        this.beginCount = beginIg.getBeginCount();
        this.beginConvertPieceCount = beginIg.getBeginConvertPieceCount();
        this.beginCost = beginIg.getBeginCost();
        this.beginCostExcludeTax = beginIg.getBeginCostExcludeTax();
        this.endCount = endIg.getEndCount();
        this.endConvertPieceCount = endIg.getEndConvertPieceCount();
        this.endCost = endIg.getEndCost();
        this.endCostExcludeTax = endIg.getEndCostExcludeTax();
    }

    /**
     * 设置期初期末合计行数据
     *
     * @param goodsList 期初数据
     */
    public void setTaxModify(List<InventoryGoods> goodsList) {
        if (!CollUtil.isEmpty(goodsList)) {
            InventoryGoods goods = goodsList.get(0);
            this.beforeInTaxRat = goods.getBeforeInTaxRat();
            this.beforeOutTaxRat = goods.getBeforeOutTaxRat();
            this.afterTotalCostModifyExcludeTax = goods.getAfterTotalCostModifyExcludeTax();
            this.goodsAfterTotalCostModifyExcludeTax = goods.getGoodsAfterTotalCostModifyExcludeTax();
            this.goodsPharmacyAfterTotalCostModifyExcludeTax = goods.getGoodsPharmacyAfterTotalCostModifyExcludeTax();
            this.effectedTime = goods.getEffectedTime();
            this.lastModifiedBy = goods.getLastModifiedBy();
            this.lastModified = goods.getLastModified();
        }
    }

    public void setSummaryData(InventoryGoods inventoryGoods) {
        this.beginCount = this.beginCount.add(inventoryGoods.getBeginCount());
        this.beginConvertPieceCount = this.beginConvertPieceCount.add(inventoryGoods.getBeginConvertPieceCount());
        this.beginCost = this.beginCost.add(inventoryGoods.getBeginCost());
        this.beginCostExcludeTax = this.beginCostExcludeTax.add(inventoryGoods.getBeginCostExcludeTax());

        this.inInitCount = this.inInitCount.add(inventoryGoods.getInInitCount());
        this.inInitConvertPieceCount = this.inInitConvertPieceCount.add(inventoryGoods.getInInitConvertPieceCount());
        this.inInitCost = this.inInitCost.add(inventoryGoods.getInInitCost());
        this.inInitCostExcludeTax = this.inInitCostExcludeTax.add(inventoryGoods.getInInitCostExcludeTax());

        this.purchaseInCount = this.purchaseInCount.add(inventoryGoods.getPurchaseInCount());
        this.purchaseInConvertPieceCount = this.purchaseInConvertPieceCount.add(inventoryGoods.getPurchaseInConvertPieceCount());
        this.purchaseInCost = this.purchaseInCost.add(inventoryGoods.getPurchaseInCost());
        this.purchaseInCostExcludeTax = this.purchaseInCostExcludeTax.add(inventoryGoods.getPurchaseInCostExcludeTax());

        this.allotInCount = this.allotInCount.add(inventoryGoods.getAllotInCount());
        this.allotInConvertPieceCount = this.allotInConvertPieceCount.add(inventoryGoods.getAllotInConvertPieceCount());
        this.allotInCost = this.allotInCost.add(inventoryGoods.getAllotInCost());
        this.allotInCostExcludeTax = this.allotInCostExcludeTax.add(inventoryGoods.getAllotInCostExcludeTax());

        this.checkInCount = this.checkInCount.add(inventoryGoods.getCheckInCount());
        this.checkInConvertPieceCount = this.checkInConvertPieceCount.add(inventoryGoods.getCheckInConvertPieceCount());
        this.checkInCost = this.checkInCost.add(inventoryGoods.getCheckInCost());
        this.checkInCostExcludeTax = this.checkInCostExcludeTax.add(inventoryGoods.getCheckInCostExcludeTax());
        this.checkInPrice = this.checkInPrice.add(inventoryGoods.getCheckInPrice());
        this.checkInPriceExcludeTax = this.checkInPriceExcludeTax.add(inventoryGoods.getCheckInPriceExcludeTax());

        this.inReceiveCount = this.inReceiveCount.add(inventoryGoods.getInReceiveCount());
        this.inReceiveConvertPieceCount = this.inReceiveConvertPieceCount.add(inventoryGoods.getInReceiveConvertPieceCount());
        this.inReceiveAmount = this.inReceiveAmount.add(inventoryGoods.getInReceiveAmount());
        this.inReceiveAmountExcludingTax = this.inReceiveAmountExcludingTax.add(inventoryGoods.getInReceiveAmountExcludingTax());

        this.inSpecificationModificationCount = this.inSpecificationModificationCount.add(inventoryGoods.getInSpecificationModificationCount());
        this.inSpecificationModificationConvertPieceCount = this.inSpecificationModificationConvertPieceCount.add(inventoryGoods.getInSpecificationModificationConvertPieceCount());
        this.inSpecificationModificationCost = this.inSpecificationModificationCost.add(inventoryGoods.getInSpecificationModificationCost());
        this.inSpecificationModificationCostExcludeTax = this.inSpecificationModificationCostExcludeTax.add(inventoryGoods.getInSpecificationModificationCostExcludeTax());

        this.inTotalCount = this.inTotalCount.add(inventoryGoods.getInTotalCount());
        this.inTotalConvertPieceCount = this.inTotalConvertPieceCount.add(inventoryGoods.getInTotalConvertPieceCount());
        this.inTotalCost = this.inTotalCost.add(inventoryGoods.getInTotalCost());
        this.inTotalCostExcludeTax = this.inTotalCostExcludeTax.add(inventoryGoods.getInTotalCostExcludeTax());

        this.dispenseCount = this.dispenseCount.add(inventoryGoods.getDispenseCount());
        this.dispenseConvertPieceCount = this.dispenseConvertPieceCount.add(inventoryGoods.getDispenseConvertPieceCount());
        this.dispenseCost = this.dispenseCost.add(inventoryGoods.getDispenseCost());
        this.dispenseCostExcludeTax = this.dispenseCostExcludeTax.add(inventoryGoods.getDispenseCostExcludeTax());
        this.dispensePrice = this.dispensePrice.add(inventoryGoods.getDispensePrice());
        this.dispensePriceExcludeTax = this.dispensePriceExcludeTax.add(inventoryGoods.getDispensePriceExcludeTax());

        this.outPatientDispenseCount = this.outPatientDispenseCount.add(inventoryGoods.getOutPatientDispenseCount());
        this.outPatientDispenseConvertPieceCount = this.outPatientDispenseConvertPieceCount.add(inventoryGoods.getOutPatientDispenseConvertPieceCount());
        this.outPatientDispenseCost = this.outPatientDispenseCost.add(inventoryGoods.getOutPatientDispenseCost());
        this.outPatientDispenseCostExcludeTax = this.outPatientDispenseCostExcludeTax.add(inventoryGoods.getOutPatientDispenseCostExcludeTax());
        this.outPatientDispensePrice = this.outPatientDispensePrice.add(inventoryGoods.getOutPatientDispensePrice());
        this.outPatientDispensePriceExcludeTax = this.outPatientDispensePriceExcludeTax.add(inventoryGoods.getOutPatientDispensePriceExcludeTax());

        this.hospitalPharmacyDispenseCount = this.hospitalPharmacyDispenseCount.add(inventoryGoods.getHospitalPharmacyDispenseCount());
        this.hospitalPharmacyDispenseConvertPieceCount = this.hospitalPharmacyDispenseConvertPieceCount.add(inventoryGoods.getHospitalPharmacyDispenseConvertPieceCount());
        this.hospitalPharmacyDispenseCost = this.hospitalPharmacyDispenseCost.add(inventoryGoods.getHospitalPharmacyDispenseCost());
        this.hospitalPharmacyDispenseCostExcludeTax = this.hospitalPharmacyDispenseCostExcludeTax.add(inventoryGoods.getHospitalPharmacyDispenseCostExcludeTax());
        this.hospitalPharmacyDispensePrice = this.hospitalPharmacyDispensePrice.add(inventoryGoods.getHospitalPharmacyDispensePrice());
        this.hospitalPharmacyDispensePriceExcludeTax = this.hospitalPharmacyDispensePriceExcludeTax.add(inventoryGoods.getHospitalPharmacyDispensePriceExcludeTax());

        this.hospitalAutomaticDispenseCount = this.hospitalAutomaticDispenseCount.add(inventoryGoods.getHospitalAutomaticDispenseCount());
        this.hospitalAutomaticDispenseConvertPieceCount = this.hospitalAutomaticDispenseConvertPieceCount.add(inventoryGoods.getHospitalAutomaticDispenseConvertPieceCount());
        this.hospitalAutomaticDispenseCost = this.hospitalAutomaticDispenseCost.add(inventoryGoods.getHospitalAutomaticDispenseCost());
        this.hospitalAutomaticDispenseCostExcludeTax = this.hospitalAutomaticDispenseCostExcludeTax.add(inventoryGoods.getHospitalAutomaticDispenseCostExcludeTax());
        this.hospitalAutomaticDispensePrice = this.hospitalAutomaticDispensePrice.add(inventoryGoods.getHospitalAutomaticDispensePrice());
        this.hospitalAutomaticDispensePriceExcludeTax = this.hospitalAutomaticDispensePriceExcludeTax.add(inventoryGoods.getHospitalAutomaticDispensePriceExcludeTax());

        this.hospitalNoSettleDispenseCount = this.hospitalNoSettleDispenseCount.add(inventoryGoods.getHospitalNoSettleDispenseCount());
        this.hospitalNoSettleDispenseConvertPieceCount = this.hospitalNoSettleDispenseConvertPieceCount.add(inventoryGoods.getHospitalNoSettleDispenseConvertPieceCount());
        this.hospitalNoSettleDispenseCost = this.hospitalNoSettleDispenseCost.add(inventoryGoods.getHospitalNoSettleDispenseCost());
        this.hospitalNoSettleDispenseCostExcludeTax = this.hospitalNoSettleDispenseCostExcludeTax.add(inventoryGoods.getHospitalNoSettleDispenseCostExcludeTax());
        this.hospitalNoSettleDispensePrice = this.hospitalNoSettleDispensePrice.add(inventoryGoods.getHospitalNoSettleDispensePrice());
        this.hospitalNoSettleDispensePriceExcludeTax = this.hospitalNoSettleDispensePriceExcludeTax.add(inventoryGoods.getHospitalNoSettleDispensePriceExcludeTax());

        this.collectOutCount = this.collectOutCount.add(inventoryGoods.getCollectOutCount());
        this.collectOutConvertPieceCount = this.collectOutConvertPieceCount.add(inventoryGoods.getCollectOutConvertPieceCount());
        this.collectOutCost = this.collectOutCost.add(inventoryGoods.getCollectOutCost());
        this.collectOutCostExcludeTax = this.collectOutCostExcludeTax.add(inventoryGoods.getCollectOutCostExcludeTax());

        this.allotOutCount = this.allotOutCount.add(inventoryGoods.getAllotOutCount());
        this.allotOutConvertPieceCount = this.allotOutConvertPieceCount.add(inventoryGoods.getAllotOutConvertPieceCount());
        this.allotOutCost = this.allotOutCost.add(inventoryGoods.getAllotOutCost());
        this.allotOutCostExcludeTax = this.allotOutCostExcludeTax.add(inventoryGoods.getAllotOutCostExcludeTax());

        this.damagedOutCount = this.damagedOutCount.add(inventoryGoods.getDamagedOutCount());
        this.damagedOutConvertPieceCount = this.damagedOutConvertPieceCount.add(inventoryGoods.getDamagedOutConvertPieceCount());
        this.damagedOutCost = this.damagedOutCost.add(inventoryGoods.getDamagedOutCost());
        this.damagedOutCostExcludeTax = this.damagedOutCostExcludeTax.add(inventoryGoods.getDamagedOutCostExcludeTax());

        this.outDepartmentConsumptionCount = this.outDepartmentConsumptionCount.add(inventoryGoods.getOutDepartmentConsumptionCount());
        this.outDepartmentConsumptionConvertPieceCount = this.outDepartmentConsumptionConvertPieceCount.add(inventoryGoods.getOutDepartmentConsumptionConvertPieceCount());
        this.outDepartmentConsumptionAmount = this.outDepartmentConsumptionAmount.add(inventoryGoods.getOutDepartmentConsumptionAmount());
        this.outDepartmentConsumptionAmountExcludingTax = this.outDepartmentConsumptionAmountExcludingTax.add(inventoryGoods.getOutDepartmentConsumptionAmountExcludingTax());

        this.outOtherCount = this.outOtherCount.add(inventoryGoods.getOutOtherCount());
        this.outOtherConvertPieceCount = this.outOtherConvertPieceCount.add(inventoryGoods.getOutOtherConvertPieceCount());
        this.outOtherAmount = this.outOtherAmount.add(inventoryGoods.getOutOtherAmount());
        this.outOtherAmountExcludingTax = this.outOtherAmountExcludingTax.add(inventoryGoods.getOutOtherAmountExcludingTax());

        this.checkOutCount = this.checkOutCount.add(inventoryGoods.getCheckOutCount());
        this.checkOutConvertPieceCount = this.checkOutConvertPieceCount.add(inventoryGoods.getCheckOutConvertPieceCount());
        this.checkOutCost = this.checkOutCost.add(inventoryGoods.getCheckOutCost());
        this.checkOutCostExcludeTax = this.checkOutCostExcludeTax.add(inventoryGoods.getCheckOutCostExcludeTax());
        this.checkOutPrice = this.checkOutPrice.add(inventoryGoods.getCheckOutPrice());
        this.checkOutPriceExcludeTax = this.checkOutPriceExcludeTax.add(inventoryGoods.getCheckOutPriceExcludeTax());

        this.productionOutCount = this.productionOutCount.add(inventoryGoods.getProductionOutCount());
        this.productionOutConvertPieceCount = this.productionOutConvertPieceCount.add(inventoryGoods.getProductionOutConvertPieceCount());
        this.productionOutCost = this.productionOutCost.add(inventoryGoods.getProductionOutCost());
        this.productionOutCostExcludeTax = this.productionOutCostExcludeTax.add(inventoryGoods.getProductionOutCostExcludeTax());

        this.deliveryOutCount = this.deliveryOutCount.add(inventoryGoods.getDeliveryOutCount());
        this.deliveryOutConvertPieceCount = this.deliveryOutConvertPieceCount.add(inventoryGoods.getDeliveryOutConvertPieceCount());
        this.deliveryOutCost = this.deliveryOutCost.add(inventoryGoods.getDeliveryOutCost());
        this.deliveryOutCostExcludeTax = this.deliveryOutCostExcludeTax.add(inventoryGoods.getDeliveryOutCostExcludeTax());

        this.outTotalCount = this.outTotalCount.add(inventoryGoods.getOutTotalCount());
        this.outTotalConvertPieceCount = this.outTotalConvertPieceCount.add(inventoryGoods.getOutTotalConvertPieceCount());
        this.outTotalCost = this.outTotalCost.add(inventoryGoods.getOutTotalCost());
        this.outTotalCostExcludeTax = this.outTotalCostExcludeTax.add(inventoryGoods.getOutTotalCostExcludeTax());

        this.endCount = this.endCount.add(inventoryGoods.getEndCount());
        this.endConvertPieceCount = this.endConvertPieceCount.add(inventoryGoods.getEndConvertPieceCount());
        this.endCost = this.endCost.add(inventoryGoods.getEndCost());
        this.endCostExcludeTax = this.endCostExcludeTax.add(inventoryGoods.getEndCostExcludeTax());

        this.afterTotalCostModifyExcludeTax = this.afterTotalCostModifyExcludeTax.add(inventoryGoods.getAfterTotalCostModifyExcludeTax());
        this.goodsAfterTotalCostModifyExcludeTax = this.goodsAfterTotalCostModifyExcludeTax.add(inventoryGoods.getGoodsAfterTotalCostModifyExcludeTax());
        this.goodsPharmacyAfterTotalCostModifyExcludeTax = this.goodsPharmacyAfterTotalCostModifyExcludeTax.add(inventoryGoods.getGoodsPharmacyAfterTotalCostModifyExcludeTax());
    }

    public String getGoodsShortId() {
        return this.goodsShortId;
    }

    public String getGoodsId() {
        return this.goodsId;
    }

    public String getGoodsText() {
        return this.goodsText;
    }

    public String getFeeType1() {
        return this.feeType1;
    }

    public String getFeeType1Text() {
        return this.feeType1Text;
    }

    public Integer getFeeType2() {
        return this.feeType2;
    }

    public String getFeeType2Text() {
        return this.feeType2Text;
    }

    public String getSpecification() {
        return this.specification;
    }

    public String getManufacturer() {
        return this.manufacturer;
    }

    public String getPieceUnit() {
        return this.pieceUnit;
    }

    public String getMinPieceUnit() {
        return this.minPieceUnit;
    }

    public String getPackageUnit() {
        return this.packageUnit;
    }

    public BigDecimal getPiecePrice() {
        return this.piecePrice;
    }

    public String getPiecePriceText() {
        return this.piecePriceText;
    }

    public BigDecimal getPackagePrice() {
        return this.packagePrice;
    }

    public String getNmpn() {
        return this.nmpn;
    }

    public BigDecimal getLastPackageCostPrice() {
        return this.lastPackageCostPrice;
    }

    public String getLastPackageCostPriceText() {
        return this.lastPackageCostPriceText;
    }

    public String getLastStockInOrderSupplier() {
        return this.lastStockInOrderSupplier;
    }

    public String getSheBaoCodeNationalCode() {
        return this.sheBaoCodeNationalCode;
    }

    public Long getFeeTypeId() {
        return this.feeTypeId;
    }

    public String getFeeTypeName() {
        return this.feeTypeName;
    }

    public Long getProfitCategoryTypeId() {
        return this.profitCategoryTypeId;
    }

    public String getProfitCategoryTypeName() {
        return this.profitCategoryTypeName;
    }

    public String getBeginCountText() {
        return this.beginCountText;
    }

    public String getBeginConvertPieceCountText() {
        return this.beginConvertPieceCountText;
    }

    public String getBeginPackageCountText() {
        return this.beginPackageCountText;
    }

    public String getBeginPieceCountText() {
        return this.beginPieceCountText;
    }

    public String getBeginCostExcludeTaxText() {
        return this.beginCostExcludeTaxText;
    }

    public String getBeginCostText() {
        return this.beginCostText;
    }

    public String getBeginPriceExcludeTaxText() {
        return this.beginPriceExcludeTaxText;
    }

    public String getBeginPriceText() {
        return this.beginPriceText;
    }

    public String getInInitCountText() {
        return this.inInitCountText;
    }

    public String getInInitConvertPieceCountText() {
        return this.inInitConvertPieceCountText;
    }

    public String getInInitPackageCountText() {
        return this.inInitPackageCountText;
    }

    public String getInInitPieceCountText() {
        return this.inInitPieceCountText;
    }

    public String getInInitCostExcludeTaxText() {
        return this.inInitCostExcludeTaxText;
    }

    public String getInInitCostText() {
        return this.inInitCostText;
    }

    public String getInInitPriceExcludeTaxText() {
        return this.inInitPriceExcludeTaxText;
    }

    public String getInInitPriceText() {
        return this.inInitPriceText;
    }

    public String getEndCountText() {
        return this.endCountText;
    }

    public String getEndConvertPieceCountText() {
        return this.endConvertPieceCountText;
    }

    public String getEndPackageCountText() {
        return this.endPackageCountText;
    }

    public String getEndPieceCountText() {
        return this.endPieceCountText;
    }

    public String getEndCostExcludeTaxText() {
        return this.endCostExcludeTaxText;
    }

    public String getEndCostText() {
        return this.endCostText;
    }

    public String getEndPriceExcludeTaxText() {
        return this.endPriceExcludeTaxText;
    }

    public String getEndPriceText() {
        return this.endPriceText;
    }

    public String getPurchaseInCountText() {
        return this.purchaseInCountText;
    }

    public String getPurchaseInConvertPieceCountText() {
        return this.purchaseInConvertPieceCountText;
    }

    public String getPurchaseInPackageCountText() {
        return this.purchaseInPackageCountText;
    }

    public String getPurchaseInPieceCountText() {
        return this.purchaseInPieceCountText;
    }

    public String getPurchaseInCostExcludeTaxText() {
        return this.purchaseInCostExcludeTaxText;
    }

    public String getPurchaseInCostText() {
        return this.purchaseInCostText;
    }

    public String getPurchaseInPriceExcludeTaxText() {
        return this.purchaseInPriceExcludeTaxText;
    }

    public String getPurchaseInPriceText() {
        return this.purchaseInPriceText;
    }

    public String getAllotInCountText() {
        return this.allotInCountText;
    }

    public String getAllotInConvertPieceCountText() {
        return this.allotInConvertPieceCountText;
    }

    public String getAllotInPackageCountText() {
        return this.allotInPackageCountText;
    }

    public String getAllotInPieceCountText() {
        return this.allotInPieceCountText;
    }

    public String getAllotInCostExcludeTaxText() {
        return this.allotInCostExcludeTaxText;
    }

    public String getAllotInCostText() {
        return this.allotInCostText;
    }

    public String getAllotInPriceExcludeTaxText() {
        return this.allotInPriceExcludeTaxText;
    }

    public String getAllotInPriceText() {
        return this.allotInPriceText;
    }

    public String getAllotInInsideCountText() {
        return this.allotInInsideCountText;
    }

    public String getAllotInInsideConvertPieceCountText() {
        return this.allotInInsideConvertPieceCountText;
    }

    public String getAllotInInsidePackageCountText() {
        return this.allotInInsidePackageCountText;
    }

    public String getAllotInInsidePieceCountText() {
        return this.allotInInsidePieceCountText;
    }

    public String getAllotInInsideCostExcludeTaxText() {
        return this.allotInInsideCostExcludeTaxText;
    }

    public String getAllotInInsideCostText() {
        return this.allotInInsideCostText;
    }

    public String getAllotInInsidePriceExcludeTaxText() {
        return this.allotInInsidePriceExcludeTaxText;
    }

    public String getAllotInInsidePriceText() {
        return this.allotInInsidePriceText;
    }

    public String getCheckInCountText() {
        return this.checkInCountText;
    }

    public String getCheckInConvertPieceCountText() {
        return this.checkInConvertPieceCountText;
    }

    public String getCheckInPackageCountText() {
        return this.checkInPackageCountText;
    }

    public String getCheckInPieceCountText() {
        return this.checkInPieceCountText;
    }

    public String getCheckInPriceExcludeTaxText() {
        return this.checkInPriceExcludeTaxText;
    }

    public String getCheckInPriceText() {
        return this.checkInPriceText;
    }

    public String getCheckInCostExcludeTaxText() {
        return this.checkInCostExcludeTaxText;
    }

    public String getCheckInCostText() {
        return this.checkInCostText;
    }

    public String getInReceiveCountText() {
        return this.inReceiveCountText;
    }

    public String getInReceiveConvertPieceCountText() {
        return this.inReceiveConvertPieceCountText;
    }

    public String getInReceivePackageCountText() {
        return this.inReceivePackageCountText;
    }

    public String getInReceivePieceCountText() {
        return this.inReceivePieceCountText;
    }

    public String getInReceiveCostAmountText() {
        return this.inReceiveCostAmountText;
    }

    public String getInReceiveCostAmountExcludingTaxText() {
        return this.inReceiveCostAmountExcludingTaxText;
    }

    public String getInReceiveAmountText() {
        return this.inReceiveAmountText;
    }

    public String getInReceiveAmountExcludingTaxText() {
        return this.inReceiveAmountExcludingTaxText;
    }

    public String getInSpecificationModificationCountText() {
        return this.inSpecificationModificationCountText;
    }

    public String getInSpecificationModificationConvertPieceCountText() {
        return this.inSpecificationModificationConvertPieceCountText;
    }

    public String getInSpecificationModificationCostExcludeTaxText() {
        return this.inSpecificationModificationCostExcludeTaxText;
    }

    public String getInSpecificationModificationCostText() {
        return this.inSpecificationModificationCostText;
    }

    public String getInSpecificationModificationPriceExcludeTaxText() {
        return this.inSpecificationModificationPriceExcludeTaxText;
    }

    public String getInSpecificationModificationPriceText() {
        return this.inSpecificationModificationPriceText;
    }

    public String getInTotalCountText() {
        return this.inTotalCountText;
    }

    public String getInTotalConvertPieceCountText() {
        return this.inTotalConvertPieceCountText;
    }

    public String getInTotalCostExcludeTaxText() {
        return this.inTotalCostExcludeTaxText;
    }

    public String getInTotalCostText() {
        return this.inTotalCostText;
    }

    public String getDispenseCountText() {
        return this.dispenseCountText;
    }

    public String getDispenseConvertPieceCountText() {
        return this.dispenseConvertPieceCountText;
    }

    public String getDispensePackageCountText() {
        return this.dispensePackageCountText;
    }

    public String getDispensePieceCountText() {
        return this.dispensePieceCountText;
    }

    public String getDispensePriceExcludeTaxText() {
        return this.dispensePriceExcludeTaxText;
    }

    public String getDispensePriceText() {
        return this.dispensePriceText;
    }

    public String getDispenseCostExcludeTaxText() {
        return this.dispenseCostExcludeTaxText;
    }

    public String getDispenseCostText() {
        return this.dispenseCostText;
    }

    public String getOutPatientDispenseCountText() {
        return this.outPatientDispenseCountText;
    }

    public String getOutPatientDispenseConvertPieceCountText() {
        return this.outPatientDispenseConvertPieceCountText;
    }

    public String getOutPatientDispensePackageCountText() {
        return this.outPatientDispensePackageCountText;
    }

    public String getOutPatientDispensePieceCountText() {
        return this.outPatientDispensePieceCountText;
    }

    public String getOutPatientDispensePriceExcludeTaxText() {
        return this.outPatientDispensePriceExcludeTaxText;
    }

    public String getOutPatientDispensePriceText() {
        return this.outPatientDispensePriceText;
    }

    public String getOutPatientDispenseCostExcludeTaxText() {
        return this.outPatientDispenseCostExcludeTaxText;
    }

    public String getOutPatientDispenseCostText() {
        return this.outPatientDispenseCostText;
    }

    public String getHospitalPharmacyDispenseCountText() {
        return this.hospitalPharmacyDispenseCountText;
    }

    public String getHospitalPharmacyDispenseConvertPieceCountText() {
        return this.hospitalPharmacyDispenseConvertPieceCountText;
    }

    public String getHospitalPharmacyDispensePackageCountText() {
        return this.hospitalPharmacyDispensePackageCountText;
    }

    public String getHospitalPharmacyDispensePieceCountText() {
        return this.hospitalPharmacyDispensePieceCountText;
    }

    public String getHospitalPharmacyDispensePriceExcludeTaxText() {
        return this.hospitalPharmacyDispensePriceExcludeTaxText;
    }

    public String getHospitalPharmacyDispensePriceText() {
        return this.hospitalPharmacyDispensePriceText;
    }

    public String getHospitalPharmacyDispenseCostExcludeTaxText() {
        return this.hospitalPharmacyDispenseCostExcludeTaxText;
    }

    public String getHospitalPharmacyDispenseCostText() {
        return this.hospitalPharmacyDispenseCostText;
    }

    public String getHospitalAutomaticDispenseCountText() {
        return this.hospitalAutomaticDispenseCountText;
    }

    public String getHospitalAutomaticDispenseConvertPieceCountText() {
        return this.hospitalAutomaticDispenseConvertPieceCountText;
    }

    public String getHospitalAutomaticDispensePackageCountText() {
        return this.hospitalAutomaticDispensePackageCountText;
    }

    public String getHospitalAutomaticDispensePieceCountText() {
        return this.hospitalAutomaticDispensePieceCountText;
    }

    public String getHospitalAutomaticDispensePriceExcludeTaxText() {
        return this.hospitalAutomaticDispensePriceExcludeTaxText;
    }

    public String getHospitalAutomaticDispensePriceText() {
        return this.hospitalAutomaticDispensePriceText;
    }

    public String getHospitalAutomaticDispenseCostExcludeTaxText() {
        return this.hospitalAutomaticDispenseCostExcludeTaxText;
    }

    public String getHospitalAutomaticDispenseCostText() {
        return this.hospitalAutomaticDispenseCostText;
    }

    public String getHospitalNoSettleDispenseCountText() {
        return this.hospitalNoSettleDispenseCountText;
    }

    public String getHospitalNoSettleDispenseConvertPieceCountText() {
        return this.hospitalNoSettleDispenseConvertPieceCountText;
    }

    public String getHospitalNoSettleDispensePackageCountText() {
        return this.hospitalNoSettleDispensePackageCountText;
    }

    public String getHospitalNoSettleDispensePieceCountText() {
        return this.hospitalNoSettleDispensePieceCountText;
    }

    public String getHospitalNoSettleDispensePriceExcludeTaxText() {
        return this.hospitalNoSettleDispensePriceExcludeTaxText;
    }

    public String getHospitalNoSettleDispensePriceText() {
        return this.hospitalNoSettleDispensePriceText;
    }

    public String getHospitalNoSettleDispenseCostExcludeTaxText() {
        return this.hospitalNoSettleDispenseCostExcludeTaxText;
    }

    public String getHospitalNoSettleDispenseCostText() {
        return this.hospitalNoSettleDispenseCostText;
    }

    public String getCollectOutCountText() {
        return this.collectOutCountText;
    }

    public String getCollectOutConvertPieceCountText() {
        return this.collectOutConvertPieceCountText;
    }

    public String getCollectOutPackageCountText() {
        return this.collectOutPackageCountText;
    }

    public String getCollectOutPieceCountText() {
        return this.collectOutPieceCountText;
    }

    public String getCollectOutCostExcludeTaxText() {
        return this.collectOutCostExcludeTaxText;
    }

    public String getCollectOutCostText() {
        return this.collectOutCostText;
    }

    public String getCollectOutPriceExcludeTaxText() {
        return this.collectOutPriceExcludeTaxText;
    }

    public String getCollectOutPriceText() {
        return this.collectOutPriceText;
    }

    public String getAllotOutCountText() {
        return this.allotOutCountText;
    }

    public String getAllotOutConvertPieceCountText() {
        return this.allotOutConvertPieceCountText;
    }

    public String getAllotOutPackageCountText() {
        return this.allotOutPackageCountText;
    }

    public String getAllotOutPieceCountText() {
        return this.allotOutPieceCountText;
    }

    public String getAllotOutCostExcludeTaxText() {
        return this.allotOutCostExcludeTaxText;
    }

    public String getAllotOutCostText() {
        return this.allotOutCostText;
    }

    public String getAllotOutPriceExcludeTaxText() {
        return this.allotOutPriceExcludeTaxText;
    }

    public String getAllotOutPriceText() {
        return this.allotOutPriceText;
    }

    public String getAllotOutInsideCountText() {
        return this.allotOutInsideCountText;
    }

    public String getAllotOutInsideConvertPieceCountText() {
        return this.allotOutInsideConvertPieceCountText;
    }

    public String getAllotOutInsidePackageCountText() {
        return this.allotOutInsidePackageCountText;
    }

    public String getAllotOutInsidePieceCountText() {
        return this.allotOutInsidePieceCountText;
    }

    public String getAllotOutInsideCostExcludeTaxText() {
        return this.allotOutInsideCostExcludeTaxText;
    }

    public String getAllotOutInsideCostText() {
        return this.allotOutInsideCostText;
    }

    public String getAllotOutInsidePriceExcludeTaxText() {
        return this.allotOutInsidePriceExcludeTaxText;
    }

    public String getAllotOutInsidePriceText() {
        return this.allotOutInsidePriceText;
    }

    public String getDamagedOutCountText() {
        return this.damagedOutCountText;
    }

    public String getDamagedOutConvertPieceCountText() {
        return this.damagedOutConvertPieceCountText;
    }

    public String getDamagedOutPackageCountText() {
        return this.damagedOutPackageCountText;
    }

    public String getDamagedOutPieceCountText() {
        return this.damagedOutPieceCountText;
    }

    public String getDamagedOutCostExcludeTaxText() {
        return this.damagedOutCostExcludeTaxText;
    }

    public String getDamagedOutCostText() {
        return this.damagedOutCostText;
    }

    public String getDamagedOutPriceExcludeTaxText() {
        return this.damagedOutPriceExcludeTaxText;
    }

    public String getDamagedOutPriceText() {
        return this.damagedOutPriceText;
    }

    public String getOutDepartmentConsumptionCountText() {
        return this.outDepartmentConsumptionCountText;
    }

    public String getOutDepartmentConsumptionConvertPieceCountText() {
        return this.outDepartmentConsumptionConvertPieceCountText;
    }

    public String getOutDepartmentConsumptionPackageCountText() {
        return this.outDepartmentConsumptionPackageCountText;
    }

    public String getOutDepartmentConsumptionPieceCountText() {
        return this.outDepartmentConsumptionPieceCountText;
    }

    public String getOutDepartmentConsumptionCostAmountText() {
        return this.outDepartmentConsumptionCostAmountText;
    }

    public String getOutDepartmentConsumptionCostAmountExcludingTaxText() {
        return this.outDepartmentConsumptionCostAmountExcludingTaxText;
    }

    public String getOutDepartmentConsumptionAmountText() {
        return this.outDepartmentConsumptionAmountText;
    }

    public String getOutDepartmentConsumptionAmountExcludingTaxText() {
        return this.outDepartmentConsumptionAmountExcludingTaxText;
    }

    public String getOutOtherCountText() {
        return this.outOtherCountText;
    }

    public String getOutOtherConvertPieceCountText() {
        return this.outOtherConvertPieceCountText;
    }

    public String getOutOtherPackageCountText() {
        return this.outOtherPackageCountText;
    }

    public String getOutOtherPieceCountText() {
        return this.outOtherPieceCountText;
    }

    public String getOutOtherCostAmountText() {
        return this.outOtherCostAmountText;
    }

    public String getOutOtherCostAmountExcludingTaxText() {
        return this.outOtherCostAmountExcludingTaxText;
    }

    public String getOutOtherAmountText() {
        return this.outOtherAmountText;
    }

    public String getOutOtherAmountExcludingTaxText() {
        return this.outOtherAmountExcludingTaxText;
    }

    public String getCheckOutCountText() {
        return this.checkOutCountText;
    }

    public String getCheckOutConvertPieceCountText() {
        return this.checkOutConvertPieceCountText;
    }

    public String getCheckOutPackageCountText() {
        return this.checkOutPackageCountText;
    }

    public String getCheckOutPieceCountText() {
        return this.checkOutPieceCountText;
    }

    public String getCheckOutPriceExcludeTaxText() {
        return this.checkOutPriceExcludeTaxText;
    }

    public String getCheckOutPriceText() {
        return this.checkOutPriceText;
    }

    public String getCheckOutCostExcludeTaxText() {
        return this.checkOutCostExcludeTaxText;
    }

    public String getCheckOutCostText() {
        return this.checkOutCostText;
    }

    public String getProductionOutCountText() {
        return this.productionOutCountText;
    }

    public String getProductionOutConvertPieceCountText() {
        return this.productionOutConvertPieceCountText;
    }

    public String getProductionOutPackageCountText() {
        return this.productionOutPackageCountText;
    }

    public String getProductionOutPieceCountText() {
        return this.productionOutPieceCountText;
    }

    public String getProductionOutPriceExcludeTaxText() {
        return this.productionOutPriceExcludeTaxText;
    }

    public String getProductionOutPriceText() {
        return this.productionOutPriceText;
    }

    public String getProductionOutCostExcludeTaxText() {
        return this.productionOutCostExcludeTaxText;
    }

    public String getProductionOutCostText() {
        return this.productionOutCostText;
    }

    public String getOutTotalCountText() {
        return this.outTotalCountText;
    }

    public String getOutTotalConvertPieceCountText() {
        return this.outTotalConvertPieceCountText;
    }

    public String getOutTotalCostExcludeTaxText() {
        return this.outTotalCostExcludeTaxText;
    }

    public String getOutTotalCostText() {
        return this.outTotalCostText;
    }

    public BigDecimal getInTaxRat() {
        return this.inTaxRat;
    }

    public BigDecimal getOutTaxRat() {
        return this.outTaxRat;
    }

    public String getInTaxRatText() {
        return this.inTaxRatText;
    }

    public String getOutTaxRatText() {
        return this.outTaxRatText;
    }

    public String getBeforeInTaxRatText() {
        return this.beforeInTaxRatText;
    }

    public String getBeforeOutTaxRatText() {
        return this.beforeOutTaxRatText;
    }

    public String getAfterTotalCostModifyExcludeTaxText() {
        return this.afterTotalCostModifyExcludeTaxText;
    }

    public String getGoodsAfterTotalCostModifyExcludeTaxText() {
        return this.goodsAfterTotalCostModifyExcludeTaxText;
    }

    public String getGoodsPharmacyAfterTotalCostModifyExcludeTaxText() {
        return this.goodsPharmacyAfterTotalCostModifyExcludeTaxText;
    }

    public String getEffectedTime() {
        return this.effectedTime;
    }

    public String getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    public String getLastModified() {
        return this.lastModified;
    }

    public String getDosageFormTypeName() {
        return this.dosageFormTypeName;
    }

    public String getNationalStandardImplementationName() {
        return this.nationalStandardImplementationName;
    }


    public void setGoodsShortId(String goodsShortId) {
        this.goodsShortId = goodsShortId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsText(String goodsText) {
        this.goodsText = goodsText;
    }

    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public void setFeeType1Text(String feeType1Text) {
        this.feeType1Text = feeType1Text;
    }

    public void setFeeType2(Integer feeType2) {
        this.feeType2 = feeType2;
    }

    public void setFeeType2Text(String feeType2Text) {
        this.feeType2Text = feeType2Text;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public void setPieceUnit(String pieceUnit) {
        this.pieceUnit = pieceUnit;
    }

    public void setMinPieceUnit(String minPieceUnit) {
        this.minPieceUnit = minPieceUnit;
    }

    public void setPackageUnit(String packageUnit) {
        this.packageUnit = packageUnit;
    }

    public void setPiecePrice(BigDecimal piecePrice) {
        this.piecePrice = piecePrice;
    }

    public void setPiecePriceText(String piecePriceText) {
        this.piecePriceText = piecePriceText;
    }

    public void setPackagePrice(BigDecimal packagePrice) {
        this.packagePrice = packagePrice;
    }

    public void setNmpn(String nmpn) {
        this.nmpn = nmpn;
    }

    public void setLastPackageCostPrice(BigDecimal lastPackageCostPrice) {
        this.lastPackageCostPrice = lastPackageCostPrice;
    }

    public void setLastPackageCostPriceText(String lastPackageCostPriceText) {
        this.lastPackageCostPriceText = lastPackageCostPriceText;
    }

    public void setLastStockInOrderSupplier(String lastStockInOrderSupplier) {
        this.lastStockInOrderSupplier = lastStockInOrderSupplier;
    }

    public void setSheBaoCodeNationalCode(String sheBaoCodeNationalCode) {
        this.sheBaoCodeNationalCode = sheBaoCodeNationalCode;
    }

    public void setFeeTypeId(Long feeTypeId) {
        this.feeTypeId = feeTypeId;
    }

    public void setFeeTypeName(String feeTypeName) {
        this.feeTypeName = feeTypeName;
    }

    public void setProfitCategoryTypeId(Long profitCategoryTypeId) {
        this.profitCategoryTypeId = profitCategoryTypeId;
    }

    public void setProfitCategoryTypeName(String profitCategoryTypeName) {
        this.profitCategoryTypeName = profitCategoryTypeName;
    }

    public void setBeginCountText(String beginCountText) {
        this.beginCountText = beginCountText;
    }

    public void setBeginConvertPieceCountText(String beginConvertPieceCountText) {
        this.beginConvertPieceCountText = beginConvertPieceCountText;
    }

    public void setBeginPackageCountText(String beginPackageCountText) {
        this.beginPackageCountText = beginPackageCountText;
    }

    public void setBeginPieceCountText(String beginPieceCountText) {
        this.beginPieceCountText = beginPieceCountText;
    }

    public void setBeginCostExcludeTaxText(String beginCostExcludeTaxText) {
        this.beginCostExcludeTaxText = beginCostExcludeTaxText;
    }

    public void setBeginCostText(String beginCostText) {
        this.beginCostText = beginCostText;
    }

    public void setBeginPriceExcludeTaxText(String beginPriceExcludeTaxText) {
        this.beginPriceExcludeTaxText = beginPriceExcludeTaxText;
    }

    public void setBeginPriceText(String beginPriceText) {
        this.beginPriceText = beginPriceText;
    }

    public void setInInitCountText(String inInitCountText) {
        this.inInitCountText = inInitCountText;
    }

    public void setInInitConvertPieceCountText(String inInitConvertPieceCountText) {
        this.inInitConvertPieceCountText = inInitConvertPieceCountText;
    }

    public void setInInitPackageCountText(String inInitPackageCountText) {
        this.inInitPackageCountText = inInitPackageCountText;
    }

    public void setInInitPieceCountText(String inInitPieceCountText) {
        this.inInitPieceCountText = inInitPieceCountText;
    }

    public void setInInitCostExcludeTaxText(String inInitCostExcludeTaxText) {
        this.inInitCostExcludeTaxText = inInitCostExcludeTaxText;
    }

    public void setInInitCostText(String inInitCostText) {
        this.inInitCostText = inInitCostText;
    }

    public void setInInitPriceExcludeTaxText(String inInitPriceExcludeTaxText) {
        this.inInitPriceExcludeTaxText = inInitPriceExcludeTaxText;
    }

    public void setInInitPriceText(String inInitPriceText) {
        this.inInitPriceText = inInitPriceText;
    }

    public void setEndCountText(String endCountText) {
        this.endCountText = endCountText;
    }

    public void setEndConvertPieceCountText(String endConvertPieceCountText) {
        this.endConvertPieceCountText = endConvertPieceCountText;
    }

    public void setEndPackageCountText(String endPackageCountText) {
        this.endPackageCountText = endPackageCountText;
    }

    public void setEndPieceCountText(String endPieceCountText) {
        this.endPieceCountText = endPieceCountText;
    }

    public void setEndCostExcludeTaxText(String endCostExcludeTaxText) {
        this.endCostExcludeTaxText = endCostExcludeTaxText;
    }

    public void setEndCostText(String endCostText) {
        this.endCostText = endCostText;
    }

    public void setEndPriceExcludeTaxText(String endPriceExcludeTaxText) {
        this.endPriceExcludeTaxText = endPriceExcludeTaxText;
    }

    public void setEndPriceText(String endPriceText) {
        this.endPriceText = endPriceText;
    }

    public void setPurchaseInCountText(String purchaseInCountText) {
        this.purchaseInCountText = purchaseInCountText;
    }

    public void setPurchaseInConvertPieceCountText(String purchaseInConvertPieceCountText) {
        this.purchaseInConvertPieceCountText = purchaseInConvertPieceCountText;
    }

    public void setPurchaseInPackageCountText(String purchaseInPackageCountText) {
        this.purchaseInPackageCountText = purchaseInPackageCountText;
    }

    public void setPurchaseInPieceCountText(String purchaseInPieceCountText) {
        this.purchaseInPieceCountText = purchaseInPieceCountText;
    }

    public void setPurchaseInCostExcludeTaxText(String purchaseInCostExcludeTaxText) {
        this.purchaseInCostExcludeTaxText = purchaseInCostExcludeTaxText;
    }

    public void setPurchaseInCostText(String purchaseInCostText) {
        this.purchaseInCostText = purchaseInCostText;
    }

    public void setPurchaseInPriceExcludeTaxText(String purchaseInPriceExcludeTaxText) {
        this.purchaseInPriceExcludeTaxText = purchaseInPriceExcludeTaxText;
    }

    public void setPurchaseInPriceText(String purchaseInPriceText) {
        this.purchaseInPriceText = purchaseInPriceText;
    }

    public void setAllotInCountText(String allotInCountText) {
        this.allotInCountText = allotInCountText;
    }

    public void setAllotInConvertPieceCountText(String allotInConvertPieceCountText) {
        this.allotInConvertPieceCountText = allotInConvertPieceCountText;
    }

    public void setAllotInPackageCountText(String allotInPackageCountText) {
        this.allotInPackageCountText = allotInPackageCountText;
    }

    public void setAllotInPieceCountText(String allotInPieceCountText) {
        this.allotInPieceCountText = allotInPieceCountText;
    }

    public void setAllotInCostExcludeTaxText(String allotInCostExcludeTaxText) {
        this.allotInCostExcludeTaxText = allotInCostExcludeTaxText;
    }

    public void setAllotInCostText(String allotInCostText) {
        this.allotInCostText = allotInCostText;
    }

    public void setAllotInPriceExcludeTaxText(String allotInPriceExcludeTaxText) {
        this.allotInPriceExcludeTaxText = allotInPriceExcludeTaxText;
    }

    public void setAllotInPriceText(String allotInPriceText) {
        this.allotInPriceText = allotInPriceText;
    }

    public void setAllotInInsideCountText(String allotInInsideCountText) {
        this.allotInInsideCountText = allotInInsideCountText;
    }

    public void setAllotInInsideConvertPieceCountText(String allotInInsideConvertPieceCountText) {
        this.allotInInsideConvertPieceCountText = allotInInsideConvertPieceCountText;
    }

    public void setAllotInInsidePackageCountText(String allotInInsidePackageCountText) {
        this.allotInInsidePackageCountText = allotInInsidePackageCountText;
    }

    public void setAllotInInsidePieceCountText(String allotInInsidePieceCountText) {
        this.allotInInsidePieceCountText = allotInInsidePieceCountText;
    }

    public void setAllotInInsideCostExcludeTaxText(String allotInInsideCostExcludeTaxText) {
        this.allotInInsideCostExcludeTaxText = allotInInsideCostExcludeTaxText;
    }

    public void setAllotInInsideCostText(String allotInInsideCostText) {
        this.allotInInsideCostText = allotInInsideCostText;
    }

    public void setAllotInInsidePriceExcludeTaxText(String allotInInsidePriceExcludeTaxText) {
        this.allotInInsidePriceExcludeTaxText = allotInInsidePriceExcludeTaxText;
    }

    public void setAllotInInsidePriceText(String allotInInsidePriceText) {
        this.allotInInsidePriceText = allotInInsidePriceText;
    }

    public void setCheckInCountText(String checkInCountText) {
        this.checkInCountText = checkInCountText;
    }

    public void setCheckInConvertPieceCountText(String checkInConvertPieceCountText) {
        this.checkInConvertPieceCountText = checkInConvertPieceCountText;
    }

    public void setCheckInPackageCountText(String checkInPackageCountText) {
        this.checkInPackageCountText = checkInPackageCountText;
    }

    public void setCheckInPieceCountText(String checkInPieceCountText) {
        this.checkInPieceCountText = checkInPieceCountText;
    }

    public void setCheckInPriceExcludeTaxText(String checkInPriceExcludeTaxText) {
        this.checkInPriceExcludeTaxText = checkInPriceExcludeTaxText;
    }

    public void setCheckInPriceText(String checkInPriceText) {
        this.checkInPriceText = checkInPriceText;
    }

    public void setCheckInCostExcludeTaxText(String checkInCostExcludeTaxText) {
        this.checkInCostExcludeTaxText = checkInCostExcludeTaxText;
    }

    public void setCheckInCostText(String checkInCostText) {
        this.checkInCostText = checkInCostText;
    }

    public void setInReceiveCountText(String inReceiveCountText) {
        this.inReceiveCountText = inReceiveCountText;
    }

    public void setInReceiveConvertPieceCountText(String inReceiveConvertPieceCountText) {
        this.inReceiveConvertPieceCountText = inReceiveConvertPieceCountText;
    }

    public void setInReceivePackageCountText(String inReceivePackageCountText) {
        this.inReceivePackageCountText = inReceivePackageCountText;
    }

    public void setInReceivePieceCountText(String inReceivePieceCountText) {
        this.inReceivePieceCountText = inReceivePieceCountText;
    }

    public void setInReceiveCostAmountText(String inReceiveCostAmountText) {
        this.inReceiveCostAmountText = inReceiveCostAmountText;
    }

    public void setInReceiveCostAmountExcludingTaxText(String inReceiveCostAmountExcludingTaxText) {
        this.inReceiveCostAmountExcludingTaxText = inReceiveCostAmountExcludingTaxText;
    }

    public void setInReceiveAmountText(String inReceiveAmountText) {
        this.inReceiveAmountText = inReceiveAmountText;
    }

    public void setInReceiveAmountExcludingTaxText(String inReceiveAmountExcludingTaxText) {
        this.inReceiveAmountExcludingTaxText = inReceiveAmountExcludingTaxText;
    }

    public void setInSpecificationModificationCountText(String inSpecificationModificationCountText) {
        this.inSpecificationModificationCountText = inSpecificationModificationCountText;
    }

    public void setInSpecificationModificationConvertPieceCountText(String inSpecificationModificationConvertPieceCountText) {
        this.inSpecificationModificationConvertPieceCountText = inSpecificationModificationConvertPieceCountText;
    }

    public void setInSpecificationModificationCostExcludeTaxText(String inSpecificationModificationCostExcludeTaxText) {
        this.inSpecificationModificationCostExcludeTaxText = inSpecificationModificationCostExcludeTaxText;
    }

    public void setInSpecificationModificationCostText(String inSpecificationModificationCostText) {
        this.inSpecificationModificationCostText = inSpecificationModificationCostText;
    }

    public void setInSpecificationModificationPriceExcludeTaxText(String inSpecificationModificationPriceExcludeTaxText) {
        this.inSpecificationModificationPriceExcludeTaxText = inSpecificationModificationPriceExcludeTaxText;
    }

    public void setInSpecificationModificationPriceText(String inSpecificationModificationPriceText) {
        this.inSpecificationModificationPriceText = inSpecificationModificationPriceText;
    }

    public void setInTotalCountText(String inTotalCountText) {
        this.inTotalCountText = inTotalCountText;
    }

    public void setInTotalConvertPieceCountText(String inTotalConvertPieceCountText) {
        this.inTotalConvertPieceCountText = inTotalConvertPieceCountText;
    }

    public void setInTotalCostExcludeTaxText(String inTotalCostExcludeTaxText) {
        this.inTotalCostExcludeTaxText = inTotalCostExcludeTaxText;
    }

    public void setInTotalCostText(String inTotalCostText) {
        this.inTotalCostText = inTotalCostText;
    }

    public void setDispenseCountText(String dispenseCountText) {
        this.dispenseCountText = dispenseCountText;
    }

    public void setDispenseConvertPieceCountText(String dispenseConvertPieceCountText) {
        this.dispenseConvertPieceCountText = dispenseConvertPieceCountText;
    }

    public void setDispensePackageCountText(String dispensePackageCountText) {
        this.dispensePackageCountText = dispensePackageCountText;
    }

    public void setDispensePieceCountText(String dispensePieceCountText) {
        this.dispensePieceCountText = dispensePieceCountText;
    }

    public void setDispensePriceExcludeTaxText(String dispensePriceExcludeTaxText) {
        this.dispensePriceExcludeTaxText = dispensePriceExcludeTaxText;
    }

    public void setDispensePriceText(String dispensePriceText) {
        this.dispensePriceText = dispensePriceText;
    }

    public void setDispenseCostExcludeTaxText(String dispenseCostExcludeTaxText) {
        this.dispenseCostExcludeTaxText = dispenseCostExcludeTaxText;
    }

    public void setDispenseCostText(String dispenseCostText) {
        this.dispenseCostText = dispenseCostText;
    }

    public void setOutPatientDispenseCountText(String outPatientDispenseCountText) {
        this.outPatientDispenseCountText = outPatientDispenseCountText;
    }

    public void setOutPatientDispenseConvertPieceCountText(String outPatientDispenseConvertPieceCountText) {
        this.outPatientDispenseConvertPieceCountText = outPatientDispenseConvertPieceCountText;
    }

    public void setOutPatientDispensePackageCountText(String outPatientDispensePackageCountText) {
        this.outPatientDispensePackageCountText = outPatientDispensePackageCountText;
    }

    public void setOutPatientDispensePieceCountText(String outPatientDispensePieceCountText) {
        this.outPatientDispensePieceCountText = outPatientDispensePieceCountText;
    }

    public void setOutPatientDispensePriceExcludeTaxText(String outPatientDispensePriceExcludeTaxText) {
        this.outPatientDispensePriceExcludeTaxText = outPatientDispensePriceExcludeTaxText;
    }

    public void setOutPatientDispensePriceText(String outPatientDispensePriceText) {
        this.outPatientDispensePriceText = outPatientDispensePriceText;
    }

    public void setOutPatientDispenseCostExcludeTaxText(String outPatientDispenseCostExcludeTaxText) {
        this.outPatientDispenseCostExcludeTaxText = outPatientDispenseCostExcludeTaxText;
    }

    public void setOutPatientDispenseCostText(String outPatientDispenseCostText) {
        this.outPatientDispenseCostText = outPatientDispenseCostText;
    }

    public void setHospitalPharmacyDispenseCountText(String hospitalPharmacyDispenseCountText) {
        this.hospitalPharmacyDispenseCountText = hospitalPharmacyDispenseCountText;
    }

    public void setHospitalPharmacyDispenseConvertPieceCountText(String hospitalPharmacyDispenseConvertPieceCountText) {
        this.hospitalPharmacyDispenseConvertPieceCountText = hospitalPharmacyDispenseConvertPieceCountText;
    }

    public void setHospitalPharmacyDispensePackageCountText(String hospitalPharmacyDispensePackageCountText) {
        this.hospitalPharmacyDispensePackageCountText = hospitalPharmacyDispensePackageCountText;
    }

    public void setHospitalPharmacyDispensePieceCountText(String hospitalPharmacyDispensePieceCountText) {
        this.hospitalPharmacyDispensePieceCountText = hospitalPharmacyDispensePieceCountText;
    }

    public void setHospitalPharmacyDispensePriceExcludeTaxText(String hospitalPharmacyDispensePriceExcludeTaxText) {
        this.hospitalPharmacyDispensePriceExcludeTaxText = hospitalPharmacyDispensePriceExcludeTaxText;
    }

    public void setHospitalPharmacyDispensePriceText(String hospitalPharmacyDispensePriceText) {
        this.hospitalPharmacyDispensePriceText = hospitalPharmacyDispensePriceText;
    }

    public void setHospitalPharmacyDispenseCostExcludeTaxText(String hospitalPharmacyDispenseCostExcludeTaxText) {
        this.hospitalPharmacyDispenseCostExcludeTaxText = hospitalPharmacyDispenseCostExcludeTaxText;
    }

    public void setHospitalPharmacyDispenseCostText(String hospitalPharmacyDispenseCostText) {
        this.hospitalPharmacyDispenseCostText = hospitalPharmacyDispenseCostText;
    }

    public void setHospitalAutomaticDispenseCountText(String hospitalAutomaticDispenseCountText) {
        this.hospitalAutomaticDispenseCountText = hospitalAutomaticDispenseCountText;
    }

    public void setHospitalAutomaticDispenseConvertPieceCountText(String hospitalAutomaticDispenseConvertPieceCountText) {
        this.hospitalAutomaticDispenseConvertPieceCountText = hospitalAutomaticDispenseConvertPieceCountText;
    }

    public void setHospitalAutomaticDispensePackageCountText(String hospitalAutomaticDispensePackageCountText) {
        this.hospitalAutomaticDispensePackageCountText = hospitalAutomaticDispensePackageCountText;
    }

    public void setHospitalAutomaticDispensePieceCountText(String hospitalAutomaticDispensePieceCountText) {
        this.hospitalAutomaticDispensePieceCountText = hospitalAutomaticDispensePieceCountText;
    }

    public void setHospitalAutomaticDispensePriceExcludeTaxText(String hospitalAutomaticDispensePriceExcludeTaxText) {
        this.hospitalAutomaticDispensePriceExcludeTaxText = hospitalAutomaticDispensePriceExcludeTaxText;
    }

    public void setHospitalAutomaticDispensePriceText(String hospitalAutomaticDispensePriceText) {
        this.hospitalAutomaticDispensePriceText = hospitalAutomaticDispensePriceText;
    }

    public void setHospitalAutomaticDispenseCostExcludeTaxText(String hospitalAutomaticDispenseCostExcludeTaxText) {
        this.hospitalAutomaticDispenseCostExcludeTaxText = hospitalAutomaticDispenseCostExcludeTaxText;
    }

    public void setHospitalAutomaticDispenseCostText(String hospitalAutomaticDispenseCostText) {
        this.hospitalAutomaticDispenseCostText = hospitalAutomaticDispenseCostText;
    }

    public void setHospitalNoSettleDispenseCountText(String hospitalNoSettleDispenseCountText) {
        this.hospitalNoSettleDispenseCountText = hospitalNoSettleDispenseCountText;
    }

    public void setHospitalNoSettleDispenseConvertPieceCountText(String hospitalNoSettleDispenseConvertPieceCountText) {
        this.hospitalNoSettleDispenseConvertPieceCountText = hospitalNoSettleDispenseConvertPieceCountText;
    }

    public void setHospitalNoSettleDispensePackageCountText(String hospitalNoSettleDispensePackageCountText) {
        this.hospitalNoSettleDispensePackageCountText = hospitalNoSettleDispensePackageCountText;
    }

    public void setHospitalNoSettleDispensePieceCountText(String hospitalNoSettleDispensePieceCountText) {
        this.hospitalNoSettleDispensePieceCountText = hospitalNoSettleDispensePieceCountText;
    }

    public void setHospitalNoSettleDispensePriceExcludeTaxText(String hospitalNoSettleDispensePriceExcludeTaxText) {
        this.hospitalNoSettleDispensePriceExcludeTaxText = hospitalNoSettleDispensePriceExcludeTaxText;
    }

    public void setHospitalNoSettleDispensePriceText(String hospitalNoSettleDispensePriceText) {
        this.hospitalNoSettleDispensePriceText = hospitalNoSettleDispensePriceText;
    }

    public void setHospitalNoSettleDispenseCostExcludeTaxText(String hospitalNoSettleDispenseCostExcludeTaxText) {
        this.hospitalNoSettleDispenseCostExcludeTaxText = hospitalNoSettleDispenseCostExcludeTaxText;
    }

    public void setHospitalNoSettleDispenseCostText(String hospitalNoSettleDispenseCostText) {
        this.hospitalNoSettleDispenseCostText = hospitalNoSettleDispenseCostText;
    }

    public void setCollectOutCountText(String collectOutCountText) {
        this.collectOutCountText = collectOutCountText;
    }

    public void setCollectOutConvertPieceCountText(String collectOutConvertPieceCountText) {
        this.collectOutConvertPieceCountText = collectOutConvertPieceCountText;
    }

    public void setCollectOutPackageCountText(String collectOutPackageCountText) {
        this.collectOutPackageCountText = collectOutPackageCountText;
    }

    public void setCollectOutPieceCountText(String collectOutPieceCountText) {
        this.collectOutPieceCountText = collectOutPieceCountText;
    }

    public void setCollectOutCostExcludeTaxText(String collectOutCostExcludeTaxText) {
        this.collectOutCostExcludeTaxText = collectOutCostExcludeTaxText;
    }

    public void setCollectOutCostText(String collectOutCostText) {
        this.collectOutCostText = collectOutCostText;
    }

    public void setCollectOutPriceExcludeTaxText(String collectOutPriceExcludeTaxText) {
        this.collectOutPriceExcludeTaxText = collectOutPriceExcludeTaxText;
    }

    public void setCollectOutPriceText(String collectOutPriceText) {
        this.collectOutPriceText = collectOutPriceText;
    }

    public void setAllotOutCountText(String allotOutCountText) {
        this.allotOutCountText = allotOutCountText;
    }

    public void setAllotOutConvertPieceCountText(String allotOutConvertPieceCountText) {
        this.allotOutConvertPieceCountText = allotOutConvertPieceCountText;
    }

    public void setAllotOutPackageCountText(String allotOutPackageCountText) {
        this.allotOutPackageCountText = allotOutPackageCountText;
    }

    public void setAllotOutPieceCountText(String allotOutPieceCountText) {
        this.allotOutPieceCountText = allotOutPieceCountText;
    }

    public void setAllotOutCostExcludeTaxText(String allotOutCostExcludeTaxText) {
        this.allotOutCostExcludeTaxText = allotOutCostExcludeTaxText;
    }

    public void setAllotOutCostText(String allotOutCostText) {
        this.allotOutCostText = allotOutCostText;
    }

    public void setAllotOutPriceExcludeTaxText(String allotOutPriceExcludeTaxText) {
        this.allotOutPriceExcludeTaxText = allotOutPriceExcludeTaxText;
    }

    public void setAllotOutPriceText(String allotOutPriceText) {
        this.allotOutPriceText = allotOutPriceText;
    }

    public void setAllotOutInsideCountText(String allotOutInsideCountText) {
        this.allotOutInsideCountText = allotOutInsideCountText;
    }

    public void setAllotOutInsideConvertPieceCountText(String allotOutInsideConvertPieceCountText) {
        this.allotOutInsideConvertPieceCountText = allotOutInsideConvertPieceCountText;
    }

    public void setAllotOutInsidePackageCountText(String allotOutInsidePackageCountText) {
        this.allotOutInsidePackageCountText = allotOutInsidePackageCountText;
    }

    public void setAllotOutInsidePieceCountText(String allotOutInsidePieceCountText) {
        this.allotOutInsidePieceCountText = allotOutInsidePieceCountText;
    }

    public void setAllotOutInsideCostExcludeTaxText(String allotOutInsideCostExcludeTaxText) {
        this.allotOutInsideCostExcludeTaxText = allotOutInsideCostExcludeTaxText;
    }

    public void setAllotOutInsideCostText(String allotOutInsideCostText) {
        this.allotOutInsideCostText = allotOutInsideCostText;
    }

    public void setAllotOutInsidePriceExcludeTaxText(String allotOutInsidePriceExcludeTaxText) {
        this.allotOutInsidePriceExcludeTaxText = allotOutInsidePriceExcludeTaxText;
    }

    public void setAllotOutInsidePriceText(String allotOutInsidePriceText) {
        this.allotOutInsidePriceText = allotOutInsidePriceText;
    }

    public void setDamagedOutCountText(String damagedOutCountText) {
        this.damagedOutCountText = damagedOutCountText;
    }

    public void setDamagedOutConvertPieceCountText(String damagedOutConvertPieceCountText) {
        this.damagedOutConvertPieceCountText = damagedOutConvertPieceCountText;
    }

    public void setDamagedOutPackageCountText(String damagedOutPackageCountText) {
        this.damagedOutPackageCountText = damagedOutPackageCountText;
    }

    public void setDamagedOutPieceCountText(String damagedOutPieceCountText) {
        this.damagedOutPieceCountText = damagedOutPieceCountText;
    }

    public void setDamagedOutCostExcludeTaxText(String damagedOutCostExcludeTaxText) {
        this.damagedOutCostExcludeTaxText = damagedOutCostExcludeTaxText;
    }

    public void setDamagedOutCostText(String damagedOutCostText) {
        this.damagedOutCostText = damagedOutCostText;
    }

    public void setDamagedOutPriceExcludeTaxText(String damagedOutPriceExcludeTaxText) {
        this.damagedOutPriceExcludeTaxText = damagedOutPriceExcludeTaxText;
    }

    public void setDamagedOutPriceText(String damagedOutPriceText) {
        this.damagedOutPriceText = damagedOutPriceText;
    }

    public void setOutDepartmentConsumptionCountText(String outDepartmentConsumptionCountText) {
        this.outDepartmentConsumptionCountText = outDepartmentConsumptionCountText;
    }

    public void setOutDepartmentConsumptionConvertPieceCountText(String outDepartmentConsumptionConvertPieceCountText) {
        this.outDepartmentConsumptionConvertPieceCountText = outDepartmentConsumptionConvertPieceCountText;
    }

    public void setOutDepartmentConsumptionPackageCountText(String outDepartmentConsumptionPackageCountText) {
        this.outDepartmentConsumptionPackageCountText = outDepartmentConsumptionPackageCountText;
    }

    public void setOutDepartmentConsumptionPieceCountText(String outDepartmentConsumptionPieceCountText) {
        this.outDepartmentConsumptionPieceCountText = outDepartmentConsumptionPieceCountText;
    }

    public void setOutDepartmentConsumptionCostAmountText(String outDepartmentConsumptionCostAmountText) {
        this.outDepartmentConsumptionCostAmountText = outDepartmentConsumptionCostAmountText;
    }

    public void setOutDepartmentConsumptionCostAmountExcludingTaxText(String outDepartmentConsumptionCostAmountExcludingTaxText) {
        this.outDepartmentConsumptionCostAmountExcludingTaxText = outDepartmentConsumptionCostAmountExcludingTaxText;
    }

    public void setOutDepartmentConsumptionAmountText(String outDepartmentConsumptionAmountText) {
        this.outDepartmentConsumptionAmountText = outDepartmentConsumptionAmountText;
    }

    public void setOutDepartmentConsumptionAmountExcludingTaxText(String outDepartmentConsumptionAmountExcludingTaxText) {
        this.outDepartmentConsumptionAmountExcludingTaxText = outDepartmentConsumptionAmountExcludingTaxText;
    }

    public void setOutOtherCountText(String outOtherCountText) {
        this.outOtherCountText = outOtherCountText;
    }

    public void setOutOtherConvertPieceCountText(String outOtherConvertPieceCountText) {
        this.outOtherConvertPieceCountText = outOtherConvertPieceCountText;
    }

    public void setOutOtherPackageCountText(String outOtherPackageCountText) {
        this.outOtherPackageCountText = outOtherPackageCountText;
    }

    public void setOutOtherPieceCountText(String outOtherPieceCountText) {
        this.outOtherPieceCountText = outOtherPieceCountText;
    }

    public void setOutOtherCostAmountText(String outOtherCostAmountText) {
        this.outOtherCostAmountText = outOtherCostAmountText;
    }

    public void setOutOtherCostAmountExcludingTaxText(String outOtherCostAmountExcludingTaxText) {
        this.outOtherCostAmountExcludingTaxText = outOtherCostAmountExcludingTaxText;
    }

    public void setOutOtherAmountText(String outOtherAmountText) {
        this.outOtherAmountText = outOtherAmountText;
    }

    public void setOutOtherAmountExcludingTaxText(String outOtherAmountExcludingTaxText) {
        this.outOtherAmountExcludingTaxText = outOtherAmountExcludingTaxText;
    }

    public void setCheckOutCountText(String checkOutCountText) {
        this.checkOutCountText = checkOutCountText;
    }

    public void setCheckOutConvertPieceCountText(String checkOutConvertPieceCountText) {
        this.checkOutConvertPieceCountText = checkOutConvertPieceCountText;
    }

    public void setCheckOutPackageCountText(String checkOutPackageCountText) {
        this.checkOutPackageCountText = checkOutPackageCountText;
    }

    public void setCheckOutPieceCountText(String checkOutPieceCountText) {
        this.checkOutPieceCountText = checkOutPieceCountText;
    }

    public void setCheckOutPriceExcludeTaxText(String checkOutPriceExcludeTaxText) {
        this.checkOutPriceExcludeTaxText = checkOutPriceExcludeTaxText;
    }

    public void setCheckOutPriceText(String checkOutPriceText) {
        this.checkOutPriceText = checkOutPriceText;
    }

    public void setCheckOutCostExcludeTaxText(String checkOutCostExcludeTaxText) {
        this.checkOutCostExcludeTaxText = checkOutCostExcludeTaxText;
    }

    public void setCheckOutCostText(String checkOutCostText) {
        this.checkOutCostText = checkOutCostText;
    }

    public void setProductionOutCountText(String productionOutCountText) {
        this.productionOutCountText = productionOutCountText;
    }

    public void setProductionOutConvertPieceCountText(String productionOutConvertPieceCountText) {
        this.productionOutConvertPieceCountText = productionOutConvertPieceCountText;
    }

    public void setProductionOutPackageCountText(String productionOutPackageCountText) {
        this.productionOutPackageCountText = productionOutPackageCountText;
    }

    public void setProductionOutPieceCountText(String productionOutPieceCountText) {
        this.productionOutPieceCountText = productionOutPieceCountText;
    }

    public void setProductionOutPriceExcludeTaxText(String productionOutPriceExcludeTaxText) {
        this.productionOutPriceExcludeTaxText = productionOutPriceExcludeTaxText;
    }

    public void setProductionOutPriceText(String productionOutPriceText) {
        this.productionOutPriceText = productionOutPriceText;
    }

    public void setProductionOutCostExcludeTaxText(String productionOutCostExcludeTaxText) {
        this.productionOutCostExcludeTaxText = productionOutCostExcludeTaxText;
    }

    public void setProductionOutCostText(String productionOutCostText) {
        this.productionOutCostText = productionOutCostText;
    }

    public void setOutTotalCountText(String outTotalCountText) {
        this.outTotalCountText = outTotalCountText;
    }

    public void setOutTotalConvertPieceCountText(String outTotalConvertPieceCountText) {
        this.outTotalConvertPieceCountText = outTotalConvertPieceCountText;
    }

    public void setOutTotalCostExcludeTaxText(String outTotalCostExcludeTaxText) {
        this.outTotalCostExcludeTaxText = outTotalCostExcludeTaxText;
    }

    public void setOutTotalCostText(String outTotalCostText) {
        this.outTotalCostText = outTotalCostText;
    }

    public void setInTaxRat(BigDecimal inTaxRat) {
        this.inTaxRat = inTaxRat;
    }

    public void setOutTaxRat(BigDecimal outTaxRat) {
        this.outTaxRat = outTaxRat;
    }

    public void setInTaxRatText(String inTaxRatText) {
        this.inTaxRatText = inTaxRatText;
    }

    public void setOutTaxRatText(String outTaxRatText) {
        this.outTaxRatText = outTaxRatText;
    }

    public void setBeforeInTaxRatText(String beforeInTaxRatText) {
        this.beforeInTaxRatText = beforeInTaxRatText;
    }

    public void setBeforeOutTaxRatText(String beforeOutTaxRatText) {
        this.beforeOutTaxRatText = beforeOutTaxRatText;
    }

    public void setAfterTotalCostModifyExcludeTaxText(String afterTotalCostModifyExcludeTaxText) {
        this.afterTotalCostModifyExcludeTaxText = afterTotalCostModifyExcludeTaxText;
    }

    public void setGoodsAfterTotalCostModifyExcludeTaxText(String goodsAfterTotalCostModifyExcludeTaxText) {
        this.goodsAfterTotalCostModifyExcludeTaxText = goodsAfterTotalCostModifyExcludeTaxText;
    }

    public void setGoodsPharmacyAfterTotalCostModifyExcludeTaxText(String goodsPharmacyAfterTotalCostModifyExcludeTaxText) {
        this.goodsPharmacyAfterTotalCostModifyExcludeTaxText = goodsPharmacyAfterTotalCostModifyExcludeTaxText;
    }

    public void setEffectedTime(String effectedTime) {
        this.effectedTime = effectedTime;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public void setLastModified(String lastModified) {
        this.lastModified = lastModified;
    }

    public void setDosageFormTypeName(String dosageFormTypeName) {
        this.dosageFormTypeName = dosageFormTypeName;
    }

    public void setNationalStandardImplementationName(String nationalStandardImplementationName) {
        this.nationalStandardImplementationName = nationalStandardImplementationName;
    }

    public BigDecimal getBeginCount() {
        return beginCount;
    }

    public void setBeginCount(BigDecimal beginCount) {
        this.beginCount = beginCount;
    }

    public BigDecimal getBeginConvertPieceCount() {
        return beginConvertPieceCount;
    }

    public void setBeginConvertPieceCount(BigDecimal beginConvertPieceCount) {
        this.beginConvertPieceCount = beginConvertPieceCount;
    }

    public BigDecimal getBeginPackageCount() {
        return beginPackageCount;
    }

    public void setBeginPackageCount(BigDecimal beginPackageCount) {
        this.beginPackageCount = beginPackageCount;
    }

    public BigDecimal getBeginPieceCount() {
        return beginPieceCount;
    }

    public void setBeginPieceCount(BigDecimal beginPieceCount) {
        this.beginPieceCount = beginPieceCount;
    }

    public BigDecimal getBeginCostExcludeTax() {
        return beginCostExcludeTax;
    }

    public void setBeginCostExcludeTax(BigDecimal beginCostExcludeTax) {
        this.beginCostExcludeTax = beginCostExcludeTax;
    }

    public BigDecimal getBeginCost() {
        return beginCost;
    }

    public void setBeginCost(BigDecimal beginCost) {
        this.beginCost = beginCost;
    }

    public BigDecimal getBeginPriceExcludeTax() {
        return beginPriceExcludeTax;
    }

    public void setBeginPriceExcludeTax(BigDecimal beginPriceExcludeTax) {
        this.beginPriceExcludeTax = beginPriceExcludeTax;
    }

    public BigDecimal getBeginPrice() {
        return beginPrice;
    }

    public void setBeginPrice(BigDecimal beginPrice) {
        this.beginPrice = beginPrice;
    }

    public BigDecimal getInInitCount() {
        return inInitCount;
    }

    public void setInInitCount(BigDecimal inInitCount) {
        this.inInitCount = inInitCount;
    }

    public BigDecimal getInInitConvertPieceCount() {
        return inInitConvertPieceCount;
    }

    public void setInInitConvertPieceCount(BigDecimal inInitConvertPieceCount) {
        this.inInitConvertPieceCount = inInitConvertPieceCount;
    }

    public BigDecimal getInInitPackageCount() {
        return inInitPackageCount;
    }

    public void setInInitPackageCount(BigDecimal inInitPackageCount) {
        this.inInitPackageCount = inInitPackageCount;
    }

    public BigDecimal getInInitPieceCount() {
        return inInitPieceCount;
    }

    public void setInInitPieceCount(BigDecimal inInitPieceCount) {
        this.inInitPieceCount = inInitPieceCount;
    }

    public BigDecimal getInInitCostExcludeTax() {
        return inInitCostExcludeTax;
    }

    public void setInInitCostExcludeTax(BigDecimal inInitCostExcludeTax) {
        this.inInitCostExcludeTax = inInitCostExcludeTax;
    }

    public BigDecimal getInInitCost() {
        return inInitCost;
    }

    public void setInInitCost(BigDecimal inInitCost) {
        this.inInitCost = inInitCost;
    }

    public BigDecimal getInInitPriceExcludeTax() {
        return inInitPriceExcludeTax;
    }

    public void setInInitPriceExcludeTax(BigDecimal inInitPriceExcludeTax) {
        this.inInitPriceExcludeTax = inInitPriceExcludeTax;
    }

    public BigDecimal getInInitPrice() {
        return inInitPrice;
    }

    public void setInInitPrice(BigDecimal inInitPrice) {
        this.inInitPrice = inInitPrice;
    }

    public BigDecimal getEndCount() {
        return endCount;
    }

    public void setEndCount(BigDecimal endCount) {
        this.endCount = endCount;
    }

    public BigDecimal getEndConvertPieceCount() {
        return endConvertPieceCount;
    }

    public void setEndConvertPieceCount(BigDecimal endConvertPieceCount) {
        this.endConvertPieceCount = endConvertPieceCount;
    }

    public BigDecimal getEndPackageCount() {
        return endPackageCount;
    }

    public void setEndPackageCount(BigDecimal endPackageCount) {
        this.endPackageCount = endPackageCount;
    }

    public BigDecimal getEndPieceCount() {
        return endPieceCount;
    }

    public void setEndPieceCount(BigDecimal endPieceCount) {
        this.endPieceCount = endPieceCount;
    }

    public BigDecimal getEndCostExcludeTax() {
        return endCostExcludeTax;
    }

    public void setEndCostExcludeTax(BigDecimal endCostExcludeTax) {
        this.endCostExcludeTax = endCostExcludeTax;
    }

    public BigDecimal getEndCost() {
        return endCost;
    }

    public void setEndCost(BigDecimal endCost) {
        this.endCost = endCost;
    }

    public BigDecimal getEndPriceExcludeTax() {
        return endPriceExcludeTax;
    }

    public void setEndPriceExcludeTax(BigDecimal endPriceExcludeTax) {
        this.endPriceExcludeTax = endPriceExcludeTax;
    }

    public BigDecimal getEndPrice() {
        return endPrice;
    }

    public void setEndPrice(BigDecimal endPrice) {
        this.endPrice = endPrice;
    }

    public BigDecimal getPurchaseInCount() {
        return purchaseInCount;
    }

    public void setPurchaseInCount(BigDecimal purchaseInCount) {
        this.purchaseInCount = purchaseInCount;
    }

    public BigDecimal getPurchaseInConvertPieceCount() {
        return purchaseInConvertPieceCount;
    }

    public void setPurchaseInConvertPieceCount(BigDecimal purchaseInConvertPieceCount) {
        this.purchaseInConvertPieceCount = purchaseInConvertPieceCount;
    }

    public BigDecimal getPurchaseInPackageCount() {
        return purchaseInPackageCount;
    }

    public void setPurchaseInPackageCount(BigDecimal purchaseInPackageCount) {
        this.purchaseInPackageCount = purchaseInPackageCount;
    }

    public BigDecimal getPurchaseInPieceCount() {
        return purchaseInPieceCount;
    }

    public void setPurchaseInPieceCount(BigDecimal purchaseInPieceCount) {
        this.purchaseInPieceCount = purchaseInPieceCount;
    }

    public BigDecimal getPurchaseInCostExcludeTax() {
        return purchaseInCostExcludeTax;
    }

    public void setPurchaseInCostExcludeTax(BigDecimal purchaseInCostExcludeTax) {
        this.purchaseInCostExcludeTax = purchaseInCostExcludeTax;
    }

    public BigDecimal getPurchaseInCost() {
        return purchaseInCost;
    }

    public void setPurchaseInCost(BigDecimal purchaseInCost) {
        this.purchaseInCost = purchaseInCost;
    }

    public BigDecimal getPurchaseInPriceExcludeTax() {
        return purchaseInPriceExcludeTax;
    }

    public void setPurchaseInPriceExcludeTax(BigDecimal purchaseInPriceExcludeTax) {
        this.purchaseInPriceExcludeTax = purchaseInPriceExcludeTax;
    }

    public BigDecimal getPurchaseInPrice() {
        return purchaseInPrice;
    }

    public void setPurchaseInPrice(BigDecimal purchaseInPrice) {
        this.purchaseInPrice = purchaseInPrice;
    }

    public BigDecimal getAllotInCount() {
        return allotInCount;
    }

    public void setAllotInCount(BigDecimal allotInCount) {
        this.allotInCount = allotInCount;
    }

    public BigDecimal getAllotInConvertPieceCount() {
        return allotInConvertPieceCount;
    }

    public void setAllotInConvertPieceCount(BigDecimal allotInConvertPieceCount) {
        this.allotInConvertPieceCount = allotInConvertPieceCount;
    }

    public BigDecimal getAllotInPackageCount() {
        return allotInPackageCount;
    }

    public void setAllotInPackageCount(BigDecimal allotInPackageCount) {
        this.allotInPackageCount = allotInPackageCount;
    }

    public BigDecimal getAllotInPieceCount() {
        return allotInPieceCount;
    }

    public void setAllotInPieceCount(BigDecimal allotInPieceCount) {
        this.allotInPieceCount = allotInPieceCount;
    }

    public BigDecimal getAllotInCostExcludeTax() {
        return allotInCostExcludeTax;
    }

    public void setAllotInCostExcludeTax(BigDecimal allotInCostExcludeTax) {
        this.allotInCostExcludeTax = allotInCostExcludeTax;
    }

    public BigDecimal getAllotInCost() {
        return allotInCost;
    }

    public void setAllotInCost(BigDecimal allotInCost) {
        this.allotInCost = allotInCost;
    }

    public BigDecimal getAllotInPriceExcludeTax() {
        return allotInPriceExcludeTax;
    }

    public void setAllotInPriceExcludeTax(BigDecimal allotInPriceExcludeTax) {
        this.allotInPriceExcludeTax = allotInPriceExcludeTax;
    }

    public BigDecimal getAllotInPrice() {
        return allotInPrice;
    }

    public void setAllotInPrice(BigDecimal allotInPrice) {
        this.allotInPrice = allotInPrice;
    }

    public BigDecimal getAllotInInsideCount() {
        return allotInInsideCount;
    }

    public void setAllotInInsideCount(BigDecimal allotInInsideCount) {
        this.allotInInsideCount = allotInInsideCount;
    }

    public BigDecimal getAllotInInsideConvertPieceCount() {
        return allotInInsideConvertPieceCount;
    }

    public void setAllotInInsideConvertPieceCount(BigDecimal allotInInsideConvertPieceCount) {
        this.allotInInsideConvertPieceCount = allotInInsideConvertPieceCount;
    }

    public BigDecimal getAllotInInsidePackageCount() {
        return allotInInsidePackageCount;
    }

    public void setAllotInInsidePackageCount(BigDecimal allotInInsidePackageCount) {
        this.allotInInsidePackageCount = allotInInsidePackageCount;
    }

    public BigDecimal getAllotInInsidePieceCount() {
        return allotInInsidePieceCount;
    }

    public void setAllotInInsidePieceCount(BigDecimal allotInInsidePieceCount) {
        this.allotInInsidePieceCount = allotInInsidePieceCount;
    }

    public BigDecimal getAllotInInsideCostExcludeTax() {
        return allotInInsideCostExcludeTax;
    }

    public void setAllotInInsideCostExcludeTax(BigDecimal allotInInsideCostExcludeTax) {
        this.allotInInsideCostExcludeTax = allotInInsideCostExcludeTax;
    }

    public BigDecimal getAllotInInsideCost() {
        return allotInInsideCost;
    }

    public void setAllotInInsideCost(BigDecimal allotInInsideCost) {
        this.allotInInsideCost = allotInInsideCost;
    }

    public BigDecimal getAllotInInsidePriceExcludeTax() {
        return allotInInsidePriceExcludeTax;
    }

    public void setAllotInInsidePriceExcludeTax(BigDecimal allotInInsidePriceExcludeTax) {
        this.allotInInsidePriceExcludeTax = allotInInsidePriceExcludeTax;
    }

    public BigDecimal getAllotInInsidePrice() {
        return allotInInsidePrice;
    }

    public void setAllotInInsidePrice(BigDecimal allotInInsidePrice) {
        this.allotInInsidePrice = allotInInsidePrice;
    }

    public BigDecimal getCheckInCount() {
        return checkInCount;
    }

    public void setCheckInCount(BigDecimal checkInCount) {
        this.checkInCount = checkInCount;
    }

    public BigDecimal getCheckInConvertPieceCount() {
        return checkInConvertPieceCount;
    }

    public void setCheckInConvertPieceCount(BigDecimal checkInConvertPieceCount) {
        this.checkInConvertPieceCount = checkInConvertPieceCount;
    }

    public BigDecimal getCheckInPackageCount() {
        return checkInPackageCount;
    }

    public void setCheckInPackageCount(BigDecimal checkInPackageCount) {
        this.checkInPackageCount = checkInPackageCount;
    }

    public BigDecimal getCheckInPieceCount() {
        return checkInPieceCount;
    }

    public void setCheckInPieceCount(BigDecimal checkInPieceCount) {
        this.checkInPieceCount = checkInPieceCount;
    }

    public BigDecimal getCheckInPriceExcludeTax() {
        return checkInPriceExcludeTax;
    }

    public void setCheckInPriceExcludeTax(BigDecimal checkInPriceExcludeTax) {
        this.checkInPriceExcludeTax = checkInPriceExcludeTax;
    }

    public BigDecimal getCheckInPrice() {
        return checkInPrice;
    }

    public void setCheckInPrice(BigDecimal checkInPrice) {
        this.checkInPrice = checkInPrice;
    }

    public BigDecimal getCheckInCostExcludeTax() {
        return checkInCostExcludeTax;
    }

    public void setCheckInCostExcludeTax(BigDecimal checkInCostExcludeTax) {
        this.checkInCostExcludeTax = checkInCostExcludeTax;
    }

    public BigDecimal getCheckInCost() {
        return checkInCost;
    }

    public void setCheckInCost(BigDecimal checkInCost) {
        this.checkInCost = checkInCost;
    }

    public BigDecimal getInReceiveCount() {
        return inReceiveCount;
    }

    public void setInReceiveCount(BigDecimal inReceiveCount) {
        this.inReceiveCount = inReceiveCount;
    }

    public BigDecimal getInReceiveConvertPieceCount() {
        return inReceiveConvertPieceCount;
    }

    public void setInReceiveConvertPieceCount(BigDecimal inReceiveConvertPieceCount) {
        this.inReceiveConvertPieceCount = inReceiveConvertPieceCount;
    }

    public BigDecimal getInReceivePackageCount() {
        return inReceivePackageCount;
    }

    public void setInReceivePackageCount(BigDecimal inReceivePackageCount) {
        this.inReceivePackageCount = inReceivePackageCount;
    }

    public BigDecimal getInReceivePieceCount() {
        return inReceivePieceCount;
    }

    public void setInReceivePieceCount(BigDecimal inReceivePieceCount) {
        this.inReceivePieceCount = inReceivePieceCount;
    }

    public BigDecimal getInReceiveCostAmount() {
        return inReceiveCostAmount;
    }

    public void setInReceiveCostAmount(BigDecimal inReceiveCostAmount) {
        this.inReceiveCostAmount = inReceiveCostAmount;
    }

    public BigDecimal getInReceiveCostAmountExcludingTax() {
        return inReceiveCostAmountExcludingTax;
    }

    public void setInReceiveCostAmountExcludingTax(BigDecimal inReceiveCostAmountExcludingTax) {
        this.inReceiveCostAmountExcludingTax = inReceiveCostAmountExcludingTax;
    }

    public BigDecimal getInReceiveAmount() {
        return inReceiveAmount;
    }

    public void setInReceiveAmount(BigDecimal inReceiveAmount) {
        this.inReceiveAmount = inReceiveAmount;
    }

    public BigDecimal getInReceiveAmountExcludingTax() {
        return inReceiveAmountExcludingTax;
    }

    public void setInReceiveAmountExcludingTax(BigDecimal inReceiveAmountExcludingTax) {
        this.inReceiveAmountExcludingTax = inReceiveAmountExcludingTax;
    }

    public BigDecimal getInSpecificationModificationCount() {
        return inSpecificationModificationCount;
    }

    public void setInSpecificationModificationCount(BigDecimal inSpecificationModificationCount) {
        this.inSpecificationModificationCount = inSpecificationModificationCount;
    }

    public BigDecimal getInSpecificationModificationConvertPieceCount() {
        return inSpecificationModificationConvertPieceCount;
    }

    public void setInSpecificationModificationConvertPieceCount(BigDecimal inSpecificationModificationConvertPieceCount) {
        this.inSpecificationModificationConvertPieceCount = inSpecificationModificationConvertPieceCount;
    }

    public BigDecimal getInSpecificationModificationCostExcludeTax() {
        return inSpecificationModificationCostExcludeTax;
    }

    public void setInSpecificationModificationCostExcludeTax(BigDecimal inSpecificationModificationCostExcludeTax) {
        this.inSpecificationModificationCostExcludeTax = inSpecificationModificationCostExcludeTax;
    }

    public BigDecimal getInSpecificationModificationCost() {
        return inSpecificationModificationCost;
    }

    public void setInSpecificationModificationCost(BigDecimal inSpecificationModificationCost) {
        this.inSpecificationModificationCost = inSpecificationModificationCost;
    }

    public BigDecimal getInSpecificationModificationPriceExcludeTax() {
        return inSpecificationModificationPriceExcludeTax;
    }

    public void setInSpecificationModificationPriceExcludeTax(BigDecimal inSpecificationModificationPriceExcludeTax) {
        this.inSpecificationModificationPriceExcludeTax = inSpecificationModificationPriceExcludeTax;
    }

    public BigDecimal getInSpecificationModificationPrice() {
        return inSpecificationModificationPrice;
    }

    public void setInSpecificationModificationPrice(BigDecimal inSpecificationModificationPrice) {
        this.inSpecificationModificationPrice = inSpecificationModificationPrice;
    }

    public BigDecimal getInTotalCount() {
        return inTotalCount;
    }

    public void setInTotalCount(BigDecimal inTotalCount) {
        this.inTotalCount = inTotalCount;
    }

    public BigDecimal getInTotalConvertPieceCount() {
        return inTotalConvertPieceCount;
    }

    public void setInTotalConvertPieceCount(BigDecimal inTotalConvertPieceCount) {
        this.inTotalConvertPieceCount = inTotalConvertPieceCount;
    }

    public BigDecimal getInTotalPackageCount() {
        return inTotalPackageCount;
    }

    public void setInTotalPackageCount(BigDecimal inTotalPackageCount) {
        this.inTotalPackageCount = inTotalPackageCount;
    }

    public BigDecimal getInTotalPieceCount() {
        return inTotalPieceCount;
    }

    public void setInTotalPieceCount(BigDecimal inTotalPieceCount) {
        this.inTotalPieceCount = inTotalPieceCount;
    }

    public BigDecimal getInTotalPriceExcludeTax() {
        return inTotalPriceExcludeTax;
    }

    public void setInTotalPriceExcludeTax(BigDecimal inTotalPriceExcludeTax) {
        this.inTotalPriceExcludeTax = inTotalPriceExcludeTax;
    }

    public BigDecimal getInTotalPrice() {
        return inTotalPrice;
    }

    public void setInTotalPrice(BigDecimal inTotalPrice) {
        this.inTotalPrice = inTotalPrice;
    }

    public BigDecimal getInTotalCostExcludeTax() {
        return inTotalCostExcludeTax;
    }

    public void setInTotalCostExcludeTax(BigDecimal inTotalCostExcludeTax) {
        this.inTotalCostExcludeTax = inTotalCostExcludeTax;
    }

    public BigDecimal getInTotalCost() {
        return inTotalCost;
    }

    public void setInTotalCost(BigDecimal inTotalCost) {
        this.inTotalCost = inTotalCost;
    }

    public BigDecimal getDispenseCount() {
        return dispenseCount;
    }

    public void setDispenseCount(BigDecimal dispenseCount) {
        this.dispenseCount = dispenseCount;
    }

    public BigDecimal getDispenseConvertPieceCount() {
        return dispenseConvertPieceCount;
    }

    public void setDispenseConvertPieceCount(BigDecimal dispenseConvertPieceCount) {
        this.dispenseConvertPieceCount = dispenseConvertPieceCount;
    }

    public BigDecimal getDispensePackageCount() {
        return dispensePackageCount;
    }

    public void setDispensePackageCount(BigDecimal dispensePackageCount) {
        this.dispensePackageCount = dispensePackageCount;
    }

    public BigDecimal getDispensePieceCount() {
        return dispensePieceCount;
    }

    public void setDispensePieceCount(BigDecimal dispensePieceCount) {
        this.dispensePieceCount = dispensePieceCount;
    }

    public BigDecimal getDispensePriceExcludeTax() {
        return dispensePriceExcludeTax;
    }

    public void setDispensePriceExcludeTax(BigDecimal dispensePriceExcludeTax) {
        this.dispensePriceExcludeTax = dispensePriceExcludeTax;
    }

    public BigDecimal getDispensePrice() {
        return dispensePrice;
    }

    public void setDispensePrice(BigDecimal dispensePrice) {
        this.dispensePrice = dispensePrice;
    }

    public BigDecimal getDispenseCostExcludeTax() {
        return dispenseCostExcludeTax;
    }

    public void setDispenseCostExcludeTax(BigDecimal dispenseCostExcludeTax) {
        this.dispenseCostExcludeTax = dispenseCostExcludeTax;
    }

    public BigDecimal getDispenseCost() {
        return dispenseCost;
    }

    public void setDispenseCost(BigDecimal dispenseCost) {
        this.dispenseCost = dispenseCost;
    }

    public BigDecimal getOutPatientDispenseCount() {
        return outPatientDispenseCount;
    }

    public void setOutPatientDispenseCount(BigDecimal outPatientDispenseCount) {
        this.outPatientDispenseCount = outPatientDispenseCount;
    }

    public BigDecimal getOutPatientDispenseConvertPieceCount() {
        return outPatientDispenseConvertPieceCount;
    }

    public void setOutPatientDispenseConvertPieceCount(BigDecimal outPatientDispenseConvertPieceCount) {
        this.outPatientDispenseConvertPieceCount = outPatientDispenseConvertPieceCount;
    }

    public BigDecimal getOutPatientDispensePackageCount() {
        return outPatientDispensePackageCount;
    }

    public void setOutPatientDispensePackageCount(BigDecimal outPatientDispensePackageCount) {
        this.outPatientDispensePackageCount = outPatientDispensePackageCount;
    }

    public BigDecimal getOutPatientDispensePieceCount() {
        return outPatientDispensePieceCount;
    }

    public void setOutPatientDispensePieceCount(BigDecimal outPatientDispensePieceCount) {
        this.outPatientDispensePieceCount = outPatientDispensePieceCount;
    }

    public BigDecimal getOutPatientDispensePriceExcludeTax() {
        return outPatientDispensePriceExcludeTax;
    }

    public void setOutPatientDispensePriceExcludeTax(BigDecimal outPatientDispensePriceExcludeTax) {
        this.outPatientDispensePriceExcludeTax = outPatientDispensePriceExcludeTax;
    }

    public BigDecimal getOutPatientDispensePrice() {
        return outPatientDispensePrice;
    }

    public void setOutPatientDispensePrice(BigDecimal outPatientDispensePrice) {
        this.outPatientDispensePrice = outPatientDispensePrice;
    }

    public BigDecimal getOutPatientDispenseCostExcludeTax() {
        return outPatientDispenseCostExcludeTax;
    }

    public void setOutPatientDispenseCostExcludeTax(BigDecimal outPatientDispenseCostExcludeTax) {
        this.outPatientDispenseCostExcludeTax = outPatientDispenseCostExcludeTax;
    }

    public BigDecimal getOutPatientDispenseCost() {
        return outPatientDispenseCost;
    }

    public void setOutPatientDispenseCost(BigDecimal outPatientDispenseCost) {
        this.outPatientDispenseCost = outPatientDispenseCost;
    }

    public BigDecimal getHospitalPharmacyDispenseCount() {
        return hospitalPharmacyDispenseCount;
    }

    public void setHospitalPharmacyDispenseCount(BigDecimal hospitalPharmacyDispenseCount) {
        this.hospitalPharmacyDispenseCount = hospitalPharmacyDispenseCount;
    }

    public BigDecimal getHospitalPharmacyDispenseConvertPieceCount() {
        return hospitalPharmacyDispenseConvertPieceCount;
    }

    public void setHospitalPharmacyDispenseConvertPieceCount(BigDecimal hospitalPharmacyDispenseConvertPieceCount) {
        this.hospitalPharmacyDispenseConvertPieceCount = hospitalPharmacyDispenseConvertPieceCount;
    }

    public BigDecimal getHospitalPharmacyDispensePackageCount() {
        return hospitalPharmacyDispensePackageCount;
    }

    public void setHospitalPharmacyDispensePackageCount(BigDecimal hospitalPharmacyDispensePackageCount) {
        this.hospitalPharmacyDispensePackageCount = hospitalPharmacyDispensePackageCount;
    }

    public BigDecimal getHospitalPharmacyDispensePieceCount() {
        return hospitalPharmacyDispensePieceCount;
    }

    public void setHospitalPharmacyDispensePieceCount(BigDecimal hospitalPharmacyDispensePieceCount) {
        this.hospitalPharmacyDispensePieceCount = hospitalPharmacyDispensePieceCount;
    }

    public BigDecimal getHospitalPharmacyDispensePriceExcludeTax() {
        return hospitalPharmacyDispensePriceExcludeTax;
    }

    public void setHospitalPharmacyDispensePriceExcludeTax(BigDecimal hospitalPharmacyDispensePriceExcludeTax) {
        this.hospitalPharmacyDispensePriceExcludeTax = hospitalPharmacyDispensePriceExcludeTax;
    }

    public BigDecimal getHospitalPharmacyDispensePrice() {
        return hospitalPharmacyDispensePrice;
    }

    public void setHospitalPharmacyDispensePrice(BigDecimal hospitalPharmacyDispensePrice) {
        this.hospitalPharmacyDispensePrice = hospitalPharmacyDispensePrice;
    }

    public BigDecimal getHospitalPharmacyDispenseCostExcludeTax() {
        return hospitalPharmacyDispenseCostExcludeTax;
    }

    public void setHospitalPharmacyDispenseCostExcludeTax(BigDecimal hospitalPharmacyDispenseCostExcludeTax) {
        this.hospitalPharmacyDispenseCostExcludeTax = hospitalPharmacyDispenseCostExcludeTax;
    }

    public BigDecimal getHospitalPharmacyDispenseCost() {
        return hospitalPharmacyDispenseCost;
    }

    public void setHospitalPharmacyDispenseCost(BigDecimal hospitalPharmacyDispenseCost) {
        this.hospitalPharmacyDispenseCost = hospitalPharmacyDispenseCost;
    }

    public BigDecimal getHospitalAutomaticDispenseCount() {
        return hospitalAutomaticDispenseCount;
    }

    public void setHospitalAutomaticDispenseCount(BigDecimal hospitalAutomaticDispenseCount) {
        this.hospitalAutomaticDispenseCount = hospitalAutomaticDispenseCount;
    }

    public BigDecimal getHospitalAutomaticDispenseConvertPieceCount() {
        return hospitalAutomaticDispenseConvertPieceCount;
    }

    public void setHospitalAutomaticDispenseConvertPieceCount(BigDecimal hospitalAutomaticDispenseConvertPieceCount) {
        this.hospitalAutomaticDispenseConvertPieceCount = hospitalAutomaticDispenseConvertPieceCount;
    }

    public BigDecimal getHospitalAutomaticDispensePackageCount() {
        return hospitalAutomaticDispensePackageCount;
    }

    public void setHospitalAutomaticDispensePackageCount(BigDecimal hospitalAutomaticDispensePackageCount) {
        this.hospitalAutomaticDispensePackageCount = hospitalAutomaticDispensePackageCount;
    }

    public BigDecimal getHospitalAutomaticDispensePieceCount() {
        return hospitalAutomaticDispensePieceCount;
    }

    public void setHospitalAutomaticDispensePieceCount(BigDecimal hospitalAutomaticDispensePieceCount) {
        this.hospitalAutomaticDispensePieceCount = hospitalAutomaticDispensePieceCount;
    }

    public BigDecimal getHospitalAutomaticDispensePriceExcludeTax() {
        return hospitalAutomaticDispensePriceExcludeTax;
    }

    public void setHospitalAutomaticDispensePriceExcludeTax(BigDecimal hospitalAutomaticDispensePriceExcludeTax) {
        this.hospitalAutomaticDispensePriceExcludeTax = hospitalAutomaticDispensePriceExcludeTax;
    }

    public BigDecimal getHospitalAutomaticDispensePrice() {
        return hospitalAutomaticDispensePrice;
    }

    public void setHospitalAutomaticDispensePrice(BigDecimal hospitalAutomaticDispensePrice) {
        this.hospitalAutomaticDispensePrice = hospitalAutomaticDispensePrice;
    }

    public BigDecimal getHospitalAutomaticDispenseCostExcludeTax() {
        return hospitalAutomaticDispenseCostExcludeTax;
    }

    public void setHospitalAutomaticDispenseCostExcludeTax(BigDecimal hospitalAutomaticDispenseCostExcludeTax) {
        this.hospitalAutomaticDispenseCostExcludeTax = hospitalAutomaticDispenseCostExcludeTax;
    }

    public BigDecimal getHospitalAutomaticDispenseCost() {
        return hospitalAutomaticDispenseCost;
    }

    public void setHospitalAutomaticDispenseCost(BigDecimal hospitalAutomaticDispenseCost) {
        this.hospitalAutomaticDispenseCost = hospitalAutomaticDispenseCost;
    }

    public BigDecimal getHospitalNoSettleDispenseCount() {
        return hospitalNoSettleDispenseCount;
    }

    public void setHospitalNoSettleDispenseCount(BigDecimal hospitalNoSettleDispenseCount) {
        this.hospitalNoSettleDispenseCount = hospitalNoSettleDispenseCount;
    }

    public BigDecimal getHospitalNoSettleDispenseConvertPieceCount() {
        return hospitalNoSettleDispenseConvertPieceCount;
    }

    public void setHospitalNoSettleDispenseConvertPieceCount(BigDecimal hospitalNoSettleDispenseConvertPieceCount) {
        this.hospitalNoSettleDispenseConvertPieceCount = hospitalNoSettleDispenseConvertPieceCount;
    }

    public BigDecimal getHospitalNoSettleDispensePackageCount() {
        return hospitalNoSettleDispensePackageCount;
    }

    public void setHospitalNoSettleDispensePackageCount(BigDecimal hospitalNoSettleDispensePackageCount) {
        this.hospitalNoSettleDispensePackageCount = hospitalNoSettleDispensePackageCount;
    }

    public BigDecimal getHospitalNoSettleDispensePieceCount() {
        return hospitalNoSettleDispensePieceCount;
    }

    public void setHospitalNoSettleDispensePieceCount(BigDecimal hospitalNoSettleDispensePieceCount) {
        this.hospitalNoSettleDispensePieceCount = hospitalNoSettleDispensePieceCount;
    }

    public BigDecimal getHospitalNoSettleDispensePriceExcludeTax() {
        return hospitalNoSettleDispensePriceExcludeTax;
    }

    public void setHospitalNoSettleDispensePriceExcludeTax(BigDecimal hospitalNoSettleDispensePriceExcludeTax) {
        this.hospitalNoSettleDispensePriceExcludeTax = hospitalNoSettleDispensePriceExcludeTax;
    }

    public BigDecimal getHospitalNoSettleDispensePrice() {
        return hospitalNoSettleDispensePrice;
    }

    public void setHospitalNoSettleDispensePrice(BigDecimal hospitalNoSettleDispensePrice) {
        this.hospitalNoSettleDispensePrice = hospitalNoSettleDispensePrice;
    }

    public BigDecimal getHospitalNoSettleDispenseCostExcludeTax() {
        return hospitalNoSettleDispenseCostExcludeTax;
    }

    public void setHospitalNoSettleDispenseCostExcludeTax(BigDecimal hospitalNoSettleDispenseCostExcludeTax) {
        this.hospitalNoSettleDispenseCostExcludeTax = hospitalNoSettleDispenseCostExcludeTax;
    }

    public BigDecimal getHospitalNoSettleDispenseCost() {
        return hospitalNoSettleDispenseCost;
    }

    public void setHospitalNoSettleDispenseCost(BigDecimal hospitalNoSettleDispenseCost) {
        this.hospitalNoSettleDispenseCost = hospitalNoSettleDispenseCost;
    }

    public BigDecimal getCollectOutCount() {
        return collectOutCount;
    }

    public void setCollectOutCount(BigDecimal collectOutCount) {
        this.collectOutCount = collectOutCount;
    }

    public BigDecimal getCollectOutConvertPieceCount() {
        return collectOutConvertPieceCount;
    }

    public void setCollectOutConvertPieceCount(BigDecimal collectOutConvertPieceCount) {
        this.collectOutConvertPieceCount = collectOutConvertPieceCount;
    }

    public BigDecimal getCollectOutPackageCount() {
        return collectOutPackageCount;
    }

    public void setCollectOutPackageCount(BigDecimal collectOutPackageCount) {
        this.collectOutPackageCount = collectOutPackageCount;
    }

    public BigDecimal getCollectOutPieceCount() {
        return collectOutPieceCount;
    }

    public void setCollectOutPieceCount(BigDecimal collectOutPieceCount) {
        this.collectOutPieceCount = collectOutPieceCount;
    }

    public BigDecimal getCollectOutCostExcludeTax() {
        return collectOutCostExcludeTax;
    }

    public void setCollectOutCostExcludeTax(BigDecimal collectOutCostExcludeTax) {
        this.collectOutCostExcludeTax = collectOutCostExcludeTax;
    }

    public BigDecimal getCollectOutCost() {
        return collectOutCost;
    }

    public void setCollectOutCost(BigDecimal collectOutCost) {
        this.collectOutCost = collectOutCost;
    }

    public BigDecimal getCollectOutPriceExcludeTax() {
        return collectOutPriceExcludeTax;
    }

    public void setCollectOutPriceExcludeTax(BigDecimal collectOutPriceExcludeTax) {
        this.collectOutPriceExcludeTax = collectOutPriceExcludeTax;
    }

    public BigDecimal getCollectOutPrice() {
        return collectOutPrice;
    }

    public void setCollectOutPrice(BigDecimal collectOutPrice) {
        this.collectOutPrice = collectOutPrice;
    }

    public BigDecimal getAllotOutCount() {
        return allotOutCount;
    }

    public void setAllotOutCount(BigDecimal allotOutCount) {
        this.allotOutCount = allotOutCount;
    }

    public BigDecimal getAllotOutConvertPieceCount() {
        return allotOutConvertPieceCount;
    }

    public void setAllotOutConvertPieceCount(BigDecimal allotOutConvertPieceCount) {
        this.allotOutConvertPieceCount = allotOutConvertPieceCount;
    }

    public BigDecimal getAllotOutPackageCount() {
        return allotOutPackageCount;
    }

    public void setAllotOutPackageCount(BigDecimal allotOutPackageCount) {
        this.allotOutPackageCount = allotOutPackageCount;
    }

    public BigDecimal getAllotOutPieceCount() {
        return allotOutPieceCount;
    }

    public void setAllotOutPieceCount(BigDecimal allotOutPieceCount) {
        this.allotOutPieceCount = allotOutPieceCount;
    }

    public BigDecimal getAllotOutCostExcludeTax() {
        return allotOutCostExcludeTax;
    }

    public void setAllotOutCostExcludeTax(BigDecimal allotOutCostExcludeTax) {
        this.allotOutCostExcludeTax = allotOutCostExcludeTax;
    }

    public BigDecimal getAllotOutCost() {
        return allotOutCost;
    }

    public void setAllotOutCost(BigDecimal allotOutCost) {
        this.allotOutCost = allotOutCost;
    }

    public BigDecimal getAllotOutPriceExcludeTax() {
        return allotOutPriceExcludeTax;
    }

    public void setAllotOutPriceExcludeTax(BigDecimal allotOutPriceExcludeTax) {
        this.allotOutPriceExcludeTax = allotOutPriceExcludeTax;
    }

    public BigDecimal getAllotOutPrice() {
        return allotOutPrice;
    }

    public void setAllotOutPrice(BigDecimal allotOutPrice) {
        this.allotOutPrice = allotOutPrice;
    }

    public BigDecimal getAllotOutInsideCount() {
        return allotOutInsideCount;
    }

    public void setAllotOutInsideCount(BigDecimal allotOutInsideCount) {
        this.allotOutInsideCount = allotOutInsideCount;
    }

    public BigDecimal getAllotOutInsideConvertPieceCount() {
        return allotOutInsideConvertPieceCount;
    }

    public void setAllotOutInsideConvertPieceCount(BigDecimal allotOutInsideConvertPieceCount) {
        this.allotOutInsideConvertPieceCount = allotOutInsideConvertPieceCount;
    }

    public BigDecimal getAllotOutInsidePackageCount() {
        return allotOutInsidePackageCount;
    }

    public void setAllotOutInsidePackageCount(BigDecimal allotOutInsidePackageCount) {
        this.allotOutInsidePackageCount = allotOutInsidePackageCount;
    }

    public BigDecimal getAllotOutInsidePieceCount() {
        return allotOutInsidePieceCount;
    }

    public void setAllotOutInsidePieceCount(BigDecimal allotOutInsidePieceCount) {
        this.allotOutInsidePieceCount = allotOutInsidePieceCount;
    }

    public BigDecimal getAllotOutInsideCostExcludeTax() {
        return allotOutInsideCostExcludeTax;
    }

    public void setAllotOutInsideCostExcludeTax(BigDecimal allotOutInsideCostExcludeTax) {
        this.allotOutInsideCostExcludeTax = allotOutInsideCostExcludeTax;
    }

    public BigDecimal getAllotOutInsideCost() {
        return allotOutInsideCost;
    }

    public void setAllotOutInsideCost(BigDecimal allotOutInsideCost) {
        this.allotOutInsideCost = allotOutInsideCost;
    }

    public BigDecimal getAllotOutInsidePriceExcludeTax() {
        return allotOutInsidePriceExcludeTax;
    }

    public void setAllotOutInsidePriceExcludeTax(BigDecimal allotOutInsidePriceExcludeTax) {
        this.allotOutInsidePriceExcludeTax = allotOutInsidePriceExcludeTax;
    }

    public BigDecimal getAllotOutInsidePrice() {
        return allotOutInsidePrice;
    }

    public void setAllotOutInsidePrice(BigDecimal allotOutInsidePrice) {
        this.allotOutInsidePrice = allotOutInsidePrice;
    }

    public BigDecimal getDamagedOutCount() {
        return damagedOutCount;
    }

    public void setDamagedOutCount(BigDecimal damagedOutCount) {
        this.damagedOutCount = damagedOutCount;
    }

    public BigDecimal getDamagedOutConvertPieceCount() {
        return damagedOutConvertPieceCount;
    }

    public void setDamagedOutConvertPieceCount(BigDecimal damagedOutConvertPieceCount) {
        this.damagedOutConvertPieceCount = damagedOutConvertPieceCount;
    }

    public BigDecimal getDamagedOutPackageCount() {
        return damagedOutPackageCount;
    }

    public void setDamagedOutPackageCount(BigDecimal damagedOutPackageCount) {
        this.damagedOutPackageCount = damagedOutPackageCount;
    }

    public BigDecimal getDamagedOutPieceCount() {
        return damagedOutPieceCount;
    }

    public void setDamagedOutPieceCount(BigDecimal damagedOutPieceCount) {
        this.damagedOutPieceCount = damagedOutPieceCount;
    }

    public BigDecimal getDamagedOutCostExcludeTax() {
        return damagedOutCostExcludeTax;
    }

    public void setDamagedOutCostExcludeTax(BigDecimal damagedOutCostExcludeTax) {
        this.damagedOutCostExcludeTax = damagedOutCostExcludeTax;
    }

    public BigDecimal getDamagedOutCost() {
        return damagedOutCost;
    }

    public void setDamagedOutCost(BigDecimal damagedOutCost) {
        this.damagedOutCost = damagedOutCost;
    }

    public BigDecimal getDamagedOutPriceExcludeTax() {
        return damagedOutPriceExcludeTax;
    }

    public void setDamagedOutPriceExcludeTax(BigDecimal damagedOutPriceExcludeTax) {
        this.damagedOutPriceExcludeTax = damagedOutPriceExcludeTax;
    }

    public BigDecimal getDamagedOutPrice() {
        return damagedOutPrice;
    }

    public void setDamagedOutPrice(BigDecimal damagedOutPrice) {
        this.damagedOutPrice = damagedOutPrice;
    }

    public BigDecimal getOutDepartmentConsumptionCount() {
        return outDepartmentConsumptionCount;
    }

    public void setOutDepartmentConsumptionCount(BigDecimal outDepartmentConsumptionCount) {
        this.outDepartmentConsumptionCount = outDepartmentConsumptionCount;
    }

    public BigDecimal getOutDepartmentConsumptionConvertPieceCount() {
        return outDepartmentConsumptionConvertPieceCount;
    }

    public void setOutDepartmentConsumptionConvertPieceCount(BigDecimal outDepartmentConsumptionConvertPieceCount) {
        this.outDepartmentConsumptionConvertPieceCount = outDepartmentConsumptionConvertPieceCount;
    }

    public BigDecimal getOutDepartmentConsumptionPackageCount() {
        return outDepartmentConsumptionPackageCount;
    }

    public void setOutDepartmentConsumptionPackageCount(BigDecimal outDepartmentConsumptionPackageCount) {
        this.outDepartmentConsumptionPackageCount = outDepartmentConsumptionPackageCount;
    }

    public BigDecimal getOutDepartmentConsumptionPieceCount() {
        return outDepartmentConsumptionPieceCount;
    }

    public void setOutDepartmentConsumptionPieceCount(BigDecimal outDepartmentConsumptionPieceCount) {
        this.outDepartmentConsumptionPieceCount = outDepartmentConsumptionPieceCount;
    }

    public BigDecimal getOutDepartmentConsumptionCostAmount() {
        return outDepartmentConsumptionCostAmount;
    }

    public void setOutDepartmentConsumptionCostAmount(BigDecimal outDepartmentConsumptionCostAmount) {
        this.outDepartmentConsumptionCostAmount = outDepartmentConsumptionCostAmount;
    }

    public BigDecimal getOutDepartmentConsumptionCostAmountExcludingTax() {
        return outDepartmentConsumptionCostAmountExcludingTax;
    }

    public void setOutDepartmentConsumptionCostAmountExcludingTax(BigDecimal outDepartmentConsumptionCostAmountExcludingTax) {
        this.outDepartmentConsumptionCostAmountExcludingTax = outDepartmentConsumptionCostAmountExcludingTax;
    }

    public BigDecimal getOutDepartmentConsumptionAmount() {
        return outDepartmentConsumptionAmount;
    }

    public void setOutDepartmentConsumptionAmount(BigDecimal outDepartmentConsumptionAmount) {
        this.outDepartmentConsumptionAmount = outDepartmentConsumptionAmount;
    }

    public BigDecimal getOutDepartmentConsumptionAmountExcludingTax() {
        return outDepartmentConsumptionAmountExcludingTax;
    }

    public void setOutDepartmentConsumptionAmountExcludingTax(BigDecimal outDepartmentConsumptionAmountExcludingTax) {
        this.outDepartmentConsumptionAmountExcludingTax = outDepartmentConsumptionAmountExcludingTax;
    }

    public BigDecimal getOutOtherCount() {
        return outOtherCount;
    }

    public void setOutOtherCount(BigDecimal outOtherCount) {
        this.outOtherCount = outOtherCount;
    }

    public BigDecimal getOutOtherConvertPieceCount() {
        return outOtherConvertPieceCount;
    }

    public void setOutOtherConvertPieceCount(BigDecimal outOtherConvertPieceCount) {
        this.outOtherConvertPieceCount = outOtherConvertPieceCount;
    }

    public BigDecimal getOutOtherPackageCount() {
        return outOtherPackageCount;
    }

    public void setOutOtherPackageCount(BigDecimal outOtherPackageCount) {
        this.outOtherPackageCount = outOtherPackageCount;
    }

    public BigDecimal getOutOtherPieceCount() {
        return outOtherPieceCount;
    }

    public void setOutOtherPieceCount(BigDecimal outOtherPieceCount) {
        this.outOtherPieceCount = outOtherPieceCount;
    }

    public BigDecimal getOutOtherCostAmount() {
        return outOtherCostAmount;
    }

    public void setOutOtherCostAmount(BigDecimal outOtherCostAmount) {
        this.outOtherCostAmount = outOtherCostAmount;
    }

    public BigDecimal getOutOtherCostAmountExcludingTax() {
        return outOtherCostAmountExcludingTax;
    }

    public void setOutOtherCostAmountExcludingTax(BigDecimal outOtherCostAmountExcludingTax) {
        this.outOtherCostAmountExcludingTax = outOtherCostAmountExcludingTax;
    }

    public BigDecimal getOutOtherAmount() {
        return outOtherAmount;
    }

    public void setOutOtherAmount(BigDecimal outOtherAmount) {
        this.outOtherAmount = outOtherAmount;
    }

    public BigDecimal getOutOtherAmountExcludingTax() {
        return outOtherAmountExcludingTax;
    }

    public void setOutOtherAmountExcludingTax(BigDecimal outOtherAmountExcludingTax) {
        this.outOtherAmountExcludingTax = outOtherAmountExcludingTax;
    }

    public BigDecimal getCheckOutCount() {
        return checkOutCount;
    }

    public void setCheckOutCount(BigDecimal checkOutCount) {
        this.checkOutCount = checkOutCount;
    }

    public BigDecimal getCheckOutConvertPieceCount() {
        return checkOutConvertPieceCount;
    }

    public void setCheckOutConvertPieceCount(BigDecimal checkOutConvertPieceCount) {
        this.checkOutConvertPieceCount = checkOutConvertPieceCount;
    }

    public BigDecimal getCheckOutPackageCount() {
        return checkOutPackageCount;
    }

    public void setCheckOutPackageCount(BigDecimal checkOutPackageCount) {
        this.checkOutPackageCount = checkOutPackageCount;
    }

    public BigDecimal getCheckOutPieceCount() {
        return checkOutPieceCount;
    }

    public void setCheckOutPieceCount(BigDecimal checkOutPieceCount) {
        this.checkOutPieceCount = checkOutPieceCount;
    }

    public BigDecimal getCheckOutPriceExcludeTax() {
        return checkOutPriceExcludeTax;
    }

    public void setCheckOutPriceExcludeTax(BigDecimal checkOutPriceExcludeTax) {
        this.checkOutPriceExcludeTax = checkOutPriceExcludeTax;
    }

    public BigDecimal getCheckOutPrice() {
        return checkOutPrice;
    }

    public void setCheckOutPrice(BigDecimal checkOutPrice) {
        this.checkOutPrice = checkOutPrice;
    }

    public BigDecimal getCheckOutCostExcludeTax() {
        return checkOutCostExcludeTax;
    }

    public void setCheckOutCostExcludeTax(BigDecimal checkOutCostExcludeTax) {
        this.checkOutCostExcludeTax = checkOutCostExcludeTax;
    }

    public BigDecimal getCheckOutCost() {
        return checkOutCost;
    }

    public void setCheckOutCost(BigDecimal checkOutCost) {
        this.checkOutCost = checkOutCost;
    }

    public BigDecimal getProductionOutCount() {
        return productionOutCount;
    }

    public void setProductionOutCount(BigDecimal productionOutCount) {
        this.productionOutCount = productionOutCount;
    }

    public BigDecimal getProductionOutConvertPieceCount() {
        return productionOutConvertPieceCount;
    }

    public void setProductionOutConvertPieceCount(BigDecimal productionOutConvertPieceCount) {
        this.productionOutConvertPieceCount = productionOutConvertPieceCount;
    }

    public BigDecimal getProductionOutPackageCount() {
        return productionOutPackageCount;
    }

    public void setProductionOutPackageCount(BigDecimal productionOutPackageCount) {
        this.productionOutPackageCount = productionOutPackageCount;
    }

    public BigDecimal getProductionOutPieceCount() {
        return productionOutPieceCount;
    }

    public void setProductionOutPieceCount(BigDecimal productionOutPieceCount) {
        this.productionOutPieceCount = productionOutPieceCount;
    }

    public BigDecimal getProductionOutPriceExcludeTax() {
        return productionOutPriceExcludeTax;
    }

    public void setProductionOutPriceExcludeTax(BigDecimal productionOutPriceExcludeTax) {
        this.productionOutPriceExcludeTax = productionOutPriceExcludeTax;
    }

    public BigDecimal getProductionOutPrice() {
        return productionOutPrice;
    }

    public void setProductionOutPrice(BigDecimal productionOutPrice) {
        this.productionOutPrice = productionOutPrice;
    }

    public BigDecimal getProductionOutCostExcludeTax() {
        return productionOutCostExcludeTax;
    }

    public void setProductionOutCostExcludeTax(BigDecimal productionOutCostExcludeTax) {
        this.productionOutCostExcludeTax = productionOutCostExcludeTax;
    }

    public BigDecimal getProductionOutCost() {
        return productionOutCost;
    }

    public void setProductionOutCost(BigDecimal productionOutCost) {
        this.productionOutCost = productionOutCost;
    }

    public BigDecimal getOutTotalCount() {
        return outTotalCount;
    }

    public void setOutTotalCount(BigDecimal outTotalCount) {
        this.outTotalCount = outTotalCount;
    }

    public BigDecimal getOutTotalConvertPieceCount() {
        return outTotalConvertPieceCount;
    }

    public void setOutTotalConvertPieceCount(BigDecimal outTotalConvertPieceCount) {
        this.outTotalConvertPieceCount = outTotalConvertPieceCount;
    }

    public BigDecimal getOutTotalPackageCount() {
        return outTotalPackageCount;
    }

    public void setOutTotalPackageCount(BigDecimal outTotalPackageCount) {
        this.outTotalPackageCount = outTotalPackageCount;
    }

    public BigDecimal getOutTotalPieceCount() {
        return outTotalPieceCount;
    }

    public void setOutTotalPieceCount(BigDecimal outTotalPieceCount) {
        this.outTotalPieceCount = outTotalPieceCount;
    }

    public BigDecimal getOutTotalPriceExcludeTax() {
        return outTotalPriceExcludeTax;
    }

    public void setOutTotalPriceExcludeTax(BigDecimal outTotalPriceExcludeTax) {
        this.outTotalPriceExcludeTax = outTotalPriceExcludeTax;
    }

    public BigDecimal getOutTotalPrice() {
        return outTotalPrice;
    }

    public void setOutTotalPrice(BigDecimal outTotalPrice) {
        this.outTotalPrice = outTotalPrice;
    }

    public BigDecimal getOutTotalCostExcludeTax() {
        return outTotalCostExcludeTax;
    }

    public void setOutTotalCostExcludeTax(BigDecimal outTotalCostExcludeTax) {
        this.outTotalCostExcludeTax = outTotalCostExcludeTax;
    }

    public BigDecimal getOutTotalCost() {
        return outTotalCost;
    }

    public void setOutTotalCost(BigDecimal outTotalCost) {
        this.outTotalCost = outTotalCost;
    }

    public BigDecimal getBeforeInTaxRat() {
        return beforeInTaxRat;
    }

    public void setBeforeInTaxRat(BigDecimal beforeInTaxRat) {
        this.beforeInTaxRat = beforeInTaxRat;
    }

    public BigDecimal getBeforeOutTaxRat() {
        return beforeOutTaxRat;
    }

    public void setBeforeOutTaxRat(BigDecimal beforeOutTaxRat) {
        this.beforeOutTaxRat = beforeOutTaxRat;
    }

    public BigDecimal getAfterTotalCostModifyExcludeTax() {
        return afterTotalCostModifyExcludeTax;
    }

    public void setAfterTotalCostModifyExcludeTax(BigDecimal afterTotalCostModifyExcludeTax) {
        this.afterTotalCostModifyExcludeTax = afterTotalCostModifyExcludeTax;
    }

    public BigDecimal getGoodsAfterTotalCostModifyExcludeTax() {
        return goodsAfterTotalCostModifyExcludeTax;
    }

    public void setGoodsAfterTotalCostModifyExcludeTax(BigDecimal goodsAfterTotalCostModifyExcludeTax) {
        this.goodsAfterTotalCostModifyExcludeTax = goodsAfterTotalCostModifyExcludeTax;
    }

    public BigDecimal getGoodsPharmacyAfterTotalCostModifyExcludeTax() {
        return goodsPharmacyAfterTotalCostModifyExcludeTax;
    }

    public void setGoodsPharmacyAfterTotalCostModifyExcludeTax(BigDecimal goodsPharmacyAfterTotalCostModifyExcludeTax) {
        this.goodsPharmacyAfterTotalCostModifyExcludeTax = goodsPharmacyAfterTotalCostModifyExcludeTax;
    }

    public String getMedicineNmpn() {
        return medicineNmpn;
    }

    public void setMedicineNmpn(String medicineNmpn) {
        this.medicineNmpn = medicineNmpn;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getDeliveryOutCount() {
        return deliveryOutCount;
    }

    public void setDeliveryOutCount(BigDecimal deliveryOutCount) {
        this.deliveryOutCount = deliveryOutCount;
    }

    public BigDecimal getDeliveryOutConvertPieceCount() {
        return deliveryOutConvertPieceCount;
    }

    public void setDeliveryOutConvertPieceCount(BigDecimal deliveryOutConvertPieceCount) {
        this.deliveryOutConvertPieceCount = deliveryOutConvertPieceCount;
    }

    public BigDecimal getDeliveryOutPackageCount() {
        return deliveryOutPackageCount;
    }

    public void setDeliveryOutPackageCount(BigDecimal deliveryOutPackageCount) {
        this.deliveryOutPackageCount = deliveryOutPackageCount;
    }

    public BigDecimal getDeliveryOutPieceCount() {
        return deliveryOutPieceCount;
    }

    public void setDeliveryOutPieceCount(BigDecimal deliveryOutPieceCount) {
        this.deliveryOutPieceCount = deliveryOutPieceCount;
    }

    public BigDecimal getDeliveryOutPriceExcludeTax() {
        return deliveryOutPriceExcludeTax;
    }

    public void setDeliveryOutPriceExcludeTax(BigDecimal deliveryOutPriceExcludeTax) {
        this.deliveryOutPriceExcludeTax = deliveryOutPriceExcludeTax;
    }

    public BigDecimal getDeliveryOutPrice() {
        return deliveryOutPrice;
    }

    public void setDeliveryOutPrice(BigDecimal deliveryOutPrice) {
        this.deliveryOutPrice = deliveryOutPrice;
    }

    public BigDecimal getDeliveryOutCostExcludeTax() {
        return deliveryOutCostExcludeTax;
    }

    public void setDeliveryOutCostExcludeTax(BigDecimal deliveryOutCostExcludeTax) {
        this.deliveryOutCostExcludeTax = deliveryOutCostExcludeTax;
    }

    public BigDecimal getDeliveryOutCost() {
        return deliveryOutCost;
    }

    public void setDeliveryOutCost(BigDecimal deliveryOutCost) {
        this.deliveryOutCost = deliveryOutCost;
    }

    public String getDeliveryOutCountText() {
        return deliveryOutCountText;
    }

    public void setDeliveryOutCountText(String deliveryOutCountText) {
        this.deliveryOutCountText = deliveryOutCountText;
    }

    public String getDeliveryOutConvertPieceCountText() {
        return deliveryOutConvertPieceCountText;
    }

    public void setDeliveryOutConvertPieceCountText(String deliveryOutConvertPieceCountText) {
        this.deliveryOutConvertPieceCountText = deliveryOutConvertPieceCountText;
    }

    public String getDeliveryOutPackageCountText() {
        return deliveryOutPackageCountText;
    }

    public void setDeliveryOutPackageCountText(String deliveryOutPackageCountText) {
        this.deliveryOutPackageCountText = deliveryOutPackageCountText;
    }

    public String getDeliveryOutPieceCountText() {
        return deliveryOutPieceCountText;
    }

    public void setDeliveryOutPieceCountText(String deliveryOutPieceCountText) {
        this.deliveryOutPieceCountText = deliveryOutPieceCountText;
    }

    public String getDeliveryOutPriceExcludeTaxText() {
        return deliveryOutPriceExcludeTaxText;
    }

    public void setDeliveryOutPriceExcludeTaxText(String deliveryOutPriceExcludeTaxText) {
        this.deliveryOutPriceExcludeTaxText = deliveryOutPriceExcludeTaxText;
    }

    public String getDeliveryOutPriceText() {
        return deliveryOutPriceText;
    }

    public void setDeliveryOutPriceText(String deliveryOutPriceText) {
        this.deliveryOutPriceText = deliveryOutPriceText;
    }

    public String getDeliveryOutCostExcludeTaxText() {
        return deliveryOutCostExcludeTaxText;
    }

    public void setDeliveryOutCostExcludeTaxText(String deliveryOutCostExcludeTaxText) {
        this.deliveryOutCostExcludeTaxText = deliveryOutCostExcludeTaxText;
    }

    public String getDeliveryOutCostText() {
        return deliveryOutCostText;
    }

    public void setDeliveryOutCostText(String deliveryOutCostText) {
        this.deliveryOutCostText = deliveryOutCostText;
    }

    public BigDecimal getReturnGoodsOutCount() {
        return returnGoodsOutCount;
    }

    public void setReturnGoodsOutCount(BigDecimal returnGoodsOutCount) {
        this.returnGoodsOutCount = returnGoodsOutCount;
    }

    public BigDecimal getReturnGoodsOutConvertPieceCount() {
        return returnGoodsOutConvertPieceCount;
    }

    public void setReturnGoodsOutConvertPieceCount(BigDecimal returnGoodsOutConvertPieceCount) {
        this.returnGoodsOutConvertPieceCount = returnGoodsOutConvertPieceCount;
    }

    public BigDecimal getReturnGoodsOutPackageCount() {
        return returnGoodsOutPackageCount;
    }

    public void setReturnGoodsOutPackageCount(BigDecimal returnGoodsOutPackageCount) {
        this.returnGoodsOutPackageCount = returnGoodsOutPackageCount;
    }

    public BigDecimal getReturnGoodsOutPieceCount() {
        return returnGoodsOutPieceCount;
    }

    public void setReturnGoodsOutPieceCount(BigDecimal returnGoodsOutPieceCount) {
        this.returnGoodsOutPieceCount = returnGoodsOutPieceCount;
    }

    public BigDecimal getReturnGoodsOutCostExcludeTax() {
        return returnGoodsOutCostExcludeTax;
    }

    public void setReturnGoodsOutCostExcludeTax(BigDecimal returnGoodsOutCostExcludeTax) {
        this.returnGoodsOutCostExcludeTax = returnGoodsOutCostExcludeTax;
    }

    public BigDecimal getReturnGoodsOutCost() {
        return returnGoodsOutCost;
    }

    public void setReturnGoodsOutCost(BigDecimal returnGoodsOutCost) {
        this.returnGoodsOutCost = returnGoodsOutCost;
    }

    public BigDecimal getReturnGoodsOutPriceExcludeTax() {
        return returnGoodsOutPriceExcludeTax;
    }

    public void setReturnGoodsOutPriceExcludeTax(BigDecimal returnGoodsOutPriceExcludeTax) {
        this.returnGoodsOutPriceExcludeTax = returnGoodsOutPriceExcludeTax;
    }

    public BigDecimal getReturnGoodsOutPrice() {
        return returnGoodsOutPrice;
    }

    public void setReturnGoodsOutPrice(BigDecimal returnGoodsOutPrice) {
        this.returnGoodsOutPrice = returnGoodsOutPrice;
    }

    public String getReturnGoodsOutCountText() {
        return returnGoodsOutCountText;
    }

    public void setReturnGoodsOutCountText(String returnGoodsOutCountText) {
        this.returnGoodsOutCountText = returnGoodsOutCountText;
    }

    public String getReturnGoodsOutConvertPieceCountText() {
        return returnGoodsOutConvertPieceCountText;
    }

    public void setReturnGoodsOutConvertPieceCountText(String returnGoodsOutConvertPieceCountText) {
        this.returnGoodsOutConvertPieceCountText = returnGoodsOutConvertPieceCountText;
    }

    public String getReturnGoodsOutPackageCountText() {
        return returnGoodsOutPackageCountText;
    }

    public void setReturnGoodsOutPackageCountText(String returnGoodsOutPackageCountText) {
        this.returnGoodsOutPackageCountText = returnGoodsOutPackageCountText;
    }

    public String getReturnGoodsOutPieceCountText() {
        return returnGoodsOutPieceCountText;
    }

    public void setReturnGoodsOutPieceCountText(String returnGoodsOutPieceCountText) {
        this.returnGoodsOutPieceCountText = returnGoodsOutPieceCountText;
    }

    public String getReturnGoodsOutCostExcludeTaxText() {
        return returnGoodsOutCostExcludeTaxText;
    }

    public void setReturnGoodsOutCostExcludeTaxText(String returnGoodsOutCostExcludeTaxText) {
        this.returnGoodsOutCostExcludeTaxText = returnGoodsOutCostExcludeTaxText;
    }

    public String getReturnGoodsOutCostText() {
        return returnGoodsOutCostText;
    }

    public void setReturnGoodsOutCostText(String returnGoodsOutCostText) {
        this.returnGoodsOutCostText = returnGoodsOutCostText;
    }

    public String getReturnGoodsOutPriceExcludeTaxText() {
        return returnGoodsOutPriceExcludeTaxText;
    }

    public void setReturnGoodsOutPriceExcludeTaxText(String returnGoodsOutPriceExcludeTaxText) {
        this.returnGoodsOutPriceExcludeTaxText = returnGoodsOutPriceExcludeTaxText;
    }

    public String getReturnGoodsOutPriceText() {
        return returnGoodsOutPriceText;
    }

    public void setReturnGoodsOutPriceText(String returnGoodsOutPriceText) {
        this.returnGoodsOutPriceText = returnGoodsOutPriceText;
    }

    public BigDecimal getInInitReturnCount() {
        return inInitReturnCount;
    }

    public void setInInitReturnCount(BigDecimal inInitReturnCount) {
        this.inInitReturnCount = inInitReturnCount;
    }

    public BigDecimal getInInitReturnConvertPieceCount() {
        return inInitReturnConvertPieceCount;
    }

    public void setInInitReturnConvertPieceCount(BigDecimal inInitReturnConvertPieceCount) {
        this.inInitReturnConvertPieceCount = inInitReturnConvertPieceCount;
    }

    public BigDecimal getInInitReturnPackageCount() {
        return inInitReturnPackageCount;
    }

    public void setInInitReturnPackageCount(BigDecimal inInitReturnPackageCount) {
        this.inInitReturnPackageCount = inInitReturnPackageCount;
    }

    public BigDecimal getInInitReturnPieceCount() {
        return inInitReturnPieceCount;
    }

    public void setInInitReturnPieceCount(BigDecimal inInitReturnPieceCount) {
        this.inInitReturnPieceCount = inInitReturnPieceCount;
    }

    public BigDecimal getInInitReturnCostExcludeTax() {
        return inInitReturnCostExcludeTax;
    }

    public void setInInitReturnCostExcludeTax(BigDecimal inInitReturnCostExcludeTax) {
        this.inInitReturnCostExcludeTax = inInitReturnCostExcludeTax;
    }

    public BigDecimal getInInitReturnCost() {
        return inInitReturnCost;
    }

    public void setInInitReturnCost(BigDecimal inInitReturnCost) {
        this.inInitReturnCost = inInitReturnCost;
    }

    public BigDecimal getInInitReturnPriceExcludeTax() {
        return inInitReturnPriceExcludeTax;
    }

    public void setInInitReturnPriceExcludeTax(BigDecimal inInitReturnPriceExcludeTax) {
        this.inInitReturnPriceExcludeTax = inInitReturnPriceExcludeTax;
    }

    public BigDecimal getInInitReturnPrice() {
        return inInitReturnPrice;
    }

    public void setInInitReturnPrice(BigDecimal inInitReturnPrice) {
        this.inInitReturnPrice = inInitReturnPrice;
    }

    public String getInInitReturnCountText() {
        return inInitReturnCountText;
    }

    public void setInInitReturnCountText(String inInitReturnCountText) {
        this.inInitReturnCountText = inInitReturnCountText;
    }

    public String getInInitReturnConvertPieceCountText() {
        return inInitReturnConvertPieceCountText;
    }

    public void setInInitReturnConvertPieceCountText(String inInitReturnConvertPieceCountText) {
        this.inInitReturnConvertPieceCountText = inInitReturnConvertPieceCountText;
    }

    public String getInInitReturnPackageCountText() {
        return inInitReturnPackageCountText;
    }

    public void setInInitReturnPackageCountText(String inInitReturnPackageCountText) {
        this.inInitReturnPackageCountText = inInitReturnPackageCountText;
    }

    public String getInInitReturnPieceCountText() {
        return inInitReturnPieceCountText;
    }

    public void setInInitReturnPieceCountText(String inInitReturnPieceCountText) {
        this.inInitReturnPieceCountText = inInitReturnPieceCountText;
    }

    public String getInInitReturnCostExcludeTaxText() {
        return inInitReturnCostExcludeTaxText;
    }

    public void setInInitReturnCostExcludeTaxText(String inInitReturnCostExcludeTaxText) {
        this.inInitReturnCostExcludeTaxText = inInitReturnCostExcludeTaxText;
    }

    public String getInInitReturnCostText() {
        return inInitReturnCostText;
    }

    public void setInInitReturnCostText(String inInitReturnCostText) {
        this.inInitReturnCostText = inInitReturnCostText;
    }

    public String getInInitReturnPriceExcludeTaxText() {
        return inInitReturnPriceExcludeTaxText;
    }

    public void setInInitReturnPriceExcludeTaxText(String inInitReturnPriceExcludeTaxText) {
        this.inInitReturnPriceExcludeTaxText = inInitReturnPriceExcludeTaxText;
    }

    public String getInInitReturnPriceText() {
        return inInitReturnPriceText;
    }

    public void setInInitReturnPriceText(String inInitReturnPriceText) {
        this.inInitReturnPriceText = inInitReturnPriceText;
    }

    public String getDosage() {
        return dosage;
    }

    public void setDosage(String dosage) {
        this.dosage = dosage;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getMinimumPricingUnit() {
        return minimumPricingUnit;
    }

    public void setMinimumPricingUnit(String minimumPricingUnit) {
        this.minimumPricingUnit = minimumPricingUnit;
    }
}
