package cn.abc.flink.stat.service.cis.achievement.execute.domain;



import java.math.BigDecimal;

/**
 * @description:
 * @author: dy
 * @create: 2021-12-06 16:52
 */

public class ExecuteDetailDao {
    private String chainId;
    private String executeClinicId;
    private String sheetClinicId;
    private String id;
    private Byte status;
    private String created;
    private String chargeFormItemId;
    private String chargeSheetId;
    private String productId;   //用法和goods_id
    private String productName;
    private String classifyLevel1Id;
    private Integer classifyLevel2Id;
    private BigDecimal averageCount;
    private BigDecimal unitCount;
    private BigDecimal originalFee;
    private String patientId;
    private String comment;
    private String recordEffectInfo;
    private String sheetCreatedBy;
    private String sheetCreated;
    private String createdBy;
    private BigDecimal receivedPrice;
    private BigDecimal deductPrice;
    private String executorIds;
    private BigDecimal receivedFee;
    private BigDecimal deductFee;
    private String executeContent;     //执行内容
    private Integer groupId;    //分组id
    /**
     * 首诊来源 一级 id
     */
    private String patientSourceId1;
    /**
     * 首诊来源 二级 id
     */
    private String patientSourceId2;
    private Integer patientSourceFromType;
    private String patientSourceFrom;

    /**
     * 所属套餐id
     */
    private String composeParentProductId;

    /**
     * 科室id
     */
    private String departmentId;
    /**
     * 就诊单id
     */
    private String patientOrderId;
    /**
     * 执行内容中项目数量
     */
    private String executeProductInfo;
    /**
     * 分组执行次数信息
     */
    private String groupCountInfo;

    public String getChainId() {
        return this.chainId;
    }

    public String getExecuteClinicId() {
        return this.executeClinicId;
    }

    public String getSheetClinicId() {
        return this.sheetClinicId;
    }

    public String getId() {
        return this.id;
    }

    public Byte getStatus() {
        return this.status;
    }

    public String getCreated() {
        return this.created;
    }

    public String getChargeFormItemId() {
        return this.chargeFormItemId;
    }

    public String getChargeSheetId() {
        return this.chargeSheetId;
    }

    public String getProductId() {
        return this.productId;
    }

    public String getProductName() {
        return this.productName;
    }

    public String getClassifyLevel1Id() {
        return this.classifyLevel1Id;
    }

    public Integer getClassifyLevel2Id() {
        return this.classifyLevel2Id;
    }

    public BigDecimal getAverageCount() {
        return this.averageCount;
    }

    public BigDecimal getUnitCount() {
        return this.unitCount;
    }

    public BigDecimal getOriginalFee() {
        return this.originalFee;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getComment() {
        return this.comment;
    }

    public String getRecordEffectInfo() {
        return this.recordEffectInfo;
    }

    public String getSheetCreatedBy() {
        return this.sheetCreatedBy;
    }

    public String getSheetCreated() {
        return this.sheetCreated;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public BigDecimal getReceivedPrice() {
        return this.receivedPrice;
    }

    public BigDecimal getDeductPrice() {
        return this.deductPrice;
    }

    public String getExecutorIds() {
        return this.executorIds;
    }

    public BigDecimal getReceivedFee() {
        return this.receivedFee;
    }

    public BigDecimal getDeductFee() {
        return this.deductFee;
    }

    public String getExecuteContent() {
        return this.executeContent;
    }

    public Integer getGroupId() {
        return this.groupId;
    }

    public String getPatientSourceId1() {
        return this.patientSourceId1;
    }

    public String getPatientSourceId2() {
        return this.patientSourceId2;
    }

    public Integer getPatientSourceFromType() {
        return this.patientSourceFromType;
    }

    public String getPatientSourceFrom() {
        return this.patientSourceFrom;
    }

    public String getComposeParentProductId() {
        return this.composeParentProductId;
    }

    public String getDepartmentId() {
        return this.departmentId;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setExecuteClinicId(String executeClinicId) {
        this.executeClinicId = executeClinicId;
    }

    public void setSheetClinicId(String sheetClinicId) {
        this.sheetClinicId = sheetClinicId;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public void setChargeFormItemId(String chargeFormItemId) {
        this.chargeFormItemId = chargeFormItemId;
    }

    public void setChargeSheetId(String chargeSheetId) {
        this.chargeSheetId = chargeSheetId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public void setClassifyLevel1Id(String classifyLevel1Id) {
        this.classifyLevel1Id = classifyLevel1Id;
    }

    public void setClassifyLevel2Id(Integer classifyLevel2Id) {
        this.classifyLevel2Id = classifyLevel2Id;
    }

    public void setAverageCount(BigDecimal averageCount) {
        this.averageCount = averageCount;
    }

    public void setUnitCount(BigDecimal unitCount) {
        this.unitCount = unitCount;
    }

    public void setOriginalFee(BigDecimal originalFee) {
        this.originalFee = originalFee;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public void setRecordEffectInfo(String recordEffectInfo) {
        this.recordEffectInfo = recordEffectInfo;
    }

    public void setSheetCreatedBy(String sheetCreatedBy) {
        this.sheetCreatedBy = sheetCreatedBy;
    }

    public void setSheetCreated(String sheetCreated) {
        this.sheetCreated = sheetCreated;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setReceivedPrice(BigDecimal receivedPrice) {
        this.receivedPrice = receivedPrice;
    }

    public void setDeductPrice(BigDecimal deductPrice) {
        this.deductPrice = deductPrice;
    }

    public void setExecutorIds(String executorIds) {
        this.executorIds = executorIds;
    }

    public void setReceivedFee(BigDecimal receivedFee) {
        this.receivedFee = receivedFee;
    }

    public void setDeductFee(BigDecimal deductFee) {
        this.deductFee = deductFee;
    }

    public void setExecuteContent(String executeContent) {
        this.executeContent = executeContent;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public void setPatientSourceId1(String patientSourceId1) {
        this.patientSourceId1 = patientSourceId1;
    }

    public void setPatientSourceId2(String patientSourceId2) {
        this.patientSourceId2 = patientSourceId2;
    }

    public void setPatientSourceFromType(Integer patientSourceFromType) {
        this.patientSourceFromType = patientSourceFromType;
    }

    public void setPatientSourceFrom(String patientSourceFrom) {
        this.patientSourceFrom = patientSourceFrom;
    }

    public void setComposeParentProductId(String composeParentProductId) {
        this.composeParentProductId = composeParentProductId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public String getPatientOrderId() {
        return patientOrderId;
    }

    public void setPatientOrderId(String patientOrderId) {
        this.patientOrderId = patientOrderId;
    }

    public String getExecuteProductInfo() {
        return executeProductInfo;
    }

    public void setExecuteProductInfo(String executeProductInfo) {
        this.executeProductInfo = executeProductInfo;
    }

    public String getGroupCountInfo() {
        return groupCountInfo;
    }

    public void setGroupCountInfo(String groupCountInfo) {
        this.groupCountInfo = groupCountInfo;
    }
}
