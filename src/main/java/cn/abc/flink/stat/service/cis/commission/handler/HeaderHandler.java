package cn.abc.flink.stat.service.cis.commission.handler;

import cn.abc.flink.stat.common.contants.CommonConstants;
import cn.abc.flink.stat.common.response.StatResponseHeaderItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/22 15:04
 */
public class HeaderHandler {

    /**
     * @param
     * @param headerList -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.response.StatResponseHeaderItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 15:05
     */
    public static List<StatResponseHeaderItem> getRegistrationDetailHeader(List<StatResponseHeaderItem> headerList) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "患者", "patientName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "性别", "patientSex", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "手机号", "patientMobile", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY_FIVE));
        headerList.add(new StatResponseHeaderItem(
                "渠道", "typeText", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "就诊推荐", "visitSourceFromName", "left", CommonConstants.NUMBER_NINETY, "recommendPopover"));
        headerList.add(new StatResponseHeaderItem(
                "就诊推荐备注", "visitSourceRemark", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "初诊/复诊", "revisitStatusText", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "科室", "departmentName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "号数", "orderNoStr", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "状态", "statusText", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "原价金额", "fee", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "实收金额", "received", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利", "gross", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "成本", "costPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "收费员", "cashierName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "创建人", "createdByName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "挂号创建时间", "created", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        headerList.add(new StatResponseHeaderItem(
                "预计就诊时间", "orderNoReserveStart4Export", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        return headerList;
    }

    /**
     * @param headerList -
     * @param hisType    -
     * @return -
     */
    public static List<StatResponseHeaderItem> getSaleDetailHeader(List<StatResponseHeaderItem> headerList,
                                                                   String hisType) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY, true));
        headerList.add(new StatResponseHeaderItem(
                "提成类型", "commissionTypeName", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY, true));
        headerList.add(new StatResponseHeaderItem(
                "一级分类", "feeType1", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "二级分类", "feeType2", "left", CommonConstants.NUMBER_NINETY));
        if (CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(hisType)) {
            headerList.add(new StatResponseHeaderItem(
                    "医嘱", "name", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY));
            headerList.add(new StatResponseHeaderItem(
                    "医嘱编码", "shortId", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY));
        } else if (CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(hisType)) {
            headerList.add(new StatResponseHeaderItem(
                    "商品", "name", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY));
            headerList.add(new StatResponseHeaderItem(
                    "商品编码", "shortId", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY));
        } else {
            headerList.add(new StatResponseHeaderItem(
                    "项目", "name", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY));
            headerList.add(new StatResponseHeaderItem(
                    "项目编码", "shortId", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY));
        }
        headerList.add(new StatResponseHeaderItem(
                "厂家", "manufacturer", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "规格", "spec", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "单位", "unit", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "数量", "count", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "原价金额", "originPrice", "money","right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "实收金额", "amount", "money","right", CommonConstants.NUMBER_NINETY));
        if (!CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(hisType)) {
            headerList.add(new StatResponseHeaderItem(
                    "抵扣金额", "deductPrice", "money","right", CommonConstants.NUMBER_NINETY));
        }
        headerList.add(new StatResponseHeaderItem(
                "成本", "costPrice", "money","right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利", "gross", "money","right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利率", "profitText", "right", CommonConstants.NUMBER_NINETY));
        if (!CisJWTUtils.CIS_HIS_TYPE_PHARMACY.equals(hisType)) {
            headerList.add(new StatResponseHeaderItem(
                    "处方数", "prescriptionNumText", "right", CommonConstants.NUMBER_NINETY));
            headerList.add(new StatResponseHeaderItem(
                    "门诊单数", "patientOrderNumText", "right", CommonConstants.NUMBER_NINETY));
        }
        if (CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(hisType)) {
            headerList.add(new StatResponseHeaderItem(
                    "来源", "source", "right", CommonConstants.NUMBER_NINETY));
        }
        return headerList;
    }

    /**
     * @param headerList -
     * @return -
     */
    public static List<StatResponseHeaderItem> getSaleFeeTypeDetailHeader(List<StatResponseHeaderItem> headerList) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY, true));
        headerList.add(new StatResponseHeaderItem(
                "提成类型", "commissionTypeName", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY, true));
        headerList.add(new StatResponseHeaderItem(
                "费用项分类", "feeTypeName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "费用项", "name", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY));
        headerList.add(new StatResponseHeaderItem(
                "费用项编码", "shortId", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY));
//        headerList.add(new StatResponseHeaderItem(
//                "厂家", "manufacturer", "left", CommonConstants.NUMBER_NINETY));
//        headerList.add(new StatResponseHeaderItem(
//                "规格", "spec", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "单位", "unit", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "数量", "countText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "原价金额", "originPriceText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "实收金额", "receivedPriceText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "抵扣金额", "deductPriceText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "成本", "costPriceText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利", "grossText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利率", "profitText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "来源", "source", "right", CommonConstants.NUMBER_NINETY));
        return headerList;
    }

    /**
     * @param
     * @param headerList -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.response.StatResponseHeaderItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 15:36
     */
    public static List<StatResponseHeaderItem> getTollManDetailHeader(List<StatResponseHeaderItem> headerList) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "类型", "sourceType", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "诊号", "patientOrderNo", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "患者", "patientName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "性别", "patientSex", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "手机号", "patientMobile", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY));
        headerList.add(new StatResponseHeaderItem(
                "原价金额", "originPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "实收金额", "receivedPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "开单人", "chargeName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "收费时间", "created", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        return headerList;
    }

    /**
     * @param headerList -
     * @param hisType    -
     * @return -
     */
    public static List<StatResponseHeaderItem> getDispensingDetailHeader(List<StatResponseHeaderItem> headerList,
                                                                         String hisType) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "发药单数", "dispensingNumber", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "发药种数", "typeNumber", "right", CommonConstants.NUMBER_NINETY));
        StatResponseHeaderItem western = new StatResponseHeaderItem(
                "中西成药", "western", "center", CommonConstants.NUMBER_ONE_HUNDRED_EIGHTY);
        List<StatResponseHeaderItem> westernList = new ArrayList<>();
        westernList.add(new StatResponseHeaderItem(
                "处方数", "westernPrescriptionNum", "right", CommonConstants.NUMBER_NINETY));
        westernList.add(new StatResponseHeaderItem(
                "发药种数", "westernGoodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
        westernList.add(new StatResponseHeaderItem(
                "实收金额", "westernReceivedPrice", "money","right", CommonConstants.NUMBER_NINETY));
        westernList.add(new StatResponseHeaderItem(
                "原价金额", "westernOriginalPrice", "money","right", CommonConstants.NUMBER_NINETY));
        westernList.add(new StatResponseHeaderItem(
                "成本", "westernCostPrice", "money","right", CommonConstants.NUMBER_NINETY));
        western.setColumnChildren(westernList);
        headerList.add(western);
        StatResponseHeaderItem slices = new StatResponseHeaderItem(
                "中药饮片", "slices", "center", CommonConstants.NUMBER_TWO_HUNDRED_SEVENTY);
        List<StatResponseHeaderItem> slicesList = new ArrayList<>();
        slicesList.add(new StatResponseHeaderItem(
                "处方数", "slicesPrescriptionNum", "right", CommonConstants.NUMBER_NINETY));
        slicesList.add(new StatResponseHeaderItem(
                "剂数", "slicesDose", "right", CommonConstants.NUMBER_NINETY));
        slicesList.add(new StatResponseHeaderItem(
                "味数x剂数", "slicesDoseAndUnitCount", "right", CommonConstants.NUMBER_NINETY));
        slicesList.add(new StatResponseHeaderItem(
                "实收金额", "slicesReceivedPrice", "money","right", CommonConstants.NUMBER_NINETY));
        slicesList.add(new StatResponseHeaderItem(
                "原价金额", "slicesOriginalPrice", "money","right", CommonConstants.NUMBER_NINETY));
        slicesList.add(new StatResponseHeaderItem(
                "成本", "slicesCostPrice", "money","right", CommonConstants.NUMBER_NINETY));
        slices.setColumnChildren(slicesList);
        headerList.add(slices);
        StatResponseHeaderItem granular = new StatResponseHeaderItem(
                "中药颗粒", "granular", "center", CommonConstants.NUMBER_TWO_HUNDRED_SEVENTY);
        List<StatResponseHeaderItem> granularList = new ArrayList<>();
        granularList.add(new StatResponseHeaderItem(
                "处方数", "granularPrescriptionNum", "right", CommonConstants.NUMBER_NINETY));
        granularList.add(new StatResponseHeaderItem(
                "剂数", "granularDose", "right", CommonConstants.NUMBER_NINETY));
        granularList.add(new StatResponseHeaderItem(
                "味数x剂数", "granularDoseAndUnitCount", "right", CommonConstants.NUMBER_NINETY));
        granularList.add(new StatResponseHeaderItem(
                "实收金额", "granularReceivedPrice", "money","right", CommonConstants.NUMBER_NINETY));
        granularList.add(new StatResponseHeaderItem(
                "原价金额", "granularOriginalPrice", "money","right", CommonConstants.NUMBER_NINETY));
        granularList.add(new StatResponseHeaderItem(
                "成本", "granularCostPrice", "money","right", CommonConstants.NUMBER_NINETY));
        granular.setColumnChildren(granularList);
        headerList.add(granular);
        if (CisJWTUtils.CIS_HIS_TYPE_OPHTHALMOLOGY.equals(hisType) || CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(hisType)) {
            StatResponseHeaderItem lens = new StatResponseHeaderItem(
                    "镜片", "lens", "center", CommonConstants.NUMBER_NINETY);
            List<StatResponseHeaderItem> lenList = new ArrayList<>();
            lenList.add(new StatResponseHeaderItem(
                    "发药种数", "lensGoodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
            lens.setColumnChildren(lenList);
            headerList.add(lens);
            StatResponseHeaderItem spectaclesFrame = new StatResponseHeaderItem(
                    "镜架", "spectaclesFrame", "center", CommonConstants.NUMBER_NINETY);
            List<StatResponseHeaderItem> spectaclesFrameList = new ArrayList<>();
            spectaclesFrameList.add(new StatResponseHeaderItem(
                    "发药种数", "spectaclesFrameGoodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
            spectaclesFrame.setColumnChildren(spectaclesFrameList);
            headerList.add(spectaclesFrame);
            StatResponseHeaderItem cornealShapingMirror = new StatResponseHeaderItem(
                    "角膜塑形镜", "cornealShapingMirror", "center", CommonConstants.NUMBER_NINETY);
            List<StatResponseHeaderItem> cornealShapingMirrorList = new ArrayList<>();
            cornealShapingMirrorList.add(new StatResponseHeaderItem(
                    "发药种数", "cornealShapingMirrorGoodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
            cornealShapingMirror.setColumnChildren(cornealShapingMirrorList);
            headerList.add(cornealShapingMirror);
            StatResponseHeaderItem softHydrophilicMirror = new StatResponseHeaderItem(
                    "软性亲水镜", "softHydrophilicMirror", "center", CommonConstants.NUMBER_NINETY);
            List<StatResponseHeaderItem> softHydrophilicMirrorList = new ArrayList<>();
            softHydrophilicMirrorList.add(new StatResponseHeaderItem(
                    "发药种数", "softHydrophilicMirrorGoodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
            softHydrophilicMirror.setColumnChildren(softHydrophilicMirrorList);
            headerList.add(softHydrophilicMirror);
            StatResponseHeaderItem rigidOxygenPermeableMirror = new StatResponseHeaderItem(
                    "硬性透氧镜", "rigidOxygenPermeableMirror", "center", CommonConstants.NUMBER_NINETY);
            List<StatResponseHeaderItem> rigidOxygenPermeableMirrorList = new ArrayList<>();
            rigidOxygenPermeableMirrorList.add(new StatResponseHeaderItem(
                    "发药种数", "rigidOxygenPermeableMirrorGoodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
            rigidOxygenPermeableMirror.setColumnChildren(rigidOxygenPermeableMirrorList);
            headerList.add(rigidOxygenPermeableMirror);
            StatResponseHeaderItem sunglasses = new StatResponseHeaderItem(
                    "太阳镜", "sunglasses", "center", CommonConstants.NUMBER_NINETY);
            List<StatResponseHeaderItem> sunglassesList = new ArrayList<>();
            sunglassesList.add(new StatResponseHeaderItem(
                    "发药种数", "sunglassesGoodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
            sunglasses.setColumnChildren(sunglassesList);
            headerList.add(sunglasses);
        }
        StatResponseHeaderItem material = new StatResponseHeaderItem(
                "材料商品", "material", "center", CommonConstants.NUMBER_TWO_HUNDRED_SEVENTY);
        List<StatResponseHeaderItem> materialList = new ArrayList<>();
        materialList.add(new StatResponseHeaderItem(
                "发药种数", "materialNum", "right", CommonConstants.NUMBER_NINETY));
        materialList.add(new StatResponseHeaderItem(
                "实收金额", "materialReceivedPrice", "money","right", CommonConstants.NUMBER_NINETY));
        materialList.add(new StatResponseHeaderItem(
                "原价金额", "materialOriginalPrice", "money","right", CommonConstants.NUMBER_NINETY));
        materialList.add(new StatResponseHeaderItem(
                "成本", "materialCostPrice", "money","right", CommonConstants.NUMBER_NINETY));
        material.setColumnChildren(materialList);
        headerList.add(material);
        StatResponseHeaderItem compose = new StatResponseHeaderItem(
                "套餐内项目", "compose", "center", CommonConstants.NUMBER_TWO_HUNDRED_SEVENTY);
        List<StatResponseHeaderItem> composeList = new ArrayList<>();
        composeList.add(new StatResponseHeaderItem(
                "发药种数", "goodsTypeNum", "right", CommonConstants.NUMBER_NINETY));
        composeList.add(new StatResponseHeaderItem(
                "实收金额", "composeReceivedPrice", "money","right", CommonConstants.NUMBER_NINETY));
        composeList.add(new StatResponseHeaderItem(
                "原价金额", "composeOriginalPrice","money", "right", CommonConstants.NUMBER_NINETY));
        composeList.add(new StatResponseHeaderItem(
                "成本", "composeCostPrice", "money","right", CommonConstants.NUMBER_NINETY));
        compose.setColumnChildren(composeList);
        headerList.add(compose);
        return headerList;
    }

    /**
     * @param
     * @param headerList -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.response.StatResponseHeaderItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 15:34
     */
    public static List<StatResponseHeaderItem> getReChargeDetailHeader(List<StatResponseHeaderItem> headerList) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "患者", "patientName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "性别", "patientSex", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "手机号", "patientMobile", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY_FIVE));
        headerList.add(new StatResponseHeaderItem(
                "充值对象", "rechargedName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "状态", "action", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "实收金额", "receivedPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "充值本金", "rechargePrincipal", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "充值赠金", "rechargePresent", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "操作人", "operatorName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "操作时间", "created", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        return headerList;
    }

    /**
     * @param
     * @param headerList -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.response.StatResponseHeaderItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 15:35
     */
    public static List<StatResponseHeaderItem> getExaminationDetailHeader(List<StatResponseHeaderItem> headerList) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "患者", "patientName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "性别", "patientSex", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "手机号", "patientMobile", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY_FIVE));
        headerList.add(new StatResponseHeaderItem(
                "检验/检查时间", "reportTime", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        headerList.add(new StatResponseHeaderItem(
                "一级分类", "goodsTypeName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "二级分类", "subTypeName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "项目", "goodsName", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY));
        headerList.add(new StatResponseHeaderItem(
                "次数", "countNum", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "原价金额", "originPriceText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "实收金额", "receivedPriceText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "抵扣金额", "deductPriceText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "成本", "costPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利", "grossText", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利率", "profit", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "开单门店", "clinicName", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY));
        headerList.add(new StatResponseHeaderItem(
                "开单人", "sellerName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "登记人", "createdByName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "开单时间", "created", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        return headerList;
    }

    /**
     * @param
     * @param headerList -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.response.StatResponseHeaderItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 15:35
     */
    public static List<StatResponseHeaderItem> getPromotionCardDetailHeader(List<StatResponseHeaderItem> headerList) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "患者", "patientName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "手机号", "patientMobile", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY_FIVE));
        headerList.add(new StatResponseHeaderItem(
                "卡名称", "cardName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "开卡金额", "openCardPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "操作人", "operatorName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "操作时间", "created", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY));
        return headerList;
    }


    /**
     * @param
     * @param headerList -
     * @return
     * @return java.util.List<cn.abc.flink.stat.common.response.StatResponseHeaderItem>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 15:36
     */
    public static List<StatResponseHeaderItem> getExecuteDetailHeader(List<StatResponseHeaderItem> headerList) {
        headerList.add(new StatResponseHeaderItem(
                "提成金额", "commissionAmt", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "患者", "patientName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "性别", "patientSex", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "手机号", "patientMobile", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY_FIVE));
        headerList.add(new StatResponseHeaderItem(
                "执行时间", "executeCreated", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        headerList.add(new StatResponseHeaderItem(
                "一级分类", "feeType1", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "二级分类", "feeType2", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "项目", "productName", "left", CommonConstants.NUMBER_ONE_HUNDRED_TWENTY));
        headerList.add(new StatResponseHeaderItem(
                "次数", "executeCount", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "原价金额", "originPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "实收金额", "amount", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "抵扣金额", "deductFee", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "成本", "costPrice", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利", "gross", "money", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "毛利率", "profit", "right", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "开单门店", "chargeClinicName", "left", CommonConstants.NUMBER_ONE_HUNDRED_FORTY));
        headerList.add(new StatResponseHeaderItem(
                "开单人", "chargeEmployeeName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "登记人", "registrationEmployeeName", "left", CommonConstants.NUMBER_NINETY));
        headerList.add(new StatResponseHeaderItem(
                "开单时间", "chargeCreated", "left", CommonConstants.NUMBER_ONE_HUNDRED_SIXTY));
        return headerList;
    }
}
