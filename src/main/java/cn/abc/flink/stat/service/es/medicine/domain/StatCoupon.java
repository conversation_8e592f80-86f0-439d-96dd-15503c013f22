package cn.abc.flink.stat.service.es.medicine.domain;

public class StatCoupon {

    private String chargeId;

    private Double receivedFee;

    private Double discountFee;

    private Double refundFee;

    private Integer status;

    private String patientId;

    private String patientOrderId;

    private String couponId;

    private String couponPromotionInfo;

    public String getChargeId() {
        return chargeId;
    }

    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Double getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(Double refundFee) {
        this.refundFee = refundFee;
    }

    public Double getReceivedFee() {
        return receivedFee;
    }

    public void setReceivedFee(Double receivedFee) {
        this.receivedFee = receivedFee;
    }

    public Double getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(Double discountFee) {
        this.discountFee = discountFee;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientOrderId() {
        return patientOrderId;
    }

    public void setPatientOrderId(String patientOrderId) {
        this.patientOrderId = patientOrderId;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponPromotionInfo() {
        return couponPromotionInfo;
    }

    public void setCouponPromotionInfo(String couponPromotionInfo) {
        this.couponPromotionInfo = couponPromotionInfo;
    }
}
