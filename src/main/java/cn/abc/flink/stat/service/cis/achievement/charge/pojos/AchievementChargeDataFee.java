package cn.abc.flink.stat.service.cis.achievement.charge.pojos;



import java.math.BigDecimal;

/**
 * 分类
 */
public class AchievementChargeDataFee {
    private String name;
    /**
     * 实收
     */
    private BigDecimal value;
    private String field;
    /**
     * 成本
     */
    private BigDecimal cost = BigDecimal.ZERO;
    /**
     * 毛利
     */
    private BigDecimal gross = BigDecimal.ZERO;
    /**
     * 原价
     */
    private BigDecimal origin = BigDecimal.ZERO;
    /**
     * 折扣
     */
    private BigDecimal deduct = BigDecimal.ZERO;
    /**
     * 计提
     */
    private BigDecimal commissionAmt = BigDecimal.ZERO;

    /**
     * 初始化费用
     */
    public void initFee() {
        if (this.value == null) {
            this.value = BigDecimal.ZERO;
        }
        if (this.cost == null) {
            this.cost = BigDecimal.ZERO;
        }
    }

    public String getName() {
        return this.name;
    }

    public BigDecimal getValue() {
        return this.value;
    }

    public String getField() {
        return this.field;
    }


    public void setName(String name) {
        this.name = name;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public void setField(String field) {
        this.field = field;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public BigDecimal getGross() {
        return gross;
    }

    public void setGross(BigDecimal gross) {
        this.gross = gross;
    }

    public BigDecimal getOrigin() {
        return origin;
    }

    public void setOrigin(BigDecimal origin) {
        this.origin = origin;
    }

    public BigDecimal getDeduct() {
        return deduct;
    }

    public void setDeduct(BigDecimal deduct) {
        this.deduct = deduct;
    }

    public BigDecimal getCommissionAmt() {
        return commissionAmt;
    }

    public void setCommissionAmt(BigDecimal commissionAmt) {
        this.commissionAmt = commissionAmt;
    }


}
