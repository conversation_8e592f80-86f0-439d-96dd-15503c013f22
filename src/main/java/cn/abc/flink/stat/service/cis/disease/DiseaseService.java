package cn.abc.flink.stat.service.cis.disease;

import cn.abc.flink.stat.common.SqlUtils;
import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresDiseaseMapper;
import cn.abc.flink.stat.db.dao.DiseaseMysqlMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Department;
import cn.abc.flink.stat.dimension.domain.V2ClinicChainEmployeeSnap;
import cn.abc.flink.stat.service.cis.disease.domain.DataTotal;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseCategory;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseCatgoryResult;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfo;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfoResult;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseParam;
import cn.abc.flink.stat.service.cis.disease.domain.DiseaseProp;
import cn.abc.flink.stat.service.cis.disease.domain.DiseasePropResult;
import cn.abc.flink.stat.service.cis.disease.domain.DoctorDisease;
import cn.abc.flink.stat.service.cis.disease.domain.DoctorDiseaseResult;
import cn.abc.flink.stat.service.cis.handler.EmployeeHandler;
import cn.abc.flink.stat.service.cis.handler.EmployeeSnapHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/2 19:48
 */
@Service("diseaseService1")
public class DiseaseService {
    private Logger log = LoggerFactory.getLogger(DiseaseService.class);
    @Autowired
    HologresDiseaseMapper hologresDiseaseMapper;
    @Resource
    DiseaseMysqlMapper diseaseMysqlMapper;
    @Autowired
    DiseaseExportHandler exportHandler;
    @Resource
    DimensionQuery query;
    @Resource
    StoreUtils storeUtils;
    @Resource
    EmployeeSnapHandler employeeSnapHandler;

    private static final String DOCTOR_FILED_NAME = "doctor_id";
    private static final String DOCTOR_NAME = "doctorName";
    private static final String SNAP_FILED_NAME = "snap_name";

    /**
     * @Description: 病种分析-病种占比
     * @param
     * @param param chainId -clinicId -beginDate -endDate -offset -size -
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DiseasePropResult
     * @Author: zs
     * @Date: 2022/8/2 19:50
     * @throws ParseException -
     */
    public DiseasePropResult getDiseaseProp(DiseaseParam param) throws ParseException {
        String env = TableUtils.getCisTable();
        DiseasePropResult result = new DiseasePropResult();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(param.getBeginDate(), param.getEndDate());
        List<DiseaseProp> diseases = hologresDiseaseMapper
                .selectDiseaseProportion(whereSQL, env, param);
        Long totalCount = hologresDiseaseMapper
                .selectDiseaseProportionSum(whereSQL, env, param);
//        HashMap<String, String> clinicNameMap = getClinicNameMap(chainId, clinicId);
        //病种占比
        setRatio(diseases, totalCount);
        result.setDiseaseCount(totalCount);
        result.setList(diseases);
        return result;
    }
    /**
     * @Description:病种占比
     * @param
     * @param propDisease -
     * @param totalCount -
     * @return
     * @Author: zs
     * @Date: 2022/8/2 19:51
     */
    public void setRatio(List<DiseaseProp> propDisease, Long totalCount) {
        for (DiseaseProp pr : propDisease) {
//            pr.setClinicName(clinicNameMap.get(pr.getClinicId()));
            BigDecimal divide = BigDecimal.valueOf(pr.getSummary())
                    .divide(BigDecimal.valueOf(totalCount), CommonConstants.NUMBER_SIX, BigDecimal.ROUND_HALF_UP);
            pr.setPercent(divide.doubleValue());
        }
    }

    /**
     * @Description:病种分析-医生诊断病种
     * @param
     * @param param :chainId clinicId begin end category offset size
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DoctorDiseaseResult
     * @Author: zs
     * @Date: 2022/8/2 19:54
     * @throws ParseException -
     */
    public DoctorDiseaseResult getDoctorDiseaseOrder(DiseaseParam param) throws ParseException {
        String env = TableUtils.getCisTable();
        DoctorDiseaseResult result = new DoctorDiseaseResult();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(param.getBeginDate(), param.getEndDate());
        List<DoctorDisease> doctors = hologresDiseaseMapper
                .selectDoctorDiseaseOrder(whereSQL, env, param);
//        HashMap<String, String> clinicNameMap = getClinicNameMap(chainId, clinicId);
        //取Top10
        doctors = param.getSize() == null ? (doctors.size() > CommonConstants.NUMBER_TEN
                ? doctors.subList(0, CommonConstants.NUMBER_TEN) : doctors) : doctors;
        Map<String, DiseaseDoctorInfo> doctorNames =
                getDoctorNameMap(param.getChainId(), param.getClinicId(), null);
        for (DoctorDisease d : doctors) {
            DiseaseDoctorInfo info = doctorNames.get(d.getDoctorId());
            if (d.getDoctorName() == null || "".equals(d.getDoctorName())) {
                if (info != null) {
                    d.setDoctorName(info.getDoctorName());
                }
            }
            if (d.getDoctorName() == null) {
                d.setDoctorName("-");
            }
//            d.setClinicName(clinicNameMap.get(d.getClinicId()));
        }
        result.setList(doctors);
        return result;
    }
    /**
     * @Description:
     * @param param chainId clinicId begin end doctorId category offset size
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DoctorDiseaseResult
     * @Author: zs
     * @Date: 2022/8/2 19:59
     * @throws ParseException -
     */
    public DoctorDiseaseResult getDoctorDiseaseDetail(DiseaseParam param) throws ParseException {
        //查询snapId
        if (param != null && param.getFilterDoctorParams() != null) {
            param.setSearchDoctorSql(employeeSnapHandler.initEmployeeSnapNameSql(SNAP_FILED_NAME, DOCTOR_FILED_NAME, DOCTOR_NAME, true, param.getFilterDoctorParams(), false));
        }
        String env = TableUtils.getCisTable();
        DoctorDiseaseResult result = new DoctorDiseaseResult();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(param.getBeginDate(), param.getEndDate());
        List<DoctorDisease> doctors = hologresDiseaseMapper
                .selectDoctorDiseaseDetail(whereSQL, env, param);
        Map<String, Department> departmentMap
                = query.queryBatchDepartmentsByOrgan(param.getChainId(), param.getClinicId());
        log.info("接口：doctor/detail");
        log.info("传入参数：chainId" + param.getChainId()
                + ",clinicId:" + param.getClinicId() + ",doctorId:" + param.getDoctorId());
//        HashMap<String, String> clinicNameMap = getClinicNameMap(chainId, clinicId);

        Map<String, DiseaseDoctorInfo> doctorNames =
                getDoctorNameMap(param.getChainId(), param.getClinicId(), param.getDoctorId());
        for (DoctorDisease d : doctors) {
            DiseaseDoctorInfo doctor = doctorNames.get(d.getDoctorId());
            if (d.getDoctorName() == null || "".equals(d.getDoctorName())) {
                if (doctor != null) {
                    d.setDoctorName(doctor.getDoctorName());
                }
            }
            if (d.getDoctorName() == null) {
                d.setDoctorName("-");
            }
            log.info("返回数据：clinicId:" + d.getClinicId() + ",doctorId:" + d.getDoctorId()
                    + ",category:" + d.getCategory() + ",dept:" + d.getDepartmentName());
//            d.setClinicName(clinicNameMap.get(d.getClinicId()));
            if (departmentMap.containsKey(d.getDepartmentId())) {
                Department orDefault
                        = departmentMap.getOrDefault(d.getDepartmentId(), new Department());
                d.setDepartmentName(orDefault.getName());
            }
            d.setDepartmentName(d.getDepartmentName() == null
                    || d.getDepartmentName().equals("") ? "其他" : d.getDepartmentName());
        }
        result.setList(doctors);
        return result;
    }

    /**
     * @Description:
     * @param
     * @param chainId -
     * @param clinicId -
     * @param doctorId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfo>
     * @Author: zs
     * @Date: 2022/8/3 18:54
     */
    public List<DiseaseDoctorInfo> getMysqlDoctorsINfo(String chainId, String clinicId, String doctorId) {
        return diseaseMysqlMapper.selectDoctorInfo(TableUtils.getCisBasicTable(), chainId, clinicId, doctorId);
    }
    /**
     * @Description:
     * @param
     * @param chainId -
     * @param clinicId -
     * @param doctorId -
     * @return
     * @return java.util.Map<java.lang.String,cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfo>
     * @Author: zs
     * @Date: 2022/8/3 18:54
     */
    public Map<String, DiseaseDoctorInfo> getDoctorNameMap(String chainId, String clinicId, String doctorId) {
        List<DiseaseDoctorInfo> doctorList = getMysqlDoctorsINfo(chainId, clinicId, doctorId);
        for (DiseaseDoctorInfo d : doctorList) {
            log.info("返回医生数据:" + d.getDoctorId() + "," + d.getDoctorName());
        }
        Map<String, DiseaseDoctorInfo> map = new HashMap<>();
        for (DiseaseDoctorInfo d : doctorList) {
            map.put(d.getDoctorId(), d);
        }
        return map;
    }
    public Map<Long, V2ClinicChainEmployeeSnap> getEmployeeSnapMap(Set<Long> ids) {
        Map<Long, V2ClinicChainEmployeeSnap> map = query.queryEmployeeSnaps(ids);
        return map;
    }

    /**
     * @Description: 病种占比-共计求和
     * @param
     * @param param :chainId -clinicId -begin -end -
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DataTotal
     * @Author: zs
     * @Date: 2022/8/3 20:20
     * @throws ParseException -
     */
    public DataTotal getDiseasePropTotal(DiseaseParam param) throws ParseException {
        String env = TableUtils.getCisTable();
        DataTotal total = new DataTotal();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(param.getBeginDate(), param.getEndDate());
        Integer totalCount = hologresDiseaseMapper
                .selectDiseaseProportionTotal(whereSQL, env, param);
        total.setDataTotal(totalCount);
        return total;
    }

    /**
     * @Description:
     * @param
     * @param param -
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DataTotal
     * @Author: zs
     * @Date: 2022/8/3 21:15
     * @throws ParseException -
     */
    public DataTotal getDoctorDiseaseOrderTotal(DiseaseParam param) throws ParseException {
        if (param != null && param.getFilterDoctorParams() != null) {
            param.setSearchDoctorSql(employeeSnapHandler.initEmployeeSnapNameSql(SNAP_FILED_NAME, DOCTOR_FILED_NAME, DOCTOR_NAME, true, param.getFilterDoctorParams(), false));
        }
        String env = TableUtils.getCisTable();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(param.getBeginDate(), param.getEndDate());
        Integer totalCount = hologresDiseaseMapper
                .selectDoctorDiseaseOrderTotal(whereSQL, env, param);
        DataTotal total = new DataTotal();
        total.setDataTotal(totalCount);
        return total;
    }

    /**
     * @Description:
     * @param
     * @param param chainId - clinicId - begin - end - doctorId - category -
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DataTotal
     * @Author: zs
     * @Date: 2022/8/3 21:22
     * @throws  ParseException -
     */
    public DataTotal getDoctorDiseaseDetailTotal(DiseaseParam param) throws ParseException {
        if (param != null && param.getFilterDoctorParams() != null) {
            param.setSearchDoctorSql(employeeSnapHandler.initEmployeeSnapNameSql(SNAP_FILED_NAME, DOCTOR_FILED_NAME, DOCTOR_NAME, true, param.getFilterDoctorParams(), false));
        }
        String env = TableUtils.getCisTable();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(param.getBeginDate(), param.getEndDate());
        Integer totalCount = hologresDiseaseMapper
                .selectDoctorDiseaseDetailTotal(whereSQL, env, param);
        DataTotal total = new DataTotal();
        total.setDataTotal(totalCount);
        return total;
    }

    /**
     * @Description: 病种分析-病种诊断明细-导出
     * @param
     * @param param chainId -clinicId -begin -end -doctorId -category -
     * @param response -
     * @return
     * @Author: zs
     * @Date: 2022/8/3 19:53
     * @throws IOException -
     * @throws ParseException -
     */
    public void exportDoctorDiseaseDetail(DiseaseParam param,
                                          HttpServletResponse response) throws IOException, ParseException {
        DoctorDiseaseResult diseaseDetail = getDoctorDiseaseDetail(param);
        exportHandler.exportDoctorDiseaseDetail(response, param, diseaseDetail.getList());
    }

    /**
     * @Description: 病种分析-医生诊断病种-导出
     * @param
     * @param param :chainId - clinicId - begin - end - category -
     * @param response -
     * @return
     * @Author: zs
     * @Date: 2022/8/3 20:46
     * @throws  IOException -
     * @throws  ParseException -
     */
    public void exportDoctorDiseaseOrder(DiseaseParam param,
                                         HttpServletResponse response) throws IOException, ParseException {
        param.setSize(Integer.MAX_VALUE);
        param.setOffset(0);
        DoctorDiseaseResult diseaseDetail = getDoctorDiseaseOrder(param);
        exportHandler.exportDoctorDiseaseOrder(response, param, diseaseDetail.getList());
    }

    /**
     * @Description:
     * @param
     * @param param :chainId:  clinicId:  begin:  end:
     * @param response -
     * @return
     * @Author: zs
     * @Date: 2022/8/3 21:04
     * @throws IOException -
     * @throws ParseException -
     */
    public void exportDiseaseProportion(DiseaseParam param,
                                        HttpServletResponse response) throws IOException, ParseException {
        String env = TableUtils.getCisTable();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(param.getBeginDate(), param.getEndDate());
        List<DiseaseProp> diseases = hologresDiseaseMapper
                .selectDiseaseProportion(whereSQL, env, param);
//        HashMap<String, String> clinicNameMap = getClinicNameMap(chainId, clinicId);
        Long totalCount = hologresDiseaseMapper
                .selectDiseaseProportionSum(whereSQL, env, param);
//        setRatio(diseases,clinicNameMap,totalCount);
        setRatio(diseases, totalCount);
        exportHandler.exportDiseaseProportion(response, param.getBeginDate(), param.getEndDate(), diseases);
    }

    /**
     * @Description: 病种分析-病种诊断明细（医生下拉框）
     * @param
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DiseaseDoctorInfoResult
     * @Author: zs
     * @Date: 2022/8/3 18:52
     * @throws ParseException -
     */
    public DiseaseDoctorInfoResult getDoctorSelection(String chainId, String clinicId,
                                                      String beginDate, String endDate) throws ParseException {
        List<DiseaseDoctorInfo> list = new ArrayList<>();
        String env = TableUtils.getCisTable();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(beginDate, endDate);
        List<DiseaseDoctorInfo> doctors = hologresDiseaseMapper.
                selectDoctorSelection(whereSQL, env, chainId, clinicId, beginDate, endDate);
        Map<String, DiseaseDoctorInfo> doctorNameMap = getDoctorNameMap(chainId, clinicId, null);
        for (DiseaseDoctorInfo info : doctors) {
            DiseaseDoctorInfo doctor = doctorNameMap.get(info.getDoctorId());
            if (info.getDoctorName() == null || "".equals(info.getDoctorName())) {
                info.setDoctorName(doctor == null ? "未指定" : doctor.getDoctorName());
            }
            info.setChainId(chainId);
            info.setClinicId(clinicId);
        }
        DiseaseDoctorInfoResult result = new DiseaseDoctorInfoResult();
        result.setList(doctors);
        return result;
    }
    /**
     * @Description:
     * @param
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return cn.abc.flink.stat.service.cis.disease.domain.DiseaseCatgoryResult
     * @Author: zs
     * @Date: 2022/8/3 21:27
     * @throws ParseException -
     */
    public DiseaseCatgoryResult selectCategory(String chainId, String clinicId,
                                               String beginDate, String endDate) throws ParseException {
        String env = TableUtils.getCisTable();
        String whereSQL = SqlUtils.buildWhereByPeriodOfTime(beginDate, endDate);
        List<DiseaseCategory> dds = hologresDiseaseMapper.
                selectCategorySelection(whereSQL, env, chainId, clinicId, beginDate, endDate);
        DiseaseCatgoryResult rs = new DiseaseCatgoryResult();
        rs.setList(dds);
        return rs;
    }


}
