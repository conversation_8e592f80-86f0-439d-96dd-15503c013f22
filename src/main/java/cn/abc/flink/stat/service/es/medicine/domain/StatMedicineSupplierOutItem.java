package cn.abc.flink.stat.service.es.medicine.domain;

public class StatMedicineSupplierOutItem {
    private String supplierId;
    private String supplier;

    private double outBatchCount;
    private double outKindCount;
    private double outCount;
    private double outAmount;
    private double outAmountExcludeTax;

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public double getOutBatchCount() {
        return outBatchCount;
    }

    public void setOutBatchCount(double outBatchCount) {
        this.outBatchCount = outBatchCount;
    }

    public double getOutKindCount() {
        return outKindCount;
    }

    public void setOutKindCount(double outKindCount) {
        this.outKindCount = outKindCount;
    }

    public double getOutCount() {
        return outCount;
    }

    public void setOutCount(double outCount) {
        this.outCount = outCount;
    }

    public double getOutAmount() {
        return outAmount;
    }

    public void setOutAmount(double outAmount) {
        this.outAmount = outAmount;
    }

    public double getOutAmountExcludeTax() {
        return outAmountExcludeTax;
    }

    public void setOutAmountExcludeTax(double outAmountExcludeTax) {
        this.outAmountExcludeTax = outAmountExcludeTax;
    }

    @Override
    public String toString() {
        return "StatMedicineSupplierOutItem{" +
                "supplierId='" + supplierId + '\'' +
                ", supplier='" + supplier + '\'' +
                ", outBatchCount=" + outBatchCount +
                ", outKindCount=" + outKindCount +
                ", outCount=" + outCount +
                ", outAmount=" + outAmount +
                ", outAmountExcludeTax=" + outAmountExcludeTax +
                '}';
    }
}
