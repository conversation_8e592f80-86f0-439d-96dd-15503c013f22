package cn.abc.flink.stat.service.cis.achievement.domain;



import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AchievementTransactionRecordBasic {

    protected String chain_id;
    protected String clinic_id;

    protected String doctor_id;
    protected String seller_id;
    protected String copywriter_id;

    protected LocalDateTime transaction_time;
    protected String patient_id;

    protected String transaction_id;

    protected Byte fee_type;
    protected BigDecimal in_amount;


    public String key() {
        return chain_id + "-" + clinic_id + "-" + doctor_id + "-" + seller_id + "-" + copywriter_id + "-" + transaction_id;
    }

    public String getChain_id() {
        return this.chain_id;
    }

    public String getClinic_id() {
        return this.clinic_id;
    }

    public String getDoctor_id() {
        return this.doctor_id;
    }

    public String getSeller_id() {
        return this.seller_id;
    }

    public String getCopywriter_id() {
        return this.copywriter_id;
    }

    public LocalDateTime getTransaction_time() {
        return this.transaction_time;
    }

    public String getPatient_id() {
        return this.patient_id;
    }

    public String getTransaction_id() {
        return this.transaction_id;
    }

    public Byte getFee_type() {
        return this.fee_type;
    }

    public BigDecimal getIn_amount() {
        return this.in_amount;
    }


    public void setChain_id(String chain_id) {
        this.chain_id = chain_id;
    }

    public void setClinic_id(String clinic_id) {
        this.clinic_id = clinic_id;
    }

    public void setDoctor_id(String doctor_id) {
        this.doctor_id = doctor_id;
    }

    public void setSeller_id(String seller_id) {
        this.seller_id = seller_id;
    }

    public void setCopywriter_id(String copywriter_id) {
        this.copywriter_id = copywriter_id;
    }

    public void setTransaction_time(LocalDateTime transaction_time) {
        this.transaction_time = transaction_time;
    }

    public void setPatient_id(String patient_id) {
        this.patient_id = patient_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public void setFee_type(Byte fee_type) {
        this.fee_type = fee_type;
    }

    public void setIn_amount(BigDecimal in_amount) {
        this.in_amount = in_amount;
    }

}
