package cn.abc.flink.stat.service.cis.achievement.domain;



import java.math.BigDecimal;

public class AchievementPersonFee {
    private String chain_id;
    private String clinic_id;

    private String doctor_id;
    private String seller_id;
    private String copywriter_id;

    private Byte feeType;
    private BigDecimal recieved;

    public String key(boolean isWriter) {
        if (isWriter) {
            return chain_id + "-" + clinic_id + copywriter_id;
        } else {
            return chain_id + "-" + clinic_id + "-" + doctor_id + "-" + seller_id;
        }
    }

    public String getChain_id() {
        return this.chain_id;
    }

    public String getClinic_id() {
        return this.clinic_id;
    }

    public String getDoctor_id() {
        return this.doctor_id;
    }

    public String getSeller_id() {
        return this.seller_id;
    }

    public String getCopywriter_id() {
        return this.copywriter_id;
    }

    public Byte getFeeType() {
        return this.feeType;
    }

    public BigDecimal getRecieved() {
        return this.recieved;
    }


    public void setChain_id(String chain_id) {
        this.chain_id = chain_id;
    }

    public void setClinic_id(String clinic_id) {
        this.clinic_id = clinic_id;
    }

    public void setDoctor_id(String doctor_id) {
        this.doctor_id = doctor_id;
    }

    public void setSeller_id(String seller_id) {
        this.seller_id = seller_id;
    }

    public void setCopywriter_id(String copywriter_id) {
        this.copywriter_id = copywriter_id;
    }

    public void setFeeType(Byte feeType) {
        this.feeType = feeType;
    }

    public void setRecieved(BigDecimal recieved) {
        this.recieved = recieved;
    }

}
