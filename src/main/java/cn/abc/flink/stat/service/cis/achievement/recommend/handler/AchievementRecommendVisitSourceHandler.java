package cn.abc.flink.stat.service.cis.achievement.recommend.handler;

import cn.abc.flink.stat.common.BeanUtils;
import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.request.AbcPermission;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.common.utils.MathUtil;
import cn.abc.flink.stat.db.cis.common.AchievementRecommendMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.Organ;
import cn.abc.flink.stat.dimension.domain.V2GoodsCustomType;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.dimension.domain.V2PatientSourceType;
import cn.abc.flink.stat.service.cis.achievement.charge.AchievementChargeService;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeAdviceFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.impl.AchievementChargePersonnelComparator;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeAdviceFeeClassifyReqParams;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeDataFee;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementKeyValuePojo;
import cn.abc.flink.stat.service.cis.achievement.recommend.AchievementRecommendService;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendFeeClassify;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendHeader;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPerson;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonalFeeAmountEntity;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonnelBase;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonnelDispensingCost;
import cn.abc.flink.stat.service.cis.achievement.recommend.pojos.AchievementRecommendReqParams;
import cn.abc.flink.stat.service.cis.config.handler.StatConfigHandler;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.handler.EmployeeHandler;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.util.MathUtils;
import com.alibaba.fastjson.JSON;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 就诊推荐渠道处理逻辑
 *
 * <AUTHOR>
 */
public class AchievementRecommendVisitSourceHandler {
    private static final Logger logger = LoggerFactory.getLogger(AchievementRecommendVisitSourceHandler.class);

    private DimensionQuery query;

    private ExecutorService cacheExecutorService;

    private AchievementRecommendMapper mapper;

    private static final int WIDTH166 = 166;
    private static final int WIDTH90 = 90;
    private static final int WIDTH48 = 48;
    private static final int WIDTH115 = 115;

    private static final Integer PRECISION_DEFAULT_VALUE = 4;

    private static final Integer PRICE_PRECISION_DEFAULT_VALUE = 2;

    private static final Integer CLASSIFY_LEVEL_2 = 2;

    private static final Integer CLASSIFY_LEVEL_1 = 1;

    private static final BigDecimal PERCENT_VALUE = BigDecimal.valueOf(100);

    private CompletableFuture<List<AchievementRecommendPersonalFeeAmountEntity>> amountFuture;

    private CompletableFuture<List<AchievementRecommendPersonalFeeAmountEntity>> classify1Future;

    private CompletableFuture<List<AchievementRecommendFeeEntity>> listFutureFeeFirstClassifyTotal;

    private CompletableFuture<List<AchievementRecommendFeeEntity>> listFutureFeeSecondClassifyTotal;

    private CompletableFuture<List<AchievementChargeAdviceFeeEntity>> listFutureAdviceFeeClassifyTotal;

    private CompletableFuture<List<AchievementRecommendPersonalFeeAmountEntity>> adviceClassifyFuture;

    private CompletableFuture<List<AchievementRecommendPersonalFeeAmountEntity>> classify2Future;

    private CompletableFuture<Map<String, Organ>> listFutureOrgan;

    private CompletableFuture<Map<String, Employee>> listFutureEmployee;

    private CompletableFuture<Map<Integer, V2GoodsCustomType>> listFutureCustomType;

    private CompletableFuture<Map<String, V2PatientSourceType>> listFutureSource;

    private CompletableFuture<Map<Long, V2GoodsFeeType>> listFutureAdviceFeeType;

    public AchievementRecommendVisitSourceHandler(
            DimensionQuery query,
            AchievementRecommendMapper mapper,
            ExecutorService cacheExecutorService) {
        this.query = query;
        this.mapper = mapper;
        this.cacheExecutorService = cacheExecutorService;
    }

    /**
     * 处理数据
     *
     * @param params   参数
     * @param baseList 收费
     * @param costList 发药
     * @param dto      计提
     * @return AchievementRecommendPersonRsp
     * @throws ExecutionException   exception
     * @throws InterruptedException exception
     * @throws ParseException       exception
     */
    public V2StatResponse handle(AchievementRecommendReqParams params,
                                 List<AchievementRecommendPersonnelBase> baseList,
                                 List<AchievementRecommendPersonnelDispensingCost> costList,
                                 StatConfigDto dto) throws ExecutionException, InterruptedException, ParseException {
        //纬度数据
        fetchDimensionFuture(params);
        //费用分类数据
        fetchClassifyFuture(params);
        //除发药成本的实收和成本
        fetchOtherFuture(params);
        CompletableFuture.allOf(this.listFutureOrgan, this.listFutureSource,
                this.listFutureCustomType, this.amountFuture, this.listFutureEmployee).join();
        //数据存放在当前对象字段中
        Map<String, Organ> organMap = this.listFutureOrgan.get();
        Map<String, Employee> employeeMap = this.listFutureEmployee.get();
        Map<String, V2PatientSourceType> patientSourceTypeMap = this.listFutureSource.get();
        //merge 费用分类
        Map<Integer, V2GoodsCustomType> customTypeMap = listFutureCustomType.get();
        //金额
        List<AchievementRecommendPersonalFeeAmountEntity> amount = amountFuture.get();
        //合并成本
        Map<String, AchievementRecommendPerson> mergeDispensingCost = mergeDispensingCost(baseList, costList,
                patientSourceTypeMap, organMap, employeeMap);
        Map<String, AchievementRecommendPerson> sortMap = MapUtils.sortMapByKey(mergeDispensingCost,
                new AchievementChargePersonnelComparator());
        //金额处理
        mergeAmount(sortMap, amount, dto);
        List<AchievementRecommendPerson> data = new ArrayList<>(sortMap.values());
        fillPersonnelNullValue(data, params.getPermission());
        Map<String, List<AchievementChargeDataFee>> feeTypes = new HashMap<>();
        List<AchievementRecommendFeeClassify> feeList = new ArrayList<>();
        if ("100".equals(params.getHisType())) {
            CompletableFuture.allOf(adviceClassifyFuture, listFutureAdviceFeeType, listFutureAdviceFeeClassifyTotal).join();
            Map<Long, V2GoodsFeeType> feeTypeMap = listFutureAdviceFeeType.get();
            feeTypes = mergeAndFlatAdviceFeeClassify(adviceClassifyFuture.get(), feeTypeMap, dto);
            feeList = getAdviceFeeListTotal(listFutureAdviceFeeClassifyTotal.get(), feeTypeMap);
        } else {
            if (classify1Future != null && classify2Future != null) {
                CompletableFuture.allOf(listFutureCustomType, classify1Future, classify2Future).join();
                feeTypes = mergeAndFlatFeeClassify(classify1Future.get(), classify2Future.get(), customTypeMap, dto, params.getHisType());
            } else if (classify1Future == null && classify2Future != null) {
                CompletableFuture.allOf(listFutureCustomType, classify2Future).join();
                feeTypes = mergeAndFlatFeeClassify(new ArrayList<>(), classify2Future.get(), customTypeMap, dto, params.getHisType());
            } else if (classify1Future != null) {
                CompletableFuture.allOf(listFutureCustomType, classify1Future).join();
                feeTypes = mergeAndFlatFeeClassify(classify1Future.get(), new ArrayList<>(), customTypeMap, dto, params.getHisType());
            }
            //费用分类最终结果
            feeList = getFeeListTotal(listFutureFeeFirstClassifyTotal, listFutureFeeSecondClassifyTotal, customTypeMap, params.getChainId(), params.getHisType());
        }

        // 求和并转换为map list
        Map<String, Object> summaryMap = new HashMap<>();
        List<Map<String, Object>> maps = sumDataAndToMap(sortMap.values(), feeTypes, summaryMap);

        logger.info("费用分类最终结果集{}", JSON.toJSONString(feeList));
        if (params.getPayModeSql() != null && !"".equals(params.getPayModeSql().trim())) {
            data.forEach(d -> {
                d.setCost(null);
                d.setGross(null);
                d.setProfit(BigDecimal.ONE.negate());
            });
        }
        V2StatResponse response = new V2StatResponse();
        response.setHeader(getVisitSourceHeader(params, feeList));
        response.setData(maps);
        response.setSummary(summaryMap);
        response.setTotal(new StatResponseTotal(ConvertUtils.getAsLong(data.size())));
        return response;
    }


    /**
     * 获取推荐渠道表头
     *
     * @param params  -
     * @param feeList -
     * @return -
     */
    public List<TableHeaderEmployeeItem> getVisitSourceHeader(AchievementRecommendReqParams params,
                                                              List<AchievementRecommendFeeClassify> feeList) {
        List<TableHeaderEmployeeItem> tableHeaderEmployeeItems = query.getTableHeaderEmployeeItems(
                params.getParams().getEmployeeId(), HeaderTableKeyConfig.ACHIEVEMENT_RECOMMEND_VISIT_SOURCE,
                params.getParams().getViewModeInteger(), params.getParams().getNodeType(),
                HeaderTableKeyConfig.EXCLUDE_HIDDEN_1);
        tableHeaderEmployeeItems = AchievementRecommendHandler
                .handleHeaderItemOfFeeType(tableHeaderEmployeeItems, feeList, params.getHisType());
        return tableHeaderEmployeeItems;
    }

    /**
     * 触发维度的future，获取mysql中的数据
     *
     * @param params 参数
     */
    private void fetchDimensionFuture(AchievementRecommendReqParams params) {
        this.listFutureOrgan = CompletableFuture.supplyAsync(() -> {
            return query.queryOrganByParentId(params.getChainId());
        }, cacheExecutorService);
        this.listFutureEmployee = CompletableFuture.supplyAsync(() -> {
            return query.queryEmployeeByChainId(params.getChainId());
        }, cacheExecutorService);
        this.listFutureCustomType = CompletableFuture.supplyAsync(() -> {
            return query.queryProductCustomTypeTextByChainId(params.getChainId());
        }, cacheExecutorService);
        this.listFutureSource = CompletableFuture.supplyAsync(() -> {
            return query.queryPatientSourceType(params.getChainId());
        }, cacheExecutorService);
        this.listFutureAdviceFeeType = CompletableFuture.supplyAsync(() ->
                query.selectAdviceFeeType(params.getChainId()), cacheExecutorService);
    }

    /**
     * 触发费用分类的future，获取mysql中的数据
     *
     * @param params 参数
     */
    private void fetchClassifyFuture(AchievementRecommendReqParams params) {
        //二级分类参数
        AchievementRecommendReqParams fee2Params = MapUtils.string2Object(JSON.toJSONString(params), AchievementRecommendReqParams.class);
        fee2Params.setFee1(null);
        //一级分类参数
        AchievementRecommendReqParams fee1Params = MapUtils.string2Object(JSON.toJSONString(params), AchievementRecommendReqParams.class);
        fee1Params.setFee2(null);
        if (params.getFee2() != null) {
            this.classify2Future = CompletableFuture.supplyAsync(() -> mapper
                    .selectPersonalFeeClassify(TableUtils.getCisTable(), fee2Params, CLASSIFY_LEVEL_2), cacheExecutorService);

            this.listFutureFeeSecondClassifyTotal = CompletableFuture.supplyAsync(() -> mapper
                    .selectFeeSecondClassify(TableUtils.getCisTable(), fee2Params), cacheExecutorService);
        }
        if (params.getFee1() != null || params.getFee2() == null) {
            this.classify1Future = CompletableFuture.supplyAsync(() -> mapper
                    .selectPersonalFeeClassify(TableUtils.getCisTable(), fee1Params, CLASSIFY_LEVEL_1));
            this.listFutureFeeFirstClassifyTotal = CompletableFuture.supplyAsync(() -> mapper
                    .selectFeeFirstClassify(TableUtils.getCisTable(), fee1Params), cacheExecutorService);
        }
        if ("100".equals(params.getHisType())) {
            this.adviceClassifyFuture = CompletableFuture.supplyAsync(() -> mapper
                    .selectPersonalAdviceClassify(TableUtils.getCisTable(), params), cacheExecutorService);
            this.listFutureAdviceFeeClassifyTotal = CompletableFuture.supplyAsync(() -> mapper
                    .selectAdviceFeeClassify(TableUtils.getCisTable(),
                            new AchievementChargeAdviceFeeClassifyReqParams(params.getChainId(),
                                    params.getClinicId(),
                                    null,
                                    null,
                                    null,
                                    params.getBeginDate(),
                                    params.getEndDate(),
                                    params.getFeeTypeIdSql(),
                                    params.getIncludeReg(),
                                    params.getVisitSource1(),
                                    params.getVisitSource2(),
                                    null,
                                    params.getHisType(),
                                    null,
                                    params.getConfig())), cacheExecutorService);
        }
    }

    /**
     * 触发其他的future，获取mysql中的数据
     *
     * @param params 参数
     */
    private void fetchOtherFuture(AchievementRecommendReqParams params) {
        // 实收和成本(除了发药成本意外的其他成本)
        this.amountFuture = CompletableFuture.supplyAsync(() -> mapper
                .selectPersonalAmount(TableUtils.getCisTable(), params), cacheExecutorService);
    }

    /**
     * 从future中获取所有费用分类，拼装成List
     *
     * @param listFutureFeeFirstClassifyTotal  future
     * @param listFutureFeeSecondClassifyTotal future
     * @param customTypeMap                    自定义分类
     * @return AchievementRecommendFeeClassify list
     * @throws ExecutionException   exception
     * @throws InterruptedException exception
     */
    public List<AchievementRecommendFeeClassify> getFeeListTotal(
            CompletableFuture<List<AchievementRecommendFeeEntity>> listFutureFeeFirstClassifyTotal,
            CompletableFuture<List<AchievementRecommendFeeEntity>> listFutureFeeSecondClassifyTotal,
            Map<Integer, V2GoodsCustomType> customTypeMap, String chainId, String hisType
    ) throws ExecutionException, InterruptedException {
        List<AchievementRecommendFeeClassify> feeList = null;
        if (listFutureFeeFirstClassifyTotal != null && listFutureFeeSecondClassifyTotal != null) {
            CompletableFuture.allOf(listFutureFeeFirstClassifyTotal, listFutureFeeSecondClassifyTotal).join();
            feeList = buildFeeList(buildFeeMap(query,
                    listFutureFeeFirstClassifyTotal.get(),
                    listFutureFeeSecondClassifyTotal.get(),
                    customTypeMap,
                    AchievementChargeService.NOT_ADD_UNSPECIFIED_SECONNDARY_CLASSIFY_LIST, chainId, hisType
                    ),
                    AchievementChargeService.SORT_FEE_TITLE);
        } else if (listFutureFeeFirstClassifyTotal == null && listFutureFeeSecondClassifyTotal != null) {
            listFutureFeeSecondClassifyTotal.join();
            feeList = buildFeeList(buildFeeMap(
                    query,
                    new ArrayList<>(),
                    listFutureFeeSecondClassifyTotal.get(),
                    customTypeMap,
                    AchievementChargeService.NOT_ADD_UNSPECIFIED_SECONNDARY_CLASSIFY_LIST, chainId, hisType
                    ),
                    AchievementChargeService.SORT_FEE_TITLE);
        } else if (listFutureFeeFirstClassifyTotal != null) {
            listFutureFeeFirstClassifyTotal.join();
            feeList = buildFeeList(buildFeeMap(query,
                    listFutureFeeFirstClassifyTotal.get(),
                    new ArrayList<>(),
                    customTypeMap,
                    AchievementChargeService.NOT_ADD_UNSPECIFIED_SECONNDARY_CLASSIFY_LIST, chainId, hisType),
                    AchievementRecommendService.SORT_FEE_TITLE);
        }
        return feeList.stream().filter(
                fee -> !"会员充值本金".equals(fee.getName().trim())
                        && !"卡项充值本金".equals(fee.getName().trim())
                        && !"-".equals(fee.getName().trim())
        ).collect(Collectors.toList());
    }

    /**
     * 从future中获取所有费用分类，拼装成List
     *
     * @param adviceFeeClassifyTotal future
     * @param feeTypeMap             -
     */
    public List<AchievementRecommendFeeClassify> getAdviceFeeListTotal(
            List<AchievementChargeAdviceFeeEntity> adviceFeeClassifyTotal,
            Map<Long, V2GoodsFeeType> feeTypeMap) {
        Map<Long, AchievementRecommendFeeClassify> classifyMap = new HashMap<>();
        adviceFeeClassifyTotal.forEach(fee -> {
            if (feeTypeMap.containsKey(fee.getFeeTypeId())) {
                AchievementRecommendFeeClassify feeClassify = new AchievementRecommendFeeClassify();
                feeClassify.setName(feeTypeMap.get(fee.getFeeTypeId()).getName());
                feeClassify.setValue(fee.getFeeTypeId().toString());
                classifyMap.put(fee.getFeeTypeId(), feeClassify);
            } else {
                AchievementRecommendFeeClassify feeClassify = new AchievementRecommendFeeClassify();
                feeClassify.setName("-");
                feeClassify.setValue("-1");
                classifyMap.put(-1L, feeClassify);
            }
        });
        return new ArrayList<>(classifyMap.values());
    }

    /**
     * 构建费用分类Map
     *
     * @param query                   query
     * @param fee1List                fee1List
     * @param fee2List                fee2List
     * @param customTypeMap           customTypeMap
     * @param noSecondaryClassifyList noSecondaryClassifyList
     * @return map
     */
    public static Map<String, AchievementRecommendFeeClassify> buildFeeMap(
            DimensionQuery query,
            List<AchievementRecommendFeeEntity> fee1List,
            List<AchievementRecommendFeeEntity> fee2List,
            Map<Integer, V2GoodsCustomType> customTypeMap,
            List<String> noSecondaryClassifyList,
            String chainId,
            String hisType) {
        Map<String, AchievementRecommendFeeClassify> feeMap = new HashMap<>();
        logger.info("一级费用分类结果{}", JSON.toJSONString(fee1List));
        logger.info("二级级费用分类结果{}", JSON.toJSONString(fee2List));
        for (AchievementRecommendFeeEntity fee1 : fee1List) {
            if (fee1 != null) {
                AchievementRecommendFeeClassify classify =
                        getAchievementChargeFeeClassifyInstance(query,
                                fee1.getClassifyLevel1Id(), chainId, hisType);
                feeMap.put(fee1.getClassifyLevel1Id(), classify);
            }
        }

        if (fee2List.size() > 0) {
            for (AchievementRecommendFeeEntity fee2 : fee2List) {
                if (fee2 != null) {
                    if (fee2.getClassifyLevel2Id() != null) {
                        AchievementRecommendFeeClassify classify = buildFeeList2(query, fee2,
                                feeMap, customTypeMap, noSecondaryClassifyList, chainId, hisType);
                        feeMap.put(fee2.getClassifyLevel1Id(), classify);
                    }
                }
            }
        }
        return feeMap;
    }

    /**
     * 构建费用分类list
     *
     * @param sortFeeTitle header顺序
     * @param feeMap       feeMap
     * @return list
     */
    public static List<AchievementRecommendFeeClassify> buildFeeList(
            Map<String, AchievementRecommendFeeClassify> feeMap,
            String[] sortFeeTitle) {

        Map<String, AchievementRecommendFeeClassify> map = new HashMap<>();
        for (AchievementRecommendFeeClassify classify : feeMap.values()) {
            map.put(classify.getName(), classify);
        }
        List<AchievementRecommendFeeClassify> list = new ArrayList<>();
        for (String feeTitle : sortFeeTitle) {
            if (map.containsKey(feeTitle)) {
                list.add(map.get(feeTitle));
            }
        }
        return list;
    }

    /**
     * buildFeeList方法中处理二级费用分类
     *
     * @param query                   query
     * @param fee2                    二分费用分类对象
     * @param feeMap                  map
     * @param customTypeMap           自定义分类
     * @param noSecondaryClassifyList noSecondaryClassifyList
     * @return AchievementRecommendFeeClassify
     */
    private static AchievementRecommendFeeClassify buildFeeList2(
            DimensionQuery query,
            AchievementRecommendFeeEntity fee2,
            Map<String, AchievementRecommendFeeClassify> feeMap,
            Map<Integer, V2GoodsCustomType> customTypeMap,
            List<String> noSecondaryClassifyList,
            String chainId,
            String hisType) {
        AchievementRecommendFeeClassify classify = feeMap.getOrDefault(
                fee2.getClassifyLevel1Id(),
                getAchievementChargeFeeClassifyInstance(
                        query,
                        fee2.getClassifyLevel1Id(), chainId, hisType)
        );
        Map<String, String> map = new HashMap<>();
        if (fee2.getClassifyLevel2Id() == 0
                && !AchievementChargeService
                .NOT_ADD_UNSPECIFIED_SECONNDARY_CLASSIFY_LIST
                .contains(classify.getName())
        ) {
            map.put("name", "未指定");
            map.put("value", classify.getValue() + "/0");
            classify.getChildren().add(map);
        } else {
            V2GoodsCustomType customType =
                    customTypeMap.get(fee2.getClassifyLevel2Id());
            if (customType != null) {
                map.put("name", customType.getName());
                map.put("value", classify.getValue() + "/" + fee2.getClassifyLevel2Id());
                classify.getChildren().add(map);
            } else {
                if (!noSecondaryClassifyList.contains(
                        query.queryProductClassifyLevel1(chainId, fee2.getClassifyLevel1Id(), hisType))) {
                    map.put("name", "未指定");
                    map.put("value", classify.getValue() + "/0");
                    classify.getChildren().add(map);
                }
            }
        }
        return classify;
    }

    /**
     * 获取分类实例对象
     *
     * @param query query
     * @param fee1  分类
     * @return AchievementRecommendFeeClassify
     */
    private static AchievementRecommendFeeClassify getAchievementChargeFeeClassifyInstance(DimensionQuery query,
                                                                                           String fee1,
                                                                                           String chainId,
                                                                                           String hisType) {
        AchievementRecommendFeeClassify acfc = new AchievementRecommendFeeClassify();
        acfc.setName(query.queryProductClassifyLevel1(chainId, fee1, hisType));
        acfc.setValue(fee1);
        acfc.setChildren(new ArrayList<>());
        return acfc;
    }

    /**
     * 获取header
     *
     * @param feeLists     分类
     * @param isChainAdmin isChainAdmin
     * @param hisType      hisType
     * @return headers
     */
    public List<AchievementRecommendHeader> getHearder(List<AchievementRecommendFeeClassify> feeLists,
                                                       Integer isChainAdmin, String hisType) {
        List<AchievementRecommendHeader> personnelHeader = new ArrayList<>();
        logger.info("param:isChainAdmin{}", isChainAdmin);
        if (isChainAdmin == 1) {
            personnelHeader.add(new AchievementRecommendHeader(
                    "门店", "clinicName", "left", WIDTH166, null));

        }
        personnelHeader.add(new AchievementRecommendHeader(
                "本次推荐", "recommendName", "left", WIDTH166, null));
        personnelHeader.add(new AchievementRecommendHeader(
                "计提金额", "commissionAmount", "right", WIDTH90,
                "money", "commissionAmountPopover"));
        personnelHeader.add(new AchievementRecommendHeader(
                "客量", "patientCount", "right", WIDTH48, null));
        personnelHeader.add(new AchievementRecommendHeader(
                "客单", "avgPatientAmount", "right", WIDTH90, "money"));
        personnelHeader.add(new AchievementRecommendHeader(
                "成本", "cost", "right", WIDTH90, "money"));
        personnelHeader.add(new AchievementRecommendHeader(
                "毛利", "gross", "right", WIDTH90, "money"));
        personnelHeader.add(new AchievementRecommendHeader(
                "毛利率", "profitStr", "center", WIDTH90, null));
        List<AchievementRecommendHeader> leveltwo = new ArrayList<>();
        for (AchievementRecommendFeeClassify fee : feeLists) {

            AchievementRecommendHeader recommendHeader = new AchievementRecommendHeader(
                    fee.getName(), "feeType-" + fee.getValue(), "right", WIDTH115, "money");
            if (fee.getChildren() != null) {
                List<AchievementRecommendHeader> levelThree = new ArrayList<>();
                for (Map<String, String> child : fee.getChildren()) {
                    AchievementRecommendHeader recommendHeader2 = new AchievementRecommendHeader(
                            child.get("name"), "feeType-" + child.get("value"),
                            "right", WIDTH115, "money");
                    levelThree.add(recommendHeader2);
                }
                recommendHeader.setColumnChildren(levelThree);
            }
            leveltwo.add(recommendHeader);
        }
        if ("100".equals(hisType)) {
            personnelHeader.add(new AchievementRecommendHeader(
                    "费用类型", "feeType", "center", WIDTH115, null, leveltwo));
        } else {
            personnelHeader.add(new AchievementRecommendHeader(
                    "费用分类", "feeType", "center", WIDTH115, null, leveltwo));
        }
        personnelHeader.add(new AchievementRecommendHeader(
                "转诊医生", "referralDoctorName", "center", WIDTH115, null));
        return personnelHeader;
    }


    /**
     * 合并发药成本，并处理只有发药没有实收的场景
     *
     * @param baseList             收费数据
     * @param costList             成本数据
     * @param patientSourceTypeMap 就诊推荐
     * @param organMap             门店
     * @param employeeMap          人员
     * @return map
     */
    public Map<String, AchievementRecommendPerson> mergeDispensingCost(
            List<AchievementRecommendPersonnelBase> baseList,
            List<AchievementRecommendPersonnelDispensingCost> costList,
            Map<String, V2PatientSourceType> patientSourceTypeMap,
            Map<String, Organ> organMap, Map<String, Employee> employeeMap) {
        Map<String, AchievementRecommendPerson> map = new HashMap<>();
        for (AchievementRecommendPersonnelBase base : baseList) {
            AchievementRecommendPerson person = new AchievementRecommendPerson(base.getChainId(), base.getClinicId(),
                    base.getSourceLevelOne(), base.getSourceLevelTwo(), base.getIsCopywriter(), base.getPatientCount(),
                    base.getVisitSourceFromType(), base.getVisitSourceFrom());
            // 门店
            Organ o = organMap.get(base.getClinicId());
            if (o != null) {
                person.setClinicName(o.getShortName() != null && !"".equals(o.getShortName())
                        ? o.getShortName() : o.getName());
            }
            // 人员
            person.setVisitSource(query, patientSourceTypeMap);
            person.setReferralDoctorName(EmployeeHandler.handleEmployeeNameById(employeeMap,
                    base.getReferralDoctorId()));
            String sourceLevelOne = person.getSourceLevelOne();
            String sourceLevelTwo = person.getSourceLevelTwo();
            String visitSourceFrom = person.getVisitSourceFrom();
            String key = person.getChainId() + "-" + person.getClinicId() + "-"
                    + sourceLevelOne;
            if (sourceLevelTwo != null) {
                key += "-" + sourceLevelTwo;
            }
            if (visitSourceFrom != null) {
                key += "-" + visitSourceFrom;
            }
            map.put(key, person);
        }

        //处理成本
        for (AchievementRecommendPersonnelDispensingCost dispensingCost : costList) {
            AchievementRecommendPerson person = null;
            String sourceLevelOne = dispensingCost.getSourceLevelOne();
            String sourceLevelTwo = dispensingCost.getSourceLevelTwo();
            String visitSourceFrom = dispensingCost.getVisitSourceFrom();
            if (sourceLevelTwo != null) {
                sourceLevelOne += "-" + sourceLevelTwo;
            }
            if (visitSourceFrom != null) {
                sourceLevelOne += "-" + visitSourceFrom;
            }
            String str = dispensingCost.getChainId() + "-"
                    + dispensingCost.getClinicId() + "-" + sourceLevelOne;
            if (map.containsKey(str)) {
                person = map.get(str);
                person.setHoverCode(MathUtil.bitRepresentation(0, dispensingCost.getHoverCode()));
            } else {
                person = new AchievementRecommendPerson(dispensingCost.getChainId(), dispensingCost.getClinicId(),
                        dispensingCost.getSourceLevelOne(), dispensingCost.getSourceLevelTwo(),
                        dispensingCost.getIsCopywriter(), 0L, dispensingCost.getVisitSourceFromType(),
                        dispensingCost.getVisitSourceFrom());
                // 就诊推荐
                person.setVisitSource(query, patientSourceTypeMap);
                // 门店
                Organ o = organMap.get(dispensingCost.getClinicId());
                if (o != null) {
                    person.setClinicName(o.getShortName() != null && !"".equals(o.getShortName())
                            ? o.getShortName() : o.getName());
                }
                String sourceLevelOne1 = person.getSourceLevelOne();
                String sourceLevelTwo2 = person.getSourceLevelTwo();
                String sourceLevelTwo3 = person.getVisitSourceFrom();
                if (sourceLevelTwo2 != null) {
                    sourceLevelOne1 += "-" + sourceLevelTwo2;
                }
                if (sourceLevelTwo3 != null) {
                    sourceLevelOne1 += "-" + sourceLevelTwo3;
                }
                String str2 = person.getChainId() + "-" + person.getClinicId() + "-" + sourceLevelOne1;
                person.setHoverCode(MathUtil.bitRepresentation(1, dispensingCost.getHoverCode()));
                map.put(str2, person);
            }
            if (person.getCost() == null || person.getCost().compareTo(BigDecimal.ZERO) == 0) {
                person.setCost(dispensingCost.getCostPrice());
            } else {
                person.setCost(person.getCost().add(dispensingCost.getCostPrice()));
            }
        }
        return map;
    }


    /**
     * 合并实收和非发药成本
     *
     * @param map        main data
     * @param dto        计提配置
     * @param amountList 实收
     */
    public void mergeAmount(Map<String, AchievementRecommendPerson> map,
                            List<AchievementRecommendPersonalFeeAmountEntity> amountList,
                            StatConfigDto dto) {
        DecimalFormat f = new DecimalFormat("#.00");
        for (AchievementRecommendPersonalFeeAmountEntity cost : amountList) {
            String sourceLevelOne = cost.getSourceLevelOne();
            String sourceLevelTwo = cost.getSourceLevelTwo();
            String visitSourceFrom = cost.getVisitSourceFrom();
            if (sourceLevelTwo != null) {
                sourceLevelOne += "-" + sourceLevelTwo;
            }
            if (visitSourceFrom != null) {
                sourceLevelOne += "-" + visitSourceFrom;
            }
            String key = cost.getChainId() + "-" + cost.getClinicId() + "-" + sourceLevelOne;
            AchievementRecommendPerson person = map.getOrDefault(key, null);
            if (person != null) {
                person.setCost(person.getCost() != null ? person.getCost().add(cost.getCostPrice())
                        : cost.getCostPrice());
                person.setAmount(person.getAmount() != null ? person.getAmount().add(cost.getReceivedPrice())
                        : cost.getReceivedPrice());
                person.setOrigin(person.getOrigin() != null ? person.getOrigin().add(cost.getOriginPrice())
                        : cost.getOriginPrice());
                person.setDeduct(person.getDeduct() != null ? person.getDeduct().add(cost.getDeductPrice())
                        : cost.getDeductPrice());

                // 计提金额
                person.setCommissionAmount(StatConfigHandler.getCommissionAmount(dto, person.getOrigin(),
                        person.getAmount(), null, person.getDeduct()));

                if (person.getPatientCount() != null && person.getPatientCount() != 0) {
                    person.setAvgPatientAmount(person.getCommissionAmount().
                            divide(new BigDecimal(person.getPatientCount()),
                                    PRICE_PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP));
                }
                BigDecimal profitValue;
                BigDecimal grossValue = person.getCommissionAmount().subtract(person.getCost());
                if (person.getCommissionAmount().compareTo(BigDecimal.ZERO) == 0) {
                    profitValue = BigDecimal.ONE.negate();
                } else {
                    profitValue = grossValue.divide(person.getCommissionAmount(), PRECISION_DEFAULT_VALUE,
                            RoundingMode.HALF_UP);
                }
                person.setGross(grossValue);
                person.setProfit(profitValue);
                person.setProfitStr(f.format(profitValue.multiply(PERCENT_VALUE).doubleValue()) + "%");
                person.setFeeTypes(new ArrayList<>());
            }
        }
    }

    /**
     * 求和并且将对象list转换为map list
     *
     * @param list       对象list
     * @param feeTypes   费用分类
     * @param summaryMap 求和map
     * @return map list
     */
    private List<Map<String, Object>> sumDataAndToMap(Collection<AchievementRecommendPerson> list,
                                                      Map<String, List<AchievementChargeDataFee>> feeTypes,
                                                      Map<String, Object> summaryMap) {
        DecimalFormat f = new DecimalFormat("#.00");
        List<Map<String, Object>> result = new ArrayList<>();

        String keyName = "";
        summaryMap.put("recommendName", "合计");
        summaryMap.put("profit", "-");
        summaryMap.put("avgPatientAmount", 0.00);

        // 扁平化费用分类与求汇总
        for (AchievementRecommendPerson person : list) {
            String sourceLevelOne = person.getSourceLevelOne();
            String sourceLevelTwo = person.getSourceLevelTwo();
            String clinicId = person.getClinicId();
            String visitSourceFrom = person.getVisitSourceFrom();
            if (sourceLevelTwo != null) {
                sourceLevelOne += "-" + sourceLevelTwo;
            }
            if (visitSourceFrom != null) {
                sourceLevelOne += "-" + visitSourceFrom;
            }
            if (clinicId != null) {
                sourceLevelOne += "-" + clinicId;
            }
            Map<String, Object> one = BeanUtils.toMap(AchievementRecommendPerson.class, person);
            if (one != null) {
                one.remove("feeTypes");
                List<AchievementChargeDataFee> feeList = feeTypes.get(sourceLevelOne);
                if (feeList != null) {
                    for (AchievementChargeDataFee fee : feeList) {
                        //费用分类扁平化
                        one.put("feeType-" + fee.getField(), fee.getCommissionAmt());

                        // 费用分类求汇总
                        keyName = "feeType-" + fee.getField();
                        Object obj = summaryMap.get(keyName);
                        if (obj == null) {
                            summaryMap.put(keyName, fee.getValue().doubleValue());
                        } else {
                            double v1 = (double) obj;
                            double v2 = fee.getValue().doubleValue();
                            summaryMap.put(keyName, v1 + v2);
                        }
                    }
                }
                if (person.getCommissionAmount() != null) {
                    summaryMap.put("commissionAmount", (double) summaryMap.getOrDefault("commissionAmount", 0.00)
                            + person.getCommissionAmount().doubleValue());
                }
                if (person.getPatientCount() != null) {
                    summaryMap.put("patientCount", (long) summaryMap.getOrDefault("patientCount", 0L)
                            + person.getPatientCount());
                }
                if (person.getCost() != null) {
                    summaryMap.put("cost", (double) summaryMap.getOrDefault("cost", 0.00)
                            + person.getCost().doubleValue());
                }
                if (person.getGross() != null) {
                    summaryMap.put("gross", (double) summaryMap.getOrDefault("gross", 0.00)
                            + person.getGross().doubleValue());
                }
                if (summaryMap.get("patientCount") != null && (long) summaryMap.get("patientCount") != 0) {
                    summaryMap.put("avgPatientAmount", ((double) summaryMap.getOrDefault("commissionAmount", 0.00)) / (long) summaryMap.get("patientCount"));
                }
                BigDecimal profitValue;
                BigDecimal commissionAmount = new BigDecimal(summaryMap.get("commissionAmount").toString());
                BigDecimal grossValue = commissionAmount.subtract(new BigDecimal(summaryMap.getOrDefault("cost", 0.00).toString()));
                if (commissionAmount.compareTo(BigDecimal.ZERO) == 0) {
                    profitValue = BigDecimal.ONE.negate();
                } else {
                    profitValue = grossValue.divide(commissionAmount, PRECISION_DEFAULT_VALUE, RoundingMode.HALF_UP);
                }
                summaryMap.put("profitStr", f.format(profitValue.multiply(PERCENT_VALUE).doubleValue()) + "%");
                result.add(one);
            }
        }
        return result;
    }

    /**
     * 将医嘱费用分类拉平，并且合并
     *
     * @param adviceClassifyList 费用分类
     * @param feeTypeMap         费用分类
     * @param dto                计提配置
     */
    public Map<String, List<AchievementChargeDataFee>>
    mergeAndFlatAdviceFeeClassify(List<AchievementRecommendPersonalFeeAmountEntity> adviceClassifyList,
                                  Map<Long, V2GoodsFeeType> feeTypeMap,
                                  StatConfigDto dto) {
        Map<String, List<AchievementChargeDataFee>> feeTypes = new HashMap<>();
        for (AchievementRecommendPersonalFeeAmountEntity fee : adviceClassifyList) {
            String sourceLevelOne = fee.getSourceLevelOne();
            String sourceLevelTwo = fee.getSourceLevelTwo();
            String visitSourceFrom = fee.getVisitSourceFrom();
            String clinicId = fee.getClinicId();
            if (sourceLevelTwo != null) {
                sourceLevelOne += "-" + sourceLevelTwo;
            }
            if (visitSourceFrom != null) {
                sourceLevelOne += "-" + visitSourceFrom;
            }
            if (clinicId != null) {
                sourceLevelOne += "-" + clinicId;
            }
            AchievementChargeDataFee dataFee = new AchievementChargeDataFee();
            getAdviceFeeType(feeTypeMap, fee, dataFee, dto);
            List<AchievementChargeDataFee> fees = feeTypes.get(sourceLevelOne);
            if (fees != null) {
                fees.add(dataFee);
            } else {
                List<AchievementChargeDataFee> feeList = new ArrayList<>();
                feeList.add(dataFee);
                feeTypes.put(sourceLevelOne, feeList);
            }
        }
        return feeTypes;
    }

    /**
     * 填充医嘱费用分类金额信息
     *
     * @param feeTypeMap -
     * @param detail     -
     * @param fee        -
     * @param dto        -
     */
    public void getAdviceFeeType(Map<Long, V2GoodsFeeType> feeTypeMap,
                                 AchievementRecommendPersonalFeeAmountEntity detail,
                                 AchievementChargeDataFee fee,
                                 StatConfigDto dto) {
        if (feeTypeMap.containsKey(detail.getFeeTypeId())) {
            fee.setName(feeTypeMap.getOrDefault(detail.getFeeTypeId(), new V2GoodsFeeType()).getName());
        }
        fee.setField(detail.getFeeTypeId() == null || detail.getFeeTypeId() == 0 ? "-1" : detail.getFeeTypeId().toString());

        fee.setOrigin(detail.getOriginPrice());
        fee.setDeduct(detail.getDeductPrice());
        fee.setValue(detail.getReceivedPrice());
        // 计提金额
        fee.setCommissionAmt(StatConfigHandler.getCommissionAmount(dto, fee.getOrigin(),
                fee.getValue(), null, fee.getDeduct()));
        fee.setGross(fee.getCommissionAmt().subtract(fee.getCost()));
    }

    /**
     * 将费用分类拉平，并且合并
     *
     * @param classify1List classify1List
     * @param classify2List classify2List
     * @param customTypeMap customTypeMap
     * @param dto           -
     * @return feeTypes
     */
    public Map<String, List<AchievementChargeDataFee>> mergeAndFlatFeeClassify(
            List<AchievementRecommendPersonalFeeAmountEntity> classify1List,
            List<AchievementRecommendPersonalFeeAmountEntity> classify2List,
            Map<Integer, V2GoodsCustomType> customTypeMap, StatConfigDto dto,
            String hisType) {
        Map<String, List<AchievementChargeDataFee>> feeTypes = new HashMap<>();
        for (AchievementRecommendPersonalFeeAmountEntity fee1 : classify1List) {
            String sourceLevelOne = fee1.getSourceLevelOne();
            String sourceLevelTwo = fee1.getSourceLevelTwo();
            String visitSourceFrom = fee1.getVisitSourceFrom();

            if (sourceLevelTwo != null) {
                sourceLevelOne += "-" + sourceLevelTwo;
            }
            if (visitSourceFrom != null) {
                sourceLevelOne += "-" + visitSourceFrom;
            }
            if (fee1.getClinicId() != null) {
                sourceLevelOne += "-" + fee1.getClinicId();
            }
            AchievementChargeDataFee fee = new AchievementChargeDataFee();
            setFeeType1(fee, fee1, dto, hisType);
            List<AchievementChargeDataFee> fees = feeTypes.get(sourceLevelOne);
            if (fees != null) {
                fees.add(fee);
            } else {
                List<AchievementChargeDataFee> feeList = new ArrayList<>();
                feeList.add(fee);
                feeTypes.put(sourceLevelOne, feeList);
            }
        }

        for (AchievementRecommendPersonalFeeAmountEntity detail : classify2List) {
            String sourceLevelOne = detail.getSourceLevelOne();
            String sourceLevelTwo = detail.getSourceLevelTwo();
            String visitSourceFrom = detail.getVisitSourceFrom();
            if (sourceLevelTwo != null) {
                sourceLevelOne += "-" + sourceLevelTwo;
            }
            if (visitSourceFrom != null) {
                sourceLevelOne += "-" + visitSourceFrom;
            }
            if (detail.getClinicId() != null) {
                sourceLevelOne += "-" + detail.getClinicId();
            }

            AchievementChargeDataFee fee = new AchievementChargeDataFee();
            if (detail.getClassifyLevel2() != null) {
                String fee1 = query.queryProductClassifyLevel1(dto.getChainId(), detail.getClassifyLevel1(), hisType);
                if (!AchievementChargeService.NOT_ADD_UNSPECIFIED_SECONNDARY_CLASSIFY_LIST.contains(fee1)) {
                    setFeeType2(fee, detail, customTypeMap, dto);
                }
                List<AchievementChargeDataFee> fees = feeTypes.get(sourceLevelOne);
                if (fees != null) {
                    fees.add(fee);
                } else {
                    List<AchievementChargeDataFee> feeList = new ArrayList<>();
                    feeList.add(fee);
                    feeTypes.put(sourceLevelOne, feeList);
                }
            }
        }
        return feeTypes;
    }

    /**
     * 设置一级费用分类
     *
     * @param fee           fee
     * @param detail        detail
     * @param customTypeMap customTypeMap
     * @param dto           -
     */
    public void setFeeType2(AchievementChargeDataFee fee,
                            AchievementRecommendPersonalFeeAmountEntity detail,
                            Map<Integer, V2GoodsCustomType> customTypeMap,
                            StatConfigDto dto) {
        V2GoodsCustomType customType = customTypeMap.get(detail.getClassifyLevel2());
        if (customType != null) {
            fee.setName(customType.getName());
        } else {
            fee.setName("未指定");
        }
        fee.setField(detail.getClassifyLevel1() + "/" + detail.getClassifyLevel2());
        fee.setOrigin(fee.getOrigin() != null ? fee.getOrigin() : BigDecimal.ZERO);
        fee.setValue(fee.getValue() != null ? fee.getValue() : BigDecimal.ZERO);
        fee.setGross(fee.getValue().subtract(fee.getCost()));
        // 计提金额
        fee.setCommissionAmt(StatConfigHandler.getCommissionAmount(dto, fee.getOrigin(),
                fee.getValue(), null, fee.getDeduct()));
    }

    /**
     * 设置一级费用分类
     *
     * @param fee    fee
     * @param detail detail
     * @param dto    -
     */
    public void setFeeType1(AchievementChargeDataFee fee,
                            AchievementRecommendPersonalFeeAmountEntity detail,
                            StatConfigDto dto, String hisType) {
        fee.setName(query.queryProductClassifyLevel1(dto.getChainId(), detail.getClassifyLevel1(), hisType));
        fee.setField(detail.getClassifyLevel1());
        fee.setOrigin(detail.getOriginPrice());
        fee.setValue(detail.getReceivedPrice());
        fee.setDeduct(detail.getDeductPrice());
        fee.setGross(fee.getValue().subtract(fee.getCost()));
        // 计提金额
        fee.setCommissionAmt(StatConfigHandler.getCommissionAmount(dto, fee.getOrigin(),
                fee.getValue(), null, fee.getDeduct()));
    }

    /**
     * 人员tab设置默认值
     *
     * @param personList list
     * @param permission 权限配置
     */
    public void fillPersonnelNullValue(List<AchievementRecommendPerson> personList,
                                       AbcPermission permission) {
        for (AchievementRecommendPerson person : personList) {
            person.setDefault();
            if (!permission.isEnableCost()) {
                person.setCost(null);
            }
            if (!permission.isEnableGross()) {
                person.setGross(null);
                person.setGrossStr("");
                person.setProfit(null);
                person.setProfitStr("");
            }
        }
    }

    /**
     * 组装用法和处方的key/value
     *
     * @param name  key
     * @param value value
     * @return AchievementKeyValuePojo
     */
    public static AchievementKeyValuePojo getKeyValuePojo(String name, Object value) {
        AchievementKeyValuePojo pojo = new AchievementKeyValuePojo();
        pojo.setName(name);
        pojo.setValue(value);
        return pojo;
    }

    /**
     * 构建prescriptionList
     *
     * @param map    map
     * @param kvList list
     */
    private static void addPrescriptionToMap(Map<String, Map<String, String>> map,
                                             List<AchievementKeyValuePojo> kvList) {
        for (AchievementKeyValuePojo pojo : kvList) {
            Map<String, String> m = new HashMap<>();
            m.put("name", pojo.getName());
            m.put("value", pojo.getName());
            map.put(pojo.getName(), m);
        }
    }

}
