package cn.abc.flink.stat.service.cis.member;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HisTypeEnum;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.AbcPermission;
import cn.abc.flink.stat.common.request.params.MemberParam;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

@Component
@ApiModel("营销统计-会员统计异步导出")
public class MemberAsynExportService implements BaseAsyncExportInterface {

    @Autowired
    private MemberService service;

    @Override
    public String getKey() {
        return "promotion-member";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        return "会员统计" + beginDate + "_" + endDate + ".xlsx";
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        String chainId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "chainId"));
        String clinicId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "clinicId"));
        String beginDate = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "beginDate"));
        String endDate = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "endDate"));
        String memberTypeId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "memberTypeId"));
        String patientId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "patientId"));
        String memberCreateClinicId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "memberCreateClinicId"));
        Integer clinicType = ConvertUtils.getAsInteger(params.get("headerClinicType"));
        String headerClinicId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerClinicId"));
        String viewMode = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerViewMode"));
        String employeeId = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerEmployeeId"));
        Integer enablePatientMobile = ConvertUtils.getAsInteger(params.get("enablePatientMobile"));
        String seller = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "seller"));
        String hisType = ConvertUtils.getAsString(MapUtils.isExistsAndReturn(params, "headerHisType"));
        List<String> actionList = null;
        if (MapUtils.isExistsAndReturn(params, "actionList") != null) {
            actionList = (List<String>) MapUtils.isExistsAndReturn(params, "actionList");
        }
        MemberParam param = new MemberParam();
        param.initAbcCisBaseQueryParams(chainId, clinicId, employeeId, viewMode, clinicType);
        param.setSingleStore(viewMode, String.valueOf(clinicType));
        param.setBegindate(beginDate);
        param.setEnddate(endDate);
        param.setMemberTypeId(memberTypeId);
        param.setPatientId(patientId);
        param.setMemberCreateClinicId(memberCreateClinicId);
        param.setClinicType(clinicType);
        param.getParams().setClinicType(String.valueOf(clinicType));
        param.setViewMode(viewMode);
        param.setClinicIdByHeaderClinicId(headerClinicId);
        param.setActionList(actionList);
        param.setSeller(seller);
        param.setHisType(hisType);
        param.setSingleStore(viewMode, String.valueOf(clinicType));
        if (param.getDispensaryType().equals(HisTypeEnum.CLINIC.getTypeNumber())) {
            param.setClinicId(param.getParams().getHeaderClinicId());
        }
        AbcPermission permission = new AbcPermission(enablePatientMobile);
        param.setPermission(permission);
        List<ExcelUtils.AbcExcelSheet> sheets = service.memberAsyncExport(param);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
