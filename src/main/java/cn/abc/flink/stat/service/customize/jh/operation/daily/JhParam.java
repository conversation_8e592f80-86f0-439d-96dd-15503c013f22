package cn.abc.flink.stat.service.customize.jh.operation.daily;

import java.util.List;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.customize.jh.operation.daily
 * @Author: zs
 * @CreateTime: 2022-08-22  17:40
 * @Description: 济华接口参数实体类
 * @Version: 1.0
 */
public class JhParam {

    private String chainId;
    private String clinicId;
    private String beginDate;
    private String endDate;


    //selectGoodsRevenue方法参数1
    private List<String> goodsIds;
    private List<String> doctorIds;
    private List<String> sellerIds;
    private List<String> externalDoctorIds;
    private List<String> specialDoctorIds;
    private Boolean isFlat;

    //selectRevenueDataWithFeeClass方法参数
    private String feeType1;
    private String feeType2;
    //    private List<String> doctorIds;
//    private List<String> sellerIds;
    private List<String> employeeIds;

    //selectGaoFangJhRevenue方法参数
    private List<String> fullOintmentGoodsIds;
    private List<String> halfOintmentGoodsIds;
    private List<String> auxiliaryOintmentGoodsIds;
    private List<String> processingFeeOintment;

    //selectRevisitStat方法参数
    String revisitDimension;
    String dateDimension;
    //String doctorIds;
    Integer pageindex;
    Integer pagesize;
    String departIds;

    //
    private String revisitCol;
    private String doctorIdIn;
    private String deptIds;
    private String dateSql;

    //selectVisitSource方法参数
//    private String revisitDimension;
//    private String dateDimension;
    private Integer isRevisit;
    private String sourceIds;

    private List<String> departmentIds;

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public List<String> getGoodsIds() {
        return this.goodsIds;
    }

    public List<String> getDoctorIds() {
        return this.doctorIds;
    }

    public List<String> getSellerIds() {
        return this.sellerIds;
    }

    public List<String> getExternalDoctorIds() {
        return this.externalDoctorIds;
    }

    public List<String> getSpecialDoctorIds() {
        return this.specialDoctorIds;
    }

    public Boolean getIsFlat() {
        return this.isFlat;
    }

    public String getFeeType1() {
        return this.feeType1;
    }

    public String getFeeType2() {
        return this.feeType2;
    }

    public List<String> getEmployeeIds() {
        return this.employeeIds;
    }

    public List<String> getFullOintmentGoodsIds() {
        return this.fullOintmentGoodsIds;
    }

    public List<String> getHalfOintmentGoodsIds() {
        return this.halfOintmentGoodsIds;
    }

    public List<String> getAuxiliaryOintmentGoodsIds() {
        return this.auxiliaryOintmentGoodsIds;
    }

    public List<String> getProcessingFeeOintment() {
        return this.processingFeeOintment;
    }

    public String getRevisitCol() {
        return this.revisitCol;
    }

    public String getDoctorIdIn() {
        return this.doctorIdIn;
    }

    public String getDeptIds() {
        return this.deptIds;
    }

    public String getDateSql() {
        return this.dateSql;
    }

    public String getRevisitDimension() {
        return this.revisitDimension;
    }

    public String getDateDimension() {
        return this.dateDimension;
    }

    public Integer getIsRevisit() {
        return this.isRevisit;
    }

    public String getSourceIds() {
        return this.sourceIds;
    }

    public List<String> getDepartmentIds() {
        return this.departmentIds;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setGoodsIds(List<String> goodsIds) {
        this.goodsIds = goodsIds;
    }

    public void setDoctorIds(List<String> doctorIds) {
        this.doctorIds = doctorIds;
    }

    public void setSellerIds(List<String> sellerIds) {
        this.sellerIds = sellerIds;
    }

    public void setExternalDoctorIds(List<String> externalDoctorIds) {
        this.externalDoctorIds = externalDoctorIds;
    }

    public void setSpecialDoctorIds(List<String> specialDoctorIds) {
        this.specialDoctorIds = specialDoctorIds;
    }

    public void setIsFlat(Boolean isFlat) {
        this.isFlat = isFlat;
    }

    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public void setFeeType2(String feeType2) {
        this.feeType2 = feeType2;
    }

    public void setEmployeeIds(List<String> employeeIds) {
        this.employeeIds = employeeIds;
    }

    public void setFullOintmentGoodsIds(List<String> fullOintmentGoodsIds) {
        this.fullOintmentGoodsIds = fullOintmentGoodsIds;
    }

    public void setHalfOintmentGoodsIds(List<String> halfOintmentGoodsIds) {
        this.halfOintmentGoodsIds = halfOintmentGoodsIds;
    }

    public void setAuxiliaryOintmentGoodsIds(List<String> auxiliaryOintmentGoodsIds) {
        this.auxiliaryOintmentGoodsIds = auxiliaryOintmentGoodsIds;
    }

    public void setProcessingFeeOintment(List<String> processingFeeOintment) {
        this.processingFeeOintment = processingFeeOintment;
    }

    public void setRevisitCol(String revisitCol) {
        this.revisitCol = revisitCol;
    }

    public void setDoctorIdIn(String doctorIdIn) {
        this.doctorIdIn = doctorIdIn;
    }

    public void setDeptIds(String deptIds) {
        this.deptIds = deptIds;
    }

    public void setDateSql(String dateSql) {
        this.dateSql = dateSql;
    }

    public void setRevisitDimension(String revisitDimension) {
        this.revisitDimension = revisitDimension;
    }

    public void setDateDimension(String dateDimension) {
        this.dateDimension = dateDimension;
    }

    public void setIsRevisit(Integer isRevisit) {
        this.isRevisit = isRevisit;
    }

    public void setSourceIds(String sourceIds) {
        this.sourceIds = sourceIds;
    }

    public void setDepartmentIds(List<String> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public Boolean getFlat() {
        return isFlat;
    }

    public void setFlat(Boolean flat) {
        isFlat = flat;
    }

    public Integer getPageindex() {
        return pageindex;
    }

    public void setPageindex(Integer pageindex) {
        this.pageindex = pageindex;
    }

    public Integer getPagesize() {
        return pagesize;
    }

    public void setPagesize(Integer pagesize) {
        this.pagesize = pagesize;
    }

    public String getDepartIds() {
        return departIds;
    }

    public void setDepartIds(String departIds) {
        this.departIds = departIds;
    }
}
