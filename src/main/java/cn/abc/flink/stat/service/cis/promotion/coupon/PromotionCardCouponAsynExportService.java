package cn.abc.flink.stat.service.cis.promotion.coupon;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.common.request.params.PromotionCardParam;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-01-05 18:51
 **/
@Component
public class PromotionCardCouponAsynExportService implements BaseAsyncExportInterface {
    private static final Logger LOGGER = LoggerFactory.getLogger(PromotionCardCouponAsynExportService.class);
    @Autowired
    private PromotionCardCouponService service;

    @Override
    public String getKey() {
        return "promotion-discount";
    }

    @Override
    public String setFileName(Map<String, Object> params) {
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        if (beginDate != null) {
            beginDate = TimeUtils.appendBegin(beginDate);
        }
        if (endDate != null) {
            endDate = TimeUtils.appendEnd(endDate);
        }
        String fileName = "优惠券统计" + beginDate.substring(0, CommonConstants.DATE_LENGTH) + "_"
                + endDate.substring(0, CommonConstants.DATE_LENGTH) + ".xlsx";
        return fileName;
    }

    @Override
    public OutputStream export(Map<String, Object> params) throws Exception {
        LOGGER.info("优惠券统计异步导出key：promotion-discount 参数:{}", JSON.toJSONString(params));
        String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
        String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
        String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginDate");
        String endDate = (String) MapUtils.isExistsAndReturn(params, "endDate");
        Object promotionIdObj = MapUtils.isExistsAndReturn(params, "promotionId");
        String headerEmployeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
        Long promotionId = null;
        if (promotionIdObj != null) {
            String promotionIdStr = promotionIdObj.toString();
            if (!"".equals(promotionIdStr)) {
                promotionId = Long.parseLong(promotionIdStr);
            }
        }
        String patientId = (String) MapUtils.isExistsAndReturn(params, "patientId");
        Integer chargeType = null;
        Object chargeTypeObj = MapUtils.isExistsAndReturn(params, "chargeType");
        if (chargeTypeObj != null) {
            String chargeTypeStr = chargeTypeObj.toString();
            if (!"".equals(chargeTypeStr)) {
                chargeType = new Double(chargeTypeStr).intValue();
            }
        }
        if (beginDate != null) {
            beginDate = TimeUtils.appendBegin(beginDate);
        }
        if (endDate != null) {
            endDate = TimeUtils.appendEnd(endDate);
        }
        Integer clinicType = Double.valueOf((double) params.get("headerClinicType")).intValue();
        String viewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
        PromotionCardParam param = new PromotionCardParam();
        param.initAbcCisBaseQueryParams(chainId, clinicId, headerEmployeeId, viewMode, clinicType);
        param.setBeginDate(beginDate);
        param.setEndDate(endDate);
        param.setPromotionId(promotionId);
        param.setPatientId(patientId);
        param.setChargeType(chargeType);
        List<ExcelUtils.AbcExcelSheet> sheets = service.asynExportCoupon(param);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ExcelUtils.export(baos, sheets);
        return baos;
    }
}
