package cn.abc.flink.stat.service.customize.revenue.entity;



import java.math.BigDecimal;
/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/23 11:08
 */
public class RevenueDataWithDepartmentDao {
    private String clinicId;
    private String created;

    private BigDecimal amount;
    private BigDecimal ptAmount; // 理疗师开单
    private BigDecimal doctorAmount; // 医生开单

    public String getClinicId() {
        return this.clinicId;
    }

    public String getCreated() {
        return this.created;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public BigDecimal getPtAmount() {
        return this.ptAmount;
    }

    public BigDecimal getDoctorAmount() {
        return this.doctorAmount;
    }


    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setPtAmount(BigDecimal ptAmount) {
        this.ptAmount = ptAmount;
    }

    public void setDoctorAmount(BigDecimal doctorAmount) {
        this.doctorAmount = doctorAmount;
    }

}
