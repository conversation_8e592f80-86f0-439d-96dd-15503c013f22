package cn.abc.flink.stat.service.cis.achievement.charge.pojos;

import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import io.swagger.annotations.ApiModelProperty;


/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/25 11:04
 */
public class AchievementChargeAppParams extends AbcScStatRequestParams {
	@ApiModelProperty(value = "人员id，不用传")
	private String employeeId;

	@ApiModelProperty(value = "科室id，用户点击某个科室后，查询某个科室的数据")
	private String departmentId;

	@ApiModelProperty(hidden = true)
	private Integer commissionType;

	@ApiModelProperty(hidden = true)
	private Integer arrearsCommissionTiming;

	public void initStatConfig(StatConfigDto statConfigDto) {
		this.commissionType = statConfigDto.getCommissionType();
		this.arrearsCommissionTiming = statConfigDto.getArrearsCommissionTiming();
	}

    public String getEmployeeId() {
        return this.employeeId;
    }

    public String getDepartmentId() {
        return this.departmentId;
    }

    public Integer getCommissionType() {
        return this.commissionType;
    }

    public Integer getArrearsCommissionTiming() {
        return this.arrearsCommissionTiming;
    }


    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public void setCommissionType(Integer commissionType) {
        this.commissionType = commissionType;
    }

    public void setArrearsCommissionTiming(Integer arrearsCommissionTiming) {
        this.arrearsCommissionTiming = arrearsCommissionTiming;
    }

}
