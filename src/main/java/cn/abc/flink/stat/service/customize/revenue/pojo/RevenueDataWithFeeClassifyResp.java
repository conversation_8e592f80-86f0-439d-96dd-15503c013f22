package cn.abc.flink.stat.service.customize.revenue.pojo;

import cn.abc.flink.stat.service.cis.selection.pojo.FeeClassifyResp;


import java.util.ArrayList;
import java.util.List;
/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/23 11:10
 */
public class RevenueDataWithFeeClassifyResp {
    private List<RevenueDataWithFeeClassifyDto> fees;
    private List<FeeClassifyResp> feeClassifyList;

    public RevenueDataWithFeeClassifyResp() {
        fees = new ArrayList<>();
        feeClassifyList = new ArrayList<>();
    }

    public RevenueDataWithFeeClassifyResp(List<RevenueDataWithFeeClassifyDto> fees,
                                          List<FeeClassifyResp> feeClassifyList) {
        this.fees = fees;
        this.feeClassifyList = feeClassifyList;
    }

    public List<RevenueDataWithFeeClassifyDto> getFees() {
        return this.fees;
    }

    public List<FeeClassifyResp> getFeeClassifyList() {
        return this.feeClassifyList;
    }


    public void setFees(List<RevenueDataWithFeeClassifyDto> fees) {
        this.fees = fees;
    }

    public void setFeeClassifyList(List<FeeClassifyResp> feeClassifyList) {
        this.feeClassifyList = feeClassifyList;
    }

}
