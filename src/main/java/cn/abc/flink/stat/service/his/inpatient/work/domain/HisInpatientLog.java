package cn.abc.flink.stat.service.his.inpatient.work.domain;

import cn.abc.flink.stat.common.constants.CommonConstants;
import cn.abc.flink.stat.dimension.domain.Department;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.service.cis.handler.PatientHandler;
import io.swagger.annotations.ApiModelProperty;

import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: 住院医务统计-住院日志
 */
public class HisInpatientLog {
    @ApiModelProperty(value = "hospital_extend主键id", hidden = true)
    private String id;

    @ApiModelProperty(value = "入院日期")
    private String hospitalAdmissionDate;

    @ApiModelProperty(value = "出院日期")
    private String hospitalDischargeDate;

    @ApiModelProperty(value = "结算日期")
    private String chargeSettleDate;

    @ApiModelProperty(value = "住院号")
    private String hospitalizationNumber;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "年龄")
    private String age;

    @ApiModelProperty(value = "身份证号")
    private String identificationNumber;

    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "职业")
    private String occupation;

    @ApiModelProperty(value = "体重")
    private String weight;

    @ApiModelProperty("民族")
    private String ethnicity;

    @ApiModelProperty("身高")
    private String height;

    @ApiModelProperty("国籍")
    private String nationality;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("人群分类")
    private String crowdCategory;

    @ApiModelProperty(value = "家长姓名")
    private String parentName;

    @ApiModelProperty(value = "工作单位")
    private String company;

    @ApiModelProperty(value = "入院方式")
    private String admissionMethod;

    @ApiModelProperty(value = "入院病情")
    private String admissionCondition;

    @ApiModelProperty(value = "入院途径")
    private String admissionPathway;

    @ApiModelProperty(value = "出院原因")
    private String dischargeReason;

    @ApiModelProperty(value = "主管医生ID")
    private String doctorId;

    @ApiModelProperty(value = "主管医生")
    private String doctorName;

    @ApiModelProperty(value = "主管护士ID")
    private String attendingNurseId;

    @ApiModelProperty(value = "主管护士")
    private String attendingNurse;

    @ApiModelProperty(value = "科室ID")
    private String departmentId;

    @ApiModelProperty(value = "科室")
    private String departmentName;

    @ApiModelProperty(value = "病区ID")
    private String wardId;

    @ApiModelProperty(value = "病区")
    private String wardName;

    @ApiModelProperty(value = "床号")
    private String bedNo;

    @ApiModelProperty(value = "初步诊断")
    private String preliminaryDiagnosis;

    @ApiModelProperty(value = "入院诊断")
    private String admissionDiagnosis;

    @ApiModelProperty(value = "出院诊断")
    private String dischargeDiagnosis;

    @ApiModelProperty(value = "费别")
    private String paymentCategory;

    @ApiModelProperty(value = "费用")
    private BigDecimal costPrice;

    public void pretty() {
        if (StringUtils.isEmpty(this.hospitalAdmissionDate)) {
            this.hospitalAdmissionDate = "-";
        } else {
            this.hospitalAdmissionDate = this.hospitalAdmissionDate.substring(0, CommonConstants.NUMBER_SIXTEEN);
        }
        if (StringUtils.isEmpty(this.hospitalDischargeDate)) {
            this.hospitalDischargeDate = "-";
        } else {
            this.hospitalDischargeDate = this.hospitalDischargeDate.substring(0, CommonConstants.NUMBER_SIXTEEN);
        }
        if (StringUtils.isEmpty(this.chargeSettleDate)) {
            this.chargeSettleDate = "-";
        } else {
            this.chargeSettleDate = this.chargeSettleDate.substring(0, CommonConstants.NUMBER_SIXTEEN);
        }
        if (StringUtils.isEmpty(this.hospitalizationNumber)) {
            this.hospitalizationNumber = "-";
        } else {
            this.hospitalizationNumber = String.format("%06d", Long.valueOf(this.hospitalizationNumber));
        }
        if (StringUtils.isEmpty(this.patientName)) {
            this.patientName = "-";
        }
        if (StringUtils.isEmpty(this.patientId)) {
            this.patientId = "-";
        }
        if (StringUtils.isEmpty(this.sex)) {
            this.sex = "-";
        }
        if (StringUtils.isEmpty(this.age)) {
            this.age = "-";
        }
        if (StringUtils.isEmpty(this.identificationNumber)) {
            this.identificationNumber = "-";
        }
        if (StringUtils.isEmpty(this.phoneNumber)) {
            this.phoneNumber = "-";
        }
        if (StringUtils.isEmpty(this.address)) {
            this.address = "-";
        }
        if (StringUtils.isEmpty(this.occupation)) {
            this.occupation = "-";
        }
        if (StringUtils.isEmpty(this.weight)) {
            this.weight = "-";
        }
        if (StringUtils.isEmpty(this.parentName)) {
            this.parentName = "-";
        }
        if (StringUtils.isEmpty(this.company)) {
            this.company = "-";
        }
        if (StringUtils.isEmpty(this.admissionMethod)) {
            this.admissionMethod = "-";
        } else {
            switch (this.admissionMethod) {
                case "1":
                    this.admissionMethod = "步行";
                    break;
                case "2":
                    this.admissionMethod = "扶助";
                    break;
                case "3":
                    this.admissionMethod = "轮椅";
                    break;
                case "4":
                    this.admissionMethod = "平车";
                    break;
                default:
                    this.admissionMethod = "-";
                    break;
            }
        }
        if (StringUtils.isEmpty(this.admissionCondition)) {
            this.admissionCondition = "-";
        } else {
            switch (this.admissionCondition) {
                case "1":
                    this.admissionCondition = "病危";
                    break;
                case "2":
                    this.admissionCondition = "病重";
                    break;
                case "3":
                    this.admissionCondition = "普通病情";
                    break;
                default:
                    this.admissionCondition = "-";
                    break;
            }
        }
        if (StringUtils.isEmpty(this.admissionPathway)) {
            this.admissionPathway = "-";
        } else {
            switch (this.admissionPathway) {
                case "1":
                    this.admissionPathway = "急诊";
                    break;
                case "2":
                    this.admissionPathway = "门诊";
                    break;
                case "3":
                    this.admissionPathway = "其他医疗机构转入";
                    break;
                case "4":
                    this.admissionPathway = "其他";
                    break;
                default:
                    this.admissionPathway = "-";
                    break;
            }
        }
        if (StringUtils.isEmpty(this.dischargeReason)) {
            this.dischargeReason = "-";
        }
        if (StringUtils.isEmpty(this.doctorId)) {
            this.doctorId = "-";
        }
        if (StringUtils.isEmpty(this.doctorName)) {
            this.doctorName = "-";
        }
        if (StringUtils.isEmpty(this.attendingNurseId)) {
            this.attendingNurseId = "-";
        }
        if (StringUtils.isEmpty(this.attendingNurse)) {
            this.attendingNurse = "-";
        }
        if (StringUtils.isEmpty(this.departmentId)) {
            this.departmentId = "-";
        }
        if (StringUtils.isEmpty(this.departmentName)) {
            this.departmentName = "-";
        }
        if (StringUtils.isEmpty(this.wardId)) {
            this.wardId = "-";
        }
        if (StringUtils.isEmpty(this.wardName)) {
            this.wardName = "-";
        }
        if (StringUtils.isEmpty(this.bedNo)) {
            this.bedNo = "-";
        }
        if (StringUtils.isEmpty(this.preliminaryDiagnosis)) {
            this.preliminaryDiagnosis = "-";
        }
        if (StringUtils.isEmpty(this.admissionDiagnosis)) {
            this.admissionDiagnosis = "-";
        }
        if (StringUtils.isEmpty(this.dischargeDiagnosis)) {
            this.dischargeDiagnosis = "-";
        }
        if (StringUtils.isEmpty(this.paymentCategory)) {
            this.paymentCategory = "自费";
        }
        if (this.costPrice == null) {
            this.costPrice = BigDecimal.valueOf(0);
        }
    }

    /**
     * 设置医生名字
     *
     * @param map -
     */
    public void setEmployeeMessage(Map<String, Employee> map) {
        Employee doctor = map.get(this.getDoctorId());
        Employee nurse = map.get(this.getAttendingNurseId());
        if (doctor != null) {
            this.doctorName = doctor.getName();
        }
        if (nurse != null) {
            this.attendingNurse = nurse.getName();
        }
    }

    /**
     * 设置患者
     *
     * @param map -
     */
    public void setPatientMessage(Map<String, V2Patient> map) {
        V2Patient v2Patient = map.get(this.getPatientId());
        if (v2Patient != null) {
            this.patientName = v2Patient.getName();
            this.age = PatientHandler.getBirthdayByV2Patient(v2Patient);
            this.sex = v2Patient.getSex();
            this.identificationNumber = v2Patient.getIdCard();
            this.phoneNumber = v2Patient.getMobile();
            this.address = ((v2Patient.getAddressProvinceName() == null ? "" : v2Patient.getAddressProvinceName())
                    + (v2Patient.getAddressCityName() == null ? "" : v2Patient.getAddressCityName())
                    + (v2Patient.getAddressDistrictName() == null ? "" : v2Patient.getAddressDistrictName())
                    + (v2Patient.getAddressDetail() == null ? "" : v2Patient.getAddressDetail())).replace("null", "");
            this.occupation = v2Patient.getProfession();
            this.weight = v2Patient.getWeight() == null ? "-" : v2Patient.getWeight().toString();
            this.parentName = v2Patient.getContactName();
            this.company = v2Patient.getCompany();
            this.ethnicity = v2Patient.getEthnicity();
            this.height = v2Patient.getHeight() == null ? "-":v2Patient.getHeight().setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            this.nationality = v2Patient.getNationality();
            this.email = v2Patient.getEmail();
            this.crowdCategory = v2Patient.getCrowdCategory();
        }
    }

    /**
     * 设置科室
     *
     * @param map -
     */
    public void setDepartmentMessage(Map<String, Department> map) {
        Department department = map.get(this.getDepartmentId());
        if (department != null) {
            this.departmentName = department.getName();
        } else {
            this.departmentName = "-";
        }
    }

    /**
     * 设置病区
     *
     * @param map -
     */
    public void setWardMessage(Map<String, String> map) {
        this.wardName = map.get(this.getWardId());
        if (this.wardName == null) {
            this.wardName = "-";
        }
    }

    public String getId() {
        return this.id;
    }

    public String getHospitalAdmissionDate() {
        return this.hospitalAdmissionDate;
    }

    public String getHospitalDischargeDate() {
        return this.hospitalDischargeDate;
    }

    public String getChargeSettleDate() {
        return this.chargeSettleDate;
    }

    public String getHospitalizationNumber() {
        return this.hospitalizationNumber;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getSex() {
        return this.sex;
    }

    public String getAge() {
        return this.age;
    }

    public String getIdentificationNumber() {
        return this.identificationNumber;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public String getAddress() {
        return this.address;
    }

    public String getOccupation() {
        return this.occupation;
    }

    public String getWeight() {
        return this.weight;
    }

    public String getParentName() {
        return this.parentName;
    }

    public String getCompany() {
        return this.company;
    }

    public String getAdmissionMethod() {
        return this.admissionMethod;
    }

    public String getAdmissionCondition() {
        return this.admissionCondition;
    }

    public String getAdmissionPathway() {
        return this.admissionPathway;
    }

    public String getDischargeReason() {
        return this.dischargeReason;
    }

    public String getDoctorId() {
        return this.doctorId;
    }

    public String getDoctorName() {
        return this.doctorName;
    }

    public String getAttendingNurseId() {
        return this.attendingNurseId;
    }

    public String getAttendingNurse() {
        return this.attendingNurse;
    }

    public String getDepartmentId() {
        return this.departmentId;
    }

    public String getDepartmentName() {
        return this.departmentName;
    }

    public String getWardId() {
        return this.wardId;
    }

    public String getWardName() {
        return this.wardName;
    }

    public String getBedNo() {
        return this.bedNo;
    }

    public String getPreliminaryDiagnosis() {
        return this.preliminaryDiagnosis;
    }

    public String getAdmissionDiagnosis() {
        return this.admissionDiagnosis;
    }

    public String getDischargeDiagnosis() {
        return this.dischargeDiagnosis;
    }

    public String getPaymentCategory() {
        return this.paymentCategory;
    }

    public BigDecimal getCostPrice() {
        return this.costPrice;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setHospitalAdmissionDate(String hospitalAdmissionDate) {
        this.hospitalAdmissionDate = hospitalAdmissionDate;
    }

    public void setHospitalDischargeDate(String hospitalDischargeDate) {
        this.hospitalDischargeDate = hospitalDischargeDate;
    }

    public void setChargeSettleDate(String chargeSettleDate) {
        this.chargeSettleDate = chargeSettleDate;
    }

    public void setHospitalizationNumber(String hospitalizationNumber) {
        this.hospitalizationNumber = hospitalizationNumber;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public void setIdentificationNumber(String identificationNumber) {
        this.identificationNumber = identificationNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public void setAdmissionMethod(String admissionMethod) {
        this.admissionMethod = admissionMethod;
    }

    public void setAdmissionCondition(String admissionCondition) {
        this.admissionCondition = admissionCondition;
    }

    public void setAdmissionPathway(String admissionPathway) {
        this.admissionPathway = admissionPathway;
    }

    public void setDischargeReason(String dischargeReason) {
        this.dischargeReason = dischargeReason;
    }

    public void setDoctorId(String doctorId) {
        this.doctorId = doctorId;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public void setAttendingNurseId(String attendingNurseId) {
        this.attendingNurseId = attendingNurseId;
    }

    public void setAttendingNurse(String attendingNurse) {
        this.attendingNurse = attendingNurse;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public void setWardId(String wardId) {
        this.wardId = wardId;
    }

    public void setWardName(String wardName) {
        this.wardName = wardName;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    public void setPreliminaryDiagnosis(String preliminaryDiagnosis) {
        this.preliminaryDiagnosis = preliminaryDiagnosis;
    }

    public void setAdmissionDiagnosis(String admissionDiagnosis) {
        this.admissionDiagnosis = admissionDiagnosis;
    }

    public void setDischargeDiagnosis(String dischargeDiagnosis) {
        this.dischargeDiagnosis = dischargeDiagnosis;
    }

    public void setPaymentCategory(String paymentCategory) {
        this.paymentCategory = paymentCategory;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public String getEthnicity() {
        return ethnicity;
    }

    public void setEthnicity(String ethnicity) {
        this.ethnicity = ethnicity;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCrowdCategory() {
        return crowdCategory;
    }

    public void setCrowdCategory(String crowdCategory) {
        this.crowdCategory = crowdCategory;
    }
}
