package cn.abc.flink.stat.service.es.achievement.entity;


//import org.springframework.data.elasticsearch.annotations.Document;

//@Document(indexName = "dev.d_v1_dwd_charge_20000101_21000101_a",type = "doc")
public class AchievementVoucher {

    private String id;

    private String chain_id;

    private String v2ct_id;

    private String clinic_id;

    private String personnel;

    private String created;

    private String copywriter_id;

    private String feeType;

    private Double charge_record_amount;

    private String patient_id;

    private String patient_name;

    private String secondClassfy;

    private String c_m_spec;




    public String getId() {
        return this.id;
    }

    public String getChain_id() {
        return this.chain_id;
    }

    public String getV2ct_id() {
        return this.v2ct_id;
    }

    public String getClinic_id() {
        return this.clinic_id;
    }

    public String getPersonnel() {
        return this.personnel;
    }

    public String getCreated() {
        return this.created;
    }

    public String getCopywriter_id() {
        return this.copywriter_id;
    }

    public String getFeeType() {
        return this.feeType;
    }

    public Double getCharge_record_amount() {
        return this.charge_record_amount;
    }

    public String getPatient_id() {
        return this.patient_id;
    }

    public String getPatient_name() {
        return this.patient_name;
    }

    public String getSecondClassfy() {
        return this.secondClassfy;
    }

    public String getC_m_spec() {
        return this.c_m_spec;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setChain_id(String chain_id) {
        this.chain_id = chain_id;
    }

    public void setV2ct_id(String v2ct_id) {
        this.v2ct_id = v2ct_id;
    }

    public void setClinic_id(String clinic_id) {
        this.clinic_id = clinic_id;
    }

    public void setPersonnel(String personnel) {
        this.personnel = personnel;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public void setCopywriter_id(String copywriter_id) {
        this.copywriter_id = copywriter_id;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public void setCharge_record_amount(Double charge_record_amount) {
        this.charge_record_amount = charge_record_amount;
    }

    public void setPatient_id(String patient_id) {
        this.patient_id = patient_id;
    }

    public void setPatient_name(String patient_name) {
        this.patient_name = patient_name;
    }

    public void setSecondClassfy(String secondClassfy) {
        this.secondClassfy = secondClassfy;
    }

    public void setC_m_spec(String c_m_spec) {
        this.c_m_spec = c_m_spec;
    }

}
