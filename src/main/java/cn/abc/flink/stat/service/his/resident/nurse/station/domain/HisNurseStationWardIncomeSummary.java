package cn.abc.flink.stat.service.his.resident.nurse.station.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description: 医院统计-住护士生站-病区收入-日期汇总
 * @author: lzq
 * @Date: 2023/12/13 14:42
 */
@ToString
@ApiModel("医院统计-住院医生站-病区收入-日期汇总")
public class HisNurseStationWardIncomeSummary {

    @ApiModelProperty(value = "日期")
    private String summaryDate;

    @ApiModelProperty(value = "金额")
    private BigDecimal feeTypesTotalAmount;

    public String getSummaryDate() {
        return this.summaryDate;
    }

    public BigDecimal getFeeTypesTotalAmount() {
        return this.feeTypesTotalAmount;
    }


    public void setSummaryDate(String summaryDate) {
        this.summaryDate = summaryDate;
    }

    public void setFeeTypesTotalAmount(BigDecimal feeTypesTotalAmount) {
        this.feeTypesTotalAmount = feeTypesTotalAmount;
    }

}
