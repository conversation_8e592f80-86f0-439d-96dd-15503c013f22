package cn.abc.flink.stat.service.his.achievement.charge.domain;



import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/27 5:43 下午
 */
public class HisAchievementAdviceExecuteEmployeeSummary {
    private Long count;
    private BigDecimal totalAmount;

    public Long getCount() {
        return this.count;
    }

    public BigDecimal getTotalAmount() {
        return this.totalAmount;
    }


    public void setCount(Long count) {
        this.count = count;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

}
