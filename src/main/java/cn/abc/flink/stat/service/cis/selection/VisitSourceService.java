package cn.abc.flink.stat.service.cis.selection;

import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnSelectionMapper;
import cn.abc.flink.stat.db.cis.common.SelectionMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresSelectionMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.V2PatientorderVisitSource;
import cn.abc.flink.stat.service.cis.selection.entity.VisitSourceDao;
import cn.abc.flink.stat.service.cis.selection.pojo.VisitSourceResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class VisitSourceService {
    private Logger logger = LoggerFactory.getLogger(VisitSourceService.class);

    @Resource
    private HologresSelectionMapper selectionMapper;

    @Resource
    private ArnSelectionMapper arnMapper;

    @Autowired
    private StoreUtils storeUtils;

    @Autowired
    private DimensionQuery query;

    @Deprecated
    public List<VisitSourceResp> selectVisitSource (String chainId, String clinicId, String beginDate, String endDate) {
        List<VisitSourceDao> list = getData(beginDate, endDate).selectVisitSourceByTransactionRecord(TableUtils.getCisTable(), chainId, clinicId, beginDate, endDate);
        Map<String, V2PatientorderVisitSource> visitSourceMap = query.queryVisitSourceByChainId(chainId);
        Map<String, VisitSourceResp> map = new HashMap<>();
        list.stream().forEach( dao -> {
            if (dao != null) {
                if (dao.getVisitSource1() != null) {
                    if (map.containsKey(dao.getVisitSource1())) {
                        if (dao.getVisitSource2() != null) {
                            VisitSourceResp resp = map.get(dao.getVisitSource1());
                            resp.getChildren().add(buildKeyValue(dao.getVisitSource2(), visitSourceMap));
                        }
                    } else {
                        VisitSourceResp resp = new VisitSourceResp();
                        resp.setId(dao.getVisitSource1());
                        V2PatientorderVisitSource vs = visitSourceMap.getOrDefault(dao.getVisitSource1(), null);
                        if (vs != null) {
                            resp.setName(vs.getName());
                        } else {
                            resp.setName("未指定");
                        }
                        if (dao.getVisitSource2() != null) {
                            resp.getChildren().add(buildKeyValue(dao.getVisitSource2(), visitSourceMap));
                        }
                        map.put(dao.getVisitSource1(), resp);
                    }
                }
            }
        });
        return new ArrayList<>(map.values());
    }

    private Map<String, String> buildKeyValue (String visitSource, Map<String, V2PatientorderVisitSource> visitSourceMap) {
        Map<String, String> kv = new HashMap<>();
        kv.put("id", visitSource);
        V2PatientorderVisitSource vs = visitSourceMap.getOrDefault(visitSource, null);
        if (vs != null) {
            kv.put("name", vs.getName());
        } else {
            kv.put("name", "未指定");
        }
        return kv;
    }

    private SelectionMapper getData (String beginDate, String endDate) {
        boolean isuseKudu = storeUtils.isUseKudu(beginDate, endDate);

        if ( isuseKudu ) {
            return selectionMapper;
        }
        return arnMapper;
    }
}
