package cn.abc.flink.stat.service.cis.goods.outinventory.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

@ApiModel("领用筛选框实体")
public class CollectSelectionRsp {

    @ApiModelProperty("出库库房list")
    private List<Map<String, Object>> outPharmacyList;

    @ApiModelProperty("入库房list")
    private List<Map<String, Object>> inPharmacyList;

    @ApiModelProperty("入库科室list")
    private List<Map<String, Object>> inDepartmentList;

    public List<Map<String, Object>> getOutPharmacyList() {
        return outPharmacyList;
    }

    public void setOutPharmacyList(List<Map<String, Object>> outPharmacyList) {
        this.outPharmacyList = outPharmacyList;
    }

    public List<Map<String, Object>> getInPharmacyList() {
        return inPharmacyList;
    }

    public void setInPharmacyList(List<Map<String, Object>> inPharmacyList) {
        this.inPharmacyList = inPharmacyList;
    }

    public List<Map<String, Object>> getInDepartmentList() {
        return inDepartmentList;
    }

    public void setInDepartmentList(List<Map<String, Object>> inDepartmentList) {
        this.inDepartmentList = inDepartmentList;
    }
}
