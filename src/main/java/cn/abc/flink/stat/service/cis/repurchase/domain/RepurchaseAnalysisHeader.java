package cn.abc.flink.stat.service.cis.repurchase.domain;



/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-01-24 14:39
 **/
public class RepurchaseAnalysisHeader {
    private String label; // 渲染字段名
    private String prop; // 协议中字段名
    private String align; // 文本位置 ： center->居中
    private Integer width; //单元格宽度

    public RepurchaseAnalysisHeader(String label, String prop, String align, Integer width) {
        this.label = label;
        this.prop = prop;
        this.align = align;
        this.width = width;
    }

    public String getLabel() {
        return this.label;
    }

    public String getProp() {
        return this.prop;
    }

    public String getAlign() {
        return this.align;
    }

    public Integer getWidth() {
        return this.width;
    }


    public void setLabel(String label) {
        this.label = label;
    }

    public void setProp(String prop) {
        this.prop = prop;
    }

    public void setAlign(String align) {
        this.align = align;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

}
