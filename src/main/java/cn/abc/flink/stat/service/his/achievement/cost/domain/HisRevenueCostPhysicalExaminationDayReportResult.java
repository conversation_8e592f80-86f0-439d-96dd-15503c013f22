package cn.abc.flink.stat.service.his.achievement.cost.domain;

import cn.abc.flink.stat.common.contants.CommonConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;


/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.service.cis.revenue.charge.daily.entity
 * @Author: zs
 * @CreateTime: 2022-08-09  10:38
 * @Description: 住院收费日报导出结果类
 * @Version: 1.0
 */
@ColumnWidth(CommonConstants.NUMBER_TWENTY) //列宽
@ContentRowHeight(CommonConstants.NUMBER_TWENTY_EIGHT) //内容行高
@HeadRowHeight(CommonConstants.NUMBER_TWENTY_EIGHT) //头部行高
//表头背景颜色
@HeadStyle(fillForegroundColor = CommonConstants.NUMBER_FORTY_ONE)
//设置表头字段格式：字体名称，是否加粗，字号，颜色
@HeadFontStyle(fontName = "微软雅黑", bold = true, fontHeightInPoints = CommonConstants.NUMBER_TWELVE,
        color = CommonConstants.NUMBER_THIRTY)
//设置内容字段格式：字体名称，是否加粗，字号，颜色
@ContentFontStyle(fontName = "微软雅黑", bold = false, fontHeightInPoints = CommonConstants.NUMBER_TWELVE)
public class HisRevenueCostPhysicalExaminationDayReportResult {
    /**
     * 第一行
     */
    @ExcelProperty({"住院收费日月报", ""})
    private String firstLine;
    /**
     * 第二行
     */
    @ExcelProperty({"住院收费日月报", ""})
    private String secondLine;
    /**
     * 第三行
     */
    @ExcelProperty({"住院收费日月报", ""})
    private String thirdLine;
    /**
     * 第四行
     */
    @ExcelProperty({"住院收费日月报", ""})
    private String fourthLine;
    /**
     * 第五行
     */
    @ExcelProperty({"住院收费日月报", ""})
    private String fifthLine;
    /**
     * 第六行
     */
    @ExcelProperty({"住院收费日月报", ""})
    private String sixthLine;

    public HisRevenueCostPhysicalExaminationDayReportResult() {
    }

    public HisRevenueCostPhysicalExaminationDayReportResult(String firstLine, String secondLine, String thirdLine,
                                                            String fourthLine, String fifthLine, String sixthLine) {
        this.firstLine = firstLine;
        this.secondLine = secondLine;
        this.thirdLine = thirdLine;
        this.fourthLine = fourthLine;
        this.fifthLine = fifthLine;
        this.sixthLine = sixthLine;
    }

    public String getFirstLine() {
        return this.firstLine;
    }

    public String getSecondLine() {
        return this.secondLine;
    }

    public String getThirdLine() {
        return this.thirdLine;
    }

    public String getFourthLine() {
        return this.fourthLine;
    }

    public String getFifthLine() {
        return this.fifthLine;
    }

    public String getSixthLine() {
        return this.sixthLine;
    }


    public void setFirstLine(String firstLine) {
        this.firstLine = firstLine;
    }

    public void setSecondLine(String secondLine) {
        this.secondLine = secondLine;
    }

    public void setThirdLine(String thirdLine) {
        this.thirdLine = thirdLine;
    }

    public void setFourthLine(String fourthLine) {
        this.fourthLine = fourthLine;
    }

    public void setFifthLine(String fifthLine) {
        this.fifthLine = fifthLine;
    }

    public void setSixthLine(String sixthLine) {
        this.sixthLine = sixthLine;
    }

}
