package cn.abc.flink.stat.service.cis.selection.entity;

import cn.abc.flink.stat.common.EmployeeTypeEnum;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeSelectionReqParams;
import cn.abc.flink.stat.service.cis.achievement.recommend.pojos.AchievementRecommendSelectionReqParams;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.operation.domain.OperationChargeReqParams;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeSelectionReqParams;

import lombok.ToString;

/**
 * FeeClassifyParam
 */
@ToString
public class FeeClassifyParam {
    /**
     * 连锁id
     */
    protected String chainId;
    /**
     * 门店id
     */
    protected String clinicId;
    /**
     * 开始时间
     */
    protected String beginDate;

    /**
     * 结束时间
     */
    protected String endDate;
    /**
     * 是否包含代录人
     */
    private Integer includeWriter;

    /**
     * 是否包含挂号费
     */
    private Integer includeReg;

    private Integer employeeType;

    private String hisType;
    private StatConfigDto config;

    /**
     * @param params -
     * @param dto    -
     */
    public void setField1(AchievementChargeSelectionReqParams params, StatConfigDto dto) {
        this.chainId = params.getChainId();
        this.clinicId = params.getClinicId();
        this.beginDate = params.getBeginDate();
        this.endDate = params.getEndDate();
        this.includeWriter = params.getIncludeWriter();
        this.includeReg = params.getIncludeReg();
        this.employeeType = EmployeeTypeEnum.getTypeNumberByName(params.getEmployeeTypeEnum());
        this.hisType = params.getHisType();
        this.config = dto;
    }

    /**
     * @param params -
     * @param dto    -
     */
    public void setField2(AchievementRecommendSelectionReqParams params, StatConfigDto dto) {
        this.chainId = params.getChainId();
        this.clinicId = params.getClinicId();
        this.beginDate = params.getBeginDate();
        this.endDate = params.getEndDate();
        this.includeReg = params.getIncludeReg();
        this.employeeType = 1;
        this.config = dto;
    }

    /**
     * @param params -
     */
    public void setField3(OperationChargeReqParams params) {
        this.chainId = params.getChainId();
        this.clinicId = params.getClinicId();
        this.beginDate = params.getBeginDate();
        this.endDate = params.getEndDate();
        this.includeReg = params.getIncludeReg();
    }

    /**
     * @param chainId   -
     * @param clinicId  -
     * @param beginDate -
     * @param endDate   -
     */
    public void setField4(String chainId, String clinicId, String beginDate, String endDate) {
        this.chainId = chainId;
        this.clinicId = clinicId;
        this.beginDate = beginDate;
        this.endDate = endDate;
    }

    /**
     * @param params -
     */
    public void setField5(RevenueChargeSelectionReqParams params) {
        this.chainId = params.getChainId();
        this.clinicId = params.getClinicId();
        this.beginDate = params.getBeginDate();
        this.endDate = params.getEndDate();
    }

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public Integer getIncludeWriter() {
        return this.includeWriter;
    }

    public Integer getIncludeReg() {
        return this.includeReg;
    }

    public Integer getEmployeeType() {
        return this.employeeType;
    }

    public String getHisType() {
        return this.hisType;
    }

    public StatConfigDto getConfig() {
        return this.config;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setIncludeWriter(Integer includeWriter) {
        this.includeWriter = includeWriter;
    }

    public void setIncludeReg(Integer includeReg) {
        this.includeReg = includeReg;
    }

    public void setEmployeeType(Integer employeeType) {
        this.employeeType = employeeType;
    }

    public void setHisType(String hisType) {
        this.hisType = hisType;
    }

    public void setConfig(StatConfigDto config) {
        this.config = config;
    }

}
