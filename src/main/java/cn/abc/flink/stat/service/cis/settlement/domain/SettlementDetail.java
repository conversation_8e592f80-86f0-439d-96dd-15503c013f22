package cn.abc.flink.stat.service.cis.settlement.domain;

import cn.abc.flink.stat.common.ABCNumberUtils;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.V2Goods;
import cn.abc.flink.stat.service.cis.handler.GoodsHandler;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Map;

@ApiModel("库存统计-结算统计-结算明细实体")
public class SettlementDetail {

    @ApiModelProperty("供应商id")
    private String supplierId;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("结算单号")
    private String orderNo;

    @ApiModelProperty("结算类型")
    private String type;

    @ApiModelProperty("出/入库单号")
    private String stockOrderNo;

    @ApiModelProperty("出/入库时间")
    private String stockDate;

    @ApiModelProperty("出/入库人id")
    private String stockUserId;

    @ApiModelProperty("出/入库人名称")
    private String stockUserName;

    @ApiModelProperty("价税金额")
    private BigDecimal amount;

    @ApiModelProperty("价税金额str")
    private String amountText;

    @ApiModelProperty("价税金额(不含税金额)")
    private BigDecimal exTaxAmount;

    @ApiModelProperty("价税金额(不含税金额)str")
    private String exTaxAmountText;

    @ApiModelProperty("税额")
    private BigDecimal tax;

    @ApiModelProperty("税额str")
    private String taxText;

    @ApiModelProperty("结算时间")
    private String reviewDate;

    @ApiModelProperty("结算审核人id")
    private String reviewUserId;

    @ApiModelProperty("结算审核人名称")
    private String reviewUserName;

    @ApiModelProperty("结算创建人id")
    private String createdUserId;

    @ApiModelProperty("结算创建人名称")
    private String createdUserName;

    @ApiModelProperty("商品id")
    private String goodsId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("药品编码")
    private String goodsShortId;

    @ApiModelProperty("药品的规格")
    private String specification;

    @ApiModelProperty("厂家")
    private String manufacturer;

    @ApiModelProperty("生产批号")
    private String batchNo;

    @ApiModelProperty("效期")
    private String expiryDate;

    @ApiModelProperty("进价")
    private BigDecimal inPrice;

    @ApiModelProperty("进货数量(大单位)")
    private BigDecimal inPackageCount;

    @ApiModelProperty("进货数量(小单位)")
    private BigDecimal inPieceCount;

    @ApiModelProperty("进货数量")
    private String inCount;

    @ApiModelProperty("付款数量(大单位)")
    private BigDecimal settlementPackageCount;

    @ApiModelProperty("付款数量(小单位)")
    private BigDecimal settlementPieceCount;

    @ApiModelProperty("付款数量")
    private String settlementCount;

    @ApiModelProperty("出/入库门店id")
    private String stockClinicId;

    @ApiModelProperty("出/入库门店名称")
    private String stockClinicName;


    /**
     * 设置小数保留位数，页面保留2位，导出保留5位
     *
     * @param isExport isExport
     */
    public void apply(Boolean isExport) {
        amount = ABCNumberUtils.round2BigDecimal(amount);
        amountText = ABCNumberUtils.round5TextPretty(amount);
        exTaxAmount = ABCNumberUtils.round2BigDecimal(exTaxAmount);
        exTaxAmountText = ABCNumberUtils.round5TextPretty(exTaxAmount);
        tax = ABCNumberUtils.round2BigDecimal(tax);
        taxText = ABCNumberUtils.round5TextPretty(tax);

    }


    public String getSupplierId() {
        return this.supplierId;
    }

    public String getOrderNo() {
        return this.orderNo;
    }

    public String getType() {
        return this.type;
    }

    public String getStockOrderNo() {
        return this.stockOrderNo;
    }

    public String getStockDate() {
        return this.stockDate;
    }

    public String getStockUserId() {
        return this.stockUserId;
    }

    public String getStockUserName() {
        return this.stockUserName;
    }

    public BigDecimal getAmount() {
        return this.amount;
    }

    public String getAmountText() {
        return this.amountText;
    }

    public BigDecimal getExTaxAmount() {
        return this.exTaxAmount;
    }

    public String getExTaxAmountText() {
        return this.exTaxAmountText;
    }

    public BigDecimal getTax() {
        return this.tax;
    }

    public String getTaxText() {
        return this.taxText;
    }

    public String getReviewDate() {
        return this.reviewDate;
    }

    public String getReviewUserId() {
        return this.reviewUserId;
    }

    public String getReviewUserName() {
        return this.reviewUserName;
    }

    public String getCreatedUserId() {
        return this.createdUserId;
    }

    public String getCreatedUserName() {
        return this.createdUserName;
    }


    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setStockOrderNo(String stockOrderNo) {
        this.stockOrderNo = stockOrderNo;
    }

    public void setStockDate(String stockDate) {
        this.stockDate = stockDate;
    }

    public void setStockUserId(String stockUserId) {
        this.stockUserId = stockUserId;
    }

    public void setStockUserName(String stockUserName) {
        this.stockUserName = stockUserName;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setAmountText(String amountText) {
        this.amountText = amountText;
    }

    public void setExTaxAmount(BigDecimal exTaxAmount) {
        this.exTaxAmount = exTaxAmount;
    }

    public void setExTaxAmountText(String exTaxAmountText) {
        this.exTaxAmountText = exTaxAmountText;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public void setTaxText(String taxText) {
        this.taxText = taxText;
    }

    public void setReviewDate(String reviewDate) {
        this.reviewDate = reviewDate;
    }

    public void setReviewUserId(String reviewUserId) {
        this.reviewUserId = reviewUserId;
    }

    public void setReviewUserName(String reviewUserName) {
        this.reviewUserName = reviewUserName;
    }

    public void setCreatedUserId(String createdUserId) {
        this.createdUserId = createdUserId;
    }

    public void setCreatedUserName(String createdUserName) {
        this.createdUserName = createdUserName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsShortId() {
        return goodsShortId;
    }

    public void setGoodsShortId(String goodsShortId) {
        this.goodsShortId = goodsShortId;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public BigDecimal getInPrice() {
        return inPrice;
    }

    public void setInPrice(BigDecimal inPrice) {
        this.inPrice = inPrice;
    }

    public String getInCount() {
        return inCount;
    }

    public void setInCount(String inCount) {
        this.inCount = inCount;
    }

    public String getSettlementCount() {
        return settlementCount;
    }

    public String getStockClinicId() {
        return stockClinicId;
    }

    public void setStockClinicId(String stockClinicId) {
        this.stockClinicId = stockClinicId;
    }

    public String getStockClinicName() {
        return stockClinicName;
    }

    public void setStockClinicName(String stockClinicName) {
        this.stockClinicName = stockClinicName;
    }

    public BigDecimal getInPackageCount() {
        return inPackageCount;
    }

    public void setInPackageCount(BigDecimal inPackageCount) {
        this.inPackageCount = inPackageCount;
    }

    public BigDecimal getInPieceCount() {
        return inPieceCount;
    }

    public void setInPieceCount(BigDecimal inPieceCount) {
        this.inPieceCount = inPieceCount;
    }

    public BigDecimal getSettlementPackageCount() {
        return settlementPackageCount;
    }

    public void setSettlementPackageCount(BigDecimal settlementPackageCount) {
        this.settlementPackageCount = settlementPackageCount;
    }

    public BigDecimal getSettlementPieceCount() {
        return settlementPieceCount;
    }

    public void setSettlementPieceCount(BigDecimal settlementPieceCount) {
        this.settlementPieceCount = settlementPieceCount;
    }

    public void setSettlementCount(String settlementCount) {
        this.settlementCount = settlementCount;
    }

    public void set(Map<String, Employee> employeeMap, Map<String, V2Goods> goodsMap, Map<String, String> organMap) {
        Employee createdUser = employeeMap.get(this.createdUserId);
        Employee reviewUser = employeeMap.get(this.reviewUserId);
        Employee stockUser = employeeMap.get(this.stockUserId);
        V2Goods g = goodsMap.get(this.goodsId);
        if (!BeanUtil.isEmpty(createdUser)) {
            this.createdUserName = createdUser.getName();
        }
        if (!BeanUtil.isEmpty(reviewUser)) {
            this.reviewUserName = reviewUser.getName();
        }
        if (!BeanUtil.isEmpty(stockUser)) {
            this.stockUserName = stockUser.getName();
        }
        if (!BeanUtil.isEmpty(g)) {
            this.goodsShortId = g.getShort_id();
            this.goodsName = GoodsHandler.handleGoodsName(g.getName(), g.getMedicine_cadn());
            this.specification = GoodsHandler.getGoodsDisplaySpec(g);
            this.manufacturer = g.getManufacturer();
            this.inCount = StrUtil.isBlank(g.getPackage_unit()) && StrUtil.isBlank(g.getPiece_unit()) ? "-"
                    : GoodsHandler.buildCountText(this.inPackageCount, g.getPackage_unit(), this.inPieceCount, g.getPiece_unit(), g.getC_m_spec());
            this.settlementCount = StrUtil.isBlank(g.getPackage_unit()) && StrUtil.isBlank(g.getPiece_unit()) ? "-"
                    : GoodsHandler.buildCountText(this.settlementPackageCount, g.getPackage_unit(), this.settlementPieceCount, g.getPiece_unit(), g.getC_m_spec());
        }
        if (!StrUtil.isBlank(organMap.get(this.stockClinicId))) {
            this.stockClinicName = organMap.get(this.stockClinicId);
        }
    }
}
