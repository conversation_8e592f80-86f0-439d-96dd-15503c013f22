package cn.abc.flink.stat.service.cis.member.domain;

import cn.abc.flink.stat.common.ABCNumberUtils;
import cn.abc.flink.stat.service.cis.achievement.dispensing.handler.NumberUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("会员流水total实体")
public class MemberFeeTotal {

    @ApiModelProperty("条数")
    private Long count;

    @ApiModelProperty("总收入金额")
    private BigDecimal totalIncomeAmount;

    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmount;

    @ApiModelProperty("退储蓄金金额")
    private BigDecimal RefundSavingsFundsAmount;

    @ApiModelProperty("消费金额")
    private BigDecimal consumeAmount;

    @ApiModelProperty("退费金额")
    private BigDecimal refundAmount;

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public BigDecimal getTotalIncomeAmount() {
        return totalIncomeAmount;
    }

    public void setTotalIncomeAmount(BigDecimal totalIncomeAmount) {
        this.totalIncomeAmount = totalIncomeAmount;
    }

    public BigDecimal getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(BigDecimal rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public BigDecimal getRefundSavingsFundsAmount() {
        return RefundSavingsFundsAmount;
    }

    public void setRefundSavingsFundsAmount(BigDecimal refundSavingsFundsAmount) {
        RefundSavingsFundsAmount = refundSavingsFundsAmount;
    }

    public BigDecimal getConsumeAmount() {
        return consumeAmount;
    }

    public void setConsumeAmount(BigDecimal consumeAmount) {
        this.consumeAmount = consumeAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public void pretty() {
        this.totalIncomeAmount = ABCNumberUtils.round2Pretty(this.totalIncomeAmount);
        this.rechargeAmount = ABCNumberUtils.round2Pretty(this.rechargeAmount);
        this.RefundSavingsFundsAmount = ABCNumberUtils.round2Pretty(this.RefundSavingsFundsAmount);
        this.consumeAmount = ABCNumberUtils.round2Pretty(this.consumeAmount);
        this.refundAmount = ABCNumberUtils.round2Pretty(this.refundAmount);
    }
}
