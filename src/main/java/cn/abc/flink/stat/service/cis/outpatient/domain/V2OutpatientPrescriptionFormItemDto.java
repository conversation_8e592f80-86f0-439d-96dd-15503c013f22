package cn.abc.flink.stat.service.cis.outpatient.domain;

import cn.abc.flink.stat.dimension.domain.V2OutpatientPrescriptionFormItem;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class V2OutpatientPrescriptionFormItemDto {




    private Byte type;

    private Byte sub_type;

    private Byte compose_type;

    private String medicine_cadn;

    private String name;

    private String specification;

    private String manufacturer;


    private Integer ast;

    private String ast_result;

    private String usage;

    private BigDecimal ivgtt;

    private String ivgtt_unit;

    private String freq;

    private String dosage;

    private String dosage_unit;

    private Integer days;

    private String special_requirement;

    private Byte dismounting;

    private BigDecimal unit_count;

    private BigDecimal expected_unit_price;

    private BigDecimal source_unit_price;

    private String unit;

    private BigDecimal unit_price;

    private BigDecimal fraction_price;

    private BigDecimal single_adjustment_price;

    private BigDecimal cost_unit_price;

    private Short sort;

    private Byte is_deleted;

    private String extend_data;

    private List<Object> acupointsList;

    /**
     * extend_data里面的externalGoodsItems
     */
    private List<Object> externalGoodsItemsList;

    public V2OutpatientPrescriptionFormItemDto() {
    }

    /**
     * 构造方法：从 V2OutpatientPrescriptionFormItem 初始化 DTO
     */
    public V2OutpatientPrescriptionFormItemDto(V2OutpatientPrescriptionFormItem item) {
        if (item == null) {
            return;
        }
        this.type = item.getType();
        this.sub_type = item.getSub_type();
        this.compose_type = item.getCompose_type();
        this.medicine_cadn = item.getMedicine_cadn();
        this.name = item.getName();
        this.specification = item.getSpecification();
        this.manufacturer = item.getManufacturer();
        this.ast = item.getAst();
        this.ast_result = item.getAst_result();
        this.usage = item.getUsage();
        this.ivgtt = item.getIvgtt();
        this.ivgtt_unit = item.getIvgtt_unit();
        this.freq = item.getFreq();
        this.dosage = item.getDosage();
        this.dosage_unit = item.getDosage_unit();
        this.days = item.getDays();
        this.special_requirement = item.getSpecial_requirement();
        this.dismounting = item.getDismounting();
        this.unit_count = item.getUnit_count();
        this.expected_unit_price = item.getExpected_unit_price();
        this.source_unit_price = item.getSource_unit_price();
        this.unit = item.getUnit();
        this.unit_price = item.getUnit_price();
        this.fraction_price = item.getFraction_price();
        this.single_adjustment_price = item.getSingle_adjustment_price();
        this.cost_unit_price = item.getCost_unit_price();
        this.sort = item.getSort();
        this.extend_data = item.getExtend_data();
        this.acupointsList = item.getAcupointsList();
        this.externalGoodsItemsList = item.getExternalGoodsItemsList();
        this.is_deleted = item.getIs_deleted();
    }

    public List<Object> getExternalGoodsItemsList() {
        return externalGoodsItemsList;
    }

    public void setExternalGoodsItemsList(List<Object> externalGoodsItemsList) {
        this.externalGoodsItemsList = externalGoodsItemsList;
    }

    public List<Object> getAcupointsList() {
        return acupointsList;
    }

    public void setAcupointsList(List<Object> acupointsList) {
        this.acupointsList = acupointsList;
    }
    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Byte getSub_type() {
        return sub_type;
    }

    public void setSub_type(Byte sub_type) {
        this.sub_type = sub_type;
    }

    public Byte getCompose_type() {
        return compose_type;
    }

    public void setCompose_type(Byte compose_type) {
        this.compose_type = compose_type;
    }

    public String getMedicine_cadn() {
        return medicine_cadn;
    }

    public void setMedicine_cadn(String medicine_cadn) {
        this.medicine_cadn = medicine_cadn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getSpecification() {
        return specification;
    }
    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getAst() {
        return ast;
    }

    public void setAst(Integer ast) {
        this.ast = ast;
    }

    public String getAst_result() {
        return ast_result;
    }

    public void setAst_result(String ast_result) {
        this.ast_result = ast_result;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }
    public BigDecimal getIvgtt() {
        return ivgtt;
    }

    public void setIvgtt(BigDecimal ivgtt) {
        this.ivgtt = ivgtt;
    }

    public String getIvgtt_unit() {
        return ivgtt_unit;
    }

    public void setIvgtt_unit(String ivgtt_unit) {
        this.ivgtt_unit = ivgtt_unit;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getDosage() {
        return dosage;
    }

    public void setDosage(String dosage) {
        this.dosage = dosage;
    }

    public String getDosage_unit() {
        return dosage_unit;
    }

    public void setDosage_unit(String dosage_unit) {
        this.dosage_unit = dosage_unit;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getSpecial_requirement() {
        return special_requirement;
    }

    public void setSpecial_requirement(String special_requirement) {
        this.special_requirement = special_requirement;
    }

    public Byte getDismounting() {
        return dismounting;
    }

    public void setDismounting(Byte dismounting) {
        this.dismounting = dismounting;
    }

    public BigDecimal getUnit_count() {
        return unit_count;
    }

    public void setUnit_count(BigDecimal unit_count) {
        this.unit_count = unit_count;
    }

    public BigDecimal getExpected_unit_price() {
        return expected_unit_price;
    }

    public void setExpected_unit_price(BigDecimal expected_unit_price) {
        this.expected_unit_price = expected_unit_price;
    }

    public BigDecimal getSource_unit_price() {
        return source_unit_price;
    }

    public void setSource_unit_price(BigDecimal source_unit_price) {
        this.source_unit_price = source_unit_price;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getUnit_price() {
        return unit_price;
    }

    public void setUnit_price(BigDecimal unit_price) {
        this.unit_price = unit_price;
    }

    public BigDecimal getFraction_price() {
        return fraction_price;
    }

    public void setFraction_price(BigDecimal fraction_price) {
        this.fraction_price = fraction_price;
    }

    public BigDecimal getSingle_adjustment_price() {
        return single_adjustment_price;
    }

    public void setSingle_adjustment_price(BigDecimal single_adjustment_price) {
        this.single_adjustment_price = single_adjustment_price;
    }

    public BigDecimal getCost_unit_price() {
        return cost_unit_price;
    }

    public void setCost_unit_price(BigDecimal cost_unit_price) {
        this.cost_unit_price = cost_unit_price;
    }

    public Short getSort() {
        return sort;
    }

    public void setSort(Short sort) {
        this.sort = sort;
    }

    public Byte getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Byte is_deleted) {
        this.is_deleted = is_deleted;
    }


    public String getExtend_data() {
        return extend_data;
    }

    public void setExtend_data(String extend_data) {
        this.extend_data = extend_data;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", type=").append(type);
        sb.append(", sub_type=").append(sub_type);
        sb.append(", compose_type=").append(compose_type);
        sb.append(", medicine_cadn=").append(medicine_cadn);
        sb.append(", name=").append(name);
        sb.append(", specification=").append(specification);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", ast=").append(ast);
        sb.append(", ast_result=").append(ast_result);
        sb.append(", usage=").append(usage);
        sb.append(", ivgtt=").append(ivgtt);
        sb.append(", ivgtt_unit=").append(ivgtt_unit);
        sb.append(", freq=").append(freq);
        sb.append(", dosage=").append(dosage);
        sb.append(", dosage_unit=").append(dosage_unit);
        sb.append(", days=").append(days);
        sb.append(", special_requirement=").append(special_requirement);
        sb.append(", dismounting=").append(dismounting);
        sb.append(", unit_count=").append(unit_count);
        sb.append(", expected_unit_price=").append(expected_unit_price);
        sb.append(", source_unit_price=").append(source_unit_price);
        sb.append(", unit=").append(unit);
        sb.append(", unit_price=").append(unit_price);
        sb.append(", fraction_price=").append(fraction_price);
        sb.append(", single_adjustment_price=").append(single_adjustment_price);
        sb.append(", cost_unit_price=").append(cost_unit_price);
        sb.append(", sort=").append(sort);
        sb.append(", is_deleted=").append(is_deleted);
        sb.append(", extend_data=").append(extend_data);
        sb.append("]");
        return sb.toString();
    }


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table v2_outpatient_prescription_form_item
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getSub_type() == null) ? 0 : getSub_type().hashCode());
        result = prime * result + ((getCompose_type() == null) ? 0 : getCompose_type().hashCode());
        result = prime * result + ((getMedicine_cadn() == null) ? 0 : getMedicine_cadn().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getSpecification() == null) ? 0 : getSpecification().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getAst() == null) ? 0 : getAst().hashCode());
        result = prime * result + ((getAst_result() == null) ? 0 : getAst_result().hashCode());
        result = prime * result + ((getUsage() == null) ? 0 : getUsage().hashCode());
        result = prime * result + ((getIvgtt() == null) ? 0 : getIvgtt().hashCode());
        result = prime * result + ((getIvgtt_unit() == null) ? 0 : getIvgtt_unit().hashCode());
        result = prime * result + ((getFreq() == null) ? 0 : getFreq().hashCode());
        result = prime * result + ((getDosage() == null) ? 0 : getDosage().hashCode());
        result = prime * result + ((getDosage_unit() == null) ? 0 : getDosage_unit().hashCode());
        result = prime * result + ((getDays() == null) ? 0 : getDays().hashCode());
        result = prime * result + ((getSpecial_requirement() == null) ? 0 : getSpecial_requirement().hashCode());
        result = prime * result + ((getDismounting() == null) ? 0 : getDismounting().hashCode());
        result = prime * result + ((getUnit_count() == null) ? 0 : getUnit_count().hashCode());
        result = prime * result + ((getExpected_unit_price() == null) ? 0 : getExpected_unit_price().hashCode());
        result = prime * result + ((getSource_unit_price() == null) ? 0 : getSource_unit_price().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getUnit_price() == null) ? 0 : getUnit_price().hashCode());
        result = prime * result + ((getFraction_price() == null) ? 0 : getFraction_price().hashCode());
        result = prime * result + ((getSingle_adjustment_price() == null) ? 0 : getSingle_adjustment_price().hashCode());
        result = prime * result + ((getCost_unit_price() == null) ? 0 : getCost_unit_price().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getIs_deleted() == null) ? 0 : getIs_deleted().hashCode());
        result = prime * result + ((getExtend_data() == null) ? 0 : getExtend_data().hashCode());
        return result;
    }
}