package cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo;



import java.math.BigDecimal;

/**
 * @description: 新老客统计-新客增长趋势实体
 * @author: lzq
 * @Date: 2023/10/24
 */
public class PromotionReferralOverviewPayAmountTrend {

    /**
     * 时间
     */
    private String date;

    /**
     * 新客增长数
     */
    private BigDecimal value;


    public String getDate() {
        return this.date;
    }

    public BigDecimal getValue() {
        return this.value;
    }


    public void setDate(String date) {
        this.date = date;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

}
