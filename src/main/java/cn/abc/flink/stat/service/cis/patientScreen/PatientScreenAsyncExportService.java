package cn.abc.flink.stat.service.cis.patientScreen;

import cn.abc.flink.stat.common.ConvertUtils;
import cn.abc.flink.stat.common.EmployeeTypeEnum;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.MapUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.common.request.AbcPermission;
import cn.abc.flink.stat.common.request.params.AbcCisBaseQueryParams;
import cn.abc.flink.stat.common.request.params.AbcScStatFilterEmployee;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchivesParam;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientScreenSource;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.RevenueChargeDetailService;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.handler.RevenueChargeDetailHandler;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.pojo.RevenueChargeDetailReqParams;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @description:
 * @author: dy
 * @create: 2021-10-15 16:47
 */
@Component
public class PatientScreenAsyncExportService implements BaseAsyncExportInterface {
	private final Logger logger = LoggerFactory.getLogger(PatientScreenAsyncExportService.class);
	@Resource
	PatientScreenService patientScreenService;

	@Override
	public String getKey() {
		return "patient-stat";
	}

	@Override
	public String setFileName(Map<String, Object> params) {
		String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginCreatedDate");
		String endDate = (String) MapUtils.isExistsAndReturn(params, "endCreatedDate");
		return "患者清单" + beginDate + "_" + endDate + ".xlsx";
	}

	@Override
	public OutputStream export(Map<String, Object> params)
			throws ParseException, ExecutionException, InterruptedException {
		// 参数构造
		String chainId = (String) MapUtils.isExistsAndReturn(params, "chainId");
		String clinicId = (String) MapUtils.isExistsAndReturn(params, "clinicId");
		String beginDate = (String) MapUtils.isExistsAndReturn(params, "beginCreatedDate");
		String endDate = (String) MapUtils.isExistsAndReturn(params, "endCreatedDate");
		String patientId = (String) MapUtils.isExistsAndReturn(params, "patientId");
		List<String> tags = (List<String>) MapUtils.isExistsAndReturn(params, "tags");
		String employeeId = (String) MapUtils.isExistsAndReturn(params, "headerEmployeeId");
		String hisType = (String) MapUtils.isExistsAndReturn(params, "headerHisType");
		Integer clinicNodeType = Double.valueOf((double) params.get("headerClinicType")).intValue();
		String chainViewMode = (String) MapUtils.isExistsAndReturn(params, "headerViewMode");
		Integer enablePatientMobile = ConvertUtils.getAsInteger(params.get("enablePatientMobile"));


		PatientArchivesParam reqParams = new PatientArchivesParam();
		reqParams.setChainId(chainId);
		reqParams.setClinicId(clinicId);
		reqParams.setBeginCreatedDate(beginDate);
		reqParams.setEndCreatedDate(TimeUtils.appendEnd(endDate));
		reqParams.setPatientId(patientId);
		AbcCisBaseQueryParams abcCisBaseQueryParams = new AbcCisBaseQueryParams();
		abcCisBaseQueryParams.setEmployeeId(employeeId);
		abcCisBaseQueryParams.setViewModeInteger(Integer.valueOf(chainViewMode));
		abcCisBaseQueryParams.setNodeType(clinicNodeType);
		reqParams.setHisType(hisType);
		reqParams.setEnablePatientMobile(enablePatientMobile);

		reqParams.setSingleStore(chainViewMode, clinicNodeType.toString());
		reqParams.setParams(abcCisBaseQueryParams);


		// 获取并赋值字符串或 List 参数
		reqParams.setPatientIds((List<String>) MapUtils.isExistsAndReturn(params, "patientIds"));
		reqParams.setPatientId((String) MapUtils.isExistsAndReturn(params, "patientId"));
		reqParams.setPatientIdSql((String) MapUtils.isExistsAndReturn(params, "patientIdSql"));

		// 数字、Integer 类型参数
		reqParams.setMemberFlag(ConvertUtils.getAsInteger(params.get("memberFlag")));
		reqParams.setMaxAge(ConvertUtils.getAsInteger(params.get("maxAge")));
		reqParams.setMinAge(ConvertUtils.getAsInteger(params.get("minAge")));
		reqParams.setWxBind(ConvertUtils.getAsInteger(params.get("wxBind")));
		reqParams.setSuccessively(ConvertUtils.getAsInteger(params.get("successively")));
		reqParams.setChainVisit(ConvertUtils.getAsInteger(params.get("chainVisit")));
		reqParams.setClinicVisit(ConvertUtils.getAsInteger(params.get("clinicVisit")));
		reqParams.setDoctorVisit(ConvertUtils.getAsInteger(params.get("doctorVisit")));
		reqParams.setOutpatientType(ConvertUtils.getAsInteger(params.get("outpatientType")));
		reqParams.setMonthDifference(ConvertUtils.getAsInteger(params.get("monthDifference")));
		reqParams.setMinPayTimes(ConvertUtils.getAsInteger(params.get("minPayTimes")));
		reqParams.setMaxPayTimes(ConvertUtils.getAsInteger(params.get("maxPayTimes")));
		reqParams.setUnbindFamilyDoctorFlag(ConvertUtils.getAsInteger(params.get("unbindFamilyDoctorFlag")));
		reqParams.setIsFamilyDoctor(ConvertUtils.getAsInteger(params.get("isFamilyDoctor")));
		reqParams.setSurplusExecuteBeginCount(ConvertUtils.getAsInteger(params.get("surplusExecuteBeginCount")));
		reqParams.setSurplusExecuteEndCount(ConvertUtils.getAsInteger(params.get("surplusExecuteEndCount")));

		// 获取 List 类型参数
		reqParams.setMemberTypes((List<String>) MapUtils.isExistsAndReturn(params, "memberTypes"));
		if (MapUtils.isExistsAndReturn(params, "sourceIdFroms") != null) {
			List<PatientScreenSource> patientScreenSources = JSON.parseArray(JSON.toJSONString(MapUtils.isExistsAndReturn(params, "sourceIdFroms")), PatientScreenSource.class);
			reqParams.setSourceIdFroms(patientScreenSources);
		}

		reqParams.setFamilyDoctorServicePackIds((List<String>) MapUtils.isExistsAndReturn(params, "familyDoctorServicePackIds"));
		reqParams.setFamilyDoctorIds((List<String>) MapUtils.isExistsAndReturn(params, "familyDoctorIds"));
		reqParams.setGoodsId((List<String>) MapUtils.isExistsAndReturn(params, "goodsId"));
		reqParams.setToBeExecutedProjects((List<String>) MapUtils.isExistsAndReturn(params, "toBeExecutedProjects"));

		// 获取日期、字符串类型参数
		reqParams.setAddressCityId((String) MapUtils.isExistsAndReturn(params, "addressCityId"));
		reqParams.setAddressProvinceId((String) MapUtils.isExistsAndReturn(params, "addressProvinceId"));
		reqParams.setAddressDistrictId((String) MapUtils.isExistsAndReturn(params, "addressDistrictId"));
		reqParams.setAddressDetail((String) MapUtils.isExistsAndReturn(params, "addressDetail"));
		reqParams.setBeginBirthDay((String) MapUtils.isExistsAndReturn(params, "beginBirthDay"));
		reqParams.setEndBirthDay((String) MapUtils.isExistsAndReturn(params, "endBirthDay"));
		reqParams.setBirthdayBeginDate((String) MapUtils.isExistsAndReturn(params, "birthdayBeginDate"));
		reqParams.setBirthdayEndDate((String) MapUtils.isExistsAndReturn(params, "birthdayEndDate"));

		reqParams.setDoctorId((String) MapUtils.isExistsAndReturn(params, "doctorId"));

		reqParams.setBeginLastOutpatientDate((String) MapUtils.isExistsAndReturn(params, "beginLastOutpatientDate"));
		reqParams.setEndLastOutpatientDate((String) MapUtils.isExistsAndReturn(params, "endLastOutpatientDate"));
		reqParams.setBeginLastOutpatientDate1((String) MapUtils.isExistsAndReturn(params, "beginLastOutpatientDate1"));
		reqParams.setEndLastOutpatientDate1((String) MapUtils.isExistsAndReturn(params, "endLastOutpatientDate1"));

		reqParams.setLatestRegistrationBeginDate((String) MapUtils.isExistsAndReturn(params, "latestRegistrationBeginDate"));
		reqParams.setLatestRegistrationEndDate((String) MapUtils.isExistsAndReturn(params, "latestRegistrationEndDate"));
		reqParams.setLatestExecuteBeginDate((String) MapUtils.isExistsAndReturn(params, "latestExecuteBeginDate"));
		reqParams.setLatestExecuteEndDate((String) MapUtils.isExistsAndReturn(params, "latestExecuteEndDate"));

		reqParams.setBeginPayDate((String) MapUtils.isExistsAndReturn(params, "beginPayDate"));
		reqParams.setEndPayDate((String) MapUtils.isExistsAndReturn(params, "endPayDate"));

		// 客单价及金额统计，使用 BigDecimal 类型
		reqParams.setMinPayAmountTotal(ConvertUtils.getAsBigDecimal(params.get("minPayAmountTotal")));
		reqParams.setMaxPayAmountTotal(ConvertUtils.getAsBigDecimal(params.get("maxPayAmountTotal")));
		reqParams.setMinPayAmountEach(ConvertUtils.getAsBigDecimal(params.get("minPayAmountEach")));
		reqParams.setMaxPayAmountEach(ConvertUtils.getAsBigDecimal(params.get("maxPayAmountEach")));


		// 家庭医生相关日期
		reqParams.setFamilyDoctorSignInBeginTime((String) MapUtils.isExistsAndReturn(params, "familyDoctorSignInBeginTime"));
		reqParams.setFamilyDoctorSignInEndTime((String) MapUtils.isExistsAndReturn(params, "familyDoctorSignInEndTime"));
		reqParams.setFamilyDoctorOverdueBeginTime((String) MapUtils.isExistsAndReturn(params, "familyDoctorOverdueBeginTime"));
		reqParams.setFamilyDoctorOverdueEndTime((String) MapUtils.isExistsAndReturn(params, "familyDoctorOverdueEndTime"));

		// 创建时间以及活跃门店信息
		reqParams.setActiveClinicId((String) MapUtils.isExistsAndReturn(params, "activeClinicId"));
		reqParams.init();
		if (tags != null) {
			reqParams.setTags(tags);
		} else {
			reqParams.setTags(new ArrayList<>());
		}
		List<ExcelUtils.AbcExcelSheet> sheets = patientScreenService.export(reqParams);
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		ExcelUtils.export(baos, sheets);
		return baos;
	}
}
