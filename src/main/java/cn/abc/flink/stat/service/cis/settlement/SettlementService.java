package cn.abc.flink.stat.service.cis.settlement;

import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.db.dao.SettlementMapper;
import cn.abc.flink.stat.service.cis.settlement.domain.SettlementList;
import cn.abc.flink.stat.service.cis.settlement.domain.SettlementParam;
import cn.abc.flink.stat.service.cis.settlement.handler.SettlementHandler;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2022-06-16 17:00
 **/
@Service
public class SettlementService {
    @Autowired
    private ExecutorService cacheExecutorService;

    @Autowired
    private SettlementHandler handler;

    @Autowired
    private SettlementMapper mapper;

    String cisDB = TableUtils.getCisGoodsTable();

    public V2StatResponse getSettlementList(SettlementParam params) throws Exception {
        List<Map<String, Object>> data = handler.getSettlementListData(params);
        SettlementList summary = handler.getSettlementListSummary(params);
        List<TableHeaderEmployeeItem> header = handler.getSettlementHeader(params, HeaderTableKeyConfig.STAT_SETTLEMENT_LIST);
        StatResponseTotal total = new StatResponseTotal();
        total.setCount((long) data.size());
        V2StatResponse rsp = new V2StatResponse();
        rsp.setData(data);
        rsp.setSummary(summary);
        rsp.setTotal(total);
        rsp.setHeader(header);

        return rsp;
    }

    public V2StatResponse getSettlementDetail(SettlementParam params) throws Exception {
        List<Map<String, Object>> data = handler.getSettlementDetailData(params);
        Long count = mapper.selectSettlementDetailCount(cisDB, params);
        List<TableHeaderEmployeeItem> header = handler.getSettlementHeader(params, HeaderTableKeyConfig.STAT_SETTLEMENT_DETAIL);
        V2StatResponse rsp = new V2StatResponse();
        rsp.setData(data);
        rsp.setTotal(new StatResponseTotal(count));
        rsp.setHeader(header);
        return rsp;
    }

    public V2StatResponse getPharmacySettlementDetail(SettlementParam params) throws Exception {
        V2StatResponse rsp = new V2StatResponse();
        List<Map<String, Object>> data = handler.getPharmacySettlementDetailData(params);
        StatResponseTotal total = handler.getPharmacySettlementDetailCount(params);
        List<TableHeaderEmployeeItem> header = handler.getSettlementHeader(params, HeaderTableKeyConfig.STAT_PHARMACY_SETTLEMENT_DETAIL);
        rsp.setData(data);
        rsp.setTotal(total);
        rsp.setHeader(header);
        return rsp;
    }

    public List<ExcelUtils.AbcExcelSheet> asyncSettlementListExport(SettlementParam params) throws Exception {
        V2StatResponse settlementList;
        if (params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
            settlementList = getPharmacySettlementList(params);
        } else {
            settlementList = getSettlementList(params);
        }
        SettlementList summary = (SettlementList) settlementList.getSummary();

        Map<String, Object> map = null;
        try {
            map = BeanUtils.describe(summary);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List data = settlementList.getData();
        data.add(map);

        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList();
        ExcelUtils.AbcExcelSheet settlementListSheet = new ExcelUtils.AbcExcelSheet();
        List<String> props = new ArrayList<>();
        settlementListSheet.setName("主表");
        settlementListSheet.setSheetDefinition(handler.makeExcelHead(props, settlementList.getHeader()));
        settlementListSheet.setData(handler.makeExcelBody(props, data));
        sheets.add(settlementListSheet);
        return sheets;
    }


    public List<ExcelUtils.AbcExcelSheet> asyncSettlementDetailExport(SettlementParam params) throws Exception {
        V2StatResponse settlementDetail;
        if (params.getHisType().equals(CisJWTUtils.CIS_HIS_TYPE_PHARMACY)) {
            settlementDetail = getPharmacySettlementDetail(params);
        } else {
            settlementDetail = getSettlementDetail(params);
        }
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        ExcelUtils.AbcExcelSheet settlementDetailSheet = new ExcelUtils.AbcExcelSheet();
        List<String> props = new ArrayList<>();
        settlementDetailSheet.setName("结算明细");
        settlementDetailSheet.setSheetDefinition(handler.makeExcelHead(props, settlementDetail.getHeader()));
        settlementDetailSheet.setData(handler.makeExcelBody(props, settlementDetail.getData()));
        sheets.add(settlementDetailSheet);
        return sheets;
    }


    public V2StatResponse getPharmacySettlementList(SettlementParam params) throws Exception {
        List<Map<String, Object>> data = handler.getPharmacySettlementListData(params);
        SettlementList summary = handler.getPharmacySettlementListSummary(params);
        List<TableHeaderEmployeeItem> header = handler.getSettlementHeader(params, HeaderTableKeyConfig.STAT_SETTLEMENT_LIST);
        StatResponseTotal total = new StatResponseTotal();
        total.setCount((long) data.size());
        V2StatResponse rsp = new V2StatResponse();
        rsp.setData(data);
        rsp.setSummary(summary);
        rsp.setTotal(total);
        rsp.setHeader(header);

        return rsp;
    }
}
