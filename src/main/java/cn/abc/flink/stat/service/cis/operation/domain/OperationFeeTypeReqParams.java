package cn.abc.flink.stat.service.cis.operation.domain;

import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.common.ComPoseTypeSqlHandler;

import lombok.EqualsAndHashCode;

/**
 * 收费分类接口参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
public class OperationFeeTypeReqParams extends AbcScStatRequestParams {
    /**
     * 是否拼摊套餐 1：是 0：否
     */
    private Integer isComposeShareEqually;
    /**
     * 欠费收入统计时机 1.欠费时计入收入 2.还款时计入收入
     */
    private Integer arrearsStatTiming;

    /**
     * 套餐sql
     */
    private String composeTypeSql;

    private String oweSql;

    /**
     * 配置参数
     *
     * @param statConfigDto 配置参数
     */
    public void configParam(StatConfigDto statConfigDto) {
        if (this.beginDate != null && this.beginDate.length() <= 10) {
            this.beginDate = TimeUtils.appendBegin(this.beginDate);
        }
        if (this.endDate != null && this.endDate.length() <= 10) {
            this.endDate = TimeUtils.appendEnd(this.endDate);
        }
        this.initDs();
        this.isComposeShareEqually = statConfigDto.getIsComposeShareEqually();
        this.composeTypeSql = statConfigDto.buildSqlAboutComposeType();
        if (statConfigDto.getArrearsStatTiming() != null) {
            if (statConfigDto.getArrearsStatTiming() == StatConfigDto.ArrearsStatTiming.ARREARS_STAT_TIMING_ARREARS) {
                this.oweSql = "record_source_type in (0, 1)";
            } else if (statConfigDto.getArrearsStatTiming() == StatConfigDto.ArrearsStatTiming.ARREARS_STAT_TIMING_REPAYMENT) {
                this.oweSql = "record_source_type in (0, 2)";
            }
        }
    }

    public Integer getIsComposeShareEqually() {
        return this.isComposeShareEqually;
    }

    public Integer getArrearsStatTiming() {
        return this.arrearsStatTiming;
    }

    public String getComposeTypeSql() {
        return this.composeTypeSql;
    }

    public String getOweSql() {
        return this.oweSql;
    }


    public void setIsComposeShareEqually(Integer isComposeShareEqually) {
        this.isComposeShareEqually = isComposeShareEqually;
    }

    public void setArrearsStatTiming(Integer arrearsStatTiming) {
        this.arrearsStatTiming = arrearsStatTiming;
    }

    public void setComposeTypeSql(String composeTypeSql) {
        this.composeTypeSql = composeTypeSql;
    }

    public void setOweSql(String oweSql) {
        this.oweSql = oweSql;
    }

}
