package cn.abc.flink.stat.service.cis.achievement.recommend;


import cn.abc.flink.stat.common.AbcDefaultValueUtils;
import cn.abc.flink.stat.common.ExcelUtils;
import cn.abc.flink.stat.common.ExportKeyWordEnum;
import cn.abc.flink.stat.common.HeaderTableKeyConfig;
import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.common.response.V2StatResponse;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnAchievementChargeMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnAchievementRecommendMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresAchievementChargeMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresAchievementRecommendMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.Employee;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.domain.V2PatientSourceType;
import cn.abc.flink.stat.pojo.OrganResp;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeFeeClassify;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendGoodsEntity;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonnelBase;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendPersonnelDispensingCost;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendRecord;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendSelectRsp;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendTotalEntity;
import cn.abc.flink.stat.service.cis.achievement.recommend.domain.AchievementRecommendTransactionEntity;
import cn.abc.flink.stat.service.cis.achievement.recommend.handler.AchievementRecommendExportHandler;
import cn.abc.flink.stat.service.cis.achievement.recommend.handler.AchievementRecommendGoodsHandler;
import cn.abc.flink.stat.service.cis.achievement.recommend.handler.AchievementRecommendHandler;
import cn.abc.flink.stat.service.cis.achievement.recommend.handler.AchievementRecommendTransactionHandler;
import cn.abc.flink.stat.service.cis.achievement.recommend.handler.AchievementRecommendVisitSourceHandler;
import cn.abc.flink.stat.service.cis.achievement.recommend.pojos.AchievementRecommendReqParams;
import cn.abc.flink.stat.service.cis.achievement.recommend.pojos.AchievementRecommendSelectionReqParams;
import cn.abc.flink.stat.service.cis.config.StatConfigService;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.selection.FeeClassifyService;
import cn.abc.flink.stat.service.cis.selection.OrganService;
import cn.abc.flink.stat.service.cis.selection.RevenueSelectionService;
import cn.abc.flink.stat.service.cis.selection.entity.CommSelectParams;
import cn.abc.flink.stat.service.cis.selection.entity.FeeClassifyParam;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 就诊推荐service
 */
@Service
public class AchievementRecommendService {
    private Logger logger = LoggerFactory.getLogger(AchievementRecommendService.class);

    private static final int EXPRT_END_DATE_SUBTRING = 10;

    /**
     * n天以上不允许导出单据
     */
    private static final int EXPORT_DAYS = 93;

    @Autowired
    private OrganService organService;

    @Autowired
    private DimensionQuery query;

    @Autowired
    private ExecutorService cacheExecutorService;

    @Autowired
    private FeeClassifyService feeClassifyService;

    @Autowired
    private ArnAchievementRecommendMapper arnMapper;

    @Autowired
    private StoreUtils storeUtils;

    @Autowired
    private HologresAchievementRecommendMapper hologresAchievementRecommendMapper;

    @Autowired
    private HologresAchievementChargeMapper hologresAchievementChargeMapper;

    @Autowired
    private ArnAchievementChargeMapper arnChargeMapper;

    @Autowired
    private StatConfigService statConfigService;

    @Autowired
    private AchievementRecommendHandler achievementRecommendHandler;

    @Autowired
    private RevenueSelectionService selectionService;

    public static final String[] SORT_FEE_TITLE = new String[]{
            "挂号费", "咨询费", "西药", "中药饮片", "中药颗粒", "中成药", "医疗器械", "后勤材料", "固定资产",
            "自制成品", "保健药品", "保健食品", "其他商品", "检验", "检查", "治疗", "理疗", "其他费用",
            "加工费", "快递费", "空中药房", "议价费", "签约费", "会员充值本金", "卡项充值本金", "开卡费", "套餐",
            "眼镜", "镜片", "镜架", "角膜塑形镜", "软性亲水镜", "硬性透氧镜", "太阳镜"
    };

    /**
     * 没有二级分类的一级分类
     */
    public static final List<String> UAUSC_LIST = new Vector<>();

    private static Map<Byte, String> prescriptionMap = new HashMap<>();

    static {
        UAUSC_LIST.add("挂号费");
        UAUSC_LIST.add("咨询费");
        UAUSC_LIST.add("快递费");
        UAUSC_LIST.add("议价费");
        UAUSC_LIST.add("签约费");
        UAUSC_LIST.add("会员充值本金");
        UAUSC_LIST.add("卡项充值本金");
        UAUSC_LIST.add("开卡费");

        prescriptionMap.put(Byte.valueOf("4"), "西药处方");
        prescriptionMap.put(Byte.valueOf("51"), "输液处方");
        prescriptionMap.put(Byte.valueOf("52"), "注射处方");
        prescriptionMap.put(Byte.valueOf("53"), "雾化处方");
        prescriptionMap.put(Byte.valueOf("61"), "饮片处方");
        prescriptionMap.put(Byte.valueOf("62"), "颗粒处方");
        prescriptionMap.put(Byte.valueOf("16"), "外治处方");
    }

    /**
     * 就诊推荐-推荐渠道
     *
     * @param params 参数
     * @return AchievementRecommendPersonRsp
     * @throws ParseException       exception
     * @throws ExecutionException   exception
     * @throws InterruptedException exception
     */
    public V2StatResponse selectVisitSource(AchievementRecommendReqParams params)
            throws ParseException, ExecutionException, InterruptedException {
        StatConfigDto dto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(),
                params.getClinicId(), params.getDispensaryType()));
        params.initData(dto);
        // base 客量
        CompletableFuture<List<AchievementRecommendPersonnelBase>> baseFuture =
                CompletableFuture.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                        arnMapper, hologresAchievementRecommendMapper)
                        .selectPersonnelBase(TableUtils.getCisTable(), params), cacheExecutorService);
        // 发药成本, 特殊场景：某天只有发药没有收费
        CompletableFuture<List<AchievementRecommendPersonnelDispensingCost>> costFuture =
                CompletableFuture.supplyAsync(() -> storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                        arnMapper, hologresAchievementRecommendMapper)
                        .selectPersonnelDispensingCost(TableUtils.getCisTable(), params), cacheExecutorService);

        CompletableFuture.allOf(baseFuture, costFuture).join();
        List<AchievementRecommendPersonnelBase> baseList = baseFuture.get();
        List<AchievementRecommendPersonnelDispensingCost> costList = costFuture.get();
        AchievementRecommendVisitSourceHandler handler = new AchievementRecommendVisitSourceHandler(
                query,
                storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), arnMapper, hologresAchievementRecommendMapper),
                cacheExecutorService
        );
        //数据处理
        return handler.handle(params, baseList, costList, dto);
    }


    /**
     * 就诊推荐-项目
     *
     * @param params 参数
     * @return AchievementRecommendPersonRsp
     * @throws ParseException       exception
     * @throws ExecutionException   exception
     * @throws InterruptedException exception
     */
    public V2StatResponse selectGoods(AchievementRecommendReqParams params) throws ExecutionException,
            InterruptedException {
        StatConfigDto dto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(),
                        params.getDispensaryType()));
        params.initData(dto);
        AchievementRecommendGoodsHandler handler = new AchievementRecommendGoodsHandler(query,
                storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                        arnMapper, hologresAchievementRecommendMapper), cacheExecutorService);
        //事实数据
        handler.fetchBaseFuture(params);

        handler.getListFutureGoods().join();
        List<AchievementRecommendGoodsEntity> list = handler.getListFutureGoods().get();

        V2StatResponse response = new V2StatResponse();
        response.setHeader(achievementRecommendHandler.handleGoodsHeader(params));
        if (list.size() == 0) {
            response.setData(new ArrayList<>());
            response.setTotal(new StatResponseTotal(0L));
            return response;
        }
        Set<String> goodsIds = new HashSet<>();
        Set<String> composeGoodsIds = new HashSet<>();
        for (AchievementRecommendGoodsEntity goods : list) {
            if (goods != null && goods.getGoodsId() != null) {
                goodsIds.add(goods.getGoodsId());
            }
            if (goods != null && goods.getGoodsId() != null && goods.getClassifyLevel1().contains("11")) {
                composeGoodsIds.add(goods.getGoodsId());
            }
        }
        //获取纬度数据
        handler.fetchDimension(params, goodsIds, composeGoodsIds);
        for (AchievementRecommendGoodsEntity entity : list) {
            handler.setAchievementChargeBaseInfo(entity);
            //分类
            handler.handleGoodsFeeClassify(entity, params.getHisType());
            //处理药品相关信息
            handler.handleGoodsInfo(entity, params);
            entity.handlePrice(params.getPayModeSql(), dto);
            entity.permissionSetting(params.getPermission());
        }

        handler.getFutureGoodsTotal().join();
        AchievementRecommendTotalEntity total = handler.getFutureGoodsTotal().get();
        response.setData(list);
        response.setTotal(new StatResponseTotal(total.getCount()));
        return response;
    }

    /**
     * 就诊推荐-单据
     *
     * @param params 参数
     * @return AchievementRecommendPersonRsp
     * @throws ParseException       exception
     * @throws ExecutionException   exception
     * @throws InterruptedException exception
     */
    public V2StatResponse selectTransaction(AchievementRecommendReqParams params)
            throws ParseException, ExecutionException, InterruptedException {
        StatConfigDto dto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(),
                params.getClinicId(), params.getDispensaryType()));
        params.initData(dto);
        Map<String, V2PatientSourceType> patientSourceTypeMap = query.queryPatientSourceType(params.getChainId());
        AchievementRecommendTransactionHandler achievementRecommendTransactionHandler =
                new AchievementRecommendTransactionHandler(query, storeUtils.getMapper(params.getBeginDate(),
                        params.getEndDate(), arnMapper, hologresAchievementRecommendMapper),
                        cacheExecutorService);
        //查询就诊推荐-单据数据
        achievementRecommendTransactionHandler.fetchBaseFuture(storeUtils.getMapper(params.getBeginDate(),
                params.getEndDate(), arnMapper,hologresAchievementRecommendMapper), params);

        achievementRecommendTransactionHandler.getListFutureTransaction().join();
        List<AchievementRecommendTransactionEntity> transactionList
                = achievementRecommendTransactionHandler.getListFutureTransaction().get();
        Set<String> transIds = new HashSet<>();
        Set<String> patientIds = new HashSet<>();
        Set<String> patientorderIds = new HashSet<>();
        Set<Long> snapIds = new HashSet<>();
        for (AchievementRecommendTransactionEntity entity : transactionList) {
            if (entity != null) {
                transIds.add(entity.getTransId());
                if (entity.getPatientId() != null
                        && !AbcDefaultValueUtils.DEFAULT_ID.equals(entity.getPatientOrderId())) {
                    patientIds.add(entity.getPatientId());
                }
                if (entity.getPatientOrderId() != null) {
                    patientorderIds.add(entity.getPatientOrderId());
                }
                if (entity.getVisitSourceFromType() != null && entity.getVisitSourceFromType() == 2
                        && StringUtils.isNotBlank(entity.getVisitSourceFrom())) {
                    patientIds.add(entity.getVisitSourceFrom());
                }
                if (entity.getPersonnelSnapId() != null) {
                    snapIds.add(entity.getPersonnelSnapId());
                }
            }
        }

        //费用分类
        achievementRecommendTransactionHandler.fetchClassifyFuture(storeUtils.getMapper(params.getBeginDate(),
                params.getEndDate(), arnMapper, hologresAchievementRecommendMapper), params);
        //费用分类
        achievementRecommendTransactionHandler.fetchClassifyDataGroupByTransactionIds(
                storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), arnChargeMapper, hologresAchievementChargeMapper), params, transIds);
        //纬度数据
        achievementRecommendTransactionHandler.fetchDimemsion(params, patientIds, patientorderIds, snapIds);
        return achievementRecommendTransactionHandler.handle(params, transactionList, dto, patientSourceTypeMap);
    }

    /**
     * 就诊推荐-筛选框
     *
     * @param params 参数
     * @return AchievementRecommendPersonRsp
     * @throws ParseException       exception
     * @throws ExecutionException   exception
     * @throws InterruptedException exception
     */
    public AchievementRecommendSelectRsp selectEmployeeAndFee(AchievementRecommendSelectionReqParams params)
            throws ExecutionException, InterruptedException {
        StatConfigDto dto = statConfigService.selectConfig(new StatConfigParam(params.getChainId(),
                params.getClinicId(), params.getDispensaryType()));
        // 门店信息
        CompletableFuture<List<OrganResp>> listFutureClinics = CompletableFuture.supplyAsync(() -> {
            return organService.selectOrganFromChargeTransactionRecord(params.getBeginDate(),
                    params.getEndDate(), params.getChainId());
        }, cacheExecutorService);

        // 费用分类
        CompletableFuture<List<AchievementChargeFeeClassify>> FeeClassifyF = CompletableFuture.supplyAsync(() ->
                selectionService.getFeeTypesSelection(new CommSelectParams(params.getChainId(), params.getClinicId(),
                        null, params.getBeginDate(), params.getEndDate(),params.getIncludeReg(), params.getDispensaryType(),
                        params.getHisType(), 1), false, true), cacheExecutorService);

        CompletableFuture.allOf(FeeClassifyF, listFutureClinics).join();

        List<AchievementChargeFeeClassify> feeClassifyList = FeeClassifyF.get();
        List<AchievementChargeFeeClassify> feeList = feeClassifyList.stream().filter(fee ->
                !"会员充值本金".equals(fee.getName().trim())
                        && !"卡项充值本金".equals(fee.getName().trim())
                        && !"-".equals(fee.getName().trim())
        ).collect(Collectors.toList());

        /*payModeF.join();*/
        return new AchievementRecommendSelectRsp(feeList, listFutureClinics.get());
    }

    /**
     * 就诊推荐-导出
     *
     * @param response web
     * @param params   参数
     * @throws Exception e
     */
    public void export(HttpServletResponse response, AchievementRecommendReqParams params)
            throws Exception {

        V2StatResponse personnel = selectVisitSource(params);
        V2StatResponse goods = selectGoods(params);
        V2StatResponse transaction = selectTransaction(params);
        V2StatResponse recordResponse = selectRecommendRecord(params);

        String fileName = "本次推荐统计" + params.getBeginDate() + "_" + params.getEndDate()
                .substring(0, EXPRT_END_DATE_SUBTRING);
        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();

        ExcelUtils.AbcExcelSheet recordSheet = new ExcelUtils.AbcExcelSheet();

        recordSheet.setName("推荐记录");
        recordSheet.setData(ExcelUtils.exportDataV2(recordResponse.getData(), recordResponse.getHeader()));
        recordSheet.setSheetDefinition(ExcelUtils.exportTableHeader(recordResponse.getHeader()));
        sheets.add(recordSheet);

        ExcelUtils.AbcExcelSheet personnelSheet = new ExcelUtils.AbcExcelSheet();

        personnelSheet.setName("推荐渠道");
        personnelSheet.setData(AchievementRecommendExportHandler.makePersonnelExcelBody(personnel));
        personnelSheet.setSheetDefinition(AchievementRecommendExportHandler.makePersonnelExcelHead(personnel.getHeader()));
        sheets.add(personnelSheet);

        ExcelUtils.AbcExcelSheet goodsSheet = new ExcelUtils.AbcExcelSheet();
        goodsSheet.setName("项目");
        goodsSheet.setData(AchievementRecommendExportHandler.makeGoodsExcelBody(goods));
        goodsSheet.setSheetDefinition(AchievementRecommendExportHandler.makeGoodsExcelHead(goods.getHeader()));
        sheets.add(goodsSheet);

        ExcelUtils.AbcExcelSheet transactionSheet = new ExcelUtils.AbcExcelSheet();
        transactionSheet.setName("单据");
        transactionSheet.setData(AchievementRecommendExportHandler.makeTransactionExcelBody(transaction));
        transactionSheet.setSheetDefinition(AchievementRecommendExportHandler
                .makeTransactionExcelHead(transaction.getHeader()));
        sheets.add(transactionSheet);

        try {
            ExcelUtils.export(fileName, response, sheets);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 就诊推荐-异步导出
     *
     * @param params 参数
     * @return -
     * @throws Exception e
     */
    public List<ExcelUtils.AbcExcelSheet> export(AchievementRecommendReqParams params, List<String> keyWordList) throws Exception {
        AchievementRecommendReqParams personalParam = params.copyParams(params);
        AchievementRecommendReqParams goodsParam = params.copyParams(params);
        AchievementRecommendReqParams transactionParam = params.copyParams(params);
        AchievementRecommendReqParams recommendRecordParam = params.copyParams(params);

        DateTime startTime = DateUtil.parse(transactionParam.getBeginDate());
        DateTime endTime = DateUtil.parse(transactionParam.getEndDate());
        DateField day = DateField.DAY_OF_MONTH;
        List<DateTime> dateTimeList = DateUtil.rangeToList(startTime, endTime, day);
        boolean shouldIncludeTransaction = dateTimeList.size() <= EXPORT_DAYS;

        List<ExcelUtils.AbcExcelSheet> sheets = new ArrayList<>();
        if (keyWordList.contains(ExportKeyWordEnum.ACHIEVEMENT_RECOMMEND_RECORD.getKey())) {
            CompletableFuture<V2StatResponse> recommendRecordFuture = getRecommendRecordCompletableFuture(recommendRecordParam);
            recommendRecordFuture.join();
            V2StatResponse recordResponse = recommendRecordFuture.get();
            ExcelUtils.AbcExcelSheet recordSheet = new ExcelUtils.AbcExcelSheet();
            recordSheet.setName("推荐记录");
            List<TableHeaderEmployeeItem> recordHeader = AchievementRecommendHandler.dynamicList(recordResponse.getHeader());
            recordSheet.setData(ExcelUtils.exportDataV2(recordResponse.getData(), recordHeader));
            recordSheet.setSheetDefinition(ExcelUtils.exportTableHeader(recordHeader));
            recordSheet.setTableHeaderEmployeeItems(recordHeader);
            sheets.add(recordSheet);
        }

        if (keyWordList.contains(ExportKeyWordEnum.ACHIEVEMENT_RECOMMEND_VISIT_SOURCE.getKey())) {
            CompletableFuture<V2StatResponse> personnelFuture = getPersonRspCompletableFuture(personalParam);
            personnelFuture.join();
            V2StatResponse personRsp = personnelFuture.get();
            ExcelUtils.AbcExcelSheet personnelSheet = new ExcelUtils.AbcExcelSheet();
            personnelSheet.setName("推荐渠道");
            personnelSheet.setData(AchievementRecommendExportHandler.makePersonnelExcelBody(personRsp));
            personnelSheet.setSheetDefinition(AchievementRecommendExportHandler.makePersonnelExcelHead(personRsp.getHeader()));
            personnelSheet.setTableHeaderEmployeeItems(personRsp.getHeader());
            sheets.add(personnelSheet);
        }

        if (keyWordList.contains(ExportKeyWordEnum.ACHIEVEMENT_RECOMMEND_GOODS.getKey())) {
            CompletableFuture<V2StatResponse> goodsFuture = getGoodsRspCompletableFuture(goodsParam);
            goodsFuture.join();
            V2StatResponse goodsRsp = goodsFuture.get();
            ExcelUtils.AbcExcelSheet goodsSheet = new ExcelUtils.AbcExcelSheet();
            goodsSheet.setName("项目");
            goodsSheet.setData(AchievementRecommendExportHandler.makeGoodsExcelBody(goodsRsp));
            goodsSheet.setSheetDefinition(AchievementRecommendExportHandler.makeGoodsExcelHead(goodsRsp.getHeader()));
            goodsSheet.setTableHeaderEmployeeItems(goodsRsp.getHeader());
            sheets.add(goodsSheet);
        }

        if (shouldIncludeTransaction && keyWordList.contains(ExportKeyWordEnum.ACHIEVEMENT_RECOMMEND_TRANSACTION.getKey())) {
            CompletableFuture<V2StatResponse> transactionFuture = getTransactionRespCompletableFuture(transactionParam);
            transactionFuture.join();
            V2StatResponse transactionResp = transactionFuture.get();
            ExcelUtils.AbcExcelSheet transactionSheet = new ExcelUtils.AbcExcelSheet();
            transactionSheet.setName("单据");
            transactionSheet.setData(AchievementRecommendExportHandler.makeTransactionExcelBody(transactionResp));
            transactionSheet.setSheetDefinition(AchievementRecommendExportHandler
                    .makeTransactionExcelHead(transactionResp.getHeader()));
            transactionSheet.setTableHeaderEmployeeItems(transactionResp.getHeader());
            sheets.add(transactionSheet);
        }
        return sheets;
    }

    /**
     * 就诊推荐-单据
     *
     * @param params -
     * @return -
     */
    private CompletableFuture<V2StatResponse> getRecommendRecordCompletableFuture(AchievementRecommendReqParams params) {
        CompletableFuture<V2StatResponse> recommendRecordFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return selectRecommendRecord(params);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return new V2StatResponse();
        }, cacheExecutorService);
        return recommendRecordFuture;
    }

    /**
     * 就诊推荐-单据
     *
     * @param params -
     * @return -
     */
    private CompletableFuture<V2StatResponse> getTransactionRespCompletableFuture(AchievementRecommendReqParams params) {
        CompletableFuture<V2StatResponse> transactionFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return selectTransaction(params);
            } catch (ParseException | ExecutionException | InterruptedException e) {
                e.printStackTrace();
            }
            return new V2StatResponse();
        }, cacheExecutorService);
        return transactionFuture;
    }

    /**
     * 就诊推荐-项目
     *
     * @param params -
     * @return -
     */
    private CompletableFuture<V2StatResponse> getGoodsRspCompletableFuture(AchievementRecommendReqParams params) {
        CompletableFuture<V2StatResponse> goodsFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return selectGoods(params);
            } catch (ExecutionException | InterruptedException e) {
                e.printStackTrace();
            }
            return new V2StatResponse();
        }, cacheExecutorService);
        return goodsFuture;
    }

    /**
     * 就诊推荐-渠道
     *
     * @param params -
     * @return -
     */
    private CompletableFuture<V2StatResponse> getPersonRspCompletableFuture(AchievementRecommendReqParams params) {
        CompletableFuture<V2StatResponse> personnelFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return selectVisitSource(params);
            } catch (ParseException | ExecutionException | InterruptedException e) {
                e.printStackTrace();
            }
            return new V2StatResponse();
        }, cacheExecutorService);
        return personnelFuture;
    }

    /**
     * 就诊推荐统计-推荐记录
     *
     * @param param 就诊推荐统计param
     * @return statResponse
     * @throws Exception e
     */
    public V2StatResponse selectRecommendRecord(AchievementRecommendReqParams param) throws Exception {
        V2StatResponse response = new V2StatResponse();
        Map<String, V2PatientSourceType> patientSourceTypeMap =
                query.queryPatientSourceType(param.getChainId());
        param.setVisitSourceByRecord(patientSourceTypeMap);
        List<TableHeaderEmployeeItem> headerEmployeeItems = query.getTableHeaderEmployeeItems(
                param.getParams().getEmployeeId(),
                HeaderTableKeyConfig.STAT_ACHIEVEMENT_RECOMMEND_RECORD,
                param.getParams().getViewModeInteger(),
                param.getParams().getNodeType(), HeaderTableKeyConfig.EXCLUDE_HIDDEN_1);
        response.setHeader(headerEmployeeItems);
        CompletableFuture<List<AchievementRecommendRecord>> recordFuture =
                CompletableFuture.supplyAsync(() -> hologresAchievementRecommendMapper.selectRecommendRecord(TableUtils.getCisTable(), param), cacheExecutorService);
        CompletableFuture<Long> totalFuture =
                CompletableFuture.supplyAsync(() -> hologresAchievementRecommendMapper.selectRecommendRecordTotal(TableUtils.getCisTable(), param), cacheExecutorService);
        CompletableFuture.allOf(recordFuture, totalFuture).join();
        List<AchievementRecommendRecord> recordList = recordFuture.get();
        if (CollUtil.isEmpty(recordList)) {
            response.setTotal(new StatResponseTotal(0L));
            response.setData(new ArrayList());
            return response;
        }
        Set<String> patientIdSet = new HashSet<>();
        Set<String> organIdSet = new HashSet<>();
        for (AchievementRecommendRecord record : recordList) {
            patientIdSet.add(record.getPatientId());
            if (record.getSourceFromType() != null && record.getSourceFromType() == 2
                    && StringUtils.isNotEmpty(record.getSourceFrom())) {
                patientIdSet.add(record.getSourceFrom());
            }
            organIdSet.add(record.getSourceFromClinicId());
        }
        // 获取患者信息
        CompletableFuture<Map<String, V2Patient>> patientMapFuture = CompletableFuture.supplyAsync(() ->
                query.queryPatient(param.getChainId(), patientIdSet, param.getEnablePatientMobile()));
        CompletableFuture<Map<String, Employee>> employeeMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryEmployeeByChainId(param.getChainId());
        }, cacheExecutorService);
        CompletableFuture<Map<String, String>> organMapFuture = CompletableFuture.supplyAsync(() -> {
            return query.queryOrganByIds(organIdSet);
        }, cacheExecutorService);

        CompletableFuture.allOf(patientMapFuture, employeeMapFuture, organMapFuture).join();
        Map<String, V2Patient> patientMap = patientMapFuture.get();
        Map<String, Employee> employeeMap = employeeMapFuture.get();
        Map<String, String> organMap = organMapFuture.get();
        recordList.forEach(record -> {
            record.setPatient(patientMap, param.getPermission().isEnablePatientMobile());
            record.setSourceClinic(param.getChainId(), organMap);
            record.sourceChannelAndRecommendDetail(patientMap, patientSourceTypeMap, employeeMap);
            record.setCreatedBy(employeeMap);
        });
        response.setData(recordList);
        response.setTotal(new StatResponseTotal(totalFuture.get()));
        return response;
    }
}
