package cn.abc.flink.stat.service.cis.registration.handler;

import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.common.domain.PayModeDto;
import cn.abc.flink.stat.common.response.StatResponseTotal;
import cn.abc.flink.stat.db.cis.aurora.dao.MysqlRegistrationOverviewMapper;
import cn.abc.flink.stat.db.cis.common.RegistrationOverviewMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresRegistrationMapper;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.*;
import cn.abc.flink.stat.service.cis.config.StatConfigService;
import cn.abc.flink.stat.service.cis.config.handler.StatConfigHandler;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.handler.ChargeHandler;
import cn.abc.flink.stat.service.cis.handler.EmployeeHandler;
import cn.abc.flink.stat.service.cis.registration.domain.*;
import cn.abc.flink.stat.service.cis.revenue.charge.detail.handler.RevenueChargeDetailHandler;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static cn.abc.flink.stat.common.contants.CommonConstants.*;

/**
 * RegistrationDataHandler
 */
@Component
public class RegistrationDataHandler {

    private Logger logger = LoggerFactory.getLogger(RegistrationDataHandler.class);

    @Autowired
    private MysqlRegistrationOverviewMapper mysqlMapper;

    @Resource
    private HologresRegistrationMapper hologresRegistrationMapper;

    @Autowired
    private StoreUtils storeUtils;
    @Autowired
    DimensionQuery dimensionQuery;

    @Autowired
    private StatConfigService statConfigService;

    /**
     * @param params       -
     * @param visitSource1 -
     * @param visitSource2 -
     * @param dto          -
     * @return -
     */
    public List<RegistrationSummary> fetchSummary(RegistrationReqParams params, String visitSource1,
                                                  String visitSource2, StatConfigDto dto) {
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        List<RegistrationSummary> summaries = getMapper(isKudu).selectSummary(TableUtils.getCisTable(), params,
                visitSource1, visitSource2);
        if (summaries == null || summaries.size() == 0) {
            return new ArrayList<>();
        }

        CompletableFuture<Map<String, Employee>> employeeFuture =
                CompletableFuture.supplyAsync(() -> dimensionQuery.queryEmployeeByChainId(params.getChainId()));
        CompletableFuture<Map<String, Organ>> organMapFuture =
                CompletableFuture.supplyAsync(() -> dimensionQuery.queryOrganByParentId(params.getChainId()));

        CompletableFuture.allOf(employeeFuture, organMapFuture).join();
        Map<String, Employee> employees = null;
        Map<String, Organ> organMap = null;
        try {
            employees = employeeFuture.get();
            organMap = organMapFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }

        Map<String, RegistrationSummary> summaryMap = new HashMap<>();
        for (RegistrationSummary s : summaries) {
            if (s == null) {
                continue;
            }
            if (s.getDoctorName() == null || s.getDoctorName().isEmpty()) {
                s.setDoctorName(Optional.ofNullable(employees.get(s.getDoctorId())).map(Employee::getName).orElse("未指定"));
            }
            s.setClinicName(Optional.ofNullable(organMap.get(s.getClinicId())).map(o ->
                    StringUtils.isNotBlank(o.getShortName()) ? o.getShortName() : o.getName()).orElse("-"));
            if (!summaryMap.containsKey(s.getClinicId() + s.getDoctorId() + s.getDoctorName())) {
                RegistrationSummary summary = new RegistrationSummary();
                summary.pretty(s);
                summaryMap.put(s.getClinicId() + s.getDoctorId() + s.getDoctorName(), summary);
                summary.setSignedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getSignedFee(), s.getSignedReceived(), null,
                        s.getSignedDeductPromotionPrice()));
                summary.setNotSignedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getNotSignedFee(), s.getNotSignedReceived(), null,
                        s.getNotSignedDeductPromotionPrice()));
                summary.setServedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getServedFee(), s.getServedReceived(), null,
                        s.getServedDeductPromotionPrice()));
                summary.setServedFirstVisitCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getServedFirstVisitFee(), s.getServedFirstVisitReceived(), null,
                        s.getServedFirstVisitDeductPromotionPrice()));
                summary.setServedRevisitCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getServedRevisitFee(), s.getServedRevisitReceived(), null,
                        s.getServedRevisitDeductPromotionPrice()));
                summary.setNotServedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getNotServedFee(), s.getNotServedReceived(), null,
                        s.getNotServedDeductPromotionPrice()));
                summary.setChargeCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getChargeFee(), s.getChargeReceived(), null,
                        s.getChargeDeductPromotionPrice()));
                summary.setRejectCommissionPrice(StatConfigHandler.getCommissionAmount(dto, s.getRejectFee(), s.getRejectReceived(), null,
                        s.getRejectDeductPromotionPrice()));
            } else {
                RegistrationSummary summary = summaryMap.get(s.getClinicId() + s.getDoctorId() + s.getDoctorName());
                summary.setTotalCount(summary.getTotalCount() + s.getTotalCount());
                summary.setSignedCount(summary.getSignedCount() + s.getSignedCount());
                summary.setNotSignedCount(summary.getNotSignedCount() + s.getNotSignedCount());
                summary.setServedCount(summary.getServedCount() + s.getServedCount());
                summary.setServedFirstVisitCount(summary.getServedFirstVisitCount() + s.getServedFirstVisitCount());
                summary.setServedRevisitCount(summary.getServedRevisitCount() + s.getServedRevisitCount());
                summary.setNotServedCount(summary.getNotServedCount() + s.getNotServedCount());
                summary.setChargeCount(summary.getChargeCount() + s.getChargeCount());
                summary.setRejectCount(summary.getRejectCount() + s.getRejectCount());
                summary.setSignedCommissionPrice(summary.getSignedCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getSignedFee(), s.getSignedReceived(), null,
                                s.getSignedDeductPromotionPrice())));
                summary.setNotSignedCommissionPrice(summary.getNotSignedCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getNotSignedFee(), s.getNotSignedReceived(), null,
                                s.getNotSignedDeductPromotionPrice())));
                summary.setServedCommissionPrice(summary.getServedCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getServedFee(), s.getServedReceived(), null,
                                s.getServedDeductPromotionPrice())));
                summary.setServedFirstVisitCommissionPrice(summary.getServedFirstVisitCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getServedFirstVisitFee(), s.getServedFirstVisitReceived(), null,
                                s.getServedFirstVisitDeductPromotionPrice())));
                summary.setServedRevisitCommissionPrice(summary.getServedRevisitCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getServedRevisitFee(), s.getServedRevisitReceived(), null,
                                s.getServedRevisitDeductPromotionPrice())));
                summary.setNotServedCommissionPrice(summary.getNotServedCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getNotServedFee(), s.getNotServedReceived(), null,
                                s.getNotServedDeductPromotionPrice())));
                summary.setChargeCommissionPrice(summary.getChargeCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getChargeFee(), s.getChargeReceived(), null,
                                s.getChargeDeductPromotionPrice())));
                summary.setRejectCommissionPrice(summary.getRejectCommissionPrice().add(StatConfigHandler
                        .getCommissionAmount(dto, s.getRejectFee(), s.getRejectReceived(), null,
                                s.getRejectDeductPromotionPrice())));
            }
        }
        List<RegistrationSummary> registrationSummaries = new ArrayList<>(summaryMap.values());
        registrationSummaries.sort(Comparator.comparing(RegistrationSummary::getDoctorId));
        return registrationSummaries;
    }

    /**
     * @param params       -
     * @param visitSource1 -
     * @param visitSource2 -
     * @return -
     */
    public List fetchDetail(RegistrationReqParams params, String visitSource1,
                            String visitSource2) {
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        List<RegistrationDetail> details = getMapper(isKudu).selectDetail(TableUtils.getCisTable(), params,
                visitSource1, visitSource2);
        fillDetail(params.getChainId(), params.getClinicId(), details, params);
        return details;
    }

    /**
     * @param chainId  -
     * @param clinicId -
     * @param details  -
     * @param params   params
     */
    private void fillDetail(String chainId, String clinicId, List<RegistrationDetail> details,
                            RegistrationReqParams params) {
        if (details == null || details.size() == 0) {
            return;
        }
        Set<String> patientIds = new HashSet<>();
        for (RegistrationDetail d : details) {
            if (d == null) {
                continue;
            }
            patientIds.add(d.getPatientId());
            patientIds.add(d.getVisitSourceFrom());
        }
        StatConfigDto dto = statConfigService
                .selectConfig(new StatConfigParam(params.getChainId(), params.getClinicId(),
                        params.getDispensaryType()));
        CompletableFuture<Map<String, V2Patient>> patientFuture = CompletableFuture.supplyAsync(() ->
                dimensionQuery.queryPatient(chainId, Lists.newArrayList(patientIds)));
        CompletableFuture<Map<String, Employee>> employeeFuture = CompletableFuture.supplyAsync(() ->
                dimensionQuery.queryEmployeeByChainId(chainId));
        CompletableFuture<Map<String, String>> departmentFuture = CompletableFuture.supplyAsync(() ->
                dimensionQuery.queryBatchDepartmentNamesByOrgan(chainId, clinicId));
        CompletableFuture<Map<String, V2PatientSourceType>> sourceTypeFuture = CompletableFuture.supplyAsync(() ->
                dimensionQuery.queryPatientSourceType(chainId));
        CompletableFuture<Map<String, Organ>> organMapFuture = CompletableFuture.supplyAsync(() ->
                dimensionQuery.queryOrganByParentId(chainId));

        CompletableFuture.allOf(patientFuture, employeeFuture, departmentFuture, sourceTypeFuture,
                organMapFuture).join();

        Map<String, V2Patient> patients = null;
        Map<String, Employee> employees = null;
        Map<String, String> departments = null;
        Map<String, V2PatientSourceType> sourceType = null;
        Map<String, Organ> organMap = null;
        try {
            patients = patientFuture.get();
            employees = employeeFuture.get();
            departments = departmentFuture.get();
            sourceType = sourceTypeFuture.get();
            organMap = organMapFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }

        for (RegistrationDetail d : details) {
            setClinicNameAndDoctorName(employees, organMap, d);
            setPatientInfo(patients, employees, sourceType, d, params);
            setEmployeeAndDepartment(employees, departments, d);
            setSourceInfo(patients, employees, sourceType, d);
            d.setCommissionPrice(StatConfigHandler.getCommissionAmount(dto, d.getOriginalPrice(),
                    d.getReceived(), null, d.getDeductPromotionPrice()));
            d.pretty();
        }
    }

    /**
     * 现在就诊推荐分为3级 1级2级为自定义分类 3级为具体的人
     * 3级新逻辑使用sourceFromType来区分是工作人员还是患者  1:employee表 2：patient表
     * 老逻辑是导医推荐 医生推荐 转诊医生为工作人员 患者推荐为患者
     *
     * @param patients   -
     * @param employees  -
     * @param sourceType -
     * @param d          -
     */
    private void setSourceInfo(Map<String, V2Patient> patients, Map<String, Employee> employees, Map<String,
            V2PatientSourceType> sourceType, RegistrationDetail d) {
        V2PatientSourceType sourceType1 = sourceType.get(d.getVisitSourceLevel1Id()); //先获取一级来源
        String visitSourceLevel1Text;
        if (sourceType1 != null) {
            visitSourceLevel1Text = sourceType1.getName();
        } else {
            visitSourceLevel1Text = "-";
        }
        d.setVisitSourceLevel1Text(visitSourceLevel1Text); //设置一级来源text
        V2PatientSourceType sourceType2 = sourceType.get(d.getVisitSourceLevel2Id());
        if (sourceType2 != null) {
            d.setVisitSourceLevel2Text(sourceType2.getName()); //获取二级来源
        } else {
            d.setVisitSourceLevel2Text("-"); //获取二级来源
        }

        if (d.getVisitSourceFromType() != null && d.getVisitSourceFrom() != null) {
            if (d.getVisitSourceFromType() == 1) {
                Employee employee = employees.get(d.getVisitSourceFrom());
                if (employee != null) {
                    d.setVisitSourceFromName(employee.getName());
                } else {
                    d.setVisitSourceFromName("-");
                }
            } else {
                V2Patient v2Patient = patients.get(d.getVisitSourceFrom());
                if (v2Patient != null) {
                    d.setVisitSourceFromName(v2Patient.getName());
                } else {
                    d.setVisitSourceFromName("-");
                }
            }
        } else {
            if ("医生推荐".equals(visitSourceLevel1Text) || "导医推荐".equals(visitSourceLevel1Text)
                    || "转诊医生".equals(visitSourceLevel1Text)) {
                Employee visitSourceFromEmployee = employees.get(d.getVisitSourceFrom());
                if (visitSourceFromEmployee != null) {
                    d.setVisitSourceFromName(visitSourceFromEmployee.getName());
                } else {
                    d.setVisitSourceFromName("-");
                }
            } else if ("顾客推荐".equals(visitSourceLevel1Text)) {
                V2Patient visitSourceFromPatient = patients.get(d.getVisitSourceFrom());
                if (visitSourceFromPatient != null) {
                    d.setVisitSourceFromName(visitSourceFromPatient.getName());
                } else {
                    d.setVisitSourceFromName("-");
                }
            } else {
                d.setVisitSourceFromName("-"); //没有推荐人的，推荐人姓名设置为 "-"
            }
        }
    }

    /**
     * @param employees   -
     * @param departments -
     * @param d           -
     */
    private void setEmployeeAndDepartment(Map<String, Employee> employees, Map<String, String> departments,
                                          RegistrationDetail d) {
        String department = departments.get(d.getDepartmentId());
        if (department != null && !("".equals(department))) {
            d.setDepartmentName(department);
        } else {
            d.setDepartmentName("-");
        }

        String cashierId = d.getCashierId();
        if ("00000000000000000000000000000000".equals(cashierId)) {
            d.setCashierName("自助支付");
        } else {
            Employee cashier = employees.get(cashierId);
            if (cashier != null) {
                d.setCashierName(cashier.getName());
            } else {
                d.setCashierName("-");
            }
        }

        String createdById = d.getCreatedBy();
        if (createdById.equals("00000000000000000000000000000000")) {
            d.setCreatedByName("自助挂号");
        } else {
            Employee created = employees.get(createdById);
            if (created != null) {
                d.setCreatedByName(created.getName());
            } else {
                d.setCreatedByName("-");
            }
        }
    }

    /**
     * @param patients   -
     * @param employees  -
     * @param sourceType -
     * @param d          -
     */
    private void setPatientInfo(Map<String, V2Patient> patients, Map<String, Employee> employees,
                                Map<String, V2PatientSourceType> sourceType, RegistrationDetail d,
                                RegistrationReqParams param) {
        V2Patient patient = patients.get(d.getPatientId());
        if (patient != null) {
            //设置病人信息
            d.setPatientName(patient.getName());
            d.setPatientSex(patient.getSex());
            d.setPatientSn((patient.getSn() == null || patient.getSn().isEmpty()) ? "-" : patient.getSn());
            if (patient.getMobile() != null && !"".equals(patient.getMobile())) {
                d.setPatientMobile(patient.getMobile());
            } else {
                d.setPatientMobile("-");
            }
        } else {
            d.setPatientName("-");
            d.setPatientSex("-");
            d.setPatientSn("-");
            d.setPatientMobile("-");
        }
        //设置病人来源类型
        V2PatientSourceType dSourceType = sourceType.get(d.getPatientSourceId());
        if (dSourceType != null) {
            d.setPatientSourceText(dSourceType.getName());
        } else {
            d.setPatientSourceText("未指定");
        }

        //设置推荐病人的员工
        Employee sourceEmployee = employees.get(d.getPatientSourceFrom());
        if (sourceEmployee != null) {
            d.setPatientSourceFromName(sourceEmployee.getName());
        } else {
            d.setPatientSourceFromName("-");
        }

        if (!param.getPermission().isEnablePatientMobile()) {
            d.setPatientMobile("-");
        }
    }

    /**
     * @param employees -
     * @param organMap  -
     * @param d         -
     */
    private void setClinicNameAndDoctorName(Map<String, Employee> employees, Map<String, Organ> organMap,
                                            RegistrationDetail d) {
        Organ organ = organMap.get(d.getClinicId());
        if (organ != null) {
            String shortName = organ.getShortName();
            if (shortName != null && !"".equals(shortName)) {
                d.setClinicName(shortName);
            } else {
                d.setClinicName(organ.getName());
            }
        } else {
            d.setClinicName("--");
        }

        if (d.getDoctorName() == null || d.getDoctorName().isEmpty()) {
            Employee doctor = employees.get(d.getDoctorId());
            if (doctor != null) {
                d.setDoctorName(doctor.getName());
            } else {
                d.setDoctorName("未指定");
            }
        }
        d.setReferralDoctorName(EmployeeHandler.handleEmployeeNameById(employees, d.getReferralDoctorId()));
    }

    /**
     * @param params -
     * @return -
     */
    public RpcRegistrationByDoctor getReportRegistrationByDoctor(RegistrationReqParams params) {
        return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), mysqlMapper, hologresRegistrationMapper).selectReportRegistrationByDoctor(TableUtils.getCisTable(), params);
    }

    /**
     * @param params -
     * @return -
     */
    public RpcRegistrationSummary getReportRegistrationSummary(RegistrationReqParams params) {
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        ArrayList<Integer> listStatusV2 = new ArrayList<>();
        if (params.getDisplayStatus() != null) {
            switch (params.getDisplayStatus()) {
                case NUMBER_ONE:
                    listStatusV2.add(NUMBER_TWENTY);
                    listStatusV2.add(NUMBER_TWENTY_TWO);
                    break;
                case NUMBER_TWO:
                    listStatusV2.add(NUMBER_THIRTY);
                    listStatusV2.add(NUMBER_THIRTY_ONE);
                    break;
                case NUMBER_THREE:
                    listStatusV2.add(NUMBER_FORTY);
                    listStatusV2.add(NUMBER_FORTY_ONE);
                    break;
                case NUMBER_FIVE:
                    listStatusV2.add(NUMBER_NINETY);
                    break;
                case NUMBER_SIX:
                    listStatusV2.add(NUMBER_THIRTY);
                    listStatusV2.add(NUMBER_THIRTY_ONE);
                    listStatusV2.add(NUMBER_FORTY);
                    listStatusV2.add(NUMBER_FORTY_ONE);
                    break;
                default:
            }
        } else {
            listStatusV2.add(NUMBER_TWENTY);
            listStatusV2.add(NUMBER_TWENTY_TWO);
            listStatusV2.add(NUMBER_THIRTY);
            listStatusV2.add(NUMBER_THIRTY_ONE);
            listStatusV2.add(NUMBER_FORTY);
            listStatusV2.add(NUMBER_FORTY_ONE);
            listStatusV2.add(NUMBER_NINETY);
            listStatusV2.add(NUMBER_NINETY_ONE);
        }

        if ("00000000000000000000000000000000".equalsIgnoreCase(params.getDoctorId())) {
            params.setDoctorId("");
        }

        ArrayList<Integer> listType = null;
        if (params.getType() != null) {
            listType = new ArrayList<>();
            if (params.getType() == 0) {
                listType.add(0);
                listType.add(NUMBER_TWO);
            } else if (params.getType() == NUMBER_THREE) {
                listType.add(NUMBER_THREE);
            }
        }
        return getMapper(isKudu).selectReportRegistrationSummary(TableUtils.getCisTable(), params,
                listStatusV2, listType);
    }

    /**
     * @param params -params
     * @return -
     */
    public List<RegistrationClinic> availableClinics(RegistrationReqParams params) {
        params.initParams();
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        List<String> clinicIds = getMapper(isKudu).selectClinicIds(TableUtils.getCisTable(), params);
        Map<String, Organ> organs = dimensionQuery.queryOrganByParentId(params.getChainId());

        List<RegistrationClinic> clinics = new ArrayList<>();
        for (String clinicId : clinicIds) {
            RegistrationClinic clinic = new RegistrationClinic();
            clinic.setId(clinicId);
            Organ organ = organs.get(clinicId);
            if (organ != null) {
                clinic.setName(organ.getName());
                clinic.setShortName(organ.getShortName());
                clinic.setNamePy(organ.getNamePy());
                clinic.setNamePyFirst(organ.getNamePyFirst());
                clinics.add(clinic);
            }
        }
        return clinics;
    }

    /**
     * 挂号业绩科室纬度统计
     *
     * @param params -
     * @param dto    -
     * @return -
     */
    public List fetchDepartment(RegistrationReqParams params, StatConfigDto dto) {
        boolean isKudu = storeUtils.isUseKudu(params.getBeginDate(), params.getEndDate());
        List<RegistrationSummary> departments = getMapper(isKudu).selectDepartment(TableUtils.getCisTable(), params);
        Set<String> departmentSet = new HashSet<>();
        try {
            departments.forEach(d -> {
                if (d.getDepartmentId() != null) {
                    departmentSet.add(d.getDepartmentId());
                }
            });
            CompletableFuture<Map<String, Department>> departmentFuture = CompletableFuture.supplyAsync(() ->
                    dimensionQuery.queryDepartments(params.getChainId(), departmentSet));
            CompletableFuture<Map<String, Organ>> organMapFuture = CompletableFuture.supplyAsync(() ->
                    dimensionQuery.queryOrganByParentId(params.getChainId()));
            CompletableFuture.allOf(departmentFuture, organMapFuture).join();
            Map<String, Department> departmentMap = null;
            Map<String, Organ> organMap = null;
            try {
                departmentMap = departmentFuture.get();
                organMap = organMapFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
            for (RegistrationSummary d : departments) {
                if (d == null) {
                    continue;
                }
                setClinicNameAndDepartmentName(departmentMap, organMap, d);
                d.setSignedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, d.getSignedFee(),
                        d.getSignedReceived(), null,
                        d.getSignedDeductPromotionPrice()));
                d.setNotSignedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, d.getNotSignedFee(),
                        d.getNotSignedReceived(), null, d.getNotSignedDeductPromotionPrice()));
                d.setServedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, d.getServedFee(),
                        d.getServedReceived(), null, d.getServedDeductPromotionPrice()));
                d.setServedFirstVisitCommissionPrice(StatConfigHandler.getCommissionAmount(dto,
                        d.getServedFirstVisitFee(), d.getServedFirstVisitReceived(),
                        null, d.getServedFirstVisitDeductPromotionPrice()));
                d.setServedRevisitCommissionPrice(StatConfigHandler.getCommissionAmount(dto, d.getServedRevisitFee(),
                        d.getServedRevisitReceived(), null,
                        d.getServedRevisitDeductPromotionPrice()));
                d.setNotServedCommissionPrice(StatConfigHandler.getCommissionAmount(dto, d.getNotServedFee(),
                        d.getNotServedReceived(), null,
                        d.getNotServedDeductPromotionPrice()));
                d.setRejectCommissionPrice(StatConfigHandler.getCommissionAmount(dto, d.getRejectFee(),
                        d.getRejectReceived(), null,
                        d.getRejectDeductPromotionPrice()));
            }
            departments.sort(Comparator.comparing(RegistrationSummary::getClinicCreateDate));
        } catch (Exception e) {
            logger.error("挂号业绩科室纬度统计fetchDepartment方法异常" + params.getDepartmentId());
            e.printStackTrace();
        }
        return departments;
    }

    /**
     * @param departmentMap -
     * @param organMap      -
     * @param d             -
     */
    private void setClinicNameAndDepartmentName(Map<String, Department> departmentMap, Map<String, Organ> organMap,
                                                RegistrationSummary d) {
        Organ organ = organMap.get(d.getClinicId());
        if (organ != null) {
            String shortName = organ.getShortName();
            d.setClinicCreateDate(organ.getCreatedDate());
            if (shortName != null && !"".equals(shortName)) {
                d.setClinicName(shortName);
            } else {
                d.setClinicName(organ.getName());
            }
        } else {
            d.setClinicName("--");
        }

        Department department = departmentMap.get(d.getDepartmentId());
        if (department != null) {
            String name = department.getName();
            if (name != null && !"".equals(name)) {
                d.setDepartmentName(name);
            } else {
                d.setDepartmentName("其他科室");
            }
        } else {
            d.setDepartmentName("其他科室");
        }
    }

    /**
     * @param
     * @param isKudu : 是否kudu
     * @return
     * @return cn.abc.flink.stat.db.cis.common.RegistrationOverviewMapper
     * @Description: 获取mapper方法
     * @Author: zs
     * @Date: 2022/9/23 10:03
     */
    public RegistrationOverviewMapper getMapper(boolean isKudu) {
        if (isKudu) {
            return hologresRegistrationMapper;
        }
        return mysqlMapper;
    }

    /**
     * @param
     * @param dataList : 疫情登记表数据
     * @return
     * @Description: 处理旅居史集合，具体症状集合，接触史集合，高风险岗位集合
     * @Author: zs
     * @Date: 2022/9/26 10:20
     */
    public void dataProcessing(List<EpidemiologicalHistoryEntity> dataList) {
        logger.info("处理疫情登记表返回数据");
        if (dataList != null && dataList.size() > 0) {
            for (EpidemiologicalHistoryEntity dto : dataList) {
                //临床症状
                if (dto.getIsSuspicious() != null && dto.getIsSuspicious() == 1) {
                    String suspiciousDetail = dto.getSuspiciousDetail();
                    if (suspiciousDetail != null) {
                        List<String> strings = JSON.parseArray(suspiciousDetail, String.class);
                        if (strings.size() > 0) {
                            String s = strings.toString();
                            String replace = s.replace('[', '(').replace(']', ')');
                            dto.setSuspiciousDetail(replace);
                        } else {
                            dto.setSuspiciousDetail(null);
                        }
                    }
                }
                //旅居史
                if (dto.getIsAbroadRegionLiveHistory() != null && dto.getIsAbroadRegionLiveHistory() == 1) {
                    String abroadRegionDetail = dto.getAbroadRegionDetail();
                    if (abroadRegionDetail != null) {
                        List<String> strings = JSON.parseArray(abroadRegionDetail, String.class);
                        if (strings.size() > 0) {
                            String s = strings.toString();
                            String replace = s.replace('[', '(').replace(']', ')');
                            dto.setAbroadRegionDetail(replace);
                        } else {
                            dto.setAbroadRegionDetail(null);
                        }
                    }
                }
                //接触史
                if (dto.getIsConfiremedCasesContactHistory() != null && dto.getIsConfiremedCasesContactHistory() == 1) {
                    String confirmedCasesContactDetail = dto.getConfirmedCasesContactDetail();
                    if (confirmedCasesContactDetail != null) {
                        List<String> strings = JSON.parseArray(confirmedCasesContactDetail, String.class);
                        if (strings.size() > 0) {
                            String s = strings.toString();
                            String replace = s.replace('[', '(').replace(']', ')');
                            dto.setConfirmedCasesContactDetail(replace);
                        } else {
                            dto.setConfirmedCasesContactDetail(null);
                        }
                    }
                }
                //高风险岗位
                if (dto.getIsMedicalWorkers() != null && dto.getIsMedicalWorkers() == 1) {
                    String medicalWorkersDetail = dto.getMedicalWorkersDetail();
                    if (medicalWorkersDetail != null) {
                        List<String> strings = JSON.parseArray(medicalWorkersDetail, String.class);
                        if (strings.size() > 0) {
                            String s = strings.toString();
                            String replace = s.replace('[', '(').replace(']', ')');
                            dto.setMedicalWorkersDetail(replace);
                        } else {
                            dto.setMedicalWorkersDetail(null);
                        }
                    }
                }
            }
        }
    }

    /**
     * @param params -
     * @return -
     */
    public List<RegistrationSummary> getDoctorDepartment(RegistrationReqParams params) {
        return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                mysqlMapper, hologresRegistrationMapper).selectDoctorDepartment(TableUtils.getCisTable(), params);

    }

    /**
     * 挂号台看板 - 汇总
     *
     * @param params -
     * @return -
     */
    public RegistrationGadgeDto gadgetSummary(RegistrationGadgetParam params) {
        RegistrationGadgeDto registrationGadgeDto = new RegistrationGadgeDto();
        List<RegistrationGadgeDto> registrationGadgeDtos = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                mysqlMapper, hologresRegistrationMapper).gadgetSummary(TableUtils.getCisTable(), params, 1);
        if (registrationGadgeDtos != null && registrationGadgeDtos.size() > 0 && registrationGadgeDtos.get(0) != null) {
            registrationGadgeDto = registrationGadgeDtos.get(0);
        }
        BigDecimal revenueAmount = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                mysqlMapper, hologresRegistrationMapper).selectRegistrationAmount(TableUtils.getCisTable(), params, 1);
        if (revenueAmount != null) {
            registrationGadgeDto.setRevenueAmount(revenueAmount);
        }
        return registrationGadgeDto;
    }

    /**
     * 挂号台看板 - 列表
     *
     * @param params       -
     * @return -
     */
    public List<RegistrationGadgeDto> gadgetList(RegistrationGadgetParam params) {
        List<RegistrationGadgeDto> registrationGadgeDto = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                mysqlMapper, hologresRegistrationMapper).gadgetSummary(TableUtils.getCisTable(), params, 0);
        if (registrationGadgeDto == null) {
            return null;
        }
        HashSet<String> empIds = new HashSet<>();
        HashSet<String> deptIds = new HashSet<>();
        for (RegistrationGadgeDto dto:registrationGadgeDto) {
            empIds.add(dto.getDoctorId());
            deptIds.add(dto.getDepartmentId());
        }
        //获取医生，科室
        Map<String, Employee> employeeMap = dimensionQuery.queryEmployeeByChainAndIds(params.getChainId(), empIds);
        Map<String, Department> departmentMap = dimensionQuery.queryDepartments(params.getChainId(), deptIds);
        for (RegistrationGadgeDto dto:registrationGadgeDto) {
            dto.setBase(employeeMap, departmentMap);
        }
        return registrationGadgeDto;
    }

    public StatResponseTotal gadgetListTotal(RegistrationGadgetParam params) {
        StatResponseTotal statResponseTotal = new StatResponseTotal();
        Long total = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                mysqlMapper, hologresRegistrationMapper).gadgetListTotal(TableUtils.getCisTable(), params, 0);
        statResponseTotal.setCount(total);
        return statResponseTotal;
    }

    public Map<String, BigDecimal> gadgetPayMode(RegistrationGadgetParam params) {
        Map<String, BigDecimal> result = new HashMap<>();
        List<PayModeDto> payModeDtos = storeUtils.getMapper(params.getBeginDate(), params.getEndDate(),
                mysqlMapper, hologresRegistrationMapper).selectRegistrationPayMode(TableUtils.getCisTable(), params);
        Map<Integer, V2ChargePayModeConfig> enableMap = dimensionQuery.queryUsingPayModeByChainId(params.getChainId());
        BigDecimal total = BigDecimal.ZERO;
        if (enableMap != null && !enableMap.isEmpty()) {
            for (V2ChargePayModeConfig value : enableMap.values()) {
                result.put(value.getName(), BigDecimal.ZERO);
            }
        }
        if (payModeDtos != null && !payModeDtos.isEmpty()) {
            Map<Integer, String> payMap = dimensionQuery.queryPayTypeTextByChainIdOrClinicId(params.getChainId(), params.getClinicId());
            for (PayModeDto dto : payModeDtos) {
                BigDecimal amount = dto.getAmount();
                result.put(ChargeHandler.handlePayMode(dimensionQuery, dto.getPayMode(), dto.getPaySubMode(), payMap), amount);
                if (amount != null) {
                    total = total.add(amount);
                }
            }
        }
        result.put("合计", total);
        return result;
    }
}
