package cn.abc.flink.stat.service.cis.charge.product.pojos;

import cn.abc.flink.stat.common.SqlUtils;
import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.common.request.params.AbcScStatFilterEmployee;
import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.SnapEmployee;
import cn.abc.flink.stat.es.SuggestService;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.handler.EmployeeHandler;
import cn.abc.flink.stat.service.cis.handler.SelectHandler;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;

/**
 * 收费项目参数对象
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
public class OperationProductReqParams extends AbcScStatRequestParams {

    private static final Logger logger = LoggerFactory.getLogger(OperationProductReqParams.class);

	private String dispensingGoodsClassify = "'1-1', '1-3', '1-12', '1-16', '1-13', '2-0', '2-1', '2-2', '2-3', '2-4', '7-0', '7-1', '7-2', '7-3', '7-4', '7-5', '24-1', '24-2', '24-3', '24-4', '24-5', '24-6'";

    private String employeeId;
    private String cashierId;
    private String feeType1;
    private String feeType2;
    private String productId;
    private String sort;
    private String order;
    private String keyword;

    private Integer offset;
    private Integer size;

    private String fee1;
    private String fee2;

    /**
     * 开单人
     */
    private List<AbcScStatFilterEmployee> employees;

    private String employeeSql;

    /**
     * 欠还款统计口径配置Sql
     */
    private String oweSql = "record_source_type in (0, 1)";

    /**
     * 套餐配置sql
     */
    private String composeSql;

    private String dispensingWhere;

    /**
     * 标签ids
     */
    private List<Long> tagIds;
    private String tagIdSql;
    private int isExcport;

    /**
     * 利润分类
     */
    private List<Long> profitCategoryTypeId;
    private String profitCategoryTypeIdSql;

    /**
     * 最小毛利和最大毛利
     */
    private BigDecimal minProfit;
    private BigDecimal maxProfit;

    @ApiModelProperty("首次入库开始时间")
    private String firstInBeginDate;

    @ApiModelProperty("首次入库结束时间")
    private String firstInEndDate;

    /**
     * 通过配置设置统计口径
     *
     * @param config 配置
     */
    public void initParams(StatConfigDto config, DimensionQuery query) {
        this.beginDate = TimeUtils.appendBegin(this.beginDate);
        this.endDate = TimeUtils.appendEnd(this.endDate);
        this.initDs();
        this.initQueryTimeRanges();

        if (config != null) {
            if (config.getArrearsStatTiming() != null) {
                if (config.getArrearsStatTiming() == StatConfigDto.ArrearsStatTiming.ARREARS_STAT_TIMING_ARREARS) {
                    this.oweSql = "record_source_type in (0, 1)";
                } else if (config.getArrearsStatTiming() == StatConfigDto.ArrearsStatTiming.ARREARS_STAT_TIMING_REPAYMENT) {
                    this.oweSql = "record_source_type in (0, 2)";
                }
            }
        }
        this.composeSql = config.buildSqlAboutComposeType();
        initWhere();

        if (StringUtils.isBlank(this.employeeId)) {
            this.employeeId = null;
        }
        if (StringUtils.isBlank(this.cashierId)) {
            this.cashierId = null;
        }
        if (StringUtils.isBlank(this.feeType1)) {
            this.feeType1 = null;
        }
        if (StringUtils.isBlank(this.feeType2)) {
            this.feeType2 = null;
        }
        if (StringUtils.isBlank(this.productId)) {
            this.productId = null;
        } else {
            this.productId = "'" + this.productId + "'";
        }
        if (StringUtils.isBlank(this.sort)) {
            this.sort = null;
        } else {
            this.sort = "profitStr".equals(this.sort) ? "profit" : this.sort;
        }
        if (StringUtils.isBlank(this.order)) {
            this.order = null;
        }
        if (StringUtils.isBlank(this.keyword)) {
            this.keyword = null;
        }
        if (this.employees != null && !this.employees.isEmpty()) {
            List<SnapEmployee> se = query.querySnapByEmployeeAndName(this.chainId, this.employees);
            this.employeeSql = EmployeeHandler.handleEmployeeSnapSql("doctor_id", "doctor_snap_id", "seller_id", true, se);
        }
        if (this.minProfit != null && this.maxProfit != null) {
            if (this.minProfit.compareTo(this.maxProfit) > 0) {
                //确保最大和最小是绝对的
                BigDecimal temp = this.minProfit;
                this.minProfit = this.maxProfit;
                this.maxProfit = temp;
            }
        }
        this.profitCategoryTypeIdSql = SqlUtils.buildLongSqlByList("profit_category_type", this.profitCategoryTypeId);
        this.firstInBeginDate = TimeUtils.appendBegin(this.firstInBeginDate);
        this.firstInEndDate = TimeUtils.appendEnd(this.firstInEndDate);
    }

    /**
     * 套餐是否拆分 0 不拆分 1拆分
     */
    private Integer isComposeShareEqually;

    /**
     * 构建分类的SQL
     */
    public void setFee() {
        this.fee1 = SelectHandler.buildChargeFeeClassify1Sql(this.feeType1);
        this.fee2 = SelectHandler.buildChargeFeeClassify2Sql(this.feeType2);
    }

    /**
     * 搜索goodsId
     *
     * @param suggestService 搜索服务
     */
    public void searchProductIds(SuggestService suggestService) {
        logger.info("============searchProductIds===============");
        if (StringUtils.isBlank(this.productId) && !StringUtils.isBlank(this.keyword)) {
            logger.info("chainId:{}", this.chainId);
            logger.info("headerClinicId:{}", this.params.getHeaderClinicId());
            logger.info("clinicId:{}", this.clinicId);
            logger.info("employeeId:{}", this.params.getEmployeeId());
            logger.info("keyword:{}", this.keyword);
            List<String> s = suggestService.fetchIds(
                    this.chainId, this.clinicId, this.params.getEmployeeId(), this.keyword);
            this.productId = "'" + String.join("','", s) + "'";
            logger.info("业务获取的productId:{}", this.productId);
        }
        logger.info("productId:{}", this.productId);
    }

    private void initWhere() {
        StringBuilder dispensingSb = new StringBuilder();
        if (CisJWTUtils.CIS_HIS_TYPE_HOSPITAL.equals(this.hisType)) {
            dispensingSb.append(" and form_type = 0");
        }
        this.dispensingWhere = dispensingSb.toString();
    }

    public String getEmployeeId() {
        return this.employeeId;
    }

    public String getCashierId() {
        return this.cashierId;
    }

    public String getFeeType1() {
        return this.feeType1;
    }

    public String getFeeType2() {
        return this.feeType2;
    }

    public String getProductId() {
        return this.productId;
    }

    public String getSort() {
        return this.sort;
    }

    public String getOrder() {
        return this.order;
    }

    public String getKeyword() {
        return this.keyword;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public Integer getSize() {
        return this.size;
    }

    public String getFee1() {
        return this.fee1;
    }

    public String getFee2() {
        return this.fee2;
    }

    public List<AbcScStatFilterEmployee> getEmployees() {
        return this.employees;
    }

    public String getEmployeeSql() {
        return this.employeeSql;
    }

    public String getComposeSql() {
        return this.composeSql;
    }

    public String getDispensingWhere() {
        return this.dispensingWhere;
    }

    public List<Long> getTagIds() {
        return this.tagIds;
    }

    public String getTagIdSql() {
        return this.tagIdSql;
    }

    public int getIsExcport() {
        return this.isExcport;
    }

    public List<Long> getProfitCategoryTypeId() {
        return this.profitCategoryTypeId;
    }

    public BigDecimal getMinProfit() {
        return this.minProfit;
    }

    public BigDecimal getMaxProfit() {
        return this.maxProfit;
    }

    public Integer getIsComposeShareEqually() {
        return this.isComposeShareEqually;
    }


    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public void setCashierId(String cashierId) {
        this.cashierId = cashierId;
    }

    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public void setFeeType2(String feeType2) {
        this.feeType2 = feeType2;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public void setFee1(String fee1) {
        this.fee1 = fee1;
    }

    public void setFee2(String fee2) {
        this.fee2 = fee2;
    }

    public void setEmployees(List<AbcScStatFilterEmployee> employees) {
        this.employees = employees;
    }

    public void setEmployeeSql(String employeeSql) {
        this.employeeSql = employeeSql;
    }

    public void setComposeSql(String composeSql) {
        this.composeSql = composeSql;
    }

    public void setDispensingWhere(String dispensingWhere) {
        this.dispensingWhere = dispensingWhere;
    }

    public void setTagIds(List<Long> tagIds) {
        this.tagIds = tagIds;
    }

    public void setTagIdSql(String tagIdSql) {
        this.tagIdSql = tagIdSql;
    }

    public void setIsExcport(int isExcport) {
        this.isExcport = isExcport;
    }

    public void setProfitCategoryTypeId(List<Long> profitCategoryTypeId) {
        this.profitCategoryTypeId = profitCategoryTypeId;
    }

    public void setMinProfit(BigDecimal minProfit) {
        this.minProfit = minProfit;
    }

    public void setMaxProfit(BigDecimal maxProfit) {
        this.maxProfit = maxProfit;
    }

    public void setIsComposeShareEqually(Integer isComposeShareEqually) {
        this.isComposeShareEqually = isComposeShareEqually;
    }

    public String getDispensingGoodsClassify() {
        return dispensingGoodsClassify;
    }

    public void setDispensingGoodsClassify(String dispensingGoodsClassify) {
        this.dispensingGoodsClassify = dispensingGoodsClassify;
    }

    public String getOweSql() {
        return oweSql;
    }

    public void setOweSql(String oweSql) {
        this.oweSql = oweSql;
    }

    public String getProfitCategoryTypeIdSql() {
        return profitCategoryTypeIdSql;
    }

    public void setProfitCategoryTypeIdSql(String profitCategoryTypeIdSql) {
        this.profitCategoryTypeIdSql = profitCategoryTypeIdSql;
    }

    public String getFirstInBeginDate() {
        return firstInBeginDate;
    }

    public void setFirstInBeginDate(String firstInBeginDate) {
        this.firstInBeginDate = firstInBeginDate;
    }

    public String getFirstInEndDate() {
        return firstInEndDate;
    }

    public void setFirstInEndDate(String firstInEndDate) {
        this.firstInEndDate = firstInEndDate;
    }
}
