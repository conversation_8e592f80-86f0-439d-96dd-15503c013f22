package cn.abc.flink.stat.service.cis.achievement.examination.domain;



import java.math.BigDecimal;

/**
 * @description:
 * @author: dy
 * @create: 2021-08-18 11:46
 */
public class AchievementExaminationDetailDao {
    private BigDecimal amount;
    private BigDecimal amountNum;
    private String chargeFormItemId;
    private String clinicId;
    private String clinicName;
    private Integer count;
    private Integer countNum;
    private String created;
    private String createdBy;
    private String createdByName;
    private Integer customTypeId;
    private String goodsName;
    private String goodsId;
    private Integer goodsType;
    private String goodsTypeName;
    private String testTime;
    private String patientName;
    private String patientAge;
    private String patientSex;
    /**
     * 患者档案号
     */
    private String patientSn;
    private String patientId;
    private String sellerId;
    private Long sellerSnapId;
    private String sellerName;
    private Integer subType;
    private String subTypeName;
    private String testerId;
    private String testerName;

    private BigDecimal originPrice;
    private BigDecimal deductPrice;
    private BigDecimal commissionPrice;
    /**
     * 成本
     */
    private BigDecimal costPrice;
    /**
     * 毛利
     */
    private BigDecimal gross;
    /**
     * 毛利率
     */
    private String profit;
    /**
     * 送检机构
     */
    private String supplierId;
    private String supplierName;
    private Integer pharmacyType;
    private String coClinicId;
    /**
     * 审核人
     */
    private String checkerId;
    private String checkerName;
    /**
     * 开单来源
     */
    private String chargeSource;
    /**
     * 诊断标志
     */
    private String diagnosisFlag;
    /**
     * 会诊医生
     */
    private String consultationDoctorId;
    private String consultationDoctorName;
    /**
     * 记录医生
     */
    private String recordDoctorId;
    private String recordDoctorName;

    public BigDecimal getAmount() {
        return this.amount;
    }

    public BigDecimal getAmountNum() {
        return this.amountNum;
    }

    public String getChargeFormItemId() {
        return this.chargeFormItemId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getClinicName() {
        return this.clinicName;
    }

    public Integer getCount() {
        return this.count;
    }

    public Integer getCountNum() {
        return this.countNum;
    }

    public String getCreated() {
        return this.created;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public String getCreatedByName() {
        return this.createdByName;
    }

    public Integer getCustomTypeId() {
        return this.customTypeId;
    }

    public String getGoodsName() {
        return this.goodsName;
    }

    public String getGoodsId() {
        return this.goodsId;
    }

    public Integer getGoodsType() {
        return this.goodsType;
    }

    public String getGoodsTypeName() {
        return this.goodsTypeName;
    }

    public String getTestTime() {
        return this.testTime;
    }

    public String getPatientName() {
        return this.patientName;
    }

    public String getPatientId() {
        return this.patientId;
    }

    public String getSellerId() {
        return this.sellerId;
    }

    public Long getSellerSnapId() {
        return this.sellerSnapId;
    }

    public String getSellerName() {
        return this.sellerName;
    }

    public Integer getSubType() {
        return this.subType;
    }

    public String getSubTypeName() {
        return this.subTypeName;
    }

    public String getTesterId() {
        return this.testerId;
    }

    public String getTesterName() {
        return this.testerName;
    }

    public BigDecimal getOriginPrice() {
        return this.originPrice;
    }

    public BigDecimal getDeductPrice() {
        return this.deductPrice;
    }

    public BigDecimal getCommissionPrice() {
        return this.commissionPrice;
    }

    public BigDecimal getCostPrice() {
        return this.costPrice;
    }

    public BigDecimal getGross() {
        return this.gross;
    }

    public String getProfit() {
        return this.profit;
    }

    public String getSupplierId() {
        return this.supplierId;
    }

    public String getSupplierName() {
        return this.supplierName;
    }

    public Integer getPharmacyType() {
        return this.pharmacyType;
    }

    public String getCoClinicId() {
        return this.coClinicId;
    }


    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setAmountNum(BigDecimal amountNum) {
        this.amountNum = amountNum;
    }

    public void setChargeFormItemId(String chargeFormItemId) {
        this.chargeFormItemId = chargeFormItemId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public void setCountNum(Integer countNum) {
        this.countNum = countNum;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    public void setCustomTypeId(Integer customTypeId) {
        this.customTypeId = customTypeId;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public void setGoodsTypeName(String goodsTypeName) {
        this.goodsTypeName = goodsTypeName;
    }

    public void setTestTime(String testTime) {
        this.testTime = testTime;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public void setSellerSnapId(Long sellerSnapId) {
        this.sellerSnapId = sellerSnapId;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public void setSubTypeName(String subTypeName) {
        this.subTypeName = subTypeName;
    }

    public void setTesterId(String testerId) {
        this.testerId = testerId;
    }

    public void setTesterName(String testerName) {
        this.testerName = testerName;
    }

    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    public void setDeductPrice(BigDecimal deductPrice) {
        this.deductPrice = deductPrice;
    }

    public void setCommissionPrice(BigDecimal commissionPrice) {
        this.commissionPrice = commissionPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public void setGross(BigDecimal gross) {
        this.gross = gross;
    }

    public void setProfit(String profit) {
        this.profit = profit;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public void setPharmacyType(Integer pharmacyType) {
        this.pharmacyType = pharmacyType;
    }

    public void setCoClinicId(String coClinicId) {
        this.coClinicId = coClinicId;
    }

    public String getCheckerId() {
        return checkerId;
    }

    public void setCheckerId(String checkerId) {
        this.checkerId = checkerId;
    }

    public String getCheckerName() {
        return checkerName;
    }

    public void setCheckerName(String checkerName) {
        this.checkerName = checkerName;
    }

    public String getChargeSource() {
        return chargeSource;
    }

    public void setChargeSource(String chargeSource) {
        this.chargeSource = chargeSource;
    }

    public String getDiagnosisFlag() {
        return diagnosisFlag;
    }

    public void setDiagnosisFlag(String diagnosisFlag) {
        this.diagnosisFlag = diagnosisFlag;
    }

    public String getConsultationDoctorId() {
        return consultationDoctorId;
    }

    public void setConsultationDoctorId(String consultationDoctorId) {
        this.consultationDoctorId = consultationDoctorId;
    }

    public String getConsultationDoctorName() {
        return consultationDoctorName;
    }

    public void setConsultationDoctorName(String consultationDoctorName) {
        this.consultationDoctorName = consultationDoctorName;
    }

    public String getRecordDoctorId() {
        return recordDoctorId;
    }

    public void setRecordDoctorId(String recordDoctorId) {
        this.recordDoctorId = recordDoctorId;
    }

    public String getRecordDoctorName() {
        return recordDoctorName;
    }

    public void setRecordDoctorName(String recordDoctorName) {
        this.recordDoctorName = recordDoctorName;
    }

    public String getPatientAge() {
        return patientAge;
    }

    public void setPatientAge(String patientAge) {
        this.patientAge = patientAge;
    }

    public String getPatientSex() {
        return patientSex;
    }

    public void setPatientSex(String patientSex) {
        this.patientSex = patientSex;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }
}
