package cn.abc.flink.stat.service.cis.registration.domain.response;

import java.util.HashMap;
import java.util.Map;

import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_FORTY;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_FORTY_ONE;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_NINETY;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_NINETY_ONE;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_NINETY_TWO;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_TEN;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_THIRTY;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_THIRTY_ONE;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_TWENTY;
import static cn.abc.flink.stat.common.contants.CommonConstants.NUMBER_TWENTY_ONE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/26 6:02 下午
 * @modified ZhouLei
 */
public final class RegistrationStatusMap {
    private static Map<Integer, String> statusMap = new HashMap<>();

    private RegistrationStatusMap() {
    }

    static {
        statusMap.put(0, "未知");
        statusMap.put(NUMBER_TEN, "待支付");
        statusMap.put(NUMBER_TWENTY, "待签到");
        statusMap.put(NUMBER_TWENTY_ONE, "待签到");
        statusMap.put(NUMBER_THIRTY, "待诊");
        statusMap.put(NUMBER_THIRTY_ONE, "待诊");
        statusMap.put(NUMBER_FORTY, "已诊");
        statusMap.put(NUMBER_FORTY_ONE, "已诊");
        statusMap.put(NUMBER_NINETY, "已退");
        statusMap.put(NUMBER_NINETY_ONE, "已过期");
        statusMap.put(NUMBER_NINETY_TWO, "取消");
    }

    public static Map<Integer, String> getStatusMap() {
        return statusMap;
    }
}
