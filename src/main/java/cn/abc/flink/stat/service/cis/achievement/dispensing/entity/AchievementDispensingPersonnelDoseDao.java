package cn.abc.flink.stat.service.cis.achievement.dispensing.entity;



import java.math.BigDecimal;

/**
 * AchievementDispensingPersonnelDoseDao
 */
public class AchievementDispensingPersonnelDoseDao {
    private String chainId;
    private String clinicId;
    private String dispensedBy;
    private Byte prescriptionType;
    private BigDecimal doseAndUnitCount;
    private BigDecimal doseCount;

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getDispensedBy() {
        return this.dispensedBy;
    }

    public Byte getPrescriptionType() {
        return this.prescriptionType;
    }

    public BigDecimal getDoseAndUnitCount() {
        return this.doseAndUnitCount;
    }

    public BigDecimal getDoseCount() {
        return this.doseCount;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setDispensedBy(String dispensedBy) {
        this.dispensedBy = dispensedBy;
    }

    public void setPrescriptionType(Byte prescriptionType) {
        this.prescriptionType = prescriptionType;
    }

    public void setDoseAndUnitCount(BigDecimal doseAndUnitCount) {
        this.doseAndUnitCount = doseAndUnitCount;
    }

    public void setDoseCount(BigDecimal doseCount) {
        this.doseCount = doseCount;
    }

}
