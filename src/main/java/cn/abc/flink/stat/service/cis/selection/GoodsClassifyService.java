package cn.abc.flink.stat.service.cis.selection;


import cn.abc.flink.stat.common.StoreUtils;
import cn.abc.flink.stat.common.TableUtils;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnAchievementChargeMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.ArnSelectionMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresAchievementChargeMapper;
import cn.abc.flink.stat.db.cis.hologres.dao.HologresSelectionMapper;
import cn.abc.flink.stat.db.his.hologres.dao.HoloHisAchievementChargeMapping;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.dimension.domain.V2GoodsCustomType;
import cn.abc.flink.stat.dimension.domain.V2GoodsFeeType;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.handler.AchievementChargeHandler;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargeFeeClassify;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.DispensingFeeFirstClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.DispensingFeeSecondClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.FeeFirstClassifyParam;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.FeeSecondClassifyParam;
import cn.abc.flink.stat.service.cis.config.StatConfigService;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
import cn.abc.flink.stat.service.cis.config.pojo.StatConfigParam;
import cn.abc.flink.stat.service.cis.selection.entity.CommSelectParams;
import cn.abc.flink.stat.service.cis.selection.entity.FeeClassifyParam;
import cn.abc.flink.stat.service.cis.selection.entity.GoodsClassifyDao;
import cn.abc.flink.stat.service.cis.selection.handler.FeeTypeHandler;
import cn.abc.flink.stat.service.cis.selection.handler.GoodsClassifyHandler;
import cn.abc.flink.stat.service.cis.selection.pojo.FeeClassifyResp;
import cn.abc.flink.stat.service.cis.selection.pojo.GoodsClassifyResp;
import cn.abc.flink.stat.service.his.achievement.charge.domain.HisAchievementChargeReqParams;
import cn.abc.flink.stat.source.AbcThreadExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

/**
 * -GoodsClassifyService
 */
@Service
public class GoodsClassifyService {

    @Autowired
    private DimensionQuery query;

    @Autowired
    private StoreUtils storeUtils;

    @Resource
    private AbcThreadExecutor executor;

    @Resource
    private HologresSelectionMapper hologresSelectionMapper;

    @Resource
    private ArnSelectionMapper arnMapper;

    @Resource
    private GoodsClassifyHandler goodsClassifyHandler;

    public List<GoodsClassifyResp> selectDispensingGoodsClassify(CommSelectParams params) throws Exception {
        CompletableFuture<Map<Integer, V2GoodsCustomType>> customTypeF = executor.supplyAsync(() ->
                query.queryProductCustomTypeTextByChainId(params.getChainId()));
        if (params.getScope() == 1) {
            //住院项目分类
            CompletableFuture<List<GoodsClassifyDao>> dispensingGoodsClassifyF = executor.supplyAsync(() ->
                    getDispensingGoodsClassify(params));
            CompletableFuture.allOf(customTypeF,dispensingGoodsClassifyF).join();
           return goodsClassifyHandler.handleDispensingGoodsClassifyTypeData(customTypeF.get(),params,
                   dispensingGoodsClassifyF.get());
        }
        return null;
    }

    private List<GoodsClassifyDao> getDispensingGoodsClassify(CommSelectParams params) {
        return storeUtils.getMapper(params.getBeginDate(), params.getEndDate(), arnMapper, hologresSelectionMapper)
                .selectDispensingGoodsClassify(TableUtils.getCisTable(), params);
    }

}
