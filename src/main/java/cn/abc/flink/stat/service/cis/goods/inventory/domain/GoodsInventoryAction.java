package cn.abc.flink.stat.service.cis.goods.inventory.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.List;

@ApiModel("进销存动作实体")
public class GoodsInventoryAction {

    @ApiModelProperty("动作")
    private String action;

    @ApiModelProperty("发药场景")
    private Integer scene;

    @ApiModelProperty("发药类型")
    private Integer dispenseType;

    @ApiModelProperty("发药方式")
    private Integer dispensingMethod;

    @ApiModelProperty("动作名称")
    private String name;

    @ApiModelProperty("动作别名")
    private String alias;

    @ApiModelProperty("子动作")
    private List<GoodsInventoryAction> childList;

    public GoodsInventoryAction() {
    }

    public GoodsInventoryAction(String name) {
        this.name = name;
    }

    public GoodsInventoryAction(String name, String alias) {
        this.name = name;
        this.alias = alias;
    }

    public String getAction() {
        return this.action;
    }

    public Integer getScene() {
        return this.scene;
    }

    public Integer getDispenseType() {
        return this.dispenseType;
    }

    public Integer getDispensingMethod() {
        return this.dispensingMethod;
    }

    public String getName() {
        return this.name;
    }

    public String getAlias() {
        return this.alias;
    }

    public List<GoodsInventoryAction> getChildList() {
        return this.childList;
    }


    public void setAction(String action) {
        this.action = action;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public void setDispenseType(Integer dispenseType) {
        this.dispenseType = dispenseType;
    }

    public void setDispensingMethod(Integer dispensingMethod) {
        this.dispensingMethod = dispensingMethod;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public void setChildList(List<GoodsInventoryAction> childList) {
        this.childList = childList;
    }

}
