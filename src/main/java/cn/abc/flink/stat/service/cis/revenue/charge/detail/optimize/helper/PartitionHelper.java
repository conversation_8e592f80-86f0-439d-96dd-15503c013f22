package cn.abc.flink.stat.service.cis.revenue.charge.detail.optimize.helper;
import java.util.*;

/**
 * 分区查询辅助类
 * 根据各月分区的行数和分页参数，定位需要查询的分区区间及新的 limit/offset
 */
public class PartitionHelper {

    /**
     * 分区范围及新的分页参数
     */
    public static class PartitionRange {
        private final int startMonth;
        private final int endMonth;
        private final int limit;
        private final int offset;

        public PartitionRange(int startMonth, int endMonth, int limit, int offset) {
            this.startMonth = startMonth;
            this.endMonth = endMonth;
            this.limit = limit;
            this.offset = offset;
        }

        public int getStartMonth() {
            return startMonth;
        }

        public int getEndMonth() {
            return endMonth;
        }

        public int getLimit() {
            return limit;
        }

        public int getOffset() {
            return offset;
        }

        @Override
        public String toString() {
            return String.format("PartitionRange{startMonth=%d, endMonth=%d, limit=%d, offset=%d}",
                    startMonth, endMonth, limit, offset);
        }
    }

    /**
     * 计算应查询的分区范围及新的 limit/offset。
     *
     * @param partitionCounts 分区->行数 Map，Key 为 YYYYMM 格式的整数，如 202404
     * @param limit           原始 limit
     * @param offset          原始 offset
     * @return PartitionRange，包含按升序排列的开始分区、结束分区，以及新的 limit 和 offset
     */
    public static PartitionRange calcPartitionRange(Map<Integer, Integer> partitionCounts,
                                                    int limit, int offset) {
        if (partitionCounts == null || partitionCounts.isEmpty()) {
            throw new IllegalArgumentException("partitionCounts 不能为空");
        }
        // 按分区降序排序（最新月份到最旧月份）
        List<Integer> months = new ArrayList<>(partitionCounts.keySet());
        months.sort(Comparator.reverseOrder());

        // 跳过 offset 行，定位起始分区索引
        int skipped = 0;
        int startIdx = 0;
        for (; startIdx < months.size(); startIdx++) {
            int month = months.get(startIdx);
            int cnt = partitionCounts.get(month);
            if (skipped + cnt <= offset) {
                skipped += cnt;
            } else {
                break;
            }
        }
        if (startIdx >= months.size()) {
            throw new IllegalArgumentException("offset 超出总行数");
        }
        // 计算在起始分区内需跳过的行数作为新的 offset
        int offsetWithinStart = offset - skipped;
        // 根据 limit 累积分区，定位结束分区索引
        int need = limit;
        int idx = startIdx;
        for (; idx < months.size() && need > 0; idx++) {
            int cnt = partitionCounts.get(months.get(idx));
            if (idx == startIdx) {
                cnt -= offsetWithinStart;
            }
            need -= cnt;
        }
        int endIdx = Math.min(idx, months.size()) - 1;

        // 获取原始索引对应的月份
        int m1 = months.get(startIdx);
        int m2 = months.get(endIdx);
        // 确保 startMonth < endMonth（升序）
        int startMonth = Math.min(m1, m2);
        int endMonth = Math.max(m1, m2);
        return new PartitionRange(startMonth, endMonth, limit, offsetWithinStart);
    }
}
