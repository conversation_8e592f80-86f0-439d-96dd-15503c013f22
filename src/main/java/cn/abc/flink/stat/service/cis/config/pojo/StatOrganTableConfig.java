package cn.abc.flink.stat.service.cis.config.pojo;



import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/12 11:53 上午
 */
public class StatOrganTableConfig {
    private String id;
    private String clinicId;
    private String name;
    /**
     * table类型包含的业务范围，1:门诊，2:住院，3:体检
     */
    private List<Integer> businessScope;
    /**
     * 组件列表(有序)
     */
    private List<String> components;

    public void pretty(StatOrganTableConfigDao configDao) {
        this.id = configDao.getId();
        this.clinicId = configDao.getClinicId();
        this.name = configDao.getName();
    }

    public String getId() {
        return this.id;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getName() {
        return this.name;
    }

    public List<Integer> getBusinessScope() {
        return this.businessScope;
    }

    public List<String> getComponents() {
        return this.components;
    }


    public void setId(String id) {
        this.id = id;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBusinessScope(List<Integer> businessScope) {
        this.businessScope = businessScope;
    }

    public void setComponents(List<String> components) {
        this.components = components;
    }

}
