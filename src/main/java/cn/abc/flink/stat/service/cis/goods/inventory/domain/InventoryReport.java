package cn.abc.flink.stat.service.cis.goods.inventory.domain;

import cn.abc.flink.stat.common.ABCNumberUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;


import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/8/24 6:28 下午
 */
public class InventoryReport {


    @ApiModelProperty("一级分类类型id")
    private String feeType1;

    @ApiModelProperty("一级分类类型名称")
    private String feeType1Name;

    @ApiModelProperty("二级分类类型id")
    private Integer feeType2;

    @ApiModelProperty("二级分类类型名称")
    private String feeType2Name;

    @ApiModelProperty("期初成本")
    private BigDecimal beginCost = BigDecimal.ZERO;

    @ApiModelProperty("期末成本")
    private BigDecimal endCost = BigDecimal.ZERO;

    @ApiModelProperty("初始化入库成本含税")
    private BigDecimal inInitCost = BigDecimal.ZERO;

    @ApiModelProperty("采购成本")
    private BigDecimal purchaseInCost = BigDecimal.ZERO;

    @ApiModelProperty("调拨入库成本")
    private BigDecimal allotInCost = BigDecimal.ZERO;

    @ApiModelProperty("盘盈成本")
    private BigDecimal checkInCost = BigDecimal.ZERO;

    @ApiModelProperty("领用入库成本")
    private BigDecimal inReceiveCostAmount = BigDecimal.ZERO;

    @ApiModelProperty("规格修改成本")
    private BigDecimal inSpecificationModificationCost = BigDecimal.ZERO;

    @ApiModelProperty("入库合计成本")
    private BigDecimal inTotalCost = BigDecimal.ZERO;

    @ApiModelProperty("发药售价")
    private BigDecimal dispensePrice = BigDecimal.ZERO;

    @ApiModelProperty("发药成本")
    private BigDecimal dispenseCost = BigDecimal.ZERO;

    @ApiModelProperty("门诊发药售价")
    private BigDecimal outPatientDispensePrice = BigDecimal.ZERO;

    @ApiModelProperty("门诊发药成本")
    private BigDecimal outPatientDispenseCost = BigDecimal.ZERO;

    @ApiModelProperty("住院药房发药售价")
    private BigDecimal hospitalPharmacyDispensePrice = BigDecimal.ZERO;

    @ApiModelProperty("住院药房发药成本")
    private BigDecimal hospitalPharmacyDispenseCost = BigDecimal.ZERO;

    @ApiModelProperty("住院自动发药售价")
    private BigDecimal hospitalAutomaticDispensePrice = BigDecimal.ZERO;

    @ApiModelProperty("住院自动发药成本")
    private BigDecimal hospitalAutomaticDispenseCost = BigDecimal.ZERO;

    @ApiModelProperty("领用出库成本")
    private BigDecimal collectOutCost = BigDecimal.ZERO;

    @ApiModelProperty("调拨出库成本")
    private BigDecimal allotOutCost = BigDecimal.ZERO;

    @ApiModelProperty("破损出库成本")
    private BigDecimal damagedOutCost = BigDecimal.ZERO;

    @ApiModelProperty("消耗成本")
    private BigDecimal outDepartmentConsumptionCostAmount = BigDecimal.ZERO;

    @ApiModelProperty("其他出库成本")
    private BigDecimal outOtherCostAmount = BigDecimal.ZERO;

    @ApiModelProperty("盘亏出库成本")
    private BigDecimal checkOutCost = BigDecimal.ZERO;

    @ApiModelProperty("出库合计成本")
    private BigDecimal outTotalCost = BigDecimal.ZERO;

    @ApiModelProperty("医院药房发药-未结算数量")
    private BigDecimal hospitalPharmacyNoSettleDispenseCount = BigDecimal.ZERO;

    @ApiModelProperty("医院自动发药-未结算数量")
    private BigDecimal hospitalAutomaticNoSettleDispenseCount = BigDecimal.ZERO;

    /**
     * @param param param
     * @return InventoryGoods
     */
    public InventoryReport pretty(GoodsInventoryParam param) {
        if (StrUtil.isBlank(this.feeType1Name)) {
            this.feeType1Name = "-";
        }
        if (StrUtil.isBlank(this.feeType2Name)) {
            this.feeType2Name = "-";
        }

        // 期初以及期末数据初始化
        beginAndEndDataInitialize();

        // 入库数据初始化
        inDataInitialize();

        // 出库数据初始化
        outDataInitialize(param.getIsExport());
        if (!param.getPermission().isEnableCost()) {
            setCostNull();
        }
        return this;
    }

    /**
     * 期初以及期末数据初始化
     */
    private void beginAndEndDataInitialize() {
        this.beginCost = ABCNumberUtils.round4Pretty(this.beginCost);
        this.endCost = ABCNumberUtils.round4Pretty(this.endCost);
    }

    /**
     * 入库数据初始化
     *
     */
    private void inDataInitialize() {
        this.purchaseInCost = ABCNumberUtils.round2Pretty(this.purchaseInCost);
        this.inReceiveCostAmount = ABCNumberUtils.round4Pretty(this.inReceiveCostAmount);
        this.allotInCost = ABCNumberUtils.round4Pretty(this.allotInCost);
        this.checkInCost = ABCNumberUtils.round4Pretty(this.checkInCost);
        this.inSpecificationModificationCost = ABCNumberUtils.round4Pretty(this.inSpecificationModificationCost);
        this.inTotalCost = ABCNumberUtils.round4Pretty(this.inTotalCost);
    }

    /**
     * 出库数据初始化
     *
     * @param isExport 是否是导出
     */
    private void outDataInitialize(Boolean isExport) {
        if (isExport) {
            // 发药出库
            this.dispensePrice = ABCNumberUtils.round4Pretty(this.dispensePrice);
            this.hospitalPharmacyDispensePrice = ABCNumberUtils.round4Pretty(this.hospitalPharmacyDispensePrice);
            this.hospitalAutomaticDispensePrice = ABCNumberUtils.round4Pretty(this.hospitalAutomaticDispensePrice);
        } else {
            // 发药出库
            this.dispensePrice = ABCNumberUtils.round2Pretty(this.dispensePrice);
            this.hospitalPharmacyDispensePrice = ABCNumberUtils.round2Pretty(this.hospitalPharmacyDispensePrice);
            this.hospitalAutomaticDispensePrice = ABCNumberUtils.round2Pretty(this.hospitalAutomaticDispensePrice);
        }
        this.dispenseCost = ABCNumberUtils.round4Pretty(this.dispenseCost);
        this.collectOutCost = ABCNumberUtils.round4Pretty(this.collectOutCost);
        this.allotOutCost = ABCNumberUtils.round4Pretty(this.allotOutCost);
        this.damagedOutCost = ABCNumberUtils.round4Pretty(this.damagedOutCost);
        this.outDepartmentConsumptionCostAmount = ABCNumberUtils.round4Pretty(this.outDepartmentConsumptionCostAmount);
        this.outOtherCostAmount = ABCNumberUtils.round4Pretty(this.outOtherCostAmount);
        this.checkOutCost = ABCNumberUtils.round4Pretty(this.checkOutCost);
        this.outTotalCost = ABCNumberUtils.round4Pretty(this.outTotalCost);
    }

    /**
     * 设置值为null
     */
    public void setCostNull() {
        // 期初以及期末数据成本为null
        beginAndEndDataCostSetNull();

        // 入库数据成本为null
        inDataCostSetNull();

        // 出库数据成本为null
        outDataCostSetNull();
    }

    /**
     * 期初以及期末数据成本为null
     */
    private void beginAndEndDataCostSetNull() {
        this.beginCost = null;
        this.endCost = null;
    }

    /**
     * 入库数据成本为null
     */
    private void inDataCostSetNull() {
        // 采购入库
        this.purchaseInCost = null;
        // 领用入库
        this.inReceiveCostAmount = null;
        // 调拨入库
        this.allotInCost = null;
        // 盘盈入库
        this.checkInCost = null;
        // 规格修改
        this.inSpecificationModificationCost = null;
        // 入库合计
        this.inTotalCost = null;
    }

    /**
     * 出库数据成本为null
     */
    private void outDataCostSetNull() {
        // 发药出库
        this.dispensePrice = null;
        this.dispenseCost = null;
        // 门诊发药出库
        this.outPatientDispensePrice = null;
        this.outPatientDispenseCost = null;
        // 住院药房发药出库
        this.hospitalPharmacyDispensePrice = null;
        this.hospitalPharmacyDispenseCost = null;
        // 住院自动发药出库
        this.hospitalAutomaticDispensePrice = null;
        this.hospitalAutomaticDispenseCost = null;
        // 领料出库
        this.collectOutCost = null;
        // 调拨出库
        this.allotOutCost = null;
        // 破损出库
        this.damagedOutCost = null;
        // 科室消耗
        this.outDepartmentConsumptionCostAmount = null;
        // 科室消耗
        this.outOtherCostAmount = null;
        // 盘亏出库
        this.checkOutCost = null;
        // 出库合计
        this.outTotalCost = null;
    }

    /**
     * 设置期初期末数据
     *
     * @param beginIRList 期初数据
     * @param endIrList   期末数据
     */
    public void setBeginAndEndData(List<InventoryReport> beginIRList, List<InventoryReport> endIrList) {
        if (!CollUtil.isEmpty(beginIRList)) {
            List<InventoryReport> beginFilterList
                    = beginIRList.stream().filter(beginIr -> beginIr.getFeeType1().equals(this.feeType1))
                    .collect(Collectors.toList());
            if (!CollUtil.isEmpty(beginFilterList)) {
                InventoryReport beginIr = beginFilterList.get(0);
                this.beginCost = beginIr.getBeginCost();
            }
        }
        if (!CollUtil.isEmpty(endIrList)) {
            List<InventoryReport> endFilterList
                    = endIrList.stream().filter(endIr -> endIr.getFeeType1().equals(this.feeType1))
                    .collect(Collectors.toList());
            if (!CollUtil.isEmpty(endFilterList)) {
                InventoryReport endIr = endFilterList.get(0);
                this.endCost = endIr.getEndCost();
            }

        }
    }

    public void setBeginAndEndSummaryData(InventoryReport beginReportSummaryData, InventoryReport endReportSummaryData) {
        if (!BeanUtil.isEmpty(beginReportSummaryData)) {
            this.beginCost = beginReportSummaryData.getBeginCost();
        }
        if (!BeanUtil.isEmpty(endReportSummaryData)) {
            this.endCost = endReportSummaryData.getEndCost();
        }
    }

    public String getFeeType1() {
        return this.feeType1;
    }

    public String getFeeType1Name() {
        return this.feeType1Name;
    }

    public Integer getFeeType2() {
        return this.feeType2;
    }

    public String getFeeType2Name() {
        return this.feeType2Name;
    }


    public void setFeeType1(String feeType1) {
        this.feeType1 = feeType1;
    }

    public void setFeeType1Name(String feeType1Name) {
        this.feeType1Name = feeType1Name;
    }

    public void setFeeType2(Integer feeType2) {
        this.feeType2 = feeType2;
    }

    public void setFeeType2Name(String feeType2Name) {
        this.feeType2Name = feeType2Name;
    }

    public BigDecimal getBeginCost() {
        return beginCost;
    }

    public void setBeginCost(BigDecimal beginCost) {
        this.beginCost = beginCost;
    }

    public BigDecimal getEndCost() {
        return endCost;
    }

    public void setEndCost(BigDecimal endCost) {
        this.endCost = endCost;
    }

    public BigDecimal getPurchaseInCost() {
        return purchaseInCost;
    }

    public void setPurchaseInCost(BigDecimal purchaseInCost) {
        this.purchaseInCost = purchaseInCost;
    }

    public BigDecimal getAllotInCost() {
        return allotInCost;
    }

    public void setAllotInCost(BigDecimal allotInCost) {
        this.allotInCost = allotInCost;
    }

    public BigDecimal getCheckInCost() {
        return checkInCost;
    }

    public void setCheckInCost(BigDecimal checkInCost) {
        this.checkInCost = checkInCost;
    }

    public BigDecimal getInReceiveCostAmount() {
        return inReceiveCostAmount;
    }

    public void setInReceiveCostAmount(BigDecimal inReceiveCostAmount) {
        this.inReceiveCostAmount = inReceiveCostAmount;
    }

    public BigDecimal getInSpecificationModificationCost() {
        return inSpecificationModificationCost;
    }

    public void setInSpecificationModificationCost(BigDecimal inSpecificationModificationCost) {
        this.inSpecificationModificationCost = inSpecificationModificationCost;
    }

    public BigDecimal getInTotalCost() {
        return inTotalCost;
    }

    public void setInTotalCost(BigDecimal inTotalCost) {
        this.inTotalCost = inTotalCost;
    }

    public BigDecimal getDispensePrice() {
        return dispensePrice;
    }

    public void setDispensePrice(BigDecimal dispensePrice) {
        this.dispensePrice = dispensePrice;
    }

    public BigDecimal getDispenseCost() {
        return dispenseCost;
    }

    public void setDispenseCost(BigDecimal dispenseCost) {
        this.dispenseCost = dispenseCost;
    }

    public BigDecimal getOutPatientDispensePrice() {
        return outPatientDispensePrice;
    }

    public void setOutPatientDispensePrice(BigDecimal outPatientDispensePrice) {
        this.outPatientDispensePrice = outPatientDispensePrice;
    }

    public BigDecimal getOutPatientDispenseCost() {
        return outPatientDispenseCost;
    }

    public void setOutPatientDispenseCost(BigDecimal outPatientDispenseCost) {
        this.outPatientDispenseCost = outPatientDispenseCost;
    }

    public BigDecimal getHospitalPharmacyDispensePrice() {
        return hospitalPharmacyDispensePrice;
    }

    public void setHospitalPharmacyDispensePrice(BigDecimal hospitalPharmacyDispensePrice) {
        this.hospitalPharmacyDispensePrice = hospitalPharmacyDispensePrice;
    }

    public BigDecimal getHospitalPharmacyDispenseCost() {
        return hospitalPharmacyDispenseCost;
    }

    public void setHospitalPharmacyDispenseCost(BigDecimal hospitalPharmacyDispenseCost) {
        this.hospitalPharmacyDispenseCost = hospitalPharmacyDispenseCost;
    }

    public BigDecimal getHospitalAutomaticDispensePrice() {
        return hospitalAutomaticDispensePrice;
    }

    public void setHospitalAutomaticDispensePrice(BigDecimal hospitalAutomaticDispensePrice) {
        this.hospitalAutomaticDispensePrice = hospitalAutomaticDispensePrice;
    }

    public BigDecimal getHospitalAutomaticDispenseCost() {
        return hospitalAutomaticDispenseCost;
    }

    public void setHospitalAutomaticDispenseCost(BigDecimal hospitalAutomaticDispenseCost) {
        this.hospitalAutomaticDispenseCost = hospitalAutomaticDispenseCost;
    }

    public BigDecimal getCollectOutCost() {
        return collectOutCost;
    }

    public void setCollectOutCost(BigDecimal collectOutCost) {
        this.collectOutCost = collectOutCost;
    }

    public BigDecimal getAllotOutCost() {
        return allotOutCost;
    }

    public void setAllotOutCost(BigDecimal allotOutCost) {
        this.allotOutCost = allotOutCost;
    }

    public BigDecimal getDamagedOutCost() {
        return damagedOutCost;
    }

    public void setDamagedOutCost(BigDecimal damagedOutCost) {
        this.damagedOutCost = damagedOutCost;
    }

    public BigDecimal getOutDepartmentConsumptionCostAmount() {
        return outDepartmentConsumptionCostAmount;
    }

    public void setOutDepartmentConsumptionCostAmount(BigDecimal outDepartmentConsumptionCostAmount) {
        this.outDepartmentConsumptionCostAmount = outDepartmentConsumptionCostAmount;
    }

    public BigDecimal getOutOtherCostAmount() {
        return outOtherCostAmount;
    }

    public void setOutOtherCostAmount(BigDecimal outOtherCostAmount) {
        this.outOtherCostAmount = outOtherCostAmount;
    }

    public BigDecimal getCheckOutCost() {
        return checkOutCost;
    }

    public void setCheckOutCost(BigDecimal checkOutCost) {
        this.checkOutCost = checkOutCost;
    }

    public BigDecimal getOutTotalCost() {
        return outTotalCost;
    }

    public void setOutTotalCost(BigDecimal outTotalCost) {
        this.outTotalCost = outTotalCost;
    }

    public BigDecimal getHospitalPharmacyNoSettleDispenseCount() {
        return hospitalPharmacyNoSettleDispenseCount;
    }

    public void setHospitalPharmacyNoSettleDispenseCount(BigDecimal hospitalPharmacyNoSettleDispenseCount) {
        this.hospitalPharmacyNoSettleDispenseCount = hospitalPharmacyNoSettleDispenseCount;
    }

    public BigDecimal getHospitalAutomaticNoSettleDispenseCount() {
        return hospitalAutomaticNoSettleDispenseCount;
    }

    public void setHospitalAutomaticNoSettleDispenseCount(BigDecimal hospitalAutomaticNoSettleDispenseCount) {
        this.hospitalAutomaticNoSettleDispenseCount = hospitalAutomaticNoSettleDispenseCount;
    }

    public BigDecimal getInInitCost() {
        return inInitCost;
    }

    public void setInInitCost(BigDecimal inInitCost) {
        this.inInitCost = inInitCost;
    }
}
