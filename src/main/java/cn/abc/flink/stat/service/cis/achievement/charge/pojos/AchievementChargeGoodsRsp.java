package cn.abc.flink.stat.service.cis.achievement.charge.pojos;

import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeGoodsEntity;


import java.math.BigDecimal;
import java.util.List;

/**
 * 项目接口返回对象
 * <AUTHOR>
 */
public class AchievementChargeGoodsRsp {
    private Long count;
    private BigDecimal totalSum;
    private List<AchievementChargeGoodsEntity> data;

    public AchievementChargeGoodsRsp(Long count, BigDecimal totalSum,
                                     List<AchievementChargeGoodsEntity> data) {
        this.count = count;
        this.totalSum = totalSum;
        this.data = data;
    }

    public AchievementChargeGoodsRsp() {
    }

    public Long getCount() {
        return this.count;
    }

    public BigDecimal getTotalSum() {
        return this.totalSum;
    }

    public List<AchievementChargeGoodsEntity> getData() {
        return this.data;
    }


    public void setCount(Long count) {
        this.count = count;
    }

    public void setTotalSum(BigDecimal totalSum) {
        this.totalSum = totalSum;
    }

    public void setData(List<AchievementChargeGoodsEntity> data) {
        this.data = data;
    }

}
