package cn.abc.flink.stat.service.cis.goods.outinventory.handler;

import cn.abc.flink.stat.common.response.StatResponseHeaderItem;
import cn.abc.flink.stat.service.cis.goods.ininventory.domain.InInventoryParams;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/8/18 8:19 下午
 */
@Component
public class OutInventoryHeaderHandler {
    private static final int ONE_HUNDRED_AND_TWENTY = 120;
    private static final int TWO_HUNDRED_AND_TEN = 210;
    private static final int NINETY = 90;
    private static final int ONE_HUNDRED = 100;
    private static final int ONE_HUNDRED_AND_TEN = 110;

    /**
     * @return List<StatResponseHeaderItem>
     */
    public List<StatResponseHeaderItem> outInventory(InInventoryParams param) {
        List<StatResponseHeaderItem> headers = new ArrayList<>();
        headers.add(new StatResponseHeaderItem("门店", "clinicName", "", ONE_HUNDRED_AND_TWENTY, true));
        if (param.getIsMultiPharmacy() == 1) {
            headers.add(new StatResponseHeaderItem("药房", "pharmacyName", "", ONE_HUNDRED_AND_TWENTY,
                    true));
        }
        headers.add(new StatResponseHeaderItem("品种", "goodsKind", "", TWO_HUNDRED_AND_TEN, true));
        headers.add(new StatResponseHeaderItem("数量", "goodsCountText", "", NINETY));
        headers.add(new StatResponseHeaderItem("金额（含税）", "goodsAmountText", "", ONE_HUNDRED));
        headers.add(new StatResponseHeaderItem("金额（不含税）", "goodsAmountExcludedTaxText", "", ONE_HUNDRED_AND_TEN));

        return headers;
    }

    /**
     * 报损出库表头处理 筛选库房去掉库房列
     *
     * @param headerEmployeeItems 表头list
     * @param param               出入库param
     * @return 处理后表头
     */
    public List<TableHeaderEmployeeItem> damagedOutHandlerHeader(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                                                 InInventoryParams param) {
        if (param.getPharmacyNo() != null) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getProp().equals("pharmacyName")).collect(Collectors.toList());
        }
        return headerEmployeeItems;
    }

    /**
     * 消耗出库表头处理 筛选库房去掉库房列
     *
     * @param headerEmployeeItems 表头list
     * @param param               出入库param
     * @return 处理后表头
     */
    public List<TableHeaderEmployeeItem> consumptionOutHandlerHeader(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                                                     InInventoryParams param) {
        if (param.getPharmacyNo() != null) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getProp().equals("pharmacyName")).collect(Collectors.toList());
        }
        return headerEmployeeItems;
    }

    /**
     * 其他出库表头处理 筛选库房去掉库房列
     *
     * @param headerEmployeeItems 表头list
     * @param param               出入库param
     * @return 处理后表头
     */
    public List<TableHeaderEmployeeItem> otherOutHandlerHeader(List<TableHeaderEmployeeItem> headerEmployeeItems,
                                                               InInventoryParams param) {
        if (param.getPharmacyNo() != null) {
            headerEmployeeItems = headerEmployeeItems.stream().filter(tableHeaderEmployeeItem -> !tableHeaderEmployeeItem.getProp().equals("pharmacyName")).collect(Collectors.toList());
        }
        return headerEmployeeItems;
    }
}
