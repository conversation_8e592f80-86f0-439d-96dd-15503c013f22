package cn.abc.flink.stat;

import cn.abcyun.bis.rpc.sdk.cis.client.*;
import cn.abcyun.bis.rpc.sdk.property.model.CrmMemberSettings;
import cn.abcyun.cis.core.annotation.EnableAsyncExecutor;
import cn.abcyun.region.rpc.sdk.client.cis.AbcCisScStatFeignClient;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = {
        org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration.class
})
//@MapperScan("cn.abc.flink.stat.db.dao")
@EnableAsync
@EnableTransactionManagement()
@MapperScan(basePackages = "cn.abc.flink.stat.db.dao", sqlSessionTemplateRef = "db1SqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.read", sqlSessionTemplateRef = "db1SqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.bis.mysql.dao", sqlSessionTemplateRef = "dbBisSqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.cis.aurora.dao", sqlSessionTemplateRef = "statMysqlSqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.cis.promotion.dao", sqlSessionTemplateRef = "dbPromotionSqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.cis.patient.dao", sqlSessionTemplateRef = "dbPatientSqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.bis.stat.dao", sqlSessionTemplateRef = "statMysqlSqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.his.hologres.dao", sqlSessionTemplateRef = "hologresSqlSessionTemplate")
@MapperScan(basePackages = "cn.abc.flink.stat.db.cis.hologres.dao", sqlSessionTemplateRef = "hologresSqlSessionTemplate")
@EnableFeignClients(clients = {
        AbcCisShebaoStatFeignClient.class, AbcCisShebaoFeignClient.class, AbcCisMonitorFeignClient.class, CrmMemberSettings.class,
        AbcCisScClinicFeignClient.class, AbcCisScGoodsFeignClient.class, AbcCisScStatFeignClient.class, AbcCisSearchFeignClient.class, AbcCisOssProxyFeignClient.class
})
@EnableCaching
@EnableAsyncExecutor //core包统一线程池自动扫描注解
public class StatApplication {

    public static void main(String[] args) {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        SpringApplication.run(StatApplication.class, args);
    }

}
