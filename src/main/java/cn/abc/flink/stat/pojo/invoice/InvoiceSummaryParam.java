package cn.abc.flink.stat.pojo.invoice;





@Deprecated
public class InvoiceSummaryParam {

    private String chainId;

    private String clinicId;

    private String beginDate;

    private String endDate;

    private String beginDateStr;

    private String endDateStr;

    private Integer invoiceType;

    //id
    private String operationUser;

    private Integer offset;

    private Integer limit;



    private String cisTable;

    private Integer isPage = 1;

    public String getChainId() {
        return this.chainId;
    }

    public String getClinicId() {
        return this.clinicId;
    }

    public String getBeginDate() {
        return this.beginDate;
    }

    public String getEndDate() {
        return this.endDate;
    }

    public String getBeginDateStr() {
        return this.beginDateStr;
    }

    public String getEndDateStr() {
        return this.endDateStr;
    }

    public Integer getInvoiceType() {
        return this.invoiceType;
    }

    public String getOperationUser() {
        return this.operationUser;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public Integer getLimit() {
        return this.limit;
    }

    public String getCisTable() {
        return this.cisTable;
    }


    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setBeginDateStr(String beginDateStr) {
        this.beginDateStr = beginDateStr;
    }

    public void setEndDateStr(String endDateStr) {
        this.endDateStr = endDateStr;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public void setOperationUser(String operationUser) {
        this.operationUser = operationUser;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setCisTable(String cisTable) {
        this.cisTable = cisTable;
    }

}
