package cn.abc.flink.stat.common.constants;

import io.swagger.annotations.ApiModel;

@ApiModel("发药类型常量类")
public final class GoodsInventoryDispenseTypeConstants {

    /**
     * 发药类型 门诊发药
     */
    public static final Integer DISPENSE_TYPE_OUTPATIENT = 0;

    /**
     * 发药类型 住院发药
     */
    public static final Integer DISPENSE_TYPE_HOSPITAL = 10;

    /**
     * 发药场景 b2c发药
     */
    public static final Integer DISPENSE_SCENE_B2C = 20;

    /**
     * 发药场景 零售发药
     */
    public static final Integer DISPENSE_SCENE_RETAIL = 40;

    /**
     * 发药场景 美团发药
     */
    public static final Integer DISPENSE_SCENE_MT = 25;

    /**
     * 发药方式 手动
     */
    public static final Integer DISPENSE_METHOD_MANUAL = 0;

    /**
     * 发药方式 自动
     */
    public static final Integer DISPENSE_METHOD_AUTOMATIC = 10;


    private GoodsInventoryDispenseTypeConstants() {
    }


}
