package cn.abc.flink.stat.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @description: 该注解用于接口限流(在方法上添加该注解)
 * @author: lzq
 * @Date: 2022/11/25 4:47 下午
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface InterfaceCurrentLimiting {
}
