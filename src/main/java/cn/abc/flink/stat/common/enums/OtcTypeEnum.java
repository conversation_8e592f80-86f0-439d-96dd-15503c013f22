package cn.abc.flink.stat.common.enums;

/**
 * @Description: OTC药物/非处方药:null无含义； 1处方药 2甲类非处方 4乙类非处方
 * @param
 * @return
 * @Author: zs
 * @Date: 2024/4/9 19:16
 */
public enum OtcTypeEnum {
    PRESCRIPTION_DRUGS(1, "处方药"),
    CLASSA_COUNTER_DRUGS(2, "甲类非处方"),
    CLASSB_COUNTER_DRUGS(4, "乙类非处方");

    private Integer type;
    private String name;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    OtcTypeEnum (Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getName (Integer type) {
        String result = null;
        switch (type) {
            case 1:
                result = PRESCRIPTION_DRUGS.getName();
                break;
            case 2:
                result = CLASSA_COUNTER_DRUGS.getName();
                break;
            case 4:
                result = CLASSB_COUNTER_DRUGS.getName();
                break;
            default:
        }
        return result;
    }
}
