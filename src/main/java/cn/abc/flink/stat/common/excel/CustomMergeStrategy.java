package cn.abc.flink.stat.common.excel;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.AbstractRowWriteHandler;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 将指定列中相同内容的单元格进行合并
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/26 16:34
 */
public class CustomMergeStrategy implements CellWriteHandler {
	private List<Integer> mergeColumnIndexList;

	public CustomMergeStrategy(int mergeColumnIndex) {
		this.mergeColumnIndexList = new ArrayList<>(mergeColumnIndex);
	}

	public CustomMergeStrategy(List<Integer> mergeColumnIndexList) {
		this.mergeColumnIndexList = mergeColumnIndexList;
	}


	@Override
	public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {

	}

	@Override
	public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

	}

	@Override
	public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

	}

	@Override
	public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
		if (isHead){
			//如果是表头不做处理
			return;
		}
		if(relativeRowIndex==0){
			//如果当前是第一行不做处理
			return;
		}
		int columnIndex = cell.getColumnIndex();
		if (!mergeColumnIndexList.contains(columnIndex)) {
			return;
		}

		//获取当前行下标，上一行下标，上一行对象，上一列对象
		Sheet sheet = cell.getSheet();
		int rowIndex = cell.getRowIndex();
		int rowIndexPrev=rowIndex - 1;
		Row row = sheet.getRow(rowIndexPrev);
		Cell cellPrev = row.getCell(cell.getColumnIndex());

		//得到当前单元格值和上一行单元格
		Object cellValue = cell.getCellTypeEnum()== CellType.STRING ? cell.getStringCellValue() : cell.getNumericCellValue();
		Object cellValuePrev = cellPrev.getCellTypeEnum()==CellType.STRING ? cellPrev.getStringCellValue() : cellPrev.getNumericCellValue();
		//如果当前单元格和上一行单元格值相等就合并
		if (!cellValue.equals(cellValuePrev)) {
			return;
		}
		//获取已有策略
		List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

		boolean mergen=false;
		for (int i = 0; i < mergedRegions.size(); i++) {
			CellRangeAddress cellAddresses = mergedRegions.get(i);
			if(cellAddresses.isInRange(rowIndexPrev,cell.getColumnIndex())){
				sheet.removeMergedRegion(i);
				cellAddresses.setLastRow(rowIndex);
				sheet.addMergedRegion(cellAddresses);
				mergen=true;
				break;
			}

		}
		if (!mergen){
			CellRangeAddress cellAddresses = new CellRangeAddress(rowIndexPrev, rowIndex, cell.getColumnIndex(), cell.getColumnIndex());
			sheet.addMergedRegion(cellAddresses);
		}
	}
}