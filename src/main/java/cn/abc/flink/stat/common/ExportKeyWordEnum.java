package cn.abc.flink.stat.common;

import lombok.Getter;

/**
 * @description: 导出权限枚举
 * @author: lzq
 * @Date: 2022/9/29
 */
@Getter
public enum ExportKeyWordEnum {
    REVENUE_DETAIL_TRANSACTION("revenue.detail.transaction", "收费明细-单据"),
    REVENUE_DETAIL_CLASSIFY("revenue.detail.classify", "收费明细-分类"),
    REVENUE_DETAIL_ITEMS("revenue.detail.items", "收费明细-明细"),
    REVENUE_DETAIL_ADVICE("revenue.detail.advice", "收费明细-医嘱"),
    REVENUE_DETAIL_PROJECT("revenue.detail.project", "收费明细-项目"),
    GOODS_INVENTORY_CLASSIFY("goods.inventory.classify", "进销存统计-分类"),
    GOODS_INVENTORY_GOODS("goods.inventory.goods", "进销存统计-汇总"),
    GOODS_INVENTORY_RECORD("goods.inventory.record", "进销存统计-明细"),
    ACHIEVEMENT_CHARGE_PERSONNEL("achievement.charge.personnel", "开单业绩-人员"),
    ACHIEVEMENT_CHARGE_DEPARTMENT("achievement.charge.department", "开单业绩-科室"),
    ACHIEVEMENT_CHARGE_GOODS("achievement.charge.goods", "开单业绩-项目"),
    ACHIEVEMENT_CHARGE_TRANSACTION("achievement.charge.transaction", "开单业绩-单据"),
    ACHIEVEMENT_CHARGE_DETAIL("achievement.charge.detail", "开单业绩-明细"),
    ACHIEVEMENT_RECOMMEND_RECORD("stat.achievement.recommend.record", "就诊推荐-推荐记录"),
    ACHIEVEMENT_RECOMMEND_VISIT_SOURCE("achievement.recommend.visit.source", "就诊推荐-推荐渠道"),
    ACHIEVEMENT_RECOMMEND_GOODS("achievement.recommend.goods", "就诊推荐-项目"),
    ACHIEVEMENT_RECOMMEND_TRANSACTION("achievement.recommend.transaction", "就诊推荐-单据"),
    ACHIEVEMENT_REGISTRATION_PERSONNEL("achievement.registration.personnel", "挂号业绩-人员"),
    ACHIEVEMENT_REGISTRATION_DEPARTMENT("achievement.registration.department", "挂号业绩-科室"),
    ACHIEVEMENT_REGISTRATION_DETAIL("achievement.registration.detail", "挂号业绩-明细"),
    ACHIEVEMENT_EXAMINATION_PERSONNEL("achievement.examination.personnel", "检验业绩-人员"),
    ACHIEVEMENT_EXAMINATION_SUPPLIER("achievement.examination.supplier", "检验业绩-机构"),
    ACHIEVEMENT_EXAMINATION_DETAIL("achievement.examination.detail", "检验业绩-明细"),
    ACHIEVEMENT_DISPENSING_PERSONNEL("achievement.dispensing.personnel", "发药业绩-人员"),
    ACHIEVEMENT_DISPENSING_DETAIL("achievement.dispensing.detail", "发药业绩-明细"),
    INVENTORY_DISPENSE_SUMMARY("inventory.dispense.summary", "发药统计-汇总"),
    INVENTORY_DISPENSE_GOODS("inventory.dispense.goods", "发药统计-药品"),
    INVENTORY_DISPENSE_RECORD("inventory.dispense.record", "发药统计-流水");

    private String key;
    private String name;

    ExportKeyWordEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

}
