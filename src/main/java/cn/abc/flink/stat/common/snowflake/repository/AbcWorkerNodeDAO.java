package cn.abc.flink.stat.common.snowflake.repository;

import cn.abc.flink.stat.common.snowflake.entity.AbcWorkerNodeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Description: DAO for M_WORKER_NODE
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/29 11:12
 */
@Repository
public interface AbcWorkerNodeDAO extends JpaRepository<AbcWorkerNodeEntity, Long> {
	/**
	 * 通过host和port获取AbcWorkerNodeEntity对象
	 * @param host host
	 * @param port port
	 * @return entity
	 */
	AbcWorkerNodeEntity findByHostNameAndPort(String host, String port);
}
