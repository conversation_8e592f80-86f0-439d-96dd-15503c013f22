package cn.abc.flink.stat.common.response;

import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;


import java.util.List;

/**
 * @description:
 * @author: lzq
 * @Date: 2022/8/1 3:52 下午
 */
public class V2StatResponse {
    /**
     * 表头  通过业务返回
     */
    private List<TableHeaderEmployeeItem> header;
    /**
     * 表头上的蓝色汇总区域
     */
    private List<StatResponseKeyDataItem> keyData;
    /**
     * 扁平化的数据
     */
    private List data;
    /**
     * 汇总行 是不是吸底由前端来控制
     */
    private Object summary;
    /**
     * 右下角的汇总信息及分页  如果只有分页，其他的空着就可以了
     */
    private StatResponseTotal total;

    public List<TableHeaderEmployeeItem> getHeader() {
        return this.header;
    }

    public List<StatResponseKeyDataItem> getKeyData() {
        return this.keyData;
    }

    public List getData() {
        return this.data;
    }

    public Object getSummary() {
        return this.summary;
    }

    public StatResponseTotal getTotal() {
        return this.total;
    }


    public void setHeader(List<TableHeaderEmployeeItem> header) {
        this.header = header;
    }

    public void setKeyData(List<StatResponseKeyDataItem> keyData) {
        this.keyData = keyData;
    }

    public void setData(List data) {
        this.data = data;
    }

    public void setSummary(Object summary) {
        this.summary = summary;
    }

    public void setTotal(StatResponseTotal total) {
        this.total = total;
    }

}
