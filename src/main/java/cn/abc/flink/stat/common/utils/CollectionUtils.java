package cn.abc.flink.stat.common.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Description: 集合工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/26 14:36
 */
public class CollectionUtils {
	/**
	 * 把一个大集合拆分为多个小集合
	 * @param collection 大集合
	 * @param chunkSize 拆分后的集合最大size
	 * @return 小集合
	 */
	public static <T> List<List<T>> splitCollection(Collection<T> collection, int chunkSize) {
		List<T> list = new ArrayList<>(collection);
		List<List<T>> chunks = new ArrayList<>();
		int totalSize = list.size();

		for (int i = 0; i < totalSize; i += chunkSize) {
			int end = Math.min(totalSize, i + chunkSize);
			chunks.add(list.subList(i, end));
		}
		return chunks;
	}
}
