package cn.abc.flink.stat.common.request.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.io.Serializable;
import java.util.List;

/**
 * @Description: 营销活动param
 * @param
 * @return
 * @Author: zs
 * @Date: 2024/4/30 10:49
 */
@ApiModel("业务会员日")
public class MemberDayDtoParam extends AbcScStatRequestParams implements Serializable {

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private String activityId;

    /**
     * 会员日期 格式2024-05-08
     */
    @ApiModelProperty(value = "会员日期 格式2024-05-08")
    private List<String> memberDates;


    /**
     * 活动日期 格式2024-05-08
     */
    @ApiModelProperty(value = "活动日期 格式2024-05-08")
    private List<String> activityDates;

    public String getActivityId() {
        return this.activityId;
    }

    public List<String> getMemberDates() {
        return this.memberDates;
    }

    public List<String> getActivityDates() {
        return this.activityDates;
    }


    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public void setMemberDates(List<String> memberDates) {
        this.memberDates = memberDates;
    }

    public void setActivityDates(List<String> activityDates) {
        this.activityDates = activityDates;
    }

}
