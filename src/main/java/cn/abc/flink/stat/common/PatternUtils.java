package cn.abc.flink.stat.common;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/6 11:41
 */
public class PatternUtils {
	/**
	 * 正则匹配
	 * 目前只支持捕获组模式
	 * @param regex 正则
	 * @param content 匹配内容
	 * @return list
	 */
	public static List<String> matchs(String regex, String content) {
		List<String> list = new ArrayList<>();
		Pattern p = Pattern.compile(regex);
		Matcher m = p.matcher(content);
		if (m.find() && m.groupCount() > 0) {
			for (int i = 0; i < m.groupCount(); i++) {
				list.add(m.group(i + 1));
			}
		}
		return list;
	}

	/**
	 * 切分字符串
	 * @param regex 正则
	 * @param content 内容
	 * @return list
	 */
	public static List<String> split(String regex, String content) {
		List<String> list = new ArrayList<>();
		String[] contents = content.split(regex);
		for (String c : contents) {
			if (c.trim().length() > 0) {
				list.add(c.trim());
			}
		}
		return list;
	}
}
