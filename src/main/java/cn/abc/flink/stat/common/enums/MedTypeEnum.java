package cn.abc.flink.stat.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum MedTypeEnum {
    GENERAL_OUTPATIENT("11", "普通门诊"),
    COVID_OUTPATIENT("1102", "新冠门诊"),
    ACCOUNT_PAYMENT("110102", "个账代支"),
    OUTPATIENT_POOL("110104", "门诊统筹"),
    SPECIAL_DRUG_OUTPATIENT("110202", "特药门诊"),
    OUTPATIENT_REGISTRATION("12", "门诊挂号"),
    EMERGENCY("13", "急诊"),
    CHRONIC_SPECIAL_OUTPATIENT("14", "门诊慢特病"),
    OUTPATIENT_MAJOR_DISEASE("140101", "门诊大病"),
    OUTPATIENT_CHRONIC_DISEASE("140104", "门诊慢病"),
    OUTPATIENT_SPECIAL_DISEASE("140201", "门诊特病"),
    GENERAL_HOSPITALIZATION("21", "普通住院"),
    TRAUMA_HOSPITALIZATION("22", "外伤住院"),
    REFERRED_TREATMENT_HOSPITALIZATION("23", "转外诊治住院"),
    EMERGENCY_TO_HOSPITALIZATION("24", "急诊转住院"),
    PHARMACY_PURCHASE("41", "定点药店购药"),
    MATERNITY_OUTPATIENT("51", "生育门诊"),
    MATERNITY_HOSPITALIZATION("52", "生育住院"),
    FAMILY_PLANNING_SURGERY("53", "计划生育手术费"),
    OTHER_OUTPATIENT("91", "其他门诊"),
    OTHER_HOSPITALIZATION("92", "其他住院"),
    OTHER_DRUG_PURCHASE("93", "其他购药"),
    LOCAL_EXTENDED_MEDICAL("99", "地方扩展医疗类别");

    private final String id;
    private final String name;

    private static final Map<String, MedTypeEnum> ID_TO_ENUM = new HashMap<>();

    static {
        for (MedTypeEnum type : values()) {
            ID_TO_ENUM.put(type.getId(), type);
        }
    }

    MedTypeEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getNameById(String id) {
        MedTypeEnum type = ID_TO_ENUM.get(id);
        return type != null ? type.getName() : null;
    }

    public static MedTypeEnum getById(String id) {
        return ID_TO_ENUM.get(id);
    }
}