package cn.abc.flink.stat.common.request.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @date 2024/6/18 2:42 下午
 */
@ApiModel("营销页面-活码获客params")
public class PromotionLiveCodeParam extends AbcScStatRequestParams{

    @ApiModelProperty(value = "渠道ID")
    private Long channelId;

    @ApiModelProperty(value = "成员ID")
    private String employeeId;

    @ApiModelProperty(value = "首次消费开始时间")
    private String firstChargeBeginDate;

    @ApiModelProperty(value = "首次消费结束时间")
    private String firstChargeEndDate;

    @ApiModelProperty(value = "首次就诊医生")
    private String firstOutpatientDoctorId;

    @ApiModelProperty(value = "添加人")
    private String createdBy;

    @ApiModelProperty(value = "偏移量")
    private Integer offset;

    @ApiModelProperty(value = "大小")
    private Integer limit;

    @ApiModelProperty(value = "权限参数 0查看全部 1查看自己")
    private Integer statDataPermission;

    public void init() {
        if (this.statDataPermission != null && statDataPermission == 0) {
            this.employeeId = null;
        }
    }

    public Long getChannelId() {
        return this.channelId;
    }

    public String getEmployeeId() {
        return this.employeeId;
    }

    public String getFirstChargeBeginDate() {
        return this.firstChargeBeginDate;
    }

    public String getFirstChargeEndDate() {
        return this.firstChargeEndDate;
    }

    public String getFirstOutpatientDoctorId() {
        return this.firstOutpatientDoctorId;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public Integer getLimit() {
        return this.limit;
    }

    public Integer getStatDataPermission() {
        return this.statDataPermission;
    }


    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public void setFirstChargeBeginDate(String firstChargeBeginDate) {
        this.firstChargeBeginDate = firstChargeBeginDate;
    }

    public void setFirstChargeEndDate(String firstChargeEndDate) {
        this.firstChargeEndDate = firstChargeEndDate;
    }

    public void setFirstOutpatientDoctorId(String firstOutpatientDoctorId) {
        this.firstOutpatientDoctorId = firstOutpatientDoctorId;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public void setStatDataPermission(Integer statDataPermission) {
        this.statDataPermission = statDataPermission;
    }

}
