package cn.abc.flink.stat.common.aspect;

import cn.abc.flink.stat.common.request.params.AbcScStatRequestParams;
import cn.abc.flink.stat.dimension.DimensionQuery;
import cn.abc.flink.stat.general.utils.R;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.jwt.CisJWTBody;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 * @description: jwt权限验证
 * @author: lzq
 * @Date: 2022/11/25 4:49 下午
 */
@Aspect
@Component
public class JwtPermissionsValidation {
    private static final Logger logger = LoggerFactory.getLogger(JwtPermissionsValidation.class);

    //是否打印cookies日志
    @Value("${abc.log.isPrint}")
    public boolean isPrint;

    //针对打印日志的chainId
    @Value("${abc.log.parintChainIds}")
    public String parintChainIds;

    @Autowired
    private DimensionQuery dimensionQuery;

    /**
     * 切入点
     * 当我们调用添加 @Permissions 注解的方法时,下面的方法自动获取被调用的方法
     */
    @Pointcut("@annotation(cn.abc.flink.stat.common.annotation.JwtPermissions)")
    public void permissionsPoint() {
    }

    /**
     * 通过方法名获取切入点
     * 在请求接口前做jwt权限验证
     *
     * @param joinPoint -
     * @return Object -
     * @throws Throwable -
     */
    @Around("permissionsPoint()")
    public Object permissionsBefore(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取到请求的属性
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 获取到请求对象
        HttpServletRequest request = attributes.getRequest();
        // 获取jwt进行校验
        String jwtEncodeStr = request.getHeader(CisJWTUtils.JWT_HEADER_KEY);
        CisJWTBody cisJWTBody = CisJWTUtils.decodeFromHeader(jwtEncodeStr);
        if (cisJWTBody == null) {
            throw new CisCustomException(R.AUTH_ERROR, "auth error");
        }
        if (StrUtil.isBlank(cisJWTBody.chainId) || StrUtil.isBlank(cisJWTBody.clinicId)) {
            throw new CisCustomException(R.ERROR_HTTP_REQUEST, "");
        }
        // 获取所有的header初始化jwt内容
        String viewMode = request.getHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE);
        String hisType = request.getHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE);
        //登陆类型 testLogin/password
        String loginWay = request.getHeader(CisJWTUtils.CIS_HEADER_LOGIN_WAY);
        Integer supportedBusiness = 1;
        if (request.getHeader(CisJWTUtils.CIS_HEADER_CLINIC_SUPPORTED_BUSINESS) != null) {
            try {
                supportedBusiness = Integer.parseInt(request.getHeader(CisJWTUtils.CIS_HEADER_CLINIC_SUPPORTED_BUSINESS));
            } catch (Exception e) {
                logger.error("表头获取supportedBusiness异常,supportedBusiness:{},异常：{}", CisJWTUtils.CIS_HEADER_CLINIC_SUPPORTED_BUSINESS, e);
            }
        } else {
            supportedBusiness = dimensionQuery.queryOrgan(cisJWTBody.clinicId).getBusSupportFlag();
        }
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            Class<?> aClass = arg.getClass();
            if (AbcScStatRequestParams.class.isAssignableFrom(aClass)) {
                ((AbcScStatRequestParams) arg).aopInitAbcCisBaseQueryParams(jwtEncodeStr, viewMode, hisType,
                        ((AbcScStatRequestParams) arg).getChainId(), ((AbcScStatRequestParams) arg).getClinicId(),
                        supportedBusiness, loginWay);
                ((AbcScStatRequestParams) arg).setSingleStore(viewMode, String.valueOf(cisJWTBody.clinicType));
                ((AbcScStatRequestParams) arg).initAbcPermission();
                ((AbcScStatRequestParams) arg).initHint();
                printLog(request, arg);
            }
        }
        Object proceed = joinPoint.proceed();
        return proceed;
    }

    /**
     * @param
     * @param request 请求体
     * @param arg     参数类
     * @return
     * @Description: 打印日志
     * @Author: zs
     * @Date: 2023/8/3 12:03
     */
    public void printLog(HttpServletRequest request, Object arg) {
        try {
            String chainId = request.getHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID);
            String uri = request.getRequestURI();
            String method = request.getMethod();
            Cookie[] cookies = request.getCookies();
            //打印cookie和打印自己的param日志
            if (isPrint && chainId != null && parintChainIds.contains(chainId)) {
                //是否打印切是否在指定chainId中
                logger.info("sc-stat cookies:{}", JSON.toJSONString(cookies));
            }
            if ("POST".equals(method)) {
                logger.info("sc-stat uri:{} param:{}", uri, ((AbcScStatRequestParams) arg).toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("jwt鉴权打印日志出现异常");
        }
    }

}
