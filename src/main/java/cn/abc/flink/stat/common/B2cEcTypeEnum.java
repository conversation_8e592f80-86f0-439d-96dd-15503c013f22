package cn.abc.flink.stat.common;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@ApiModel("b2c电商类型枚举")
public enum B2cEcTypeEnum {

    PDD(1, "PDD", "拼多多"),
    ELE(2, "ELE", "饿了么"),
    JD(3, "JD", "京东"),
    MT(4, "MT", "美团");

    private final Integer typeId;
    private final String typeShortName;
    private final String typeName;

    B2cEcTypeEnum(Integer typeId, String typeShortName, String typeName) {
        this.typeId = typeId;
        this.typeShortName = typeShortName;
        this.typeName = typeName;
    }

    /**
     * 根据电商类型id获取电商名称
     *
     * @param typeId 电商类型id
     * @return 电商名称
     */
    public static String getTypeNameByTypeId(Integer typeId) {
        String typename = "-";
        for (B2cEcTypeEnum typeEnum : B2cEcTypeEnum.values()) {
            if (typeEnum.typeId.equals(typeId)) {
                typename = typeEnum.typeName;
            }
        }
        return typename;
    }

    /**
     * 根据电商类型id获取b2c电商类型list
     *
     * @return b2c电商类型list
     */
    public static List<Map<Integer, String>> getTypeIdAndNameByIdSet(Set<Integer> typeIdSet) {
        List<Map<Integer, String>> typeList = new ArrayList<>();
        for (B2cEcTypeEnum typeEnum : B2cEcTypeEnum.values()) {
            if (typeIdSet.contains(typeEnum.typeId)) {
                Map<Integer, String> map = new HashMap<>();
                map.put(typeEnum.typeId, typeEnum.typeName);
                typeList.add(map);
            }
        }
        return typeList;
    }
}
