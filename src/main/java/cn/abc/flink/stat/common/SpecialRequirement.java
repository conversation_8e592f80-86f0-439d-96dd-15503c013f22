package cn.abc.flink.stat.common;

import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;

import java.util.List;

/**
 * 单个客户特殊需求
 */
public class SpecialRequirement {
    /**
     * 特殊处理，有一家店需要展示治疗师（杭州彩虹鱼康复护理院）
     *
     * @param tableHeaderEmployeeItems -
     * @param chainId                  -
     * @return -
     */
    public static List<TableHeaderEmployeeItem> handleTherapist(List<TableHeaderEmployeeItem> tableHeaderEmployeeItems,
                                                                String chainId) {
        if ("ffffffff0000000034af8c0db13cc000".equals(chainId)
                || "ffffffff00000000347ca195a4618000".equals(chainId)
                || "ffffffff00000000347ca188a4614001".equals(chainId)) {
            TableHeaderEmployeeItem dutyTherapistItem = new TableHeaderEmployeeItem();
            dutyTherapistItem.setLabel("责任治疗师");
            dutyTherapistItem.setProp("dutyTherapistName");
            dutyTherapistItem.setWidth(110);

            TableHeaderEmployeeItem primaryTherapistItem = new TableHeaderEmployeeItem();
            primaryTherapistItem.setLabel("首评治疗师");
            primaryTherapistItem.setProp("primaryTherapistName");
            primaryTherapistItem.setWidth(110);

            tableHeaderEmployeeItems.add(dutyTherapistItem);
            tableHeaderEmployeeItems.add(primaryTherapistItem);
        }
        return tableHeaderEmployeeItems;
    }

    /**
     * 单个店特殊处理
     *
     * @param tableHeaderEmployeeItems -
     * @param chainId                  -
     * @param labelList                 -
     * @return -
     */
    public static List<TableHeaderEmployeeItem> handleTherapist(List<TableHeaderEmployeeItem> tableHeaderEmployeeItems,
                                                                String chainId, List<TableHeaderEmployeeItem> labelList) {
        if ("ffffffff0000000034af8c0db13cc000".equals(chainId)
                || "ffffffff00000000347ca195a4618000".equals(chainId)
                || "ffffffff00000000347ca188a4614001".equals(chainId)) {
            for (TableHeaderEmployeeItem tableItem : labelList) {
                if (tableHeaderEmployeeItems.size() < tableItem.getPosition()) {
                    tableHeaderEmployeeItems.add(tableItem);
                } else {
                    tableHeaderEmployeeItems.add(tableItem.getPosition(), tableItem);
                }
            }
        }
        return tableHeaderEmployeeItems;
    }
}
