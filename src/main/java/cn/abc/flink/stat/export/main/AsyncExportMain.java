package cn.abc.flink.stat.export.main;

import cn.abc.flink.stat.common.aspect.InterfaceLimiting;
import cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface;
import cn.abc.flink.stat.db.cis.aurora.dao.AsyncExportWriteMapper;
import cn.abc.flink.stat.db.cis.aurora.dao.AsyncExportMapper;
import cn.abc.flink.stat.export.entity.QueueAsyncRule;
import cn.abc.flink.stat.export.entity.QueueRuleEnum;
import cn.abc.flink.stat.export.thread.AsyncExportHraetBeatThread;
import cn.abc.flink.stat.export.thread.V2AsyncExportThread;
import cn.abc.flink.stat.redis.RedisLock;
import cn.abc.flink.stat.service.export.AsyncExportService;
import com.aliyun.oss.OSS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/11/9 11:42
 */
@Service
public class AsyncExportMain implements ApplicationContextAware {
    private Logger logger = LoggerFactory.getLogger(AsyncExportMain.class);
    @Autowired
    private RedisLock redisLock;

    @Autowired
    private AsyncExportMapper readMapper;

    @Autowired
    private AsyncExportWriteMapper writeMapper;

    @Autowired
    private AsyncExportService asyncExportService;

    @Autowired
    private OSS ossClient;

    @Autowired
    private InterfaceLimiting token;

    @Value("${spring.redis.key}")
    private String key;

    @Value("${aliyun.bucket}")
    private String bucketName;

    @Value("${abc.async.export.env}")
    private String env;

    @Value("${spring.redis.key-num}")
    private int keyNum;

    @Value("${abc.async.export.switch}")
    private boolean asyncExportSwitch;

    private Map<String, BaseAsyncExportInterface> exportImplMap = new HashMap<>();

    private static final int THREADPOLL = 1;
    /**
     * 开启缓存所需天数/day
     */
    private static final int CACHEDAY = 365;

    /**
     * @Description : -
     * @param -
     * @return
     * @Author: zs
     * @Date: 2022/11/9 11:18
     * @throws InterruptedException -
     */
    @PostConstruct
    public void init() throws InterruptedException {
        logger.info("switch value = " + asyncExportSwitch);
        if (asyncExportSwitch) {
            //初始化队列和超时时间,查询规则
            List<QueueAsyncRule> queueAsyncRules = new ArrayList<>();
            List<Integer> runningList = new LinkedList<Integer>();
            //通过手动添加，后续需要调整就将规则存到集合中
            queueAsyncRules.add(new QueueAsyncRule("查询当天",
                    QueueAsyncRule.TO_DAY_TIME_OUT, QueueRuleEnum.DAY, "toDay"));
            queueAsyncRules.add(new QueueAsyncRule("查询三十天以下",
                    QueueAsyncRule.SAME_MONTH_TIME_OUT, QueueRuleEnum.MOUTH, "sameMonth"));
            queueAsyncRules.add(new QueueAsyncRule("查询三十天以上",
                    QueueAsyncRule.THAT_YEAR_TIME_OUT, QueueRuleEnum.YEAR, "thatYear"));
            //创建队列和规则
            for (QueueAsyncRule rule : queueAsyncRules) {
                Runnable run = new V2AsyncExportThread(exportImplMap,
                        env, bucketName, rule.getLockName(), keyNum,
                        asyncExportService,
                        redisLock, readMapper, writeMapper, ossClient,
                        runningList, rule.getOutTime(), rule, CACHEDAY, token);
                Thread t = new Thread(run);
                t.start();
            }

            for (int i = 1; i <= THREADPOLL; i++) {
                Runnable r = new AsyncExportHraetBeatThread(runningList, writeMapper);
                Thread th = new Thread(r);
                th.start();
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, BaseAsyncExportInterface> initExportMap = putAsyncExportImplToMap(applicationContext);
        for (Map.Entry<String, BaseAsyncExportInterface> entry : initExportMap.entrySet()) {
            this.exportImplMap.put(entry.getValue().getKey(), entry.getValue());
        }
    }

    /**
     * @Description:
     * @param
     * @param applicationContext -
     * @return
     * @return java.util.Map<java.lang.String,cn.abc.flink.stat.common.interfaces.BaseAsyncExportInterface>
     * @Author: zs
     * @Date: 2022/11/9 11:20
     */
    private Map<String, BaseAsyncExportInterface> putAsyncExportImplToMap(ApplicationContext applicationContext) {
        return applicationContext.getBeansOfType(BaseAsyncExportInterface.class);
    }
}
