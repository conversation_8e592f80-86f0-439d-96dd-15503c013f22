package cn.abc.flink.stat.export.thread;

import cn.abc.flink.stat.common.TimeUtils;
import cn.abc.flink.stat.export.entity.AsyncExportTask;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.List;

/**
 * @BelongsProject: stat
 * @BelongsPackage: cn.abc.flink.stat.export.thread
 * @Author: zs
 * @CreateTime: 2022-12-13  18:32
 * @Description: 异步导出线程工具类
 * @Version: 1.0
 */
@Component
public class ThreadHandler {
    /**
     * @param
     * @param beginDate -
     * @param endDate   -
     * @param cacheDay  多少天
     * @return
     * @return java.lang.Boolean
     * @Description: 判断开始时间和结束时间是否满足缓存要求
     * @Author: zs
     * @Date: 2022/12/13 15:58
     */
    public static Boolean isCache(String beginDate, String endDate, int cacheDay) {
        Boolean result = false;
        if (beginDate != null && endDate != null) {
            boolean today = true;
            try {
                today = TimeUtils.isToday(endDate);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //只有不是当天才需要缓存
            if (!today) {
                long betweenDay = DateUtil.between(DateUtil.parse(beginDate), DateUtil.parse(endDate), DateUnit.DAY);
                if (betweenDay >= cacheDay) {
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * @param
     * @param todayTask todays
     * @param info      当前任务
     * @return
     * @return java.lang.Boolean
     * @Description: 判断当前任务参数当天是否存在过
     * @Author: zs
     * @Date: 2022/12/14 15:03
     */
    public static Boolean paramCompare(List<AsyncExportTask> todayTask, AsyncExportTask info) {
        Boolean result = false;
        if (CollUtil.isEmpty(todayTask)) {
            return result;
        }
        for (AsyncExportTask task : todayTask) {
            //比较两个参数是否相等
            if (task.getParams().equals(info.getParams()) && task.getOss() != null) {
                //相等就直接返回，并将oss地址赋值
                info.setOss(task.getOss());
                result = true;
                break;
            }
        }
        return result;
    }
}
