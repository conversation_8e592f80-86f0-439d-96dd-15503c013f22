package cn.abc.flink.stat.websocket.service;

import cn.abc.flink.stat.websocket.domain.RpcRequestNotification;
import cn.abc.flink.stat.websocket.domain.WebSocketMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * WebSocket通知服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class WebSocketNotificationService {
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 存储用户会话信息
     * Key: sessionId, Value: 用户信息
     */
    private final ConcurrentMap<String, UserSession> userSessions = new ConcurrentHashMap<>();
    
    /**
     * 发送RPC请求通知给所有连接的客户端
     * 
     * @param notification RPC请求通知
     */
    public void sendRpcRequestNotification(RpcRequestNotification notification) {
        WebSocketMessage message = WebSocketMessage.builder()
                .type(WebSocketMessage.MessageType.RPC_REQUEST)
                .content(notification)
                .timestamp(LocalDateTime.now())
                .source(notification.getRequestPath())
                .messageId(UUID.randomUUID().toString())
                .build();
        
        // 发送给所有订阅了 /topic/rpc-notifications 的客户端
        messagingTemplate.convertAndSend("/topic/rpc-notifications", message);
        
        log.info("发送RPC请求通知: {}", notification.getRequestPath());
    }
    
    /**
     * 发送RPC请求通知给特定连锁的用户
     * 
     * @param chainId 连锁ID
     * @param notification RPC请求通知
     */
    public void sendRpcRequestNotificationToChain(String chainId, RpcRequestNotification notification) {
        WebSocketMessage message = WebSocketMessage.builder()
                .type(WebSocketMessage.MessageType.RPC_REQUEST)
                .content(notification)
                .timestamp(LocalDateTime.now())
                .source(notification.getRequestPath())
                .chainId(chainId)
                .messageId(UUID.randomUUID().toString())
                .build();
        
        // 发送给特定连锁的用户
        messagingTemplate.convertAndSend("/topic/rpc-notifications/" + chainId, message);
        
        log.info("发送RPC请求通知给连锁 {}: {}", chainId, notification.getRequestPath());
    }
    
    /**
     * 发送RPC请求通知给特定用户
     * 
     * @param userId 用户ID
     * @param notification RPC请求通知
     */
    public void sendRpcRequestNotificationToUser(String userId, RpcRequestNotification notification) {
        WebSocketMessage message = WebSocketMessage.builder()
                .type(WebSocketMessage.MessageType.RPC_REQUEST)
                .content(notification)
                .timestamp(LocalDateTime.now())
                .source(notification.getRequestPath())
                .userId(userId)
                .messageId(UUID.randomUUID().toString())
                .build();
        
        // 发送给特定用户
        messagingTemplate.convertAndSendToUser(userId, "/queue/rpc-notifications", message);
        
        log.info("发送RPC请求通知给用户 {}: {}", userId, notification.getRequestPath());
    }
    
    /**
     * 发送系统通知
     * 
     * @param content 通知内容
     */
    public void sendSystemNotification(Object content) {
        WebSocketMessage message = WebSocketMessage.builder()
                .type(WebSocketMessage.MessageType.SYSTEM_NOTIFICATION)
                .content(content)
                .timestamp(LocalDateTime.now())
                .messageId(UUID.randomUUID().toString())
                .build();
        
        messagingTemplate.convertAndSend("/topic/system-notifications", message);
        
        log.info("发送系统通知: {}", content);
    }
    
    /**
     * 发送数据更新通知
     * 
     * @param dataType 数据类型
     * @param content 更新内容
     */
    public void sendDataUpdateNotification(String dataType, Object content) {
        WebSocketMessage message = WebSocketMessage.builder()
                .type(WebSocketMessage.MessageType.DATA_UPDATE)
                .content(content)
                .timestamp(LocalDateTime.now())
                .source(dataType)
                .messageId(UUID.randomUUID().toString())
                .build();
        
        messagingTemplate.convertAndSend("/topic/data-updates/" + dataType, message);
        
        log.info("发送数据更新通知 {}: {}", dataType, content);
    }
    
    /**
     * 注册用户会话
     * 
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param chainId 连锁ID
     * @param clinicId 门店ID
     */
    public void registerUserSession(String sessionId, String userId, String chainId, String clinicId) {
        UserSession userSession = UserSession.builder()
                .sessionId(sessionId)
                .userId(userId)
                .chainId(chainId)
                .clinicId(clinicId)
                .connectTime(LocalDateTime.now())
                .build();
        
        userSessions.put(sessionId, userSession);
        log.info("用户会话注册: sessionId={}, userId={}, chainId={}", sessionId, userId, chainId);
    }
    
    /**
     * 移除用户会话
     * 
     * @param sessionId 会话ID
     */
    public void removeUserSession(String sessionId) {
        UserSession removed = userSessions.remove(sessionId);
        if (removed != null) {
            log.info("用户会话移除: sessionId={}, userId={}", sessionId, removed.getUserId());
        }
    }
    
    /**
     * 获取在线用户数量
     * 
     * @return 在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }
    
    /**
     * 获取指定连锁的在线用户数量
     * 
     * @param chainId 连锁ID
     * @return 在线用户数量
     */
    public long getOnlineUserCountByChain(String chainId) {
        return userSessions.values().stream()
                .filter(session -> chainId.equals(session.getChainId()))
                .count();
    }
    
    /**
     * 用户会话信息
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UserSession {
        private String sessionId;
        private String userId;
        private String chainId;
        private String clinicId;
        private LocalDateTime connectTime;
    }
}
