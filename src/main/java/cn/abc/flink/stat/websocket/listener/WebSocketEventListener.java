package cn.abc.flink.stat.websocket.listener;

import cn.abc.flink.stat.websocket.service.WebSocketNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionSubscribeEvent;
import org.springframework.web.socket.messaging.SessionUnsubscribeEvent;

/**
 * WebSocket事件监听器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class WebSocketEventListener {
    
    @Autowired
    private WebSocketNotificationService notificationService;
    
    /**
     * 处理WebSocket连接事件
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        
        log.info("WebSocket连接建立: sessionId={}", sessionId);
        
        // 从请求头中获取用户信息（如果有的话）
        String userId = headerAccessor.getFirstNativeHeader("userId");
        String chainId = headerAccessor.getFirstNativeHeader("chainId");
        String clinicId = headerAccessor.getFirstNativeHeader("clinicId");
        
        if (userId != null && chainId != null) {
            notificationService.registerUserSession(sessionId, userId, chainId, clinicId);
        }
        
        // 发送连接成功消息
        notificationService.sendSystemNotification("用户连接成功: " + sessionId);
    }
    
    /**
     * 处理WebSocket断开连接事件
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        
        log.info("WebSocket连接断开: sessionId={}", sessionId);
        
        notificationService.removeUserSession(sessionId);
        
        // 发送断开连接消息
        notificationService.sendSystemNotification("用户断开连接: " + sessionId);
    }
    
    /**
     * 处理WebSocket订阅事件
     */
    @EventListener
    public void handleWebSocketSubscribeListener(SessionSubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        String destination = headerAccessor.getDestination();
        
        log.info("WebSocket订阅: sessionId={}, destination={}", sessionId, destination);
    }
    
    /**
     * 处理WebSocket取消订阅事件
     */
    @EventListener
    public void handleWebSocketUnsubscribeListener(SessionUnsubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        String subscriptionId = headerAccessor.getSubscriptionId();
        
        log.info("WebSocket取消订阅: sessionId={}, subscriptionId={}", sessionId, subscriptionId);
    }
}
