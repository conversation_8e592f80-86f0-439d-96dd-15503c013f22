package cn.abc.flink.stat.websocket.interceptor;

import cn.abc.flink.stat.websocket.domain.RpcRequestNotification;
import cn.abc.flink.stat.websocket.service.WebSocketNotificationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * RPC请求拦截器
 * 用于拦截RPC请求并通过WebSocket通知前端
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RpcRequestInterceptor implements HandlerInterceptor {
    
    @Autowired
    private WebSocketNotificationService notificationService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private static final String START_TIME_ATTRIBUTE = "startTime";
    private static final String RPC_PATH_PREFIX = "/rpc/";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestPath = request.getRequestURI();
        
        // 只处理RPC请求
        if (!requestPath.startsWith(RPC_PATH_PREFIX)) {
            return true;
        }
        
        // 记录请求开始时间
        request.setAttribute(START_TIME_ATTRIBUTE, System.currentTimeMillis());
        
        try {
            // 构建RPC请求通知
            RpcRequestNotification notification = buildRpcRequestNotification(request, RpcRequestNotification.RequestStatus.STARTED);
            
            // 从请求参数中提取chainId（如果有的话）
            String chainId = extractChainId(request);
            
            if (chainId != null) {
                // 发送给特定连锁的用户
                notificationService.sendRpcRequestNotificationToChain(chainId, notification);
            } else {
                // 发送给所有用户
                notificationService.sendRpcRequestNotification(notification);
            }
            
        } catch (Exception e) {
            log.error("发送RPC请求开始通知失败: {}", e.getMessage(), e);
        }
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        String requestPath = request.getRequestURI();
        
        // 只处理RPC请求
        if (!requestPath.startsWith(RPC_PATH_PREFIX)) {
            return;
        }
        
        try {
            // 计算处理时长
            Long startTime = (Long) request.getAttribute(START_TIME_ATTRIBUTE);
            long processingTime = startTime != null ? System.currentTimeMillis() - startTime : 0;
            
            // 确定请求状态
            RpcRequestNotification.RequestStatus status;
            String errorMessage = null;
            
            if (ex != null) {
                status = RpcRequestNotification.RequestStatus.FAILED;
                errorMessage = ex.getMessage();
            } else if (response.getStatus() >= 400) {
                status = RpcRequestNotification.RequestStatus.FAILED;
                errorMessage = "HTTP Status: " + response.getStatus();
            } else {
                status = RpcRequestNotification.RequestStatus.SUCCESS;
            }
            
            // 构建RPC请求完成通知
            RpcRequestNotification notification = buildRpcRequestNotification(request, status);
            notification.setProcessingTime(processingTime);
            notification.setErrorMessage(errorMessage);
            
            // 从请求参数中提取chainId（如果有的话）
            String chainId = extractChainId(request);
            
            if (chainId != null) {
                // 发送给特定连锁的用户
                notificationService.sendRpcRequestNotificationToChain(chainId, notification);
            } else {
                // 发送给所有用户
                notificationService.sendRpcRequestNotification(notification);
            }
            
        } catch (Exception e) {
            log.error("发送RPC请求完成通知失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 构建RPC请求通知对象
     */
    private RpcRequestNotification buildRpcRequestNotification(HttpServletRequest request, RpcRequestNotification.RequestStatus status) {
        return RpcRequestNotification.builder()
                .requestPath(request.getRequestURI())
                .httpMethod(request.getMethod())
                .requestParams(extractRequestParams(request))
                .headers(extractHeaders(request))
                .clientIp(getClientIpAddress(request))
                .userAgent(request.getHeader("User-Agent"))
                .status(status)
                .build();
    }
    
    /**
     * 提取请求参数
     */
    private Map<String, Object> extractRequestParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();
        
        // 提取查询参数
        request.getParameterMap().forEach((key, values) -> {
            if (values.length == 1) {
                params.put(key, values[0]);
            } else {
                params.put(key, values);
            }
        });
        
        return params;
    }
    
    /**
     * 提取请求头
     */
    private Map<String, String> extractHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            // 只提取关键的请求头，避免敏感信息
            if (isImportantHeader(headerName)) {
                headers.put(headerName, request.getHeader(headerName));
            }
        }
        
        return headers;
    }
    
    /**
     * 判断是否为重要的请求头
     */
    private boolean isImportantHeader(String headerName) {
        String lowerCaseName = headerName.toLowerCase();
        return lowerCaseName.equals("content-type") ||
               lowerCaseName.equals("accept") ||
               lowerCaseName.equals("user-agent") ||
               lowerCaseName.equals("referer") ||
               lowerCaseName.startsWith("x-") ||
               lowerCaseName.equals("authorization");
    }
    
    /**
     * 从请求中提取chainId
     */
    private String extractChainId(HttpServletRequest request) {
        // 首先尝试从请求参数中获取
        String chainId = request.getParameter("chainId");
        
        // 如果参数中没有，尝试从请求头中获取
        if (chainId == null) {
            chainId = request.getHeader("chainId");
        }
        
        // 如果还是没有，尝试从请求头中获取其他可能的字段
        if (chainId == null) {
            chainId = request.getHeader("X-Chain-Id");
        }
        
        return chainId;
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
