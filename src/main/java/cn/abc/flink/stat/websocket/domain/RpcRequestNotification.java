package cn.abc.flink.stat.websocket.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * RPC请求通知数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RpcRequestNotification {
    
    /**
     * 请求路径
     */
    private String requestPath;
    
    /**
     * HTTP方法
     */
    private String httpMethod;
    
    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;
    
    /**
     * 请求头信息
     */
    private Map<String, String> headers;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 请求状态
     */
    private RequestStatus status;
    
    /**
     * 处理时长（毫秒）
     */
    private Long processingTime;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 请求状态枚举
     */
    public enum RequestStatus {
        /**
         * 请求开始
         */
        STARTED,
        
        /**
         * 请求成功
         */
        SUCCESS,
        
        /**
         * 请求失败
         */
        FAILED,
        
        /**
         * 请求超时
         */
        TIMEOUT
    }
}
