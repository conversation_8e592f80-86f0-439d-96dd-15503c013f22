package cn.abc.flink.stat.websocket.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * WebSocket消息实体
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {
    
    /**
     * 消息类型
     */
    private MessageType type;
    
    /**
     * 消息内容
     */
    private Object content;
    
    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * 消息来源（RPC接口路径）
     */
    private String source;
    
    /**
     * 连锁ID
     */
    private String chainId;
    
    /**
     * 门店ID
     */
    private String clinicId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        /**
         * RPC请求通知
         */
        RPC_REQUEST,
        
        /**
         * 系统通知
         */
        SYSTEM_NOTIFICATION,
        
        /**
         * 数据更新通知
         */
        DATA_UPDATE,
        
        /**
         * 错误通知
         */
        ERROR_NOTIFICATION,
        
        /**
         * 心跳消息
         */
        HEARTBEAT
    }
}
