package cn.abc.flink.stat.db.cis.aurora.dao;

import cn.abc.flink.stat.pojo.EmployeeResp;
import cn.abc.flink.stat.service.cis.achievement.charge.entity.AchievementChargeFeeEntity;
import cn.abc.flink.stat.service.cis.achievement.charge.pojos.AchievementChargePersonParam;
import cn.abc.flink.stat.service.cis.selection.entity.CommSelectParams;
import cn.abc.flink.stat.service.cis.selection.entity.PayModeDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/19 19:20
 */
public interface ArnRevenueSelectMapper {

    /**
     * 获取支付方式筛选框内容
     *
     * @param env       -
     * @param chainId   连锁总部id
     * @param clinicId  连锁子店id
     * @param beginDate 查询开始时间
     * @param endDate   查询结束时间
     * @return 支付方式集
     */
    List<PayModeDao> selectPayModes(@Param("env") String env,
                                    @Param("chainId") String chainId,
                                    @Param("clinicId") String clinicId,
                                    @Param("beginDate") String beginDate,
                                    @Param("endDate") String endDate);

    /**
     * 获取费用类别筛选框内容
     *
     * @param env       -
     * @return 费用集
     */
    List<AchievementChargeFeeEntity> selectFeeSecondClassify(@Param("env") String env,
                                                             @Param("param") CommSelectParams param);


    /**
     * 获取收费员筛选框内容
     *
     * @param env       -
     * @param chainId   连锁总部id
     * @param clinicId  连锁子店id
     * @param beginDate 查询开始时间
     * @param endDate   查询结束时间
     * @return -
     */
    List<String> selectCashierIdsByTransactionRecord(@Param("env") String env,
                                                     @Param("chainId") String chainId,
                                                     @Param("clinicId") String clinicId,
                                                     @Param("beginDate") String beginDate,
                                                     @Param("endDate") String endDate);

    /**
     * @param env   env
     * @param param param
     * @return List<String>
     */
    List<String> selectDepartmentIdsByTransactionRecord(@Param("env") String env,
                                                        @Param("param") AchievementChargePersonParam param);

    /**
     * 获取人员
     *
     * @param env   env
     * @param param param
     * @return List<String>
     */
    List<EmployeeResp> selectEmployeeIdsByTransactionRecord(@Param("env") String env,
                                                            @Param("param") AchievementChargePersonParam param);

    /**
     * @param env   env
     * @param param param
     * @return 开单人id/医生id/护士id
     */
    List<EmployeeResp> selectEmployeeIdsExcludeCopywriterByTransactionRecord(
            @Param("env") String env,
            @Param("param") AchievementChargePersonParam param);

    /**
     * @param env    env
     * @param params params
     * @return List<String>
     */
    List<String> selectCopywriterEmployeeIdsByTransactionRecord(@Param("env") String env,
                                                                @Param("params") AchievementChargePersonParam params);

    List<EmployeeResp> selectEmployeeFromDwsCharge(@Param("env") String env,
                                                   @Param("param") CommSelectParams params);
}
