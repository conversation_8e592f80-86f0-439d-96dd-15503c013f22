package cn.abc.flink.stat.db.cis.hologres.dao;

import cn.abc.flink.stat.db.cis.common.CommissionParentMapper;
import cn.abc.flink.stat.service.cis.commission.domain.CommissionEmployeeSummary;
import cn.abc.flink.stat.service.cis.commission.domain.CommissionParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/07 5:03 下午
 */
@Qualifier("hologresSqlSessionTemplate")
public interface HologresCommissionMapper extends CommissionParentMapper {


    List<CommissionEmployeeSummary> getHistoryCommissionEmployeeSummary(@Param("env") String env,
                                                                        @Param("param") CommissionParam param);
}