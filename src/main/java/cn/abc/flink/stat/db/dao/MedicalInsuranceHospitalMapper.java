package cn.abc.flink.stat.db.dao;

import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCount;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalParam;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalPatient;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Collection;
import java.util.List;
import java.util.Set;
/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/16 15:20
 */
@Qualifier("adbMysqlSqlSessionTemplate")
public interface MedicalInsuranceHospitalMapper {
    /**
     * @Description:患者tab主体内容(除了成本)
     * @param
     * @param patientorderDB -
     * @param chargeDB -
     * @param param chainId-clinicId-beginDate-endDate-patientId-chargeType-hospitalStatus-directDoctorId-offset-size-
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalPatient>
     * @Author: zs
     * @Date: 2022/8/16 10:12
     */
    List<MedicalInsuranceHospitalPatient> selectHospitalPatients(@Param("patientorderDB") String patientorderDB,
                                                                 @Param("chargeDB") String chargeDB,
                                                                 @Param("param") MedicalInsuranceHospitalParam param
    );

    /**
     * @Description:患者tab表头合计行
     * @param
     * @param patientorderDB -
     * @param chargeDB -
     * @param shebaoDB -
     * @param param chainId-clinicId-beginDate-endDate-patientId-chargeType-hospitalStatus-directDoctorId-offset-size-
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalPatient>
     * @Author: zs
     * @Date: 2022/8/16 10:12
     */
    MedicalInsuranceHospitalSummary selectHospitalPatientsSummy(@Param("patientorderDB") String patientorderDB,
                                                                @Param("chargeDB") String chargeDB,
                                                                @Param("shebaoDB") String shebaoDB,
                                                                @Param("param") MedicalInsuranceHospitalParam param
    );

    /**
     * @Description:患者tab表头合计行-查询ids，group_concat有长度限制
     * @param
     * @param patientorderDB -
     * @param chargeDB -
     * @param shebaoDB -
     * @param param chainId-clinicId-beginDate-endDate-patientId-chargeType-hospitalStatus-directDoctorId-offset-size-
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalPatient>
     * @Author: zs
     * @Date: 2022/8/16 10:12
     */
    Set<String> selectHospitalPatientsIds(@Param("patientorderDB") String patientorderDB,
                                          @Param("chargeDB") String chargeDB,
                                          @Param("shebaoDB") String shebaoDB,
                                          @Param("param") MedicalInsuranceHospitalParam param
    );

    /**
     * @Description: 患者tab 计算其他成本
     * @param
     * @param patientorderDB -
     * @param chargeDB -
     * @param chainId -
     * @param clinicId -
     * @param ids -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost>
     * @Author: zs
     * @Date: 2022/8/16 10:44
     */
    List<MedicalInsuranceHospitalCost> selectTransactionCost(@Param("patientorderDB") String patientorderDB,
                                                             @Param("chargeRecordDb") String chargeRecordDb,
                                                             @Param("chainId") String chainId,
                                                             @Param("clinicId") String clinicId,
                                                             @Param("ids") Set<String> ids
    );


    /**
     * @Description: 患者tab 计算发药成本
     * @param
     * @param patientorderDB -
     * @param dispensingDB -
     * @param chainId -
     * @param clinicId -
     * @param ids -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost>
     * @Author: zs
     * @Date: 2022/8/16 15:22
     */
    List<MedicalInsuranceHospitalCost> selectDispensingCost(@Param("patientorderDB") String patientorderDB,
                                                            @Param("dispensingDB") String dispensingDB,
                                                            @Param("chainId") String chainId,
                                                            @Param("clinicId") String clinicId,
                                                            @Param("ids") Set<String> ids
    );

    /**
     * @Description:患者tab 记录数
     * @param patientorderDB -
     * @param param patientorderDB-chainId-clinicId-beginDate-endDate-
     *              patientId-chargeType-hospitalStatus-directDoctorId-
     * @return
     * @return java.lang.Integer
     * @Author: zs
     * @Date: 2022/8/16 10:23
     */
    MedicalInsuranceHospitalCount selectHospitalPatientsCount(@Param("patientorderDB") String patientorderDB,
                                                              @Param("param") MedicalInsuranceHospitalParam param);


    /**
     * @Description: 医生tab-责任医生
     * @param
     * @param patientorderDB -
     * @param chargeDB -
     * @param param chainId -clinicId -beginDate -endDate -doctorId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor>
     * @Author: zs
     * @Date: 2022/8/16 11:14
     */
    List<MedicalInsuranceHospitalDoctor> selectHospitalDirectDoctor(
            @Param("patientorderDB") String patientorderDB,
            @Param("chargeDB") String chargeDB,
            @Param("param") MedicalInsuranceHospitalParam param);

    /**
     * @Description: 医生tab-登记医生
     * @param
     * @param patientorderDB -
     * @param chargeDB -
     * @param param chainId -clinicId -beginDate -endDate -doctorId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor>
     * @Author: zs
     * @Date: 2022/8/16 11:18
     */
    List<MedicalInsuranceHospitalDoctor> selectHospitalRegisterDoctor(
            @Param("patientorderDB") String patientorderDB,
            @Param("chargeDB") String chargeDB,
            @Param("param") MedicalInsuranceHospitalParam param);

    /**
     * @Description: 医生tab-开单医生
     * @param
     * @param patientorderDB -
     * @param outpatientDB -
     * @param param chainId -clinicId -beginDate -endDate -doctorId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor>
     * @Author: zs
     * @Date: 2022/8/16 15:23
     */
    List<MedicalInsuranceHospitalDoctor> selectHospitalOrderDoctor(
            @Param("patientorderDB") String patientorderDB,
            @Param("outpatientDB") String outpatientDB,
            @Param("param") MedicalInsuranceHospitalParam param);

    /**
     * @Description: 医生tab-汇总
     * @param
     * @param patientorderDB -
     * @param chargeDB -
     * @param param chainId -clinicId -beginDate -endDate -doctorId -
     * @return
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor
     * @Author: zs
     * @Date: 2022/8/16 14:18
     */
    MedicalInsuranceHospitalDoctor selectHospitalSummary(@Param("patientorderDB") String patientorderDB,
                                                         @Param("chargeDB") String chargeDB,
                                                         @Param("param") MedicalInsuranceHospitalParam param);

    /**
     * @Description: 筛选框-责任医生
     * @param
     * @param patientorderDB -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return java.util.List<java.lang.String>
     * @Author: zs
     * @Date: 2022/8/16 15:27
     */
    List<String> selectConditionDirectDoctor(@Param("patientorderDB") String patientorderDB,
                                             @Param("chainId") String chainId,
                                             @Param("clinicId") String clinicId,
                                             @Param("beginDate") String beginDate,
                                             @Param("endDate") String endDate);

    /**
     * @Description: 筛选框-结算类型
     * @param
     * @param patientorderDB -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return java.util.List<java.lang.String>
     * @Author: zs
     * @Date: 2022/8/16 15:28
     */
    List<String> selectConditionChargeType(@Param("patientorderDB") String patientorderDB,
                                           @Param("chainId") String chainId,
                                           @Param("clinicId") String clinicId,
                                           @Param("beginDate") String beginDate,
                                           @Param("endDate") String endDate);
    /**
     * @Description: 筛选框-个人类别
     * @param
     * @param patientorderDB -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return java.util.List<java.lang.String>
     * @Author: zs
     * @Date: 2022/8/16 15:28
     */
    List<String> selectPersonalType(@Param("patientorderDB") String patientorderDB,
                                           @Param("chainId") String chainId,
                                           @Param("clinicId") String clinicId,
                                           @Param("beginDate") String beginDate,
                                           @Param("endDate") String endDate);


    /**
     * @Description:
     * @param
     * @param patientorderDB -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return java.util.List<java.lang.Long>
     * @Author: zs
     * @Date: 2022/8/16 11:24
     */
    List<Long> selectHospitalPatientOrderIds(@Param("patientorderDB") String patientorderDB,
                                             @Param("chainId") String chainId,
                                             @Param("clinicId") String clinicId,
                                             @Param("beginDate") String beginDate,
                                             @Param("endDate") String endDate);


    /**
     * @Description: 长护统计查询外诊费用
     * @param
     * @param shebaodb -
     * @param param -
     * @param ids -
     * @return
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary
     * @Author: zs
     * @Date: 2023/5/12 11:51
     */
    MedicalInsuranceHospitalSummary selectExternalDiagnosis(@Param("shebaodb")String shebaodb,
                                                            @Param("params") MedicalInsuranceHospitalParam param,
                                                            @Param("ids") Set<String> ids);

    /**
     * @Description: 长护统计-医保结算总额
     * @param
     * @param shebaodb -
     * @param param -
     * @param ids -
     * @return
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary
     * @Author: zs
     * @Date: 2023/5/12 11:51
     */
    Double selectTotalSettlementAmount(@Param("shebaodb")String shebaodb,
                                       @Param("params") MedicalInsuranceHospitalParam param,
                                       @Param("ids") Set<String> ids);

    /**
     * @Description: 长护统计-待结算总额
     * @param
     * @param chargeDb -
     * @param chargeRecordDb -
     * @param goodsDb -
     * @param ids -
     * @return
     * @return java.lang.Double
     * @Author: zs
     * @Date: 2025/7/31 11:02
     */
    Double selectPendingSettlementAmount(@Param("chargeDb") String chargeDb,
                                         @Param("chargeRecordDb") String chargeRecordDb,
                                         @Param("goodsDb") String goodsDb,
                                         @Param("patientorderDb") String patientorderDb,
                                         @Param("shebaoDb") String shebaoDb,
                                         @Param("ids") Collection<String> ids);


    /**
     * @Description: 长护统计查询总费用
     * @param
     * @param chargedb -
     * @param param -
     * @param ids -
     * @return
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary
     * @Author: zs
     * @Date: 2023/5/12 14:08
     */
    MedicalInsuranceHospitalSummary selectTotalFee(@Param("chargedb")String chargedb,
                                                   @Param("params")MedicalInsuranceHospitalParam param,
                                                   @Param("ids")Set<String> ids);

    /**
     * @Description: 长护统计查询期间院内总费用
     * @param
     * @param chargedb -
     * @param param -
     * @param ids -
     * @return
     * @return cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalSummary
     * @Author: zs
     * @Date: 2023/5/12 14:08
     */
    MedicalInsuranceHospitalSummary selectPartInHospitalFee(@Param("chargedb")String chargedb,
                                                            @Param("params")MedicalInsuranceHospitalParam param,
                                                            @Param("ids")Set<String> ids);

}
