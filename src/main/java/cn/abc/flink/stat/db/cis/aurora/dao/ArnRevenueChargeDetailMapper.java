package cn.abc.flink.stat.db.cis.aurora.dao;

import cn.abc.flink.stat.common.dto.AmountDto;
import cn.abc.flink.stat.db.cis.common.RevenueChargeDetailMapper;
import cn.abc.flink.stat.service.cis.patientScreen.domain.CountAndAmountDto;
import cn.abc.flink.stat.service.cis.patientScreen.domain.OutpatientDto;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchivesParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: lzq
 * @Date: 2023/4/4 4:11 下午
 */
@Qualifier("statMysqlSqlSessionTemplate")
public interface ArnRevenueChargeDetailMapper extends RevenueChargeDetailMapper {

    List<CountAndAmountDto> selectCountAndAmontByPatientIds(@Param("env") String cisTable,
                                                            @Param("params") PatientArchivesParam params,
                                                            @Param("ids") List<String> patientIds);

    List<OutpatientDto> selectOutpatientByPatientIds(@Param("env") String cisTable,
                                                     @Param("params") PatientArchivesParam params,
                                                     @Param("ids") List<String> patientIds);

    AmountDto selectAmontByPatientIds(@Param("env") String cisTable,
                                            @Param("chainId") String chainId,
                                            @Param("clinicId") String clinicId,
                                            @Param("arrearsStatTiming") String arrearsStatTiming,
                                            @Param("ids") List<String> patientIds);
}
