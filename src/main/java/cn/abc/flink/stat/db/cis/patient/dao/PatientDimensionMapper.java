package cn.abc.flink.stat.db.cis.patient.dao;

import cn.abc.flink.stat.common.request.params.MemberParam;
import cn.abc.flink.stat.dimension.domain.FamilyDoctorConfig;
import cn.abc.flink.stat.dimension.domain.V2Patient;
import cn.abc.flink.stat.dimension.domain.V2PatientClinic;
import cn.abc.flink.stat.dimension.domain.V2PatientExtend;
import cn.abc.flink.stat.dimension.domain.V2PatientMemberType;
import cn.abc.flink.stat.dimension.domain.V2PatientOrgan;
import cn.abc.flink.stat.dimension.domain.V2PatientPointsConfig;
import cn.abc.flink.stat.dimension.domain.V2PatientSourceType;
import cn.abc.flink.stat.dimension.domain.V2PatientTag;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Qualifier("dbPatientSqlSessionTemplate")
public interface PatientDimensionMapper {

    /**
     * @param id id
     * @return V2Patient
     */
    V2Patient selectPatientById(@Param("id") String id);

    /**
     * @param chainId chainId
     * @param list    list
     * @return List<V2Patient>
     */
    List<V2Patient> selectPatientByIds(@Param("chainId") String chainId,
                                       @Param("list") Collection<String> list);

    /**
     * @param chainId -
     * @param list -
     * @return -
     */
    List<V2Patient> selectPatientAndMemberTypeByIds(@Param("chainId") String chainId,
                                                    @Param("list") Collection<String> list);

    /**
     * @param chainId chainId
     * @param list    list
     * @return List<V2Patient>
     */
    List<V2PatientClinic> selectPatientClinicIdByIds(@Param("chainId") String chainId,
                                                     @Param("clinicId") String clinicId,
                                                     @Param("list") Collection<String> list);

    /**
     * @param chainId chainId
     * @param list    list
     * @return List<V2Patient>
     */
    List<V2PatientExtend> selectPatientExtendByIds(@Param("chainId") String chainId,
                                                   @Param("list") Collection<String> list);

    /**
     * @param list list
     * @return List<V2PatientMemberType>
     */
    List<V2PatientMemberType> selectMemberByIds(@Param("list") List<String> list);

    /**
     * @param chainId  chainId
     * @param clinicId clinicId
     * @return List<V2PatientMemberType>
     */
    List<V2PatientMemberType> selectMemberTypeByOrgan(@Param("chainId") String chainId,
                                                      @Param("clinicId") String clinicId);

    /**
     * @param chainId 连锁id
     * @param clinicId 门店id
     * @param memberTypeId 会员类型id
     * @return List<String>
     */
    List<String> selectMemberIdsByCreatedClinicAndMemberType(@Param("chainId") String chainId,
                                                             @Param("clinicId") String clinicId,
                                                             @Param("memberTypeId") String memberTypeId);

    /**
     * @param id id
     * @return FamilyDoctorConfig
     */
    FamilyDoctorConfig selectFamilyDoctorConfigById(@Param("id") String id);

    /**
     * @param chainId chainId
     * @return List<FamilyDoctorConfig>
     */
    List<FamilyDoctorConfig> selectFamilyDoctorConfigByChainId(@Param("chainId") String chainId);

    /**
     * @param list list
     * @return List<FamilyDoctorConfig>
     */
    List<FamilyDoctorConfig> selectFamilyDoctorConfigByIds(@Param("list") List<String> list);

    /**
     * @param chainId chainId
     * @return List<V2PatientSourceType>
     */
    List<V2PatientSourceType> selectPatientSourceTypeByChainId(@Param("chainId") String chainId);

    /**
     * @param chainId chainId
     * @return V2PatientPointsConfig
     */
    V2PatientPointsConfig selectPatientPointsConfigByChainId(@Param("chainId") String chainId);

    /**
     * @param chainId  chainId
     * @param parentId parentId
     * @return chainId
     */
    List<V2PatientSourceType> selectPatientSourceTypeByParentId(@Param("chainId") String chainId,
                                                                @Param("parentId") String parentId);

    /**
     * @param chainId chainId
     * @return chainId
     */
    List<V2PatientTag> selectPatientTagByChainId(@Param("chainId") String chainId);

    /**
     * @param chainId chainId
     * @return chainId
     */
    List<V2PatientTag> selectPatientTagByIds(@Param("chainId") String chainId, @Param("tags") Set<String> tags, @Param("patientIds") Collection<String> patientIds);

    /**
     * @param chainId chainId
     * @return chainId
     */
    List<V2PatientTag> selectPatientTagByTagIds(@Param("chainId") String chainId, @Param("tags") Collection<String> tags);

    /**
     * 机构客户id查询机构客户信息
     *
     * @param id 机构客户id
     * @return 机构客户信息
     */
    V2PatientOrgan selectPatientOrganById(@Param("id") String id);

    /**
     * 根据连锁id以及机构客户ids查询机构客户信息
     *
     * @param chainId       连锁id
     * @param notInCacheIds 机构客户ids
     * @return 客户信息map
     */
    List<V2PatientOrgan> selectPatientOrganByIds(@Param("chainId")String chainId,@Param("notInCacheIds") Set<Long> notInCacheIds);
}
