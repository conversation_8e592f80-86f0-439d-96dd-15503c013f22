package cn.abc.flink.stat.db.cis.aurora.dao;

import cn.abc.flink.stat.db.cis.common.OperationCashierParentMapper;
import cn.abc.flink.stat.service.cis.operation.domain.CashierFeeClassify;
import cn.abc.flink.stat.service.cis.operation.domain.CashierOverall;
import cn.abc.flink.stat.service.cis.operation.domain.CashierPayMode;
import cn.abc.flink.stat.service.cis.operation.domain.DispensingLogCost;
import cn.abc.flink.stat.service.cis.operation.domain.OperationChargeReqParams;
import cn.abc.flink.stat.service.cis.revenue.charge.daily.entity.RevenueParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;

/**
 * <AUTHOR>
 */
@Qualifier("statMysqlSqlSessionTemplate")
public interface ArnOperationCashierMapper  extends OperationCashierParentMapper {

    /**
     * 统计 支付方式
     * @param bisTable abc_bis
     * @param cisTable abc_cis
     * @param params 参数
     * @param group 分组
     * @return list
     */
    List<CashierPayMode> fetchPayMode(@Param("bisTable") String bisTable,
                                      @Param("cisTable") String cisTable,
                                      @Param("params") OperationChargeReqParams params,
                                      @Param("group") String group);

    /**
     * 统计 支付方式
     * @param bisTable abc_bis
     * @param cisTable abc_cis
     * @param params 参数
     * @param group 分组
     * @return list
     */
    List<CashierFeeClassify> fetchFeeClassify(@Param("bisTable") String bisTable,
                                              @Param("cisTable") String cisTable,
                                              @Param("params") OperationChargeReqParams params,
                                              @Param("group") String group);

    /**
     * 统计 支付方式(医院管家)
     * @param bisTable abc_bis
     * @param cisTable abc_cis
     * @param params 参数
     * @param group 分组
     * @return list
     */
    List<CashierFeeClassify> fetchAdviceFeeClassify(@Param("bisTable") String bisTable,
                                                    @Param("cisTable") String cisTable,
                                                    @Param("params") OperationChargeReqParams params,
                                                    @Param("group") String group);

    /**
     * 统计 支付方式
     * @param bisTable abc_bis
     * @param cisTable abc_cis
     * @param params 参数
     * @param group 分组
     * @return list
     */
    List<CashierOverall> fetchOverall(@Param("bisTable") String bisTable,
                                      @Param("cisTable") String cisTable,
                                      @Param("params") OperationChargeReqParams params,
                                      @Param("group") String group);

    /**
     * 统计 支付方式
     * @param bisTable abc_bis
     * @param cisTable abc_cis
     * @param params 参数
     * @param group 分组
     * @return list
     */
    List<DispensingLogCost> fetchDispensingCost(@Param("bisTable") String bisTable,
                                                @Param("cisTable") String cisTable,
                                                @Param("params") OperationChargeReqParams params,
                                                @Param("group") String group);

    /**
     * 统计 支付方式
     * @param bisTable abc_bis
     * @param cisTable abc_cis
     * @param params 参数
     * @param group 分组
     * @return list
     */
    List<CashierPayMode> fetchMember(@Param("bisTable") String bisTable,
                                     @Param("cisTable") String cisTable,
                                     @Param("params") OperationChargeReqParams params,
                                     @Param("group") String group);

    List<String> selectChargeTransactionCashierIds(@Param("cisTable") String cisTable,
                                                   @Param("beginDate") String beginDate,
                                                   @Param("params") RevenueParam params);

    List<String> selectChargeTransactionDepartmentIds(@Param("cisTable") String cisTable,
                                                      @Param("beginDate") String beginDate,
                                                      @Param("params") RevenueParam params);
}
