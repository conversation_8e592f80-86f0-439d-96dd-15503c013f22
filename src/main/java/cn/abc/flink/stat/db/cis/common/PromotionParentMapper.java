package cn.abc.flink.stat.db.cis.common;

import cn.abc.flink.stat.common.request.params.PromotionParam;
import cn.abc.flink.stat.service.cis.promotion.card.pojo.PromotionCardOverviewDao;
import cn.abc.flink.stat.service.cis.promotion.card.pojo.PromotioncardOverviewGroupbyCardDao;
import cn.abc.flink.stat.service.cis.promotion.discount.entity.PromotionDiscountOverviewDao;
import cn.abc.flink.stat.service.cis.promotion.gift.entity.PromotionGiftOverviewDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Description: new java files header..
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/27 15:11
 */
public interface PromotionParentMapper {
	/**
	 * @Description:
	 * @param
	 * @param env -
	 * @param chainId -
	 * @param clinicId -
	 * @param beginDate -
	 * @param endDate -
	 * @return
	 * @return cn.abc.flink.stat.service.cis.promotion.card.pojo.PromotionCardOverviewDao
	 * @Author: zs
	 * @Date: 2023/4/4 13:45
	 */
	PromotionCardOverviewDao selectCardOverview(@Param("env") String env,
	                                            @Param("chainId") String chainId,
	                                            @Param("clinicId") String clinicId,
	                                            @Param("beginDate") String beginDate,
	                                            @Param("endDate") String endDate);

	/**
	 * @Description:
	 * @param
	 * @param env -
	 * @param chainId -
	 * @param clinicId -
	 * @param beginDate -
	 * @param endDate -
	 * @return
	 * @return java.util.List<cn.abc.flink.stat.service.cis.promotion.card.pojo.PromotioncardOverviewGroupbyCardDao>
	 * @Author: zs
	 * @Date: 2023/4/4 13:45
	 */
	List<PromotioncardOverviewGroupbyCardDao> selectCardOverviewGroupByCard(@Param("env") String env,
	                                                                        @Param("chainId") String chainId,
	                                                                        @Param("clinicId") String clinicId,
	                                                                        @Param("beginDate") String beginDate,
	                                                                        @Param("endDate") String endDate);

	/**
	 *
	 * @param db db
	 * @param param param
	 * @param sheetIdsSQL sheetIdsSQL
	 * @param isGroupBy isGroupBy
	 * @return List<PromotionGiftOverviewDao>
	 */
	List<PromotionGiftOverviewDao> selectGiftPriceBySheetIds(@Param("db") String db,
	                                                         @Param("param") PromotionParam param,
	                                                         @Param("sheetIdsSQL") String sheetIdsSQL,
	                                                         @Param("isGroupBy") boolean isGroupBy);

	/**
	 *
	 * @param db db
	 * @param param param
	 * @param sheetIdsSQL sheetIdsSQL
	 * @return PromotionDiscountOverviewDao
	 */
	PromotionDiscountOverviewDao selectDiscountPriceBySheetIds(@Param("db") String db,
	                                                           @Param("param") PromotionParam param,
	                                                           @Param("sheetIdsSQL") String sheetIdsSQL);

	/**
	 *
	 * @param db db
	 * @param param param
	 * @param sheetIdsSQL sheetIdsSQL
	 * @return List<PromotionDiscountOverviewDao>
	 */
	List<PromotionDiscountOverviewDao> selectDiscountPriceListBySheetIds(@Param("db") String db,
	                                                                     @Param("param") PromotionParam param,
	                                                                     @Param("sheetIdsSQL") String sheetIdsSQL);

	/**
	 * @param db            db
	 * @param param         param
	 * @param promotionType 1.优惠折扣   2.满减返
	 * @return List<String>
	 */
	List<String> selectSheetIdsFromTransactionRecord(@Param("db") String db,
	                                                 @Param("param") PromotionParam param,
	                                                 @Param("promotionType") Integer promotionType);
}
