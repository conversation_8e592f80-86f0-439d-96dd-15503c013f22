package cn.abc.flink.stat.db.cis.hologres.dao;

import cn.abc.flink.stat.db.cis.common.MedicalInsuranceHospitalParentMapper;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor;
import cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/8/16 10:45
 */
public interface HoloMedicalInsuranceHospitalMapper extends MedicalInsuranceHospitalParentMapper {


    /**
     * @Description: 患者tab 计算发药成本
     * @param
     * @param env -
     * @param param -
     * @param ids -
     * @param isSum 是否sum
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost>
     * @Author: zs
     * @Date: 2022/8/16 10:46
     */
    List<MedicalInsuranceHospitalCost> selectDispensingCost(@Param("env") String env,
                                                            @Param("params") MedicalInsuranceHospitalParam param,
                                                            @Param("isSum") int isSum,
                                                            @Param("ids") Set<Long> ids
    );

    /**
     * @Description: 患者tab 计算收费成本
     * @param
     * @param env -
     * @param param -
     * @param ids -
     * @param isSum 是否sum
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalCost>
     * @Author: zs
     * @Date: 2022/8/16 10:46
     */
    List<MedicalInsuranceHospitalCost> selectTransactionCost(@Param("env") String env,
                                                            @Param("params") MedicalInsuranceHospitalParam param,
                                                            @Param("isSum") int isSum,
                                                            @Param("ids") Set<Long> ids
    );

    /**
     * @Description: 医生tab-开单医生
     * @param ids -
     * @param env -
     * @param param chainId -clinicId -beginDate -endDate -doctorId -ids -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.hospital.domain.MedicalInsuranceHospitalDoctor>
     * @Author: zs
     * @Date: 2022/8/16 10:48
     */
    List<MedicalInsuranceHospitalDoctor> selectHospitalOrderDoctor(
            @Param("env") String env,
            @Param("param") MedicalInsuranceHospitalParam param,
            @Param("ids") List<Long> ids
    );





}
