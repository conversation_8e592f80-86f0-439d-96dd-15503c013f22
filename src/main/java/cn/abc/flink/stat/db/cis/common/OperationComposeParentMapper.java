package cn.abc.flink.stat.db.cis.common;

import cn.abc.flink.stat.common.MapResultHandler;
import cn.abc.flink.stat.service.cis.operation.domain.OperationComposeReqParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description:收费套餐统计mapper
 * @return
 * @Author: zs
 * @Date: 2023/3/13 14:01
 */
public interface OperationComposeParentMapper {
    /**
     * @Description: 收费套餐
     * @param
     * @param bisTable -
     * @param cisTable -
     * @param params -
     * @return
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * @Author: zs
     * @Date: 2023/3/13 14:01
     */
    List<Map<String, Object>> listOperationCompose(@Param("bisTable") String bisTable,
                                                   @Param("cisTable") String cisTable,
                                                   @Param("params") OperationComposeReqParams params);

    /**
     * @Description: 收费套餐消费次数查询
     * @param
     * @param bisTable -
     * @param cisTable -
     * @param params -
     * @param mapResultHandler -
     * @return
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * @Author: zs
     * @Date: 2023/3/27 19:11
     */
    void listOperationComposeCount(@Param("bisTable") String bisTable,
                                   @Param("cisTable") String cisTable,
                                   @Param("params") OperationComposeReqParams params,
                                   MapResultHandler mapResultHandler);

    /**
     * @Description: 收费套餐
     * @param
     * @param bisTable -
     * @param cisTable -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @param goodsId -
     * @return
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * @Author: zs
     * @Date: 2023/3/13 14:01
     */
    List<Map<String, Object>> listOperationComposeExcludedDispensingCost(@Param("bisTable") String bisTable,
                                                                         @Param("cisTable") String cisTable,
                                                                         @Param("chainId") String chainId,
                                                                         @Param("clinicId") String clinicId,
                                                                         @Param("beginDate") String beginDate,
                                                                         @Param("endDate") String endDate,
                                                                         @Param("goodsId") String goodsId);
}
