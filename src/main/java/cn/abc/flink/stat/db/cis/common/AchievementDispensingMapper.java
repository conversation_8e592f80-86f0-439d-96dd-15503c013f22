package cn.abc.flink.stat.db.cis.common;

import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingDetailDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingGadgetDetailDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingGadgetSummaryDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelAmountDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelBaseDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelDoseDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelGoodsTypeDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.AchievementDispensingPersonnelPrescriptionDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.DispensingOperationDetailDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.entity.DispensingOperationPersonnelDao;
import cn.abc.flink.stat.service.cis.achievement.dispensing.pojo.AchievementDispensingReqParams;
import cn.abc.flink.stat.service.his.achievement.dispensing.domain.HisAchievementDispensingDetailDao;
import cn.abc.flink.stat.service.his.achievement.dispensing.domain.HisAchievementDispensingPersonnelBaseDao;
import cn.abc.flink.stat.service.his.achievement.dispensing.domain.HisAchievementDispensingPersonnelDoseDao;
import cn.abc.flink.stat.service.his.achievement.dispensing.domain.HisAchievementDispensingReqParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: lzq
 * @Date: 2023/4/17 3:03 下午
 */
public interface AchievementDispensingMapper {
    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelBaseDao> selectPersonnelBase(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelGoodsTypeDao> selectPersonnelGoodsType(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelGoodsTypeDao> selectPersonnelMaterialGoodsType(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);


    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelGoodsTypeDao> selectPersonnelComposeGoodsType(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);


    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelPrescriptionDao> selectPersonnelPerscription(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelPrescriptionDao> selectDispenserCount(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelDoseDao> selectPersonnelDose(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingPersonnelDoseDao> selectPersonnelDoseAndUnitCount(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingDetailDao> selectDispensingDetail(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    Long selectDispensingDetailTotal(@Param("env") String env,
                                     @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    Integer getDispensingNum(@Param("env") String env,
                             @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    AchievementDispensingGadgetSummaryDao getDispensingPrescription(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    Integer getDispensingRetailNum(@Param("env") String env,
                                   @Param("params") AchievementDispensingReqParams params);


    /**
     * @param env            -
     * @param params         chainId -clinicId -
     * @param processFormSet -
     * @param formSize       -
     * @return -
     */
    Integer getProcessNUm(@Param("env") String env,
                          @Param("params") AchievementDispensingReqParams params,
                          @Param("processFormSet") Set<String> processFormSet,
                          @Param("formSize") Integer formSize);


    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<AchievementDispensingGadgetDetailDao> getDispensingGadgetDetail(
            @Param("env") String env,
            @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    Long getDispensingGadgetDetailTotal(@Param("env") String env,
                                        @Param("params") AchievementDispensingReqParams params);


    //下面是kudu/statAdb中有的方法mysql中没有

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<DispensingOperationPersonnelDao>
    getDispensingOperationPersonnel(@Param("env") String env,
                                    @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<DispensingOperationPersonnelDao>
    getDeploymentPersonnel(@Param("env") String env,
                           @Param("params") AchievementDispensingReqParams params);


    /**
     * @param env            -
     * @param params         -
     * @return -
     */
    DispensingOperationPersonnelDao
    getDispensingOperationPersonnelSummary(@Param("env") String env,
                                           @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env            -
     * @param params         -
     * @return -
     */
    DispensingOperationPersonnelDao
    getDeploymentPersonnelSummary(@Param("env") String env,
                                  @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<DispensingOperationPersonnelDao>
    getDispensingNumberSummary(@Param("env") String env,
                               @Param("params") AchievementDispensingReqParams params);


    /**
     * @param env               -
     * @param params            -
     * @param prescriptionTypes -
     * @return -
     */
    List<DispensingOperationDetailDao>
    getDispensingOperationDetail(@Param("env") String env,
                                 @Param("params") AchievementDispensingReqParams params,
                                 @Param("prescriptionTypes") Set<Integer> prescriptionTypes);

    /**
     * @param env               -
     * @param params            -
     * @param prescriptionTypes -
     * @return -
     */
    Long getDispensingOperationDetailTotal(@Param("env") String env,
                                           @Param("params") AchievementDispensingReqParams params,
                                           @Param("prescriptionTypes") Set<Integer> prescriptionTypes);

    /**
     * @param env    -
     * @param params -
     * @return -
     */
    List<String> getDispensingOperationSelectPerson(@Param("env") String env,
                                                    @Param("params") AchievementDispensingReqParams params);


    /**
     * @param env       -
     * @param params   -
     * @return -
     */
    List<Integer> getDispensingOperationSelectProcessType(@Param("env") String env,
                                                          @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env           -
     * @param params       -
     * @return -
     */
    List<String> getDispensingOperationSelectClinic(@Param("env") String env,
                                                    @Param("params") AchievementDispensingReqParams params);

    /**
     * 获取加工的fromid
     *
     * @param env    -
     * @param params -
     * @return -
     */
    Set<String> getDispensingProcess(@Param("env") String env,
                                     @Param("params") AchievementDispensingReqParams params);

    /**
     * 根据fromID获取发药的最小时间和最大时间
     *
     * @param env    -
     * @param params -
     * @return -
     */
    AchievementDispensingReqParams getDispensingMedicineTime(@Param("env") String env,
                                                             @Param("params") AchievementDispensingReqParams params);

    /**
     * @param env       -
     * @param params   -
     * @return -
     * 查询加工业绩-加工方式
     */
    List<String> getDispensingOperationSelectProcessUsage(@Param("env") String env,
                                                          @Param("params") AchievementDispensingReqParams params);

    /**
     * 住院药房业绩-人员-发药,退药种数
     *
     * @param env    -
     * @param params -
     * @return -
     */
    List<HisAchievementDispensingPersonnelBaseDao> selectHisPersonnelBase(@Param("env") String env,
                                                                          @Param("params") HisAchievementDispensingReqParams params);

    /**
     * 住院药房业绩-人员-发药,退药种数-合计行
     *
     * @param env    -
     * @param params -
     * @return -
     */
    HisAchievementDispensingPersonnelBaseDao selectHisPersonnelBaseSummary(@Param("env") String env,
                                                                           @Param("params") HisAchievementDispensingReqParams params);

    /**
     * 住院药房业绩-人员剂数
     *
     * @param env    -
     * @param params -
     * @return -
     */
    List<HisAchievementDispensingPersonnelDoseDao> selectHisPersonnelDose(@Param("env") String env,
                                                                          @Param("params") HisAchievementDispensingReqParams params);

    /**
     * 住院药房业绩-人员剂数-合计行
     *
     * @param env    -
     * @param params -
     * @return -
     */
    HisAchievementDispensingPersonnelDoseDao selectHisPersonnelDoseSummary(@Param("env") String env,
                                                                           @Param("params") HisAchievementDispensingReqParams params);

    /**
     * 住院药房业绩-明细
     *
     * @param env    -
     * @param params -
     * @return -
     */
    List<HisAchievementDispensingDetailDao> selectHisDispensingDetail(@Param("env") String env,
                                                                      @Param("params") HisAchievementDispensingReqParams params);

    /**
     * 住院药房业绩-明细汇总
     *
     * @param env    -
     * @param params -
     * @return -
     */
    Long selectHisDispensingDetailTotal(@Param("env") String env,
                                        @Param("params") HisAchievementDispensingReqParams params);

    List<AchievementDispensingPersonnelAmountDao> selectPersonnelAmount(@Param("env") String env,
                                                                        @Param("params") AchievementDispensingReqParams params);

}
