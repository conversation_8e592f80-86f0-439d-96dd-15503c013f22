package cn.abc.flink.stat.db.cis.aurora.dao;

import cn.abc.flink.stat.common.request.params.PromotionParam;
import cn.abc.flink.stat.db.cis.common.PatientPointsMapper;
import cn.abc.flink.stat.service.cis.promotion.points.entity.PointsTransactionRecordLogDao;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;

/**
 * @description:
 * @author: dy
 * @create: 2021-09-26 10:29
 */
@Qualifier("statMysqlSqlSessionTemplate")
public interface ArnPatientPointsMapper extends PatientPointsMapper {

    /**
     * @param table   table
     * @param basicDb basicDb
     * @param param   param
     * @return List<PointsTransactionRecordLogDao>
     */
    List<PointsTransactionRecordLogDao> selectPointTransactionRecord(@Param("table") String table,
                                                                     @Param("basicDb") String basicDb,
                                                                     @Param("param") PromotionParam param);

    /**
     * @param table table
     * @param param param
     * @return Long
     */
    Long selectPointTransactionRecordTotal(@Param("table") String table,
                                           @Param("param") PromotionParam param);
}
