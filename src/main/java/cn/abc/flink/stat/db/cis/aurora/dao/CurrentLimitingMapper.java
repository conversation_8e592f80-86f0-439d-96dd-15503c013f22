package cn.abc.flink.stat.db.cis.aurora.dao;

import cn.abc.flink.stat.common.aspect.doman.InterfaceLimitingDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;


/**
 * @Description:
 * @return
 * @Author: zs
 * @Date: 2022/11/9 11:43
 */
@Qualifier("statMysqlSqlSessionTemplate")
public interface CurrentLimitingMapper {

    /**
     * @Description: 获取令牌
     * @param
     * @param cisTable db
     * @return
     * @return cn.abc.flink.stat.common.aspect.doman.InterfaceLimitingDto
     * @Author: zs
     * @Date: 2022/12/16 09:40
     */
    InterfaceLimitingDto getToken(@Param("db")String cisTable);

    /**
     * @Description: 修改令牌
     * @param
     * @param cisTable db
     * @param token 令牌实体类
     * @param status 条件状态
     * @return
     * @Author: zs
     * @Date: 2022/12/16 10:20
     */
    void updateToken(@Param("db")String cisTable,
                     @Param("token")InterfaceLimitingDto token,
                     @Param("status")Integer status);
}
