package cn.abc.flink.stat.db.cis.common;

import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientFirstRevisitRsp;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientIdDto;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientParam;
import cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientSummaryDao;
import cn.abc.flink.stat.service.cis.outpatient.domain.PatientListInfo;
import cn.abc.flink.stat.service.cis.outpatient.domain.RevisitStatRsp;
import cn.abc.flink.stat.service.cis.outpatient.domain.VisitSourceInfo;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralDaily;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralReferrerRewardParam;
import cn.abc.flink.stat.service.cis.promotion.referral.referrer.reward.pojo.PromotionReferralRpcParam;
import cn.abc.flink.stat.service.customize.jh.operation.daily.JhParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/18 1:53 下午
 * @modified ljc
 */
public interface OutpatientMapper {

    /**
     * @param
     * @param env   -
     * @param param chainId-clinicId-beginDate-endDate-doctorIdIn-pageindex-pagesize-revisitCol-dateSql-departmentIds-
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.RevisitStatRsp>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 22:51
     */
    List<RevisitStatRsp> selectRevisitStat(@Param("env") String env,
                                           @Param("param") JhParam param);

    /**
     * @param
     * @param env          -
     * @param param        chainId -clinicId -beginDate -endDate -
     * @param pageindex    -
     * @param pagesize     -
     * @param departmentId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.RevisitStatRsp>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/22 22:58
     */
    List<RevisitStatRsp> selectExpertOutpatientRevisitStat(@Param("env") String env,
                                                           @Param("param") JhParam param,
                                                           @Param("pageindex") Integer pageindex,
                                                           @Param("pagesize") Integer pagesize,
                                                           @Param("departmentId") String departmentId);

    /**
     * @param
     * @param env       -
     * @param param     chainId -clinicId -beginDate -endDate -pageindex -pagesize -sourceIds -
     * @param filterSql -
     * @param dateSq    -
     * @param
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.VisitSourceInfo>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/23 08:36
     */
    List<VisitSourceInfo> selectVisitSourceStat(@Param("env") String env,
                                                @Param("param") JhParam param,
                                                @Param("filterSql") String filterSql,
                                                @Param("dateSql") String dateSq);

    /**
     * @param
     * @param env   -
     * @param param chainId -clinicId -beginDate -endDate -patientId -pageindex -pagesize -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.PatientListInfo>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/10 19:10
     */
    List<PatientListInfo> selectPatientList(@Param("env") String env,
                                            @Param("param") OutpatientParam param);


    /**
     * @param
     * @param env   -
     * @param param chainId -clinicId -beginDate -endDate -patientId -
     * @return
     * @return java.lang.Long
     * @Description:
     * @Author: zs
     * @Date: 2022/8/10 19:17
     */
    Long getPatientListCount(@Param("env") String env,
                             @Param("param") OutpatientParam param);

    /**
     * @param
     * @param env      -
     * @param param    chainId -clinicId -beginDate -endDate -patientId -employeeId -pageindex -pagesize -
     * @param isOnline -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo>
     * @Description: 运营分析-门诊日志
     * @Author: zs
     * @Date: 2022/8/11 09:44
     */
    List<OutpatientListInfo> selectOutpatientList(@Param("env") String env,
                                                  @Param("param") OutpatientParam param,
                                                  @Param("isOnline") Integer isOnline);
    /**
     * @param
     * @param env      -
     * @param param    chainId -clinicId -beginDate -endDate -patientId -employeeId -pageindex -pagesize -
     * @param isOnline -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo>
     * @Description: 运营分析-门诊日志-分布查询
     * @Author: zs
     * @Date: 2022/8/11 09:44
     */
    List<OutpatientIdDto> selectOutpatientIds(@Param("env") String env,
                                              @Param("param") OutpatientParam param,
                                              @Param("isOnline") Integer isOnline);
    /**
     * @param
     * @param env      -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientListInfo>
     * @Description: 根据ID查询数据
     * @Author: zs
     * @Date: 2022/8/11 09:44
     */
    List<OutpatientListInfo> selectOutpatientListByIds(@Param("env") String env,
                                                       @Param("ids") List<String> ids,
                                                       @Param("beginDate") String beginDate,
                                                       @Param("endDate") String endDate);


    /**
     * @param
     * @param env      -
     * @param param    chainId -clinicId -beginDate -endDate -patientId -employeeId -
     * @param isOnline -
     * @return
     * @return java.lang.Long
     * @Description: 运营分析-门诊日志汇总
     * @Author: zs
     * @Date: 2022/8/11 09:55
     */
    Long getOutpatientListCount(@Param("env") String env,
                                @Param("param") OutpatientParam param,
                                @Param("isOnline") Integer isOnline);

    /**
     * @param
     * @param env        -
     * @param chainId    -
     * @param clinicId   -
     * @param beginDate  -
     * @param endDate    -
     * @param employeeId -
     * @return
     * @return cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientSummaryDao
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 19:08
     */
    OutpatientSummaryDao selectOutpatientSummary(@Param("env") String env,
                                                 @Param("chainId") String chainId,
                                                 @Param("clinicId") String clinicId,
                                                 @Param("beginDate") String beginDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("employeeId") String employeeId);

    /**
     * @param
     * @param env        -
     * @param chainId    -
     * @param clinicId   -
     * @param beginDate  -
     * @param endDate    -
     * @param employeeId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.outpatient.domain.OutpatientFirstRevisitRsp>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 19:12
     */
    List<OutpatientFirstRevisitRsp> getOutpatientFirstRevisitNum(@Param("db") String env,
                                                                 @Param("chainId") String chainId,
                                                                 @Param("clinicId") String clinicId,
                                                                 @Param("beginDate") String beginDate,
                                                                 @Param("endDate") String endDate,
                                                                 @Param("employeeId") String employeeId
    );

    /**
     * @param
     * @param env        -
     * @param chainId    -
     * @param clinicId   -
     * @param beginDate  -
     * @param endDate    -
     * @param employeeId -
     * @return
     * @return java.util.List<java.lang.String>
     * @Description:
     * @Author: zs
     * @Date: 2022/8/11 19:19
     */
    List<String> selectPrescriptionCountByDoctorId(@Param("db") String env,
                                                   @Param("chainId") String chainId,
                                                   @Param("clinicId") String clinicId,
                                                   @Param("beginDate") String beginDate,
                                                   @Param("endDate") String endDate,
                                                   @Param("employeeId") String employeeId
    );

    /**
     * @param outpatientDb 库名
     * @param list         -
     * @param chainId      连锁id
     * @param clinicId     门店id
     * @param hisType      诊所类型
     * @return -
     */
    List<Map<String, Object>> outpatientProductFormItem(@Param("outpatientDb") String outpatientDb,
                                                        @Param("list") List<String> list,
                                                        @Param("chainId") String chainId,
                                                        @Param("clinicId") String clinicId,
                                                        @Param("hisType") String hisType);

    /**
     * 获取今日工作台数据(处方量)
     *
     * @param cisDb db
     * @param param 查询参数
     * @return 处方量
     */
    Integer getPrescriptionCount(@Param("cisDb") String cisDb,
                                 @Param("param") OutpatientParam param);

    /**
     * 查询老带新活动新客idList
     *
     * @param outpatientDb 库名
     * @param param        老带新param
     * @return 新客idList
     */
    Set<String> selectArrivalPatientIdList(@Param("db") String outpatientDb,
                                           @Param("param") PromotionReferralReferrerRewardParam param);

    /**
     * 根据连锁id查询老带新活动新客idList
     *
     * @param cisTable 库名
     * @param param        老带新param
     * @return 新客idList
     */
    List<PromotionReferralDaily> selectOutPatientListByChainIdList(@Param("db")String cisTable,
                                                                   @Param("param")PromotionReferralRpcParam param);

    /**
     * @Description: 查询指定时间内的就诊患者
     * @param
     * @param cisTable -
     * @param chainId -
     * @param clinicId -
     * @param beginDate -
     * @param endDate -
     * @return
     * @return java.util.Set<java.lang.String>
     * @Author: zs
     * @Date: 2024/6/25 16:02
     */
    Set<String> selectOutpatientPatientIds(@Param("env") String cisTable,
                                           @Param("chainId") String chainId,
                                           @Param("clinicId") String clinicId,
                                           @Param("beginDate") String beginDate,
                                           @Param("endDate") String endDate,
                                           @Param("employeeId") String employeeId);
}
