package cn.abc.flink.stat.db.cis.hologres.dao;

import cn.abc.flink.stat.db.cis.common.AchievenmentParentMapper;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievementPromotionSummaryEntity;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievenmentDetailEntity;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievenmentDetailSelectionCard;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievenmentDetailSelectionClinic;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievenmentDetailSelectionSeller;
import cn.abc.flink.stat.service.cis.achievement.promotion.card.domain.AchievenmentParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2021-07-21 16:32
 **/
public interface HologresAchievenmentMapper extends AchievenmentParentMapper {

    /**
     *
     * @param env -
     * @param param -
     * @return -
     */
    List<AchievenmentDetailEntity> selectAchievenmentDetail(@Param("env") String env,
                                                            @Param("param") AchievenmentParam param);

    /**
     *
     * @param env -
     * @param param -
     * @return -
     */
    long selectAchievenmentTotal(@Param("env") String env,
                                 @Param("param") AchievenmentParam param);

    /**
     *
     * @param env -
     * @param param -
     * @return -
     */
    List<AchievenmentDetailSelectionSeller> selectOptionSeller(@Param("env") String env,
                                                               @Param("param") AchievenmentParam param);

    /**
     *
     * @param env -
     * @param param -
     * @return -
     */
    List<AchievenmentDetailSelectionCard> selectOptionCard(@Param("env") String env,
                                                           @Param("param") AchievenmentParam param);

    /**
     *
     * @param env -
     * @param param -
     * @return -
     */
    List<AchievenmentDetailSelectionClinic> selectOptionClinic(@Param("env") String env,
                                                               @Param("param") AchievenmentParam param);

    /**
     *
     * @param env -
     * @param param -
     * @return -
     */
    List<AchievementPromotionSummaryEntity> selectAchievenmentSummary(@Param("env") String env,
                                                                      @Param("param") AchievenmentParam param);

}
