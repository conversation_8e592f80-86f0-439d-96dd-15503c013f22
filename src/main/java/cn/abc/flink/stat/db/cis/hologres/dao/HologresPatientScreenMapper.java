package cn.abc.flink.stat.db.cis.hologres.dao;

import cn.abc.flink.stat.db.cis.common.PatientScreenParentMapper;
import cn.abc.flink.stat.service.cis.patientScreen.domain.Patient;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchives;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchivesParam;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientScreenCondition;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientScreenLable;
import cn.abc.flink.stat.service.cis.patientScreen.domain.PatientWxCountDto;
import cn.abc.flink.stat.service.cis.patientScreen.domain.V2PatientStat;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: libl
 * @version: 1.0
 * @modified:
 * @date: 2021-08-02 15:47
 **/
@Qualifier("hologresSqlSessionTemplate")
@Repository
public interface HologresPatientScreenMapper extends PatientScreenParentMapper {


    /**
     * @Description: 患者筛选-患者来源
     * @param
     * @param env -
     * @param chainId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.PatientScreenLable>
     * @Author: zs
     * @Date: 2023/3/7 10:23
     */
    List<PatientScreenLable> selectPatientSourceList(@Param("env") String env,
                                                     @Param("chainId") String chainId
                                                     //, @Param("clinicId") String clinicId
    );

    /**
     * @Description: 患者筛选-标签
     * @param
     * @param env -
     * @param chainId -
     * @return
     * @return java.util.List<java.lang.String>
     * @Author: zs
     * @Date: 2023/3/7 10:24
     */
    List<String> selectPatientTagList(@Param("env") String env,
                                      @Param("chainId") String chainId
                                      //,@Param("clinicId") String clinicId
    );


    /**
     * @Description: 患者筛选-服务包
     * @param
     * @param env -
     * @param chainId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.PatientScreenLable>
     * @Author: zs
     * @Date: 2023/3/7 10:24
     */
    List<PatientScreenLable> selectPatientPackList(@Param("env") String env,
                                                   @Param("chainId") String chainId
                                                   //,@Param("clinicId") String clinicId
    );


    /**
     * @Description: 患者筛选-会员类型
     * @param
     * @param env -
     * @param chainId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.PatientScreenLable>
     * @Author: zs
     * @Date: 2023/3/7 10:24
     */
    List<PatientScreenLable> selectMemberTypeList(@Param("env") String env,
                                                  @Param("chainId") String chainId
                                                  //,@Param("clinicId") String clinicId
    );

    /**
     * @Description: 患者筛选-签约医生
     * @param
     * @param env -
     * @param chainId -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.PatientScreenLable>
     * @Author: zs
     * @Date: 2023/3/7 10:25
     */
    List<PatientScreenLable> selectDoctorList(@Param("env") String env,
                                              @Param("chainId") String chainId
                                              //,@Param("clinicId") String clinicId
    );


    /**
     * @Description: 患者筛选-筛选
     * @param
     * @param patientScreenCondition -
     * @param env -
     * @param isFamilyDoctor -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.Patient>
     * @Author: zs
     * @Date: 2023/3/7 10:25
     */
    List<Patient> selectPatients(@Param("params") PatientScreenCondition patientScreenCondition,
                                 @Param("env") String env,
                                 @Param("hisDb") String hisDb,
                                 @Param("isFamilyDoctor")  Integer isFamilyDoctor
    );


    /**
     * @Description:
     * @param
     * @param patientScreenCondition -
     * @param env -
     * @param isFamilyDoctor -
     * @return
     * @return java.lang.Long
     * @Author: zs
     * @Date: 2023/3/7 10:25
     */
    Long selectPatientCount(@Param("params") PatientScreenCondition patientScreenCondition,
                            @Param("env") String env,
                            @Param("hisDb") String hisDb,
                            @Param("isFamilyDoctor")  Integer isFamilyDoctor
    );

   /* List<PatientArchives> selectPatientArchives(@Param("chainId") String chainId,
                                                @Param("patientIds") List<String> patientIds,
                                                @Param("beginDate") String beginDate,
                                                @Param("endDate") String endDate,
                                                @Param("env") String env
    );*/


    /**
     * @Description:
     * @param
     * @param chainId -
     * @param clinicId -
     * @param patientIds -
     * @param beginDate -
     * @param endDate -
     * @param env -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchives>
     * @Author: zs
     * @Date: 2023/3/7 10:26
     */
    List<PatientArchives> selectPatientArchives(@Param("params") PatientArchivesParam params,
                                                @Param("env") String env
    );



    /**
     * @Description:
     * @param
     * @param chainId -
     * @param clinicId -
     * @param patientIds -
     * @param env -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchives>
     * @Author: zs
     * @Date: 2023/3/7 10:26
     */
    List<PatientArchives> selectPatientCumulativeAmount(@Param("chainId") String chainId,
                                                           @Param("clinicId") String clinicId,
                                                           @Param("patientIds") String patientIds,
                                                           @Param("env") String env);


    /**
     * @Description:
     * @param
     * @param chainId -
     * @param patientIds -
     * @param beginDate -
     * @param endDate -
     * @param env -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.PatientArchives>
     * @Author: zs
     * @Date: 2023/3/7 10:26
     */
    List<PatientArchives> selectRetailCount(@Param("chainId") String chainId,
                                               @Param("patientIds") String patientIds,
                                               @Param("beginDate") String beginDate,
                                               @Param("endDate") String endDate,
                                               @Param("env")String env
                                               );


    /**
     * @Description: 查询患者统计list
     * @param
     * @param cisTable -
     * @param params -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.V2PatientStat>
     * @Author: zs
     * @Date: 2024/4/16 15:13
     */
    List<V2PatientStat> selectPatientStat(@Param("env") String cisTable, @Param("hisDb") String hisDb, @Param("params") PatientArchivesParam params);

    /**
     * @Description: 查询患者统计count
     * @param
     * @param cisTable -
     * @param params -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.V2PatientStat>
     * @Author: zs
     * @Date: 2024/4/16 15:13
     */
    Long selectPatientStatCount(@Param("env") String cisTable, @Param("hisDb") String hisDb, @Param("params") PatientArchivesParam params);

    /**
     * @Description: 根据门店ID查询患者绑定数
     * @param
     * @param env -
     * @param ids -
     * @return
     * @return java.util.List<cn.abc.flink.stat.service.cis.patientScreen.domain.V2PatientStat>
     * @Author: zs
     * @Date: 2024/4/16 15:13
     */
    List<PatientWxCountDto> selectPatientWxCount(@Param("env") String env, @Param("chainId") String chainId, @Param("ids") List<String> ids);
}


