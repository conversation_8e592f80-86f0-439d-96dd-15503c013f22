package cn.abc.flink.stat.mq.rabbit.config;

import cn.abc.flink.stat.common.constants.CommonConstants;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * @description: rabbitMq配置类
 * @author: lzq
 * @Date: 2023/4/6 11:34 上午
 */
@Configuration
@ConditionalOnProperty(name = "amqp.enabled", havingValue = "true", matchIfMissing = false)
public class RabbitMqConfiguration {

    @Value("${amqp.host}")
    private String host;

    @Value("${amqp.port}")
    private int port;

    @Value("${amqp.vhost}")
    private String vhostName;

    @Value("${amqp.fanout-exchange-organ.name}")
    private String fanoutExchangeOrganName;
    @Value("${amqp.fanout-exchange-organ.queue.name}")
    private String fanoutExchangeOrganQueueName;


    /**
     * 构建rabbitMq连接工厂
     * @return rabbit连接工厂
     */
    @Bean
    public ConnectionFactory getConnectionFactory() {
        com.rabbitmq.client.ConnectionFactory factory = new com.rabbitmq.client.ConnectionFactory();
        factory.setHost(host);
        factory.setAutomaticRecoveryEnabled(false);

        factory.setVirtualHost(vhostName);

        factory.setPort(port);
        factory.setConnectionTimeout(CommonConstants.RABBIT_MQ_CONNECTION_TIME_OUT_TIME);
        factory.setHandshakeTimeout(CommonConstants.RABBIT_MQ_HANDSHAKE_TIME_OUT_TIME);
        factory.setShutdownTimeout(0);
        return new CachingConnectionFactory(factory);
    }


    /**
     * 构建rabbitMqTemplate
     * @return rabbitMqTemplate
     */
    @Bean
    public RabbitTemplate rabbitTemplate() {
        RabbitTemplate template = new RabbitTemplate(getConnectionFactory());
        template.setMessageConverter(getAMQPJackson2JsonMessageConverter());
        return template;
    }

    /**
     * 构建rabbit监听器工厂
     * @return rabbit监听器工厂
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory() {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(getConnectionFactory());
        factory.setMessageConverter(getAMQPJackson2JsonMessageConverter());
        return factory;
    }

    /**
     * 构建rabbitMq消息json转换
     * @return 消息转换
     */
    @Bean
    public Jackson2JsonMessageConverter getAMQPJackson2JsonMessageConverter() {
        ObjectMapper objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return new Jackson2JsonMessageConverter(objectMapper);
    }

    @Bean
    public Queue getOrganQueue() {
        return new Queue(fanoutExchangeOrganQueueName);
    }

    @Bean
    public FanoutExchange getOrganFanoutExchange() {
        return new FanoutExchange(fanoutExchangeOrganName);
    }

    @Bean
    public Binding getOrganBinding() {
        return BindingBuilder.bind(getOrganQueue()).to(getOrganFanoutExchange());
    }
}
