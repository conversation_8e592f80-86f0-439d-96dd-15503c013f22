//package cn.abc.flink.stat.mq.rabbit.consumer;
//
//import cn.abc.flink.stat.common.HisTypeEnum;
//import cn.abc.flink.stat.service.cis.config.StatConfigService;
//import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
//import cn.abcyun.cis.commons.amqp.message.OrganMessage;
//import cn.abcyun.cis.commons.model.Organ;
//import cn.abcyun.cis.commons.util.JsonUtils;
//import cn.hutool.core.bean.BeanUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//
///**
// * 业务门店创建RabbitMq消息消费者
// * 业务门店创建后统计服务生成统计的默认配置
// */
//@Component
//public class ClinicCreateStatConfigMqConsumer {
//
//    private final StatConfigService statConfigService;
//
//    private static final Logger logger = LoggerFactory.getLogger(ClinicCreateStatConfigMqConsumer.class);
//
//    @Autowired
//    public ClinicCreateStatConfigMqConsumer(StatConfigService statConfigService) {
//        this.statConfigService = statConfigService;
//    }
//
//    /**
//     * 门店创建 统计服务生成默认的配置
//     *
//     * @param message 门店创建消息
//     */
//    @RabbitListener(queues = "${amqp.fanout-exchange-organ.queue.name}", group = "${spring.application.name}")
//    public void receiveOrganMessage(OrganMessage message) {
//        try {
//            logger.info("receiveOrganMessage:{}, clinicId:{}, message:{}", message.getType(),
//                    message.getClinicId(), JsonUtils.dump(message));
//            // 如果不是新建直接返回
//            if (message.getType() != OrganMessage.MSG_TYPE_ORGAN_CREATE) {
//                return;
//            }
//            Organ organ = message.getNewVal();
//            // 直接调用service新建默认配置
//            if (!BeanUtil.isEmpty(organ)) {
//                StatConfigDto dto = new StatConfigDto();
//                if (organ.getNodeType() == HisTypeEnum.CLINIC.getTypeNumber()) {
//                    dto.setClinicId(organ.getId());
//                }
//                dto.setChainId(organ.getParentId());
//                dto.setEmployeeId("00000000000000000000000000000000");
//                dto.setCreatedBy("00000000000000000000000000000000");
//                dto.setCreated(LocalDateTime.now());
//                dto.setLastModified(LocalDateTime.now());
//                dto.setLastModifiedBy("00000000000000000000000000000000");
//                statConfigService.insertOrUpdateConfig(dto);
//            }
//        } catch (Exception e) {
//            logger.error("error", e);
//        }
//    }
//}
