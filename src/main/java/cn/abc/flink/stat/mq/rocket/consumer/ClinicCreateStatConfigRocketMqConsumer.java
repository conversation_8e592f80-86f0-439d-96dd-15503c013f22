//package cn.abc.flink.stat.mq.rocket.consumer;
//
//import cn.abc.flink.stat.common.HisTypeEnum;
//import cn.abc.flink.stat.service.cis.config.StatConfigService;
//import cn.abc.flink.stat.service.cis.config.pojo.StatConfigDto;
//import cn.abcyun.cis.commons.amqp.message.OrganMessage;
//import cn.abcyun.cis.commons.model.Organ;
//import cn.abcyun.cis.commons.util.JsonUtils;
//import cn.hutool.core.bean.BeanUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.apache.rocketmq.spring.core.RocketMQListener;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//
///**
// * @description: 业务门店创建RocketMq消息消费者
// *
// *  业务门店创建后统计服务生成统计的默认配置
// *
// * @author: lzq
// * @Date: 2023/10/13 15:50
// */
//@Component
//public class ClinicCreateStatConfigRocketMqConsumer {
//
//    private static final Logger logger = LoggerFactory.getLogger(ClinicCreateStatConfigRocketMqConsumer.class);
//
//    private final StatConfigService statConfigService;
//
//    @Autowired
//    public ClinicCreateStatConfigRocketMqConsumer(StatConfigService statConfigService) {
//        this.statConfigService = statConfigService;
//    }
//
//    // topic需要和生产者的topic一致，consumerGroup属性是必须指定的，内容可以随意
//    // selectorExpression的意思指的就是tag，默认为“*”，不设置的话会监听所有消息
//
//    /**
//     * 门店创建 统计服务生成默认的配置
//     */
//    @RocketMQMessageListener(topic = "${rocketmq.topic.clinic.name}", //topic名
//            selectorExpression = "${rocketmq.tag.clinic.clinic-create-update}", //tag名
//            consumerGroup = "${rocketmq.group.clinic-create-create-stat-config.name}" //group名
//    )
//    public class ClinicCreateMessageListener implements RocketMQListener<OrganMessage> {
//        @Override
//        public void onMessage(OrganMessage message) {
//            // 监听到消息就会执行此方法
//            try {
//                logger.info("receiveOrganMessage:{}, clinicId:{}, message:{}", message.getType(),
//                        message.getClinicId(), JsonUtils.dump(message));
//                // 如果不是新建直接返回
//                if (message.getType() != OrganMessage.MSG_TYPE_ORGAN_CREATE) {
//                    return;
//                }
//                Organ organ = message.getNewVal();
//                // 直接调用service新建默认配置
//                if (!BeanUtil.isEmpty(organ)) {
//                    StatConfigDto dto = new StatConfigDto();
//                    if (organ.getNodeType() == HisTypeEnum.CLINIC.getTypeNumber()) {
//                        dto.setClinicId(organ.getId());
//                    }
//                    dto.setChainId(organ.getParentId());
//                    dto.setEmployeeId("00000000000000000000000000000000");
//                    dto.setCreatedBy("00000000000000000000000000000000");
//                    dto.setCreated(LocalDateTime.now());
//                    dto.setLastModified(LocalDateTime.now());
//                    dto.setLastModifiedBy("00000000000000000000000000000000");
//                    statConfigService.insertOrUpdateConfig(dto);
//                }
//            } catch (Exception e) {
//                logger.error("error", e);
//            }
//        }
//    }
//
//}
