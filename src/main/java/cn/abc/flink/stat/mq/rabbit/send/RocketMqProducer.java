//package cn.abc.flink.stat.mq.rabbit.send;
//
//import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.UserOperationLogMessage;
//import cn.abcyun.bis.rpc.sdk.his.message.advice.AdviceExecuteMessage;
//import cn.abcyun.bis.rpc.sdk.his.message.advice.AdviceMessage;
//import cn.abcyun.cis.commons.exception.ParamNotValidException;
//import cn.abcyun.cis.commons.util.JsonUtils;
//import cn.abcyun.common.log.marker.AbcLogMarker;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.rocketmq.spring.core.RocketMQTemplate;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.messaging.Message;
//import org.springframework.messaging.support.MessageBuilder;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.support.TransactionSynchronization;
//import org.springframework.transaction.support.TransactionSynchronizationManager;
//
//import java.util.List;
//
///**
// * rocket mq 生产者
// *
// * <AUTHOR>
// * @version RocketMqProducer.java, 2022/9/28 上午11:44
// */
//@Component
//public class RocketMqProducer {
//
//    private static final Logger logger = LoggerFactory.getLogger(RocketMqProducer.class);
//
//    private final RocketMQTemplate rocketMqTemplate;
//
//    /**
//     * 消息topic
//     */
//    @Value("${rocketmq.topic.cis-user-operation-log.name}")
//    private String cisUserOperationLogTopic;
//
//    /**
//     * 医嘱状态变更tag
//     */
//    @Value("${rocketmq.tag.cis-user-operation-log.create-log}")
//    private String createUserOperationLogTag;
//
//    @Autowired
//    public RocketMqProducer(RocketMQTemplate rocketMqTemplate) {
//        this.rocketMqTemplate = rocketMqTemplate;
//    }
//
//    /**
//     * 发送医嘱状态变更消息
//     *
//     * @param userOperationLogMessage 用户操作日志上报消息
//     */
//    public void sendOperationLog(UserOperationLogMessage userOperationLogMessage) {
//        logger.info("sendOperationLog: {}", JsonUtils.dump(userOperationLogMessage));
//        if (userOperationLogMessage == null) {
//            return;
//        }
//        try {
//            Message < UserOperationLogMessage > message = MessageBuilder.withPayload(userOperationLogMessage).build();
//            rocketMqTemplate.send(getDestination(cisUserOperationLogTopic, createUserOperationLogTag), message);
//        } catch (Exception e) {
//            logger.error(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "send userOperationLogMessage to rocketMq failed: msg = {}, e = {}", JsonUtils.dump(userOperationLogMessage), e);
//        }
//    }
//
//    private String getDestination(String topic, String tag) {
//        if (StringUtils.isBlank(topic)) {
//            throw new ParamNotValidException("topic is null");
//        }
//        if (StringUtils.isBlank(tag)) {
//            return topic;
//        }
//        return String.format("%s:%s", topic, tag);
//    }
//
//    public static void doAfterTransactionCommit(Runnable runnable) {
//        // 需要等待事务结束后才通知消息
//        // 这是实现方式之一
//        // 还可以使用TransactionalEventListener，参考文章https://plu.one/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/2018/06/04/spring-transactional-event-listener/
//        if (TransactionSynchronizationManager.isActualTransactionActive()) {
//            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {@
//                Override
//                public void afterCommit() {
//                    runnable.run();
//                }
//            });
//        } else {
//            runnable.run();
//        }
//    }
//}