//package cn.abc.flink.stat.mq.rabbit.send;
//
//import cn.abc.flink.stat.export.entity.AsyncExportTask;
//import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OpSource;
//import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OpType;
//import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OperationObjectType;
//import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.UserOperationLogMessage;
//import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.PatientOrder;
//import cn.abcyun.cis.core.util.JsonUtils;
//import com.fasterxml.jackson.databind.JsonNode;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.time.Instant;
//
///**
// * @BelongsProject: stat
// * @BelongsPackage: cn.abc.flink.stat.mq.rabbit.send
// * @Author: zs
// * @CreateTime: 2024-06-18  15:53
// * @Description: TODO
// * @Version: 1.0
// */
//@Component
//public class RabbitMqSendAsyncExport {
//
//    @Autowired
//    private RocketMqProducer rocketMqProducer;
//
//    public void sendOperatorLogMessage(AsyncExportTask task, String loginWay) {
//        UserOperationLogMessage operationLogMessage = new UserOperationLogMessage();
//        operationLogMessage.setChainId(task.getChainId());
//        operationLogMessage.setClinicId(task.getClinicId());
//        operationLogMessage.setOperatorId(task.getEmployeeId());
//
//        operationLogMessage.setCreated(Instant.now());
//        if (task != null && task.getOss() != null) {
//            operationLogMessage.setEntityId(task.getId().toString());
//            operationLogMessage.setEntityNo(task.getId().toString());
//            // 报表名称 + 时间 + 下载
//            String oss = task.getOss();
//            //按斜杠分割取最后一个
//            String[] split = oss.split("/");
//            String fileName = split[split.length - 1];
//            operationLogMessage.setSummary("下载文件：" + fileName);
//        }
//
//        operationLogMessage.setOpType(OpType.EXPORT);
//        operationLogMessage.setOpSubType(0);
//        operationLogMessage.setOperationObjectType(OperationObjectType.STATISTICS_REPORT);
//        operationLogMessage.setOperationObjectSubType(OperationObjectType.StatisticsReportSubObjectType.STATISTICS_REPORT);
//        operationLogMessage.setOpSource(OpSource.PC);
//        operationLogMessage.setLoginWay(loginWay);
//
//        rocketMqProducer.sendOperationLog(operationLogMessage);
//    }
//
//    public void sendOperatorLogMessages(String chainId, String clinicId, String employeeId, String fileName, String loginWay) {
//        UserOperationLogMessage operationLogMessage = new UserOperationLogMessage();
//        operationLogMessage.setChainId(chainId);
//        operationLogMessage.setClinicId(clinicId);
//        operationLogMessage.setOperatorId(employeeId);
//
//        operationLogMessage.setCreated(Instant.now());
//        if (fileName != null) {
//            operationLogMessage.setSummary("下载文件：" + fileName);
//        }
//
//        operationLogMessage.setOpType(OpType.EXPORT);
//        operationLogMessage.setOpSubType(0);
//        operationLogMessage.setOperationObjectType(OperationObjectType.STATISTICS_REPORT);
//        operationLogMessage.setOperationObjectSubType(OperationObjectType.StatisticsReportSubObjectType.STATISTICS_REPORT);
//        operationLogMessage.setOpSource(OpSource.PC);
//        operationLogMessage.setLoginWay(loginWay);
//
//        rocketMqProducer.sendOperationLog(operationLogMessage);
//    }
//}
