# WebSocket RPC通知功能使用说明

## 功能概述

本功能实现了当RPC请求到达时，通过WebSocket实时通知前端的能力。系统会自动拦截所有`/rpc/`路径下的请求，并将请求信息通过WebSocket推送给连接的客户端。

## 架构组件

### 1. 核心组件

- **WebSocketConfig**: WebSocket配置类，配置STOMP端点和消息代理
- **WebSocketNotificationService**: WebSocket通知服务，负责发送各种类型的通知
- **RpcRequestInterceptor**: RPC请求拦截器，拦截RPC请求并发送通知
- **WebSocketEventListener**: WebSocket事件监听器，处理连接/断开等事件
- **WebSocketController**: WebSocket控制器，提供管理接口和消息处理

### 2. 数据模型

- **WebSocketMessage**: WebSocket消息基础模型
- **RpcRequestNotification**: RPC请求通知数据模型

## 使用方式

### 1. 前端连接WebSocket

```javascript
// 使用SockJS和STOMP连接
const socket = new SockJS('/ws');
const stompClient = Stomp.over(socket);

// 连接时可以传递用户信息
const headers = {
    'userId': 'user123',
    'chainId': 'chain456',
    'clinicId': 'clinic789'
};

stompClient.connect(headers, function(frame) {
    console.log('连接成功:', frame);
    
    // 订阅RPC通知
    stompClient.subscribe('/topic/rpc-notifications', function(message) {
        const notification = JSON.parse(message.body);
        console.log('收到RPC通知:', notification);
    });
});
```

### 2. 订阅不同类型的通知

```javascript
// 订阅所有RPC通知
stompClient.subscribe('/topic/rpc-notifications', handleRpcNotification);

// 订阅特定连锁的RPC通知
stompClient.subscribe('/topic/rpc-notifications/chainId', handleRpcNotification);

// 订阅系统通知
stompClient.subscribe('/topic/system-notifications', handleSystemNotification);

// 订阅数据更新通知
stompClient.subscribe('/topic/data-updates/dataType', handleDataUpdate);

// 订阅个人通知
stompClient.subscribe('/user/queue/rpc-notifications', handlePersonalNotification);
```

### 3. 处理通知消息

```javascript
function handleRpcNotification(message) {
    const notification = JSON.parse(message.body);
    
    console.log('RPC请求信息:', {
        path: notification.content.requestPath,
        method: notification.content.httpMethod,
        status: notification.content.status,
        processingTime: notification.content.processingTime,
        timestamp: notification.timestamp
    });
    
    // 根据不同状态处理
    switch(notification.content.status) {
        case 'STARTED':
            showRequestStarted(notification);
            break;
        case 'SUCCESS':
            showRequestSuccess(notification);
            break;
        case 'FAILED':
            showRequestFailed(notification);
            break;
    }
}
```

## API接口

### 1. WebSocket管理接口

- `GET /websocket/stats` - 获取连接统计信息
- `GET /websocket/test/notification` - 发送测试通知
- `GET /websocket/test/data-update` - 发送数据更新测试通知

### 2. WebSocket端点

- `/ws` - 主要的WebSocket端点（支持SockJS）
- `/ws-native` - 原生WebSocket端点

### 3. 消息频道

- `/topic/rpc-notifications` - 全局RPC通知
- `/topic/rpc-notifications/{chainId}` - 特定连锁RPC通知
- `/topic/system-notifications` - 系统通知
- `/topic/data-updates/{dataType}` - 数据更新通知
- `/user/queue/rpc-notifications` - 个人RPC通知

## 消息格式

### RPC请求通知消息格式

```json
{
  "type": "RPC_REQUEST",
  "content": {
    "requestPath": "/rpc/sc/stat/revenue/charge/product/list",
    "httpMethod": "GET",
    "requestParams": {
      "chainId": "chain123",
      "clinicId": "clinic456"
    },
    "headers": {
      "Content-Type": "application/json",
      "User-Agent": "Mozilla/5.0..."
    },
    "clientIp": "*************",
    "userAgent": "Mozilla/5.0...",
    "status": "SUCCESS",
    "processingTime": 150,
    "errorMessage": null
  },
  "timestamp": "2024-01-01T12:00:00",
  "source": "/rpc/sc/stat/revenue/charge/product/list",
  "chainId": "chain123",
  "messageId": "uuid-123-456"
}
```

### 系统通知消息格式

```json
{
  "type": "SYSTEM_NOTIFICATION",
  "content": "用户连接成功: session-123",
  "timestamp": "2024-01-01T12:00:00",
  "messageId": "uuid-789-012"
}
```

## 测试方法

### 1. 使用测试页面

访问 `http://localhost:8080/websocket-test.html` 打开测试页面，可以：

- 连接/断开WebSocket
- 订阅不同类型的通知
- 发送测试消息
- 查看实时通知

### 2. 手动测试RPC请求

```bash
# 发送RPC请求，观察WebSocket通知
curl "http://localhost:8080/rpc/sc/stat/revenue/charge/product/list?chainId=test&clinicId=test&beginDate=2024-01-01&endDate=2024-01-31"
```

### 3. 使用管理接口

```bash
# 获取连接统计
curl "http://localhost:8080/websocket/stats"

# 发送测试通知
curl "http://localhost:8080/websocket/test/notification?message=测试消息"
```

## 配置说明

### 1. 拦截器配置

在 `WebMvcConfig` 中配置了RPC请求拦截器：

```java
registry.addInterceptor(rpcRequestInterceptor)
        .addPathPatterns("/rpc/**")
        .excludePathPatterns("/rpc/health", "/rpc/actuator/**");
```

### 2. WebSocket配置

在 `WebSocketConfig` 中配置了消息代理和端点：

```java
// 消息代理前缀
config.enableSimpleBroker("/topic", "/queue");
// 应用程序目标前缀
config.setApplicationDestinationPrefixes("/app");
// 用户目标前缀
config.setUserDestinationPrefix("/user");
```

## 扩展功能

### 1. 添加自定义通知类型

```java
// 在WebSocketMessage.MessageType中添加新类型
public enum MessageType {
    // ... 现有类型
    CUSTOM_NOTIFICATION
}

// 在WebSocketNotificationService中添加发送方法
public void sendCustomNotification(Object content) {
    WebSocketMessage message = WebSocketMessage.builder()
            .type(WebSocketMessage.MessageType.CUSTOM_NOTIFICATION)
            .content(content)
            .timestamp(LocalDateTime.now())
            .messageId(UUID.randomUUID().toString())
            .build();
    
    messagingTemplate.convertAndSend("/topic/custom-notifications", message);
}
```

### 2. 添加消息过滤

可以在拦截器中添加更复杂的过滤逻辑：

```java
// 在RpcRequestInterceptor中添加过滤条件
private boolean shouldNotify(HttpServletRequest request) {
    String path = request.getRequestURI();
    
    // 只通知特定的RPC接口
    return path.contains("/charge/product/list") || 
           path.contains("/revenue/detail");
}
```

### 3. 添加认证和授权

```java
// 在WebSocketConfig中添加拦截器
@Override
public void configureClientInboundChannel(ChannelRegistration registration) {
    registration.interceptors(new AuthChannelInterceptor());
}
```

## 注意事项

1. **性能考虑**: 大量RPC请求可能产生大量WebSocket消息，建议根据实际需求添加过滤和限流机制
2. **安全考虑**: 避免在通知中包含敏感信息，如密码、token等
3. **连接管理**: 客户端应该实现重连机制，处理网络断开等异常情况
4. **消息持久化**: 当前实现不支持消息持久化，离线用户无法接收历史消息
5. **集群部署**: 如果应用部署在多个实例上，需要考虑使用外部消息代理（如Redis、RabbitMQ）来同步消息

## 故障排除

### 1. 连接失败

- 检查WebSocket端点是否正确配置
- 确认防火墙和代理设置
- 查看浏览器控制台错误信息

### 2. 收不到通知

- 确认已正确订阅相应的频道
- 检查RPC请求是否被拦截器捕获
- 查看服务器日志中的错误信息

### 3. 性能问题

- 监控WebSocket连接数量
- 检查消息发送频率
- 考虑添加消息缓存和批量发送机制
