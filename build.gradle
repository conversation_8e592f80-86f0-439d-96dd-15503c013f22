buildscript {
    dependencies {
        classpath('se.transmode.gradle:gradle-docker:1.2')
    }
}

plugins {
    id 'org.springframework.boot' version '2.3.3.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
}

apply plugin: 'io.spring.dependency-management'
apply plugin: 'docker'

group = 'cn.abc.flink'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

repositories {
    mavenCentral()
//    maven { url "https://artifacts.elastic.co/maven" }
    maven {
        credentials {
            username 'ZL<PERSON>Zuu'
            password 'Nl4rmLzuy7'
        }
        url 'https://repo.rdc.aliyun.com/repository/105566-release-Sy2Ug0/'
    }
    maven {
        credentials {
            username 'ZLmZuu'
            password 'Nl4rmLzuy7'
        }
        url 'https://packages.aliyun.com/maven/repository/105566-release-Sy2Ug0/'
    }
    maven {
        credentials {
            username '<PERSON><PERSON><PERSON><PERSON>u<PERSON>'
            password 'Nl4rmLzuy7'
        }
        url 'https://packages.aliyun.com/maven/repository/105566-snapshot-k87VEs/'
    }
    maven { url "https://repository.cloudera.com/artifactory/cloudera-repos/" }
    maven { url "https://repository.cloudera.com/content/repositories/releases/" }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
}


ext {
//    set('springCloudVersion', 'Greenwich.SR1')
    set('dockerApplication', 'abc-cis-sc-stat-service')
    set('dockerRegistry', 'registry.cn-shanghai.aliyuncs.com')
    set('dockerGroup', 'byteflow')
    set('dockerVersion', 'latest')
    set('springCloudVersion', "Hoxton.SR7")
}



dependencies {

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    implementation 'org.springframework.cloud:spring-cloud-starter-zipkin'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter'
    implementation "org.springframework.boot:spring-boot-configuration-processor"
    implementation 'org.springframework.boot:spring-boot-starter-freemarker'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'io.springfox:springfox-boot-starter:3.0.0'
    implementation 'com.google.code.gson:gson'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-jetty
    implementation ('org.springframework.boot:spring-boot-starter-jetty') {
        exclude group: 'org.ow2.asm'
    }
    implementation ('org.apache.rocketmq:rocketmq-spring-boot-starter:2.2.3') {
        exclude group: 'org.apache.tomcat', module: 'annotations-api'
    }
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
//    implementation 'org.springframework.boot:spring-boot-starter-data-elasticsearch'

    implementation 'com.alibaba:easyexcel:2.2.3'
    compile group: 'org.ahocorasick', name: 'ahocorasick', version: '0.4.0'

    compile group: 'com.alibaba', name: 'druid', version: '1.1.23'
//    compile(group: 'org.apache.hive', name: 'hive-jdbc', version: '2.1.1') {
//        exclude group: 'org.apache.logging.log4j'
//    }
    compile group: 'cglib', name: 'cglib', version: '2.2'
    implementation 'cn.abcyun.cis:abc-cis-commons:2.0.7.46'
    implementation 'cn.abcyun.cis:abc-cis-id-generator:0.0.8'
    implementation ('cn.abcyun.cis:abc-cis-core:0.2.13') {
//        exclude group: 'cn.abcyun.common', module: 'abc-common-model'
        exclude group: 'org.ow2.asm'
    }
    runtimeOnly 'mysql:mysql-connector-java'
    compile 'io.jsonwebtoken:jjwt:0.9.1'

    implementation 'cn.hutool:hutool-all:5.3.10'
    implementation group: 'org.apache.logging.log4j', name: 'log4j-api', version: '2.17.0'
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:2.17.0'

    testImplementation('org.springframework.boot:spring-boot-starter-test'){
        exclude group: 'org.ow2.asm'
    }
    implementation 'com.aliyun.openservices:aliyun-log-logback-appender:0.1.15'

    compile(group: 'com.github.pagehelper', name: 'pagehelper-spring-boot-starter', version: '1.2.12'){
        exclude group: 'org.ow2.asm'
        exclude group: 'org.apache.logging.log4j'
    }
    compile (group: 'org.apache.hive', name: 'hive-jdbc', version: '2.3.5') {
        exclude group: 'org.apache.logging.log4j'
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
        exclude group: 'org.eclipse.jetty.orbit', module: 'javax.servlet'
        exclude group: 'org.eclipse.jetty.aggregate'
        exclude group: 'javax.servlet'
        exclude group: 'org.mortbay.jetty'
        exclude group: 'asm'
    }

    compile group: 'com.alibaba', name: 'fastjson', version: '1.2.73'
    compile group: 'org.apache.commons', name: 'commons-pool2', version: '2.8.0'
    compile group: 'com.aliyun.oss', name: 'aliyun-sdk-oss', version: '3.10.2'
    compile group: 'org.postgresql', name: 'postgresql', version: '42.5.1'

    implementation 'cn.abcyun.common:abc-common-model:1.0.6'
    implementation 'cn.abcyun.common:abc-common-log:0.0.7'
    implementation 'commons-beanutils:commons-beanutils:1.8.3'
    implementation 'cn.abcyun.bis:abc-bis-rpc-sdk:2.96.26'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'

}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

task buildUnpackJar(type: Copy) {
    dependsOn clean
    dependsOn bootJar
    tasks.findByName('bootJar').mustRunAfter('clean')

    from(zipTree(tasks.bootJar.outputs.files.singleFile))
    into("build/dependency")
}

task buildDocker(type: Docker) {
    dependsOn buildUnpackJar
    tag = "${dockerRegistry}/${dockerGroup}/${dockerApplication}"
    tagVersion = "${dockerVersion}"
    dockerfile = file('Dockerfile')

    doFirst {
        copy {
            from "build/dependency"
            into "${stageDir}/build/dependency"
        }
    }
}

task deployDocker(type: Exec) {
    dependsOn buildDocker
    commandLine "docker", "push", "${dockerRegistry}/${dockerGroup}/${dockerApplication}:${dockerVersion}"
}

// 配置并行构建
tasks.withType(JavaCompile) {
    options.fork = true
    options.incremental = true
    options.forkOptions.memoryMaximumSize = "2048m"
}